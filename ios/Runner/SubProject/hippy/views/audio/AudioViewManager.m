/*!
* iOS SDK
*
* <PERSON><PERSON> is pleased to support the open source community by making
* Hippy available.
*
* Copyright (C) 2019 THL A29 Limited, a Tencent company.
* All rights reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

#import "AudioViewManager.h"
#import "AudioView.h"
#import "AudioPlayerMananger.h"

@implementation AudioViewManager

HIPPY_EXPORT_MODULE(ABCAudioView)

HIPPY_EXPORT_VIEW_PROPERTY(src, NSString)
HIPPY_EXPORT_VIEW_PROPERTY(emitTimeupdateRate, NSInteger)
HIPPY_EXPORT_VIEW_PROPERTY(onTimeupdate, HippyDirectEventBlock)
HIPPY_EXPORT_VIEW_PROPERTY(onPause, HippyDirectEventBlock)
HIPPY_EXPORT_VIEW_PROPERTY(onPlaying, HippyDirectEventBlock)
HIPPY_EXPORT_VIEW_PROPERTY(onEnded, HippyDirectEventBlock)
HIPPY_EXPORT_VIEW_PROPERTY(onError, HippyDirectEventBlock)

- (UIView *)view {
    AudioView* audioView = [[AudioView alloc] initWithBridge:self.bridge props:self.props];
    audioView.delegate = [AudioPlayerMananger sharedInstance];
    
    return audioView;
}

HIPPY_EXPORT_METHOD(play:(nonnull NSNumber *)hippyTag src:(NSString*)src) {
    [self.bridge.uiManager addUIBlock:^(__unused HippyUIManager *uiManager, NSDictionary<NSNumber *,UIView *> *viewRegistry) {
        AudioView *view = (AudioView *)viewRegistry[hippyTag];
        if (view == nil) return ;
        if (![view isKindOfClass:[AudioView class]]) {
            HippyLogError(@"Invalid view returned from registry, expecting AudioView, got: %@", view);
        }
        [view play: src];
    }];
}


HIPPY_EXPORT_METHOD(pause:(nonnull NSNumber *)hippyTag) {
    [self.bridge.uiManager addUIBlock:^(__unused HippyUIManager *uiManager, NSDictionary<NSNumber *,UIView *> *viewRegistry) {
        AudioView *view = (AudioView *)viewRegistry[hippyTag];
        if (view == nil) return ;
        if (![view isKindOfClass:[AudioView class]]) {
            HippyLogError(@"Invalid view returned from registry, expecting AudioView, got: %@", view);
        }
        [view pause];
    }];
}
@end
