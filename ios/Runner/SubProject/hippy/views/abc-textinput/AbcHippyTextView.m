/*!
 * iOS SDK
 *
 * <PERSON><PERSON> is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import "AbcHippyTextView.h"

#import "HippyConvert.h"
#import "HippyText.h"
#import "HippyUtils.h"
#import "HippyTextSelection.h"
#import "UIView+Hippy.h"
#import "AbcInputAccessoryView.h"
#import "AbcHippyTextViewImpl.h"
#import "AbcHippyTextField.h"
#import "AbcCustomKeyboardView.h"

@interface AbcHippyTextView()
@end

@implementation AbcHippyTextView
{
    __weak AbcInputAccessoryView* _accessoryView;
    
    __weak AbcHippyTextField* _textField;
    __weak AbcHippyTextViewImpl* _textView;
    
    __weak AbcCustomKeyboardView* _customKeyboardView;
    
    
}

- (void)setResizeMode:(BOOL)resizeMode {
    _resizeMode = resizeMode;

    [self adjustResizeMode];
}

- (void) adjustResizeMode {
    if (_textView) {
        _textView.resizeMode = self.resizeMode;
    }
    
    if (_textField) {
        _textField.resizeMode = self.resizeMode;
    }
 }

- (void)insertHippySubview:(UIView *)subview atIndex:(NSInteger)atIndex {
    [super insertHippySubview:subview atIndex:atIndex];
    if ([subview isKindOfClass:[AbcInputAccessoryView class]]) {
        _accessoryView = (AbcInputAccessoryView*)subview;
        _accessoryView.textView = _textView.textView;
        _accessoryView.textField = _textField.textView;
        
    }
    else if ([subview isKindOfClass:[AbcHippyTextField class]]) {
        _textField = ((AbcHippyTextField*) subview);
        if(_accessoryView) {
            _accessoryView.textField = _textField.textView;
        }
        if (_customKeyboardView)
            _customKeyboardView.textField = _textField.textView;
        [self adjustResizeMode];
    }
    else if ([subview isKindOfClass:[AbcHippyTextViewImpl class]]) {
        _textView = ((AbcHippyTextViewImpl*) subview);
        if (_accessoryView) {
            _accessoryView.textView = _textView.textView;
        }
        
        if (_customKeyboardView)
            _customKeyboardView.textView = _textView.textView;;
        
        [self adjustResizeMode];
    }
    else if ([subview isKindOfClass:[AbcCustomKeyboardView class]]) {
        _customKeyboardView = (AbcCustomKeyboardView*) subview;
        _customKeyboardView.textView = _textView.textView;
        _customKeyboardView.textField = _textField.textView;
    }
}

- (void)removeHippySubview:(UIView *)subview {
    [super removeHippySubview:subview];
    if ([subview isKindOfClass:[AbcCustomKeyboardView class]]) {
        _customKeyboardView = nil;
    }
}
@end
