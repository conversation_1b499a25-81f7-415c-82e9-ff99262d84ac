#import "AbcCustomKeyboardView.h"
#import "HippyTouchHandler.h"

#import "UIView+Hippy.h"
#import "HippyUIManager.h"

#import <FBKVOController.h>


@interface AbcInputViewHost : UIView
@property (nonatomic, assign) CGFloat intrinsicHeight;
@end

@implementation AbcInputViewHost : UIView
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.translatesAutoresizingMaskIntoConstraints = NO;
    }
    return self;
}
-(CGSize)intrinsicContentSize {
    return CGSizeMake([UIScreen mainScreen].bounds.size.width, self.intrinsicHeight);
}

@end


@interface AbcCustomKeyboardView ()
@property (nonatomic, weak) HippyBridge *bridge;
@end

@implementation AbcCustomKeyboardView {
    AbcInputViewHost* _contentView;
    HippyTouchHandler *_touchHandler;
    FBKVOController* _kvoController;
    
    UITextField* _textField;
    UITextView* _textView;
    
    CGFloat _keyboardHeight;
}


- (CGFloat)keyboardHeight {
    return _keyboardHeight;
}

- (instancetype)initWithBridge:(HippyBridge *)bridge
{
    if (self = [super init]) {
        _bridge = bridge;
        self.clipsToBounds = YES;
        _contentView = [AbcInputViewHost new];
//        _contentView.allowsSelfSizing = YES;
//        _contentView.autoresizingMask = UIViewAutoresizingFlexibleWidth;
        _touchHandler = [[HippyTouchHandler alloc] initWithRootView: _contentView bridge:bridge];
        
        _kvoController = [FBKVOController controllerWithObserver:self];
    }
    
    return self;
}

- (UITextField *)textField {
    return _textField;
}
- (void)setTextField:(UITextField *)textField {
    _textField = textField;
    [self adjustInputView];
}

- (UITextView *)textView {
    return _textView;
}
- (void)setTextView:(UITextView *)textView {
    _textView = textView;
    [self adjustInputView];
}

- (void)setKeyboardHeight:(CGFloat)keyboardHeight {
    _keyboardHeight = keyboardHeight;
    [self adjustInputView];
}

- (void) adjustInputView {
    _contentView.frame = CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, self.keyboardHeight);
    _contentView.intrinsicHeight = self.keyboardHeight;
    
    if (self.window) {
        if (self.textView) {
              self.textView.inputView = _contentView;
//            [self.textView setKeyboardType:UIKeyboardTypeDefault];
            
          }
          if (self.textField) {
              self.textField.inputView = _contentView;
//              [self.textView setKeyboardType:UIKeyboardTypeDefault];
          }
    }
    else {
        if (self.textView)
            self.textView.inputView = nil;
        if (self.textField)
            self.textField.inputView = nil;
    }
}

- (void)insertHippySubview:(UIView *)subview atIndex:(NSInteger)atIndex
{
    HippyAssert(atIndex == 0, @"AbcCustomKeyboardView view can only have one subview");
    [super insertHippySubview:subview atIndex:atIndex];
    
    [subview addGestureRecognizer:_touchHandler];
    [_contentView insertSubview:subview atIndex:atIndex];
    
    [_kvoController observe:subview keyPath:@"frame" options:0 block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSKeyValueChangeKey,id> * _Nonnull change) {
       CGRect newFrame = [[object valueForKeyPath:@"frame"] CGRectValue];
        if (!CGSizeEqualToSize(_contentView.frame.size, newFrame.size)) {
            _contentView.frame = CGRectMake(0, newFrame.origin.y, newFrame.size.width, self.keyboardHeight);
            _contentView.intrinsicHeight = self.keyboardHeight;
            [_contentView invalidateIntrinsicContentSize];
        }
    }];
}

- (void)removeHippySubview:(UIView *)subview
{
    [super removeHippySubview:subview];
    [subview removeGestureRecognizer:_touchHandler];
}


- (void)notifyForBoundsChange:(CGRect)newBounds
{
    UIView* firstView =_contentView.subviews[0];
    if (firstView) {
        [self.bridge.uiManager setFrame:newBounds forView:firstView];
    }
}

- (void)didUpdateHippySubviews
{
    // Do nothing, as subview (singular) is managed by `insertHippySubview:atIndex:`
}


- (void)invalidate
{
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.textView setInputView:nil];
        [self.textField setInputView:nil];
    });
}

- (void)didMoveToWindow
{
    [super didMoveToWindow];
    [self adjustInputView];

    if (self.window) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self notifyForBoundsChange: CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, NAN)];
        });
    }
}
@end
