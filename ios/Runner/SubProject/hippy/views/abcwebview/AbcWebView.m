/*!
 * iOS SDK
 *
 * <PERSON><PERSON> is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import "AbcWebView.h"
#import "HippyAssert.h"
#import "HippyUtils.h"

@implementation AbcWebView

- (instancetype)init {
    WKUserContentController* userContentController = [[WKUserContentController alloc] init];
    
    [self reigsterCookies:userContentController];
    WKWebViewConfiguration* configuration = [[WKWebViewConfiguration alloc] init];
    
    [configuration.preferences setJavaScriptEnabled: YES];
    [configuration.preferences setJavaScriptEnabled: YES];
    configuration.userContentController = userContentController;
    [self updateAutoMediaPlaybackPolicy:configuration];

    self = [super initWithFrame:CGRectZero configuration:configuration];
    
    
    if (self) {
        self.UIDelegate = self;
        self.navigationDelegate = self;
        
    }
    return self;
}

- (void)setSource:(NSDictionary *)source {
    _source = source;
    
    
    if (source) {
        if([source[@"uri"] isKindOfClass:[NSString class]]) {
            NSString *urlString = source[@"uri"];
            [self loadUrl:urlString];
        } else if([source[@"html"] isKindOfClass:[NSString class]]) {
            NSString *htmlString = source[@"html"];
            [self loadHtmlFragment: htmlString];
        }
    }
    
    
}

- (void)loadUrl:(NSString *)urlString  {
    _url = urlString;
    NSURL *url = HippyURLWithString(urlString, NULL);
    if (!url) {
        HippyFatal(HippyErrorWithMessage(@"Error in [HippyWebview setUrl]: illegal url"));
        return;
    }
    NSURLRequest *request = [NSURLRequest requestWithURL:url];
    [self loadRequest:request];
}

- (void)loadHtmlFragment:(NSString *)htmlString {
    NSURL *baseUrl = HippyURLWithString(_source[@"baseUrl"], NULL);
    
    [self loadHTMLString:htmlString baseURL:baseUrl];
}

- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation {
    if (_onLoadStart) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithCapacity:1];
        NSString *url = [[webView URL] absoluteString];
        if (url) {
            [dic setObject:url forKey:@"url"];
        }
        _onLoadStart(dic);
    }
}

- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithCapacity:2];
    NSString *url = [[webView URL] absoluteString];
    if (url) {
        [dic setObject:url forKey:@"url"];
    }
    if (_onLoad) {
        _onLoad(dic);
    }
    if (_onLoadEnd) {
        [dic setObject:@(YES) forKey:@"success"];
        _onLoadEnd(dic);
    }
}

- (void)webView:(WKWebView *)webView didFailNavigation:(WKNavigation *)navigation withError:(NSError *)error {
    if (_onLoadEnd) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithCapacity:3];
        NSString *url = [[webView URL] absoluteString];
        NSString *errString = [error localizedFailureReason];
        if (url) {
            [dic setObject:url forKey:@"url"];
        }
        if (errString) {
            [dic setObject:errString forKey:@"error"];
        }
        [dic setObject:@(NO) forKey:@"success"];
        _onLoadEnd(dic);
    }
}

- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation withError:(NSError *)error {
    if (_onLoadEnd) {
        NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithCapacity:3];
        NSString *url = [[webView URL] absoluteString];
        NSString *errString = [error localizedFailureReason];
        if (url) {
            [dic setObject:url forKey:@"url"];
        }
        if (errString) {
            [dic setObject:errString forKey:@"error"];
        }
        [dic setObject:@(NO) forKey:@"success"];
        _onLoadEnd(dic);
    }
}

- (WKWebView *)webView:(WKWebView *)webView createWebViewWithConfiguration:(WKWebViewConfiguration *)configuration forNavigationAction:(WKNavigationAction *)navigationAction windowFeatures:(WKWindowFeatures *)windowFeatures {
    [webView loadRequest:navigationAction.request];
    return nil;
}

//拦截当前url
- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler {
    
    NSURLRequest *request = navigationAction.request;
    NSString *absoluteString = [navigationAction.request.URL.absoluteString stringByRemovingPercentEncoding];
    
    if ([absoluteString hasPrefix:@"https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb"] && ![absoluteString containsString:@"redirect_url=www.abcyun.cn://"]) {
        decisionHandler(WKNavigationActionPolicyCancel);
        NSString *redirectUrl = nil;
        if ([absoluteString containsString:@"redirect_url="]) {
            NSRange redirectRange = [absoluteString rangeOfString:@"redirect_url"];
            //原始redirect url
            NSString *originalRedirectUrl = [absoluteString substringFromIndex:(redirectRange.location + redirectRange.length + 1)];
            
            //拼接redirect参数部份
            NSString* abcyunRedirectUrlParam  = [NSString stringWithFormat:@"www.abcyun.cn://webview?url=%@", originalRedirectUrl];
            
            //对redirect参数部份进行encodeURIComponent编码
            NSCharacterSet *encodeSet = [NSCharacterSet characterSetWithCharactersInString:@":/?&=;+!@#$()',*% "].invertedSet;
            NSString* abcyunRedirectUrlParamEncoded =[abcyunRedirectUrlParam stringByAddingPercentEncodingWithAllowedCharacters:encodeSet];
            
            //拼接跳转地址
            NSString* abcyunRedirectUrl = [NSString stringWithFormat: @"redirect_url=%@", abcyunRedirectUrlParamEncoded];
            redirectUrl = [[absoluteString substringToIndex:redirectRange.location] stringByAppendingString: abcyunRedirectUrl];
        }
        NSMutableURLRequest *newRequest = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:redirectUrl] cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:30];
        newRequest.allHTTPHeaderFields = request.allHTTPHeaderFields;
        newRequest.URL = [NSURL URLWithString:redirectUrl];
        [webView loadRequest:newRequest];
        return;
    }

    if ([absoluteString hasPrefix:@"weixin://"]) {
        decisionHandler(WKNavigationActionPolicyAllow);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if ([[UIApplication sharedApplication] canOpenURL:navigationAction.request.URL]) {
                [[UIApplication sharedApplication] openURL:navigationAction.request.URL];
            }
        });
        return;
    }
    
    decisionHandler(WKNavigationActionPolicyAllow);
    return;
}





- (void)updateAutoMediaPlaybackPolicy:(WKWebViewConfiguration*)configuration {
    if (@available(iOS 10.0, *)) {
        configuration.mediaTypesRequiringUserActionForPlayback = WKAudiovisualMediaTypeNone;
    } else {
        configuration.mediaPlaybackRequiresUserAction = false;
    }
}

- (void)reigsterCookies:(WKUserContentController*)userContentController {
    WKUserScript * cookieScript = [[WKUserScript alloc] initWithSource:[self cookieJSString] injectionTime:WKUserScriptInjectionTimeAtDocumentStart forMainFrameOnly:NO];
    [userContentController addUserScript:cookieScript];
}


- (NSString *)cookieJSString {
    NSMutableString *script = [NSMutableString string];
    [script appendString:@"var cookieNames = document.cookie.split('; ').map(function(cookie) { return cookie.split('=')[0] } );\n"];
    for (NSHTTPCookie *cookie in [[NSHTTPCookieStorage sharedHTTPCookieStorage] cookies]) {
        
        if ([cookie.value rangeOfString:@"'"].location != NSNotFound) {
            continue;
        }
        
        [script appendFormat:@"if (cookieNames.indexOf('%@') == -1) { document.cookie='%@'; };\n", cookie.name, [self da_javascriptString:cookie]];
    }
    return script;
}

- (NSString*)da_javascriptString:(NSHTTPCookie*) cookie{
    NSString*string = [NSString stringWithFormat:@"%@=%@;domain=%@;path=%@",cookie.name,cookie.value,cookie.domain,cookie.path ?:@"/"];
    if(cookie.secure) {
        string = [string stringByAppendingString:@";secure=true"];
    }
    
    return string;
}

@end
