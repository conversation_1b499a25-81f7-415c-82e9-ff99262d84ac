//
//  SVGManager.h
//  Runner
//
//  Created by f<PERSON><PERSON> on 2022/10/11.
//  Copyright © 2022 The Chromium Authors. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "Singleton.h"

#import "SVGKImage.h"


NS_ASSUME_NONNULL_BEGIN

typedef void (^SVGGetImageCallback)(SVGKImage* image);

@interface SVGManager : NSObject
AS_SINGLETON(SVGManager);

- (void) svgImage: (NSString* )svgContent callback: (SVGGetImageCallback) callback;

@end

NS_ASSUME_NONNULL_END
