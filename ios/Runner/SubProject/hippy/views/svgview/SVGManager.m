//
//  SVGManager.m
//  Runner
//
//  Created by f<PERSON><PERSON> on 2022/10/11.
//  Copyright © 2022 The Chromium Authors. All rights reserved.
//

#import "SVGManager.h"
#import "LruCache.h"
#import "ABCMacros.h"




@interface SVGManager()
@property (nonatomic, strong) LruCache* svgCache;
@end

@implementation SVGManager




DEF_SINGLETON(SVGManager);



const char* SVGDecodeQueueName = "com.abcyun.svgdecodequeue";


dispatch_queue_t SVGDecodeQueue(void) {
    static dispatch_queue_t shadowQueue;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        if ([NSOperation instancesRespondToSelector:@selector(qualityOfService)]) {
            dispatch_queue_attr_t attr = dispatch_queue_attr_make_with_qos_class(DISPATCH_QUEUE_SERIAL, QOS_CLASS_USER_INTERACTIVE, 0);
            shadowQueue = dispatch_queue_create(SVGDecodeQueueName, attr);
        } else {
            shadowQueue = dispatch_queue_create(SVGDecodeQueueName, DISPATCH_QUEUE_SERIAL);
            dispatch_set_target_queue(shadowQueue, dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_HIGH, 0));
        }
    });
    return shadowQueue;
}

- (id) init {
    self = [super init];
    if (self) {
        self.svgCache = [[LruCache alloc] initWithMaxSize:32];
    }
    
    return self;
}


- (void) svgImage: (NSString* )svgContent callback: (SVGGetImageCallback) callback {
    DefineWeakSelfBeforeBlock();
    
    dispatch_async(SVGDecodeQueue(), ^{
        DefineStrongSelfInBlock(sself);
        SVGKImage* svgImage = [_svgCache get:svgContent];
        if (svgImage == nil && svgContent.length) {
            svgImage = [SVGKImage imageWithData:[svgContent dataUsingEncoding:NSUTF8StringEncoding]];
            [sself.svgCache put:svgContent value:svgImage];
        }
        
        if (callback) callback(svgImage);
    });
}

@end
