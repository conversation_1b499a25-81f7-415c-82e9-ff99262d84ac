/*!
* iOS SDK
*
* <PERSON><PERSON> is pleased to support the open source community by making
* Hippy available.
*
* Copyright (C) 2019 THL A29 Limited, a Tencent company.
* All rights reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

#import "WillPopListener.h"
#import "HippyUIManager.h"
#import "UIView+ABCExt.h"
#import "ABCNavigatorItem.h"
#import "IBackListener.h"
@interface WillPopListener ()<IBackListener>
@end

@implementation WillPopListener

- (instancetype)initWithBridge:(HippyBridge *)bridge
{
    if (self = [super init]) {
        self.bridge = bridge;
    }
    
    return self;
}

- (void)didMoveToWindow {
    [super didMoveToWindow];
    ABCNavigatorItem* naviItem = (ABCNavigatorItem*)[self nearestSupperViewWithClass:[ABCNavigatorItem class]];
    if (!naviItem) return;

    if (self.window)  {
        [naviItem addBackListener:self];
    }
    else {
        [naviItem removeBackListener:self];
    }
}

- (BOOL) handleBack:(BOOL) fromEdgeGesture {
    if (self.onWillPop) {
        self.onWillPop(@{
            @"fromEdgeGesture": @(fromEdgeGesture)
        });
    }
    
    return true;
}

@end
