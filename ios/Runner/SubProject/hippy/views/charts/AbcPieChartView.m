//
//  AbcPieChartView.m
//  Runner
//
//  Created by 邓杰 on 2021/6/28.
//  Copyright © 2021 The Chromium Authors. All rights reserved.
//

#import "AbcPieChartView.h"
#import "UIView+Hippy.h"
#import "HippyLog.h"
#import "UIView+ABCAnimation.h"
#import "ABCViewAnimationController.h"
#import <FBKVOController.h>
#import <Charts-Swift.h>
#import "HippyFont.h"
#import "HippyConvert.h"
#import "DateValueFormatter.h"

@interface AbcPieChartView()<ChartViewDelegate>
@property (nonatomic, strong)  PieChartView* chartView;



@end

@implementation AbcPieChartView{
    HippyBridge *_bridge;
    DateValueFormatter* _valueFormat;
}

#pragma mark life cycle
- (instancetype) initWithBridge:(HippyBridge *)bridge props:(nonnull NSDictionary *)props{
    self = [super init];
    if (self) {
        _chartView = [[PieChartView alloc] init];
        _chartView.autoresizingMask = UIViewAutoresizingFlexibleHeight |
        UIViewAutoresizingFlexibleWidth;
        [self addSubview:_chartView];
    
        //设置偏移
        [_chartView setExtraOffsetsWithLeft:0 top:0 right:0 bottom:0];
    
        //无内容显示
        _chartView.noDataText = @"无内容显示";
        
        //关闭描述
        _chartView.chartDescription.enabled = NO;
        _chartView.chartDescription.text = @"tiny`s barChart demo";
        
        //关闭图例
        _chartView.legend.enabled = NO;
        
        //将数据转换为百分比
        _chartView.usePercentValuesEnabled = YES;
        
        //惯性
        _chartView.dragDecelerationFrictionCoef = 0.5;  //0 1 惯性
        
        //设置中间文字
        _chartView.drawCenterTextEnabled = NO;
//        _chartView.centerText = @"我是中间文字";
    
        /* 设置饼状图中间的同心圆 */
        _chartView.drawHoleEnabled = YES; //饼状图是否是空心圆,设置为NO之后，半透明空心圆也消失咯
        _chartView.holeRadiusPercent = 0.8;//第一个空心圆半径占比
        _chartView.holeColor = [UIColor clearColor];//第一个空心圆颜色
        _chartView.transparentCircleRadiusPercent = 1;//第二个空心圆半径占比，半径占比和第一个空心圆半径占比设置为一样的时候，只有一个圆咯
        _chartView.transparentCircleColor = [UIColor clearColor];//第二个空心圆颜色
    
        //显示扇形区域文字
        _chartView.drawEntryLabelsEnabled = NO;
        
        //可以旋转
        _chartView.rotationEnabled = NO;
        
        //扇区可点击
        _chartView.highlightPerTapEnabled = YES;
        
        //代理
        _chartView.delegate = self;
    }
    return self;
}

-(void)setData:(NSArray *)data {
    _data = data;

    if (_data.count != 0) {
        
        NSMutableArray *values = [NSMutableArray array];
        NSMutableArray<UIColor*> *colors = [NSMutableArray array];
        for (NSInteger i = 0; i < _data.count; ++i) {
            NSDictionary* rawDataSet = [_data objectAtIndex:i];
            NSString* label = [rawDataSet objectForKey:@"label"];
            NSNumber* rawValue = [rawDataSet objectForKey:@"value"];
            NSString* color = [rawDataSet objectForKey:@"color"];
            if (color != nil) {
                [colors addObject:[HippyConvert UIColor:color]];
            }
            [values addObject:[[PieChartDataEntry alloc] initWithValue:[rawValue integerValue] label:label data:rawDataSet]];
        }
        
        PieChartDataSet *set = [[PieChartDataSet alloc] initWithEntries:values label:@"Pie DataSet"];
        //颜色(每个扇形区域可以单独设置颜色)
        set.colors = colors;
        set.entryLabelFont = [UIFont systemFontOfSize:20];
        set.entryLabelColor = [UIColor blackColor];
        set.drawIconsEnabled = NO;
        
        //    当饼状图带折线时，dataSet.yValuePosition 数值的位置只有设置为
        //    PieChartValuePositionOutsideSlice，折线才会显示，valueLine相关属性才有用
        set.drawValuesEnabled = NO;
        set.valueFont = [UIFont systemFontOfSize:20];
        set.valueColors = @[UIColor.redColor,UIColor.blueColor,UIColor.cyanColor];
        set.yValuePosition = PieChartValuePositionOutsideSlice;
        set.valueLineColor = UIColor.greenColor;
        //格式化
//        NSNumberFormatter *pFormatter = [[NSNumberFormatter alloc] init];
//        pFormatter.numberStyle = NSNumberFormatterPercentStyle;
//        pFormatter.maximumFractionDigits = 1;
//        pFormatter.multiplier = @1.f;
//        pFormatter.percentSymbol = @" %";
//        set.valueFormatter = [[ChartDefaultValueFormatter alloc] initWithFormatter:pFormatter];

        //相邻区块之间的间距
        set.sliceSpace = 5;
        //扇形区域放大范围
        set.selectionShift = 0;
        //动画开始的角度
        _chartView.rotationAngle = -90.0;//动画开始时的角度在-90.0度
        
        
        _chartView.data = [[PieChartData alloc] initWithDataSet:set];
        [_chartView animateWithXAxisDuration:2.0f easingOption:ChartEasingOptionEaseOutExpo];//设置动画效果
    }
}

-(void) setRotationEnabled:(BOOL)rotationEnabled {
    [_chartView setRotationEnabled:rotationEnabled];
}

-(void) setDrawValuesEnabled:(BOOL)drawValuesEnabled {
    [_chartView.data setDrawValues:drawValuesEnabled];
}

-(void) setDrawEntryLabelsEnabled:(BOOL)drawEntryLabelsEnabled{
    [_chartView setDrawEntryLabelsEnabled:drawEntryLabelsEnabled];
}

-(void) setHighlightPerTapEnabled:(BOOL)highlightPerTapEnabled {
    [_chartView setHighlightPerTapEnabled:highlightPerTapEnabled];
}

-(void) setSliceSpace:(NSNumber *)sliceSpace {
    if ([_chartView.data isKindOfClass:[PieChartDataSet class]]) {
        [((PieChartDataSet*) (_chartView.data)) setSliceSpace:[sliceSpace doubleValue]];
    }
}

-(void) setSelectionShift:(NSNumber *)selectionShift {
    if ([_chartView.data isKindOfClass:[PieChartDataSet class]]) {
        [((PieChartDataSet*) (_chartView.data)) setSelectionShift:[selectionShift doubleValue]];
    }
}

- (void)chartValueSelected:(ChartViewBase *)chartView entry:(ChartDataEntry *)entry highlight:(ChartHighlight *)highlight{
    if (self.onSelectChanged) {
        self.onSelectChanged(entry.data);
    }
}

#pragma mark hippy native methods

- (void)didUpdateHippySubviews {
    [super didUpdateHippySubviews];
}
@end
