//
//  AbcPieChartView.h
//  Runner
//
//  Created by 邓杰 on 2021/6/28.
//  Copyright © 2021 The Chromium Authors. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "HippyBridge.h"
#import "HippyView.h"

@interface AbcPieChartView : UIView

@property (nonatomic, strong) HippyDirectEventBlock onSelectChanged;

@property (nonatomic, strong) NSArray* data;
@property (nonatomic, assign) BOOL drawValuesEnabled;
@property (nonatomic, assign) BOOL drawEntryLabelsEnabled;
@property (nonatomic, assign) BOOL rotationEnabled;
@property (nonatomic, assign) BOOL highlightPerTapEnabled;

@property (nonatomic, strong) NSNumber* pieChartPercent;
@property (nonatomic, strong) NSNumber* sliceSpace;
@property (nonatomic, strong) NSNumber* selectionShift;





- (instancetype) initWithBridge:(HippyBridge *)bridge props:(nonnull NSDictionary *)props;

@end
