/*!
 * iOS SDK
 *
 * <PERSON><PERSON> is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import "AbcLineChartView.h"
#import "UIView+Hippy.h"
#import "HippyLog.h"
#import "UIView+ABCAnimation.h"
#import "ABCViewAnimationController.h"
#import <FBKVOController.h>
#import <Charts-Swift.h>
#import "HippyFont.h"
#import "HippyConvert.h"
#import "DateValueFormatter.h"

@interface AbcLineChartView()<ChartViewDelegate>
@property (nonatomic, strong)  LineChartView *chartView;



@end

@implementation AbcLineChartView{
    HippyBridge *_bridge;
    DateValueFormatter* _valueFormat;
}

#pragma mark life cycle
- (instancetype) initWithBridge:(HippyBridge *)bridge props:(nonnull NSDictionary *)props{
    self = [super init];
    if (self) {
        _chartView = [[LineChartView alloc] init];
        _chartView.autoresizingMask = UIViewAutoresizingFlexibleHeight |
        UIViewAutoresizingFlexibleWidth;
        [self addSubview:_chartView];
        
        _chartView.delegate = self;
        
        _chartView.chartDescription.enabled = NO;
        
        _chartView.dragEnabled = NO;
        [_chartView setScaleEnabled:NO];
        _chartView.pinchZoomEnabled = NO;
        _chartView.drawGridBackgroundEnabled = NO;
        _chartView.highlightPerDragEnabled = YES;
        
        _chartView.backgroundColor = UIColor.whiteColor;
        
        _chartView.legend.enabled = NO;
        
        ChartXAxis *xAxis = _chartView.xAxis;
        xAxis.labelPosition = XAxisLabelPositionBottomInside;
        xAxis.labelFont = [UIFont fontWithName:@"HelveticaNeue-Light" size:10.f];
        xAxis.labelTextColor = [UIColor colorWithRed:1.0 green:0.0 blue:0.0 alpha:1.0];
        xAxis.drawAxisLineEnabled = NO;
        xAxis.drawGridLinesEnabled = YES;
        xAxis.centerAxisLabelsEnabled = NO;
        
        _valueFormat = [[DateValueFormatter alloc] init];
        xAxis.valueFormatter = _valueFormat;
        
        ChartYAxis *leftAxis = _chartView.leftAxis;
        leftAxis.labelPosition = YAxisLabelPositionInsideChart;
        leftAxis.labelFont = [UIFont fontWithName:@"HelveticaNeue-Light" size:12.f];
        leftAxis.drawGridLinesEnabled = YES;
        leftAxis.granularityEnabled = YES;
        
        leftAxis.yOffset = 0;
        leftAxis.labelTextColor = [UIColor colorWithRed:1.0 green:0.0 blue:0.0 alpha:1.0];
        
        _chartView.rightAxis.enabled = NO;
        
        _chartView.legend.form = ChartLegendFormLine;
    }
    return self;
}



- (void)setXAxis:(NSDictionary *)xAxis {
    _xAxis = xAxis;
    
    if (!_xAxis) {
        return;
    }
    
    [self updateAxisWithAttribes:_chartView.xAxis attributes:_xAxis];
}

- (void)setLeftAxis:(NSDictionary *)leftAxis {
    _leftAxis = leftAxis;
    if (!_leftAxis)return;
    
    [self updateAxisWithAttribes:_chartView.leftAxis attributes:_leftAxis];
}


- (void)setData:(NSArray *)data {
    _data = data;
    
    NSMutableArray<LineChartDataSet*>* dataSets = [NSMutableArray arrayWithCapacity:_data.count];
    
    if(_data.count != 0) {
        for (NSInteger i = 0; i < _data.count; ++i) {
            NSDictionary* rawDataSet = [_data objectAtIndex:i];
            NSString* label = [rawDataSet objectForKey:@"label"];
            NSString* axisDependency = [rawDataSet objectForKey:@"axisDependency"];
            NSArray* rawValues = [rawDataSet objectForKey:@"values"];
            NSNumber* lineWidth = [rawDataSet objectForKey:@"lineWidth"];
            NSString* lineColor = [rawDataSet objectForKey:@"lineColor"];
            NSNumber* drawCircles = [rawDataSet objectForKey:@"drawCircles"];
            NSNumber* circleRadius = [rawDataSet objectForKey:@"circleRadius"];
            NSString* circleColor = [rawDataSet objectForKey:@"circleColor"];

            
            
            NSMutableArray *values = [[NSMutableArray alloc] init];
            if (rawValues.count > 0) {
                for (NSInteger i = 0; i < rawValues.count; ++i)
                {
                    NSDictionary* value = [rawValues objectAtIndex:i];
                    CGFloat x = [[value objectForKey:@"x"] floatValue];
                    CGFloat y = [[value objectForKey:@"y"] floatValue];
                    [values addObject:[[ChartDataEntry alloc] initWithX:x y:y]];
                }
            }
            LineChartDataSet* dataSet = [[LineChartDataSet alloc] initWithEntries:values label:label];
            if ([axisDependency isEqualToString:@"left"])
                dataSet.axisDependency = AxisDependencyLeft;
            
            dataSet.valueTextColor = [UIColor colorWithRed:1.0 green:0.0 blue:0.0 alpha:1.0];
            dataSet.lineWidth = 1.5;
            
        
            dataSet.drawCirclesEnabled = [drawCircles boolValue];
            dataSet.circleRadius = [circleRadius floatValue];
            if (circleColor)
                   [dataSet setCircleColor:[HippyConvert UIColor:circleColor]];
            
            dataSet.drawValuesEnabled = NO;
            
            dataSet.fillColor = [UIColor colorWithRed:0 green:1.0 blue:0.0 alpha:1.0];
            dataSet.highlightColor = [UIColor colorWithRed:0 green:0 blue:1.0 alpha:1.0];
            dataSet.drawCircleHoleEnabled = NO;
            
            if (lineColor != nil) {
                [dataSet setColor:[HippyConvert UIColor:lineColor]];
                
            }
            if (lineWidth != nil) {
                dataSet.lineWidth = [lineWidth floatValue];
            }
            
            [dataSets addObject:dataSet];
        }
    }
    
    _chartView.data = [[LineChartData alloc] initWithDataSets:dataSets];
    [_chartView.data notifyDataChanged];
    [_chartView notifyDataSetChanged];
}


- (void) updateAxisWithAttribes:(ChartAxisBase*) axis attributes:(NSDictionary*)attributes {
    if ([axis isKindOfClass:[ChartXAxis class]]) {
        ChartXAxis* xAxis = (ChartXAxis*) axis;
        NSString* position= [attributes objectForKey:@"labelPosition"];
        if ([position isEqualToString:@"topInside"]) {
            xAxis.labelPosition =XAxisLabelPositionTopInside;
        }
        else if ([position isEqualToString:@"bottomInside"]){
            xAxis.labelPosition =XAxisLabelPositionBottomInside;
        }
        else if ([position isEqualToString:@"top"]){
            xAxis.labelPosition =XAxisLabelPositionTop;
        }
        else if ([position isEqualToString:@"bottom"]){
            xAxis.labelPosition =XAxisLabelPositionBottom;
        }
        
        NSDictionary* valueFormatter = [attributes objectForKey:@"valueFormatter"];
        if (valueFormatter) {
            NSString* type = [valueFormatter objectForKey:@"time"];
            NSString* format = [valueFormatter objectForKey:@"format"];
            if ([@"time" isEqualToString:type]) {
                _valueFormat.dateFormat = format;
            }
        }
    }
    
    if ([axis isKindOfClass:[ChartYAxis class]]) {
        ChartYAxis* yAxis = (ChartYAxis*) axis;
        NSString* position= [attributes objectForKey:@"labelPosition"];
        if ([position isEqualToString:@"inside"]) {
            yAxis.labelPosition =YAxisLabelPositionInsideChart;
        }
        else if ([position isEqualToString:@"outside"]){
            yAxis.labelPosition =YAxisLabelPositionOutsideChart;
        }
    }
    
    

    
      NSNumber* axisMinimum = [attributes objectForKey:@"axisMinimum"];
      NSNumber* axisMaximum = [attributes objectForKey:@"axisMaximum"];
      if (axisMinimum) {
          axis.axisMinimum = [axisMinimum floatValue];
      }
      if (axisMaximum) {
          axis.axisMaximum = [axisMaximum floatValue];
      }
    
    NSNumber* granularity = [attributes objectForKey:@"granularity"];
    if (granularity) {
        axis.granularity = [granularity floatValue];
    }

    NSDictionary* labelFontStyle = [attributes objectForKey:@"labelFontStyle"];
    if (labelFontStyle) {
        CGFloat fontSize = 14;
        
        NSNumber* fontSizeNum = [attributes objectForKey:@"fontSize"];
        if (fontSizeNum != nil) {
            fontSize = [fontSizeNum floatValue];
        }
        
        UIFont* font =[UIFont systemFontOfSize:fontSize];
        
        NSString* fontWeight = [labelFontStyle objectForKey:@"fontWeight"];
        if (fontWeight != nil) {
            font = [HippyFont updateFont:font withWeight:fontWeight];
        }
        
        NSString* fontFamiliy = [labelFontStyle objectForKey:@"fontFamily"];
        if (fontFamiliy != nil) {
            font = [HippyFont updateFont:font withFamily:fontFamiliy];
        }
        
        axis.labelFont = font;
        
        NSString* fontColor = [labelFontStyle objectForKey:@"color"];
        if (fontColor != nil) {
            axis.labelTextColor = [HippyConvert UIColor:fontColor];
        }
    }
}

#pragma mark hippy native methods

- (void)didUpdateHippySubviews {
    [super didUpdateHippySubviews];
}
@end
