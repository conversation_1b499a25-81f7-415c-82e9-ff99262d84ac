/*!
* iOS SDK
*
* <PERSON><PERSON> is pleased to support the open source community by making
* Hippy available.
*
* Copyright (C) 2019 THL A29 Limited, a Tencent company.
* All rights reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

#import "CaptureViewManager.h"
#import "CaptureView.h"
//#import "UIView+Hippy.h"
#import "HippyUIManager.h"

@implementation CaptureViewManager
HIPPY_EXPORT_MODULE(CaptureView)

- (UIView *)view {
    return [[CaptureView alloc] initWithBridge:self.bridge];
}



HIPPY_EXPORT_METHOD(captureToFile:(nonnull NSNumber *)hippyTag file:(NSString*)file callback:(HippyResponseSenderBlock)callback) {
    if (file.length == 0) {
        callback([NSArray arrayWithObject:@{
            @"error":@"文件名不能为空"
        }]);
        return;
    }
        
    [self.bridge.uiManager addUIBlock:^(__unused HippyUIManager *uiManager, NSDictionary<NSNumber *,UIView *> *viewRegistry) {
        CaptureView *view = (CaptureView *)viewRegistry[hippyTag];
        if (view == nil) return ;
        if (![view isKindOfClass:[CaptureView class]]) {
            HippyLogError(@"Invalid view returned from registry, expecting AudioView, got: %@", view);
        }
        
        @autoreleasepool {
            UIGraphicsBeginImageContextWithOptions(view.frame.size, NO, [UIScreen mainScreen].scale);
            [view.layer renderInContext:UIGraphicsGetCurrentContext()];
            UIImage *snapshotImage = UIGraphicsGetImageFromCurrentImageContext();
            UIGraphicsEndImageContext();
            
            NSData * binaryImageData = UIImagePNGRepresentation(snapshotImage);
            [binaryImageData writeToFile:file atomically:YES];
        }
        
        callback([NSArray arrayWithObject:@{
            @"result":@YES
        }]);
    }];
}

@end
