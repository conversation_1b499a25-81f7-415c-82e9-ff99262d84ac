/*
 *
 * <PERSON><PERSON> is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2021 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */


#include "core/napi/js_native_turbo.h"
#include "core/napi/jsc/js_native_api_jsc.h"

namespace hippy {
namespace napi {

class ObjcTurboEnv : public TurboEnv {
 public:
  ObjcTurboEnv(std::shared_ptr <Ctx> ctx);
  ~ObjcTurboEnv();

  std::shared_ptr <CtxValue> CreateObject(const std::shared_ptr <HostObject> &hostObject) override;
  std::shared_ptr <CtxValue> CreateFunction(const std::shared_ptr <CtxValue> &name,
                                            int paramCount,
                                            HostFunctionType func) override;
};

}  // namespace napi
}  // namespace hippy
