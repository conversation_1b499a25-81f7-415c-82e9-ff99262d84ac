#import "PermissionHandlerModule.h"
#import "PermissionManager.h"

@implementation PermissionHandlerModule {
    PermissionManager *_Nonnull _permissionManager;
    _Nullable HippyMethodInvoideCallback _methodResult;
}


- (instancetype)init
{
    self = [super init];
    if (self) {
        _permissionManager =  [[PermissionManager alloc] initWithStrategyInstances];;
    }
    return self;
}

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}


HIPPY_EXPORT_MODULE(PermissionHandler)

HIPPY_EXPORT_METHOD(checkPermissionStatus:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    PermissionGroup permission = [Codec decodePermissionGroupFrom:params[@"permissionType"]];
    [PermissionManager checkPermissionStatus:permission result:GeneralHippyInvokeCallbackHandler];
}


HIPPY_EXPORT_METHOD(checkServiceStatus:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    PermissionGroup permission = [Codec decodePermissionGroupFrom:params[@"permissionType"]];
    [PermissionManager checkServiceStatus:permission result:GeneralHippyInvokeCallbackHandler];
}


HIPPY_EXPORT_METHOD(requestPermission:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self requestPermission:params[@"permissionType"] result:GeneralHippyInvokeCallbackHandler];
}


HIPPY_EXPORT_METHOD(shouldShowRequestPermissionRationale:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    resolve(@(NO));
}


HIPPY_EXPORT_METHOD(openAppSettings:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [PermissionManager openAppSettings:GeneralHippyInvokeCallbackHandler];
}



-(void) requestPermission:(NSNumber*) permissionType result:(HippyMethodInvoideCallback)result{
    if (_methodResult != nil) {
        result(nil,InvokeError(-1, @"A request for permissions is already running, please wait for it to finish before doing another request (note that you can request multiple permissions at the same time)."));
    }
    
    _methodResult = result;
    NSArray *permissions = [Codec decodePermissionGroupsFrom:[NSArray arrayWithObject:permissionType]];
    
    [_permissionManager requestPermissions:permissions completion:^(NSDictionary *permissionRequestResults) {
        if (self->_methodResult != nil) {
            self->_methodResult(permissionRequestResults[permissionType], nil);
        }
        
        self->_methodResult = nil;
    }];
}


@end
