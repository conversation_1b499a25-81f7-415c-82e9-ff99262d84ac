//
// Created by <PERSON> on 11/7/17.
//

#import "BarcodeScannerViewController.h"
#import <MTBBarcodeScanner/MTBBarcodeScanner.h>
#import "ScannerOverlay.h"
#import "UIUtils.h"



typedef void (^OnTapCallback)(void);
@interface BarcodeScannerNavigationController()
@end

@implementation BarcodeScannerNavigationController
- (instancetype)initWithRootViewController:(UIViewController *)rootViewController {
    return [super initWithRootViewController:rootViewController];
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleLightContent;
}

- (UIViewController *)childViewControllerForStatusBarStyle {
    return self.topViewController;
}

- (UIViewController *)childViewControllerForStatusBarHidden {
    return self.topViewController;
}

@end


@interface NavigationBar: UIView
@property(retain,nonatomic) UIButton* backBtn;
@property(retain, nonatomic) UILabel* title;
@property(retain, nonatomic) UIButton* rightBtn;
@property(copy) OnTapCallback backTap;
@property(copy) OnTapCallback rightImageTap;
@end


#define constWidth(view, width) [NSLayoutConstraint constraintWithItem:(view) \
attribute:NSLayoutAttributeWidth \
relatedBy:NSLayoutRelationEqual \
toItem:nil \
attribute:NSLayoutAttributeWidth \
multiplier:1 \
constant:(width)]


#define constHeight(view, height) [NSLayoutConstraint constraintWithItem:(view) \
attribute:NSLayoutAttributeHeight \
relatedBy:NSLayoutRelationEqual \
toItem:nil \
attribute:NSLayoutAttributeHeight \
multiplier:1 \
constant:(height)]


#define DefineWeakVarBeforeBlock(var) \
__block __weak __typeof(var) __weak_##var = var

#define DefineStrongVarInBlock(var) \
__typeof(__weak_##var) var = __weak_##var

#define DefineWeakSelfBeforeBlock() \
__weak __typeof(self) __weak_self = self

#define DefineStrongSelfInBlock(strongSelf) \
__typeof(__weak_self) strongSelf = __weak_self

#define IconWidth 20
#define kNavigationBarHeight 48
#define kNavigationPadding 16




@implementation NavigationBar
- (instancetype) initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.translatesAutoresizingMaskIntoConstraints = NO;
        _backBtn = [[UIButton alloc] initWithFrame:CGRectZero];
        [_backBtn setImage: [UIUtils loadImage:@"back"] forState:UIControlStateNormal];
        _backBtn.tintColor = [UIColor whiteColor];
        _backBtn.translatesAutoresizingMaskIntoConstraints = NO;
        [_backBtn addTarget:self action:@selector(_backTap:) forControlEvents:UIControlEventTouchUpInside];
        [_backBtn setHitTestEdgeInsets:UIEdgeInsetsMake(-16, -16, -16, -16)];
        
        [self addSubview:_backBtn];
        
        
        _title = [[UILabel alloc] initWithFrame:frame];
        _title.textAlignment = NSTextAlignmentCenter;
        _title.textColor = [UIColor whiteColor];
        _title.font = [UIFont systemFontOfSize:16];
        _title.translatesAutoresizingMaskIntoConstraints = NO;
        [self addSubview:_title];
        
        NSLog(@"NavigationBar.initWithFrame frame.size.width = %f", frame.size.width);
        _rightBtn = [[UIButton alloc] initWithFrame:CGRectZero];
        _rightBtn.translatesAutoresizingMaskIntoConstraints = NO;
        _rightBtn.tintColor = [UIColor whiteColor];
        _rightBtn.userInteractionEnabled = YES;
        [_rightBtn addTarget:self action:@selector(_rigthBtnTap:) forControlEvents:UIControlEventTouchUpInside];
        [_rightBtn setHitTestEdgeInsets:UIEdgeInsetsMake(-16, -16, -16, -16)];
        
        [self addSubview:_rightBtn];
        
        
        
        [self addConstraint:constWidth(_backBtn, IconWidth)];
        [self addConstraint:constHeight(_backBtn, IconWidth)];
        
        [self addConstraint:[NSLayoutConstraint constraintWithItem:_backBtn
                                                         attribute:NSLayoutAttributeHeight
                                                         relatedBy:NSLayoutRelationEqual
                                                            toItem:nil
                                                         attribute:NSLayoutAttributeHeight
                                                        multiplier:1
                                                          constant:IconWidth]];
        
        [self addConstraint:[NSLayoutConstraint constraintWithItem:_backBtn
                                                         attribute:NSLayoutAttributeCenterY
                                                         relatedBy:NSLayoutRelationEqual
                                                            toItem:self
                                                         attribute:NSLayoutAttributeCenterY
                                                        multiplier:1
                                                          constant:0]];
        [self addConstraint:[NSLayoutConstraint constraintWithItem:_backBtn
                                                         attribute:NSLayoutAttributeLeft
                                                         relatedBy:NSLayoutRelationEqual
                                                            toItem:self
                                                         attribute:NSLayoutAttributeLeft
                                                        multiplier:1
                                                          constant:kNavigationPadding]];
        
        
        
        [self addConstraint:[NSLayoutConstraint constraintWithItem:_title
                                                         attribute:NSLayoutAttributeCenterX
                                                         relatedBy:NSLayoutRelationEqual
                                                            toItem:self
                                                         attribute:NSLayoutAttributeCenterX
                                                        multiplier:1
                                                          constant:0]];
        
        [self addConstraint:[NSLayoutConstraint constraintWithItem:_title
                                                         attribute:NSLayoutAttributeCenterY
                                                         relatedBy:NSLayoutRelationEqual
                                                            toItem:self
                                                         attribute:NSLayoutAttributeCenterY
                                                        multiplier:1
                                                          constant:0]];
        
        
         [self addConstraint:constWidth(_rightBtn, IconWidth)];
        [self addConstraint:constHeight(_rightBtn, IconWidth)];
        [self addConstraint:[NSLayoutConstraint constraintWithItem:_rightBtn
                                                         attribute:NSLayoutAttributeCenterY
                                                         relatedBy:NSLayoutRelationEqual
                                                            toItem:self
                                                         attribute:NSLayoutAttributeCenterY
                                                        multiplier:1
                                                          constant:0]];
        
        [self addConstraint:[NSLayoutConstraint constraintWithItem:_rightBtn
                                                         attribute:NSLayoutAttributeRight
                                                         relatedBy:NSLayoutRelationEqual
                                                            toItem:self
                                                         attribute:NSLayoutAttributeRight
                                                        multiplier:1
                                                          constant:-kNavigationPadding]];
    }
    
    return self;
}


- (void) _backTap:(id)view {
    NSLog(@"_backTap");
    if (self.backTap != nil) {
        self.backTap();
    }
}

- (void) _rigthBtnTap:(id) view {
    if (self.rightImageTap != nil) {
        self.rightImageTap();
    }
}
@end


@interface BarcodeScannerViewController()
@property(nonatomic, retain) NavigationBar *navigationBar;
@property (nonatomic, retain) UILabel* selectTipsLabel;
@property (nonatomic, retain) UILabel* selectCountLablel;
@property (nonatomic, retain) NSLayoutConstraint* navigationBarTopConstraint;
@end

@implementation BarcodeScannerViewController


- (void) loadView {
    [super loadView];
}


- (void) prventEventToBellow:(id)view {
    
}
- (void)viewDidLoad {
    [super viewDidLoad];
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(prventEventToBellow:)];
    [self.view addGestureRecognizer:tap];
    
    if (@available(iOS 11.0, *)) {
        self.view.insetsLayoutMarginsFromSafeArea = NO;
    } else {
        // Fallback on earlier versions
    }
    self.navigationController.navigationBarHidden = YES;
    //    self.view.backgroundColor=[UIColor blueColor];
    NSLog(@"viewDidLoad = viewWidth = %f", self.view.bounds.size.width);
    self.previewView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.bounds.size.width, self.view.bounds.size.height)];
    self.previewView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:_previewView];
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem:_previewView attribute:NSLayoutAttributeBottom relatedBy:NSLayoutRelationEqual toItem:self.view attribute:NSLayoutAttributeBottom multiplier:1.0 constant:0]];
    
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem:_previewView attribute:NSLayoutAttributeCenterX relatedBy:NSLayoutRelationEqual toItem:self.view attribute:NSLayoutAttributeCenterX multiplier:1.0 constant:0]];
    
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem:_previewView attribute:NSLayoutAttributeCenterY relatedBy:NSLayoutRelationEqual toItem:self.view attribute:NSLayoutAttributeCenterY multiplier:1.0 constant:0]];
    
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem:_previewView attribute:NSLayoutAttributeWidth relatedBy:NSLayoutRelationEqual toItem:self.view attribute:NSLayoutAttributeWidth multiplier:1.0 constant:0]];
    
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem:_previewView attribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:self.view attribute:NSLayoutAttributeHeight multiplier:1.0 constant:0]];
    
    self.scanRect = [[ScannerOverlay alloc] initWithFrame:self.view.bounds];
    self.scanRect.translatesAutoresizingMaskIntoConstraints = NO;
    self.scanRect.backgroundColor = UIColor.clearColor;
    [self.view addSubview:_scanRect];
    
    
    
    [self.view addConstraints:[NSLayoutConstraint
                               constraintsWithVisualFormat:@"V:[scanRect]"
                               options:NSLayoutFormatAlignAllBottom
                               metrics:nil
                               views:@{@"scanRect": _scanRect}]];
    [self.view addConstraints:[NSLayoutConstraint
                               constraintsWithVisualFormat:@"H:[scanRect]"
                               options:NSLayoutFormatAlignAllBottom
                               metrics:nil
                               views:@{@"scanRect": _scanRect}
                               
                               ]];
    [_scanRect startAnimating];
    self.scanner = [[MTBBarcodeScanner alloc] initWithPreviewView:_previewView];
    
    [self buildNavigationBar];
    [self buildScanTipsView];
    [self updateFlashButton];
    
    [self setNeedsStatusBarAppearanceUpdate];
}

- (void) buildNavigationBar {
    self.navigationBar = [[NavigationBar alloc] initWithFrame:CGRectMake(0, 0, self.view.bounds.size.width, kNavigationBarHeight)];
    [self.view addSubview:self.navigationBar];
    
    
    self.navigationBarTopConstraint =[NSLayoutConstraint constraintWithItem: self.navigationBar
                                                                  attribute:NSLayoutAttributeTop
                                                                  relatedBy:NSLayoutRelationEqual
                                                                     toItem:self.view
                                                                  attribute:NSLayoutAttributeTop
                                                                 multiplier:1
                                                                   constant:55];
    
    [self.view addConstraint:self.navigationBarTopConstraint];
    
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem: self.navigationBar
                                                          attribute:NSLayoutAttributeWidth
                                                          relatedBy:NSLayoutRelationEqual
                                                             toItem:self.view
                                                          attribute:NSLayoutAttributeWidth
                                                         multiplier:1
                                                           constant:0]];
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem: self.navigationBar
                                                          attribute:NSLayoutAttributeHeight
                                                          relatedBy:NSLayoutRelationEqual
                                                             toItem:nil
                                                          attribute:NSLayoutAttributeHeight
                                                         multiplier:1
                                                           constant:kNavigationBarHeight]];
    
    
    self.navigationBar.title.text = self.title;
    DefineWeakSelfBeforeBlock();
    self.navigationBar.backTap =^(){
        [__weak_self cancel];
    };
    
    self.navigationBar.rightImageTap = ^(){
        [__weak_self toggle];
    };
    
};

-(void) buildScanTipsView {
    if (self.selectedTips.length == 0)
        return;
    
    UIView*  scanTipsContainer = [[UIView alloc] init];
    scanTipsContainer.userInteractionEnabled = true;
    scanTipsContainer.translatesAutoresizingMaskIntoConstraints = NO;
    scanTipsContainer.layer.backgroundColor = [UIColor colorWithRed:1.0 green:1.0 blue:1.0 alpha:1.0].CGColor;
    scanTipsContainer.layer.cornerRadius = 16;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onScanTipsTap:)];
    [scanTipsContainer addGestureRecognizer:tap];
    [self.view addSubview:scanTipsContainer];
    
    self.selectTipsLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    self.selectTipsLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.selectTipsLabel.text = self.selectedTips;
    self.selectTipsLabel.textColor = [UIColor blackColor];
    self.selectTipsLabel.font = [UIFont systemFontOfSize:12];
    [scanTipsContainer addSubview:self.selectTipsLabel];
    
    self.selectCountLablel = [[UILabel alloc] initWithFrame:CGRectZero];
    self.selectCountLablel.translatesAutoresizingMaskIntoConstraints = NO;
    self.selectCountLablel.text = [NSString stringWithFormat:@"%lu", (unsigned long)self.selectedCount];
    self.selectCountLablel.textColor = [UIColor redColor];
    if (@available(iOS 8.2, *)) {
        self.selectCountLablel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightMedium];
    } else {
        // Fallback on earlier versions
    }
    [scanTipsContainer addSubview:self.selectCountLablel];
    
    UILabel* type = [[UILabel alloc] initWithFrame:CGRectZero];
    type.translatesAutoresizingMaskIntoConstraints = NO;
    type.text = @"种";
    type.textColor =[UIColor blackColor];
    type.font = [UIFont systemFontOfSize:12];
    [scanTipsContainer addSubview:type];
    
    
    [scanTipsContainer addConstraint:[NSLayoutConstraint constraintWithItem:self.selectTipsLabel attribute:NSLayoutAttributeLeft relatedBy:NSLayoutRelationEqual toItem:scanTipsContainer attribute:NSLayoutAttributeLeft multiplier:1.0 constant: 16]];
    [scanTipsContainer addConstraint:[NSLayoutConstraint constraintWithItem:self.selectTipsLabel attribute:NSLayoutAttributeCenterY relatedBy:NSLayoutRelationEqual toItem:scanTipsContainer attribute:NSLayoutAttributeCenterY multiplier:1.0 constant:0]];
    
    
    [scanTipsContainer addConstraint:[NSLayoutConstraint constraintWithItem:self.selectCountLablel attribute:NSLayoutAttributeLeft relatedBy:NSLayoutRelationEqual toItem:self.selectTipsLabel attribute:NSLayoutAttributeRight multiplier:1.0 constant:10]];
    
    [scanTipsContainer addConstraint:[NSLayoutConstraint constraintWithItem:self.selectCountLablel attribute:NSLayoutAttributeCenterY relatedBy:NSLayoutRelationEqual toItem:scanTipsContainer attribute:NSLayoutAttributeCenterY multiplier:1.0 constant:0]];
    
    [scanTipsContainer addConstraint:[NSLayoutConstraint constraintWithItem:type attribute:NSLayoutAttributeLeft relatedBy:NSLayoutRelationEqual toItem:self.selectCountLablel attribute:NSLayoutAttributeRight multiplier:1.0 constant:10]];
    
    [scanTipsContainer addConstraint:[NSLayoutConstraint constraintWithItem:type attribute:NSLayoutAttributeCenterY relatedBy:NSLayoutRelationEqual toItem:scanTipsContainer attribute:NSLayoutAttributeCenterY multiplier:1.0 constant:0]];
    
    [scanTipsContainer addConstraint:[NSLayoutConstraint constraintWithItem:type attribute:NSLayoutAttributeRight relatedBy:NSLayoutRelationEqual toItem:scanTipsContainer attribute:NSLayoutAttributeRight multiplier:1.0 constant: -16]];
    
    [self.view addConstraint: constHeight(scanTipsContainer, 32)];
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem:scanTipsContainer attribute:NSLayoutAttributeCenterX relatedBy:NSLayoutRelationEqual toItem:self.view attribute:NSLayoutAttributeCenterX multiplier:1 constant:0]];
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem:scanTipsContainer attribute:NSLayoutAttributeBottom relatedBy:NSLayoutRelationEqual toItem:self.view attribute:NSLayoutAttributeBottom multiplier:1 constant:-150]];
}

- (void) onScanTipsTap:(id) view {
    [self.delegate barcodeScannerViewController:self didOnViewScanListTap:nil];
    [self dismissViewControllerAnimated:true completion:nil];
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleLightContent;
}

- (BOOL)prefersStatusBarHidden {
    return NO;
}


- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    if (self.scanner.isScanning) {
        [self.scanner stopScanning];
    }
    [MTBBarcodeScanner requestCameraPermissionWithSuccess:^(BOOL success) {
        if (success) {
            [self startScan];
        } else {
            [self.delegate barcodeScannerViewController:self didFailWithErrorCode:@"PERMISSION_NOT_GRANTED"];
            [self dismissViewControllerAnimated:NO completion:nil];
        }
    }];
    
    [self setNeedsStatusBarAppearanceUpdate];
}

- (void)viewWillDisappear:(BOOL)animated {
    [self.scanner stopScanning];
    [super viewWillDisappear:animated];
    if ([self isFlashOn]) {
        [self toggleFlash:NO];
    }
}

- (void)startScan {
    NSError *error;
    [self.scanner startScanningWithResultBlock:^(NSArray<AVMetadataMachineReadableCodeObject *> *codes) {
        [self.scanner stopScanning];
        AVMetadataMachineReadableCodeObject *code = codes.firstObject;
        if (code) {
            [self.delegate barcodeScannerViewController:self didScanBarcodeWithResult:code.stringValue];
            [self dismissViewControllerAnimated:NO completion:nil];
        }
    } error:&error];
}

- (void)cancel {
    [self dismissViewControllerAnimated:true completion:nil];
    [self.delegate barcodeScannerViewController:self didCancel:nil];
}

- (void)updateFlashButton {
    if (!self.hasTorch) {
        return;
    }
    [self.navigationBar.rightBtn  setImage:[UIUtils loadImage:self.isFlashOn?@"flash_on":@"flash_off"] forState:UIControlStateNormal];
}

- (void)toggle {
    [self toggleFlash:!self.isFlashOn];
    [self updateFlashButton];
}

- (BOOL)isFlashOn {
    AVCaptureDevice *device = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    if (device) {
        return device.torchMode == AVCaptureFlashModeOn || device.torchMode == AVCaptureTorchModeOn;
    }
    return NO;
}

- (BOOL)hasTorch {
    AVCaptureDevice *device = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    if (device) {
        return device.hasTorch;
    }
    return false;
}

- (void)toggleFlash:(BOOL)on {
    AVCaptureDevice *device = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    if (!device) return;
    
    NSError *err;
    if (device.hasFlash && device.hasTorch) {
        [device lockForConfiguration:&err];
        if (err != nil) return;
        if (on) {
            device.flashMode = AVCaptureFlashModeOn;
            device.torchMode = AVCaptureTorchModeOn;
        } else {
            device.flashMode = AVCaptureFlashModeOff;
            device.torchMode = AVCaptureTorchModeOff;
        }
        [device unlockForConfiguration];
    }
}


- (void)viewSafeAreaInsetsDidChange {
    [super viewSafeAreaInsetsDidChange];
    self.navigationBarTopConstraint.constant = self.view.safeAreaInsets.top;
    [self.view updateConstraints];
}

@end
