//
// Created by <PERSON> on 11/7/17.
//

#import <Foundation/Foundation.h>
#import <MTBBarcodeScanner/MTBBarcodeScanner.h>

#import "BarcodeScannerViewControllerDelegate.h"
#import "ScannerOverlay.h"
#import "UIButton+TouchArea.h"


@interface BarcodeScannerNavigationController: UINavigationController {
    
}
@end

@interface BarcodeScannerViewController : UIViewController
@property(nonatomic, retain) UIView *previewView;
  @property(nonatomic, retain) ScannerOverlay *scanRect;
@property(nonatomic, retain) MTBBarcodeScanner *scanner;
@property(nonatomic, weak) id<BarcodeScannerViewControllerDelegate> delegate;


@property(nonatomic, assign) NSUInteger selectedCount;
@property(nonatomic, retain) NSString* selectedTips;

@end
