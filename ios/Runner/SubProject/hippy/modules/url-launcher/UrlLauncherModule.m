#import "UrlLauncherModule.h"
#import <Photos/Photos.h>
#import <UIKit/UIKit.h>
#import "ABCMacros.h"
#import "HippyMethodInvoideCallback.h"
#import <SafariServices/SafariServices.h>

@interface UrlLauncherSession : NSObject<SFSafariViewControllerDelegate>
@property(copy, nonatomic) HippyMethodInvoideCallback result;
@property(strong, nonatomic) NSURL *url;
@property(strong, nonatomic) SFSafariViewController *safari;
@property(nonatomic, copy) void (^didFinish)(void);
@end

@implementation UrlLauncherSession
- (instancetype)initWithUrl:url withResult:result {
  self = [super init];
  if (self) {
    self.url = url;
    self.result = result;
    if (@available(iOS 9.0, *)) {
      self.safari = [[SFSafariViewController alloc] initWithURL:url];
      self.safari.delegate = self;
    }
  }
  return self;
}

- (void)safariViewController:(SFSafariViewController *)controller
      didCompleteInitialLoad:(BOOL)didLoadSuccessfully API_AVAILABLE(ios(9.0)) {
  if (didLoadSuccessfully) {
    self.result(nil, nil);
  } else {
      self.result(nil, InvokeError(-1, ([NSString stringWithFormat:@"Error while launching %@", self.url])));
  }
}

- (void)safariViewControllerDidFinish:(SFSafariViewController *)controller API_AVAILABLE(ios(9.0)) {
  [controller dismissViewControllerAnimated:YES completion:nil];
  self.didFinish();
}

- (void)close {
  [self safariViewControllerDidFinish:self.safari];
}


@end


@interface UrlLauncherModule()
@property(strong, nonatomic) UrlLauncherSession *currentSession;

@end
@implementation UrlLauncherModule
HIPPY_EXPORT_MODULE(UrlLauncher)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}


HIPPY_EXPORT_METHOD(canLaunch:(NSDictionary*)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self canLaunch:params result:GeneralHippyInvokeCallbackHandler];
}

HIPPY_EXPORT_METHOD(launch:(NSDictionary*)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self launch:params result:GeneralHippyInvokeCallbackHandler];
}

HIPPY_EXPORT_METHOD(closeWebView:(NSDictionary*)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    if (self.currentSession != nil) {
      [self.currentSession close];
    }
    
    resolve(nil);
}




- (void) canLaunch:(NSDictionary*)params result:(HippyMethodInvoideCallback) result {
    NSString *urlString =[params objectForKey:@"url"];
    NSURL *url = [NSURL URLWithString:urlString];
    UIApplication *application = [UIApplication sharedApplication];
    result(@([application canOpenURL:url]),nil);
}


- (void) launch:(NSDictionary*)params result:(HippyMethodInvoideCallback) result {
    NSNumber *useSafariVC = [params objectForKey:@"useSafariVC"];
    NSString *urlString =[params objectForKey:@"url"];
    if (useSafariVC.boolValue) {
      if (@available(iOS 9.0, *)) {
        [self launchURLInVC:urlString result:result];
      } else {
        [self launchURL:urlString params:params result:result];
      }
    } else {
      [self launchURL:urlString params:params result:result];
    }
}



- (void)launchURLInVC:(NSString *)urlString result:(HippyMethodInvoideCallback)result API_AVAILABLE(ios(9.0)) {
  NSURL *url = [NSURL URLWithString:urlString];
  self.currentSession = [[UrlLauncherSession alloc] initWithUrl:url withResult:result];
  __weak typeof(self) weakSelf = self;
  self.currentSession.didFinish = ^(void) {
    weakSelf.currentSession = nil;
  };
  [self.topViewController presentViewController:self.currentSession.safari
                                       animated:YES
                                     completion:nil];
}

- (void)launchURL:(NSString *)urlString
           params:(NSDictionary *)params
           result:(HippyMethodInvoideCallback)result {
  NSURL *url = [NSURL URLWithString:urlString];
  UIApplication *application = [UIApplication sharedApplication];

  if (@available(iOS 10.0, *)) {
    NSNumber *universalLinksOnly = params[@"universalLinksOnly"] ?: @0;
    NSDictionary *options = @{UIApplicationOpenURLOptionUniversalLinksOnly : universalLinksOnly};
    [application openURL:url
                  options:options
        completionHandler:^(BOOL success) {
          result(@(success), nil);
        }];
  } else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    BOOL success = [application openURL:url];
#pragma clang diagnostic pop
    result(@(success), nil);
  }
}


- (UIViewController *)topViewController {
  return [self topViewControllerFromViewController:[UIApplication sharedApplication]
                                                       .keyWindow.rootViewController];
}

/**
 * This method recursively iterate through the view hierarchy
 * to return the top most view controller.
 *
 * It supports the following scenarios:
 *
 * - The view controller is presenting another view.
 * - The view controller is a UINavigationController.
 * - The view controller is a UITabBarController.
 *
 * @return The top most view controller.
 */
- (UIViewController *)topViewControllerFromViewController:(UIViewController *)viewController {
  if ([viewController isKindOfClass:[UINavigationController class]]) {
    UINavigationController *navigationController = (UINavigationController *)viewController;
    return [self
        topViewControllerFromViewController:[navigationController.viewControllers lastObject]];
  }
  if ([viewController isKindOfClass:[UITabBarController class]]) {
    UITabBarController *tabController = (UITabBarController *)viewController;
    return [self topViewControllerFromViewController:tabController.selectedViewController];
  }
  if (viewController.presentedViewController) {
    return [self topViewControllerFromViewController:viewController.presentedViewController];
  }
  return viewController;
}

@end



