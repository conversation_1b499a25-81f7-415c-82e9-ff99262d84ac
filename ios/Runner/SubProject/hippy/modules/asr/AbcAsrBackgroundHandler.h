//
//  AbcAsrBackgroundHandler.h
//  AbcASR
//
//  iOS后台服务处理器，对应Android的AbcAsrService
//  处理后台音频录制和通知管理
//

#import <Foundation/Foundation.h>
#import <UserNotifications/UserNotifications.h>
#import "AbcAsrConfig.h"

NS_ASSUME_NONNULL_BEGIN

@protocol AbcAsrBackgroundHandlerDelegate <NSObject>

@optional
- (void)backgroundHandlerDidStartService:(id)handler;
- (void)backgroundHandlerDidStopService:(id)handler;
- (void)backgroundHandler:(id)handler didFailWithError:(NSError *)error;

@end

@interface AbcAsrBackgroundHandler : NSObject

@property (nonatomic, weak, nullable) id<AbcAsrBackgroundHandlerDelegate> delegate;
@property (nonatomic, strong, readonly) AbcAsrConfig *config;
@property (nonatomic, assign, readonly) BOOL isServiceActive;

// 初始化方法
- (instancetype)initWithConfig:(AbcAsrConfig *)config;

// 服务控制
- (void)startService;
- (void)stopService;

// 后台任务管理
- (void)beginBackgroundTask;
- (void)endBackgroundTask;

// 通知管理
- (void)showServiceNotification;
- (void)hideServiceNotification;

// 音频会话管理
- (BOOL)configureBackgroundAudioSession;
- (void)restoreAudioSession;

@end

NS_ASSUME_NONNULL_END