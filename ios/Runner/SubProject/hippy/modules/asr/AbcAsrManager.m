//
//  AbcAsrManager.m
//  AbcASR
//
//  iOS ASR管理器实现，对应Android的AbcAsrManager
//  负责协调音频录制和WebSocket通信
//

#import "AbcAsrManager.h"
#import "AbcAudioRecorder.h"
#import "AbcAsrWebSocketClient.h"
#import <CallKit/CallKit.h>

typedef NS_ENUM(NSInteger, AsrState) {
    AsrStateIdle,
    AsrStateConnecting,
    AsrStateConnected,
    AsrStateRecording,
    AsrStateStopped
};

@interface AbcAsrManager () <AbcAudioRecorderDelegate, AbcAsrWebSocketClientDelegate, CXCallObserverDelegate>

@property (nonatomic, strong) AbcAudioRecorder *audioRecorder;
@property (nonatomic, strong) AbcAsrWebSocketClient *webSocketClient;
@property (nonatomic, strong) CXCallObserver *callObserver;

@property (nonatomic, assign) AsrState currentState;
@property (nonatomic, strong) dispatch_queue_t workerQueue;
@property (nonatomic, strong) NSString *currentAudioEvent; // 存储当前音频事件名

@end

@implementation AbcAsrManager

- (instancetype)initWithConfig:(AbcAsrConfig *)config {
    self = [super init];
    if (self) {
        _config = config;
        _currentState = AsrStateIdle;
        _workerQueue = dispatch_queue_create("com.abc.asr.manager", DISPATCH_QUEUE_SERIAL);

        [self initializeCallObserver];
    }
    return self;
}

- (void)initializeCallObserver {
    // 初始化电话监听器
    self.callObserver = [[CXCallObserver alloc] init];
    [self.callObserver setDelegate:self queue:self.workerQueue];
}

#pragma mark - ASR Control Methods

- (BOOL)initConnection {
    NSLog(@"[AbcAsrManager] Initializing ASR connection with config: %@", self.config);

    dispatch_async(self.workerQueue, ^{
        @try {
            // 1. 初始化WebSocket客户端
            self.webSocketClient = [[AbcAsrWebSocketClient alloc] initWithConfig:self.config];
            self.webSocketClient.delegate = self;

            // 2. 连接WebSocket
            [self.webSocketClient connect];

            [self notifyStateChanged:@"CONNECTING" message:@"正在建立连接"];
        } @catch (NSException *exception) {
            NSLog(@"[AbcAsrManager] Failed to initialize connection: %@", exception.reason);
            [self notifyError:@"INIT_FAILED" message:[NSString stringWithFormat:@"初始化连接失败: %@", exception.reason]];
        }
    });

    return YES;
}

- (BOOL)startRecording:(NSString *)event {
    NSLog(@"[AbcAsrManager] Starting audio recording with event: %@", event);

    dispatch_async(self.workerQueue, ^{
        @try {
            [self initializeAudioRecorder:event];
        } @catch (NSException *exception) {
            NSLog(@"[AbcAsrManager] Failed to start recording: %@", exception.reason);
            [self notifyError:@"RECORD_FAILED" message:[NSString stringWithFormat:@"开始录音失败: %@", exception.reason]];
        }
    });

    return YES;
}

- (void)initializeAudioRecorder:(NSString *)event {
    // 如果已有录音器，先清理
    if (self.audioRecorder) {
        NSLog(@"[AbcAsrManager] Cleaning up existing audio recorder");
        [self.audioRecorder stopRecording];
        self.audioRecorder = nil;
    }

    NSLog(@"[AbcAsrManager] Initializing new audio recorder with event: %@", event);

    // 存储当前音频事件名
    self.currentAudioEvent = event;

    self.audioRecorder = [[AbcAudioRecorder alloc] initWithConfig:self.config];
    self.audioRecorder.delegate = self;

    // 开始录音
    BOOL success = [self.audioRecorder startRecording];
    if (success) {
        NSLog(@"[AbcAsrManager] Audio recording started successfully");
        self.currentState = AsrStateRecording;
        [self notifyStateChanged:@"RECORDING" message:@"开始录音"];
    } else {
        NSLog(@"[AbcAsrManager] Failed to start audio recording");
        [self notifyError:@"AUDIO_RECORD_FAILED" message:@"音频录制启动失败"];
        [self stopRecognition];
    }
}

- (void)stopRecognition {
    NSLog(@"[AbcAsrManager] Stopping ASR recognition");

    dispatch_async(self.workerQueue, ^{
        @try {
            // 停止音频录制
            if (self.audioRecorder) {
                [self.audioRecorder stopRecording];
                self.audioRecorder = nil;
            }

            self.currentState = AsrStateStopped;
            [self notifyStateChanged:@"STOPPED" message:@"识别已停止"];
            NSLog(@"[AbcAsrManager] ASR recognition stopped");
        } @catch (NSException *exception) {
            NSLog(@"[AbcAsrManager] Error stopping recognition: %@", exception.reason);
        }
    });
}

#pragma mark - Socket.IO Methods

- (void)sendSocketMessage:(NSString *)event data:(NSDictionary *)data {
    if (self.webSocketClient) {
        [self.webSocketClient sendMessage:data forEvent:event];
    } else {
        NSLog(@"[AbcAsrManager] ❌ Cannot send socket message - client not initialized");
    }
}

- (void)addEventListener:(NSString *)event {
    if (self.webSocketClient) {
        __weak typeof(self) weakSelf = self;
        [self.webSocketClient addEventListener:event callback:^(NSArray *items) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if ([weakSelf.delegate respondsToSelector:@selector(asrManager:didReceiveSocketEvent:message:)]) {
                    [weakSelf.delegate asrManager:weakSelf didReceiveSocketEvent:event message:items];
                }
            });
        }];
    }
}

- (void)removeEventListener:(NSString *)event {
    if (self.webSocketClient) {
        [self.webSocketClient removeEventListener:event];
    }
}

#pragma mark - Call Monitoring

- (void)startCallListener {
    NSLog(@"[AbcAsrManager] Setting up phone call listener");

    // CallKit已经在初始化时设置，这里只需要通知状态
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.delegate respondsToSelector:@selector(asrManager:didChangeState:message:)]) {
            [self.delegate asrManager:self didChangeState:@"CALL_LISTENER_READY" message:@"来电监听已成功设置"];
        }
    });
}

#pragma mark - AbcAudioRecorderDelegate

- (void)audioRecorder:(id)recorder didReceiveAudioData:(NSData *)audioData {
    // 发送音频数据到WebSocket
    if (self.webSocketClient) {
        // 使用存储的事件名
        NSString *eventName = self.currentAudioEvent ?: @"audio-data";
        [self.webSocketClient sendAudioData:audioData forEvent:eventName];
    }
}

- (void)audioRecorder:(id)recorder didReceiveWaveformData:(float)waveformData {
    [self notifyWaveformData:waveformData];
}

- (void)audioRecorder:(id)recorder didReceiveWaveformDataArray:(NSArray<NSNumber *> *)waveformDataArray {
    [self notifyWaveformDataArray:waveformDataArray];
}

- (void)audioRecorderDidStartRecording:(id)recorder {
    NSLog(@"[AbcAsrManager] Audio recorder started successfully");
}

- (void)audioRecorderDidStopRecording:(id)recorder {
    NSLog(@"[AbcAsrManager] Audio recorder stopped");
}

- (void)audioRecorder:(id)recorder didFailWithError:(NSError *)error {
    NSLog(@"[AbcAsrManager] Audio recorder failed with error: %@", error.localizedDescription);
    [self notifyError:@"AUDIO_RECORD_FAILED" message:error.localizedDescription];
}

#pragma mark - AbcAsrWebSocketClientDelegate

- (void)webSocketClientDidConnect:(id)client {
    NSLog(@"[AbcAsrManager] WebSocket client connected successfully");
    self.currentState = AsrStateConnected;
    [self notifyStateChanged:@"CONNECTED" message:@"连接已建立"];
}

- (void)webSocketClient:(id)client didDisconnectWithError:(NSError *)error {
    NSLog(@"[AbcAsrManager] WebSocket client disconnected");
    self.currentState = AsrStateIdle;
    [self notifyStateChanged:@"DISCONNECTED" message:@"连接已断开"];
}

- (void)webSocketClient:(id)client didReceiveMessage:(NSArray *)items forEvent:(NSString *)event {
    // 转发Socket.IO事件给代理
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.delegate respondsToSelector:@selector(asrManager:didReceiveSocketEvent:message:)]) {
            [self.delegate asrManager:self didReceiveSocketEvent:event message:items];
        }
    });
}

- (void)webSocketClient:(id)client didFailWithError:(NSError *)error {
    NSLog(@"[AbcAsrManager] WebSocket client failed with error: %@", error.localizedDescription);
    [self notifyError:@"WEBSOCKET_ERROR" message:error.localizedDescription];
}

#pragma mark - CXCallObserverDelegate

- (void)callObserver:(CXCallObserver *)callObserver callChanged:(CXCall *)call {
    NSString *callState;
    NSString *phoneNumber = @""; // CXCall不直接提供电话号码访问

    if (call.hasEnded) {
        callState = @"IDLE";
    } else if (call.hasConnected) {
        callState = @"OFFHOOK";
    } else if (call.isOutgoing) {
        callState = @"OUTGOING";
    } else {
        callState = @"RINGING";
    }

    NSLog(@"[AbcAsrManager] Call state changed: %@", callState);

    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.delegate respondsToSelector:@selector(asrManager:didReceiveCallStateChange:phoneNumber:)]) {
            [self.delegate asrManager:self didReceiveCallStateChange:callState phoneNumber:phoneNumber];
        }
    });
}

#pragma mark - Notification Methods

- (void)notifyError:(NSString *)errorCode message:(NSString *)errorMessage {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.delegate respondsToSelector:@selector(asrManager:didReceiveError:message:)]) {
            [self.delegate asrManager:self didReceiveError:errorCode message:errorMessage];
        }
    });
}

- (void)notifyStateChanged:(NSString *)state message:(NSString *)message {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.delegate respondsToSelector:@selector(asrManager:didChangeState:message:)]) {
            [self.delegate asrManager:self didChangeState:state message:message];
        }
    });
}

- (void)notifyWaveformData:(float)waveformData {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.delegate respondsToSelector:@selector(asrManager:didReceiveWaveformData:)]) {
            [self.delegate asrManager:self didReceiveWaveformData:waveformData];
        }
    });
}

- (void)notifyWaveformDataArray:(NSArray<NSNumber *> *)waveformDataArray {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.delegate respondsToSelector:@selector(asrManager:didReceiveWaveformDataArray:)]) {
            [self.delegate asrManager:self didReceiveWaveformDataArray:waveformDataArray];
        }
    });
}

#pragma mark - Resource Management

- (void)cleanup {
    NSLog(@"[AbcAsrManager] Cleaning up ASR manager resources");

    // 停止电话监听
    if (self.callObserver) {
        [self.callObserver setDelegate:nil queue:nil];
        self.callObserver = nil;
    }

    // 等待工作队列完成
    dispatch_sync(self.workerQueue, ^{
        // 释放音频录制器
        if (self.audioRecorder) {
            [self.audioRecorder stopRecording];
            self.audioRecorder = nil;
        }

        // 释放WebSocket客户端
        if (self.webSocketClient) {
            [self.webSocketClient cleanup];
            self.webSocketClient = nil;
        }
    });

    NSLog(@"[AbcAsrManager] ASR manager resources cleaned up");
}

- (void)dealloc {
    [self cleanup];
}

@end
