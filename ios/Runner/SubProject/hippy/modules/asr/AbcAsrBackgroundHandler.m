//
//  AbcAsrBackgroundHandler.m
//  AbcASR
//
//  iOS后台服务处理器实现，对应Android的AbcAsrService
//  处理后台音频录制和通知管理
//

#import "AbcAsrBackgroundHandler.h"
#import <UIKit/UIKit.h>
#import <AVFoundation/AVFoundation.h>
#import <UserNotifications/UserNotifications.h>

@interface AbcAsrBackgroundHandler () <UNUserNotificationCenterDelegate>

@property (nonatomic, assign) UIBackgroundTaskIdentifier backgroundTaskId;
@property (nonatomic, strong) UNUserNotificationCenter *notificationCenter;
@property (nonatomic, strong) NSString *notificationIdentifier;

@end

@implementation AbcAsrBackgroundHandler

- (instancetype)initWithConfig:(AbcAsrConfig *)config {
    self = [super init];
    if (self) {
        _config = config;
        _backgroundTaskId = UIBackgroundTaskInvalid;
        _notificationIdentifier = @"com.abc.asr.service.notification";
        
        [self initializeNotificationCenter];
    }
    return self;
}

- (void)initializeNotificationCenter {
    self.notificationCenter = [UNUserNotificationCenter currentNotificationCenter];
    self.notificationCenter.delegate = self;
    
    // 请求通知权限
    [self.notificationCenter requestAuthorizationWithOptions:(UNAuthorizationOptionAlert | UNAuthorizationOptionSound) 
                                            completionHandler:^(BOOL granted, NSError * _Nullable error) {
        if (granted) {
            NSLog(@"[AbcAsrBackgroundHandler] Notification permission granted");
        } else {
            NSLog(@"[AbcAsrBackgroundHandler] Notification permission denied: %@", error.localizedDescription);
        }
    }];
}

#pragma mark - Service Control

- (void)startService {
    NSLog(@"[AbcAsrBackgroundHandler] Starting ASR background service");
    
    // 1. 配置后台音频会话
    if (![self configureBackgroundAudioSession]) {
        NSError *error = [NSError errorWithDomain:@"AbcAsrBackgroundHandler" 
                                             code:-1001 
                                         userInfo:@{NSLocalizedDescriptionKey: @"Failed to configure background audio session"}];
        [self notifyError:error];
        return;
    }
    
    // 2. 开始后台任务
    [self beginBackgroundTask];
    
    // 3. 显示服务通知
    if (self.config.enableForegroundService) {
        [self showServiceNotification];
    }
    
    _isServiceActive = YES;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.delegate respondsToSelector:@selector(backgroundHandlerDidStartService:)]) {
            [self.delegate backgroundHandlerDidStartService:self];
        }
    });
    
    NSLog(@"[AbcAsrBackgroundHandler] ASR background service started successfully");
}

- (void)stopService {
    NSLog(@"[AbcAsrBackgroundHandler] Stopping ASR background service");
    
    // 1. 隐藏服务通知
    [self hideServiceNotification];
    
    // 2. 结束后台任务
    [self endBackgroundTask];
    
    // 3. 恢复音频会话
    [self restoreAudioSession];
    
    _isServiceActive = NO;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.delegate respondsToSelector:@selector(backgroundHandlerDidStopService:)]) {
            [self.delegate backgroundHandlerDidStopService:self];
        }
    });
    
    NSLog(@"[AbcAsrBackgroundHandler] ASR background service stopped");
}

#pragma mark - Background Task Management

- (void)beginBackgroundTask {
    if (self.backgroundTaskId != UIBackgroundTaskInvalid) {
        NSLog(@"[AbcAsrBackgroundHandler] Background task already active");
        return;
    }
    
    __weak typeof(self) weakSelf = self;
    self.backgroundTaskId = [[UIApplication sharedApplication] beginBackgroundTaskWithName:@"AbcAsrBackgroundTask" 
                                                                          expirationHandler:^{
        NSLog(@"[AbcAsrBackgroundHandler] Background task expired");
        [weakSelf endBackgroundTask];
    }];
    
    if (self.backgroundTaskId == UIBackgroundTaskInvalid) {
        NSLog(@"[AbcAsrBackgroundHandler] ❌ Failed to start background task");
    } else {
        NSLog(@"[AbcAsrBackgroundHandler] ✅ Background task started with ID: %lu", (unsigned long)self.backgroundTaskId);
    }
}

- (void)endBackgroundTask {
    if (self.backgroundTaskId != UIBackgroundTaskInvalid) {
        NSLog(@"[AbcAsrBackgroundHandler] Ending background task: %lu", (unsigned long)self.backgroundTaskId);
        
        [[UIApplication sharedApplication] endBackgroundTask:self.backgroundTaskId];
        self.backgroundTaskId = UIBackgroundTaskInvalid;
    }
}

#pragma mark - Notification Management

- (void)showServiceNotification {
    UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
    content.title = self.config.serviceNotificationTitle ?: @"语音识别服务";
    content.body = self.config.serviceNotificationContent ?: @"正在进行语音识别...";
    content.sound = nil; // 静音通知
    
    // 添加停止按钮
    UNNotificationAction *stopAction = [UNNotificationAction actionWithIdentifier:@"STOP_ASR_ACTION"
                                                                             title:@"停止"
                                                                           options:UNNotificationActionOptionForeground];
    
    UNNotificationCategory *category = [UNNotificationCategory categoryWithIdentifier:@"ASR_SERVICE_CATEGORY"
                                                                               actions:@[stopAction]
                                                                     intentIdentifiers:@[]
                                                                               options:UNNotificationCategoryOptionNone];
    
    [self.notificationCenter setNotificationCategories:[NSSet setWithObject:category]];
    content.categoryIdentifier = @"ASR_SERVICE_CATEGORY";
    
    // 创建通知请求
    UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:self.notificationIdentifier
                                                                          content:content
                                                                          trigger:nil]; // 立即显示
    
    [self.notificationCenter addNotificationRequest:request withCompletionHandler:^(NSError * _Nullable error) {
        if (error) {
            NSLog(@"[AbcAsrBackgroundHandler] Failed to show service notification: %@", error.localizedDescription);
        } else {
            NSLog(@"[AbcAsrBackgroundHandler] Service notification displayed");
        }
    }];
}

- (void)hideServiceNotification {
    [self.notificationCenter removeDeliveredNotificationsWithIdentifiers:@[self.notificationIdentifier]];
    [self.notificationCenter removePendingNotificationRequestsWithIdentifiers:@[self.notificationIdentifier]];
    
    NSLog(@"[AbcAsrBackgroundHandler] Service notification hidden");
}

#pragma mark - Audio Session Management

- (BOOL)configureBackgroundAudioSession {
    AVAudioSession *session = [AVAudioSession sharedInstance];
    NSError *error = nil;
    
    // 设置音频会话类别，支持后台录音
    if (![session setCategory:AVAudioSessionCategoryPlayAndRecord 
                         mode:AVAudioSessionModeDefault
                      options:AVAudioSessionCategoryOptionMixWithOthers | 
                              AVAudioSessionCategoryOptionAllowBluetooth |
                              AVAudioSessionCategoryOptionDefaultToSpeaker
                        error:&error]) {
        NSLog(@"[AbcAsrBackgroundHandler] Failed to set audio session category: %@", error.localizedDescription);
        return NO;
    }
    
    // 激活音频会话
    if (![session setActive:YES error:&error]) {
        NSLog(@"[AbcAsrBackgroundHandler] Failed to activate audio session: %@", error.localizedDescription);
        return NO;
    }
    
    NSLog(@"[AbcAsrBackgroundHandler] Background audio session configured successfully");
    return YES;
}

- (void)restoreAudioSession {
    AVAudioSession *session = [AVAudioSession sharedInstance];
    NSError *error = nil;
    
    // 恢复默认音频会话设置
    if (![session setCategory:AVAudioSessionCategorySoloAmbient error:&error]) {
        NSLog(@"[AbcAsrBackgroundHandler] Failed to restore audio session category: %@", error.localizedDescription);
        return;
    }
    
    // 停用音频会话
    if (![session setActive:NO error:&error]) {
        NSLog(@"[AbcAsrBackgroundHandler] Failed to deactivate audio session: %@", error.localizedDescription);
    }
    
    NSLog(@"[AbcAsrBackgroundHandler] Audio session restored");
}

#pragma mark - UNUserNotificationCenterDelegate

- (void)userNotificationCenter:(UNUserNotificationCenter *)center 
       willPresentNotification:(UNNotification *)notification 
         withCompletionHandler:(void (^)(UNNotificationPresentationOptions))completionHandler {
    // 在前台也显示通知
    completionHandler(UNNotificationPresentationOptionAlert | UNNotificationPresentationOptionSound);
}

- (void)userNotificationCenter:(UNUserNotificationCenter *)center 
didReceiveNotificationResponse:(UNNotificationResponse *)response 
         withCompletionHandler:(void (^)(void))completionHandler {
    
    if ([response.actionIdentifier isEqualToString:@"STOP_ASR_ACTION"]) {
        NSLog(@"[AbcAsrBackgroundHandler] User requested to stop ASR service via notification");
        [self stopService];
    }
    
    completionHandler();
}

#pragma mark - Helper Methods

- (void)notifyError:(NSError *)error {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.delegate respondsToSelector:@selector(backgroundHandler:didFailWithError:)]) {
            [self.delegate backgroundHandler:self didFailWithError:error];
        }
    });
}

#pragma mark - Static Convenience Methods

+ (void)startAsrService:(AbcAsrConfig *)config {
    // iOS中没有像Android那样的静态服务启动，这里提供一个便捷方法
    // 实际的服务管理应该由主模块负责
    NSLog(@"[AbcAsrBackgroundHandler] Static service start requested - should be handled by main module");
}

+ (void)stopAsrService {
    // iOS中没有像Android那样的静态服务停止，这里提供一个便捷方法
    // 实际的服务管理应该由主模块负责
    NSLog(@"[AbcAsrBackgroundHandler] Static service stop requested - should be handled by main module");
}

#pragma mark - Cleanup

- (void)dealloc {
    [self stopService];
    NSLog(@"[AbcAsrBackgroundHandler] Background handler deallocated");
}

@end
