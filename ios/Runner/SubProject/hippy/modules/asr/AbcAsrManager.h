//
//  AbcAsrManager.h
//  AbcASR
//
//  iOS ASR管理器，对应Android的AbcAsrManager
//  负责协调音频录制和WebSocket通信
//

#import <Foundation/Foundation.h>
#import <CallKit/CallKit.h>
#import "AbcAsrConfig.h"

NS_ASSUME_NONNULL_BEGIN

@protocol AbcAsrManagerDelegate <NSObject>

@optional
- (void)asrManager:(id)manager didChangeState:(NSString *)state message:(NSString *)message;
- (void)asrManager:(id)manager didReceiveError:(NSString *)errorCode message:(NSString *)errorMessage;
- (void)asrManager:(id)manager didReceiveWaveformData:(float)waveformData;
- (void)asrManager:(id)manager didReceiveWaveformDataArray:(NSArray<NSNumber *> *)waveformDataArray;
- (void)asrManager:(id)manager didReceiveCallStateChange:(NSString *)state phoneNumber:(nullable NSString *)phoneNumber;
- (void)asrManager:(id)manager didReceiveSocketEvent:(NSString *)event message:(NSArray *)items;

@end

@interface AbcAsrManager : NSObject

@property (nonatomic, weak, nullable) id<AbcAsrManagerDelegate> delegate;
@property (nonatomic, strong, readonly) AbcAsrConfig *config;

// 初始化方法
- (instancetype)initWithConfig:(AbcAsrConfig *)config;

// ASR控制
- (BOOL)initConnection;
- (BOOL)startRecording:(NSString *)event;
- (void)stopRecognition;

// Socket.IO消息处理
- (void)sendSocketMessage:(NSString *)event data:(NSDictionary *)data;
- (void)addEventListener:(NSString *)event;
- (void)removeEventListener:(NSString *)event;

// 电话监听
- (void)startCallListener;

// 资源清理
- (void)cleanup;

@end

NS_ASSUME_NONNULL_END
