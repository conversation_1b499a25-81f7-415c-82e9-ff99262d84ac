//
//  AbcAudioRecorder.m
//  AbcASR
//
//  iOS音频录制器实现，对应Android的AbcAudioRecorder
//

#import "AbcAudioRecorder.h"

// 固定头部标识，对应Android的AUDIO_HEADER
static const UInt8 kAudioHeader[] = {0xFF, 0xFE};

@interface AbcAudioRecorder ()

@property (nonatomic, strong) AVAudioEngine *audioEngine;
@property (nonatomic, strong) AVAudioInputNode *inputNode;
@property (nonatomic, strong) AVAudioFormat *recordingFormat;
@property (nonatomic, strong) AVAudioFormat *targetFormat;
@property (nonatomic, strong) AVAudioConverter *audioConverter;

@property (nonatomic, strong) NSMutableData *accumulatedBuffer;
@property (nonatomic, assign) NSUInteger accumulatedBytes;
@property (nonatomic, assign) NSTimeInterval lastWaveformCallbackTime;
@property (nonatomic, assign) NSUInteger audioDataCount;
@property (nonatomic, assign) float currentVolume;

// 新增：用于波形数据数组处理
@property (nonatomic, strong) NSMutableArray<NSNumber *> *waveformDataBuffer;
@property (nonatomic, assign) NSUInteger waveformSampleCount;

@property (nonatomic, strong) dispatch_queue_t audioQueue;

@end

@implementation AbcAudioRecorder

- (instancetype)initWithConfig:(AbcAsrConfig *)config {
    self = [super init];
    if (self) {
        _config = config;
        [self setupAudioComponents];
    }
    return self;
}

- (void)setupAudioComponents {
    self.audioQueue = dispatch_queue_create("com.abc.asr.audio", DISPATCH_QUEUE_SERIAL);

    // 配置目标音频格式（与Android保持一致）
    AVAudioChannelCount channels = (AVAudioChannelCount)self.config.channels;
    double sampleRate = (double)self.config.sampleRate;

    self.targetFormat = [[AVAudioFormat alloc]
                        initWithCommonFormat:AVAudioPCMFormatInt16
                        sampleRate:sampleRate
                        channels:channels
                        interleaved:YES];

    // 初始化累积缓冲区
    self.accumulatedBuffer = [[NSMutableData alloc] init];

    // 初始化波形数据缓冲区
    self.waveformDataBuffer = [[NSMutableArray alloc] init];
    self.waveformSampleCount = 0;

    NSLog(@"[AbcAudioRecorder] Audio setup completed - targetFormat: %@", self.targetFormat);
}

- (void)initializeAudioEngine {
    // 每次启动时重新创建音频引擎，确保状态干净
    if (self.audioEngine) {
        [self cleanupAudioEngine];
    }

    self.audioEngine = [[AVAudioEngine alloc] init];
    self.inputNode = self.audioEngine.inputNode;

    // 获取输入节点的硬件格式 - 每次重新获取以确保有效性
    self.recordingFormat = [self.inputNode outputFormatForBus:0];

    // 创建音频转换器
    self.audioConverter = [[AVAudioConverter alloc]
                          initFromFormat:self.recordingFormat
                          toFormat:self.targetFormat];

    if (!self.audioConverter) {
        NSLog(@"[AbcAudioRecorder] Failed to create audio converter");
    }

    NSLog(@"[AbcAudioRecorder] Audio engine initialized - recordingFormat: %@", self.recordingFormat);
}

- (BOOL)startRecording {
    NSLog(@"[AbcAudioRecorder] Starting audio recording");

    // 如果已经在录音，先停止
    if (self.isRecording) {
        NSLog(@"[AbcAudioRecorder] Already recording, stopping first");
        [self stopRecording];
        // 等待停止完成
        dispatch_sync(self.audioQueue, ^{
            // 等待队列中的停止操作完成
        });
    }

    // 配置音频会话
    if (![self configureAudioSession]) {
        return NO;
    }

    dispatch_async(self.audioQueue, ^{
        [self initializeAudioEngine];
        [self startAudioEngine];
    });

    return YES;
}

- (BOOL)configureAudioSession {
    AVAudioSession *session = [AVAudioSession sharedInstance];
    NSError *error = nil;

    // 设置音频会话类别
    if (![session setCategory:AVAudioSessionCategoryPlayAndRecord
                     withOptions:AVAudioSessionCategoryOptionMixWithOthers |
                                AVAudioSessionCategoryOptionDefaultToSpeaker
                          error:&error]) {
        NSLog(@"[AbcAudioRecorder] Failed to set audio session category: %@", error.localizedDescription);
        return NO;
    }

    // 激活音频会话
    if (![session setActive:YES error:&error]) {
        NSLog(@"[AbcAudioRecorder] Failed to activate audio session: %@", error.localizedDescription);
        return NO;
    }

    NSLog(@"[AbcAudioRecorder] Audio session configured successfully");
    return YES;
}

- (void)startAudioEngine {
    if (self.audioEngine.isRunning) {
        NSLog(@"[AbcAudioRecorder] Audio engine is already running");
        return;
    }

    // 验证音频格式有效性
    if (!self.recordingFormat || !self.targetFormat || !self.audioConverter) {
        NSLog(@"[AbcAudioRecorder] Invalid audio format or converter");
        dispatch_async(dispatch_get_main_queue(), ^{
            NSError *error = [NSError errorWithDomain:@"AbcAudioRecorder"
                                               code:-1
                                           userInfo:@{NSLocalizedDescriptionKey: @"Invalid audio format"}];
            if ([self.delegate respondsToSelector:@selector(audioRecorder:didFailWithError:)]) {
                [self.delegate audioRecorder:self didFailWithError:error];
            }
        });
        return;
    }

    // 计算缓冲区大小（基于采样率和格式）
    AVAudioFrameCount bufferSize = 1024; // 默认缓冲区大小

    NSLog(@"[AbcAudioRecorder] Installing tap with format: %@", self.recordingFormat);

    // 安装音频处理回调
    @try {
        [self.inputNode installTapOnBus:0
                             bufferSize:bufferSize
                                 format:self.recordingFormat
                                  block:^(AVAudioPCMBuffer * _Nonnull buffer, AVAudioTime * _Nonnull when) {
            [self processAudioBuffer:buffer];
        }];
    } @catch (NSException *exception) {
        NSLog(@"[AbcAudioRecorder] Failed to install tap: %@", exception.reason);
        dispatch_async(dispatch_get_main_queue(), ^{
            NSError *error = [NSError errorWithDomain:@"AbcAudioRecorder"
                                               code:-2
                                           userInfo:@{NSLocalizedDescriptionKey: exception.reason}];
            if ([self.delegate respondsToSelector:@selector(audioRecorder:didFailWithError:)]) {
                [self.delegate audioRecorder:self didFailWithError:error];
            }
        });
        return;
    }

    // 准备并启动音频引擎
    [self.audioEngine prepare];

    NSError *error = nil;
    if ([self.audioEngine startAndReturnError:&error]) {
        _isRecording = YES;
        self.audioDataCount = 0;
        self.lastWaveformCallbackTime = 0;
        [self.accumulatedBuffer setLength:0];
        self.accumulatedBytes = 0;

        // 重置波形数据缓冲区
        [self.waveformDataBuffer removeAllObjects];
        self.waveformSampleCount = 0;

        dispatch_async(dispatch_get_main_queue(), ^{
            if ([self.delegate respondsToSelector:@selector(audioRecorderDidStartRecording:)]) {
                [self.delegate audioRecorderDidStartRecording:self];
            }
        });

        NSLog(@"[AbcAudioRecorder] Audio engine started successfully");
    } else {
        NSLog(@"[AbcAudioRecorder] Failed to start audio engine: %@", error.localizedDescription);

        dispatch_async(dispatch_get_main_queue(), ^{
            if ([self.delegate respondsToSelector:@selector(audioRecorder:didFailWithError:)]) {
                [self.delegate audioRecorder:self didFailWithError:error];
            }
        });
    }
}

- (void)processAudioBuffer:(AVAudioPCMBuffer *)buffer {
    if (!self.isRecording || !self.audioConverter) {
        return;
    }
    NSLog(@"[AbcAudioRecorder] processAudioBuffer");

    // 创建输出缓冲区
    AVAudioFrameCount outputFrameCapacity = buffer.frameLength;
    AVAudioPCMBuffer *outputBuffer = [[AVAudioPCMBuffer alloc]
                                     initWithPCMFormat:self.targetFormat
                                     frameCapacity:outputFrameCapacity];

    if (!outputBuffer) {
        NSLog(@"[AbcAudioRecorder] Failed to create output buffer");
        return;
    }

    // 音频格式转换
    NSError *error = nil;
    AVAudioConverterInputStatus inputStatus = [self.audioConverter convertToBuffer:outputBuffer
                                                                               error:&error
                                                                    withInputFromBlock:^AVAudioBuffer * _Nullable(AVAudioPacketCount inNumberOfPackets, AVAudioConverterInputStatus * _Nonnull outStatus) {
        *outStatus = AVAudioConverterInputStatus_HaveData;
        return buffer;
    }];

    if (inputStatus != AVAudioConverterInputStatus_HaveData) {
        NSLog(@"[AbcAudioRecorder] Audio conversion failed: %@", error.localizedDescription);
        return;
    }

    // 处理转换后的音频数据
    [self handleConvertedAudioData:outputBuffer];
}

- (void)handleConvertedAudioData:(AVAudioPCMBuffer *)buffer {
    // 计算音频数据大小
    NSUInteger dataSize = buffer.frameLength * self.targetFormat.channelCount * sizeof(int16_t);

    // 创建音频数据
    NSData *audioData = [NSData dataWithBytes:buffer.int16ChannelData[0] length:dataSize];

    // 累积音频数据
    [self.accumulatedBuffer appendData:audioData];
    self.accumulatedBytes += dataSize;

    // 计算波形数据（音量）- 保持兼容性
    [self calculateWaveformData:buffer.int16ChannelData[0] frameLength:buffer.frameLength];

    // 新增：收集波形数据数组
    [self collectWaveformDataArray:buffer.int16ChannelData[0] frameLength:buffer.frameLength];

    // 控制波形数据回调频率
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970] * 1000; // 转换为毫秒
    NSTimeInterval interval = currentTime - self.lastWaveformCallbackTime;

//    if (self.lastWaveformCallbackTime == 0 || interval >= self.config.waveformCallbackInterval) {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSLog(@"[AbcAudioRecorder] Audio waveformData callback");
        // 保持原有的单个音量值回调（兼容性）
//        if ([self.delegate respondsToSelector:@selector(audioRecorder:didReceiveWaveformData:)]) {
//            [self.delegate audioRecorder:self didReceiveWaveformData:self.currentVolume];
//        }

        // 新增：发送波形数据数组
        if ([self.delegate respondsToSelector:@selector(audioRecorder:didReceiveWaveformDataArray:)]) {
            NSArray<NSNumber *> *waveformArray = [self getAndResetWaveformDataArray];
            [self.delegate audioRecorder:self didReceiveWaveformDataArray:waveformArray];
        }
    });
    self.lastWaveformCallbackTime = currentTime;
//    }

    // 当累积的数据达到期望大小时才发送
    if (self.accumulatedBytes >= self.config.bufferSize) {
        [self sendAccumulatedAudioData];
    }
}

- (void)calculateWaveformData:(int16_t *)samples frameLength:(AVAudioFrameCount)frameLength {
    float maxVolume = 0.0f;

    for (AVAudioFrameCount i = 0; i < frameLength; i++) {
        float sample = samples[i] / 32767.0f; // 归一化到 -1.0 到 1.0
        float volume = fabsf(sample);
        if (volume > maxVolume) {
            maxVolume = volume;
        }
    }

    self.currentVolume = maxVolume;
}

// 新增：收集波形数据数组，用于更流畅的波形绘制
- (void)collectWaveformDataArray:(int16_t *)samples frameLength:(AVAudioFrameCount)frameLength {
    // 计算降采样参数：将音频数据降采样为合适数量的数据点
    // 目标：100ms内生成约12个数据点
    const NSUInteger targetDataPoints = 12;
    NSUInteger samplesPerPoint = frameLength / targetDataPoints;

    if (samplesPerPoint == 0) {
        samplesPerPoint = 1;
    }

    for (NSUInteger pointIndex = 0; pointIndex < targetDataPoints; pointIndex++) {
        NSUInteger startSample = pointIndex * samplesPerPoint;
        NSUInteger endSample = MIN((pointIndex + 1) * samplesPerPoint, frameLength);

        if (startSample >= frameLength) break;

        // 计算这个区间内的最大音量
        float maxVolume = 0.0f;
        for (NSUInteger i = startSample; i < endSample; i++) {
            float sample = samples[i] / 32767.0f; // 归一化到 -1.0 到 1.0
            float volume = fabsf(sample);
            if (volume > maxVolume) {
                maxVolume = volume;
            }
        }

        // 添加到波形数据缓冲区
        [self.waveformDataBuffer addObject:@(maxVolume)];
        self.waveformSampleCount++;
    }
}

// 新增：获取并重置波形数据数组
- (NSArray<NSNumber *> *)getAndResetWaveformDataArray {
    NSArray<NSNumber *> *result = [self.waveformDataBuffer copy];
    [self.waveformDataBuffer removeAllObjects];
    self.waveformSampleCount = 0;

    NSLog(@"[AbcAudioRecorder] Generated waveform data array with %lu points", (unsigned long)result.count);
    return result;
}

- (void)sendAccumulatedAudioData {
    // 创建要发送的音频数据
    NSData *rawAudioData = [self.accumulatedBuffer subdataWithRange:NSMakeRange(0, self.config.bufferSize)];

    // 根据配置决定是否添加头部标识
    NSData *finalAudioData;
    if (self.config.enableAudioHeader) {
        finalAudioData = [self createAudioPacketWithHeader:rawAudioData];
    } else {
        finalAudioData = rawAudioData;
    }

    // 每10个数据包打印一次调试信息
    self.audioDataCount++;
    if (self.audioDataCount % 10 == 0) {
        float durationMs = (float)self.config.bufferSize / (self.config.sampleRate * self.config.channels * (self.config.audioFormat / 8)) * 1000;
        NSString *headerInfo = self.config.enableAudioHeader ?
            [NSString stringWithFormat:@", with header=%ld bytes", (unsigned long)finalAudioData.length] : @"";

        NSLog(@"[AbcAudioRecorder] Audio data #%ld: raw=%ld bytes%@, %.1fms, volume: %.1f",
              (unsigned long)self.audioDataCount,
              (unsigned long)self.config.bufferSize,
              headerInfo,
              durationMs,
              self.currentVolume);
    }

    // 发送音频数据
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([self.delegate respondsToSelector:@selector(audioRecorder:didReceiveAudioData:)]) {
            [self.delegate audioRecorder:self didReceiveAudioData:finalAudioData];
        }
    });

    // 处理剩余数据
    NSUInteger remainingBytes = self.accumulatedBytes - self.config.bufferSize;
    if (remainingBytes > 0) {
        NSData *remainingData = [self.accumulatedBuffer subdataWithRange:NSMakeRange(self.config.bufferSize, remainingBytes)];
        [self.accumulatedBuffer setData:remainingData];
    } else {
        [self.accumulatedBuffer setLength:0];
    }
    self.accumulatedBytes = remainingBytes;
}

- (NSData *)createAudioPacketWithHeader:(NSData *)audioData {
    // 创建包含头部和音频数据的数据包
    NSMutableData *packet = [[NSMutableData alloc] init];

    // 添加头部标识 (0xFF, 0xFE)
    [packet appendBytes:kAudioHeader length:sizeof(kAudioHeader)];

    // 添加音频数据
    [packet appendData:audioData];

    // 每100个数据包打印一次头部信息用于调试
    if (self.audioDataCount % 100 == 0) {
        NSLog(@"[AbcAudioRecorder] Audio packet #%ld: header=[0x%02X, 0x%02X], audioSize=%ld, totalSize=%ld",
              (unsigned long)self.audioDataCount,
              kAudioHeader[0], kAudioHeader[1],
              (unsigned long)audioData.length,
              (unsigned long)packet.length);
    }

    return packet;
}

- (void)cleanupAudioEngine {
    NSLog(@"[AbcAudioRecorder] Cleaning up audio engine");

    if (self.audioEngine) {
        if (self.audioEngine.isRunning) {
            [self.audioEngine stop];
        }

        // 安全地移除tap
        @try {
            if (self.inputNode) {
                [self.inputNode removeTapOnBus:0];
            }
        } @catch (NSException *exception) {
            NSLog(@"[AbcAudioRecorder] Exception removing tap: %@", exception.reason);
        }

        // 重置引用
        self.audioEngine = nil;
        self.inputNode = nil;
        self.recordingFormat = nil;
        self.audioConverter = nil;
    }
}

- (void)stopRecording {
    NSLog(@"[AbcAudioRecorder] Stopping audio recording");

    _isRecording = NO;

    dispatch_async(self.audioQueue, ^{
        [self cleanupAudioEngine];

        // 清理累积缓冲区
        [self.accumulatedBuffer setLength:0];
        self.accumulatedBytes = 0;

        dispatch_async(dispatch_get_main_queue(), ^{
            if ([self.delegate respondsToSelector:@selector(audioRecorderDidStopRecording:)]) {
                [self.delegate audioRecorderDidStopRecording:self];
            }
        });

        NSLog(@"[AbcAudioRecorder] Audio recording stopped and cleaned up");
    });

    // 延迟停用音频会话，避免与重新启动冲突
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSError *error = nil;
        if (![[AVAudioSession sharedInstance] setActive:NO withOptions:AVAudioSessionSetActiveOptionNotifyOthersOnDeactivation error:&error]) {
            NSLog(@"[AbcAudioRecorder] Failed to deactivate audio session: %@", error.localizedDescription);
        }
    });
}

- (float)getCurrentVolume {
    return self.currentVolume;
}

- (void)dealloc {
    NSLog(@"[AbcAudioRecorder] Deallocating audio recorder");
    _isRecording = NO;
    [self cleanupAudioEngine];
}

@end
