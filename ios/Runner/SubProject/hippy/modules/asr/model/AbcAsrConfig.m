//
//  AbcAsrConfig.m
//  AbcASR
//
//  ASR配置类实现，与Android端AsrConfig保持一致
//

#import "AbcAsrConfig.h"

@implementation AbcAsrConfig

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setDefaultValues];
    }
    return self;
}

- (instancetype)initWithServerUrl:(NSString *)serverUrl sessionId:(NSString *)sessionId {
    self = [self init];
    if (self) {
        self.serverUrl = serverUrl;
        self.sessionId = sessionId;
    }
    return self;
}

- (void)setDefaultValues {
    // WebSocket连接配置
    self.connectTimeout = 10000;
    self.readTimeout = 30000;

    // 音频配置
    self.sampleRate = 16000;
    self.channels = 1;
    self.audioFormat = 16;
    self.bufferSize = 3200; // 100ms = 16000 * 1 * 2 / 10
    self.enableAudioHeader = YES;

    // 识别配置
    self.language = @"zh-CN";
    self.enableVad = YES;
    self.enablePunctuation = YES;
    self.enableNumberConvert = YES;
    self.enableDirtyFilter = YES;

    // 静音检测配置
    self.enableSilentDetection = NO;
    self.silentTimeout = 5000;
    self.silentThreshold = -40.0f;

    // 音量回调配置
    self.volumeCallbackInterval = 100;

    // 波形数据回调配置
    self.waveformCallbackInterval = 100; // 改为100ms，与音频数据处理频率一致

    // 前台服务配置
    self.enableForegroundService = YES;
    self.serviceNotificationTitle = @"语音识别服务";
    self.serviceNotificationContent = @"正在进行语音识别...";

    // Socket.IO连接配置
    self.extraHeaders = [[NSMutableDictionary alloc] init];
    self.userAgent = @"AbcAsrClient/1.0";
    self.withCredentials = YES;
    self.reconnectionDelay = -1;
    self.forceWebsockets = YES;
    self.socketPath = @"/api/asr/socket.io";
    self.connectParams = [[NSMutableDictionary alloc] init];
}

- (void)addExtraHeader:(NSString *)key value:(NSString *)value {
    if (!self.extraHeaders) {
        self.extraHeaders = [[NSMutableDictionary alloc] init];
    }
    [self.extraHeaders setObject:value forKey:key];
}

+ (instancetype)configFromDictionary:(NSDictionary *)params sessionId:(NSString *)sessionId {
    AbcAsrConfig *config = [[AbcAsrConfig alloc] init];

    // 基本配置
    config.sessionId = sessionId;
    if (params[@"serverUrl"]) {
        config.serverUrl = params[@"serverUrl"];
    }

    // 音频配置
    if (params[@"sampleRate"]) {
        config.sampleRate = [params[@"sampleRate"] integerValue];
    }
    if (params[@"channels"]) {
        config.channels = [params[@"channels"] integerValue];
    }
    if (params[@"audioFormat"]) {
        config.audioFormat = [params[@"audioFormat"] integerValue];
    }

    // 识别配置
    if (params[@"language"]) {
        config.language = params[@"language"];
    }
    if (params[@"enableVad"]) {
        config.enableVad = [params[@"enableVad"] boolValue];
    }
    if (params[@"enablePunctuation"]) {
        config.enablePunctuation = [params[@"enablePunctuation"] boolValue];
    }
    if (params[@"enableNumberConvert"]) {
        config.enableNumberConvert = [params[@"enableNumberConvert"] boolValue];
    }
    if (params[@"enableDirtyFilter"]) {
        config.enableDirtyFilter = [params[@"enableDirtyFilter"] boolValue];
    }

    // 静音检测配置
    if (params[@"enableSilentDetection"]) {
        config.enableSilentDetection = [params[@"enableSilentDetection"] boolValue];
    }
    if (params[@"silentTimeout"]) {
        config.silentTimeout = [params[@"silentTimeout"] integerValue];
    }
    if (params[@"silentThreshold"]) {
        config.silentThreshold = [params[@"silentThreshold"] floatValue];
    }

    // 回调配置
    if (params[@"volumeCallbackInterval"]) {
        config.volumeCallbackInterval = [params[@"volumeCallbackInterval"] integerValue];
    }
    if (params[@"waveformCallbackInterval"]) {
        config.waveformCallbackInterval = [params[@"waveformCallbackInterval"] integerValue];
    }

    // 前台服务配置
    if (params[@"enableForegroundService"]) {
        config.enableForegroundService = [params[@"enableForegroundService"] boolValue];
    }
    if (params[@"serviceNotificationTitle"]) {
        config.serviceNotificationTitle = params[@"serviceNotificationTitle"];
    }
    if (params[@"serviceNotificationContent"]) {
        config.serviceNotificationContent = params[@"serviceNotificationContent"];
    }

    // Socket.IO连接配置
    if (params[@"cookies"]) {
        id cookiesParam = params[@"cookies"];
        if ([cookiesParam isKindOfClass:[NSString class]]) {
            config.cookies = cookiesParam;
        } else {
            NSLog(@"[AbcAsrConfig] ❌ cookies parameter is not a string: %@ (class: %@)", cookiesParam, [cookiesParam class]);
            config.cookies = [cookiesParam description]; // 尝试转换为字符串
        }
    }
    if (params[@"userAgent"]) {
        config.userAgent = params[@"userAgent"];
    }
    if (params[@"withCredentials"]) {
        config.withCredentials = [params[@"withCredentials"] boolValue];
    }
    if (params[@"namespace"]) {
        id namespaceParam = params[@"namespace"];
        if ([namespaceParam isKindOfClass:[NSString class]]) {
            config.namespace = namespaceParam;
        } else {
            NSLog(@"[AbcAsrConfig] ❌ namespace parameter is not a string: %@ (class: %@)", namespaceParam, [namespaceParam class]);
            config.namespace = [namespaceParam description]; // 尝试转换为字符串
        }
    }
    if (params[@"reconnectionDelay"]) {
        config.reconnectionDelay = [params[@"reconnectionDelay"] integerValue];
    }
    if (params[@"forceWebsockets"]) {
        config.forceWebsockets = [params[@"forceWebsockets"] boolValue];
    }
    if (params[@"path"]) {
        id pathParam = params[@"path"];
        if ([pathParam isKindOfClass:[NSString class]]) {
            config.socketPath = pathParam;
        } else {
            NSLog(@"[AbcAsrConfig] ❌ path parameter is not a string: %@ (class: %@)", pathParam, [pathParam class]);
            config.socketPath = [pathParam description]; // 尝试转换为字符串
        }
    }

    // 处理连接参数
    if (params[@"connectParams"] && [params[@"connectParams"] isKindOfClass:[NSDictionary class]]) {
        NSDictionary *connectParams = params[@"connectParams"];
        for (NSString *key in connectParams) {
            id value = connectParams[key];
            if (value) {
                [config.connectParams setObject:[value description] forKey:key];
            }
        }
    }

    // 处理额外的请求头
    if (params[@"extraHeaders"] && [params[@"extraHeaders"] isKindOfClass:[NSDictionary class]]) {
        NSDictionary *extraHeaders = params[@"extraHeaders"];
        for (NSString *key in extraHeaders) {
            id value = extraHeaders[key];
            if (value) {
                [config addExtraHeader:key value:[value description]];
            }
        }
    }

    return config;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"AbcAsrConfig{serverUrl='%@', sessionId='%@', sampleRate=%ld, channels=%ld, language='%@', enableVad=%@, enableSilentDetection=%@, silentTimeout=%ld, waveformCallbackInterval=%ld, volumeCallbackInterval=%ld}",
            self.serverUrl,
            self.sessionId,
            (long)self.sampleRate,
            (long)self.channels,
            self.language,
            self.enableVad ? @"YES" : @"NO",
            self.enableSilentDetection ? @"YES" : @"NO",
            (long)self.silentTimeout,
            (long)self.waveformCallbackInterval,
            (long)self.volumeCallbackInterval];
}

@end
