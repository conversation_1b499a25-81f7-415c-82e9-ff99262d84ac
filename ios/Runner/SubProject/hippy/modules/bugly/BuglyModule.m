#import "BuglyModule.h"

#import <Bugly/Bugly.h>

@implementation BuglyModule

HIPPY_EXPORT_MODULE(Bugly)

- (instancetype)init
{
    self = [super init];
    if (self) {
        
    }
    
    return self;
}
- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

HIPPY_EXPORT_METHOD(init:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    BuglyConfig * config = [[BuglyConfig alloc] init];
    [Bugly startWithAppId:@"8b280fd94d" config:config];
    
    resolve(nil);
}

HIPPY_EXPORT_METHOD(postCatchedException:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSString *crash_detail = params[@"crash_detail"];
    NSString *crash_message = params[@"crash_message"];
    NSString *stack = params[@"crash_stack"];
    NSDictionary *extra_info = params[@"extra_info"];
    if (crash_detail == nil || crash_detail == NULL) {
        crash_message = @"";
    }
    if ([crash_detail isKindOfClass:[NSNull class]]) {
        crash_message = @"";
    }
    NSArray *stackTraceArray = [NSMutableArray arrayWithObject:@"[empty stack]"];
    if (stack)
        stackTraceArray = [stack componentsSeparatedByString:@"\n"];

    
    [Bugly reportExceptionWithCategory:5 name:crash_message reason:crash_detail callStack:stackTraceArray extraInfo:extra_info terminateApp:NO];

    resolve(nil);
}

HIPPY_EXPORT_METHOD(setUserId:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSString *userId = params[@"userId"];
    if (![self isBlankString:userId]) {
        [Bugly setUserIdentifier:userId];
    }
    resolve(nil);
}


HIPPY_EXPORT_METHOD(putUserData:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSString *key = params[@"key"];
    NSString *value = params[@"value"];
    if (![self isBlankString:key]&&![self isBlankString:value]){
        [Bugly setUserValue:value forKey:key];
    }
    resolve(nil);
}


- (BOOL) isBlankString:(NSString *)string {
    if (string == nil || string == NULL) {
        return YES;
    }
    
    if ([string isKindOfClass:[NSNull class]]) {
        return YES;
    }
    if ([[string stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]] length]==0) {
        return YES;
    }
    return NO;
    
}

@end
