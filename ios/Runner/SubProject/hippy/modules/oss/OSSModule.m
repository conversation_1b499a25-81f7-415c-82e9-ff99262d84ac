#import "OSSModule.h"
#import "OssManager.h"
#import "OssFileUploader.h"
#import "HostHippyMessageBridge.h"


@interface OSSModule ()

@end

@implementation OSSModule {
}


- (instancetype)init
{
    self = [super init];
    if (self) {
    }
    return self;
}
HIPPY_EXPORT_MODULE(OSS)



HIPPY_EXPORT_METHOD(init:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self init:params result:^(NSObject * _Nullable data, NSError * _Nullable error) {
        if (error) {
            reject(@"-1", error.description, error);
            return;
        }
        
        resolve(data);
    }];
}


HIPPY_EXPORT_METHOD(upload:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self upload:params result:^(NSObject * _Nullable data, NSError * _Nullable error) {
        if (error) {
            reject([NSString stringWithFormat:@"%ld", error.code], error.description, error);
            return;
        }
        
        resolve(data);
    }];
}


- (void) init: (NSDictionary *)arguments result:(HippyMethodInvoideCallback)result {
    NSString* endpoint = arguments[@"endpoint"];
    NSString* accessKeyId = arguments[@"accessKeyId"];
    NSString* accessKeySecret = arguments[@"accessKeySecret"];
    NSString* securityToken = arguments[@"securityToken"];
    
    if (endpoint.length == 0) {
        result(nil, InvokeError(-1, @"endpoint不能为空"));
        return;
    }
    if (accessKeyId.length == 0) {
        result(nil, InvokeError(-1, @"accessKeyId不能为空"));
        return;
    }
    
    if (accessKeySecret.length == 0) {
        result(nil, InvokeError(-1, @"accessKeySecret不能为空"));
        return;
    }
    if (securityToken.length == 0) {
            result(nil, InvokeError(-1, @"securityToken不能为空"));
        return;
    }
    
    [[OssManager instance] initWithAccessKeyId:accessKeyId accessKeySecret:accessKeySecret securityToken:securityToken endpoint:endpoint initCallback:^{
        Log(@"OssPlugin.init finish callback");
        dispatch_async(dispatch_get_main_queue(), ^{
            result(@(true), nil);
        });
    }];
}

- (void) upload: (NSDictionary *)arguments result:(HippyMethodInvoideCallback)result {
    NSString* bucketName = arguments[@"bucketName"];
    NSString* objectKey = arguments[@"objectKey"];
    NSString* file = arguments[@"file"];
    NSString* taskId = arguments[@"id"];
    
    if (bucketName.length == 0) {
        result(nil, InvokeError(-1, @"bucketName不能为空"));
        return;
    }
    if (objectKey.length == 0) {
        result(nil, InvokeError(-1, @"objectKey不能为空"));
        return;
    }
    
    if (file.length == 0) {
        result(nil, InvokeError(-1, @"file不能为空"));
        return;
    }
    if (taskId.length == 0) {
        result(nil, InvokeError(-1, @"id不能为空"));
        return;
    }
    
    Log(@"OssPlugin.upload bucketName =%@", bucketName);
    
    OssFileUploader* uploader = [[OssFileUploader alloc] initWithTaskId:taskId bucketName:bucketName objectKey:objectKey file:file];
    [uploader start];
    
    result(@(true), nil);
}

@end
