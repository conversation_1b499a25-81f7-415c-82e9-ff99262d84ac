#import "AbcImageLoaderModule.h"
#import "Log.h"
#import "UIUtils.h"
#import "ABCMacros.h"
#import "HostHippyMessageBridge.h"
#import "HippyImageView.h"
#import <SDWebImage.h>

#import "HippyImageViewCustomLoader.h"
#import "HippyBundleURLProvider.h"

@interface AbcImageLoaderModule() <HippyImageViewCustomLoader>
@end

@implementation AbcImageLoaderModule{
    NSString* _localhost;
    NSString* _realLocalHost;
}

HIPPY_EXPORT_MODULE(AbcImageLoader)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}
- (instancetype)init
{
    self = [super init];
    if (self) {
    
    }
    return self;
}
- (void)imageView:(UIImageView *)imageView
        loadAtUrl:(NSURL *)url
 placeholderImage:(UIImage *)placeholderImage
          context:(void *)context
         progress:(void (^)(long long, long long))progressBlock
        completed:(void (^)(NSData *, UIImage*image, NSURL *, NSError *))completedBlock {
    
    //调整127.0.0.1调试模式下的地址
    if (_realLocalHost == nil) {
        HippyBundleURLProvider* provider = [HippyBundleURLProvider sharedInstance];
        _realLocalHost = [NSString stringWithFormat:@"http://%@", [provider localhost]];
    }
    
    if (_localhost == nil) {
        HippyBundleURLProvider* provider = [HippyBundleURLProvider sharedInstance];
        _localhost = [NSString stringWithFormat:@"http://127.0.0.1:%@", [provider localhostPort]];
    }
    
    NSURL* finalUrl = url;
    if ([url.absoluteString hasPrefix:_localhost]) {
        NSString* finalURLStr  = [NSString stringWithFormat:@"%@%@", _realLocalHost, [url.absoluteString substringFromIndex:_localhost.length]];
        finalUrl = [NSURL URLWithString:finalURLStr];
    }
    
    [imageView sd_setImageWithURL:finalUrl placeholderImage:placeholderImage completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        if (completedBlock) {
            completedBlock(image.images != nil?UIImagePNGRepresentation(image):nil, image, url, error);
        }
    }];
    
}

- (void)cancelImageDownload:(UIImageView *)imageView withUrl:(NSURL *)url {
    [imageView sd_cancelCurrentImageLoad];
}

@end
