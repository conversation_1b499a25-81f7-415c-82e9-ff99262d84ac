//
//  AliPayModule.m
//  Runner
//
//  Created by f<PERSON><PERSON> on 2020/8/13.
//
//


#import "AliPayModule.h"
#import "WXApiHandler.h"
#import "ABCMacros.h"
#import "StringUtil.h"

#import <AlipaySDK/AlipaySDK.h>

@implementation AliPayModule

HIPPY_EXPORT_MODULE(AliPay)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}



- (instancetype)init
{
    self = [super init];
    if (self) {
        
    }
    return self;
}


HIPPY_EXPORT_METHOD(payment:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSString *orderInfo = params[@"orderInfo"];
    // NOTE: 如果加签成功，则继续执行支付
    if (orderInfo != nil) {
        NSString *appScheme = @"abcyun";
                
        // NOTE: 调用支付结果开始支付
        [[AlipaySDK defaultService] payOrder:orderInfo fromScheme:appScheme callback:^(NSDictionary *resultDic) {
            resolve(resultDic);
        }];
    }
}


@end
