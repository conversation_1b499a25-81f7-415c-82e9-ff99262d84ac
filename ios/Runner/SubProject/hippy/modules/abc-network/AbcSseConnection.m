/*!
* iOS SDK
*
* <PERSON><PERSON> is pleased to support the open source community by making
* Hippy available.
*
* Copyright (C) 2019 THL A29 Limited, a Tencent company.
* All rights reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

#import "AbcSseConnection.h"
#import "HippyLog.h"

@interface AbcSseConnection () <NSURLSessionDataDelegate>

@property (nonatomic, strong) NSURLSession *session;
@property (nonatomic, strong) NSURLSessionDataTask *dataTask;
@property (nonatomic, strong) AbcSseEventBuilder *eventBuilder;
@property (nonatomic, assign) AbcSseConnectionState state;
@property (nonatomic, assign) NSTimeInterval createTime;
@property (nonatomic, assign) NSTimeInterval connectTime;
@property (nonatomic, strong, nullable) NSString *lastEventId;

@end

@implementation AbcSseConnection

- (instancetype)initWithConnectionId:(NSString *)connectionId 
                             request:(NSURLRequest *)request 
                            delegate:(nullable id<AbcSseConnectionDelegate>)delegate {
    self = [super init];
    if (self) {
        _connectionId = connectionId;
        _request = request;
        _delegate = delegate;
        _state = AbcSseConnectionStateDisconnected;
        _createTime = [[NSDate date] timeIntervalSince1970];
        _connectTime = 0;
        _eventBuilder = [[AbcSseEventBuilder alloc] init];
        
        // 创建 URLSession 配置
        NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
        config.timeoutIntervalForRequest = 30.0; // 连接超时30秒
        config.timeoutIntervalForResource = 0; // 资源超时设为0（无限制）
        config.HTTPShouldUsePipelining = NO;
        config.HTTPShouldSetCookies = YES;
        config.HTTPCookieAcceptPolicy = NSHTTPCookieAcceptPolicyAlways;
        
        _session = [NSURLSession sessionWithConfiguration:config 
                                                 delegate:self 
                                            delegateQueue:nil];
    }
    return self;
}

- (void)connect {
    if (self.state == AbcSseConnectionStateConnecting || 
        self.state == AbcSseConnectionStateConnected) {
        HippyLogWarn(@"SSE connection %@ is already connecting or connected", self.connectionId);
        return;
    }
    
    self.state = AbcSseConnectionStateConnecting;
    
    // 创建可变请求以添加 SSE 必需的请求头
    NSMutableURLRequest *mutableRequest = [self.request mutableCopy];
    [mutableRequest setValue:@"text/event-stream" forHTTPHeaderField:@"Accept"];
    [mutableRequest setValue:@"no-cache" forHTTPHeaderField:@"Cache-Control"];
    
    // 如果有 lastEventId，添加到请求头
    if (self.lastEventId) {
        [mutableRequest setValue:self.lastEventId forHTTPHeaderField:@"Last-Event-ID"];
    }
    
    self.dataTask = [self.session dataTaskWithRequest:mutableRequest];
    [self.dataTask resume];
    
    HippyLogInfo(@"SSE connection %@ started connecting to %@", 
                 self.connectionId, mutableRequest.URL.absoluteString);
}

- (void)disconnect {
    if (self.state == AbcSseConnectionStateDisconnected) {
        return;
    }
    
    self.state = AbcSseConnectionStateDisconnected;
    
    if (self.dataTask) {
        [self.dataTask cancel];
        self.dataTask = nil;
    }
    
    if (self.session) {
        [self.session invalidateAndCancel];
        self.session = nil;
    }
    
    [self.delegate sseConnectionDidDisconnect:self.connectionId];
    
    HippyLogInfo(@"SSE connection %@ disconnected", self.connectionId);
}

- (BOOL)isConnected {
    return self.state == AbcSseConnectionStateConnected;
}

- (NSTimeInterval)connectionDuration {
    if (self.connectTime > 0) {
        return [[NSDate date] timeIntervalSince1970] - self.connectTime;
    }
    return 0;
}

- (NSDictionary *)connectionInfo {
    return @{
        @"connectionId": self.connectionId,
        @"url": self.request.URL.absoluteString ?: @"",
        @"isConnected": @(self.isConnected),
        @"createTime": @((long long)(self.createTime * 1000)), // 转换为毫秒
        @"connectTime": @((long long)(self.connectTime * 1000)),
        @"connectionDuration": @((long long)(self.connectionDuration * 1000)),
        @"lastEventId": self.lastEventId ?: [NSNull null]
    };
}

- (void)dealloc {
    [self disconnect];
}

#pragma mark - NSURLSessionDataDelegate

- (void)URLSession:(NSURLSession *)session 
          dataTask:(NSURLSessionDataTask *)dataTask
didReceiveResponse:(NSURLResponse *)response 
 completionHandler:(void (^)(NSURLSessionResponseDisposition))completionHandler {
    
    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
    
    if (httpResponse.statusCode == 200) {
        self.state = AbcSseConnectionStateConnected;
        self.connectTime = [[NSDate date] timeIntervalSince1970];
        
        [self.delegate sseConnectionDidConnect:self.connectionId response:httpResponse];
        
        HippyLogInfo(@"SSE connection %@ established successfully", self.connectionId);
        completionHandler(NSURLSessionResponseAllow);
    } else {
        NSError *error = [NSError errorWithDomain:@"AbcSseConnection" 
                                             code:httpResponse.statusCode 
                                         userInfo:@{
                                             NSLocalizedDescriptionKey: [NSString stringWithFormat:@"HTTP %ld", (long)httpResponse.statusCode]
                                         }];
        [self handleError:error];
        completionHandler(NSURLSessionResponseCancel);
    }
}

- (void)URLSession:(NSURLSession *)session 
          dataTask:(NSURLSessionDataTask *)dataTask 
    didReceiveData:(NSData *)data {
    
    NSString *string = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    if (!string) {
        return;
    }
    
    // 按行分割数据
    NSArray<NSString *> *lines = [string componentsSeparatedByString:@"\n"];
    
    for (NSString *line in lines) {
        [self processSSELine:line];
    }
}

- (void)URLSession:(NSURLSession *)session 
              task:(NSURLSessionTask *)task 
didCompleteWithError:(nullable NSError *)error {
    
    if (error) {
        [self handleError:error];
    } else {
        self.state = AbcSseConnectionStateDisconnected;
        [self.delegate sseConnectionDidDisconnect:self.connectionId];
    }
}

#pragma mark - Private Methods

- (void)processSSELine:(NSString *)line {
    // 移除行尾的回车符
    line = [line stringByTrimmingCharactersInSet:[NSCharacterSet newlineCharacterSet]];
    
    if (line.length == 0) {
        // 空行表示事件结束
        AbcSseEvent *event = [self.eventBuilder build];
        if ([event hasData]) {
            // 保存最后的事件 ID
            if (event.eventId) {
                self.lastEventId = event.eventId;
            }
            
            [self.delegate sseConnection:self.connectionId didReceiveEvent:event];
        }
        [self.eventBuilder reset];
        return;
    }
    
    // 忽略注释行
    if ([line hasPrefix:@":"]) {
        return;
    }
    
    // 解析字段
    NSRange colonRange = [line rangeOfString:@":"];
    if (colonRange.location == NSNotFound) {
        // 没有冒号，整行作为字段名，值为空
        [self processSSEField:line value:@""];
    } else {
        NSString *field = [line substringToIndex:colonRange.location];
        NSString *value = @"";
        
        if (colonRange.location + 1 < line.length) {
            value = [line substringFromIndex:colonRange.location + 1];
            // 移除值开头的单个空格
            if ([value hasPrefix:@" "]) {
                value = [value substringFromIndex:1];
            }
        }
        
        [self processSSEField:field value:value];
    }
}

- (void)processSSEField:(NSString *)field value:(NSString *)value {
    if ([field isEqualToString:@"data"]) {
        [self.eventBuilder appendData:value];
    } else if ([field isEqualToString:@"event"]) {
        [self.eventBuilder setType:value];
    } else if ([field isEqualToString:@"id"]) {
        [self.eventBuilder setEventId:value];
    } else if ([field isEqualToString:@"retry"]) {
        NSInteger retryValue = [value integerValue];
        if (retryValue > 0) {
            [self.eventBuilder setRetry:retryValue];
        }
    }
    // 忽略未知字段
}

- (void)handleError:(NSError *)error {
    self.state = AbcSseConnectionStateError;
    [self.delegate sseConnection:self.connectionId didFailWithError:error];
    
    HippyLogError(@"SSE connection %@ failed with error: %@", self.connectionId, error.localizedDescription);
}

@end
