/*!
* iOS SDK
*
* <PERSON><PERSON> is pleased to support the open source community by making
* Hippy available.
*
* Copyright (C) 2019 THL A29 Limited, a Tencent company.
* All rights reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

#import <Foundation/Foundation.h>
#import "AbcSseEvent.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, AbcSseConnectionState) {
    AbcSseConnectionStateConnecting = 0,
    AbcSseConnectionStateConnected = 1,
    AbcSseConnectionStateDisconnected = 2,
    AbcSseConnectionStateError = 3
};

@protocol AbcSseConnectionDelegate <NSObject>

- (void)sseConnectionDidConnect:(NSString *)connectionId response:(NSHTTPURLResponse *)response;
- (void)sseConnection:(NSString *)connectionId didReceiveEvent:(AbcSseEvent *)event;
- (void)sseConnection:(NSString *)connectionId didFailWithError:(NSError *)error;
- (void)sseConnectionDidDisconnect:(NSString *)connectionId;

@end

/**
 * SSE 连接管理类
 * 对应 Android 端的 SseConnection.java
 */
@interface AbcSseConnection : NSObject

@property (nonatomic, readonly, strong) NSString *connectionId;
@property (nonatomic, readonly, strong) NSURLRequest *request;
@property (nonatomic, readonly, assign) AbcSseConnectionState state;
@property (nonatomic, readonly, assign) NSTimeInterval createTime;
@property (nonatomic, readonly, assign) NSTimeInterval connectTime;
@property (nonatomic, readonly, strong, nullable) NSString *lastEventId;
@property (nonatomic, weak, nullable) id<AbcSseConnectionDelegate> delegate;

- (instancetype)initWithConnectionId:(NSString *)connectionId 
                             request:(NSURLRequest *)request 
                            delegate:(nullable id<AbcSseConnectionDelegate>)delegate;

- (void)connect;
- (void)disconnect;
- (BOOL)isConnected;
- (NSTimeInterval)connectionDuration;
- (NSDictionary *)connectionInfo;

@end

NS_ASSUME_NONNULL_END
