#import "ShareModule.h"
#import <Photos/Photos.h>
#import <UIKit/UIKit.h>
#import "ABCMacros.h"
#import "HippyMethodInvoideCallback.h"

@implementation ShareModule


HIPPY_EXPORT_MODULE(Share)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}


HIPPY_EXPORT_METHOD(share:(NSDictionary*)arguments resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    
    NSArray *array = arguments[@"list"];
    NSString *shareType = arguments[@"type"];
    NSString *subject = arguments[@"subject"];
    
    if (array.count == 0) {
        reject(@"-1", @"Non-empty list expected", nil);
        return;
    }
    
    
    NSDictionary*sharePositionOrigin =  arguments[@"sharePositionOrigin"];
    NSNumber *originX = sharePositionOrigin[@"originX"];
    NSNumber *originY = sharePositionOrigin[@"originY"];
    NSNumber *originWidth = sharePositionOrigin[@"originWidth"];
    NSNumber *originHeight = sharePositionOrigin[@"originHeight"];
    
    CGRect originRect = CGRectZero;
    if (originX != nil && originY != nil && originWidth != nil && originHeight != nil) {
        originRect = CGRectMake([originX doubleValue], [originY doubleValue],
                                [originWidth doubleValue], [originHeight doubleValue]);
    }
    
    if ([shareType isEqualToString:@"text"]) {
        [self share:array atSource:originRect withSubject:subject];
        resolve(nil);
    }  else if ([shareType isEqualToString:@"image"]) {
        NSMutableArray * imageArray = [[NSMutableArray alloc] init];
        for (NSString * path in array) {
            UIImage *image = [UIImage imageWithContentsOfFile:path];
            [imageArray addObject:image];
        }
        [self share:imageArray atSource:originRect withSubject:subject];
    } else {
        NSMutableArray * urlArray = [[NSMutableArray alloc] init];
        for (NSString * path in array) {
            NSURL *url = [NSURL fileURLWithPath:path];
            [urlArray addObject:url];
        }
        [self share:urlArray atSource:originRect withSubject:subject];
        resolve(nil);
    }
}

-(void)share:(NSArray *)sharedItems atSource:(CGRect)origin withSubject:(NSString *) subject {
    UIActivityViewController *activityViewController = [[UIActivityViewController alloc] initWithActivityItems:sharedItems applicationActivities:nil];
    
    UIViewController *controller =[UIApplication sharedApplication].keyWindow.rootViewController;
    
    activityViewController.popoverPresentationController.sourceView = controller.view;
    if (!CGRectIsEmpty(origin)) {
        activityViewController.popoverPresentationController.sourceRect = origin;
    }
    [activityViewController setValue:subject forKey:@"subject"];
    [controller presentViewController:activityViewController animated:YES completion:nil];
}

@end
