//
//  FileUtils.h
//  Runner
//
//  Created by f<PERSON><PERSON> on 2020/8/4.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FileUtils : NSObject
{
}
/** converts a relative path to a full path */

+ (BOOL)isFileExisted:(NSString*)path;
+ (BOOL)createFile:(NSString*)path;
+ (BOOL)deleteFile:(NSString*)path;
+ (BOOL)copyFile:(NSString*)srcPath :(NSString *)dstPath;

+ (BOOL)createFile:(NSString *)path withError:(NSError **)error;
+ (BOOL)deleteFile:(NSString *)pathile:(NSString *)path withError:(NSError **)error;
+ (BOOL)copyFile:(NSString *)srcPath toPath:(NSString *)dstPath withError:(NSError **)error;
//////////////////////////////////////
//extend by wentaowang

+(NSString*)getAppDirectory:(NSSearchPathDirectory)directoryType;
+(BOOL)isDirectoryExisted:(NSString*)directoryPath;

+(NSString*)fileMD5:(NSString*)path;

+(void)setFile:(NSString*)filePath  attribute:(NSDictionary*)attribute error:(NSError*)error;
+(void)createDirectory:(NSString*)directoryPath error:(NSError*)error;
+(void)deleteDirectory:(NSString*)directoryPath error:(NSError*)error;
+(void)deleteFile:(NSString*)filePath error:(NSError*)error;
+(void)copyItemFrom:(NSString*)sourcePath to:(NSString*)targetPath error:(NSError*)error;

+(void)clearDirectory:(NSString*)directoryPath error:(NSError*)error;

//for backup
+(BOOL)disableFileBackup:(NSString*)filePath;

+(double)getDirectorySizeForPath:(NSString*)path;
+(double)getFileSystemFreeSize;

+ (BOOL)moveFileAtPath:(NSString *)srcPath toPath:(NSString *)dstPath error:(NSError **)error;



//////////////////////////////////////
@end
NS_ASSUME_NONNULL_END
