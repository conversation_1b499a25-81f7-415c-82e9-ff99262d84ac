//
//  FileUtils.m
//  Runner
//
//  Created by feihe on 2020/8/4.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import "FileUtils.h"
#import <CommonCrypto/CommonDigest.h>
#import <sys/xattr.h>

@implementation FileUtils

+ (BOOL)deleteFile:(NSString*)path
{
    if(path == nil || [path length] <= 0)
        return NO;
    return [[NSFileManager defaultManager] removeItemAtPath:path error:nil];
}


+ (BOOL)isFileExisted:(NSString*)path
{
    if(path == nil || [path length] <= 0)
        return NO;
    return [[NSFileManager defaultManager] fileExistsAtPath:path];
}

+ (BOOL)createFile:(NSString*)path
{
    return [[NSFileManager defaultManager] createDirectoryAtPath:path withIntermediateDirectories:YES attributes:nil error:nil];
}

+ (BOOL)copyFile:(NSString*)srcPath :(NSString *)dstPath
{
    return [[NSFileManager defaultManager] copyItemAtPath:srcPath toPath:dstPath error:nil];
}

+ (BOOL)createFile:(NSString *)path withError:(NSError **)error {
    return [[NSFileManager defaultManager] createDirectoryAtPath:path withIntermediateDirectories:YES attributes:nil error:error];
}

+ (BOOL)deleteFile:(NSString *)path withError:(NSError **)error {
    if(path == nil || [path length] <= 0)
        return NO;
    return [[NSFileManager defaultManager] removeItemAtPath:path error:error];
}

+ (BOOL)copyFile:(NSString *)srcPath toPath:(NSString *)dstPath withError:(NSError **)error {
    return [[NSFileManager defaultManager] copyItemAtPath:srcPath toPath:dstPath error:error];
}

+(NSString*)getAppDirectory:(NSSearchPathDirectory)directoryType
{
    if(directoryType==NSDocumentDirectory)
    {
        NSArray* dirArray = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,NSUserDomainMask,YES);
        return [dirArray objectAtIndex:0];
    }
    else if(directoryType==NSCachesDirectory)
    {
        NSArray* dirArray = NSSearchPathForDirectoriesInDomains(NSCachesDirectory,NSUserDomainMask,YES);
        return [dirArray objectAtIndex:0];
    }
    return nil;
}

+(void)setFile:(NSString*)filePath  attribute:(NSDictionary*)attribute error:(NSError*)error
{
    if(NO == [FileUtils isFileExisted:filePath])
        return;
    
    [[NSFileManager defaultManager] setAttributes:attribute ofItemAtPath:filePath error:&error];
}

+(BOOL)isDirectoryExisted:(NSString*)directoryPath
{
    BOOL isDirectory = NO;
    BOOL result = [[NSFileManager defaultManager] fileExistsAtPath:directoryPath isDirectory:&isDirectory];
    return result&isDirectory;
}

+(void)createDirectory:(NSString*)directoryPath error:(NSError*)error
{
    [[NSFileManager defaultManager] createDirectoryAtPath:directoryPath
           withIntermediateDirectories:YES
                            attributes:nil
                                 error:&error];
}

+(void)deleteDirectory:(NSString*)directoryPath error:(NSError**)error
{
    if(NO == [FileUtils isDirectoryExisted:directoryPath])
        return;
    
    [[NSFileManager defaultManager] removeItemAtPath:directoryPath error:&error];
}

+(void)clearDirectory:(NSString*)directoryPath error:(NSError*)error
{
       NSArray* files = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:directoryPath error:&error];
    
    if(error != nil)
        return;
    for(NSString* file in files)
    {
        [[NSFileManager defaultManager] removeItemAtPath:[NSString stringWithFormat:@"%@/%@",directoryPath,file]
                                error:&error];
    }
}


+(void)deleteFile:(NSString*)filePath error:(NSError*)error
{
    if(NO == [FileUtils isFileExisted:filePath])
        return;
    
    [[NSFileManager defaultManager] removeItemAtPath:filePath error:&error];
}

+(void)copyItemFrom:(NSString*)sourcePath to:(NSString*)targetPath error:(NSError*)error
{
    if([FileUtils isFileExisted:targetPath])
        [FileUtils deleteFile:targetPath error:error];
    [[NSFileManager defaultManager] copyItemAtPath:sourcePath toPath:targetPath error:&error];
}

+(double)getDirectorySizeForPath:(NSString*)directoryPath
{
    if(NO == [FileUtils isDirectoryExisted:directoryPath])
        return -1;
    
    NSDirectoryEnumerator* e = [[NSFileManager defaultManager] enumeratorAtPath:directoryPath];
    
    if(e == NULL)
        return -1;
    
    double totalSize = 0;
    while ([e nextObject])
    {
        NSDictionary *attributes = [e fileAttributes];
        
        NSNumber *fileSize = [attributes objectForKey:NSFileSize];
        
        totalSize += [fileSize longLongValue];
    }
    
    return totalSize;
}

+(double)getFileSystemFreeSize
{
    NSError* error = nil;
    
    NSDictionary* attribute = [[NSFileManager defaultManager] attributesOfFileSystemForPath:[FileUtils getAppDirectory:NSDocumentationDirectory]
                                                                   error:&error];
    if(error != nil)
        return -1;
    
    NSNumber* size = [attribute objectForKey:NSFileSystemFreeSize];
    
    return     [size doubleValue];
}

+ (NSString*)fileMD5:(NSString*)path
{
    NSFileHandle *handle = [NSFileHandle fileHandleForReadingAtPath:path];
    if(!handle)// 文件不存在
    {
        return nil;
    }
    
    CC_MD5_CTX md5;
    
    CC_MD5_Init(&md5);
    
    BOOL done = NO;
    while(!done)
    {
        @autoreleasepool
        {
            NSData* fileData = [handle readDataOfLength: 16*1024];
            CC_MD5_Update(&md5, [fileData bytes], (CC_LONG)[fileData length]);
            if( [fileData length] == 0 ) done = YES;
        }
    }
    unsigned char digest[CC_MD5_DIGEST_LENGTH];
    CC_MD5_Final(digest, &md5);
    NSString* s = [NSString stringWithFormat: @"%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x",
                   digest[0], digest[1],
                   digest[2], digest[3],
                   digest[4], digest[5],
                   digest[6], digest[7],
                   digest[8], digest[9],
                   digest[10], digest[11],
                   digest[12], digest[13],
                   digest[14], digest[15]];
    return s;
}

+(BOOL)disableFileBackup:(NSString*)filePath
{
    BOOL flag = NO;
    NSError* error = nil;
    NSURL* url = [NSURL fileURLWithPath:filePath];
    flag=[url setResourceValue:[NSNumber numberWithBool: YES]
                   forKey:NSURLIsExcludedFromBackupKey
                    error:&error];
    return flag;
}


+ (BOOL)moveFileAtPath:(NSString *)srcPath toPath:(NSString *)dstPath error:(NSError **)error {
    return [[NSFileManager defaultManager] moveItemAtPath:srcPath toPath:dstPath error:error];
}
@end
