//
//  UIView+ABCExt.m
//  Runner
//
//  Created by feihe on 2020/5/30.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import "UIView+ABCExt.h"

@implementation UIView (AbcExt)

-(UIView*) nearestSupperViewWithClass:(Class) clz {
    UIView* current = self;
    do {
        if ([current isKindOfClass:clz]) {
            return current;
        }
        current = current.superview;
    }while(current);
    
    return nil;
}
@end
