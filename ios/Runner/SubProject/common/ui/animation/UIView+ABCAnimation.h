#import <UIKit/UIKit.h>
//#import "MttAnimationCenter.h"
#import "ABCAnimationDefine.h"

@interface UIView (ABCAnimation)

- (UIView *)mttSnapshotView;

- (void)animateToFrame:(CGRect)toFrame
              duration:(NSTimeInterval)duration
       completionBlock:(void (^)(void))completionBlock;

/*
- (void)animateToFrame:(CGRect)toFrame
              duration:(NSTimeInterval)duration
                easing:(MttAnimationEasingMode)easing
       completionBlock:(void (^)(void))completionBlock;
*/

- (void)animateToOpacity:(CGFloat)toOpacity
                duration:(NSTimeInterval)duration
         completionBlock:(void (^)(void))completionBlock;

/*
- (void)animateToOpacity:(CGFloat)toOpacity
                duration:(NSTimeInterval)duration
                  easing:(MttAnimationEasingMode)easing
         completionBlock:(void (^)(void))completionBlock;
*/

/*
- (void)animateToTransform:(CGAffineTransform)toTransform
                  duration:(NSTimeInterval)duration
           completionBlock:(void (^)(void))completionBlock;
*/

- (UIImage *)mttSnapshot;
#pragma mark -7.4新动画弹性
#pragma mark -
+ (void)mttBounceAnimateWithDuration:(NSTimeInterval)duration animations:(void (^)(void))animations completion:(void (^)(BOOL finished))completion;
+ (void)mttMenuBounceAnimateWithDuration:(NSTimeInterval)duration animations:(void (^)(void))animations completion:(void (^)(BOOL finished))completion;
#pragma mark -6.1新动画方法
#pragma mark -
+ (void)mttAnimateWithDuration:(NSTimeInterval)duration animations:(void (^)(void))animations;
+ (void)mttAnimateWithDuration:(NSTimeInterval)duration animations:(void (^)(void))animations completion:(void (^)(BOOL finished))completion;
+ (void)mttAnimateWithDuration:(NSTimeInterval)duration delay:(NSTimeInterval)delay options:(UIViewAnimationOptions)options animations:(void (^)(void))animations completion:(void (^)(BOOL finished))completion;

#pragma mark -6.0新动画方法
#pragma mark -
/**
 *  显示的默认动画
 **/
- (void)animateToFrameForShowDefault:(CGRect)toFrame
                            duration:(NSTimeInterval)duration
                     completionBlock:(void (^)(void))completionBlock;
//可延时的接口
- (void)animateToFrameForShowDefault:(CGRect)toFrame
                            duration:(NSTimeInterval)duration
                               delay: (CGFloat) delay
                     completionBlock:(void (^)(void))completionBlock;

/**
 *  隐藏的默认动画
 */
- (void)animateToFrameForHideDefault:(CGRect)toFrame
                            duration:(NSTimeInterval)duration
                     completionBlock:(void (^)(void))completionBlock;
- (void)animateToFrameForHideDefault:(CGRect)toFrame
                            duration:(NSTimeInterval)duration
                            function:(CAMediaTimingFunction*)function
                     completionBlock:(void (^)(void))completionBlock;

/**
 *  遮罩使用的动画
 **/
- (void)animateToOpacityDefault:(CGFloat)toOpacity
                       duration:(NSTimeInterval)duration
                completionBlock:(void (^)(void))completionBlock;
//可延时的接口
- (void)animateToOpacityDefault:(CGFloat)toOpacity
                       duration:(NSTimeInterval)duration
                          delay: (CGFloat) delay
                completionBlock:(void (^)(void))completionBlock;

/**
 *  缩放动画
 **/
- (void)animateFromScale:(CGFloat) from toScale:(CGFloat) to
                duration:(NSTimeInterval)duration
         completionBlock:(void (^)(void))completionBlock;

- (void)animateFromScale:(CGFloat) from toScale:(CGFloat) to
                duration:(NSTimeInterval)duration
                function:(CAMediaTimingFunction*)function
         completionBlock:(void (^)(void))completionBlock;
/**
 *  移除缩放动画
 **/
- (void) removeScaleAnimation;

- (CAAnimation *) makePrivacyModeAnimation;

- (void)mttRippleAnimation:(CGFloat)duration origin:(CGPoint)origin completion:(void(^)(void))completion;
- (void)mttRippleAnimation:(CGFloat)duration
                    origin:(CGPoint)origin
                   options:(NSDictionary*)options
                completion:(void(^)(void))completion;

- (void)mttScaleEffect:(CGFloat)duration autoRepeat:(BOOL)autoRepeat completion:(void(^)(void))completion;


@end
