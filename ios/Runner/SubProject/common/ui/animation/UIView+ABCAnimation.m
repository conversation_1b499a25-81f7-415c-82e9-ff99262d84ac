#import "UIView+ABCAnimation.h"
#import "ABCCGRectUtil.h"
#import "ABCAnimationDefine.h"
#import "CAKeyframeAnimation+AHEasing.h"
#import "ABCAnimationCenter.h"
#import <QuartzCore/CAAnimation.h>

@implementation UIView (ABCAnimation)

#pragma  mark -
#pragma  mark animate frame
- (void)animateToFrame:(CGRect)toFrame
              duration:(NSTimeInterval)duration
              function:(AHEasingFunction)function
       completionBlock:(void (^)(void))completionBlock
{
    [CATransaction begin];
    [CATransaction setCompletionBlock:completionBlock];

    CGRect fromFrame = self.frame;

    CGPoint anchorPoint = self.layer.anchorPoint;

    CGFloat fromDeltaX = CGRectGetWidth(fromFrame) * (anchorPoint.x - 0.5);
    CGFloat fromDeltaY = CGRectGetHeight(fromFrame) * (anchorPoint.y - 0.5);
    CGFloat toDeltaX = CGRectGetWidth(toFrame) * (anchorPoint.x - 0.5);
    CGFloat toDeltaY = CGRectGetHeight(toFrame) * (anchorPoint.y - 0.5);

    CGPoint fromPosition = CGPointMake(CGRectGetMidX(fromFrame) + fromDeltaX, CGRectGetMidY(fromFrame) + fromDeltaY);
    CGPoint toPosition = CGPointMake(CGRectGetMidX(toFrame) + toDeltaX, CGRectGetMidY(toFrame) + toDeltaY);
    CGSize fromSize = self.layer.bounds.size;
    CGSize toSize = toFrame.size;
    CGPoint boundsOrigin = self.layer.bounds.origin;

    CAKeyframeAnimation *positionAnimation = nil;

    positionAnimation = [CAKeyframeAnimation animationWithKeyPath:@"position"
                                                         function:function
                                                        fromPoint:fromPosition
                                                          toPoint:toPosition];

    [positionAnimation setDuration:duration];

    CAKeyframeAnimation *sizeAnimation = nil;

    sizeAnimation = [CAKeyframeAnimation animationWithKeyPath:@"bounds.size"
                                                     function:function
                                                     fromSize:fromSize
                                                       toSize:toSize];

    [sizeAnimation setDuration:duration];

    [self.layer addAnimation:positionAnimation forKey:@"positionAnimation"];
    [self.layer addAnimation:sizeAnimation forKey:@"sizeAnimation"];

    self.layer.bounds = CGRectMake(boundsOrigin.x, boundsOrigin.y, toSize.width, toSize.height);
    self.layer.position = toPosition;

    [CATransaction commit];
}

- (void)animateToFrame:(CGRect)toFrame
              duration:(NSTimeInterval)duration
       completionBlock:(void (^)(void))completionBlock
{

        [self animateToFrame:toFrame
                    duration:duration
                    function:[[ABCAnimationCenter defaultCenter] defaultFunction]
             completionBlock:completionBlock];


}
/*
- (void)animateToFrame:(CGRect)toFrame
              duration:(NSTimeInterval)duration
                easing:(MttAnimationEasingMode)easing
       completionBlock:(void (^)(void))completionBlock
{
        [self animateToFrame:toFrame
                    duration:duration
                    function:[[ABCAnimationCenter defaultCenter] defaultFunctionWithEasing:easing]
             completionBlock:completionBlock];



}
*/

/**
 *  显示的默认动画
 **/
- (void)animateToFrameForShowDefault:(CGRect)toFrame
              duration:(NSTimeInterval)duration
       completionBlock:(void (^)(void))completionBlock
{

    //方法1
//    [self animateToFrame:toFrame
//                duration:duration
//                function:[ABCAnimationCenter functionWithCurve:CurveTypeCubic easing:MttAnimationEaseOut]
//         completionBlock:completionBlock];

    //方法2
    [self animateToFrameBySysemTimeFunc:toFrame duration:duration delay: 0.0 function:ABC_DEFAULT_TIMING_FUNCTION completionBlock:completionBlock];
}

- (void)animateToFrameForShowDefault:(CGRect)toFrame
                            duration:(NSTimeInterval)duration
                            delay: (CGFloat) delay
                     completionBlock:(void (^)(void))completionBlock
{
    [self animateToFrameBySysemTimeFunc:toFrame duration:duration delay: delay function:ABC_DEFAULT_TIMING_FUNCTION completionBlock:completionBlock];
}

/**
 *  隐藏的默认动画
 */
- (void)animateToFrameForHideDefault:(CGRect)toFrame
                            duration:(NSTimeInterval)duration
                     completionBlock:(void (^)(void))completionBlock
{
//    [self animateToFrame:toFrame
//                duration:duration
//                function:[ABCAnimationCenter functionWithCurve:CurveTypeCubic easing:MttAnimationEaseIn]
//         completionBlock:completionBlock];

    //方法2
//    [self animateToFrameBySysemTimeFunc:toFrame duration:duration function:[CAMediaTimingFunction functionWithControlPoints:1 :0 :.8 :.3] completionBlock:completionBlock];

    [self animateToFrameBySysemTimeFunc:toFrame duration:duration delay: 0.0 function:ABC_DEFAULT_TIMING_FUNCTION completionBlock:completionBlock];
}

- (void)animateToFrameForHideDefault:(CGRect)toFrame
                            duration:(NSTimeInterval)duration
                            function:(CAMediaTimingFunction*)function
                     completionBlock:(void (^)(void))completionBlock
{
    [self animateToFrameBySysemTimeFunc:toFrame duration:duration delay: 0.0 function:function completionBlock:completionBlock];
}

#pragma mark -7.4新动画弹性
#pragma mark -
+ (void)mttBounceAnimateWithDuration:(NSTimeInterval)duration animations:(void (^)(void))animations completion:(void (^)(BOOL finished))completion
{
    [UIView animateWithDuration:duration delay:0 usingSpringWithDamping:0.82 initialSpringVelocity:10 options:UIViewAnimationOptionCurveEaseIn animations:animations completion:completion];
}

+ (void)mttMenuBounceAnimateWithDuration:(NSTimeInterval)duration animations:(void (^)(void))animations completion:(void (^)(BOOL finished))completion
{
    if (animations) {
        [CATransaction begin];
        [CATransaction setAnimationTimingFunction:[CAMediaTimingFunction functionWithControlPoints:0.4 :0.0 :0.2 :1]];
        [CATransaction setAnimationDuration:duration];

        [UIView animateWithDuration:duration animations:^{
            animations();
        } completion:completion];

        [CATransaction commit];
    }
}

#pragma mark -6.1新动画方法
#pragma mark -
+ (void)mttAnimateWithDuration:(NSTimeInterval)duration animations:(void (^)(void))animations
{
    [UIView mttAnimateWithDuration:duration delay:0 options:0 animations:animations completion:^(BOOL finished) {}];
}

+ (void)mttAnimateWithDuration:(NSTimeInterval)duration animations:(void (^)(void))animations completion:(void (^)(BOOL finished))completion;
{
    [UIView mttAnimateWithDuration:duration delay:0 options:0 animations:animations completion:completion];
}

+ (void)mttAnimateWithDuration:(NSTimeInterval)duration delay:(NSTimeInterval)delay options:(UIViewAnimationOptions)options animations:(void (^)(void))animations completion:(void (^)(BOOL finished))completion;
{
    [UIView animateWithDuration:duration delay:delay options:options animations:^{
        [CATransaction begin];
        [CATransaction setAnimationTimingFunction:ABC_DEFAULT_TIMING_FUNCTION];
        [CATransaction setAnimationDuration:duration];

        if (animations) {
            animations();
        }

        [CATransaction commit];
    } completion:completion];
}

//使用系统默认时间函数的动画
#pragma mark -6.0新动画
- (void)animateToFrameBySysemTimeFunc:(CGRect)toFrame
              duration:(NSTimeInterval)duration
              delay: (CGFloat) delay
              function:(CAMediaTimingFunction*)function
       completionBlock:(void (^)(void))completionBlock
{
    [UIView animateWithDuration:duration delay:delay options:0 animations:^{
        [CATransaction begin];
        [CATransaction setAnimationTimingFunction:function];
        [CATransaction setAnimationDuration:duration];

        self.frame = toFrame;

        [CATransaction commit];
    } completion:^(BOOL finished) {
        if (completionBlock) {
            completionBlock();
        }
    }];
}

- (void)animateToFrameBySysemTimeFunc:(CGRect)toFrame
              duration:(NSTimeInterval)duration
       completionBlock:(void (^)(void))completionBlock
{

    [self animateToFrameBySysemTimeFunc:toFrame
                duration:duration
                delay: 0.0
                function:[CAMediaTimingFunction functionWithName: kCAMediaTimingFunctionDefault]
         completionBlock:completionBlock];
}



#pragma  mark -
#pragma  mark animate opacity
- (void)animateToOpacity:(CGFloat)toOpacity
              duration:(NSTimeInterval)duration
              function:(AHEasingFunction)function
       completionBlock:(void (^)(void))completionBlock
{
    [CATransaction begin];
    [CATransaction setCompletionBlock:completionBlock];

    CGFloat fromValue = self.layer.opacity;
    CAKeyframeAnimation *opacityAnimation = nil;

    opacityAnimation = [CAKeyframeAnimation animationWithKeyPath:@"opacity"
                                                        function:function
                                                       fromValue:fromValue
                                                         toValue:toOpacity];

    [opacityAnimation setDuration:duration];

    [self.layer addAnimation:opacityAnimation forKey:@"opacityAnimation"];
    self.layer.opacity = toOpacity;

    [CATransaction commit];
}

- (void)animateToOpacity:(CGFloat)toOpacity
              duration:(NSTimeInterval)duration
       completionBlock:(void (^)(void))completionBlock
{
    [self animateToOpacity:toOpacity
                duration:duration
                function:[[ABCAnimationCenter defaultCenter] defaultFunction]
         completionBlock:completionBlock];
}

/*
- (void)animateToOpacity:(CGFloat)toOpacity
              duration:(NSTimeInterval)duration
                easing:(MttAnimationEasingMode)easing
       completionBlock:(void (^)(void))completionBlock
{
    [self animateToOpacity:toOpacity
                duration:duration
                function:[[ABCAnimationCenter defaultCenter] defaultFunctionWithEasing:easing]
         completionBlock:completionBlock];
}
*/

/**
 *  遮罩使用的动画
 **/
- (void)animateToOpacityDefault:(CGFloat)toOpacity
                duration:(NSTimeInterval)duration
         completionBlock:(void (^)(void))completionBlock
{
//    [self animateToOpacity:toOpacity
//                  duration:duration
//                  function:[ABCAnimationCenter functionWithCurve:CurveTypeLinear easing:MttAnimationEaseIn]
//           completionBlock:completionBlock];

    //方法2
    [self animateToOpacityBySysemTimeFunc:toOpacity
                                 duration:duration
                                    delay:0.0
                                 function:[CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear]
                          completionBlock:completionBlock];
}

- (void)animateToOpacityDefault:(CGFloat)toOpacity
                       duration:(NSTimeInterval)duration
                       delay: (CGFloat) delay
                completionBlock:(void (^)(void))completionBlock
{
    [self animateToOpacityBySysemTimeFunc:toOpacity duration:duration delay:delay function:[CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear] completionBlock:completionBlock];
}

//使用系统默认时间函数的动画
#pragma mark -6.0新动画
- (void)animateToOpacityBySysemTimeFunc:(CGFloat)toOpacity
                duration:(NSTimeInterval)duration
                delay: (CGFloat) delay
                function:(CAMediaTimingFunction*)function
         completionBlock:(void (^)(void))completionBlock
{
    [CATransaction begin];
    [CATransaction setCompletionBlock:completionBlock];

    CABasicAnimation *opacityAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
    opacityAnimation.fromValue = @(self.layer.opacity);
    opacityAnimation.toValue = @(toOpacity);
    opacityAnimation.timingFunction = function;
    opacityAnimation.duration = duration;
    opacityAnimation.fillMode = kCAFillModeBackwards;
    if (delay > 0.001) {
        opacityAnimation.beginTime = CACurrentMediaTime() + delay;
    }

    [self.layer addAnimation:opacityAnimation forKey:@"opacityAnimation"];

    self.layer.opacity = toOpacity;

    [CATransaction commit];
}

- (void)animateToOpacityBySysemTimeFunc:(CGFloat)toOpacity
                duration:(NSTimeInterval)duration
         completionBlock:(void (^)(void))completionBlock
{
    [self animateToOpacityBySysemTimeFunc:toOpacity
                  duration:duration
                  delay:0.0
                  function:[CAMediaTimingFunction functionWithName: kCAMediaTimingFunctionDefault]
           completionBlock:completionBlock];
}








#pragma mark -transform
- (void)animateToTransform:(CGAffineTransform)toTransform
                  duration:(NSTimeInterval)duration
                  function:(AHEasingFunction)function
           completionBlock:(void (^)(void))completionBlock
{
    [CATransaction begin];
    [CATransaction setCompletionBlock:completionBlock];

    CGAffineTransform fromTransform = self.transform;
    CAKeyframeAnimation *transformAnimation = nil;

    transformAnimation = [CAKeyframeAnimation animationWithKeyPath:@"transform"
                                                          function:function
                                                     fromTransform:fromTransform
                                                       toTransform:toTransform];

    [transformAnimation setDuration:duration];

    [self.layer addAnimation:transformAnimation forKey:@"transformAnimation"];
    self.transform = toTransform;

    [CATransaction commit];
}

/*
- (void)animateToTransform:(CGAffineTransform)toTransform
                duration:(NSTimeInterval)duration
         completionBlock:(void (^)(void))completionBlock
{
    [self animateToTransform:(CGAffineTransform)toTransform
                  duration:duration
                  function:[[ABCAnimationCenter defaultCenter] defaultFunction]
           completionBlock:completionBlock];
}
*/

//使用系统默认时间函数的动画
#pragma mark -6.0新动画
- (void)animateToTransformBySysemTimeFunc:(CGAffineTransform)toTransform
                  duration:(NSTimeInterval)duration
                  function:(CAMediaTimingFunction*)function
           completionBlock:(void (^)(void))completionBlock
{
    [CATransaction begin];
    [CATransaction setCompletionBlock:completionBlock];

    CGAffineTransform fromTransform = self.transform;

    CABasicAnimation *transformAnimation = [CABasicAnimation animationWithKeyPath:@"transform"];
    transformAnimation.fromValue = [NSValue valueWithCGAffineTransform:fromTransform];
    transformAnimation.toValue = [NSValue valueWithCGAffineTransform:toTransform];
    transformAnimation.timingFunction = function;
    transformAnimation.duration = duration;

    [self.layer addAnimation:transformAnimation forKey:@"transformAnimation"];
    self.transform = toTransform;

    [CATransaction commit];
}

- (void)animateToTransformBySysemTimeFunc:(CGAffineTransform)toTransform
                  duration:(NSTimeInterval)duration
           completionBlock:(void (^)(void))completionBlock
{
    [self animateToTransformBySysemTimeFunc:(CGAffineTransform)toTransform
                    duration:duration
                    function:[CAMediaTimingFunction functionWithName: kCAMediaTimingFunctionDefault]
             completionBlock:completionBlock];
}


/**
 *  默认进行线性缩放
 */
- (void)animateToScaleDefault:(CGFloat) sx sy:(CGFloat) sy
                                 duration:(NSTimeInterval)duration
                          completionBlock:(void (^)(void))completionBlock
{
    CGAffineTransform transform = CGAffineTransformMakeScale(sx,sy);
    [self animateToTransformBySysemTimeFunc:transform duration:duration function:[CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear] completionBlock:completionBlock];
}

#define ABC_SCALE_ANIMATION_KEY @"ScaleAnimation"
//这种函数不修改transform，方便反向操作。
- (void)animateFromScale:(CGFloat) from toScale:(CGFloat) to
                     duration:(NSTimeInterval)duration
              completionBlock:(void (^)(void))completionBlock
{
    [self animateFromScale:from toScale:to duration:duration function:ABC_MOVING_TIMING_FUNCTION completionBlock:completionBlock];
}

- (void)animateFromScale:(CGFloat) from toScale:(CGFloat) to
                duration:(NSTimeInterval)duration
                function:(CAMediaTimingFunction*)function
         completionBlock:(void (^)(void))completionBlock
{
    [CATransaction begin];
    [CATransaction setCompletionBlock:completionBlock];

    CABasicAnimation *scaleAnimation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
    scaleAnimation.fromValue = [NSNumber numberWithFloat:from];
    scaleAnimation.toValue = [NSNumber numberWithFloat:to];
    scaleAnimation.duration = duration;
    scaleAnimation.fillMode = kCAFillModeForwards;
    scaleAnimation.removedOnCompletion = NO;
    scaleAnimation.timingFunction = function;
    [self.layer addAnimation:scaleAnimation forKey:ABC_SCALE_ANIMATION_KEY];

    [CATransaction commit];
}

- (void) removeScaleAnimation
{
    [self.layer removeAnimationForKey:ABC_SCALE_ANIMATION_KEY];
}




- (void)animateToIdentityScaleWithDuration:(NSTimeInterval)duration
              completionBlock:(void (^)(void))completionBlock
{
    CGAffineTransform transform = CGAffineTransformIdentity;
    [self animateToTransformBySysemTimeFunc:transform duration:duration function:[CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear] completionBlock:completionBlock];
}


#pragma mark -截屏
- (UIView *)mttSnapshotView
{
    UIGraphicsBeginImageContextWithOptions(self.bounds.size, NO, [[UIScreen mainScreen] scale]);

    CGContextRef contextRef = UIGraphicsGetCurrentContext();
    if([self isKindOfClass:[UITableView class]]){
        UITableView *tableView = (UITableView *)self;
        CGContextTranslateCTM(contextRef, 0, -tableView.contentOffset.y);
    }
    [self.layer renderInContext:contextRef];

    UIImage *snapshot = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();

    UIView *snapshotView = [[UIView alloc] initWithFrame:self.bounds];
    snapshotView.layer.contents = (id)snapshot.CGImage;

    return snapshotView;
}


- (UIImage *)mttSnapshot
{
    UIGraphicsBeginImageContextWithOptions(self.bounds.size, NO, [[UIScreen mainScreen] scale]);

    CGContextRef contextRef = UIGraphicsGetCurrentContext();
    if([self isKindOfClass:[UITableView class]]){
        UITableView *tableView = (UITableView *)self;
        CGContextTranslateCTM(contextRef, 0, -tableView.contentOffset.y);
    }
    [self.layer renderInContext:contextRef];

    UIImage *snapshot = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();

    return snapshot;
}

#pragma mark -隐身模式出来动画
- (CAAnimation *) makePrivacyModeAnimation
{
    CAKeyframeAnimation *keyAnim = [CAKeyframeAnimation animationWithKeyPath:@"transform.scale"];
    NSMutableArray *values = [[NSMutableArray alloc] init];
    [values addObject:[NSNumber numberWithFloat:0.0]];
    [values addObject:[NSNumber numberWithFloat:1.1]];
    [values addObject:[NSNumber numberWithFloat:1.0]];
    keyAnim.values = values;

    NSMutableArray *keyTimes = [[NSMutableArray alloc] init];
    [keyTimes addObject:[NSNumber numberWithFloat:0.0]];
    [keyTimes addObject:[NSNumber numberWithFloat:0.9]];
    [keyTimes addObject:[NSNumber numberWithFloat:1.0]];
    keyAnim.keyTimes = keyTimes;


    NSMutableArray *timingFunctions = [[NSMutableArray alloc] init];
    [timingFunctions addObject:[CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut]];
    [timingFunctions addObject:[CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseIn]];
    keyAnim.timingFunctions = timingFunctions;

    [keyAnim setDuration:ABC_DEFAULT_ANIMATION_TIME];
    [keyAnim setCalculationMode:kCAAnimationCubic];
    [keyAnim setFillMode:kCAFillModeBackwards];

    return keyAnim;
}

- (void)mttRippleAnimation:(CGFloat)duration
                    origin:(CGPoint)origin
                   options:(NSDictionary*)options
                completion:(void(^)(void))completion
{
    if (CGRectIsEmpty(self.bounds)) {
        if (completion) {
            completion();
        }
        return;
    }

    CAShapeLayer *masklayer = [CAShapeLayer layer];
    masklayer.bounds = self.bounds;
    masklayer.fillColor = [UIColor greenColor].CGColor;
    masklayer.frame = [self calculateRippleSize:origin];

    UIBezierPath* maskPath = options[CUSTOM_ANIMATION_MASKPATH_KEY];
    if ([maskPath isKindOfClass:[UIBezierPath class]]) {
        masklayer.path = maskPath.CGPath;
    } else {
        masklayer.path = [UIBezierPath bezierPathWithRect:masklayer.bounds].CGPath;
    }

    self.layer.mask = masklayer;
    [CATransaction begin];

    [CATransaction setCompletionBlock:completion];

    CABasicAnimation *scaleAnim =
    [CABasicAnimation animationWithKeyPath:@"transform.scale"];
    scaleAnim.fromValue = @0;
    CGSize originRectSize = [options[CUSTOM_ANIMATION_MASK_ORIGIN_SIZE_KEY] CGSizeValue];
    if (!CGSizeEqualToSize(originRectSize, CGSizeZero) && masklayer.frame.size.width > 0) {
        scaleAnim.fromValue = @(originRectSize.width / masklayer.frame.size.width);
    }
    scaleAnim.toValue = @1;
    scaleAnim.duration = duration;
    scaleAnim.timingFunction = ABC_DEFAULT_TIMING_FUNCTION;

    [masklayer addAnimation:scaleAnim forKey:@"scale"];

    [CATransaction commit];
}

- (void)mttRippleAnimation:(CGFloat)duration origin:(CGPoint)origin completion:(void(^)(void))completion
{
    if (CGRectIsEmpty(self.bounds)) {
        if (completion) {
            completion();
        }
        return;
    }

    CAShapeLayer *masklayer = [CAShapeLayer layer];
    masklayer.bounds = self.bounds;
    masklayer.fillColor = [UIColor greenColor].CGColor;

    masklayer.frame = [self calculateRippleSize:origin];
    masklayer.path = [UIBezierPath bezierPathWithOvalInRect:masklayer.bounds].CGPath;

    self.layer.mask = masklayer;
    [CATransaction begin];

    [CATransaction setCompletionBlock:completion];

    CABasicAnimation *opacity = [CABasicAnimation animationWithKeyPath:@"opacity"];
    opacity.fromValue = @0;
    opacity.toValue = @1;
    opacity.duration = duration;
    opacity.timingFunction = ABC_DEFAULT_TIMING_FUNCTION;
    opacity.removedOnCompletion = NO;
    [self.layer addAnimation:opacity forKey:@"opacity"];

    CABasicAnimation *scaleAnim =
    [CABasicAnimation animationWithKeyPath:@"transform.scale"];
    scaleAnim.fromValue = @0;
    scaleAnim.toValue = @1;
    scaleAnim.duration = duration;
    scaleAnim.timingFunction = ABC_DEFAULT_TIMING_FUNCTION;
    scaleAnim.removedOnCompletion = NO;
    [masklayer addAnimation:scaleAnim forKey:@"scale"];

    [CATransaction commit];
}

- (CGRect)calculateRippleSize:(CGPoint)origin
{
    CGFloat superLayerWidth = CGRectGetWidth(self.bounds);
    CGFloat superLayerHeight = CGRectGetHeight(self.bounds);
    CGPoint center = origin;
    if (CGPointEqualToPoint(center, CGPointZero)) {
        center = CGRectCenter(self.bounds);
    }
    CGFloat circleDiameter =
    sqrtf(powf(superLayerWidth, 2) + powf(superLayerHeight, 2)) *
    1.5;
    CGFloat subX = center.x - circleDiameter / 2;
    CGFloat subY = center.y - circleDiameter / 2;

    return CGRectMake(subX, subY, circleDiameter, circleDiameter);
}

- (void)mttScaleEffect:(CGFloat)duration autoRepeat:(BOOL)autoRepeat completion:(void(^)(void))completion
{
    [CATransaction begin];
    [CATransaction setCompletionBlock:completion];

    CABasicAnimation * animation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];

    animation.repeatCount = autoRepeat ? INT_MAX : 1;
    animation.autoreverses = YES;
//    animation.timingFunction = ABC_DEFAULT_TIMING_FUNCTION;
    animation.duration = duration;
    animation.fromValue = @1;
    animation.toValue = @1.2;

    [self.layer addAnimation:animation forKey:@"scale"];

    [CATransaction commit];
}

@end
