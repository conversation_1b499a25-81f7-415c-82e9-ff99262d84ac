var hippyReactBase =
/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 0);
/******/ })
/************************************************************************/
/******/ ({

/***/ "./node_modules/object-assign/index.js":
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/

/* eslint-disable no-unused-vars */

var getOwnPropertySymbols = Object.getOwnPropertySymbols;
var hasOwnProperty = Object.prototype.hasOwnProperty;
var propIsEnumerable = Object.prototype.propertyIsEnumerable;

function toObject(val) {
  if (val === null || val === undefined) {
    throw new TypeError('Object.assign cannot be called with null or undefined');
  }

  return Object(val);
}

function shouldUseNative() {
  try {
    if (!Object.assign) {
      return false;
    } // Detect buggy property enumeration order in older V8 versions.
    // https://bugs.chromium.org/p/v8/issues/detail?id=4118


    var test1 = new String('abc'); // eslint-disable-line no-new-wrappers

    test1[5] = 'de';

    if (Object.getOwnPropertyNames(test1)[0] === '5') {
      return false;
    } // https://bugs.chromium.org/p/v8/issues/detail?id=3056


    var test2 = {};

    for (var i = 0; i < 10; i++) {
      test2['_' + String.fromCharCode(i)] = i;
    }

    var order2 = Object.getOwnPropertyNames(test2).map(function (n) {
      return test2[n];
    });

    if (order2.join('') !== '0123456789') {
      return false;
    } // https://bugs.chromium.org/p/v8/issues/detail?id=3056


    var test3 = {};
    'abcdefghijklmnopqrst'.split('').forEach(function (letter) {
      test3[letter] = letter;
    });

    if (Object.keys(Object.assign({}, test3)).join('') !== 'abcdefghijklmnopqrst') {
      return false;
    }

    return true;
  } catch (err) {
    // We don't expect any of the above to throw, but better to be safe.
    return false;
  }
}

module.exports = shouldUseNative() ? Object.assign : function (target, source) {
  var from;
  var to = toObject(target);
  var symbols;

  for (var s = 1; s < arguments.length; s++) {
    from = Object(arguments[s]);

    for (var key in from) {
      if (hasOwnProperty.call(from, key)) {
        to[key] = from[key];
      }
    }

    if (getOwnPropertySymbols) {
      symbols = getOwnPropertySymbols(from);

      for (var i = 0; i < symbols.length; i++) {
        if (propIsEnumerable.call(from, symbols[i])) {
          to[symbols[i]] = from[symbols[i]];
        }
      }
    }
  }

  return to;
};

/***/ }),

/***/ "./node_modules/react/cjs/react.production.min.js":
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/** @license React v17.0.2
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */


var l = __webpack_require__("./node_modules/object-assign/index.js"),
    n = 60103,
    p = 60106;

exports.Fragment = 60107;
exports.StrictMode = 60108;
exports.Profiler = 60114;
var q = 60109,
    r = 60110,
    t = 60112;
exports.Suspense = 60113;
var u = 60115,
    v = 60116;

if ("function" === typeof Symbol && Symbol.for) {
  var w = Symbol.for;
  n = w("react.element");
  p = w("react.portal");
  exports.Fragment = w("react.fragment");
  exports.StrictMode = w("react.strict_mode");
  exports.Profiler = w("react.profiler");
  q = w("react.provider");
  r = w("react.context");
  t = w("react.forward_ref");
  exports.Suspense = w("react.suspense");
  u = w("react.memo");
  v = w("react.lazy");
}

var x = "function" === typeof Symbol && Symbol.iterator;

function y(a) {
  if (null === a || "object" !== typeof a) return null;
  a = x && a[x] || a["@@iterator"];
  return "function" === typeof a ? a : null;
}

function z(a) {
  for (var b = "https://reactjs.org/docs/error-decoder.html?invariant=" + a, c = 1; c < arguments.length; c++) {
    b += "&args[]=" + encodeURIComponent(arguments[c]);
  }

  return "Minified React error #" + a + "; visit " + b + " for the full message or use the non-minified dev environment for full errors and additional helpful warnings.";
}

var A = {
  isMounted: function isMounted() {
    return !1;
  },
  enqueueForceUpdate: function enqueueForceUpdate() {},
  enqueueReplaceState: function enqueueReplaceState() {},
  enqueueSetState: function enqueueSetState() {}
},
    B = {};

function C(a, b, c) {
  this.props = a;
  this.context = b;
  this.refs = B;
  this.updater = c || A;
}

C.prototype.isReactComponent = {};

C.prototype.setState = function (a, b) {
  if ("object" !== typeof a && "function" !== typeof a && null != a) throw Error(z(85));
  this.updater.enqueueSetState(this, a, b, "setState");
};

C.prototype.forceUpdate = function (a) {
  this.updater.enqueueForceUpdate(this, a, "forceUpdate");
};

function D() {}

D.prototype = C.prototype;

function E(a, b, c) {
  this.props = a;
  this.context = b;
  this.refs = B;
  this.updater = c || A;
}

var F = E.prototype = new D();
F.constructor = E;
l(F, C.prototype);
F.isPureReactComponent = !0;
var G = {
  current: null
},
    H = Object.prototype.hasOwnProperty,
    I = {
  key: !0,
  ref: !0,
  __self: !0,
  __source: !0
};

function J(a, b, c) {
  var e,
      d = {},
      k = null,
      h = null;
  if (null != b) for (e in void 0 !== b.ref && (h = b.ref), void 0 !== b.key && (k = "" + b.key), b) {
    H.call(b, e) && !I.hasOwnProperty(e) && (d[e] = b[e]);
  }
  var g = arguments.length - 2;
  if (1 === g) d.children = c;else if (1 < g) {
    for (var f = Array(g), m = 0; m < g; m++) {
      f[m] = arguments[m + 2];
    }

    d.children = f;
  }
  if (a && a.defaultProps) for (e in g = a.defaultProps, g) {
    void 0 === d[e] && (d[e] = g[e]);
  }
  return {
    $$typeof: n,
    type: a,
    key: k,
    ref: h,
    props: d,
    _owner: G.current
  };
}

function K(a, b) {
  return {
    $$typeof: n,
    type: a.type,
    key: b,
    ref: a.ref,
    props: a.props,
    _owner: a._owner
  };
}

function L(a) {
  return "object" === typeof a && null !== a && a.$$typeof === n;
}

function escape(a) {
  var b = {
    "=": "=0",
    ":": "=2"
  };
  return "$" + a.replace(/[=:]/g, function (a) {
    return b[a];
  });
}

var M = /\/+/g;

function N(a, b) {
  return "object" === typeof a && null !== a && null != a.key ? escape("" + a.key) : b.toString(36);
}

function O(a, b, c, e, d) {
  var k = typeof a;
  if ("undefined" === k || "boolean" === k) a = null;
  var h = !1;
  if (null === a) h = !0;else switch (k) {
    case "string":
    case "number":
      h = !0;
      break;

    case "object":
      switch (a.$$typeof) {
        case n:
        case p:
          h = !0;
      }

  }
  if (h) return h = a, d = d(h), a = "" === e ? "." + N(h, 0) : e, Array.isArray(d) ? (c = "", null != a && (c = a.replace(M, "$&/") + "/"), O(d, b, c, "", function (a) {
    return a;
  })) : null != d && (L(d) && (d = K(d, c + (!d.key || h && h.key === d.key ? "" : ("" + d.key).replace(M, "$&/") + "/") + a)), b.push(d)), 1;
  h = 0;
  e = "" === e ? "." : e + ":";
  if (Array.isArray(a)) for (var g = 0; g < a.length; g++) {
    k = a[g];
    var f = e + N(k, g);
    h += O(k, b, c, f, d);
  } else if (f = y(a), "function" === typeof f) for (a = f.call(a), g = 0; !(k = a.next()).done;) {
    k = k.value, f = e + N(k, g++), h += O(k, b, c, f, d);
  } else if ("object" === k) throw b = "" + a, Error(z(31, "[object Object]" === b ? "object with keys {" + Object.keys(a).join(", ") + "}" : b));
  return h;
}

function P(a, b, c) {
  if (null == a) return a;
  var e = [],
      d = 0;
  O(a, e, "", "", function (a) {
    return b.call(c, a, d++);
  });
  return e;
}

function Q(a) {
  if (-1 === a._status) {
    var b = a._result;
    b = b();
    a._status = 0;
    a._result = b;
    b.then(function (b) {
      0 === a._status && (b = b.default, a._status = 1, a._result = b);
    }, function (b) {
      0 === a._status && (a._status = 2, a._result = b);
    });
  }

  if (1 === a._status) return a._result;
  throw a._result;
}

var R = {
  current: null
};

function S() {
  var a = R.current;
  if (null === a) throw Error(z(321));
  return a;
}

var T = {
  ReactCurrentDispatcher: R,
  ReactCurrentBatchConfig: {
    transition: 0
  },
  ReactCurrentOwner: G,
  IsSomeRendererActing: {
    current: !1
  },
  assign: l
};
exports.Children = {
  map: P,
  forEach: function forEach(a, b, c) {
    P(a, function () {
      b.apply(this, arguments);
    }, c);
  },
  count: function count(a) {
    var b = 0;
    P(a, function () {
      b++;
    });
    return b;
  },
  toArray: function toArray(a) {
    return P(a, function (a) {
      return a;
    }) || [];
  },
  only: function only(a) {
    if (!L(a)) throw Error(z(143));
    return a;
  }
};
exports.Component = C;
exports.PureComponent = E;
exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = T;

exports.cloneElement = function (a, b, c) {
  if (null === a || void 0 === a) throw Error(z(267, a));
  var e = l({}, a.props),
      d = a.key,
      k = a.ref,
      h = a._owner;

  if (null != b) {
    void 0 !== b.ref && (k = b.ref, h = G.current);
    void 0 !== b.key && (d = "" + b.key);
    if (a.type && a.type.defaultProps) var g = a.type.defaultProps;

    for (f in b) {
      H.call(b, f) && !I.hasOwnProperty(f) && (e[f] = void 0 === b[f] && void 0 !== g ? g[f] : b[f]);
    }
  }

  var f = arguments.length - 2;
  if (1 === f) e.children = c;else if (1 < f) {
    g = Array(f);

    for (var m = 0; m < f; m++) {
      g[m] = arguments[m + 2];
    }

    e.children = g;
  }
  return {
    $$typeof: n,
    type: a.type,
    key: d,
    ref: k,
    props: e,
    _owner: h
  };
};

exports.createContext = function (a, b) {
  void 0 === b && (b = null);
  a = {
    $$typeof: r,
    _calculateChangedBits: b,
    _currentValue: a,
    _currentValue2: a,
    _threadCount: 0,
    Provider: null,
    Consumer: null
  };
  a.Provider = {
    $$typeof: q,
    _context: a
  };
  return a.Consumer = a;
};

exports.createElement = J;

exports.createFactory = function (a) {
  var b = J.bind(null, a);
  b.type = a;
  return b;
};

exports.createRef = function () {
  return {
    current: null
  };
};

exports.forwardRef = function (a) {
  return {
    $$typeof: t,
    render: a
  };
};

exports.isValidElement = L;

exports.lazy = function (a) {
  return {
    $$typeof: v,
    _payload: {
      _status: -1,
      _result: a
    },
    _init: Q
  };
};

exports.memo = function (a, b) {
  return {
    $$typeof: u,
    type: a,
    compare: void 0 === b ? null : b
  };
};

exports.useCallback = function (a, b) {
  return S().useCallback(a, b);
};

exports.useContext = function (a, b) {
  return S().useContext(a, b);
};

exports.useDebugValue = function () {};

exports.useEffect = function (a, b) {
  return S().useEffect(a, b);
};

exports.useImperativeHandle = function (a, b, c) {
  return S().useImperativeHandle(a, b, c);
};

exports.useLayoutEffect = function (a, b) {
  return S().useLayoutEffect(a, b);
};

exports.useMemo = function (a, b) {
  return S().useMemo(a, b);
};

exports.useReducer = function (a, b, c) {
  return S().useReducer(a, b, c);
};

exports.useRef = function (a) {
  return S().useRef(a);
};

exports.useState = function (a) {
  return S().useState(a);
};

exports.version = "17.0.2";

/***/ }),

/***/ "./node_modules/react/index.js":
/***/ (function(module, exports, __webpack_require__) {

"use strict";


if (true) {
  module.exports = __webpack_require__("./node_modules/react/cjs/react.production.min.js");
} else {}

/***/ }),

/***/ "./node_modules/scheduler/cjs/scheduler.production.min.js":
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/** @license React v0.20.2
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */


var _f, g, h, k;

if ("object" === typeof performance && "function" === typeof performance.now) {
  var l = performance;

  exports.unstable_now = function () {
    return l.now();
  };
} else {
  var p = Date,
      q = p.now();

  exports.unstable_now = function () {
    return p.now() - q;
  };
}

if ("undefined" === typeof window || "function" !== typeof MessageChannel) {
  var t = null,
      u = null,
      w = function w() {
    if (null !== t) try {
      var a = exports.unstable_now();
      t(!0, a);
      t = null;
    } catch (b) {
      throw setTimeout(w, 0), b;
    }
  };

  _f = function f(a) {
    null !== t ? setTimeout(_f, 0, a) : (t = a, setTimeout(w, 0));
  };

  g = function g(a, b) {
    u = setTimeout(a, b);
  };

  h = function h() {
    clearTimeout(u);
  };

  exports.unstable_shouldYield = function () {
    return !1;
  };

  k = exports.unstable_forceFrameRate = function () {};
} else {
  var x = window.setTimeout,
      y = window.clearTimeout;

  if ("undefined" !== typeof console) {
    var z = window.cancelAnimationFrame;
    "function" !== typeof window.requestAnimationFrame && console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills");
    "function" !== typeof z && console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills");
  }

  var A = !1,
      B = null,
      C = -1,
      D = 5,
      E = 0;

  exports.unstable_shouldYield = function () {
    return exports.unstable_now() >= E;
  };

  k = function k() {};

  exports.unstable_forceFrameRate = function (a) {
    0 > a || 125 < a ? console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported") : D = 0 < a ? Math.floor(1E3 / a) : 5;
  };

  var F = new MessageChannel(),
      G = F.port2;

  F.port1.onmessage = function () {
    if (null !== B) {
      var a = exports.unstable_now();
      E = a + D;

      try {
        B(!0, a) ? G.postMessage(null) : (A = !1, B = null);
      } catch (b) {
        throw G.postMessage(null), b;
      }
    } else A = !1;
  };

  _f = function _f(a) {
    B = a;
    A || (A = !0, G.postMessage(null));
  };

  g = function g(a, b) {
    C = x(function () {
      a(exports.unstable_now());
    }, b);
  };

  h = function h() {
    y(C);
    C = -1;
  };
}

function H(a, b) {
  var c = a.length;
  a.push(b);

  a: for (;;) {
    var d = c - 1 >>> 1,
        e = a[d];
    if (void 0 !== e && 0 < I(e, b)) a[d] = b, a[c] = e, c = d;else break a;
  }
}

function J(a) {
  a = a[0];
  return void 0 === a ? null : a;
}

function K(a) {
  var b = a[0];

  if (void 0 !== b) {
    var c = a.pop();

    if (c !== b) {
      a[0] = c;

      a: for (var d = 0, e = a.length; d < e;) {
        var m = 2 * (d + 1) - 1,
            n = a[m],
            v = m + 1,
            r = a[v];
        if (void 0 !== n && 0 > I(n, c)) void 0 !== r && 0 > I(r, n) ? (a[d] = r, a[v] = c, d = v) : (a[d] = n, a[m] = c, d = m);else if (void 0 !== r && 0 > I(r, c)) a[d] = r, a[v] = c, d = v;else break a;
      }
    }

    return b;
  }

  return null;
}

function I(a, b) {
  var c = a.sortIndex - b.sortIndex;
  return 0 !== c ? c : a.id - b.id;
}

var L = [],
    M = [],
    N = 1,
    O = null,
    P = 3,
    Q = !1,
    R = !1,
    S = !1;

function T(a) {
  for (var b = J(M); null !== b;) {
    if (null === b.callback) K(M);else if (b.startTime <= a) K(M), b.sortIndex = b.expirationTime, H(L, b);else break;
    b = J(M);
  }
}

function U(a) {
  S = !1;
  T(a);
  if (!R) if (null !== J(L)) R = !0, _f(V);else {
    var b = J(M);
    null !== b && g(U, b.startTime - a);
  }
}

function V(a, b) {
  R = !1;
  S && (S = !1, h());
  Q = !0;
  var c = P;

  try {
    T(b);

    for (O = J(L); null !== O && (!(O.expirationTime > b) || a && !exports.unstable_shouldYield());) {
      var d = O.callback;

      if ("function" === typeof d) {
        O.callback = null;
        P = O.priorityLevel;
        var e = d(O.expirationTime <= b);
        b = exports.unstable_now();
        "function" === typeof e ? O.callback = e : O === J(L) && K(L);
        T(b);
      } else K(L);

      O = J(L);
    }

    if (null !== O) var m = !0;else {
      var n = J(M);
      null !== n && g(U, n.startTime - b);
      m = !1;
    }
    return m;
  } finally {
    O = null, P = c, Q = !1;
  }
}

var W = k;
exports.unstable_IdlePriority = 5;
exports.unstable_ImmediatePriority = 1;
exports.unstable_LowPriority = 4;
exports.unstable_NormalPriority = 3;
exports.unstable_Profiling = null;
exports.unstable_UserBlockingPriority = 2;

exports.unstable_cancelCallback = function (a) {
  a.callback = null;
};

exports.unstable_continueExecution = function () {
  R || Q || (R = !0, _f(V));
};

exports.unstable_getCurrentPriorityLevel = function () {
  return P;
};

exports.unstable_getFirstCallbackNode = function () {
  return J(L);
};

exports.unstable_next = function (a) {
  switch (P) {
    case 1:
    case 2:
    case 3:
      var b = 3;
      break;

    default:
      b = P;
  }

  var c = P;
  P = b;

  try {
    return a();
  } finally {
    P = c;
  }
};

exports.unstable_pauseExecution = function () {};

exports.unstable_requestPaint = W;

exports.unstable_runWithPriority = function (a, b) {
  switch (a) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      break;

    default:
      a = 3;
  }

  var c = P;
  P = a;

  try {
    return b();
  } finally {
    P = c;
  }
};

exports.unstable_scheduleCallback = function (a, b, c) {
  var d = exports.unstable_now();
  "object" === typeof c && null !== c ? (c = c.delay, c = "number" === typeof c && 0 < c ? d + c : d) : c = d;

  switch (a) {
    case 1:
      var e = -1;
      break;

    case 2:
      e = 250;
      break;

    case 5:
      e = 1073741823;
      break;

    case 4:
      e = 1E4;
      break;

    default:
      e = 5E3;
  }

  e = c + e;
  a = {
    id: N++,
    callback: b,
    priorityLevel: a,
    startTime: c,
    expirationTime: e,
    sortIndex: -1
  };
  c > d ? (a.sortIndex = c, H(M, a), null === J(L) && a === J(M) && (S ? h() : S = !0, g(U, c - d))) : (a.sortIndex = e, H(L, a), R || Q || (R = !0, _f(V)));
  return a;
};

exports.unstable_wrapCallback = function (a) {
  var b = P;
  return function () {
    var c = P;
    P = b;

    try {
      return a.apply(this, arguments);
    } finally {
      P = c;
    }
  };
};

/***/ }),

/***/ "./node_modules/scheduler/index.js":
/***/ (function(module, exports, __webpack_require__) {

"use strict";


if (true) {
  module.exports = __webpack_require__("./node_modules/scheduler/cjs/scheduler.production.min.js");
} else {}

/***/ }),

/***/ "./node_modules/webpack/buildin/global.js":
/***/ (function(module, exports) {

var g; // This works in non-strict mode

g = function () {
  return this;
}();

try {
  // This works if eval is allowed (see CSP)
  g = g || new Function("return this")();
} catch (e) {
  // This works if the window reference is available
  if (typeof window === "object") g = window;
} // g can still be undefined, but nothing to do about it...
// We return undefined, instead of nothing here, so it's
// easier to handle this case. if(!global) { ...}


module.exports = g;

/***/ }),

/***/ "./node_modules/webpack/buildin/module.js":
/***/ (function(module, exports) {

module.exports = function (module) {
  if (!module.webpackPolyfill) {
    module.deprecate = function () {};

    module.paths = []; // module.parent = undefined by default

    if (!module.children) module.children = [];
    Object.defineProperty(module, "loaded", {
      enumerable: true,
      get: function get() {
        return module.l;
      }
    });
    Object.defineProperty(module, "id", {
      enumerable: true,
      get: function get() {
        return module.i;
      }
    });
    module.webpackPolyfill = 1;
  }

  return module;
};

/***/ }),

/***/ "./scripts/vendor.js":
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__("./third-party/@hippy/react/dist/index.js");

/***/ }),

/***/ "./third-party/@hippy/react/dist/index.js":
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* WEBPACK VAR INJECTION */(function(global) {/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Animated", function() { return Animated; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Animation", function() { return Animation; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "AnimationSet", function() { return AnimationSet; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "AppRegistry", function() { return AppRegistry; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "AsyncStorage", function() { return AsyncStorage; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "BackAndroid", function() { return BackAndroid; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Clipboard", function() { return Clipboard; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "ConsoleModule", function() { return ConsoleModule; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Dimensions", function() { return Dimensions; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Easing", function() { return Easing; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Focusable", function() { return Focusable; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Hippy", function() { return Hippy; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "HippyEventEmitter", function() { return HippyEventEmitter; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "HippyEventListener", function() { return HippyEventListener; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "HippyRegister", function() { return HippyRegister; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Image", function() { return Image; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "ImageBackground", function() { return ImageBackground; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "ImageLoaderModule", function() { return ImageLoaderModule; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "ListView", function() { return ListView; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "ListViewItem", function() { return ListViewItem; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Modal", function() { return Modal; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Navigator", function() { return Navigator; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "NetInfo", function() { return NetInfo; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "NetworkModule", function() { return NetworkModule; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "PixelRatio", function() { return PixelRatio; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Platform", function() { return Platform; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "PullFooter", function() { return PullFooter; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "PullHeader", function() { return PullHeader; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "RNfqb", function() { return RNfqb; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "RNfqbEventEmitter", function() { return RNfqbEventEmitter; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "RNfqbEventListener", function() { return RNfqbEventListener; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "RNfqbRegister", function() { return RNfqbRegister; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "RefreshWrapper", function() { return RefreshWrapper; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "ScrollView", function() { return ScrollView; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "StyleSheet", function() { return stylesheet; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "Text", function() { return Text; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "TextInput", function() { return TextInput; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "TimerModule", function() { return TimerModule; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "UIManagerModule", function() { return UIManagerModule; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "View", function() { return View; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "ViewPager", function() { return ViewPager; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "WaterfallView", function() { return WaterfallView; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "WebSocket", function() { return WebSocket; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "WebView", function() { return WebView; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "callNative", function() { return callNative; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "callNativeWithCallbackId", function() { return callNativeWithCallbackId; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "callNativeWithPromise", function() { return callNativeWithPromise; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "colorParse", function() { return colorParse; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "default", function() { return HippyReact; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "flushSync", function() { return flushSync; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "removeNativeCallback", function() { return removeNativeCallback; });
/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("./third-party/react-reconciler/index.js");
/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
var _excluded = ["children"],
    _excluded2 = ["collapsable", "style"],
    _excluded3 = ["style"],
    _excluded4 = ["children", "style", "imageStyle", "imageRef", "source", "sources", "src", "srcs", "tintColor", "tintColors"],
    _excluded5 = ["children"],
    _excluded6 = ["children"],
    _excluded7 = ["children", "style", "renderRow", "renderPullHeader", "renderPullFooter", "getRowType", "getRowStyle", "getRowKey", "dataSource", "initialListSize", "rowShouldSticky", "onRowLayout", "onHeaderPulling", "onHeaderReleased", "onFooterPulling", "onFooterReleased", "onAppear", "onDisappear", "onWillAppear", "onWillDisappear"],
    _excluded8 = ["children"],
    _excluded9 = ["component"],
    _excluded10 = ["initialRoute"],
    _excluded11 = ["children", "onPageScrollStateChanged"],
    _excluded12 = ["style", "renderBanner", "numberOfColumns", "columnSpacing", "interItemSpacing", "numberOfItems", "preloadItemNumber", "renderItem", "renderPullHeader", "renderPullFooter", "getItemType", "getItemKey", "getItemStyle", "contentInset", "onItemLayout", "onHeaderPulling", "onHeaderReleased", "onFooterPulling", "onFooterReleased", "containPullHeader", "containPullFooter", "containBannerView"];

function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }

function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }

function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }

function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }

function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }

function _possibleConstructorReturn(self, call) { if (call && (typeof call === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }

function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }

function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }

function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }

/*!
 * @hippy/react v2.13.7
 * Build at: Sat Jul 09 2022 19:42:31 GMT+0800 (China Standard Time)
 *
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2022 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

if (!global.__GLOBAL__) {
  global.__GLOBAL__ = {};
}

global.__GLOBAL__.nodeId = 0;
global.__GLOBAL__.animationId = 0;
var _global$Hippy = global.Hippy,
    AsyncStorage$2 = _global$Hippy.asyncStorage,
    Bridge$2 = _global$Hippy.bridge,
    Device$2 = _global$Hippy.device,
    UIManager = _global$Hippy.document,
    HippyRegister$2 = _global$Hippy.register,
    addEventListener$2 = _global$Hippy.on,
    removeEventListener$2 = _global$Hippy.off,
    dispatchEvent$1 = _global$Hippy.emit;
var HippyGlobal = /*#__PURE__*/Object.freeze({
  __proto__: null,
  addEventListener: addEventListener$2,
  removeEventListener: removeEventListener$2,
  dispatchEvent: dispatchEvent$1,
  AsyncStorage: AsyncStorage$2,
  Bridge: Bridge$2,
  Device: Device$2,
  HippyRegister: HippyRegister$2,
  UIManager: UIManager
});
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
// Single root instance

var rootContainer;
var rootViewId;
var fiberNodeCache = new Map();

function setRootContainer(rootId, root) {
  rootViewId = rootId;
  rootContainer = root;
}

function getRootContainer() {
  return rootContainer;
}

function getRootViewId() {
  if (!rootViewId) {
    throw new Error('getRootViewId must execute after setRootContainer');
  }

  return rootViewId;
}

function findNodeByCondition(condition) {
  if (!rootContainer) {
    return null;
  }

  var _rootContainer = rootContainer,
      root = _rootContainer.current;
  var queue = [root];

  while (queue.length) {
    var targetNode = queue.shift();

    if (!targetNode) {
      break;
    }

    if (condition(targetNode)) {
      return targetNode;
    }

    if (targetNode.child) {
      queue.push(targetNode.child);
    }

    if (targetNode.sibling) {
      queue.push(targetNode.sibling);
    }
  }

  return null;
}

function findNodeById(nodeId) {
  return findNodeByCondition(function (node) {
    return node.stateNode && node.stateNode.nodeId === nodeId;
  });
}
/**
 * preCacheFiberNode - cache FiberNode
 * @param {Fiber} targetNode
 * @param {number} nodeId
 */


function preCacheFiberNode(targetNode, nodeId) {
  fiberNodeCache.set(nodeId, targetNode);
}
/**
 * unCacheFiberNode - delete Fiber Node from cache
 * @param {number} nodeId
 */


function unCacheFiberNode(nodeId) {
  fiberNodeCache.delete(nodeId);
}
/**
 * getElementFromFiber - get ElementNode by Fiber
 * @param {number} fiberNode
 */


function getElementFromFiber(fiberNode) {
  return (fiberNode === null || fiberNode === void 0 ? void 0 : fiberNode.stateNode) || null;
}
/**
 * getFiberNodeFromId - get FiberNode by nodeId
 * @param {number} nodeId
 */


function getFiberNodeFromId(nodeId) {
  return fiberNodeCache.get(nodeId) || null;
}
/**
 * unCacheFiberNodeOnIdle - recursively delete FiberNode cache on idle
 * @param {ElementNode|number} node
 */


function unCacheFiberNodeOnIdle(node) {
  requestIdleCallback(function (deadline) {
    // if idle time exists or callback invoked when timeout
    if (deadline.timeRemaining() > 0 || deadline.didTimeout) {
      recursivelyUnCacheFiberNode(node);
    }
  }, {
    timeout: 50
  }); // 50ms to avoid blocking user operation
}
/**
 * recursivelyUnCacheFiberNode - delete ViewNode cache recursively
 * @param {ElementNode|number} node
 */


function recursivelyUnCacheFiberNode(node) {
  if (typeof node === 'number') {
    // if leaf node (e.g. text node)
    unCacheFiberNode(node);
  } else if (node) {
    unCacheFiberNode(node.nodeId);

    if (Array.isArray(node.childNodes)) {
      node.childNodes.forEach(function (node) {
        return recursivelyUnCacheFiberNode(node);
      });
    }
  }
}
/**
 * requestIdleCallback polyfill
 * @param {Function} cb
 * @param {{timeout: number}} [options]
 */


function requestIdleCallback(cb, options) {
  if (!global.requestIdleCallback) {
    return setTimeout(function () {
      cb({
        didTimeout: false,

        timeRemaining() {
          return Infinity;
        }

      });
    }, 1);
  }

  return global.requestIdleCallback(cb, options);
} // Event Names map


var NATIVE_EVENT = 1;
var eventNamesMap = {
  // TODO pressIn, pressOut will be deprecated in future
  // onPressIn: ['onPressIn', 'onTouchDown'],
  // onPressOut: ['onPressOut', 'onTouchEnd'],
  onTouchStart: ['onTouchStart', 'onTouchDown'],
  onPress: ['onPress', 'onClick']
};
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var IS_NUMBER_REG = new RegExp(/^\d+$/);
var silent = false;
var defaultBubbles = false;
/**
 * Trace running information
 */

function trace() {
  var _console;

  // In production build or silent
  if (true) {
    return;
  }

  (_console = console).log.apply(_console, arguments);
}
/**
 * Warning information output
 */


function warn() {
  var _console2;

  // In production build
  if (true) {
    return;
  }

  (_console2 = console).warn.apply(_console2, arguments);
}
/**
 * Convert unicode string to normal string
 * @param {string} text - The unicode string input
 */


function unicodeToChar(text) {
  return text.replace(/\\u[\dA-F]{4}|\\x[\dA-F]{2}/gi, function (match) {
    return String.fromCharCode(parseInt(match.replace(/\\u|\\x/g, ''), 16));
  });
}

var captureEventReg = new RegExp('^on.+Capture$');
/**
 * ensure capture event name
 * @param {any} eventName
 */

function isCaptureEvent(eventName) {
  return captureEventReg.test(eventName);
}
/**
 * Convert to string as possible
 */


var numberRegEx = new RegExp('^(?=.+)[+-]?\\d*\\.?\\d*([Ee][+-]?\\d+)?$');
/**
 * Try to convert something to number
 *
 * @param {any} input - The input try to convert number
 */

function tryConvertNumber(input) {
  if (typeof input === 'number') {
    return input;
  }

  if (typeof input === 'string' && numberRegEx.test(input)) {
    try {
      return parseFloat(input);
    } catch (err) {
      return input;
    }
  }

  return input;
}
/**
 * Determine input is function.
 *
 * @param {any} input - The input will determine is function.
 * @returns {boolean}
 */


function isFunction(input) {
  return Object.prototype.toString.call(input) === '[object Function]';
}
/**
 * Determine a string is number.
 * @param {string} input - the input will determine is number.
 * @returns {boolean}
 */


function isNumber(input) {
  return IS_NUMBER_REG.test(input);
}
/**
 * Make trace be silent.
 * @param {boolean} silentArg - The silent flag for log
 */


function setSilent(silentArg) {
  silent = silentArg;
}
/**
 * set bubbles config, default is false
 * @param bubbles
 */


function setBubbles() {
  var bubbles = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
  defaultBubbles = bubbles;
}
/**
 * get bubbles config
 * @returns boolean
 */


function isGlobalBubble() {
  return defaultBubbles;
}
/**
 * Convert Image url to specific type
 * @param url - image path
 */


function convertImgUrl(url) {
  if (url && !/^(http|https):\/\//.test(url) && url.indexOf('assets') > -1) {
    if (false) { var addStr1; }

    var addStr2 = 'hpfile://';
    return "".concat(addStr2, "./").concat(url);
  }

  return url;
}
/**
 * isHostComponent - judge current tag is hostComponent type
 * @param {number} tag
 */


function isHostComponent(tag) {
  return tag === 5;
}
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var HippyEventHub = /*#__PURE__*/function () {
  function HippyEventHub(eventName) {
    _classCallCheck(this, HippyEventHub);

    this.handlerContainer = {};
    this.nextIdForHandler = 0;
    this.eventName = eventName;
  }

  _createClass(HippyEventHub, [{
    key: "getEventListeners",
    value: function getEventListeners() {
      var _this = this;

      return Object.keys(this.handlerContainer).filter(function (key) {
        return _this.handlerContainer[key];
      }).map(function (key) {
        return _this.handlerContainer[key];
      });
    }
  }, {
    key: "getHandlerSize",
    value: function getHandlerSize() {
      return Object.keys(this.handlerContainer).length;
    }
  }, {
    key: "addEventHandler",
    value: function addEventHandler(handler, callContext) {
      if (!handler) {
        throw new TypeError('Invalid arguments for addEventHandler');
      }

      var currId = this.nextIdForHandler;
      this.nextIdForHandler += 1;
      var eventHandlerWrapper = {
        id: currId,
        eventHandler: handler,
        context: callContext
      };
      var idAttrName = "eventHandler_".concat(currId);
      this.handlerContainer[idAttrName] = eventHandlerWrapper;
      return currId;
    }
  }, {
    key: "notifyEvent",
    value: function notifyEvent(eventParams) {
      var _this2 = this;

      Object.keys(this.handlerContainer).forEach(function (key) {
        var instance = _this2.handlerContainer[key];

        if (!instance || !instance.eventHandler) {
          return;
        }

        if (instance.context) {
          instance.eventHandler.call(instance.context, eventParams);
        } else {
          instance.eventHandler(eventParams);
        }
      });
    }
  }, {
    key: "removeEventHandler",
    value: function removeEventHandler(handlerId) {
      if (typeof handlerId !== 'number') {
        throw new TypeError('Invalid arguments for removeEventHandler');
      }

      var idAttrName = "eventHandler_".concat(handlerId);

      if (this.handlerContainer[idAttrName]) {
        delete this.handlerContainer[idAttrName];
      }
    }
  }]);

  return HippyEventHub;
}();
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var Event = /*#__PURE__*/function () {
  /**
   * constructor
   * @param eventName - handler name, e.g. onClick
   * @param currentTarget - currentTarget is the node which the handler bind to
   * @param target - target is the node which triggered the real event
   */
  function Event(eventName, currentTarget, target) {
    _classCallCheck(this, Event);

    this.type = eventName;
    this.bubbles = true;
    this.timeStamp = Date.now(); // currentTarget is the node which the handler bind to

    this.currentTarget = currentTarget; // target is the node which triggered the real event

    this.target = target;
  }

  _createClass(Event, [{
    key: "stopPropagation",
    value: function stopPropagation() {
      this.bubbles = false;
    }
  }, {
    key: "preventDefault",
    value: function preventDefault() {// noop
    }
  }]);

  return Event;
}();
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var eventHubs = new Map();
var componentName$2 = ['%c[event]%c', 'color: green', 'color: auto'];

function receiveUIComponentEvent(nativeEvent) {
  trace.apply(void 0, componentName$2.concat(['receiveUIComponentEvent', nativeEvent]));

  if (!nativeEvent || !Array.isArray(nativeEvent) || nativeEvent.length < 2) {
    return;
  }

  var _nativeEvent = _slicedToArray(nativeEvent, 3),
      targetNodeId = _nativeEvent[0],
      eventName = _nativeEvent[1],
      eventParam = _nativeEvent[2];

  if (typeof targetNodeId !== 'number' || typeof eventName !== 'string') {
    return;
  }

  var targetNode = getFiberNodeFromId(targetNodeId);

  if (!targetNode) {
    return;
  }

  if (isNodePropFunction(eventName, targetNode)) {
    targetNode.memoizedProps[eventName](eventParam);
  }
}
/**
 * convertEventName - convert all special event name
 * @param eventName
 * @param nodeItem
 */


function convertEventName(eventName, nodeItem) {
  var processedEvenName = eventName;

  if (nodeItem.memoizedProps && !nodeItem.memoizedProps[eventName]) {
    var eventNameList = Object.keys(eventNamesMap);

    for (var i = 0; i < eventNameList.length; i += 1) {
      var uiEvent = eventNameList[i];
      var eventList = eventNamesMap[uiEvent];

      if (nodeItem.memoizedProps[uiEvent] && eventName === eventList[NATIVE_EVENT]) {
        processedEvenName = uiEvent;
        break;
      }
    }
  }

  return processedEvenName;
}

function isNodePropFunction(prop, nextNodeItem) {
  return !!(nextNodeItem.memoizedProps && typeof nextNodeItem.memoizedProps[prop] === 'function');
}
/**
 * doCaptureAndBubbleLoop - process capture phase and bubbling phase
 * @param {string} originalEventName
 * @param {NativeEvent} nativeEvent
 * @param {Fiber} nodeItem
 */


function doCaptureAndBubbleLoop(originalEventName, nativeEvent, nodeItem) {
  var eventQueue = [];
  var nextNodeItem = nodeItem;
  var eventName = originalEventName; // capture and bubbling loop

  while (nextNodeItem) {
    eventName = convertEventName(eventName, nextNodeItem);
    var captureName = "".concat(eventName, "Capture");

    if (isNodePropFunction(captureName, nextNodeItem)) {
      // capture phase to add listener at queue head
      eventQueue.unshift({
        eventName: captureName,
        listener: nextNodeItem.memoizedProps[captureName],
        isCapture: true,
        currentTarget: getElementFromFiber(nextNodeItem)
      });
    }

    if (isNodePropFunction(eventName, nextNodeItem)) {
      // bubbling phase to add listener at queue tail
      eventQueue.push({
        eventName,
        listener: nextNodeItem.memoizedProps[eventName],
        isCapture: false,
        currentTarget: getElementFromFiber(nextNodeItem)
      });
    }

    if (eventQueue.length === 0) {
      nextNodeItem = null;
    } else {
      nextNodeItem = nextNodeItem.return;

      while (nextNodeItem && !isHostComponent(nextNodeItem.tag)) {
        // only handle HostComponent
        nextNodeItem = nextNodeItem.return;
      }
    }
  }

  if (eventQueue.length > 0) {
    var listenerObj;
    var isStopBubble = false;
    var targetNode = getElementFromFiber(nodeItem);

    while (!isStopBubble && (listenerObj = eventQueue.shift()) !== undefined) {
      try {
        var _listenerObj = listenerObj,
            _eventName = _listenerObj.eventName,
            currentTargetNode = _listenerObj.currentTarget,
            listener = _listenerObj.listener,
            isCapture = _listenerObj.isCapture;
        var syntheticEvent = new Event(_eventName, currentTargetNode, targetNode);
        Object.assign(syntheticEvent, nativeEvent); // whether it is capture or bubbling event, returning false or calling stopPropagation would both stop phase

        if (isCapture) {
          listener(syntheticEvent); // event bubbles flag has higher priority

          if (!syntheticEvent.bubbles) {
            isStopBubble = true;
          }
        } else {
          isStopBubble = listener(syntheticEvent); // If callback have no return, use global bubble config to set isStopBubble.

          if (typeof isStopBubble !== 'boolean') {
            isStopBubble = !isGlobalBubble();
          } // event bubbles flag has higher priority


          if (!syntheticEvent.bubbles) {
            isStopBubble = true;
          }
        }
      } catch (err) {
        console.reportUncaughtException(err);
      }
    }
  }
}
/**
 * doBubbleLoop - process only bubbling phase
 * @param {string} originalEventName
 * @param {NativeEvent} nativeEvent
 * @param {Fiber} nodeItem
 */


function doBubbleLoop(originalEventName, nativeEvent, nodeItem) {
  var isStopBubble = false;
  var nextNodeItem = nodeItem;
  var eventName = originalEventName;
  var targetNode = getElementFromFiber(nodeItem); // only bubbling loop

  do {
    eventName = convertEventName(eventName, nextNodeItem);

    if (isNodePropFunction(eventName, nextNodeItem)) {
      try {
        var currentTargetNode = getElementFromFiber(nextNodeItem);
        var syntheticEvent = new Event(eventName, currentTargetNode, targetNode);
        Object.assign(syntheticEvent, nativeEvent);
        isStopBubble = nextNodeItem.memoizedProps[eventName](syntheticEvent); // If callback have no return, use global bubble config to set isStopBubble.

        if (typeof isStopBubble !== 'boolean') {
          isStopBubble = !isGlobalBubble();
        } // event bubbles flag has higher priority


        if (!syntheticEvent.bubbles) {
          isStopBubble = true;
        }
      } catch (err) {
        console.reportUncaughtException(err);
      }
    }

    if (isStopBubble === false) {
      nextNodeItem = nextNodeItem.return;

      while (nextNodeItem && !isHostComponent(nextNodeItem.tag)) {
        // only handle HostComponent
        nextNodeItem = nextNodeItem.return;
      }
    }
  } while (!isStopBubble && nextNodeItem);
}

function receiveNativeGesture(nativeEvent) {
  trace.apply(void 0, componentName$2.concat(['receiveNativeGesture', nativeEvent]));

  if (!nativeEvent) {
    return;
  }

  var targetNodeId = nativeEvent.id;
  var targetNode = getFiberNodeFromId(targetNodeId);

  if (!targetNode) {
    return;
  }

  var hasCapturePhase = true;
  var eventName = nativeEvent.name;
  eventName = convertEventName(eventName, targetNode);
  var captureName = "".concat(eventName, "Capture");
  var nextNodeItem = targetNode; // if current target has no capture listener, only do bubbling phase loop to improve performance

  if (targetNode.memoizedProps && typeof targetNode.memoizedProps[captureName] !== 'function') {
    hasCapturePhase = false;
  }

  if (hasCapturePhase) {
    doCaptureAndBubbleLoop(eventName, nativeEvent, nextNodeItem);
  } else {
    doBubbleLoop(eventName, nativeEvent, nextNodeItem);
  }
}

function getHippyEventHub(eventName) {
  if (typeof eventName !== 'string') {
    throw new TypeError("Invalid eventName for getHippyEventHub: ".concat(eventName));
  }

  return eventHubs.get(eventName) || null;
}

function registerNativeEventHub(eventName) {
  trace.apply(void 0, componentName$2.concat(['registerNativeEventHub', eventName]));

  if (typeof eventName !== 'string') {
    throw new TypeError("Invalid eventName for registerNativeEventHub: ".concat(eventName));
  }

  var targetEventHub = eventHubs.get(eventName);

  if (!targetEventHub) {
    targetEventHub = new HippyEventHub(eventName);
    eventHubs.set(eventName, targetEventHub);
  }

  return targetEventHub;
}

function unregisterNativeEventHub(eventName) {
  if (typeof eventName !== 'string') {
    throw new TypeError("Invalid eventName for unregisterNativeEventHub: ".concat(eventName));
  }

  if (eventHubs.has(eventName)) {
    eventHubs.delete(eventName);
  }
}

function receiveNativeEvent(nativeEvent) {
  trace.apply(void 0, componentName$2.concat(['receiveNativeEvent', nativeEvent]));

  if (!nativeEvent || !Array.isArray(nativeEvent) || nativeEvent.length < 2) {
    throw new TypeError("Invalid params for receiveNativeEvent: ".concat(JSON.stringify(nativeEvent)));
  }

  var _nativeEvent2 = _slicedToArray(nativeEvent, 2),
      eventName = _nativeEvent2[0],
      eventParams = _nativeEvent2[1];

  if (typeof eventName !== 'string') {
    throw new TypeError('Invalid arguments for nativeEvent eventName');
  }

  var currEventHub = getHippyEventHub(eventName);

  if (!currEventHub) {
    return;
  }

  currEventHub.notifyEvent(eventParams);
}

var EventDispatcher = {
  registerNativeEventHub,
  getHippyEventHub,
  unregisterNativeEventHub,
  receiveNativeEvent,
  receiveNativeGesture,
  receiveUIComponentEvent
};

if (global.__GLOBAL__) {
  global.__GLOBAL__.jsModuleList.EventDispatcher = EventDispatcher;
}
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var EventEmitterRevoker = /*#__PURE__*/function () {
  function EventEmitterRevoker(id, listener) {
    _classCallCheck(this, EventEmitterRevoker);

    this.callback = id;
    this.bindListener = listener;
  }

  _createClass(EventEmitterRevoker, [{
    key: "remove",
    value: function remove() {
      if (typeof this.callback !== 'number' || !this.bindListener) {
        return;
      }

      this.bindListener.removeCallback(this.callback);
      this.bindListener = undefined;
    }
  }]);

  return EventEmitterRevoker;
}();
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var HippyEventListener = /*#__PURE__*/function () {
  function HippyEventListener(event) {
    _classCallCheck(this, HippyEventListener);

    this.eventName = event;
    this.listenerIdList = [];
  }

  _createClass(HippyEventListener, [{
    key: "unregister",
    value: function unregister() {
      var eventHub = EventDispatcher.getHippyEventHub(this.eventName);

      if (!eventHub) {
        throw new ReferenceError("No listeners for ".concat(this.eventName));
      }

      var listenerIdSize = this.listenerIdList.length;

      for (var i = 0; i < listenerIdSize; i += 1) {
        eventHub.removeEventHandler(this.listenerIdList[i]);
      }

      this.listenerIdList = [];

      if (eventHub.getHandlerSize() === 0) {
        EventDispatcher.unregisterNativeEventHub(this.eventName);
      }
    }
  }, {
    key: "getSize",
    value: function getSize() {
      return this.listenerIdList.length;
    }
  }, {
    key: "addCallback",
    value: function addCallback(handleFunc, callContext) {
      if (typeof handleFunc !== 'function') {
        throw new TypeError('Invalid addCallback function arguments');
      }

      var targetEventHub = EventDispatcher.registerNativeEventHub(this.eventName);

      if (!targetEventHub) {
        throw new ReferenceError("No listeners for ".concat(this.eventName));
      }

      var listenerId = targetEventHub.addEventHandler(handleFunc, callContext);

      if (typeof listenerId !== 'number') {
        throw new Error('Fail to addEventHandler in addCallback function');
      }

      this.listenerIdList.push(listenerId);
      return listenerId;
    }
  }, {
    key: "removeCallback",
    value: function removeCallback(callbackId) {
      if (typeof callbackId !== 'number') {
        throw new TypeError('Invalid arguments for removeCallback');
      }

      var targetEventHub = EventDispatcher.getHippyEventHub(this.eventName);

      if (!targetEventHub) {
        throw new ReferenceError("No listeners for ".concat(this.eventName));
      }

      targetEventHub.removeEventHandler(callbackId);
      var listenerIdSize = this.listenerIdList.length;

      for (var i = 0; i < listenerIdSize; i += 1) {
        if (callbackId === this.listenerIdList[i]) {
          this.listenerIdList.splice(i, 1);
          break;
        }
      }
    }
  }]);

  return HippyEventListener;
}();
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


function getNameForEvent(event) {
  if (typeof event !== 'string') {
    throw new TypeError('Invalid arguments for getNameForEvent');
  }

  return "eventEmitter_".concat(event);
}

var HippyEventEmitter = /*#__PURE__*/function () {
  function HippyEventEmitter(sharedListeners) {
    _classCallCheck(this, HippyEventEmitter);

    if (sharedListeners && typeof sharedListeners === 'object') {
      this.hippyEventListeners = sharedListeners;
    } else {
      this.hippyEventListeners = {};
    }
  }

  _createClass(HippyEventEmitter, [{
    key: "sharedListeners",
    value: function sharedListeners() {
      return this.hippyEventListeners;
    }
  }, {
    key: "addListener",
    value: function addListener(event, callback, context) {
      if (typeof event !== 'string' || typeof callback !== 'function') {
        throw new TypeError('Invalid arguments for addListener');
      }

      var registeredListener = this.hippyEventListeners[getNameForEvent(event)];

      if (!registeredListener) {
        registeredListener = new HippyEventListener(event);
        this.hippyEventListeners[getNameForEvent(event)] = registeredListener;
      }

      var listenerId = registeredListener.addCallback(callback, context);

      if (typeof listenerId !== 'number') {
        throw new Error('Fail to addCallback in addListener');
      }

      return new EventEmitterRevoker(listenerId, registeredListener);
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners(event) {
      if (typeof event !== 'string') {
        throw new TypeError('Invalid arguments for removeAllListeners');
      }

      var registeredListener = this.hippyEventListeners[getNameForEvent(event)];

      if (registeredListener) {
        registeredListener.unregister();
        delete this.hippyEventListeners[getNameForEvent(event)];
      }
    }
  }, {
    key: "emit",
    value: function emit(event, param) {
      if (typeof event !== 'string') {
        return false;
      }

      var eventHub = EventDispatcher.getHippyEventHub(event);

      if (!eventHub) {
        return false;
      }

      eventHub.notifyEvent(param);
      return true;
    }
  }, {
    key: "listenerSize",
    value: function listenerSize(event) {
      if (typeof event !== 'string') {
        throw new TypeError('Invalid arguments for listenerSize');
      }

      var registeredListener = this.hippyEventListeners[getNameForEvent(event)];

      if (registeredListener) {
        return registeredListener.getSize();
      }

      return 0;
    }
  }]);

  return HippyEventEmitter;
}();

HippyEventEmitter.emit = HippyEventEmitter.prototype.emit;
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * The the string contents from clipboard
 */

function getString() {
  return Bridge$2.callNativeWithPromise('ClipboardModule', 'getString');
}
/**
 * Set the string content to clipboard
 *
 * @param {string} text - The string content that will set into clipboard.
 */


function setString(text) {
  Bridge$2.callNative('ClipboardModule', 'setString', text);
}

var clipboard = /*#__PURE__*/Object.freeze({
  __proto__: null,
  getString: getString,
  setString: setString
});
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Get cookies from url
 *
 * @param {string} url - Specific url for cookie
 */

function getCookies(url) {
  return Bridge$2.callNativeWithPromise('network', 'getCookie', url);
}
/**
 * Set cookie to url
 *
 * @param {string} url - Specific url for cookie.
 * @param {string} keyValue - Cookie key and value string, split with `:`.
 * @param {Date|string} [expires] - UTC Date string or Date object for cookie expire.
 */


function setCookie(url, keyValue, expires) {
  var expireStr = '';

  if (typeof expires === 'string') {
    expireStr = expires;
  }

  if (expires instanceof Date) {
    expireStr = expires.toUTCString();
  }

  Bridge$2.callNative('network', 'setCookie', url, keyValue, expireStr);
}

var cookieModule = /*#__PURE__*/Object.freeze({
  __proto__: null,
  getCookies: getCookies,
  setCookie: setCookie
});
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Get the image size before rendering.
 *
 * @param {string} url - Get image url.
 */

function _getSize(url) {
  return Bridge$2.callNativeWithPromise('ImageLoaderModule', 'getSize', url);
}
/**
 * Prefetch image, to make rendering in next more faster.
 *
 * @param {string} url - Prefetch image url.
 */


function prefetch(url) {
  Bridge$2.callNative('ImageLoaderModule', 'prefetch', url);
}

var imageLoaderModule = /*#__PURE__*/Object.freeze({
  __proto__: null,
  getSize: _getSize,
  prefetch: prefetch
});
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var DEVICE_CONNECTIVITY_EVENT = 'networkStatusDidChange';
var subScriptions = new Map();
var NetInfoEventEmitter;

var NetInfoRevoker = /*#__PURE__*/function () {
  function NetInfoRevoker(eventName, listener) {
    _classCallCheck(this, NetInfoRevoker);

    this.eventName = eventName;
    this.listener = listener;
  }

  _createClass(NetInfoRevoker, [{
    key: "remove",
    value: function remove() {
      if (!this.eventName || !this.listener) {
        return;
      }

      removeEventListener$1(this.eventName, this.listener);
      this.listener = undefined;
    }
  }]);

  return NetInfoRevoker;
}();
/**
 * Add a network status event listener
 *
 * @param {string} eventName - Event name will listen for NetInfo module,
 *                             use `change` for listen network change.
 * @param {function} listener - Event status event callback
 * @returns {object} NetInfoRevoker - The event revoker for destroy the network info event listener.
 */


function addEventListener$1(eventName, listener) {
  NetInfoEventEmitter = new HippyEventEmitter();
  var event = eventName;

  if (event && event === 'change') {
    event = DEVICE_CONNECTIVITY_EVENT;
  }

  var count = NetInfoEventEmitter.listenerSize(event);

  if (count < 1) {
    Bridge$2.callNative('NetInfo', 'addListener', event);
  }

  var handler = NetInfoEventEmitter.addListener(event, function (data) {
    listener(data);
  }); // FIXME: Seems only accept one callback for each event, should support multiple callback.

  subScriptions.set(listener, handler);
  return new NetInfoRevoker(event, listener);
}
/**
 * Remove network status event event listener
 *
 * @param {string} eventName - Event name will listen for NetInfo module,
 *                             use `change` for listen network change.
 * @param {Function} [listener] - The specific event listener will remove.
 */


function removeEventListener$1(eventName, listener) {
  if (listener instanceof NetInfoRevoker) {
    listener.remove();
    return;
  }

  var event = eventName;

  if (eventName === 'change') {
    event = DEVICE_CONNECTIVITY_EVENT;
  }

  var count = NetInfoEventEmitter.listenerSize(event);

  if (count <= 1) {
    Bridge$2.callNative('NetInfo', 'removeListener', event);
  }

  var handler = subScriptions.get(listener);

  if (!handler) {
    return;
  }

  handler.remove();
  subScriptions.delete(listener);
}
/**
 * Get the current network status
 */


function fetch() {
  return Bridge$2.callNativeWithPromise('NetInfo', 'getCurrentConnectivity').then(function (resp) {
    return resp.network_info;
  });
}

var networkInfo = /*#__PURE__*/Object.freeze({
  __proto__: null,
  addEventListener: addEventListener$1,
  removeEventListener: removeEventListener$1,
  fetch: fetch
});
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

function repeatCountDict(repeatCount) {
  if (repeatCount === 'loop') {
    return -1;
  }

  return repeatCount;
}
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var AnimationEventEmitter$1 = new HippyEventEmitter();
/**
 * parse value of special value type
 * @param valueType
 * @param originalValue
 */

function parseValue(valueType, originalValue) {
  if (valueType === 'color' && ['number', 'string'].indexOf(typeof originalValue) >= 0) {
    return colorParse(originalValue);
  }

  return originalValue;
}
/**
 * Better performance of Animation solution.
 *
 * It pushes the animation scheme to native at once.
 */


var Animation = /*#__PURE__*/function () {
  function Animation(config) {
    _classCallCheck(this, Animation);

    var _a;

    var startValue = 0;

    if (((_a = config.startValue) === null || _a === void 0 ? void 0 : _a.constructor) && config.startValue.constructor.name === 'Animation') {
      startValue = {
        animationId: config.startValue.animationId
      };
    } else {
      var tempStartValue = config.startValue;
      startValue = parseValue(config.valueType, tempStartValue);
    }

    var toValue = parseValue(config.valueType, config.toValue);
    this.mode = config.mode || 'timing';
    this.delay = config.delay || 0;
    this.startValue = startValue || 0;
    this.toValue = toValue || 0;
    this.valueType = config.valueType || undefined;
    this.duration = config.duration || 0;
    this.direction = config.direction || 'center';
    this.timingFunction = config.timingFunction || 'linear';
    this.repeatCount = repeatCountDict(config.repeatCount || 0);
    this.inputRange = config.inputRange || [];
    this.outputRange = config.outputRange || [];
    this.animationId = Bridge$1.callNativeWithCallbackId('AnimationModule', 'createAnimation', true, this.mode, Object.assign({
      delay: this.delay,
      startValue: this.startValue,
      toValue: this.toValue,
      duration: this.duration,
      direction: this.direction,
      timingFunction: this.timingFunction,
      repeatCount: this.repeatCount,
      inputRange: this.inputRange,
      outputRange: this.outputRange
    }, this.valueType ? {
      valueType: this.valueType
    } : {}));
    this.destroy = this.destroy.bind(this); // TODO: Deprecated compatible, will remove soon.

    this.onRNfqbAnimationStart = this.onAnimationStart.bind(this);
    this.onRNfqbAnimationEnd = this.onAnimationEnd.bind(this);
    this.onRNfqbAnimationCancel = this.onAnimationCancel.bind(this);
    this.onRNfqbAnimationRepeat = this.onAnimationRepeat.bind(this);
    this.onHippyAnimationStart = this.onAnimationStart.bind(this);
    this.onHippyAnimationEnd = this.onAnimationEnd.bind(this);
    this.onHippyAnimationCancel = this.onAnimationCancel.bind(this);
    this.onHippyAnimationRepeat = this.onAnimationRepeat.bind(this);
  }
  /**
   * Remove all of animation event listener
   */


  _createClass(Animation, [{
    key: "removeEventListener",
    value: function removeEventListener() {
      if (this.animationStartListener) {
        this.animationStartListener.remove();
      }

      if (this.animationEndListener) {
        this.animationEndListener.remove();
      }

      if (this.animationCancelListener) {
        this.animationCancelListener.remove();
      }

      if (this.animationRepeatListener) {
        this.animationRepeatListener.remove();
      }
    }
    /**
     * Start animation execution
     */

  }, {
    key: "start",
    value: function start() {
      var _this3 = this;

      this.removeEventListener(); // Set as iOS default

      var animationEventName = 'onAnimation'; // If running in Android, change it.

      if ( false || Device$1.platform.OS === 'android') {
        animationEventName = 'onHippyAnimation';
      }

      if (typeof this.onAnimationStartCallback === 'function') {
        this.animationStartListener = AnimationEventEmitter$1.addListener("".concat(animationEventName, "Start"), function (animationId) {
          if (animationId === _this3.animationId) {
            _this3.animationStartListener.remove();

            if (typeof _this3.onAnimationStartCallback === 'function') {
              _this3.onAnimationStartCallback();
            }
          }
        });
      }

      if (typeof this.onAnimationEndCallback === 'function') {
        this.animationEndListener = AnimationEventEmitter$1.addListener("".concat(animationEventName, "End"), function (animationId) {
          if (animationId === _this3.animationId) {
            _this3.animationEndListener.remove();

            if (typeof _this3.onAnimationEndCallback === 'function') {
              _this3.onAnimationEndCallback();
            }
          }
        });
      }

      if (typeof this.onAnimationCancelCallback === 'function') {
        this.animationCancelListener = AnimationEventEmitter$1.addListener("".concat(animationEventName, "Cancel"), function (animationId) {
          if (animationId === _this3.animationId) {
            _this3.animationCancelListener.remove();

            if (typeof _this3.onAnimationCancelCallback === 'function') {
              _this3.onAnimationCancelCallback();
            }
          }
        });
      }

      if (typeof this.onAnimationRepeatCallback === 'function') {
        this.animationRepeatListener = AnimationEventEmitter$1.addListener("".concat(animationEventName, "Repeat"), function (animationId) {
          if (animationId === _this3.animationId) {
            if (typeof _this3.onAnimationRepeatCallback === 'function') {
              _this3.onAnimationRepeatCallback();
            }
          }
        });
      }

      Bridge$1.callNative('AnimationModule', 'startAnimation', this.animationId);
    }
    /**
     * Use destroy() to destroy animation.
     */

  }, {
    key: "destory",
    value: function destory() {
      warn('Animation.destory() method will be deprecated soon, please use Animation.destroy() as soon as possible');
      this.destroy();
    }
    /**
     * Destroy the animation
     */

  }, {
    key: "destroy",
    value: function destroy() {
      this.removeEventListener();
      Bridge$1.callNative('AnimationModule', 'destroyAnimation', this.animationId);
    }
    /**
     * Pause the running animation
     */

  }, {
    key: "pause",
    value: function pause() {
      Bridge$1.callNative('AnimationModule', 'pauseAnimation', this.animationId);
    }
    /**
     * Resume execution of paused animation
     */

  }, {
    key: "resume",
    value: function resume() {
      Bridge$1.callNative('AnimationModule', 'resumeAnimation', this.animationId);
    }
    /**
     * Update to new animation scheme
     *
     * @param {Object} newConfig - new animation schema
     */

  }, {
    key: "updateAnimation",
    value: function updateAnimation(newConfig) {
      var _this4 = this;

      if (typeof newConfig !== 'object') {
        throw new TypeError('Invalid arguments');
      }

      if (typeof newConfig.mode === 'string' && newConfig.mode !== this.mode) {
        throw new TypeError('Update animation mode not supported');
      }

      Object.keys(newConfig).forEach(function (prop) {
        var value = newConfig[prop];

        if (prop === 'startValue') {
          var startValue = 0;

          if (newConfig.startValue instanceof Animation) {
            startValue = {
              animationId: newConfig.startValue.animationId
            };
          } else {
            var tempStartValue = newConfig.startValue;
            startValue = parseValue(_this4.valueType, tempStartValue);
          }

          _this4.startValue = startValue || 0;
        } else if (prop === 'repeatCount') {
          _this4.repeatCount = repeatCountDict(newConfig.repeatCount || 0);
        } else {
          Object.defineProperty(_this4, prop, {
            value
          });
        }
      });
      Bridge$1.callNative('AnimationModule', 'updateAnimation', this.animationId, Object.assign({
        delay: this.delay,
        startValue: this.startValue,
        toValue: parseValue(this.valueType, this.toValue),
        duration: this.duration,
        direction: this.direction,
        timingFunction: this.timingFunction,
        repeatCount: this.repeatCount,
        inputRange: this.inputRange,
        outputRange: this.outputRange
      }, this.valueType ? {
        valueType: this.valueType
      } : {}));
    }
    /**
     * Call when animation started.
     * @param {Function} cb - callback when animation started.
     */

  }, {
    key: "onAnimationStart",
    value: function onAnimationStart(cb) {
      this.onAnimationStartCallback = cb;
    }
    /**
     * Call when animation is ended.
     * @param {Function} cb - callback when animation started.
     */

  }, {
    key: "onAnimationEnd",
    value: function onAnimationEnd(cb) {
      this.onAnimationEndCallback = cb;
    }
    /**
     * Call when animation is canceled.
     * @param {Function} cb - callback when animation started.
     */

  }, {
    key: "onAnimationCancel",
    value: function onAnimationCancel(cb) {
      this.onAnimationCancelCallback = cb;
    }
    /**
     * Call when animation is repeated.
     * @param {Function} cb - callback when animation started.
     */

  }, {
    key: "onAnimationRepeat",
    value: function onAnimationRepeat(cb) {
      this.onAnimationRepeatCallback = cb;
    }
  }]);

  return Animation;
}();
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var AnimationEventEmitter = new HippyEventEmitter();
/**
 * Better performance of Animation series solution.
 *
 * It pushes the animation scheme to native at once.
 */

var AnimationSet = /*#__PURE__*/function () {
  function AnimationSet(config) {
    var _this5 = this;

    _classCallCheck(this, AnimationSet);

    this.animationList = [];
    config.children.forEach(function (item) {
      _this5.animationList.push({
        animationId: item.animation.animationId,
        follow: item.follow || false
      });
    });
    this.animationId = Bridge$1.callNativeWithCallbackId('AnimationModule', 'createAnimationSet', true, {
      repeatCount: repeatCountDict(config.repeatCount || 0),
      children: this.animationList,
      virtual: config.virtual
    }); // TODO: Deprecated compatible, will remove soon.

    this.onRNfqbAnimationStart = this.onAnimationStart.bind(this);
    this.onRNfqbAnimationEnd = this.onAnimationEnd.bind(this);
    this.onRNfqbAnimationCancel = this.onAnimationCancel.bind(this);
    this.onRNfqbAnimationRepeat = this.onAnimationRepeat.bind(this);
    this.onHippyAnimationStart = this.onAnimationStart.bind(this);
    this.onHippyAnimationEnd = this.onAnimationEnd.bind(this);
    this.onHippyAnimationCancel = this.onAnimationCancel.bind(this);
    this.onHippyAnimationRepeat = this.onAnimationRepeat.bind(this);
  }
  /**
   * Remove all of animation event listener
   */


  _createClass(AnimationSet, [{
    key: "removeEventListener",
    value: function removeEventListener() {
      if (this.animationStartListener) {
        this.animationStartListener.remove();
      }

      if (this.animationEndListener) {
        this.animationEndListener.remove();
      }

      if (this.animationCancelListener) {
        this.animationCancelListener.remove();
      }

      if (this.animationRepeatListener) {
        this.animationRepeatListener.remove();
      }
    }
    /**
     * Start animation execution
     */

  }, {
    key: "start",
    value: function start() {
      var _this6 = this;

      this.removeEventListener(); // Set as iOS default

      var animationEventName = 'onAnimation'; // If running in Android, change it.

      if ( false || Device$1.platform.OS === 'android') {
        animationEventName = 'onHippyAnimation';
      }

      if (typeof this.onAnimationStartCallback === 'function') {
        this.animationStartListener = AnimationEventEmitter.addListener("".concat(animationEventName, "Start"), function (animationId) {
          if (animationId === _this6.animationId) {
            _this6.animationStartListener.remove();

            if (typeof _this6.onAnimationStartCallback === 'function') {
              _this6.onAnimationStartCallback();
            }
          }
        });
      }

      if (typeof this.onAnimationEndCallback === 'function') {
        this.animationEndListener = AnimationEventEmitter.addListener("".concat(animationEventName, "End"), function (animationId) {
          if (animationId === _this6.animationId) {
            _this6.animationEndListener.remove();

            if (typeof _this6.onAnimationEndCallback === 'function') {
              _this6.onAnimationEndCallback();
            }
          }
        });
      }

      if (typeof this.onAnimationCancelCallback === 'function') {
        this.animationCancelListener = AnimationEventEmitter.addListener("".concat(animationEventName, "Cancel"), function (animationId) {
          if (animationId === _this6.animationId) {
            _this6.animationCancelListener.remove();

            if (typeof _this6.onAnimationCancelCallback === 'function') {
              _this6.onAnimationCancelCallback();
            }
          }
        });
      }

      if (typeof this.onAnimationRepeatCallback === 'function') {
        this.animationRepeatListener = AnimationEventEmitter.addListener("".concat(animationEventName, "Repeat"), function (animationId) {
          if (animationId === _this6.animationId) {
            if (typeof _this6.onAnimationRepeatCallback === 'function') {
              _this6.onAnimationRepeatCallback();
            }
          }
        });
      }

      Bridge$1.callNative('AnimationModule', 'startAnimation', this.animationId);
    }
    /**
     * Use destroy() to destroy animation.
     */

  }, {
    key: "destory",
    value: function destory() {
      warn('AnimationSet.destory() method will be deprecated soon, please use Animation.destroy() as soon as possible');
      this.destroy();
    }
    /**
     * Destroy the animation
     */

  }, {
    key: "destroy",
    value: function destroy() {
      this.removeEventListener();
      this.animationList.forEach(function (item) {
        return Number.isInteger(item.animationId) && Bridge$1.callNative('AnimationModule', 'destroyAnimation', item.animationId);
      });
      Bridge$1.callNative('AnimationModule', 'destroyAnimation', this.animationId);
    }
    /**
     * Pause the running animation
     */

  }, {
    key: "pause",
    value: function pause() {
      Bridge$1.callNative('AnimationModule', 'pauseAnimation', this.animationId);
    }
    /**
     * Resume execution of paused animation
     */

  }, {
    key: "resume",
    value: function resume() {
      Bridge$1.callNative('AnimationModule', 'resumeAnimation', this.animationId);
    }
    /**
     * Call when animation started.
     * @param {Function} cb - callback when animation started.
     */

  }, {
    key: "onAnimationStart",
    value: function onAnimationStart(cb) {
      this.onAnimationStartCallback = cb;
    }
    /**
     * Call when animation is ended.
     * @param {Function} cb - callback when animation started.
     */

  }, {
    key: "onAnimationEnd",
    value: function onAnimationEnd(cb) {
      this.onAnimationEndCallback = cb;
    }
    /**
     * Call when animation is canceled.
     * @param {Function} cb - callback when animation started.
     */

  }, {
    key: "onAnimationCancel",
    value: function onAnimationCancel(cb) {
      this.onAnimationCancelCallback = cb;
    }
    /**
     * Call when animation is repeated.
     * @param {Function} cb - callback when animation started.
     */

  }, {
    key: "onAnimationRepeat",
    value: function onAnimationRepeat(cb) {
      this.onAnimationRepeatCallback = cb;
    }
  }]);

  return AnimationSet;
}();
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var componentName$1 = ['%c[native]%c', 'color: red', 'color: auto'];
var NODE_OPERATION_TYPES = {
  createNode: Symbol('createNode'),
  updateNode: Symbol('updateNode'),
  deleteNode: Symbol('deleteNode')
};
var batchIdle = true;
var batchNodes = [];
/**
 * Convert an ordered node array into multiple fragments
 */

function chunkNodes(batchNodes) {
  var result = [];

  for (var i = 0; i < batchNodes.length; i += 1) {
    var chunk = batchNodes[i];
    var type = chunk.type,
        nodes = chunk.nodes;
    var lastChunk = result[result.length - 1];

    if (!lastChunk || lastChunk.type !== type) {
      result.push({
        type,
        nodes
      });
    } else {
      lastChunk.nodes = lastChunk.nodes.concat(nodes);
    }
  }

  return result;
}
/**
 * batch Updates from js to native
 * @param {number} rootViewId
 */


function batchUpdate(rootViewId) {
  var chunks = chunkNodes(batchNodes);
  chunks.forEach(function (chunk) {
    switch (chunk.type) {
      case NODE_OPERATION_TYPES.createNode:
        trace.apply(void 0, componentName$1.concat(['createNode', chunk.nodes]));
        createNode(rootViewId, chunk.nodes);
        break;

      case NODE_OPERATION_TYPES.updateNode:
        trace.apply(void 0, componentName$1.concat(['updateNode', chunk.nodes]));

        if (true) {
          chunk.nodes.forEach(function (node) {
            return updateNode(rootViewId, [node]);
          });
        } else {}

        break;

      case NODE_OPERATION_TYPES.deleteNode:
        trace.apply(void 0, componentName$1.concat(['deleteNode', chunk.nodes]));

        if (true) {
          chunk.nodes.forEach(function (node) {
            return deleteNode(rootViewId, [node]);
          });
        } else {}

        break;
      // pass
    }
  });
}
/**
 * endBatch - end batch update
 * @param {boolean} isHookUsed - whether used commitEffects hook
 */


function endBatch$1() {
  var isHookUsed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
  if (!batchIdle) return;
  batchIdle = false;

  if (batchNodes.length === 0) {
    batchIdle = true;
    return;
  }

  var rootViewId = getRootViewId();
  startBatch(); // if commitEffectsHook used, call batchUpdate synchronously

  if (isHookUsed) {
    batchUpdate(rootViewId);
    endBatch();
    batchNodes = [];
    batchIdle = true;
  } else {
    Promise.resolve().then(function () {
      batchUpdate(rootViewId);
      endBatch();
      batchNodes = [];
      batchIdle = true;
    });
  }
}
/**
 * Translate to native props from attributes and meta
 */


function getNativeProps(node) {
  var _node$attributes = node.attributes,
      children = _node$attributes.children,
      otherProps = _objectWithoutProperties(_node$attributes, _excluded);

  return otherProps;
}
/**
 * Get target node attributes, used to chrome devTool tag attribute show while debugging
 */


function getTargetNodeAttributes(targetNode) {
  try {
    var targetNodeAttributes = JSON.parse(JSON.stringify(targetNode.attributes));

    var attributes = _objectSpread({
      id: targetNode.id
    }, targetNodeAttributes); // delete special __bind__event attribute, which is used in C DOM


    Object.keys(attributes).forEach(function (key) {
      if (key.indexOf('__bind__') === 0 && typeof attributes[key] === 'boolean') {
        delete attributes[key];
      }
    });
    delete attributes.text;
    delete attributes.value;
    return attributes;
  } catch (e) {
    warn('getTargetNodeAttributes error:', e);
    return {};
  }
}
/**
 * Render Element to native
 */


function renderToNative(rootViewId, targetNode) {
  var _a;

  if (!targetNode.nativeName) {
    warn('Component need to define the native name', targetNode);
    return null;
  }

  if (targetNode.meta.skipAddToDom) {
    return null;
  }

  if (!targetNode.meta.component) {
    throw new Error("Specific tag is not supported yet: ".concat(targetNode.tagName));
  } // Translate to native node


  var nativeNode = {
    id: targetNode.nodeId,
    pId: ((_a = targetNode.parentNode) === null || _a === void 0 ? void 0 : _a.nodeId) || rootViewId,
    index: targetNode.index,
    name: targetNode.nativeName,
    props: _objectSpread(_objectSpread({}, getNativeProps(targetNode)), {}, {
      style: targetNode.style
    })
  }; // Add nativeNode attributes info for debugging

  if (false) {}

  return nativeNode;
}
/**
 * Render Element with children to native
 * @param {number} rootViewId - rootView id
 * @param {ViewNode} node - current node
 * @param {number} [atIndex] - current node index
 * @param {Function} [callback] - function called on each traversing process
 * @returns {HippyTypes.NativeNode[]}
 */


function renderToNativeWithChildren(rootViewId, node, atIndex, callback) {
  var nativeLanguages = [];
  var index = atIndex;

  if (typeof index === 'undefined' && node && node.parentNode) {
    index = node.parentNode.childNodes.indexOf(node);
  }

  node.traverseChildren(function (targetNode) {
    var nativeNode = renderToNative(rootViewId, targetNode);

    if (nativeNode) {
      nativeLanguages.push(nativeNode);
    }

    if (typeof callback === 'function') {
      callback(targetNode);
    }
  }, index);
  return nativeLanguages;
}

function isLayout(node) {
  var container = getRootContainer();

  if (!container) {
    return false;
  } // Determine node is a Document instance


  return node instanceof container.containerInfo.constructor;
}

function insertChild(parentNode, childNode) {
  var atIndex = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : -1;

  if (!parentNode || !childNode) {
    return;
  }

  if (childNode.meta.skipAddToDom) {
    return;
  }

  var rootViewId = getRootViewId(); // Render the root node

  if (isLayout(parentNode) && !parentNode.isMounted) {
    // Start real native work.
    var translated = renderToNativeWithChildren(rootViewId, childNode, atIndex, function (node) {
      if (!node.isMounted) {
        node.isMounted = true;
      }
    });
    batchNodes.push({
      type: NODE_OPERATION_TYPES.createNode,
      nodes: translated
    }); // endBatch();
    // Render others child nodes.
  } else if (parentNode.isMounted && !childNode.isMounted) {
    var _translated = renderToNativeWithChildren(rootViewId, childNode, atIndex, function (node) {
      if (!node.isMounted) {
        node.isMounted = true;
      }
    });

    batchNodes.push({
      type: NODE_OPERATION_TYPES.createNode,
      nodes: _translated
    }); // endBatch();
  }
}

function removeChild$1(parentNode, childNode, index) {
  if (!childNode || childNode.meta.skipAddToDom) {
    return;
  }

  childNode.isMounted = false;
  childNode.index = index;
  var rootViewId = getRootViewId();
  var deleteNodeIds = [{
    id: childNode.nodeId,
    pId: childNode.parentNode ? childNode.parentNode.nodeId : rootViewId,
    index: childNode.index
  }];
  batchNodes.push({
    type: NODE_OPERATION_TYPES.deleteNode,
    nodes: deleteNodeIds
  }); // endBatch();
}

function updateChild(parentNode) {
  if (!parentNode.isMounted) {
    return;
  }

  var rootViewId = getRootViewId();
  var translated = renderToNative(rootViewId, parentNode);

  if (translated) {
    batchNodes.push({
      type: NODE_OPERATION_TYPES.updateNode,
      nodes: [translated]
    });
  } // endBatch();

}

function updateWithChildren(parentNode) {
  if (!parentNode.isMounted) {
    return;
  }

  var rootViewId = getRootViewId();
  var translated = renderToNativeWithChildren(rootViewId, parentNode);
  batchNodes.push({
    type: NODE_OPERATION_TYPES.updateNode,
    nodes: translated
  }); // endBatch();
}
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var currentNodeId = 0;

function getNodeId() {
  currentNodeId += 1; // currentNodeId % 10 === 0 is rootView
  // It's a limitation of iOS SDK.

  if (currentNodeId % 10 === 0) {
    currentNodeId += 1;
  }

  return currentNodeId;
}

var ViewNode = /*#__PURE__*/function () {
  function ViewNode() {
    _classCallCheck(this, ViewNode);

    // Component meta information, such as native component will use.
    this.meta = {
      component: {}
    }; // Index number in children, will update at traverseChildren method.

    this.index = 0; // Relation nodes.

    this.childNodes = [];
    this.parentNode = null; // Will change to be true after insert into Native dom.

    this.mounted = false; // Virtual DOM node id, will used in native to identify.

    this.nodeId = getNodeId();
  }
  /* istanbul ignore next */


  _createClass(ViewNode, [{
    key: "toString",
    value: function toString() {
      return this.constructor.name;
    }
  }, {
    key: "isMounted",
    get: function get() {
      return this.mounted;
    },
    set: function set(isMounted) {
      // TODO: Maybe need validation, maybe not.
      this.mounted = isMounted;
    }
  }, {
    key: "insertBefore",
    value: function insertBefore(childNode, referenceNode) {
      if (!childNode) {
        throw new Error('Can\'t insert child.');
      }

      if (childNode.meta.skipAddToDom) {
        return;
      }

      if (!referenceNode) {
        return this.appendChild(childNode);
      }

      if (referenceNode.parentNode !== this) {
        throw new Error('Can\'t insert child, because the reference node has a different parent.');
      }

      if (childNode.parentNode && childNode.parentNode !== this) {
        throw new Error('Can\'t insert child, because it already has a different parent.');
      }

      var index = this.childNodes.indexOf(referenceNode);
      childNode.parentNode = this;
      this.childNodes.splice(index, 0, childNode);
      return insertChild(this, childNode, index);
    }
  }, {
    key: "moveChild",
    value: function moveChild(childNode, referenceNode) {
      if (!childNode) {
        throw new Error('Can\'t move child.');
      }

      if (childNode.meta.skipAddToDom) {
        return;
      }

      if (!referenceNode) {
        return this.appendChild(childNode);
      }

      if (referenceNode.parentNode !== this) {
        throw new Error('Can\'t move child, because the reference node has a different parent.');
      }

      if (childNode.parentNode && childNode.parentNode !== this) {
        throw new Error('Can\'t move child, because it already has a different parent.');
      }

      var oldIndex = this.childNodes.indexOf(childNode);
      var referenceIndex = this.childNodes.indexOf(referenceNode); // return if the moved index is the same as the previous one

      if (referenceIndex === oldIndex) {
        return childNode;
      } // remove old child and insert new child, which is like moving child


      this.childNodes.splice(oldIndex, 1);
      removeChild$1(this, childNode, oldIndex);
      var newIndex = this.childNodes.indexOf(referenceNode);
      this.childNodes.splice(newIndex, 0, childNode);
      return insertChild(this, childNode, newIndex);
    }
  }, {
    key: "appendChild",
    value: function appendChild(childNode) {
      if (!childNode) {
        throw new Error('Can\'t append child.');
      }

      if (childNode.meta.skipAddToDom) {
        return;
      }

      if (childNode.parentNode && childNode.parentNode !== this) {
        throw new Error('Can\'t append child, because it already has a different parent.');
      }

      childNode.parentNode = this;
      this.childNodes.push(childNode);
      insertChild(this, childNode, this.childNodes.length - 1);
    }
  }, {
    key: "removeChild",
    value: function removeChild(childNode) {
      if (!childNode) {
        throw new Error('Can\'t remove child.');
      }

      if (childNode.meta.skipAddToDom) {
        return;
      }

      if (!childNode.parentNode) {
        throw new Error('Can\'t remove child, because it has no parent.');
      }

      if (childNode.parentNode !== this) {
        throw new Error('Can\'t remove child, because it has a different parent.');
      }

      var index = this.childNodes.indexOf(childNode);
      this.childNodes.splice(index, 1);
      removeChild$1(this, childNode, index);
    }
    /**
     * Find a specific target with condition
     */

  }, {
    key: "findChild",
    value: function findChild(condition) {
      var yes = condition(this);

      if (yes) {
        return this;
      }

      if (this.childNodes.length) {
        for (var i = 0; i < this.childNodes.length; i += 1) {
          var childNode = this.childNodes[i];
          var targetChild = this.findChild.call(childNode, condition);

          if (targetChild) {
            return targetChild;
          }
        }
      }

      return null;
    }
    /**
     * Traverse the children and execute callback
     * @param callback - callback function
     * @param newIndex - index to be updated
     */

  }, {
    key: "traverseChildren",
    value: function traverseChildren(callback) {
      var _this7 = this;

      var newIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
      this.index = !this.parentNode ? 0 : newIndex;
      callback(this); // Find the children

      if (this.childNodes.length) {
        this.childNodes.forEach(function (childNode, index) {
          _this7.traverseChildren.call(childNode, callback, index);
        });
      }
    }
  }]);

  return ViewNode;
}();
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var PROPERTIES_MAP = {
  textDecoration: 'textDecorationLine',
  boxShadowOffset: 'shadowOffset',
  boxShadowOffsetX: 'shadowOffsetX',
  boxShadowOffsetY: 'shadowOffsetY',
  boxShadowOpacity: 'shadowOpacity',
  boxShadowRadius: 'shadowRadius',
  boxShadowSpread: 'shadowSpread',
  boxShadowColor: 'shadowColor'
}; // linear-gradient direction description map

var LINEAR_GRADIENT_DIRECTION_MAP = {
  totop: '0',
  totopright: 'totopright',
  toright: '90',
  tobottomright: 'tobottomright',
  tobottom: '180',
  tobottomleft: 'tobottomleft',
  toleft: '270',
  totopleft: 'totopleft'
};
var DEGREE_UNIT = {
  TURN: 'turn',
  RAD: 'rad',
  DEG: 'deg'
};
/**
 * convert string value to string degree
 * @param {string} value
 * @param {string} unit
 */

function convertToDegree(value) {
  var unit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DEGREE_UNIT.DEG;
  var convertedNumValue = parseFloat(value);
  var result = value || '';

  var _value$split = value.split('.'),
      _value$split2 = _slicedToArray(_value$split, 2),
      decimals = _value$split2[1];

  if (decimals && decimals.length > 2) {
    result = convertedNumValue.toFixed(2);
  }

  switch (unit) {
    // turn unit
    case DEGREE_UNIT.TURN:
      result = "".concat((convertedNumValue * 360).toFixed(2));
      break;
    // radius unit

    case DEGREE_UNIT.RAD:
      result = "".concat((180 / Math.PI * convertedNumValue).toFixed(2));
      break;
  }

  return result;
}
/**
 * parse gradient angle or direction
 * @param {string} value
 */


function getLinearGradientAngle(value) {
  var processedValue = (value || '').replace(/\s*/g, '').toLowerCase();
  var reg = /^([+-]?\d+\.?\d*)+(deg|turn|rad)|(to\w+)$/g;
  var valueList = reg.exec(processedValue);
  if (!Array.isArray(valueList)) return; // default direction is to bottom, i.e. 180degree

  var angle = '180';

  var _valueList = _slicedToArray(valueList, 3),
      direction = _valueList[0],
      angleValue = _valueList[1],
      angleUnit = _valueList[2];

  if (angleValue && angleUnit) {
    // angle value
    angle = convertToDegree(angleValue, angleUnit);
  } else if (direction && typeof LINEAR_GRADIENT_DIRECTION_MAP[direction] !== 'undefined') {
    // direction description
    angle = LINEAR_GRADIENT_DIRECTION_MAP[direction];
  } else {
    warn('linear-gradient direction or angle is invalid, default value [to bottom] would be used');
  }

  return angle;
}
/**
 * parse gradient color stop
 * @param {string} value
 */


function getLinearGradientColorStop(value) {
  var processedValue = (value || '').replace(/\s+/g, ' ').trim();

  var _processedValue$split = processedValue.split(/\s+(?![^(]*?\))/),
      _processedValue$split2 = _slicedToArray(_processedValue$split, 2),
      color = _processedValue$split2[0],
      percentage = _processedValue$split2[1];

  var percentageCheckReg = /^([+-]?\d+\.?\d*)%$/g;

  if (color && !percentageCheckReg.exec(color) && !percentage) {
    return {
      color: colorParse(color)
    };
  }

  if (color && percentageCheckReg.exec(percentage)) {
    return {
      // color stop ratio
      ratio: parseFloat(percentage.split('%')[0]) / 100,
      color: colorParse(color)
    };
  }

  warn('linear-gradient color stop is invalid');
}
/**
 * parse backgroundImage
 * @param {string} styleKey
 * @param {string} styleValue
 * @param style
 */


function parseBackgroundImage(styleKey, styleValue, style) {
  // handle linear-gradient style
  if (styleValue.indexOf('linear-gradient') === 0) {
    var valueString = styleValue.substring(styleValue.indexOf('(') + 1, styleValue.lastIndexOf(')'));
    var tokens = valueString.split(/,(?![^(]*?\))/);
    var colorStopList = [];
    style.linearGradient = style.linearGradient || {};
    tokens.forEach(function (value, index) {
      if (index === 0) {
        // the angle of linear-gradient parameter can be optional
        var angle = getLinearGradientAngle(value);

        if (angle) {
          style.linearGradient.angle = angle;
        } else {
          // if angle ignored, default direction is to bottom, i.e. 180degree
          style.linearGradient.angle = '180';
          var colorObject = getLinearGradientColorStop(value);
          if (colorObject) colorStopList.push(colorObject);
        }
      } else {
        var _colorObject = getLinearGradientColorStop(value);

        if (_colorObject) colorStopList.push(_colorObject);
      }
    });
    style.linearGradient.colorStopList = colorStopList;
  } else {
    style[styleKey] = convertImgUrl(styleValue);
  }

  return style;
}
/**
 * parse text shadow offset
 * @param {string} styleKey
 * @param {number} styleValue
 * @param {any} style
 */


function parseTextShadowOffset(styleKey, styleValue, style) {
  var offsetMap = {
    textShadowOffsetX: 'width',
    textShadowOffsetY: 'height'
  };
  style.textShadowOffset = style.textShadowOffset || {};
  Object.assign(style.textShadowOffset, {
    [offsetMap[styleKey]]: styleValue || 0
  });
  return style;
}
/**
 * get final key sent to native
 * @param key
 */


function getEventPropKey(key) {
  if (isCaptureEvent(key)) {
    key = key.replace('Capture', '');
  }

  if (eventNamesMap[key]) {
    return eventNamesMap[key][NATIVE_EVENT];
  }

  return key;
}

var ElementNode = /*#__PURE__*/function (_ViewNode) {
  _inherits(ElementNode, _ViewNode);

  var _super = _createSuper(ElementNode);

  function ElementNode(tagName) {
    var _this8;

    _classCallCheck(this, ElementNode);

    _this8 = _super.call(this);
    _this8.id = '';
    _this8.style = {};
    _this8.attributes = {}; // Tag name

    _this8.tagName = tagName;
    return _this8;
  }

  _createClass(ElementNode, [{
    key: "nativeName",
    get: function get() {
      return this.meta.component.name;
    }
  }, {
    key: "toString",
    value: function toString() {
      return "".concat(this.tagName, ":(").concat(this.nativeName, ")");
    }
  }, {
    key: "hasAttribute",
    value: function hasAttribute(key) {
      return !!this.attributes[key];
    }
  }, {
    key: "getAttribute",
    value: function getAttribute(key) {
      return this.attributes[key];
    }
  }, {
    key: "setStyleAttribute",
    value: function setStyleAttribute(value) {
      var _this9 = this;

      // Clean old styles
      this.style = {};
      var styleArray = value; // Convert style to array if it's a array like object
      // Forward compatibility workaround.

      if (!Array.isArray(styleArray) && Object.hasOwnProperty.call(styleArray, 0)) {
        var tempStyle = [];
        var tempObjStyle = {};
        Object.keys(styleArray).forEach(function (styleKey) {
          // Workaround for the array and object mixed style.
          if (isNumber(styleKey)) {
            tempStyle.push(styleArray[styleKey]);
          } else {
            tempObjStyle[styleKey] = styleArray[styleKey];
          }
        });
        styleArray = [].concat(tempStyle, [tempObjStyle]);
      } // Convert style to array if style is a standalone object


      if (!Array.isArray(styleArray)) {
        styleArray = [styleArray];
      } // Merge the styles if style is array


      var mergedStyles = {};
      styleArray.forEach(function (style) {
        if (Array.isArray(style)) {
          style.forEach(function (subStyle) {
            mergedStyles = _objectSpread(_objectSpread({}, mergedStyles), subStyle);
          });
        } else if (typeof style === 'object' && style) {
          // TODO: Merge transform
          mergedStyles = _objectSpread(_objectSpread({}, mergedStyles), style);
        }
      }); // Apply the styles

      Object.keys(mergedStyles).forEach(function (styleKey) {
        var styleValue = mergedStyles[styleKey]; // Convert the property to W3C standard.

        if (Object.prototype.hasOwnProperty.call(PROPERTIES_MAP, styleKey)) {
          styleKey = PROPERTIES_MAP[styleKey];
        }

        if (styleKey === 'transform') {
          var transforms = {};

          if (!Array.isArray(styleValue)) {
            throw new TypeError('transform only support array args');
          } // Merge the transform styles


          styleValue.forEach(function (transformSet) {
            Object.keys(transformSet).forEach(function (transform) {
              var transformValue = transformSet[transform];

              if (transformValue instanceof Animation || transformValue instanceof AnimationSet) {
                transforms[transform] = {
                  animationId: transformValue.animationId
                };
              } else if (transformValue === null) {
                if (transforms[transform]) {
                  delete transforms[transform];
                }
              } else if (transformValue !== undefined) {
                transforms[transform] = transformValue;
              }
            });
          }); // Save the transform styles.

          var transformsKeys = Object.keys(transforms);

          if (transformsKeys.length) {
            if (!Array.isArray(_this9.style.transform)) {
              _this9.style.transform = [];
            }

            transformsKeys.forEach(function (transform) {
              return _this9.style.transform.push({
                [transform]: transforms[transform]
              });
            });
          }
        } else if (styleValue === null && _this9.style[styleKey] !== undefined) {
          _this9.style[styleKey] = undefined; // Convert to animationId if value is instanceOf Animation/AnimationSet
        } else if (styleValue instanceof Animation || styleValue instanceof AnimationSet) {
          _this9.style[styleKey] = {
            animationId: styleValue.animationId
          }; // Translate color
        } else if (styleKey.toLowerCase().indexOf('colors') > -1) {
          _this9.style[styleKey] = colorArrayParse(styleValue);
        } else if (styleKey.toLowerCase().indexOf('color') > -1) {
          _this9.style[styleKey] = colorParse(styleValue);
        } else if (styleKey === 'backgroundImage' && styleValue) {
          _this9.style = parseBackgroundImage(styleKey, styleValue, _this9.style);
        } else if (styleKey === 'textShadowOffset') {
          var _ref = styleValue || {},
              _ref$x = _ref.x,
              x = _ref$x === void 0 ? 0 : _ref$x,
              _ref$width = _ref.width,
              width = _ref$width === void 0 ? 0 : _ref$width,
              _ref$y = _ref.y,
              y = _ref$y === void 0 ? 0 : _ref$y,
              _ref$height = _ref.height,
              height = _ref$height === void 0 ? 0 : _ref$height;

          _this9.style[styleKey] = {
            width: x || width,
            height: y || height
          };
        } else if (['textShadowOffsetX', 'textShadowOffsetY'].indexOf(styleKey) >= 0) {
          _this9.style = parseTextShadowOffset(styleKey, styleValue, _this9.style);
        } else {
          _this9.style[styleKey] = styleValue;
        }
      });
    }
    /* istanbul ignore next */

  }, {
    key: "setAttribute",
    value: function setAttribute(key, value) {
      var _this10 = this;

      try {
        // detect expandable attrs for boolean values
        // See https://vuejs.org/v2/guide/components-props.html#Passing-a-Boolean
        if (typeof this.attributes[key] === 'boolean' && value === '') {
          value = true;
        }

        if (key === undefined) {
          updateChild(this);
          return;
        }

        var caseList = [{
          match: function match() {
            return ['id'].indexOf(key) >= 0;
          },
          action: function action() {
            if (value === _this10.id) {
              return true;
            }

            _this10.id = value; // update current node and child nodes

            updateWithChildren(_this10);
            return true;
          }
        }, {
          match: function match() {
            return ['value', 'defaultValue', 'placeholder'].indexOf(key) >= 0;
          },
          action: function action() {
            _this10.attributes[key] = unicodeToChar(value);
            return false;
          }
        }, {
          match: function match() {
            return ['text'].indexOf(key) >= 0;
          },
          action: function action() {
            _this10.attributes[key] = value;
            return false;
          }
        }, {
          match: function match() {
            return ['numberOfRows'].indexOf(key) >= 0;
          },
          action: function action() {
            _this10.attributes[key] = value;
            return Device$1.platform.OS !== 'ios';
          }
        }, {
          match: function match() {
            return ['style'].indexOf(key) >= 0;
          },
          action: function action() {
            if (typeof value !== 'object' || value === undefined || value === null) {
              return true;
            }

            _this10.setStyleAttribute(value);

            return false;
          }
        }, {
          match: function match() {
            return true;
          },
          action: function action() {
            if (typeof value === 'function') {
              var processedKey = getEventPropKey(key);
              _this10.attributes[processedKey] = true;
              _this10.attributes["__bind__".concat(processedKey)] = true;
            } else {
              _this10.attributes[key] = value;

              var _processedKey = getEventPropKey(key);

              if (_this10.attributes["__bind__".concat(_processedKey)] === true && typeof value !== 'function') {
                _this10.attributes[_processedKey] = false;
                _this10.attributes["__bind__".concat(_processedKey)] = false;
              }
            }

            return false;
          }
        }];
        var isNeedReturn = false;
        caseList.some(function (conditionObj) {
          if (conditionObj.match()) {
            isNeedReturn = conditionObj.action();
            return true;
          }

          return false;
        });
        if (isNeedReturn) return; // Set useAnimation if animation exist in style

        var useAnimation = false;
        Object.keys(this.style).some(function (declare) {
          var style = _this10.style[declare];

          if (style && Array.isArray(style) && declare === 'transform') {
            for (var i = 0; i < style.length; i += 1) {
              var transform = style[i];
              /* eslint-disable-next-line no-restricted-syntax, guard-for-in */

              for (var transformKey in transform) {
                var transformValue = transform[transformKey];

                if (typeof transformValue === 'object' && transformValue !== null && Number.isInteger(transformValue.animationId)) {
                  useAnimation = true;
                  return transformValue;
                }
              }
            }
          }

          if (typeof style === 'object' && style !== null && Number.isInteger(style.animationId)) {
            useAnimation = true;
            return style;
          }

          return false;
        });

        if (useAnimation) {
          this.attributes.useAnimation = true;
        } else if (typeof this.attributes.useAnimation === 'boolean') {
          this.attributes.useAnimation = undefined;
        }

        updateChild(this);
      } catch (e) {// noop
      }
    }
  }, {
    key: "removeAttribute",
    value: function removeAttribute(key) {
      delete this.attributes[key];
    }
    /* istanbul ignore next */

  }, {
    key: "setStyle",
    value: function setStyle(property, value) {
      var isBatchUpdate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;

      if (value === null) {
        delete this.style[property];
        return;
      }

      var v = value;
      var p = property; // Convert the property to W3C standard.

      if (Object.prototype.hasOwnProperty.call(PROPERTIES_MAP, property)) {
        p = PROPERTIES_MAP[property];
      }

      if (typeof v === 'string') {
        v = value.trim();

        if (p.toLowerCase().indexOf('colors') > -1) {
          v = colorArrayParse(v);
        } else if (p.toLowerCase().indexOf('color') > -1) {
          v = colorParse(v);
        } else {
          v = tryConvertNumber(v);
        }
      }

      if (v === undefined || v === null || this.style[p] === v) {
        return;
      }

      this.style[p] = v;

      if (!isBatchUpdate) {
        updateChild(this);
      }
    }
    /**
     * set native style props
     */

  }, {
    key: "setNativeProps",
    value: function setNativeProps(nativeProps) {
      var _this11 = this;

      if (nativeProps) {
        var style = nativeProps.style;

        if (style) {
          var styleProps = style;
          Object.keys(styleProps).forEach(function (key) {
            _this11.setStyle(key, styleProps[key], true);
          });
          updateChild(this);
          endBatch$1(true);
        }
      }
    }
  }, {
    key: "setText",
    value: function setText(text) {
      if (typeof text !== 'string') {
        try {
          text = text.toString();
        } catch (err) {
          throw new Error('Only string type is acceptable for setText');
        }
      }

      text = text.trim();

      if (!text && !this.getAttribute('text')) {
        return null;
      }

      text = unicodeToChar(text);
      text = text.replace(/&nbsp;/g, ' ').replace(/\xc2/g, ' '); // FIXME: \xc2 is a template compiler error.
      // Hacking for textarea, use value props to instance text props

      if (this.tagName === 'textarea') {
        return this.setAttribute('value', text);
      }

      return this.setAttribute('text', text);
    }
  }]);

  return ElementNode;
}(ViewNode);
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var createNode = UIManager.createNode,
    updateNode = UIManager.updateNode,
    deleteNode = UIManager.deleteNode,
    flushBatch = UIManager.flushBatch,
    startBatch = UIManager.startBatch,
    endBatch = UIManager.endBatch,
    sendRenderError = UIManager.sendRenderError;
var getNodeById = findNodeById;
/**
 * Get the nodeId from FiberNode ref.
 *
 * @param {Fiber} ref - ref instance.
 */

function getElementFromFiberRef(ref) {
  if (ref instanceof ElementNode) {
    return ref;
  } // FIXME: should not use the private _reactInternalFiber


  var internalFiber = ref._reactInternalFiber || ref._reactInternals;

  if (internalFiber === null || internalFiber === void 0 ? void 0 : internalFiber.child) {
    var targetNode = internalFiber.child;

    while (targetNode && !(targetNode.stateNode instanceof ElementNode)) {
      targetNode = targetNode.child;
    }

    if (!targetNode || !targetNode.stateNode) {
      return null;
    }

    return targetNode.stateNode;
  }

  return null;
}
/**
 * Get the nodeId number by ref
 * Most use in the module access components.
 *
 * @param {string | Fiber | Fiber} ref - ref instance, reference to the class is recommend
 */


function getNodeIdByRef(ref) {
  // typeof ref === 'string'
  var tempRef = ref;

  if (typeof ref === 'string') {
    warn("getNodeIdByRef('".concat(ref, "') use string ref will affect to performance, recommend use reference to the ref instead"));
    var targetElement = findNodeByCondition(function (node) {
      /* eslint-disable-next-line no-underscore-dangle */
      if (!node.return || !node.return.ref || !node.return.ref._stringRef) {
        return false;
      }
      /* eslint-disable-next-line no-underscore-dangle */


      return node.return.ref._stringRef === ref;
    });

    if (!targetElement || !targetElement.stateNode) {
      return 0;
    }

    tempRef = targetElement.stateNode;
  } // typeof fiberRef === 'Fiber'


  if (!tempRef.nodeId) {
    var _targetElement = getElementFromFiberRef(tempRef);

    if (!_targetElement) {
      return 0;
    }

    return _targetElement.nodeId;
  } // typeof ref === 'Element'


  return tempRef.nodeId;
}
/**
 * Component access UI functions
 *
 * @param {ViewNode} ref - Element ref that have nodeId.
 * @param {string} funcName - function name.
 * @param {Array} options - function options.
 */


function callUIFunction(ref, funcName) {
  var componentName = ref.nativeName,
      nodeId = ref.nodeId;

  if (!nodeId || !componentName) {
    var targetElement = getElementFromFiberRef(ref);

    if (targetElement) {
      nodeId = targetElement.nodeId;
      componentName = targetElement.nativeName;
    }
  }

  if (!componentName) {
    throw new Error('callUIFunction is calling a unnamed component');
  }

  if (!nodeId) {
    throw new Error('callUIFunction is calling a component have no nodeId');
  }

  for (var _len = arguments.length, options = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {
    options[_key - 2] = arguments[_key];
  }

  var _options$ = options[0],
      paramList = _options$ === void 0 ? [] : _options$,
      callback = options[1];

  if (isFunction(paramList)) {
    callback = paramList;
    paramList = [];
  }

  var rootViewId = getRootViewId();

  if (rootViewId === null) {
    return;
  }

  if (Device$2.platform.OS === 'ios') {
    if (isFunction(callback) && Array.isArray(paramList)) {
      paramList.push(callback);
    }

    Bridge$2.callNative('UIManagerModule', 'callUIFunction', [componentName, nodeId, funcName, paramList]);
  } else if (Device$2.platform.OS === 'android') {
    if (isFunction(callback)) {
      Bridge$2.callNative('UIManagerModule', 'callUIFunction', [nodeId, funcName, paramList], callback);
    } else {
      Bridge$2.callNative('UIManagerModule', 'callUIFunction', [nodeId, funcName, paramList]);
    }
  }
}
/**
 * Get the ref position and size in the visible window.
 * > For the position and size in the layout, use onLayout event.
 *
 * @param {string} method
 * @param {Fiber | Element} ref - ref that need to measure.
 * @param {function} callback
 */


function measureInWindowByMethod(method, ref, callback) {
  var nodeId = getNodeIdByRef(ref);
  return new Promise(function (resolve, reject) {
    if (!nodeId) {
      if (callback && isFunction(callback)) {
        // Forward compatibility for old callback
        callback('this view is null');
      }

      return reject(new Error("".concat(method, " cannot get nodeId")));
    }

    return Bridge$2.callNative('UIManagerModule', method, nodeId, function (layout) {
      if (callback && isFunction(callback)) {
        callback(layout);
      }

      if (layout === 'this view is null') {
        return reject(new Error('Android cannot get the node'));
      }

      return resolve(layout);
    });
  });
}
/**
 * Get the ref position and size in the visible window.
 * > For the position and size in the layout, use onLayout event.
 * P.S. iOS can only obtains the layout of rootView container,
 * so measureInAppWindow method is recommended
 *
 * @deprecated
 * @param {Fiber | Element} ref - ref that need to measure.
 * @param {Function} callback
 */


function measureInWindow(ref, callback) {
  return measureInWindowByMethod('measureInWindow', ref, callback);
}
/**
 * Get the ref position and size in the App visible window.
 * > For the position and size in the layout, use onLayout event.
 *
 * @param {Fiber | Element} ref - ref that need to measure.
 * @param {Function} callback
 */


function measureInAppWindow(ref, callback) {
  if (Device$2.platform.OS === 'android') {
    return measureInWindowByMethod('measureInWindow', ref, callback);
  }

  return measureInWindowByMethod('measureInAppWindow', ref, callback);
}

var uiManagerModule = /*#__PURE__*/Object.freeze({
  __proto__: null,
  createNode: createNode,
  updateNode: updateNode,
  deleteNode: deleteNode,
  flushBatch: flushBatch,
  startBatch: startBatch,
  endBatch: endBatch,
  sendRenderError: sendRenderError,
  getNodeById: getNodeById,
  getNodeIdByRef: getNodeIdByRef,
  getElementFromFiberRef: getElementFromFiberRef,
  callUIFunction: callUIFunction,
  measureInWindow: measureInWindow,
  measureInAppWindow: measureInAppWindow
});
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var hippyEventEmitter = new HippyEventEmitter();
var backPressSubscriptions = new Set();
/**
 * Android hardware back button event listener.
 */

var realBackAndroid = {
  exitApp() {
    Bridge$2.callNative('DeviceEventModule', 'invokeDefaultBackPressHandler');
  },

  addListener(handler) {
    Bridge$2.callNative('DeviceEventModule', 'setListenBackPress', true);
    backPressSubscriptions.add(handler);
    return {
      remove() {
        realBackAndroid.removeListener(handler);
      }

    };
  },

  removeListener(handler) {
    backPressSubscriptions.delete(handler);

    if (backPressSubscriptions.size === 0) {
      Bridge$2.callNative('DeviceEventModule', 'setListenBackPress', false);
    }
  },

  initEventListener() {
    hippyEventEmitter.addListener('hardwareBackPress', function () {
      var invokeDefault = true;

      var subscriptions = _toConsumableArray(backPressSubscriptions).reverse();

      subscriptions.every(function (subscription) {
        if (typeof subscription === 'function' && subscription()) {
          invokeDefault = false;
          return false;
        }

        return true;
      });

      if (invokeDefault) {
        realBackAndroid.exitApp();
      }
    });
  }

};
/**
 * Fake BackAndroid for iOS
 */

var fakeBackAndroid = {
  exitApp() {},

  addListener(handler) {
    return {
      remove() {}

    };
  },

  removeListener(handler) {},

  initEventListener() {}

};

var BackAndroid$1 = function () {
  if ( false || Device$2.platform.OS === 'android') {
    realBackAndroid.initEventListener();
    return realBackAndroid;
  }

  return fakeBackAndroid;
}();

var isArray = Array.isArray;
var keyList = Object.keys;
var hasProp = Object.prototype.hasOwnProperty;

var fastDeepEqual = function equal(a, b) {
  if (a === b) return true;

  if (a && b && typeof a == 'object' && typeof b == 'object') {
    var arrA = isArray(a),
        arrB = isArray(b),
        i,
        length,
        key;

    if (arrA && arrB) {
      length = a.length;
      if (length != b.length) return false;

      for (i = length; i-- !== 0;) {
        if (!equal(a[i], b[i])) return false;
      }

      return true;
    }

    if (arrA != arrB) return false;
    var dateA = a instanceof Date,
        dateB = b instanceof Date;
    if (dateA != dateB) return false;
    if (dateA && dateB) return a.getTime() == b.getTime();
    var regexpA = a instanceof RegExp,
        regexpB = b instanceof RegExp;
    if (regexpA != regexpB) return false;
    if (regexpA && regexpB) return a.toString() == b.toString();
    var keys = keyList(a);
    length = keys.length;
    if (length !== keyList(b).length) return false;

    for (i = length; i-- !== 0;) {
      if (!hasProp.call(b, keys[i])) return false;
    }

    for (i = length; i-- !== 0;) {
      key = keys[i];
      if (!equal(a[key], b[key])) return false;
    }

    return true;
  }

  return a !== a && b !== b;
};
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


function appendChild(parent, child) {
  if (parent.childNodes.indexOf(child) >= 0) {
    parent.removeChild(child);
  }

  parent.appendChild(child);
}

function appendChildToContainer(container, child) {
  container.appendChild(child);
}

function appendInitialChild(parent, child) {
  parent.appendChild(child);
}

function commitMount() {}

function commitTextUpdate() {}

function commitUpdate(instance, updatePayload, type, oldProps, newProps, workInProgress) {
  preCacheFiberNode(workInProgress, instance.nodeId);
  var updatePayloadPropList = Object.keys(updatePayload);
  if (updatePayloadPropList.length === 0) return;
  updatePayloadPropList.forEach(function (propKey) {
    return instance.setAttribute(propKey, updatePayload[propKey]);
  });
} // this is the hook when commitMutationEffects begin


function commitMutationEffectsBegin() {// noop
} // this is the hook when commitMutationEffects finish


function commitMutationEffectsComplete() {
  endBatch$1(true);
}

function prepareUpdate(instance, type, oldProps, newProps) {
  var updatePayload = {};
  var hasFunctionProp = false;
  Object.keys(newProps).forEach(function (key) {
    var oldPropValue = oldProps[key];
    var newPropValue = newProps[key];

    switch (key) {
      case 'children':
        {
          if (oldPropValue !== newPropValue && (typeof newPropValue === 'number' || typeof newPropValue === 'string')) {
            updatePayload[key] = newPropValue;
          }

          break;
        }

      default:
        {
          if (typeof oldPropValue === 'function' && typeof newPropValue === 'function' && !fastDeepEqual(oldPropValue, newPropValue)) {
            hasFunctionProp = true;
          } else if (!fastDeepEqual(oldPropValue, newPropValue)) {
            updatePayload[key] = newPropValue;
          }
        }
    }
  });
  var isUpdatePayloadEmpty = Object.keys(updatePayload).length === 0;

  if (isUpdatePayloadEmpty && hasFunctionProp) {
    /**
     * if updatePayload only has function property,
     * return empty object to trigger commitUpdate for updating fiberNode Cache
     */
    return {};
  }

  if (isUpdatePayloadEmpty) {
    /**
     * prepareUpdate returning null would not trigger commitUpdate
     */
    return null;
  }

  return updatePayload;
}

function createContainerChildSet() {}

function createInstance(type, newProps, rootContainerInstance, currentHostContext, workInProgress) {
  var element = rootContainerInstance.createElement(type);
  Object.keys(newProps).forEach(function (attr) {
    switch (attr) {
      case 'children':
        // Ignore children attribute
        break;

      case 'nativeName':
        element.meta.component.name = newProps.nativeName;
        break;

      default:
        {
          element.setAttribute(attr, newProps[attr]);
        }
    }
  }); // only HostComponent (5) or Fragment (7) rendered to native

  if ([5, 7].indexOf(workInProgress.tag) < 0) {
    element.meta.skipAddToDom = true;
  }

  preCacheFiberNode(workInProgress, element.nodeId);
  return element;
}

function createTextInstance(newText, rootContainerInstance, hostContext, workInProgress) {
  var element = rootContainerInstance.createElement('p');
  element.setAttribute('text', unicodeToChar(newText));
  element.meta = {
    component: {
      name: 'Text'
    }
  };
  preCacheFiberNode(workInProgress, element.nodeId);
  return element;
}

function finalizeInitialChildren() {
  return true;
}

function finalizeContainerChildren() {}

function getPublicInstance(instance) {
  return instance;
}

function insertBefore(parent, child, beforeChild) {
  if (parent.childNodes.indexOf(child) >= 0) {
    // move it if the node has existed
    parent.moveChild(child, beforeChild);
  } else {
    parent.insertBefore(child, beforeChild);
  }
}

function prepareForCommit() {
  return null;
}

function replaceContainerChildren() {}

function removeChild(parent, child) {
  parent.removeChild(child);
  unCacheFiberNodeOnIdle(child);
}

function removeChildFromContainer(parent, child) {
  parent.removeChild(child);
  unCacheFiberNodeOnIdle(child);
}

function resetAfterCommit() {}

function resetTextContent() {}

function getRootHostContext() {
  return {};
}

function getChildHostContext() {
  return {};
}

function getCurrentEventPriority() {
  return 0b0000000000000000000000000010000;
}

function shouldDeprioritizeSubtree() {
  return true;
}

function shouldSetTextContent(type, nextProps) {
  if (nextProps && nextProps.nativeName === 'Text' || ['p', 'span'].indexOf(type) !== -1) {
    var children = nextProps.children;
    return typeof children === 'string' || typeof children === 'number';
  }

  return false;
}

function hideInstance(instance) {
  var updatePayload = {
    style: {
      display: 'none'
    }
  };
  Object.keys(updatePayload).forEach(function (attr) {
    return instance.setAttribute(attr, updatePayload[attr]);
  });
}

function hideTextInstance() {
  throw new Error('Not yet implemented.');
}

function unhideInstance(instance, props) {
  var updatePayload = _objectSpread(_objectSpread({}, props), {}, {
    style: _objectSpread(_objectSpread({}, props.style), {}, {
      display: 'flex'
    })
  });

  Object.keys(updatePayload).forEach(function (attr) {
    return instance.setAttribute(attr, updatePayload[attr]);
  });
}

function clearContainer() {// TODO Implement this in future
  // UIManager does not expose a "remove all" type method.
}

function unhideTextInstance() {
  throw new Error('Not yet implemented.');
}

function getFundamentalComponentInstance() {
  throw new Error('Not yet implemented.');
}

function mountFundamentalComponent() {
  throw new Error('Not yet implemented.');
}

function shouldUpdateFundamentalComponent() {
  throw new Error('Not yet implemented.');
}

function updateFundamentalComponent() {
  throw new Error('Not yet implemented.');
}

function unmountFundamentalComponent() {
  throw new Error('Not yet implemented.');
}

function getInstanceFromNode() {
  throw new Error('Not yet implemented.');
}

function isOpaqueHydratingObject() {
  throw new Error('Not yet implemented');
}

function makeOpaqueHydratingObject() {
  throw new Error('Not yet implemented.');
}

function makeClientId() {
  throw new Error('Not yet implemented');
}

function makeClientIdInDEV() {
  throw new Error('Not yet implemented');
}

function beforeActiveInstanceBlur() {// noop
}

function afterActiveInstanceBlur() {// noop
}

function preparePortalMount() {// noop
}

function detachDeletedInstance() {// noop
}

var scheduleTimeout = setTimeout;
var cancelTimeout = clearTimeout; // @ts-ignore

var noTimeout = -1;
var hostConfigs = /*#__PURE__*/Object.freeze({
  __proto__: null,
  commitMutationEffectsBegin: commitMutationEffectsBegin,
  commitMutationEffectsComplete: commitMutationEffectsComplete,
  getCurrentEventPriority: getCurrentEventPriority,
  scheduleTimeout: scheduleTimeout,
  cancelTimeout: cancelTimeout,
  noTimeout: noTimeout,
  afterActiveInstanceBlur: afterActiveInstanceBlur,
  appendChild: appendChild,
  appendChildToContainer: appendChildToContainer,
  appendInitialChild: appendInitialChild,
  beforeActiveInstanceBlur: beforeActiveInstanceBlur,
  commitMount: commitMount,
  commitTextUpdate: commitTextUpdate,
  commitUpdate: commitUpdate,
  clearContainer: clearContainer,
  createContainerChildSet: createContainerChildSet,
  createInstance: createInstance,
  createTextInstance: createTextInstance,
  detachDeletedInstance: detachDeletedInstance,
  finalizeContainerChildren: finalizeContainerChildren,
  finalizeInitialChildren: finalizeInitialChildren,
  getChildHostContext: getChildHostContext,
  getPublicInstance: getPublicInstance,
  getInstanceFromNode: getInstanceFromNode,
  getFundamentalComponentInstance: getFundamentalComponentInstance,
  getRootHostContext: getRootHostContext,
  hideInstance: hideInstance,
  hideTextInstance: hideTextInstance,
  insertBefore: insertBefore,
  isOpaqueHydratingObject: isOpaqueHydratingObject,
  makeClientId: makeClientId,
  makeClientIdInDEV: makeClientIdInDEV,
  makeOpaqueHydratingObject: makeOpaqueHydratingObject,
  mountFundamentalComponent: mountFundamentalComponent,
  prepareForCommit: prepareForCommit,
  preparePortalMount: preparePortalMount,
  prepareUpdate: prepareUpdate,
  replaceContainerChildren: replaceContainerChildren,
  removeChild: removeChild,
  removeChildFromContainer: removeChildFromContainer,
  resetAfterCommit: resetAfterCommit,
  resetTextContent: resetTextContent,
  unmountFundamentalComponent: unmountFundamentalComponent,
  updateFundamentalComponent: updateFundamentalComponent,
  unhideTextInstance: unhideTextInstance,
  unhideInstance: unhideInstance,
  shouldDeprioritizeSubtree: shouldDeprioritizeSubtree,
  shouldUpdateFundamentalComponent: shouldUpdateFundamentalComponent,
  shouldSetTextContent: shouldSetTextContent
});
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var hippyReconciler = react_reconciler__WEBPACK_IMPORTED_MODULE_0___default()(_objectSpread(_objectSpread({}, hostConfigs), {}, {
  // @ts-ignore compatible for React 16 and React latest
  clearTimeout,
  setTimeout,
  isPrimaryRenderer: true,
  noTimeout: -1,
  supportsMutation: true,
  supportsHydration: false,
  supportsPersistence: false,
  now: Date.now,
  scheduleDeferredCallback: function scheduleDeferredCallback() {},
  cancelDeferredCallback: function cancelDeferredCallback() {}
}));
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var flushSync$1 = hippyReconciler.flushSync;
var addEventListener = HippyGlobal.addEventListener,
    removeEventListener = HippyGlobal.removeEventListener,
    dispatchEvent = HippyGlobal.dispatchEvent,
    AsyncStorage$1 = HippyGlobal.AsyncStorage,
    Bridge$1 = HippyGlobal.Bridge,
    Device$1 = HippyGlobal.Device,
    HippyRegister$1 = HippyGlobal.HippyRegister;
var Native = /*#__PURE__*/Object.freeze({
  __proto__: null,
  addEventListener: addEventListener,
  removeEventListener: removeEventListener,
  dispatchEvent: dispatchEvent,
  AsyncStorage: AsyncStorage$1,
  BackAndroid: BackAndroid$1,
  Bridge: Bridge$1,
  Clipboard: clipboard,
  Cookie: cookieModule,
  Device: Device$1,
  HippyRegister: HippyRegister$1,
  ImageLoader: imageLoaderModule,
  NetworkInfo: networkInfo,
  UIManager: uiManagerModule,
  flushSync: flushSync$1
});
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

function call() {
  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
    args[_key2] = arguments[_key2];
  }

  return "\\(\\s*(".concat(args.join(')\\s*,\\s*('), ")\\s*\\)");
}

var colors = {
  transparent: 0x00000000,
  aliceblue: 0xf0f8ffff,
  antiquewhite: 0xfaebd7ff,
  aqua: 0x00ffffff,
  aquamarine: 0x7fffd4ff,
  azure: 0xf0ffffff,
  beige: 0xf5f5dcff,
  bisque: 0xffe4c4ff,
  black: 0x000000ff,
  blanchedalmond: 0xffebcdff,
  blue: 0x0000ffff,
  blueviolet: 0x8a2be2ff,
  brown: 0xa52a2aff,
  burlywood: 0xdeb887ff,
  burntsienna: 0xea7e5dff,
  cadetblue: 0x5f9ea0ff,
  chartreuse: 0x7fff00ff,
  chocolate: 0xd2691eff,
  coral: 0xff7f50ff,
  cornflowerblue: 0x6495edff,
  cornsilk: 0xfff8dcff,
  crimson: 0xdc143cff,
  cyan: 0x00ffffff,
  darkblue: 0x00008bff,
  darkcyan: 0x008b8bff,
  darkgoldenrod: 0xb8860bff,
  darkgray: 0xa9a9a9ff,
  darkgreen: 0x006400ff,
  darkgrey: 0xa9a9a9ff,
  darkkhaki: 0xbdb76bff,
  darkmagenta: 0x8b008bff,
  darkolivegreen: 0x556b2fff,
  darkorange: 0xff8c00ff,
  darkorchid: 0x9932ccff,
  darkred: 0x8b0000ff,
  darksalmon: 0xe9967aff,
  darkseagreen: 0x8fbc8fff,
  darkslateblue: 0x483d8bff,
  darkslategray: 0x2f4f4fff,
  darkslategrey: 0x2f4f4fff,
  darkturquoise: 0x00ced1ff,
  darkviolet: 0x9400d3ff,
  deeppink: 0xff1493ff,
  deepskyblue: 0x00bfffff,
  dimgray: 0x696969ff,
  dimgrey: 0x696969ff,
  dodgerblue: 0x1e90ffff,
  firebrick: 0xb22222ff,
  floralwhite: 0xfffaf0ff,
  forestgreen: 0x228b22ff,
  fuchsia: 0xff00ffff,
  gainsboro: 0xdcdcdcff,
  ghostwhite: 0xf8f8ffff,
  gold: 0xffd700ff,
  goldenrod: 0xdaa520ff,
  gray: 0x808080ff,
  green: 0x008000ff,
  greenyellow: 0xadff2fff,
  grey: 0x808080ff,
  honeydew: 0xf0fff0ff,
  hotpink: 0xff69b4ff,
  indianred: 0xcd5c5cff,
  indigo: 0x4b0082ff,
  ivory: 0xfffff0ff,
  khaki: 0xf0e68cff,
  lavender: 0xe6e6faff,
  lavenderblush: 0xfff0f5ff,
  lawngreen: 0x7cfc00ff,
  lemonchiffon: 0xfffacdff,
  lightblue: 0xadd8e6ff,
  lightcoral: 0xf08080ff,
  lightcyan: 0xe0ffffff,
  lightgoldenrodyellow: 0xfafad2ff,
  lightgray: 0xd3d3d3ff,
  lightgreen: 0x90ee90ff,
  lightgrey: 0xd3d3d3ff,
  lightpink: 0xffb6c1ff,
  lightsalmon: 0xffa07aff,
  lightseagreen: 0x20b2aaff,
  lightskyblue: 0x87cefaff,
  lightslategray: 0x778899ff,
  lightslategrey: 0x778899ff,
  lightsteelblue: 0xb0c4deff,
  lightyellow: 0xffffe0ff,
  lime: 0x00ff00ff,
  limegreen: 0x32cd32ff,
  linen: 0xfaf0e6ff,
  magenta: 0xff00ffff,
  maroon: 0x800000ff,
  mediumaquamarine: 0x66cdaaff,
  mediumblue: 0x0000cdff,
  mediumorchid: 0xba55d3ff,
  mediumpurple: 0x9370dbff,
  mediumseagreen: 0x3cb371ff,
  mediumslateblue: 0x7b68eeff,
  mediumspringgreen: 0x00fa9aff,
  mediumturquoise: 0x48d1ccff,
  mediumvioletred: 0xc71585ff,
  midnightblue: 0x191970ff,
  mintcream: 0xf5fffaff,
  mistyrose: 0xffe4e1ff,
  moccasin: 0xffe4b5ff,
  navajowhite: 0xffdeadff,
  navy: 0x000080ff,
  oldlace: 0xfdf5e6ff,
  olive: 0x808000ff,
  olivedrab: 0x6b8e23ff,
  orange: 0xffa500ff,
  orangered: 0xff4500ff,
  orchid: 0xda70d6ff,
  palegoldenrod: 0xeee8aaff,
  palegreen: 0x98fb98ff,
  paleturquoise: 0xafeeeeff,
  palevioletred: 0xdb7093ff,
  papayawhip: 0xffefd5ff,
  peachpuff: 0xffdab9ff,
  peru: 0xcd853fff,
  pink: 0xffc0cbff,
  plum: 0xdda0ddff,
  powderblue: 0xb0e0e6ff,
  purple: 0x800080ff,
  rebeccapurple: 0x663399ff,
  red: 0xff0000ff,
  rosybrown: 0xbc8f8fff,
  royalblue: 0x4169e1ff,
  saddlebrown: 0x8b4513ff,
  salmon: 0xfa8072ff,
  sandybrown: 0xf4a460ff,
  seagreen: 0x2e8b57ff,
  seashell: 0xfff5eeff,
  sienna: 0xa0522dff,
  silver: 0xc0c0c0ff,
  skyblue: 0x87ceebff,
  slateblue: 0x6a5acdff,
  slategray: 0x708090ff,
  slategrey: 0x708090ff,
  snow: 0xfffafaff,
  springgreen: 0x00ff7fff,
  steelblue: 0x4682b4ff,
  tan: 0xd2b48cff,
  teal: 0x008080ff,
  thistle: 0xd8bfd8ff,
  tomato: 0xff6347ff,
  turquoise: 0x40e0d0ff,
  violet: 0xee82eeff,
  wheat: 0xf5deb3ff,
  white: 0xffffffff,
  whitesmoke: 0xf5f5f5ff,
  yellow: 0xffff00ff,
  yellowgreen: 0x9acd32ff
};
var NUMBER = '[-+]?\\d*\\.?\\d+';
var PERCENTAGE = "".concat(NUMBER, "%");
var matchers = {
  rgb: new RegExp("rgb".concat(call(NUMBER, NUMBER, NUMBER))),
  rgba: new RegExp("rgba".concat(call(NUMBER, NUMBER, NUMBER, NUMBER))),
  hsl: new RegExp("hsl".concat(call(NUMBER, PERCENTAGE, PERCENTAGE))),
  hsla: new RegExp("hsla".concat(call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER))),
  hex3: /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
  hex4: /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
  hex6: /^#([0-9a-fA-F]{6})$/,
  hex8: /^#([0-9a-fA-F]{8})$/
};
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

function parse255(str) {
  var int = parseInt(str, 10);

  if (int < 0) {
    return 0;
  }

  if (int > 255) {
    return 255;
  }

  return int;
}

function parse1(str) {
  var num = parseFloat(str);

  if (num < 0) {
    return 0;
  }

  if (num > 1) {
    return 255;
  }

  return Math.round(num * 255);
}

function hue2rgb(p, q, tx) {
  var t = tx;

  if (t < 0) {
    t += 1;
  }

  if (t > 1) {
    t -= 1;
  }

  if (t < 1 / 6) {
    return p + (q - p) * 6 * t;
  }

  if (t < 1 / 2) {
    return q;
  }

  if (t < 2 / 3) {
    return p + (q - p) * (2 / 3 - t) * 6;
  }

  return p;
}

function hslToRgb(h, s, l) {
  var q = l < 0.5 ? l * (1 + s) : l + s - l * s;
  var p = 2 * l - q;
  var r = hue2rgb(p, q, h + 1 / 3);
  var g = hue2rgb(p, q, h);
  var b = hue2rgb(p, q, h - 1 / 3);
  return Math.round(r * 255) << 24 | Math.round(g * 255) << 16 | Math.round(b * 255) << 8;
}

function parse360(str) {
  var int = parseFloat(str);
  return (int % 360 + 360) % 360 / 360;
}

function parsePercentage(str) {
  var int = parseFloat(str);

  if (int < 0) {
    return 0;
  }

  if (int > 100) {
    return 1;
  }

  return int / 100;
}

function baseColor(color) {
  var match;

  if (typeof color === 'number') {
    if (color >>> 0 === color && color >= 0 && color <= 0xffffffff) {
      return color;
    }

    return null;
  }

  match = matchers.hex6.exec(color);

  if (Array.isArray(match)) {
    return parseInt("".concat(match[1], "ff"), 16) >>> 0;
  }

  if (Object.hasOwnProperty.call(colors, color)) {
    return colors[color];
  }

  match = matchers.rgb.exec(color);

  if (Array.isArray(match)) {
    return (parse255(match[1]) << 24 // r
    | parse255(match[2]) << 16 // g
    | parse255(match[3]) << 8 // b
    | 0x000000ff // a
    ) >>> 0;
  }

  match = matchers.rgba.exec(color);

  if (match) {
    return (parse255(match[1]) << 24 // r
    | parse255(match[2]) << 16 // g
    | parse255(match[3]) << 8 // b
    | parse1(match[4]) // a
    ) >>> 0;
  }

  match = matchers.hex3.exec(color);

  if (match) {
    return parseInt("".concat(match[1] + match[1] // r
    + match[2] + match[2] // g
    + match[3] + match[3] // b
    , "ff"), // a
    16) >>> 0;
  }

  match = matchers.hex8.exec(color);

  if (match) {
    return parseInt(match[1], 16) >>> 0;
  }

  match = matchers.hex4.exec(color);

  if (match) {
    return parseInt(match[1] + match[1] // r
    + match[2] + match[2] // g
    + match[3] + match[3] // b
    + match[4] + match[4], // a
    16) >>> 0;
  }

  match = matchers.hsl.exec(color);

  if (match) {
    return (hslToRgb(parse360(match[1]), // h
    parsePercentage(match[2]), // s
    parsePercentage(match[3])) | 0x000000ff // a
    ) >>> 0;
  }

  match = matchers.hsla.exec(color);

  if (match) {
    return (hslToRgb(parse360(match[1]), // h
    parsePercentage(match[2]), // s
    parsePercentage(match[3])) | parse1(match[4]) // a
    ) >>> 0;
  }

  return null;
}
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Parse the color value to integer that native understand.
 *
 * @param {string} color - The color value.
 * @param {object} options - Color options.
 */


function colorParse(color) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};

  if (Number.isInteger(color)) {
    return color;
  }

  var int32Color = baseColor(color);

  if (!options.platform) {
    /* eslint-disable-next-line no-param-reassign */
    options.platform = "ios" || false;
  }

  if (int32Color === null) {
    return 0;
  }

  int32Color = (int32Color << 24 | int32Color >>> 8) >>> 0;

  if (options.platform === 'android') {
    int32Color |= 0;
  }

  return int32Color;
}
/**
 * Parse the color values array to integer array that native understand.
 *
 * @param {string[]} colorArray The color values array.
 * @param {object} options Color options.
 */


function colorArrayParse(colorArray, options) {
  if (!Array.isArray(colorArray)) {
    warn('Input color value is not a array', colorArray);
    return [0];
  }

  return colorArray.map(function (color) {
    return colorParse(color, options);
  });
}
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var DocumentNode = /*#__PURE__*/function (_ViewNode2) {
  _inherits(DocumentNode, _ViewNode2);

  var _super2 = _createSuper(DocumentNode);

  function DocumentNode() {
    var _this12;

    _classCallCheck(this, DocumentNode);

    _this12 = _super2.call(this);
    _this12.documentElement = new ElementNode('document');
    return _this12;
  }

  _createClass(DocumentNode, [{
    key: "createElement",
    value: function createElement(tagName) {
      return new ElementNode(tagName);
    }
  }, {
    key: "createElementNS",
    value: function createElementNS(namespace, tagName) {
      return new ElementNode("".concat(namespace, ":").concat(tagName));
    }
  }]);

  return DocumentNode;
}(ViewNode);

DocumentNode.createElement = DocumentNode.prototype.createElement;
DocumentNode.createElementNS = DocumentNode.prototype.createElementNS;
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var createContainer = hippyReconciler.createContainer,
    updateContainer = hippyReconciler.updateContainer,
    getPublicRootInstance = hippyReconciler.getPublicRootInstance,
    injectIntoDevTools = hippyReconciler.injectIntoDevTools;
var componentName = ['%c[Hippy-React "2.13.7"]%c', 'color: #61dafb', 'color: auto'];

var HippyReact = /*#__PURE__*/function () {
  /**
   * Create new Hippy instance
   *
   * @param {Object} config - Hippy config.
   * @param {string} config.appName - The name of Hippy app.
   * @param {HippyReactConfig.entryPage} config.entryPage - The Entry page of Hippy app.
   * @param {function} config.callback - The callback after rendering.
   */
  function HippyReact(config) {
    _classCallCheck(this, HippyReact);

    if (!config.appName || !config.entryPage) {
      throw new TypeError('Invalid arguments');
    }

    this.config = config;
    this.regist = this.start; // Forward compatible alias

    this.render = this.render.bind(this); // Start Render

    var rootDocument = new DocumentNode();
    this.rootContainer = createContainer(rootDocument, 0, false, null);
  } // Native methods


  _createClass(HippyReact, [{
    key: "start",
    value:
    /**
     * Start hippy app execution.
     */
    function start() {
      HippyRegister$1.regist(this.config.appName, this.render);
    }
    /**
     * Native rendering callback
     * @param {Object} superProps - The props passed by native start the app.
     */

  }, {
    key: "render",
    value: function render(superProps) {
      var _this$config = this.config,
          appName = _this$config.appName,
          entryPage = _this$config.entryPage,
          _this$config$silent = _this$config.silent,
          silent = _this$config$silent === void 0 ? false : _this$config$silent,
          _this$config$bubbles = _this$config.bubbles,
          bubbles = _this$config$bubbles === void 0 ? false : _this$config$bubbles,
          _this$config$callback = _this$config.callback,
          callback = _this$config$callback === void 0 ? function () {} : _this$config$callback;
      var rootViewId = superProps.__instanceId__;
      trace.apply(void 0, componentName.concat(['Start', appName, 'with rootViewId', rootViewId, superProps]));

      if (false) {} // Update nodeId for container


      this.rootContainer.containerInfo.nodeId = rootViewId;

      if (silent) {
        setSilent(silent);
      }

      if (bubbles) {
        setBubbles(bubbles);
      } // Save the root container


      setRootContainer(rootViewId, this.rootContainer); // Render to screen.

      var rootElement = react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(entryPage, superProps);
      updateContainer(rootElement, this.rootContainer, null, callback);
      return getPublicRootInstance(this.rootContainer);
    }
  }], [{
    key: "Native",
    get: function get() {
      warn('HippyReact.Native interface is not stable yet. DO NOT USE IT');
      return Native;
    }
  }]);

  return HippyReact;
}(); // version


HippyReact.version = "2.13.7";
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var AppRegistry = {
  registerComponent(appName, entryPage) {
    var hippy = new HippyReact({
      appName,
      entryPage
    });
    hippy.start();
  }

};
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * The most fundamental component for building a UI, `View` is a container that supports layout
 * with flexbox, style, some touch handling, and accessibility controls. `View` maps directly to
 * the native view equivalent on whatever platform React Native is running on, whether that is
 * a `UIView`, `<div>`, `android.view`, etc.
 *
 * View is designed to be nested inside other views and can have 0 to many children of any type.
 * @noInheritDoc
 */

var View = /*#__PURE__*/function (_React$Component) {
  _inherits(View, _React$Component);

  var _super3 = _createSuper(View);

  function View() {
    var _this13;

    _classCallCheck(this, View);

    _this13 = _super3.apply(this, arguments);
    _this13.instance = null;
    return _this13;
  } // startRipple


  _createClass(View, [{
    key: "setPressed",
    value: function setPressed(pressed) {
      callUIFunction(this.instance, 'setPressed', [pressed]);
    } // setRippleSpot

  }, {
    key: "setHotspot",
    value: function setHotspot(x, y) {
      callUIFunction(this.instance, 'setHotspot', [x, y]);
    }
  }, {
    key: "render",
    value: function render() {
      var _this14 = this;

      var _this$props = this.props,
          collapsable = _this$props.collapsable,
          _this$props$style = _this$props.style,
          style = _this$props$style === void 0 ? {} : _this$props$style,
          nativeProps = _objectWithoutProperties(_this$props, _excluded2);

      var nativeStyle = style;
      var nativeBackgroundAndroid = nativeProps.nativeBackgroundAndroid;

      if (typeof collapsable === 'boolean') {
        nativeStyle.collapsable = collapsable;
      }

      if (typeof (nativeBackgroundAndroid === null || nativeBackgroundAndroid === void 0 ? void 0 : nativeBackgroundAndroid.color) !== 'undefined') {
        nativeBackgroundAndroid.color = colorParse(nativeBackgroundAndroid.color);
      }

      return react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("div", _objectSpread({
        ref: function ref(_ref2) {
          _this14.instance = _ref2;
        },
        nativeName: "View",
        // @ts-ignore
        style: nativeStyle
      }, nativeProps));
    }
  }]);

  return View;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * A React component for displaying text.
 *
 * `Text` doesn't support nesting.
 * @noInheritDoc
 */


function forwardRef(_ref3, // eslint-disable-next-line max-len
ref) {
  var style = _ref3.style,
      nativeProps = _objectWithoutProperties(_ref3, _excluded3);

  var nativeStyle = style; // Fill default color
  // Workaround for Android meet empty front color not render issue.

  if (style) {
    if (Array.isArray(style)) {
      if (style.filter(function (x) {
        return typeof x === 'object' && x;
      }).findIndex(function (s) {
        return s.color || s.colors;
      }) === -1) {
        nativeStyle[0].color = '#000';
      }
    } else if (typeof style === 'object') {
      if (style.color === undefined && style.colors === undefined) {
        nativeStyle.color = '#000';
      }
    }
  } // Important: Text must receive text props.


  nativeProps.text = '';

  if (typeof nativeProps.children === 'string') {
    nativeProps.text = unicodeToChar(nativeProps.children);
  } else if (typeof nativeProps.children === 'number') {
    nativeProps.text = unicodeToChar(nativeProps.children.toString());
  } else if (Array.isArray(nativeProps.children)) {
    var text = nativeProps.children.filter(function (t) {
      return typeof t === 'string' || typeof t === 'number';
    }).join(''); // FIXME: if Text is nested, all child components of this component need to be wrapped by Text

    if (text) {
      nativeProps.text = unicodeToChar(text);
      nativeProps.children = nativeProps.text;
    }
  }

  return (// @ts-ignore
    react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("p", _objectSpread({
      ref: ref,
      nativeName: "Text",
      style: nativeStyle
    }, nativeProps))
  );
}

forwardRef.displayName = 'Text';
var Text = react__WEBPACK_IMPORTED_MODULE_1___default.a.forwardRef(forwardRef);
Text.displayName = 'Text';
var TextComp = /*#__PURE__*/Object.freeze({
  __proto__: null,
  'default': Text
});
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * A React component for displaying different types of images, including network images,
 * static resources, temporary local images, and images from local disk, such as the camera roll.
 * @noInheritDoc
 */

var Image = /*#__PURE__*/function (_React$Component2) {
  _inherits(Image, _React$Component2);

  var _super4 = _createSuper(Image);

  function Image() {
    _classCallCheck(this, Image);

    return _super4.apply(this, arguments);
  }

  _createClass(Image, [{
    key: "render",
    value: function render() {
      var _this$props2 = this.props,
          children = _this$props2.children,
          style = _this$props2.style,
          imageStyle = _this$props2.imageStyle,
          imageRef = _this$props2.imageRef,
          source = _this$props2.source,
          sources = _this$props2.sources,
          src = _this$props2.src,
          srcs = _this$props2.srcs,
          tintColor = _this$props2.tintColor,
          tintColors = _this$props2.tintColors,
          nativeProps = _objectWithoutProperties(_this$props2, _excluded4); // Define the image source url array.


      var imageUrls = this.getImageUrls({
        src,
        srcs,
        source,
        sources
      }); // Set sources props by platform specification

      if (Device$1.platform.OS === 'ios') {
        if (imageUrls.length) {
          nativeProps.source = imageUrls.map(function (uri) {
            return {
              uri
            };
          });
        }
      } else if (Device$1.platform.OS === 'android') {
        if (imageUrls.length === 1) {
          var _imageUrls = _slicedToArray(imageUrls, 1);

          nativeProps.src = _imageUrls[0];
        } else if (imageUrls.length > 1) {
          nativeProps.srcs = imageUrls;
        }
      }
      /**
       * defaultSource prop
       */


      if (typeof nativeProps.defaultSource === 'string') {
        if (nativeProps.defaultSource.indexOf('data:image/') !== 0) {
          warn('[Image] defaultSource prop must be a local base64 image');
        }

        nativeProps.defaultSource = convertImgUrl(nativeProps.defaultSource);
      }
      /**
       * tintColor(s)
       */


      var nativeStyle = _objectSpread({}, style);

      this.handleTintColor(nativeStyle, tintColor, tintColors);
      nativeProps.style = nativeStyle;

      if (children) {
        return react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(View, {
          style: style
        }, react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("img", _objectSpread(_objectSpread({}, nativeProps), {}, {
          nativeName: "Image",
          alt: "",
          // @ts-ignore
          ref: imageRef,
          // @ts-ignore
          style: [{
            position: 'absolute',
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            width: style.width,
            height: style.height
          }, imageStyle]
        })), children);
      }

      return (// @ts-ignore
        react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("img", _objectSpread(_objectSpread({}, nativeProps), {}, {
          nativeName: "Image",
          alt: "",
          ref: imageRef
        }))
      );
    }
  }, {
    key: "getImageUrls",
    value: function getImageUrls(_ref4) {
      var src = _ref4.src,
          srcs = _ref4.srcs,
          source = _ref4.source,
          sources = _ref4.sources;
      var imageUrls = [];

      if (typeof src === 'string') {
        imageUrls.push(src);
      }

      if (Array.isArray(srcs)) {
        imageUrls = [].concat(_toConsumableArray(imageUrls), _toConsumableArray(srcs));
      }

      if (source) {
        if (typeof source === 'string') {
          imageUrls.push(source);
        } else if (typeof source === 'object' && source !== null) {
          var uri = source.uri;

          if (uri) {
            imageUrls.push(uri);
          }
        }
      }

      if (sources) {
        if (Array.isArray(sources)) {
          sources.forEach(function (imageSrc) {
            if (typeof imageSrc === 'string') {
              imageUrls.push(imageSrc);
            } else if (typeof imageSrc === 'object' && imageSrc !== null && imageSrc.uri) {
              imageUrls.push(imageSrc.uri);
            }
          });
        }
      }

      if (imageUrls.length) {
        imageUrls = imageUrls.map(function (url) {
          return convertImgUrl(url);
        });
      }

      return imageUrls;
    }
  }, {
    key: "handleTintColor",
    value: function handleTintColor(nativeStyle, tintColor, tintColors) {
      if (tintColor) {
        Object.assign(nativeStyle, {
          tintColor
        });
      }

      if (Array.isArray(tintColors)) {
        Object.assign(nativeStyle, {
          tintColors
        });
      }
    }
  }], [{
    key: "resizeMode",
    get: function get() {
      return {
        contain: 'contain',
        cover: 'cover',
        stretch: 'stretch',
        center: 'center',
        repeat: 'repeat' // iOS Only

      };
    }
  }, {
    key: "getSize",
    value: function getSize(url, success, failure) {
      if (typeof url !== 'string') {
        throw new TypeError('Image.getSize first argument must be a string url');
      }

      var size = _getSize(url);

      if (typeof success === 'function') {
        size.then(function (result) {
          return success(result.width, result.height);
        });
      }

      if (typeof failure === 'function') {
        size.catch(failure);
      } else {
        size.catch(function (err) {
          return warn("Failed to get size for image: ".concat(url), err);
        });
      }

      return size;
    }
  }]);

  return Image;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);

Image.prefetch = prefetch;
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var Animated = /*#__PURE__*/function () {
  function Animated() {
    _classCallCheck(this, Animated);

    this.Value = Animated.Value;
  }

  _createClass(Animated, null, [{
    key: "Value",
    value: function Value(val) {
      return val;
    }
  }, {
    key: "timing",
    value: function timing(value, config) {
      return new Animation({
        mode: 'timing',
        delay: 0,
        startValue: value,
        toValue: config.toValue,
        duration: config.duration,
        timingFunction: config.easing || 'linear'
      });
    }
  }]);

  return Animated;
}();

Animated.View = View;
Animated.Text = TextComp;
Animated.Image = Image;
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var Easing = {
  step0(n) {
    return n > 0 ? 1 : 0;
  },

  step1(n) {
    return n >= 1 ? 1 : 0;
  },

  linear() {
    return 'linear';
  },

  ease() {
    return 'ease';
  },

  quad(t) {
    return Math.pow(t, 2);
  },

  cubic(t) {
    return Math.pow(t, 3);
  },

  poly(n) {
    return function (t) {
      return Math.pow(t, n);
    };
  },

  sin(t) {
    return 1 - Math.cos(t * Math.PI / 2);
  },

  circle(t) {
    return 1 - Math.sqrt(1 - t * t);
  },

  exp(t) {
    return Math.pow(2, 10 * (t - 1));
  },

  elastic() {
    return 'elastic';
  },

  back() {
    var s = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1.70158;
    return function (t) {
      return t * t * ((s + 1) * t - s);
    };
  },

  bounce(t_) {
    var t = t_;

    if (t < 1 / 2.75) {
      return 7.5625 * t * t;
    }

    if (t < 2 / 2.75) {
      t -= 1.5 / 2.75;
      return 7.5625 * t * t + 0.75;
    }

    if (t < 2.5 / 2.75) {
      t -= 2.25 / 2.75;
      return 7.5625 * t * t + 0.9375;
    }

    t -= 2.625 / 2.75;
    return 7.5625 * t * t + 0.984375;
  },

  bezier() {
    return 'bezier';
  },

  in() {
    return 'ease-in';
  },

  out() {
    return 'ease-out';
  },

  inOut() {
    return 'ease-in-out';
  }

};
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

function ListViewItem(props) {
  return (// @ts-ignore
    react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("li", _objectSpread({
      nativeName: "ListViewItem"
    }, props))
  );
}
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var PullHeader = /*#__PURE__*/function (_React$Component3) {
  _inherits(PullHeader, _React$Component3);

  var _super5 = _createSuper(PullHeader);

  function PullHeader() {
    var _this15;

    _classCallCheck(this, PullHeader);

    _this15 = _super5.apply(this, arguments);
    _this15.instance = null;
    return _this15;
  }
  /**
   * Expand the PullView and display the content
   */


  _createClass(PullHeader, [{
    key: "expandPullHeader",
    value: function expandPullHeader() {
      callUIFunction(this.instance, 'expandPullHeader', []);
    }
    /**
     * Collapse the PullView and hide the content
     * @param {CollapsePullHeaderOptions} [options] - additional config for pull header
     */

  }, {
    key: "collapsePullHeader",
    value: function collapsePullHeader(options) {
      if (Device$1.platform.OS === 'android') {
        callUIFunction(this.instance, 'collapsePullHeader', [options]);
      } else {
        // iOS is not supported if param invalid, so create a new function name for compatibility
        if (typeof options !== 'undefined') {
          callUIFunction(this.instance, 'collapsePullHeaderWithOptions', [options]);
        } else {
          callUIFunction(this.instance, 'collapsePullHeader', []);
        }
      }
    }
  }, {
    key: "render",
    value: function render() {
      var _this16 = this;

      var _this$props3 = this.props,
          children = _this$props3.children,
          nativeProps = _objectWithoutProperties(_this$props3, _excluded5);

      return react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("div", _objectSpread({
        nativeName: "PullHeaderView",
        ref: function ref(_ref5) {
          _this16.instance = _ref5;
        }
      }, nativeProps), children);
    }
  }]);

  return PullHeader;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var PullFooter = /*#__PURE__*/function (_React$Component4) {
  _inherits(PullFooter, _React$Component4);

  var _super6 = _createSuper(PullFooter);

  function PullFooter() {
    var _this17;

    _classCallCheck(this, PullFooter);

    _this17 = _super6.apply(this, arguments);
    _this17.instance = null;
    return _this17;
  }
  /**
   * Expand the PullView and display the content
   */


  _createClass(PullFooter, [{
    key: "expandPullFooter",
    value: function expandPullFooter() {
      callUIFunction(this.instance, 'expandPullFooter', []);
    }
    /**
     * Collapse the PullView and hide the content
     */

  }, {
    key: "collapsePullFooter",
    value: function collapsePullFooter() {
      callUIFunction(this.instance, 'collapsePullFooter', []);
    }
  }, {
    key: "render",
    value: function render() {
      var _this18 = this;

      var _this$props4 = this.props,
          children = _this$props4.children,
          nativeProps = _objectWithoutProperties(_this$props4, _excluded6);

      return react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("div", _objectSpread({
        nativeName: "PullFooterView",
        ref: function ref(_ref6) {
          _this18.instance = _ref6;
        }
      }, nativeProps), children);
    }
  }]);

  return PullFooter;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);

PullFooter.defaultProps = {
  sticky: true
};
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var androidAttrMap = {
  onDisappear: 'onDisAppear'
};
var iosAttrMap = {
  onDisappear: 'onDisappear'
};
/**
 * Recyclable list for better performance, and lower memory usage.
 * @noInheritDoc
 */

var ListView = /*#__PURE__*/function (_React$Component5) {
  _inherits(ListView, _React$Component5);

  var _super7 = _createSuper(ListView);

  function ListView(props) {
    var _this19;

    _classCallCheck(this, ListView);

    _this19 = _super7.call(this, props);
    _this19.instance = null;
    _this19.pullHeader = null;
    _this19.pullFooter = null;
    _this19.handleInitialListReady = _this19.handleInitialListReady.bind(_assertThisInitialized(_this19));
    _this19.state = {
      initialListReady: false
    };
    return _this19;
  }
  /**
   * change key
   */


  _createClass(ListView, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      var getRowKey = this.props.getRowKey;

      if (!getRowKey) {
        warn('ListView needs getRowKey to specific the key of item');
      }
    }
    /**
     * Scrolls to a given index of item, either immediately, with a smooth animation.
     *
     * @param {number} xIndex - Scroll to horizon index X.
     * @param {number} yIndex - Scroll To vertical index Y.
     * @param {boolean} animated - With smooth animation.By default is true.
     */

  }, {
    key: "scrollToIndex",
    value: function scrollToIndex(xIndex, yIndex, animated) {
      if (typeof xIndex !== 'number' || typeof yIndex !== 'number' || typeof animated !== 'boolean') {
        return;
      }

      callUIFunction(this.instance, 'scrollToIndex', [xIndex, yIndex, animated]);
    }
    /**
     * Scrolls to a given x, y offset, either immediately, with a smooth animation.
     *
     * @param {number} xOffset - Scroll to horizon offset X.
     * @param {number} yOffset - Scroll To vertical offset Y.
     * @param {boolean} animated - With smooth animation.By default is true.
     */

  }, {
    key: "scrollToContentOffset",
    value: function scrollToContentOffset(xOffset, yOffset, animated) {
      if (typeof xOffset !== 'number' || typeof yOffset !== 'number' || typeof animated !== 'boolean') {
        return;
      }

      callUIFunction(this.instance, 'scrollToContentOffset', [xOffset, yOffset, animated]);
    }
    /**
     * Expand the PullHeaderView and display the content
     */

  }, {
    key: "expandPullHeader",
    value: function expandPullHeader() {
      if (this.pullHeader) {
        this.pullHeader.expandPullHeader();
      }
    }
    /**
     * Collapse the PullHeaderView and hide the content
     */

  }, {
    key: "collapsePullHeader",
    value: function collapsePullHeader(options) {
      if (this.pullHeader) {
        this.pullHeader.collapsePullHeader(options);
      }
    }
    /**
     * Expand the PullFooterView and display the content
     */

  }, {
    key: "expandPullFooter",
    value: function expandPullFooter() {
      if (this.pullFooter) {
        this.pullFooter.expandPullFooter();
      }
    }
    /**
     * Collapse the PullView and hide the content
     */

  }, {
    key: "collapsePullFooter",
    value: function collapsePullFooter() {
      if (this.pullFooter) {
        this.pullFooter.collapsePullFooter();
      }
    }
  }, {
    key: "render",
    value: function render() {
      var _this20 = this;

      var _this$props5 = this.props,
          children = _this$props5.children,
          style = _this$props5.style,
          renderRow = _this$props5.renderRow,
          renderPullHeader = _this$props5.renderPullHeader,
          renderPullFooter = _this$props5.renderPullFooter,
          getRowType = _this$props5.getRowType,
          getRowStyle = _this$props5.getRowStyle,
          getRowKey = _this$props5.getRowKey,
          dataSource = _this$props5.dataSource,
          initialListSize = _this$props5.initialListSize,
          rowShouldSticky = _this$props5.rowShouldSticky,
          onRowLayout = _this$props5.onRowLayout,
          onHeaderPulling = _this$props5.onHeaderPulling,
          onHeaderReleased = _this$props5.onHeaderReleased,
          onFooterPulling = _this$props5.onFooterPulling,
          onFooterReleased = _this$props5.onFooterReleased,
          onAppear = _this$props5.onAppear,
          onDisappear = _this$props5.onDisappear,
          onWillAppear = _this$props5.onWillAppear,
          onWillDisappear = _this$props5.onWillDisappear,
          nativeProps = _objectWithoutProperties(_this$props5, _excluded7);

      var itemList = [];

      if (typeof renderRow === 'function') {
        var initialListReady = this.state.initialListReady;
        var numberOfRows = this.props.numberOfRows;
        var pullHeader = this.getPullHeader(renderPullHeader, onHeaderPulling, onHeaderReleased);
        var pullFooter = this.getPullFooter(renderPullFooter, onFooterPulling, onFooterReleased);

        if (!numberOfRows && dataSource) {
          numberOfRows = dataSource.length;
        }

        if (!initialListReady) {
          numberOfRows = Math.min(numberOfRows, initialListSize || 10);
        }

        var _loop = function _loop(index) {
          var itemProps = {};
          var rowChildren = void 0;

          if (dataSource) {
            rowChildren = renderRow(dataSource[index], null, index);
          } else {
            rowChildren = renderRow(index);
          }

          _this20.handleRowProps(itemProps, index, {
            getRowKey,
            getRowStyle,
            getRowType,
            onRowLayout,
            rowShouldSticky
          });

          [{
            func: onAppear,
            name: 'onAppear'
          }, {
            func: onDisappear,
            name: 'onDisappear'
          }, {
            func: onWillAppear,
            name: 'onWillAppear'
          }, {
            func: onWillDisappear,
            name: 'onWillDisappear'
          }].forEach(function (_ref7) {
            var func = _ref7.func,
                name = _ref7.name;

            if (typeof func === 'function') {
              itemProps[ListView.convertName(name)] = function () {
                func(index);
              };
            }
          });

          if (rowChildren) {
            itemList.push(react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(ListViewItem, _objectSpread({}, itemProps), rowChildren));
          }
        };

        for (var index = 0; index < numberOfRows; index += 1) {
          _loop(index);
        }

        if (pullHeader) {
          itemList.unshift(pullHeader);
        }

        if (pullFooter) {
          itemList.push(pullFooter);
        }

        if (typeof rowShouldSticky === 'function') {
          Object.assign(nativeProps, {
            rowShouldSticky: true
          });
        }

        var appearEventList = [onAppear, onDisappear, onWillAppear, onWillDisappear];
        nativeProps.exposureEventEnabled = appearEventList.some(function (func) {
          return typeof func === 'function';
        });
        nativeProps.numberOfRows = itemList.length;
        nativeProps.initialListSize = initialListSize;
        nativeProps.style = _objectSpread({
          overflow: 'scroll'
        }, style);
      }

      if (!nativeProps.onLoadMore && nativeProps.onEndReached) {
        nativeProps.onLoadMore = nativeProps.onEndReached;
      }

      return (// @ts-ignore
        react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("ul", _objectSpread({
          ref: function ref(_ref8) {
            _this20.instance = _ref8;
          },
          nativeName: "ListView",
          initialListReady: this.handleInitialListReady
        }, nativeProps), itemList.length ? itemList : children)
      );
    }
  }, {
    key: "handleInitialListReady",
    value: function handleInitialListReady() {
      this.setState({
        initialListReady: true
      });
    }
  }, {
    key: "getPullHeader",
    value: function getPullHeader(renderPullHeader, onHeaderPulling, onHeaderReleased) {
      var _this21 = this;

      var pullHeader = null;

      if (typeof renderPullHeader === 'function') {
        pullHeader = react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(PullHeader, {
          key: 'pull-header',
          ref: function ref(_ref9) {
            _this21.pullHeader = _ref9;
          },
          onHeaderPulling: onHeaderPulling,
          onHeaderReleased: onHeaderReleased
        }, renderPullHeader());
      }

      return pullHeader;
    }
  }, {
    key: "getPullFooter",
    value: function getPullFooter(renderPullFooter, onFooterPulling, onFooterReleased) {
      var _this22 = this;

      var pullFooter = null;

      if (typeof renderPullFooter === 'function') {
        pullFooter = react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(PullFooter, {
          key: 'pull-footer',
          ref: function ref(_ref10) {
            _this22.pullFooter = _ref10;
          },
          onFooterPulling: onFooterPulling,
          onFooterReleased: onFooterReleased
        }, renderPullFooter());
      }

      return pullFooter;
    }
  }, {
    key: "handleRowProps",
    value: function handleRowProps(itemProps, index, _ref11) {
      var getRowKey = _ref11.getRowKey,
          getRowStyle = _ref11.getRowStyle,
          onRowLayout = _ref11.onRowLayout,
          getRowType = _ref11.getRowType,
          rowShouldSticky = _ref11.rowShouldSticky;

      if (typeof getRowKey === 'function') {
        itemProps.key = getRowKey(index);
      }

      if (typeof getRowStyle === 'function') {
        itemProps.style = getRowStyle(index);
      }

      if (typeof onRowLayout === 'function') {
        itemProps.onLayout = function (e) {
          onRowLayout(e, index);
        };
      }

      if (typeof getRowType === 'function') {
        var type = getRowType(index);

        if (!Number.isInteger(type)) {
          warn('getRowType must returns a number');
        }

        itemProps.type = type;
      }

      if (typeof rowShouldSticky === 'function') {
        itemProps.sticky = rowShouldSticky(index);
      }
    }
  }], [{
    key: "convertName",
    value: function convertName(functionName) {
      if (Device$1.platform.OS === 'android' && androidAttrMap[functionName]) {
        return androidAttrMap[functionName];
      }

      if (Device$1.platform.OS === 'ios' && iosAttrMap[functionName]) {
        return iosAttrMap[functionName];
      }

      return functionName;
    }
  }]);

  return ListView;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);

ListView.defaultProps = {
  numberOfRows: 0
}; // export {ListItemViewProps}

/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Simply to implement the drag down to refresh feature.
 *
 * @deprecated
 * @noInheritDoc
 */

var RefreshWrapper = /*#__PURE__*/function (_React$Component6) {
  _inherits(RefreshWrapper, _React$Component6);

  var _super8 = _createSuper(RefreshWrapper);

  function RefreshWrapper(props) {
    var _this23;

    _classCallCheck(this, RefreshWrapper);

    _this23 = _super8.call(this, props);
    _this23.instance = null;
    _this23.refreshComplected = _this23.refreshCompleted.bind(_assertThisInitialized(_this23));
    return _this23;
  }
  /**
   * Call native for start refresh.
   */


  _createClass(RefreshWrapper, [{
    key: "startRefresh",
    value: function startRefresh() {
      callUIFunction(this.instance, 'startRefresh', null);
    }
    /**
     * Call native that data is refreshed
     */

  }, {
    key: "refreshCompleted",
    value: function refreshCompleted() {
      callUIFunction(this.instance, 'refreshComplected', null);
    }
    /**
     * @ignore
     */

  }, {
    key: "render",
    value: function render() {
      var _this24 = this;

      var _this$props6 = this.props,
          children = _this$props6.children,
          nativeProps = _objectWithoutProperties(_this$props6, _excluded8);

      var style = {
        left: 0,
        right: 0,
        position: 'absolute'
      };
      return react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("div", _objectSpread({
        nativeName: "RefreshWrapper",
        ref: function ref(_ref12) {
          _this24.instance = _ref12;
        }
      }, nativeProps), react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("div", {
        nativeName: "RefreshWrapperItemView",
        style: style
      }, this.getRefresh()), children);
    }
  }, {
    key: "getRefresh",
    value: function getRefresh() {
      var getRefresh = this.props.getRefresh;

      if (typeof getRefresh === 'function') {
        return getRefresh() || null;
      }

      return null;
    }
  }]);

  return RefreshWrapper;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var Stack = /*#__PURE__*/function () {
  function Stack() {
    _classCallCheck(this, Stack);

    this.top = null;
    this.size = 0;
  }
  /**
   * Push into a new page/component.
   *
   * @param {Object} route - New router
   */


  _createClass(Stack, [{
    key: "push",
    value: function push(route) {
      this.top = {
        data: route,
        next: this.top
      };
      this.size += 1;
    }
    /**
     * Returns latest push router.
     */

  }, {
    key: "peek",
    value: function peek() {
      return this.top === null ? null : this.top.data;
    }
    /**
     * Return back to previous page.
     */

  }, {
    key: "pop",
    value: function pop() {
      if (this.top === null) {
        return null;
      }

      var out = this.top;
      this.top = this.top.next;

      if (this.size > 0) {
        this.size -= 1;
      }

      return out.data;
    }
    /**
     * Clear history stack
     */

  }, {
    key: "clear",
    value: function clear() {
      this.top = null;
      this.size = 0;
    }
    /**
     * Returns all of routes
     */

  }, {
    key: "displayAll",
    value: function displayAll() {
      var arr = [];

      if (this.top === null) {
        return arr;
      }

      var current = this.top;

      for (var i = 0, len = this.size; i < len; i += 1) {
        arr[i] = current.data;
        current = current.next;
      }

      return arr;
    }
  }]);

  return Stack;
}();
/**
 * Simply router component for switch in multiple Hippy page.
 * @noInheritDoc
 */


var Navigator = /*#__PURE__*/function (_React$Component7) {
  _inherits(Navigator, _React$Component7);

  var _super9 = _createSuper(Navigator);

  /**
   * @ignore
   */
  function Navigator(props) {
    var _this25;

    _classCallCheck(this, Navigator);

    _this25 = _super9.call(this, props);
    _this25.stack = new Stack();
    _this25.instance = null;
    _this25.routeList = {};
    var initialRoute = props.initialRoute;

    if (initialRoute === null || initialRoute === void 0 ? void 0 : initialRoute.component) {
      var hippy = new HippyReact({
        appName: initialRoute.routeName,
        entryPage: initialRoute.component
      });
      hippy.regist();
      _this25.routeList[initialRoute.routeName] = true;
    }

    _this25.handleAndroidBack = _this25.handleAndroidBack.bind(_assertThisInitialized(_this25));
    return _this25;
  }
  /**
   * @ignore
   */


  _createClass(Navigator, [{
    key: "componentWillMount",
    value: function componentWillMount() {
      if (Device$1.platform.OS === 'android') {
        this.backListener = BackAndroid$1.addListener(this.handleAndroidBack);
      }
    }
    /**
     * @ignore
     */

  }, {
    key: "componentDidMount",
    value: function componentDidMount() {
      var initialRoute = this.props.initialRoute;
      this.stack.push({
        routeName: initialRoute.routeName || '',
        component: initialRoute.component || '',
        initProps: initialRoute.initProps || ''
      });
    }
    /**
     * @ignore
     */

  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      if (this.backListener) {
        this.backListener.remove();
      }
    }
  }, {
    key: "getCurrentPage",
    value: function getCurrentPage() {
      return this.stack.peek();
    }
  }, {
    key: "handleAndroidBack",
    value: function handleAndroidBack() {
      if (this.stack.size > 1) {
        this.pop({
          animated: true
        });
      }
    }
    /**
     * Push into a new page/component.
     *
     * @param {Object} route - New router
     */

  }, {
    key: "push",
    value: function push(route) {
      if (route === null || route === void 0 ? void 0 : route.component) {
        if (!this.routeList[route.routeName]) {
          var hippy = new HippyReact({
            appName: route.routeName,
            entryPage: route.component
          });
          hippy.regist();
          this.routeList[route.routeName] = true;
        } // eslint-disable-next-line no-param-reassign


        delete route.component;
      }

      var routes = [route];
      this.stack.push(route);
      callUIFunction(this.instance, 'push', routes);
    }
    /**
     * Return back to previous page.
     */

  }, {
    key: "pop",
    value: function pop(option) {
      if (this.stack.size > 1) {
        var options = [option];
        this.stack.pop();
        callUIFunction(this.instance, 'pop', options);
      }
    }
    /**
     * Clear history stack
     */

  }, {
    key: "clear",
    value: function clear() {
      this.stack.clear();
    }
    /**
     * @ignore
     */

  }, {
    key: "render",
    value: function render() {
      var _this26 = this;

      var _this$props7 = this.props,
          _this$props7$initialR = _this$props7.initialRoute,
          component = _this$props7$initialR.component,
          otherInitialRoute = _objectWithoutProperties(_this$props7$initialR, _excluded9),
          nativeProps = _objectWithoutProperties(_this$props7, _excluded10);

      nativeProps.initialRoute = otherInitialRoute;
      return react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("div", _objectSpread({
        nativeName: "Navigator",
        ref: function ref(_ref13) {
          _this26.instance = _ref13;
        }
      }, nativeProps));
    }
  }]);

  return Navigator;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


function ViewPagerItem(props) {
  return react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("div", _objectSpread(_objectSpread({
    nativeName: "ViewPagerItem"
  }, props), {}, {
    style: {
      position: 'absolute',
      left: 0,
      top: 0,
      right: 0,
      bottom: 0,
      collapsable: false
    }
  }));
}
/**
 * Container that allows to flip left and right between child views.
 * Each child view of the ViewPage will be treated as a separate page
 * and will be stretched to fill the ViewPage.
 * @noInheritDoc
 */


var ViewPager = /*#__PURE__*/function (_React$Component8) {
  _inherits(ViewPager, _React$Component8);

  var _super10 = _createSuper(ViewPager);

  function ViewPager(props) {
    var _this27;

    _classCallCheck(this, ViewPager);

    _this27 = _super10.call(this, props);
    _this27.instance = null;
    _this27.setPage = _this27.setPage.bind(_assertThisInitialized(_this27));
    _this27.setPageWithoutAnimation = _this27.setPageWithoutAnimation.bind(_assertThisInitialized(_this27));
    _this27.onPageScrollStateChanged = _this27.onPageScrollStateChanged.bind(_assertThisInitialized(_this27));
    return _this27;
  }

  _createClass(ViewPager, [{
    key: "onPageScrollStateChanged",
    value: function onPageScrollStateChanged(params) {
      var onPageScrollStateChanged = this.props.onPageScrollStateChanged;

      if (onPageScrollStateChanged) {
        onPageScrollStateChanged(params.pageScrollState);
      }
    }
  }, {
    key: "setPage",
    value: function setPage(selectedPage) {
      if (typeof selectedPage !== 'number') {
        return;
      }

      callUIFunction(this.instance, 'setPage', [selectedPage]);
    }
  }, {
    key: "setPageWithoutAnimation",
    value: function setPageWithoutAnimation(selectedPage) {
      if (typeof selectedPage !== 'number') {
        return;
      }

      callUIFunction(this.instance, 'setPageWithoutAnimation', [selectedPage]);
    }
  }, {
    key: "render",
    value: function render() {
      var _this28 = this;

      var _this$props8 = this.props,
          children = _this$props8.children,
          onPageScrollStateChanged = _this$props8.onPageScrollStateChanged,
          nativeProps = _objectWithoutProperties(_this$props8, _excluded11);

      var mappedChildren = [];

      if (Array.isArray(children)) {
        mappedChildren = children.map(function (child) {
          var viewPageItemProps = {};

          if (typeof child.key === 'string') {
            viewPageItemProps.key = "viewPager_".concat(child.key);
          } // eslint-disable-next-line react/jsx-key


          return react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(ViewPagerItem, _objectSpread({}, viewPageItemProps), child);
        });
      } else {
        mappedChildren.push(react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(ViewPagerItem, null, children));
      }

      if (typeof onPageScrollStateChanged === 'function') {
        nativeProps.onPageScrollStateChanged = this.onPageScrollStateChanged;
      }

      return (// @ts-ignore
        react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("div", _objectSpread({
          nativeName: "ViewPager",
          ref: function ref(_ref14) {
            _this28.instance = _ref14;
          }
        }, nativeProps), mappedChildren)
      );
    }
  }]);

  return ViewPager;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * is right to left display
 * @returns {boolean}
 */


function isRTL() {
  var localization = Device$2.platform.Localization;

  if (localization) {
    return localization.direction === 1;
  }

  return false;
}
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var propsMap = {
  caretColor: 'caret-color'
};
/**
 * A foundational component for inputting text into the app via a keyboard. Props provide
 * configurability for several features, such as auto-correction, auto-capitalization,
 * placeholder text, and different keyboard types, such as a numeric keypad.
 * @noInheritDoc
 */

var TextInput = /*#__PURE__*/function (_React$Component9) {
  _inherits(TextInput, _React$Component9);

  var _super11 = _createSuper(TextInput);

  function TextInput(props) {
    var _this29;

    _classCallCheck(this, TextInput);

    _this29 = _super11.call(this, props);
    _this29.instance = null;
    _this29._lastNativeText = '';
    _this29.onChangeText = _this29.onChangeText.bind(_assertThisInitialized(_this29));
    _this29.onKeyboardWillShow = _this29.onKeyboardWillShow.bind(_assertThisInitialized(_this29));
    return _this29;
  }

  _createClass(TextInput, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      var _this$props9 = this.props,
          _lastNativeText = _this$props9.value,
          autoFocus = _this$props9.autoFocus;
      this._lastNativeText = _lastNativeText;

      if (autoFocus) {
        this.focus();
      }
    }
    /**
     * @ignore
     */

  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      this.blur();
    }
    /**
     * Get the content of `TextInput`.
     *
     * @returns {Promise<string>}
     */

  }, {
    key: "getValue",
    value: function getValue() {
      var _this30 = this;

      return new Promise(function (resolve) {
        callUIFunction(_this30.instance, 'getValue', function (res) {
          return resolve(res.text);
        });
      });
    }
    /**
     * Set the content of `TextInput`.
     *
     * @param {string} value - New content of TextInput
     * @returns {string}
     */

  }, {
    key: "setValue",
    value: function setValue(value) {
      callUIFunction(this.instance, 'setValue', [value]);
      return value;
    }
    /**
     * Make the `TextInput` focused.
     */

  }, {
    key: "focus",
    value: function focus() {
      callUIFunction(this.instance, 'focusTextInput', []);
    }
    /**
     * Make the `TextInput` blurred.
     */

  }, {
    key: "blur",
    value: function blur() {
      callUIFunction(this.instance, 'blurTextInput', []);
    }
    /**
     * Show input method selection dialog.
     * @deprecated
     */

  }, {
    key: "showInputMethod",
    value: function showInputMethod() {// noop
    }
    /**
     * Hide the input method selection dialog.
     * @deprecated
     */

  }, {
    key: "hideInputMethod",
    value: function hideInputMethod() {// noop
    }
    /**
     * Clear the content of `TextInput`
     */

  }, {
    key: "clear",
    value: function clear() {
      callUIFunction(this.instance, 'clear', []);
    }
    /**
     * @ignore
     */

  }, {
    key: "render",
    value: function render() {
      var _this31 = this;

      var nativeProps = _objectSpread({}, this.props);

      ['underlineColorAndroid', 'placeholderTextColor', 'placeholderTextColors', 'caretColor', 'caret-color'].forEach(function (originalProp) {
        var prop = originalProp;
        var value = _this31.props[originalProp];

        if (typeof _this31.props[originalProp] === 'string') {
          if (propsMap[originalProp]) {
            prop = propsMap[originalProp];
          }

          if (Array.isArray(nativeProps.style)) {
            nativeProps.style.push({
              [prop]: value
            });
          } else if (nativeProps.style && typeof nativeProps.style === 'object') {
            nativeProps.style[prop] = value;
          } else {
            nativeProps.style = {
              [prop]: value
            };
          }

          delete nativeProps[originalProp];
        }
      });

      if (isRTL()) {
        if (!nativeProps.style) {
          nativeProps.style = {
            textAlign: 'right'
          };
        } else if (typeof nativeProps.style === 'object' && !Array.isArray(nativeProps.style)) {
          if (!nativeProps.style.textAlign) {
            nativeProps.style.textAlign = 'right';
          }
        }
      }

      return (// @ts-ignore
        react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("div", _objectSpread(_objectSpread({
          nativeName: "TextInput"
        }, nativeProps), {}, {
          ref: function ref(_ref15) {
            _this31.instance = _ref15;
          },
          onChangeText: this.onChangeText,
          onKeyboardWillShow: this.onKeyboardWillShow
        }))
      );
    }
  }, {
    key: "onChangeText",
    value: function onChangeText(e) {
      var onChangeText = this.props.onChangeText;

      if (typeof onChangeText === 'function') {
        onChangeText(e.text);
      }
      /**
       *  calling `this.props.onChange` or `this.props.onChangeText`
       *  may clean up the input itself. Exits here.
       */


      if (!this.instance) return;
      this._lastNativeText = e.text;
    }
  }, {
    key: "onKeyboardWillShow",
    value: function onKeyboardWillShow(originEvt) {
      var onKeyboardWillShow = this.props.onKeyboardWillShow;
      var evt = originEvt;

      if (Device$1.platform.OS === 'android') {
        evt.keyboardHeight /= Device$1.screen.scale;
      }

      if (typeof onKeyboardWillShow === 'function') {
        onKeyboardWillShow(evt);
      }
    }
  }]);

  return TextInput;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var ratio = Device$1.window.scale;
/* eslint-disable-next-line import/no-mutable-exports */

var HAIRLINE_WIDTH = Math.round(0.4 * ratio) / ratio;

if (HAIRLINE_WIDTH === 0) {
  HAIRLINE_WIDTH = 1 / ratio;
}
/**
 * Create new Stylesheet
 * @param {object} styleObj - The style object
 */


function create(styleObj) {
  // TODO: validate the style key and value.
  // TODO: Convert the color and pixel unit at create.
  return styleObj;
}

var stylesheet = /*#__PURE__*/Object.freeze({
  __proto__: null,

  get hairlineWidth() {
    return HAIRLINE_WIDTH;
  },

  create: create
});
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var styles$1 = create({
  baseVertical: {
    flexGrow: 1,
    flexShrink: 1,
    flexDirection: 'column',
    overflow: 'scroll'
  },
  baseHorizontal: {
    flexGrow: 1,
    flexShrink: 1,
    flexDirection: 'row',
    overflow: 'scroll'
  },
  contentContainerVertical: {
    collapsable: false,
    flexDirection: 'column'
  },
  contentContainerHorizontal: {
    collapsable: false,
    flexDirection: 'row'
  }
});
/**
 * Scrollable View without recycle feature.
 *
 * If you need to implement a long list, use `ListView`.
 * @noInheritDoc
 */

var ScrollView = /*#__PURE__*/function (_React$Component10) {
  _inherits(ScrollView, _React$Component10);

  var _super12 = _createSuper(ScrollView);

  function ScrollView() {
    var _this32;

    _classCallCheck(this, ScrollView);

    _this32 = _super12.apply(this, arguments);
    _this32.instance = null;
    return _this32;
  }
  /**
   * Scrolls to a given x, y offset, either immediately, with a smooth animation.
   *
   * @param {number} x - Scroll to horizon position X.
   * @param {number} y - Scroll To veritical position Y.
   * @param {boolean} animated - With smooth animation.By default is true.
   */


  _createClass(ScrollView, [{
    key: "scrollTo",
    value: function scrollTo(x, y) {
      var animated = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
      var x_ = x;
      var y_ = y;
      var animated_ = animated;

      if (typeof x === 'number') {
        warn('`scrollTo(x, y, animated)` is deprecated, Use `scrollTo({x: 5, y: 5, animated: true})` instead.');
      } else if (typeof x === 'object' && x) {
        x_ = x.x;
        y_ = x.y;
        animated_ = x.animated;
      }

      x_ = x_ || 0;
      y_ = y_ || 0;
      animated_ = !!animated_;
      callUIFunction(this.instance, 'scrollTo', [x_, y_, animated_]);
    }
    /**
     * Scrolls to a given x, y offset, with specific duration of animation.
     *
     * @param {number} x - Scroll to horizon position X.
     * @param {number} y - Scroll To vertical position Y.
     * @param {number} duration - Duration of animation execution time, with ms unit.
     *                            By default is 1000ms.
     */

  }, {
    key: "scrollToWithDuration",
    value: function scrollToWithDuration() {
      var x = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
      var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
      var duration = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1000;
      callUIFunction(this.instance, 'scrollToWithOptions', [{
        x,
        y,
        duration
      }]);
    }
    /**
     * @ignore
     */

  }, {
    key: "render",
    value: function render() {
      var _this33 = this;

      var _this$props10 = this.props,
          horizontal = _this$props10.horizontal,
          contentContainerStyle = _this$props10.contentContainerStyle,
          children = _this$props10.children,
          style = _this$props10.style,
          onContentSizeChanged = _this$props10.onContentSizeChanged;
      var contentContainerStyle_ = [horizontal ? styles$1.contentContainerHorizontal : styles$1.contentContainerVertical, contentContainerStyle];
      var newStyle = horizontal ? Object.assign({}, styles$1.baseHorizontal, style) : Object.assign({}, styles$1.baseVertical, style);

      if (horizontal) {
        newStyle.flexDirection = isRTL() ? 'row-reverse' : 'row';
      }

      return (// @ts-ignore
        react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("div", _objectSpread(_objectSpread({
          nativeName: "ScrollView",
          ref: function ref(_ref16) {
            _this33.instance = _ref16;
          }
        }, this.props), {}, {
          // @ts-ignore
          style: newStyle
        }), react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(View, {
          style: contentContainerStyle_,
          // abcyun
          onLayout: function onLayout(evt) {
            if (onContentSizeChanged) {
              onContentSizeChanged({
                width: evt.width,
                height: evt.height
              });
            }
          }
        }, children))
      );
    }
  }]);

  return ScrollView;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var styles = create({
  modal: {
    position: 'absolute',
    collapsable: false
  },
  container: {
    position: 'absolute',
    left: 0,
    top: 0
  }
});
/**
 * The Modal component is a basic way to present content above an enclosing view.
 * @noInheritDoc
 */

var Modal = /*#__PURE__*/function (_React$Component11) {
  _inherits(Modal, _React$Component11);

  var _super13 = _createSuper(Modal);

  /**
   * @ignore
   */
  function Modal(props) {
    var _this34;

    _classCallCheck(this, Modal);

    _this34 = _super13.call(this, props);
    _this34.eventSubscription = null;
    return _this34;
  }
  /**
   * @ignore
   */


  _createClass(Modal, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      var _this35 = this;

      if (Device$1.platform.OS === 'ios') {
        this.eventSubscription = new HippyEventListener('modalDismissed');
        this.eventSubscription.addCallback(function (params) {
          var _this35$props = _this35.props,
              primaryKey = _this35$props.primaryKey,
              onDismiss = _this35$props.onDismiss;

          if (params.primaryKey === primaryKey && typeof onDismiss === 'function') {
            onDismiss();
          }
        });
      }
    }
    /**
     * @ignore
     */

  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      if (Device$1.platform.OS === 'ios') {
        if (this.eventSubscription) {
          this.eventSubscription.unregister();
        }
      }
    }
    /**
     * @ignore
     */

  }, {
    key: "render",
    value: function render() {
      var _this$props11 = this.props,
          children = _this$props11.children,
          visible = _this$props11.visible,
          transparent = _this$props11.transparent,
          animated = _this$props11.animated;
      var animationType = this.props.animationType;

      if (visible === false) {
        return null;
      }

      var containerStyles = {
        backgroundColor: transparent ? 'transparent' : 'white'
      };

      if (!animationType) {
        // manually setting default prop here to keep support for the deprecated 'animated' prop
        animationType = 'none';

        if (animated) {
          animationType = 'slide';
        }
      }

      return react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("div", _objectSpread({
        nativeName: "Modal",
        animationType: animationType,
        transparent: transparent,
        // @ts-ignore
        style: styles.modal
      }, this.props), react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(View, {
        style: [styles.container, containerStyles]
      }, children));
    }
  }]);

  return Modal;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);

Modal.defaultProps = {
  visible: true
};
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * @noInheritDoc
 */

var Focusable = /*#__PURE__*/function (_React$Component12) {
  _inherits(Focusable, _React$Component12);

  var _super14 = _createSuper(Focusable);

  /**
   * @ignore
   */
  function Focusable(props) {
    var _this36;

    _classCallCheck(this, Focusable);

    _this36 = _super14.call(this, props);
    var requestFocus = _this36.props.requestFocus;
    _this36.state = {
      isFocus: !!requestFocus
    };
    _this36.handleFocus = _this36.handleFocus.bind(_assertThisInitialized(_this36));
    return _this36;
  }
  /**
   * @ignore
   */


  _createClass(Focusable, [{
    key: "render",
    value: function render() {
      var _a, _b, _c;

      var _this$props12 = this.props,
          requestFocus = _this$props12.requestFocus,
          children = _this$props12.children,
          nextFocusDownId = _this$props12.nextFocusDownId,
          nextFocusUpId = _this$props12.nextFocusUpId,
          nextFocusLeftId = _this$props12.nextFocusLeftId,
          nextFocusRightId = _this$props12.nextFocusRightId,
          style = _this$props12.style,
          noFocusStyle = _this$props12.noFocusStyle,
          focusStyle = _this$props12.focusStyle,
          onClick = _this$props12.onClick;
      var isFocus = this.state.isFocus;
      var child = react__WEBPACK_IMPORTED_MODULE_1___default.a.Children.only(children);
      var type;

      if ((_b = (_a = child === null || child === void 0 ? void 0 : child.child) === null || _a === void 0 ? void 0 : _a.memoizedProps) === null || _b === void 0 ? void 0 : _b.nativeName) {
        type = child.child.memoizedProps.nativeName;
      } else if ((_c = child === null || child === void 0 ? void 0 : child.type) === null || _c === void 0 ? void 0 : _c.displayName) {
        type = child.type.displayName;
      }

      var nextFocusDown = nextFocusDownId && getNodeIdByRef(nextFocusDownId);
      var nextFocusUp = nextFocusUpId && getNodeIdByRef(nextFocusUpId);
      var nextFocusLeft = nextFocusLeftId && getNodeIdByRef(nextFocusLeftId);
      var nextFocusRight = nextFocusRightId && getNodeIdByRef(nextFocusRightId);
      var nativeStyle = style;

      if (type !== 'Text') {
        var childStyle = child.memoizedProps.style;
        nativeStyle = _objectSpread(_objectSpread({}, nativeStyle), childStyle);
      }

      Object.assign(nativeStyle, isFocus ? focusStyle : noFocusStyle);

      if (type === 'Text') {
        return (// @ts-ignore
          react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(View, {
            focusable: true,
            nextFocusDownId: nextFocusDown,
            nextFocusUpId: nextFocusUp,
            nextFocusLeftId: nextFocusLeft,
            nextFocusRightId: nextFocusRight,
            requestFocus: requestFocus,
            style: nativeStyle,
            onClick: onClick,
            onFocus: this.handleFocus
          }, child)
        );
      }

      var childProps = child.memoizedProps.children;
      return react__WEBPACK_IMPORTED_MODULE_1___default.a.cloneElement(child, {
        nextFocusDownId,
        nextFocusUpId,
        nextFocusLeftId,
        nextFocusRightId,
        requestFocus,
        onClick,
        focusable: true,
        children: childProps,
        style: nativeStyle,
        onFocus: this.handleFocus
      });
    }
  }, {
    key: "handleFocus",
    value: function handleFocus(e) {
      var userOnFocus = this.props.onFocus;

      if (typeof userOnFocus === 'function') {
        userOnFocus(e);
      }

      var isFocus = this.state.isFocus;

      if (isFocus !== e.focus) {
        this.setState({
          isFocus: e.focus
        });
      }
    }
  }]);

  return Focusable;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * System built-in WebView
 *
 * For iOS it uses WKWebView, for Android it uses Webkit built-in.
 */


function WebView(props) {
  return (// @ts-ignore
    react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("iframe", _objectSpread({
      title: "hippy",
      nativeName: "WebView"
    }, props))
  );
}
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


var WEB_SOCKET_MODULE_NAME = 'websocket';
var websocketEventHub;
/**
 * The WebSocket API is an advanced technology that makes it possible to open a two-way
 * interactive communication session between the user's browser and a server. With this API,
 * you can send messages to a server and receive event-driven responses without having to
 * poll the server for a reply.
 */

var WebSocket = /*#__PURE__*/function () {
  /**
   * Returns a newly created WebSocket object.
   *
   * @param {string} url - The URL to which to connect; this should be the URL to which the
   *                       WebSocket server will respond.
   * @param {string | string[]} [protocols] - Either a single protocol string or an array
   *                                          of protocol strings. These strings are used to
   *                                          indicate sub-protocols, so that a single server
   *                                          can implement multiple WebSocket sub-protocols
   *                                          (for example, you might want one server to be able
   *                                          to handle different types of interactions depending
   *                                          on the specified protocol).
   *                                          If you don't specify a protocol string, an empty
   *                                          string is assumed.
   * @param {Object} extrasHeaders - Http headers will append to connection.
   */
  function WebSocket(url, protocols, extrasHeaders) {
    var _this37 = this;

    _classCallCheck(this, WebSocket);

    this.protocol = '';
    this.onWebSocketEvent = this.onWebSocketEvent.bind(this);

    if (!websocketEventHub) {
      websocketEventHub = new HippyEventListener('hippyWebsocketEvents');
    }

    this.readyState = 0
    /* CONNECTING */
    ;
    this.webSocketCallbacks = {};

    if (!url || typeof url !== 'string') {
      throw new TypeError('Invalid WebSocket url');
    }

    var headers = _objectSpread({}, extrasHeaders);

    if (protocols !== undefined) {
      if (Array.isArray(protocols) && protocols.length > 0) {
        headers['Sec-WebSocket-Protocol'] = protocols.join(',');
      } else if (typeof protocols === 'string') {
        headers['Sec-WebSocket-Protocol'] = protocols;
      } else {
        throw new TypeError('Invalid WebSocket protocols');
      }
    }

    var params = {
      headers,
      url
    };
    this.url = url;
    this.webSocketCallbackId = websocketEventHub.addCallback(this.onWebSocketEvent);
    Bridge$2.callNativeWithPromise(WEB_SOCKET_MODULE_NAME, 'connect', params).then(function (resp) {
      if (!resp || resp.code !== 0 || typeof resp.id !== 'number') {
        warn('Fail to create websocket connection', resp);
        return;
      }

      _this37.webSocketId = resp.id;
    });
  }
  /**
   * Closes the WebSocket connection or connection attempt, if any.
   * If the connection is already CLOSED, this method does nothing.
   *
   * @param {number} [code] - A numeric value indicating the status code explaining
   *                          why the connection is being closed. If this parameter
   *                          is not specified, a default value of 1005 is assumed.
   *                          See the list of status codes of CloseEvent for permitted values.
   * @param {string} [reason] - A human-readable string explaining why the connection
   *                            is closing. This string must be no longer than 123 bytes
   *                            of UTF-8 text (not characters).
   */


  _createClass(WebSocket, [{
    key: "close",
    value: function close(code, reason) {
      if (this.readyState !== 1
      /* OPEN */
      ) {
        warn('WebSocket is not connected');
        return;
      }

      this.readyState = 2
      /* CLOSING */
      ;
      Bridge$2.callNative(WEB_SOCKET_MODULE_NAME, 'close', {
        id: this.webSocketId,
        code,
        reason
      });
    }
    /**
     * Enqueues the specified data to be transmitted to the server over the WebSocket connection.
     *
     * @param {string} data - The data to send to the server. Hippy supports string type only.
     */

  }, {
    key: "send",
    value: function send(data) {
      if (this.readyState !== 1
      /* OPEN */
      ) {
        warn('WebSocket is not connected');
        return;
      }

      if (typeof data !== 'string') {
        throw new TypeError("Unsupported websocket data type: ".concat(typeof data));
      }

      Bridge$2.callNative(WEB_SOCKET_MODULE_NAME, 'send', {
        id: this.webSocketId,
        data
      });
    }
    /**
     * Set an EventHandler that is called when the WebSocket connection's readyState changes to OPEN;
     */

  }, {
    key: "onopen",
    set: function set(callback) {
      this.webSocketCallbacks.onOpen = callback;
    }
    /**
     * Set an EventHandler that is called when the WebSocket connection's readyState
     * changes to CLOSED.
     */

  }, {
    key: "onclose",
    set: function set(callback) {
      this.webSocketCallbacks.onClose = callback;
    }
    /**
     * Set an EventHandler that is called when a message is received from the server.
     */

  }, {
    key: "onerror",
    set: function set(callback) {
      this.webSocketCallbacks.onError = callback;
    }
    /**
     * Set an event handler property is a function which gets called when an error
     * occurs on the WebSocket.
     */

  }, {
    key: "onmessage",
    set: function set(callback) {
      this.webSocketCallbacks.onMessage = callback;
    }
    /**
     * WebSocket events handler from Native.
     *
     * @param {Object} param - Native response.
     */

  }, {
    key: "onWebSocketEvent",
    value: function onWebSocketEvent(param) {
      if (typeof param !== 'object' || param.id !== this.webSocketId) {
        return;
      }

      var eventType = param.type;

      if (eventType === 'onOpen') {
        this.readyState = 1
        /* OPEN */
        ;
      } else if (eventType === 'onClose') {
        this.readyState = 3
        /* CLOSED */
        ;
        websocketEventHub.removeCallback(this.webSocketCallbackId);
      }

      var callback = this.webSocketCallbacks[eventType];

      if (typeof callback === 'function') {
        callback(param.data);
      }
    }
  }]);

  return WebSocket;
}();
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


function WaterfallViewItem(props) {
  return react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("li", _objectSpread({
    nativeName: 'WaterfallItem'
  }, props));
}
/**
 * Recyclable list for better performance, and lower memory usage.
 * @noInheritDoc
 */


var WaterfallView = /*#__PURE__*/function (_React$Component13) {
  _inherits(WaterfallView, _React$Component13);

  var _super15 = _createSuper(WaterfallView);

  /**
   * @constructor
   */
  function WaterfallView(props) {
    var _this38;

    _classCallCheck(this, WaterfallView);

    _this38 = _super15.call(this, props);
    _this38.instance = null;
    _this38.pullHeader = null;
    _this38.pullFooter = null;
    _this38.handleInitialListReady = _this38.handleInitialListReady.bind(_assertThisInitialized(_this38));
    return _this38;
  }
  /**
   * Scrolls to a given index of item, either immediately, with a smooth animation.
   *
   * @param {Object} scrollToIndex params
   * @param {number} scrollToIndex.index - Scroll to specific index.
   * @param {boolean} scrollToIndex.animated - With smooth animation. By default is true.
   */


  _createClass(WaterfallView, [{
    key: "scrollToIndex",
    value: function scrollToIndex(_ref17) {
      var _ref17$index = _ref17.index,
          index = _ref17$index === void 0 ? 0 : _ref17$index,
          _ref17$animated = _ref17.animated,
          animated = _ref17$animated === void 0 ? true : _ref17$animated;
      callUIFunction(this.instance, 'scrollToIndex', [index, index, animated]);
    }
    /**
     * Scrolls to a given x, y offset, either immediately, with a smooth animation.
     *
     * @param {Object} scrollToContentOffset params
     * @param {number} scrollToContentOffset.xOffset - Scroll to horizon offset X.
     * @param {number} scrollToContentOffset.yOffset - Scroll To vertical offset Y.
     * @param {boolean} scrollToContentOffset.animated - With smooth animation. By default is true.
     */

  }, {
    key: "scrollToContentOffset",
    value: function scrollToContentOffset(_ref18) {
      var _ref18$xOffset = _ref18.xOffset,
          xOffset = _ref18$xOffset === void 0 ? 0 : _ref18$xOffset,
          _ref18$yOffset = _ref18.yOffset,
          yOffset = _ref18$yOffset === void 0 ? 0 : _ref18$yOffset,
          _ref18$animated = _ref18.animated,
          animated = _ref18$animated === void 0 ? true : _ref18$animated;
      callUIFunction(this.instance, 'scrollToContentOffset', [xOffset, yOffset, animated]);
    } // Expand the PullHeaderView and display the content

  }, {
    key: "expandPullHeader",
    value: function expandPullHeader() {
      if (this.pullHeader) {
        this.pullHeader.expandPullHeader();
      }
    } // Collapse the PullHeaderView and hide the content

  }, {
    key: "collapsePullHeader",
    value: function collapsePullHeader(options) {
      if (this.pullHeader) {
        this.pullHeader.collapsePullHeader(options);
      }
    } // Expand the PullFooterView and display the content

  }, {
    key: "expandPullFooter",
    value: function expandPullFooter() {
      if (this.pullFooter) {
        this.pullFooter.expandPullFooter();
      }
    } // Collapse the PullView and hide the content

  }, {
    key: "collapsePullFooter",
    value: function collapsePullFooter() {
      if (this.pullFooter) {
        this.pullFooter.collapsePullFooter();
      }
    }
  }, {
    key: "render",
    value: function render() {
      var _this39 = this;

      var _this$props13 = this.props,
          _this$props13$style = _this$props13.style,
          style = _this$props13$style === void 0 ? {} : _this$props13$style,
          renderBanner = _this$props13.renderBanner,
          _this$props13$numberO = _this$props13.numberOfColumns,
          numberOfColumns = _this$props13$numberO === void 0 ? 2 : _this$props13$numberO,
          _this$props13$columnS = _this$props13.columnSpacing,
          columnSpacing = _this$props13$columnS === void 0 ? 0 : _this$props13$columnS,
          _this$props13$interIt = _this$props13.interItemSpacing,
          interItemSpacing = _this$props13$interIt === void 0 ? 0 : _this$props13$interIt,
          _this$props13$numberO2 = _this$props13.numberOfItems,
          numberOfItems = _this$props13$numberO2 === void 0 ? 0 : _this$props13$numberO2,
          _this$props13$preload = _this$props13.preloadItemNumber,
          preloadItemNumber = _this$props13$preload === void 0 ? 0 : _this$props13$preload,
          renderItem = _this$props13.renderItem,
          renderPullHeader = _this$props13.renderPullHeader,
          renderPullFooter = _this$props13.renderPullFooter,
          getItemType = _this$props13.getItemType,
          getItemKey = _this$props13.getItemKey,
          getItemStyle = _this$props13.getItemStyle,
          _this$props13$content = _this$props13.contentInset,
          contentInset = _this$props13$content === void 0 ? {
        top: 0,
        left: 0,
        bottom: 0,
        right: 0
      } : _this$props13$content,
          onItemLayout = _this$props13.onItemLayout,
          onHeaderPulling = _this$props13.onHeaderPulling,
          onHeaderReleased = _this$props13.onHeaderReleased,
          onFooterPulling = _this$props13.onFooterPulling,
          onFooterReleased = _this$props13.onFooterReleased,
          _this$props13$contain = _this$props13.containPullHeader,
          containPullHeader = _this$props13$contain === void 0 ? false : _this$props13$contain,
          _this$props13$contain2 = _this$props13.containPullFooter,
          containPullFooter = _this$props13$contain2 === void 0 ? false : _this$props13$contain2,
          _this$props13$contain3 = _this$props13.containBannerView,
          containBannerView = _this$props13$contain3 === void 0 ? false : _this$props13$contain3,
          otherNativeProps = _objectWithoutProperties(_this$props13, _excluded12);

      var nativeProps = _objectSpread(_objectSpread({}, otherNativeProps), {}, {
        style,
        numberOfColumns,
        columnSpacing,
        interItemSpacing,
        preloadItemNumber,
        contentInset,
        containPullHeader,
        containPullFooter,
        containBannerView
      });

      var itemList = [];

      if (typeof renderBanner === 'function') {
        var banner = renderBanner();

        if (banner) {
          itemList.push(react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(View, {
            key: "bannerView"
          }, react__WEBPACK_IMPORTED_MODULE_1___default.a.cloneElement(banner)));
          nativeProps.containBannerView = true;
        }
      }

      if (typeof renderItem === 'function') {
        var pullHeader = this.getPullHeader(renderPullHeader, onHeaderPulling, onHeaderReleased);
        var pullFooter = this.getPullFooter(renderPullFooter, onFooterPulling, onFooterReleased);

        for (var index = 0; index < numberOfItems; index += 1) {
          var itemProps = {};
          var rowChildren = renderItem(index) || null;
          this.handleRowProps(itemProps, index, {
            getItemKey,
            getItemStyle,
            getItemType,
            onItemLayout
          });

          if (rowChildren) {
            itemList.push( // @ts-ignore
            react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(WaterfallViewItem, _objectSpread({}, itemProps), rowChildren));
          }
        }

        if (pullHeader) {
          itemList.unshift(pullHeader);
          nativeProps.containPullHeader = true;
        }

        if (pullFooter) {
          itemList.push(pullFooter);
          nativeProps.containPullFooter = true;
        }

        nativeProps.style = _objectSpread({}, style);
      } else {
        warn('Waterfall attribute [renderItem] is not Function');
      }

      return (// @ts-ignore
        react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement("ul", _objectSpread({
          nativeName: 'WaterfallView',
          ref: function ref(_ref19) {
            return _this39.instance = _ref19;
          },
          initialListReady: this.handleInitialListReady.bind(this)
        }, nativeProps), itemList)
      );
    }
    /**
     * @ignore
     */

  }, {
    key: "componentDidMount",
    value: function componentDidMount() {
      var getItemKey = this.props.getItemKey;

      if (!getItemKey) {
        warn('ListView needs getRowKey to specific the key of item');
      }
    }
  }, {
    key: "handleRowProps",
    value: function handleRowProps(itemProps, index, _ref20) {
      var _this40 = this;

      var getItemKey = _ref20.getItemKey,
          getItemStyle = _ref20.getItemStyle,
          onItemLayout = _ref20.onItemLayout,
          getItemType = _ref20.getItemType;

      if (typeof getItemKey === 'function') {
        itemProps.key = getItemKey(index);
      }

      if (typeof getItemStyle === 'function') {
        itemProps.style = getItemStyle(index);
      }

      if (typeof onItemLayout === 'function') {
        itemProps.onLayout = function (e) {
          onItemLayout.call(_this40, e, index);
        };
      }

      if (typeof getItemType === 'function') {
        var type = getItemType(index);

        if (!Number.isInteger(type)) {
          warn('getRowType must return a number');
        }

        itemProps.type = type;
      }
    }
    /**
     *
     * @param renderPullHeader - PullHeader View
     * @param onHeaderPulling - Called when header is pulled
     * @param onHeaderReleased - Called when header is released
     * @private
     */

  }, {
    key: "getPullHeader",
    value: function getPullHeader(renderPullHeader, onHeaderPulling, onHeaderReleased) {
      var _this41 = this;

      var pullHeader = null;

      if (typeof renderPullHeader === 'function') {
        pullHeader = react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(PullHeader, {
          key: 'PullHeader',
          ref: function ref(_ref21) {
            _this41.pullHeader = _ref21;
          },
          onHeaderPulling: onHeaderPulling,
          onHeaderReleased: onHeaderReleased
        }, renderPullHeader());
      }

      return pullHeader;
    }
    /**
     *
     * @param renderPullFooter - PullHeader View
     * @param onFooterPulling - Called when footer is pulled
     * @param onFooterReleased - Called when footer is released
     * @private
     */

  }, {
    key: "getPullFooter",
    value: function getPullFooter(renderPullFooter, onFooterPulling, onFooterReleased) {
      var _this42 = this;

      var pullFooter = null;

      if (typeof renderPullFooter === 'function') {
        pullFooter = react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(PullFooter, {
          key: 'PullFooter',
          ref: function ref(_ref22) {
            _this42.pullFooter = _ref22;
          },
          onFooterPulling: onFooterPulling,
          onFooterReleased: onFooterReleased
        }, renderPullFooter());
      }

      return pullFooter;
    } // initialReady callback

  }, {
    key: "handleInitialListReady",
    value: function handleInitialListReady() {
      var onInitialListReady = this.props.onInitialListReady;

      if (typeof onInitialListReady === 'function') {
        onInitialListReady();
      }
    }
  }]);

  return WaterfallView;
}(react__WEBPACK_IMPORTED_MODULE_1___default.a.Component);
/*
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


global.WebSocket = WebSocket;
var AsyncStorage = Native.AsyncStorage,
    BackAndroid = Native.BackAndroid,
    Bridge = Native.Bridge,
    Clipboard = Native.Clipboard,
    NetworkModule = Native.Cookie,
    Device = Native.Device,
    HippyRegister = Native.HippyRegister,
    ImageLoaderModule = Native.ImageLoader,
    NetInfo = Native.NetworkInfo,
    UIManagerModule = Native.UIManager,
    flushSync = Native.flushSync;
var callNative = Bridge.callNative,
    callNativeWithPromise = Bridge.callNativeWithPromise,
    callNativeWithCallbackId = Bridge.callNativeWithCallbackId,
    removeNativeCallback = Bridge.removeNativeCallback;
var TimerModule = null; // @ts-ignore

var ConsoleModule = global.ConsoleModule || global.console;
var Platform = Device.platform;
var Hippy = HippyReact;
var RNfqb = HippyReact;
var ImageBackground = Image; // Forward compatibilities

var RNfqbRegister = HippyRegister;
var RNfqbEventEmitter = HippyEventEmitter;
var RNfqbEventListener = HippyEventListener;
var Dimensions = {
  get(name) {
    return Device[name];
  }

};
var PixelRatio = {
  get() {
    return Device.screen.scale;
  }

};

/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__("./node_modules/webpack/buildin/global.js")))

/***/ }),

/***/ "./third-party/react-reconciler/cjs/react-reconciler.production.min.js":
/***/ (function(module, exports, __webpack_require__) {

/* WEBPACK VAR INJECTION */(function(module) {/** @license React v0.26.2
 * react-reconciler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
module.exports = function $$$reconciler($$$hostConfig) {
  var exports = {};
  'use strict';

  var aa = __webpack_require__("./node_modules/object-assign/index.js"),
      ca = __webpack_require__("./node_modules/react/index.js"),
      m = __webpack_require__("./node_modules/scheduler/index.js");

  function q(a) {
    for (var b = "https://reactjs.org/docs/error-decoder.html?invariant=" + a, c = 1; c < arguments.length; c++) {
      b += "&args[]=" + encodeURIComponent(arguments[c]);
    }

    return "Minified React error #" + a + "; visit " + b + " for the full message or use the non-minified dev environment for full errors and additional helpful warnings.";
  }

  var da = ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,
      ea = 60103,
      fa = 60106,
      ha = 60107,
      ia = 60108,
      ja = 60114,
      ka = 60109,
      la = 60110,
      ma = 60112,
      na = 60113,
      oa = 60120,
      pa = 60115,
      qa = 60116,
      ra = 60121,
      sa = 60129,
      ta = 60130,
      ua = 60131;

  if ("function" === typeof Symbol && Symbol.for) {
    var r = Symbol.for;
    ea = r("react.element");
    fa = r("react.portal");
    ha = r("react.fragment");
    ia = r("react.strict_mode");
    ja = r("react.profiler");
    ka = r("react.provider");
    la = r("react.context");
    ma = r("react.forward_ref");
    na = r("react.suspense");
    oa = r("react.suspense_list");
    pa = r("react.memo");
    qa = r("react.lazy");
    ra = r("react.block");
    r("react.scope");
    sa = r("react.debug_trace_mode");
    ta = r("react.offscreen");
    ua = r("react.legacy_hidden");
  }

  var va = "function" === typeof Symbol && Symbol.iterator;

  function wa(a) {
    if (null === a || "object" !== typeof a) return null;
    a = va && a[va] || a["@@iterator"];
    return "function" === typeof a ? a : null;
  }

  function xa(a) {
    if (null == a) return null;
    if ("function" === typeof a) return a.displayName || a.name || null;
    if ("string" === typeof a) return a;

    switch (a) {
      case ha:
        return "Fragment";

      case fa:
        return "Portal";

      case ja:
        return "Profiler";

      case ia:
        return "StrictMode";

      case na:
        return "Suspense";

      case oa:
        return "SuspenseList";
    }

    if ("object" === typeof a) switch (a.$$typeof) {
      case la:
        return (a.displayName || "Context") + ".Consumer";

      case ka:
        return (a._context.displayName || "Context") + ".Provider";

      case ma:
        var b = a.render;
        b = b.displayName || b.name || "";
        return a.displayName || ("" !== b ? "ForwardRef(" + b + ")" : "ForwardRef");

      case pa:
        return xa(a.type);

      case ra:
        return xa(a._render);

      case qa:
        b = a._payload;
        a = a._init;

        try {
          return xa(a(b));
        } catch (c) {}

    }
    return null;
  }

  function ya(a) {
    var b = a,
        c = a;
    if (a.alternate) for (; b.return;) {
      b = b.return;
    } else {
      a = b;

      do {
        b = a, 0 !== (b.flags & 1026) && (c = b.return), a = b.return;
      } while (a);
    }
    return 3 === b.tag ? c : null;
  }

  function za(a) {
    if (ya(a) !== a) throw Error(q(188));
  }

  function Aa(a) {
    var b = a.alternate;

    if (!b) {
      b = ya(a);
      if (null === b) throw Error(q(188));
      return b !== a ? null : a;
    }

    for (var c = a, d = b;;) {
      var e = c.return;
      if (null === e) break;
      var f = e.alternate;

      if (null === f) {
        d = e.return;

        if (null !== d) {
          c = d;
          continue;
        }

        break;
      }

      if (e.child === f.child) {
        for (f = e.child; f;) {
          if (f === c) return za(e), a;
          if (f === d) return za(e), b;
          f = f.sibling;
        }

        throw Error(q(188));
      }

      if (c.return !== d.return) c = e, d = f;else {
        for (var g = !1, h = e.child; h;) {
          if (h === c) {
            g = !0;
            c = e;
            d = f;
            break;
          }

          if (h === d) {
            g = !0;
            d = e;
            c = f;
            break;
          }

          h = h.sibling;
        }

        if (!g) {
          for (h = f.child; h;) {
            if (h === c) {
              g = !0;
              c = f;
              d = e;
              break;
            }

            if (h === d) {
              g = !0;
              d = f;
              c = e;
              break;
            }

            h = h.sibling;
          }

          if (!g) throw Error(q(189));
        }
      }
      if (c.alternate !== d) throw Error(q(190));
    }

    if (3 !== c.tag) throw Error(q(188));
    return c.stateNode.current === c ? a : b;
  }

  function Ba(a) {
    a = Aa(a);
    if (!a) return null;

    for (var b = a;;) {
      if (5 === b.tag || 6 === b.tag) return b;
      if (b.child) b.child.return = b, b = b.child;else {
        if (b === a) break;

        for (; !b.sibling;) {
          if (!b.return || b.return === a) return null;
          b = b.return;
        }

        b.sibling.return = b.return;
        b = b.sibling;
      }
    }

    return null;
  }

  function Ca(a) {
    a = Aa(a);
    if (!a) return null;

    for (var b = a;;) {
      if (5 === b.tag || 6 === b.tag) return b;
      if (b.child && 4 !== b.tag) b.child.return = b, b = b.child;else {
        if (b === a) break;

        for (; !b.sibling;) {
          if (!b.return || b.return === a) return null;
          b = b.return;
        }

        b.sibling.return = b.return;
        b = b.sibling;
      }
    }

    return null;
  }

  function Da(a, b) {
    for (var c = a.alternate; null !== b;) {
      if (b === a || b === c) return !0;
      b = b.return;
    }

    return !1;
  }

  var Ea = $$$hostConfig.getPublicInstance,
      Fa = $$$hostConfig.getRootHostContext,
      Ga = $$$hostConfig.getChildHostContext,
      Ia = $$$hostConfig.prepareForCommit,
      Ja = $$$hostConfig.resetAfterCommit,
      Ka = $$$hostConfig.createInstance,
      La = $$$hostConfig.appendInitialChild,
      Ma = $$$hostConfig.finalizeInitialChildren,
      Na = $$$hostConfig.prepareUpdate,
      Oa = $$$hostConfig.shouldSetTextContent,
      Pa = $$$hostConfig.createTextInstance,
      Qa = $$$hostConfig.scheduleTimeout,
      Ra = $$$hostConfig.cancelTimeout,
      Sa = $$$hostConfig.noTimeout,
      Ta = $$$hostConfig.isPrimaryRenderer,
      Ua = $$$hostConfig.supportsMutation,
      Va = $$$hostConfig.supportsPersistence,
      A = $$$hostConfig.supportsHydration,
      Wa = $$$hostConfig.getInstanceFromNode,
      Xa = $$$hostConfig.makeOpaqueHydratingObject,
      Ya = $$$hostConfig.makeClientId,
      Za = $$$hostConfig.beforeActiveInstanceBlur,
      $a = $$$hostConfig.afterActiveInstanceBlur,
      ab = $$$hostConfig.preparePortalMount,
      bb = $$$hostConfig.supportsTestSelectors,
      cb = $$$hostConfig.findFiberRoot,
      db = $$$hostConfig.getBoundingRect,
      eb = $$$hostConfig.getTextContent,
      fb = $$$hostConfig.isHiddenSubtree,
      gb = $$$hostConfig.matchAccessibilityRole,
      hb = $$$hostConfig.setFocusIfFocusable,
      ib = $$$hostConfig.setupIntersectionObserver,
      jb = $$$hostConfig.appendChild,
      kb = $$$hostConfig.appendChildToContainer,
      lb = $$$hostConfig.commitTextUpdate,
      mb = $$$hostConfig.commitMount,
      nb = $$$hostConfig.commitUpdate,
      ob = $$$hostConfig.insertBefore,
      pb = $$$hostConfig.insertInContainerBefore,
      qb = $$$hostConfig.removeChild,
      rb = $$$hostConfig.removeChildFromContainer,
      sb = $$$hostConfig.resetTextContent,
      tb = $$$hostConfig.hideInstance,
      ub = $$$hostConfig.hideTextInstance,
      vb = $$$hostConfig.unhideInstance,
      wb = $$$hostConfig.unhideTextInstance,
      xb = $$$hostConfig.clearContainer,
      yb = $$$hostConfig.commitMutationEffectsBegin,
      zb = $$$hostConfig.commitMutationEffectsComplete,
      Ab = $$$hostConfig.cloneInstance,
      Bb = $$$hostConfig.createContainerChildSet,
      Cb = $$$hostConfig.appendChildToContainerChildSet,
      Db = $$$hostConfig.finalizeContainerChildren,
      Eb = $$$hostConfig.replaceContainerChildren,
      Fb = $$$hostConfig.cloneHiddenInstance,
      Gb = $$$hostConfig.cloneHiddenTextInstance,
      Hb = $$$hostConfig.canHydrateInstance,
      Ib = $$$hostConfig.canHydrateTextInstance,
      Jb = $$$hostConfig.canHydrateSuspenseInstance,
      Kb = $$$hostConfig.isSuspenseInstancePending,
      Lb = $$$hostConfig.isSuspenseInstanceFallback,
      Mb = $$$hostConfig.registerSuspenseInstanceRetry,
      Nb = $$$hostConfig.getNextHydratableSibling,
      Ob = $$$hostConfig.getFirstHydratableChild,
      Pb = $$$hostConfig.hydrateInstance,
      Qb = $$$hostConfig.hydrateTextInstance,
      Rb = $$$hostConfig.hydrateSuspenseInstance,
      Sb = $$$hostConfig.getNextHydratableInstanceAfterSuspenseInstance,
      Tb = $$$hostConfig.commitHydratedContainer,
      Ub = $$$hostConfig.commitHydratedSuspenseInstance,
      Vb = $$$hostConfig.clearSuspenseBoundary,
      Wb = $$$hostConfig.clearSuspenseBoundaryFromContainer,
      Xb;

  function Yb(a) {
    if (void 0 === Xb) try {
      throw Error();
    } catch (c) {
      var b = c.stack.trim().match(/\n( *(at )?)/);
      Xb = b && b[1] || "";
    }
    return "\n" + Xb + a;
  }

  var Zb = !1;

  function $b(a, b) {
    if (!a || Zb) return "";
    Zb = !0;
    var c = Error.prepareStackTrace;
    Error.prepareStackTrace = void 0;

    try {
      if (b) {
        if (b = function b() {
          throw Error();
        }, Object.defineProperty(b.prototype, "props", {
          set: function set() {
            throw Error();
          }
        }), "object" === typeof Reflect && Reflect.construct) {
          try {
            Reflect.construct(b, []);
          } catch (k) {
            var d = k;
          }

          Reflect.construct(a, [], b);
        } else {
          try {
            b.call();
          } catch (k) {
            d = k;
          }

          a.call(b.prototype);
        }
      } else {
        try {
          throw Error();
        } catch (k) {
          d = k;
        }

        a();
      }
    } catch (k) {
      if (k && d && "string" === typeof k.stack) {
        for (var e = k.stack.split("\n"), f = d.stack.split("\n"), g = e.length - 1, h = f.length - 1; 1 <= g && 0 <= h && e[g] !== f[h];) {
          h--;
        }

        for (; 1 <= g && 0 <= h; g--, h--) {
          if (e[g] !== f[h]) {
            if (1 !== g || 1 !== h) {
              do {
                if (g--, h--, 0 > h || e[g] !== f[h]) return "\n" + e[g].replace(" at new ", " at ");
              } while (1 <= g && 0 <= h);
            }

            break;
          }
        }
      }
    } finally {
      Zb = !1, Error.prepareStackTrace = c;
    }

    return (a = a ? a.displayName || a.name : "") ? Yb(a) : "";
  }

  var ac = [],
      bc = -1;

  function cc(a) {
    return {
      current: a
    };
  }

  function B(a) {
    0 > bc || (a.current = ac[bc], ac[bc] = null, bc--);
  }

  function C(a, b) {
    bc++;
    ac[bc] = a.current;
    a.current = b;
  }

  var dc = {},
      D = cc(dc),
      E = cc(!1),
      ec = dc;

  function fc(a, b) {
    var c = a.type.contextTypes;
    if (!c) return dc;
    var d = a.stateNode;
    if (d && d.__reactInternalMemoizedUnmaskedChildContext === b) return d.__reactInternalMemoizedMaskedChildContext;
    var e = {},
        f;

    for (f in c) {
      e[f] = b[f];
    }

    d && (a = a.stateNode, a.__reactInternalMemoizedUnmaskedChildContext = b, a.__reactInternalMemoizedMaskedChildContext = e);
    return e;
  }

  function F(a) {
    a = a.childContextTypes;
    return null !== a && void 0 !== a;
  }

  function gc() {
    B(E);
    B(D);
  }

  function hc(a, b, c) {
    if (D.current !== dc) throw Error(q(168));
    C(D, b);
    C(E, c);
  }

  function ic(a, b, c) {
    var d = a.stateNode;
    a = b.childContextTypes;
    if ("function" !== typeof d.getChildContext) return c;
    d = d.getChildContext();

    for (var e in d) {
      if (!(e in a)) throw Error(q(108, xa(b) || "Unknown", e));
    }

    return aa({}, c, d);
  }

  function jc(a) {
    a = (a = a.stateNode) && a.__reactInternalMemoizedMergedChildContext || dc;
    ec = D.current;
    C(D, a);
    C(E, E.current);
    return !0;
  }

  function kc(a, b, c) {
    var d = a.stateNode;
    if (!d) throw Error(q(169));
    c ? (a = ic(a, b, ec), d.__reactInternalMemoizedMergedChildContext = a, B(E), B(D), C(D, a)) : B(E);
    C(E, c);
  }

  var lc = null,
      mc = null,
      nc = m.unstable_now;
  nc();
  var oc = 0,
      G = 8;

  function pc(a) {
    if (0 !== (1 & a)) return G = 15, 1;
    if (0 !== (2 & a)) return G = 14, 2;
    if (0 !== (4 & a)) return G = 13, 4;
    var b = 24 & a;
    if (0 !== b) return G = 12, b;
    if (0 !== (a & 32)) return G = 11, 32;
    b = 192 & a;
    if (0 !== b) return G = 10, b;
    if (0 !== (a & 256)) return G = 9, 256;
    b = 3584 & a;
    if (0 !== b) return G = 8, b;
    if (0 !== (a & 4096)) return G = 7, 4096;
    b = 4186112 & a;
    if (0 !== b) return G = 6, b;
    b = 62914560 & a;
    if (0 !== b) return G = 5, b;
    if (a & 67108864) return G = 4, 67108864;
    if (0 !== (a & 134217728)) return G = 3, 134217728;
    b = 805306368 & a;
    if (0 !== b) return G = 2, b;
    if (0 !== (1073741824 & a)) return G = 1, 1073741824;
    G = 8;
    return a;
  }

  function qc(a) {
    switch (a) {
      case 99:
        return 15;

      case 98:
        return 10;

      case 97:
      case 96:
        return 8;

      case 95:
        return 2;

      default:
        return 0;
    }
  }

  function rc(a) {
    switch (a) {
      case 15:
      case 14:
        return 99;

      case 13:
      case 12:
      case 11:
      case 10:
        return 98;

      case 9:
      case 8:
      case 7:
      case 6:
      case 4:
      case 5:
        return 97;

      case 3:
      case 2:
      case 1:
        return 95;

      case 0:
        return 90;

      default:
        throw Error(q(358, a));
    }
  }

  function sc(a, b) {
    var c = a.pendingLanes;
    if (0 === c) return G = 0;
    var d = 0,
        e = 0,
        f = a.expiredLanes,
        g = a.suspendedLanes,
        h = a.pingedLanes;
    if (0 !== f) d = f, e = G = 15;else if (f = c & 134217727, 0 !== f) {
      var k = f & ~g;
      0 !== k ? (d = pc(k), e = G) : (h &= f, 0 !== h && (d = pc(h), e = G));
    } else f = c & ~g, 0 !== f ? (d = pc(f), e = G) : 0 !== h && (d = pc(h), e = G);
    if (0 === d) return 0;
    d = 31 - tc(d);
    d = c & ((0 > d ? 0 : 1 << d) << 1) - 1;

    if (0 !== b && b !== d && 0 === (b & g)) {
      pc(b);
      if (e <= G) return b;
      G = e;
    }

    b = a.entangledLanes;
    if (0 !== b) for (a = a.entanglements, b &= d; 0 < b;) {
      c = 31 - tc(b), e = 1 << c, d |= a[c], b &= ~e;
    }
    return d;
  }

  function uc(a) {
    a = a.pendingLanes & -1073741825;
    return 0 !== a ? a : a & 1073741824 ? 1073741824 : 0;
  }

  function vc(a, b) {
    switch (a) {
      case 15:
        return 1;

      case 14:
        return 2;

      case 12:
        return a = wc(24 & ~b), 0 === a ? vc(10, b) : a;

      case 10:
        return a = wc(192 & ~b), 0 === a ? vc(8, b) : a;

      case 8:
        return a = wc(3584 & ~b), 0 === a && (a = wc(4186112 & ~b), 0 === a && (a = 512)), a;

      case 2:
        return b = wc(805306368 & ~b), 0 === b && (b = 268435456), b;
    }

    throw Error(q(358, a));
  }

  function wc(a) {
    return a & -a;
  }

  function xc(a) {
    for (var b = [], c = 0; 31 > c; c++) {
      b.push(a);
    }

    return b;
  }

  function yc(a, b, c) {
    a.pendingLanes |= b;
    var d = b - 1;
    a.suspendedLanes &= d;
    a.pingedLanes &= d;
    a = a.eventTimes;
    b = 31 - tc(b);
    a[b] = c;
  }

  var tc = Math.clz32 ? Math.clz32 : zc,
      Ac = Math.log,
      Bc = Math.LN2;

  function zc(a) {
    return 0 === a ? 32 : 31 - (Ac(a) / Bc | 0) | 0;
  }

  var Cc = m.unstable_runWithPriority,
      Dc = m.unstable_scheduleCallback,
      Ec = m.unstable_cancelCallback,
      Fc = m.unstable_shouldYield,
      Gc = m.unstable_requestPaint,
      Hc = m.unstable_now,
      Ic = m.unstable_getCurrentPriorityLevel,
      Jc = m.unstable_ImmediatePriority,
      Kc = m.unstable_UserBlockingPriority,
      Lc = m.unstable_NormalPriority,
      Mc = m.unstable_LowPriority,
      Nc = m.unstable_IdlePriority,
      Oc = {},
      Pc = void 0 !== Gc ? Gc : function () {},
      Qc = null,
      Rc = null,
      Sc = !1,
      Tc = Hc(),
      H = 1E4 > Tc ? Hc : function () {
    return Hc() - Tc;
  };

  function Uc() {
    switch (Ic()) {
      case Jc:
        return 99;

      case Kc:
        return 98;

      case Lc:
        return 97;

      case Mc:
        return 96;

      case Nc:
        return 95;

      default:
        throw Error(q(332));
    }
  }

  function Vc(a) {
    switch (a) {
      case 99:
        return Jc;

      case 98:
        return Kc;

      case 97:
        return Lc;

      case 96:
        return Mc;

      case 95:
        return Nc;

      default:
        throw Error(q(332));
    }
  }

  function Wc(a, b) {
    a = Vc(a);
    return Cc(a, b);
  }

  function Xc(a, b, c) {
    a = Vc(a);
    return Dc(a, b, c);
  }

  function I() {
    if (null !== Rc) {
      var a = Rc;
      Rc = null;
      Ec(a);
    }

    Yc();
  }

  function Yc() {
    if (!Sc && null !== Qc) {
      Sc = !0;
      var a = 0;

      try {
        var b = Qc;
        Wc(99, function () {
          for (; a < b.length; a++) {
            var c = b[a];

            do {
              c = c(!0);
            } while (null !== c);
          }
        });
        Qc = null;
      } catch (c) {
        throw null !== Qc && (Qc = Qc.slice(a + 1)), Dc(Jc, I), c;
      } finally {
        Sc = !1;
      }
    }
  }

  var Zc = da.ReactCurrentBatchConfig;

  function $c(a, b) {
    return a === b && (0 !== a || 1 / a === 1 / b) || a !== a && b !== b;
  }

  var J = "function" === typeof Object.is ? Object.is : $c,
      ad = Object.prototype.hasOwnProperty;

  function bd(a, b) {
    if (J(a, b)) return !0;
    if ("object" !== typeof a || null === a || "object" !== typeof b || null === b) return !1;
    var c = Object.keys(a),
        d = Object.keys(b);
    if (c.length !== d.length) return !1;

    for (d = 0; d < c.length; d++) {
      if (!ad.call(b, c[d]) || !J(a[c[d]], b[c[d]])) return !1;
    }

    return !0;
  }

  function cd(a) {
    switch (a.tag) {
      case 5:
        return Yb(a.type);

      case 16:
        return Yb("Lazy");

      case 13:
        return Yb("Suspense");

      case 19:
        return Yb("SuspenseList");

      case 0:
      case 2:
      case 15:
        return a = $b(a.type, !1), a;

      case 11:
        return a = $b(a.type.render, !1), a;

      case 22:
        return a = $b(a.type._render, !1), a;

      case 1:
        return a = $b(a.type, !0), a;

      default:
        return "";
    }
  }

  function dd(a, b) {
    if (a && a.defaultProps) {
      b = aa({}, b);
      a = a.defaultProps;

      for (var c in a) {
        void 0 === b[c] && (b[c] = a[c]);
      }

      return b;
    }

    return b;
  }

  var ed = cc(null),
      fd = null,
      gd = null,
      hd = null;

  function id() {
    hd = gd = fd = null;
  }

  function jd(a, b) {
    a = a.type._context;
    Ta ? (C(ed, a._currentValue), a._currentValue = b) : (C(ed, a._currentValue2), a._currentValue2 = b);
  }

  function kd(a) {
    var b = ed.current;
    B(ed);
    a = a.type._context;
    Ta ? a._currentValue = b : a._currentValue2 = b;
  }

  function ld(a, b) {
    for (; null !== a;) {
      var c = a.alternate;
      if ((a.childLanes & b) === b) {
        if (null === c || (c.childLanes & b) === b) break;else c.childLanes |= b;
      } else a.childLanes |= b, null !== c && (c.childLanes |= b);
      a = a.return;
    }
  }

  function md(a, b) {
    fd = a;
    hd = gd = null;
    a = a.dependencies;
    null !== a && null !== a.firstContext && (0 !== (a.lanes & b) && (K = !0), a.firstContext = null);
  }

  function nd(a, b) {
    if (hd !== a && !1 !== b && 0 !== b) {
      if ("number" !== typeof b || 1073741823 === b) hd = a, b = 1073741823;
      b = {
        context: a,
        observedBits: b,
        next: null
      };

      if (null === gd) {
        if (null === fd) throw Error(q(308));
        gd = b;
        fd.dependencies = {
          lanes: 0,
          firstContext: b,
          responders: null
        };
      } else gd = gd.next = b;
    }

    return Ta ? a._currentValue : a._currentValue2;
  }

  var od = !1;

  function pd(a) {
    a.updateQueue = {
      baseState: a.memoizedState,
      firstBaseUpdate: null,
      lastBaseUpdate: null,
      shared: {
        pending: null
      },
      effects: null
    };
  }

  function rd(a, b) {
    a = a.updateQueue;
    b.updateQueue === a && (b.updateQueue = {
      baseState: a.baseState,
      firstBaseUpdate: a.firstBaseUpdate,
      lastBaseUpdate: a.lastBaseUpdate,
      shared: a.shared,
      effects: a.effects
    });
  }

  function sd(a, b) {
    return {
      eventTime: a,
      lane: b,
      tag: 0,
      payload: null,
      callback: null,
      next: null
    };
  }

  function td(a, b) {
    a = a.updateQueue;

    if (null !== a) {
      a = a.shared;
      var c = a.pending;
      null === c ? b.next = b : (b.next = c.next, c.next = b);
      a.pending = b;
    }
  }

  function ud(a, b) {
    var c = a.updateQueue,
        d = a.alternate;

    if (null !== d && (d = d.updateQueue, c === d)) {
      var e = null,
          f = null;
      c = c.firstBaseUpdate;

      if (null !== c) {
        do {
          var g = {
            eventTime: c.eventTime,
            lane: c.lane,
            tag: c.tag,
            payload: c.payload,
            callback: c.callback,
            next: null
          };
          null === f ? e = f = g : f = f.next = g;
          c = c.next;
        } while (null !== c);

        null === f ? e = f = b : f = f.next = b;
      } else e = f = b;

      c = {
        baseState: d.baseState,
        firstBaseUpdate: e,
        lastBaseUpdate: f,
        shared: d.shared,
        effects: d.effects
      };
      a.updateQueue = c;
      return;
    }

    a = c.lastBaseUpdate;
    null === a ? c.firstBaseUpdate = b : a.next = b;
    c.lastBaseUpdate = b;
  }

  function vd(a, b, c, d) {
    var e = a.updateQueue;
    od = !1;
    var f = e.firstBaseUpdate,
        g = e.lastBaseUpdate,
        h = e.shared.pending;

    if (null !== h) {
      e.shared.pending = null;
      var k = h,
          l = k.next;
      k.next = null;
      null === g ? f = l : g.next = l;
      g = k;
      var p = a.alternate;

      if (null !== p) {
        p = p.updateQueue;
        var t = p.lastBaseUpdate;
        t !== g && (null === t ? p.firstBaseUpdate = l : t.next = l, p.lastBaseUpdate = k);
      }
    }

    if (null !== f) {
      t = e.baseState;
      g = 0;
      p = l = k = null;

      do {
        h = f.lane;
        var n = f.eventTime;

        if ((d & h) === h) {
          null !== p && (p = p.next = {
            eventTime: n,
            lane: 0,
            tag: f.tag,
            payload: f.payload,
            callback: f.callback,
            next: null
          });

          a: {
            var z = a,
                x = f;
            h = b;
            n = c;

            switch (x.tag) {
              case 1:
                z = x.payload;

                if ("function" === typeof z) {
                  t = z.call(n, t, h);
                  break a;
                }

                t = z;
                break a;

              case 3:
                z.flags = z.flags & -4097 | 64;

              case 0:
                z = x.payload;
                h = "function" === typeof z ? z.call(n, t, h) : z;
                if (null === h || void 0 === h) break a;
                t = aa({}, t, h);
                break a;

              case 2:
                od = !0;
            }
          }

          null !== f.callback && (a.flags |= 32, h = e.effects, null === h ? e.effects = [f] : h.push(f));
        } else n = {
          eventTime: n,
          lane: h,
          tag: f.tag,
          payload: f.payload,
          callback: f.callback,
          next: null
        }, null === p ? (l = p = n, k = t) : p = p.next = n, g |= h;

        f = f.next;
        if (null === f) if (h = e.shared.pending, null === h) break;else f = h.next, h.next = null, e.lastBaseUpdate = h, e.shared.pending = null;
      } while (1);

      null === p && (k = t);
      e.baseState = k;
      e.firstBaseUpdate = l;
      e.lastBaseUpdate = p;
      wd |= g;
      a.lanes = g;
      a.memoizedState = t;
    }
  }

  function xd(a, b, c) {
    a = b.effects;
    b.effects = null;
    if (null !== a) for (b = 0; b < a.length; b++) {
      var d = a[b],
          e = d.callback;

      if (null !== e) {
        d.callback = null;
        d = c;
        if ("function" !== typeof e) throw Error(q(191, e));
        e.call(d);
      }
    }
  }

  var yd = new ca.Component().refs;

  function zd(a, b, c, d) {
    b = a.memoizedState;
    c = c(d, b);
    c = null === c || void 0 === c ? b : aa({}, b, c);
    a.memoizedState = c;
    0 === a.lanes && (a.updateQueue.baseState = c);
  }

  var Cd = {
    isMounted: function isMounted(a) {
      return (a = a._reactInternals) ? ya(a) === a : !1;
    },
    enqueueSetState: function enqueueSetState(a, b, c) {
      a = a._reactInternals;
      var d = L(),
          e = Ad(a),
          f = sd(d, e);
      f.payload = b;
      void 0 !== c && null !== c && (f.callback = c);
      td(a, f);
      Bd(a, e, d);
    },
    enqueueReplaceState: function enqueueReplaceState(a, b, c) {
      a = a._reactInternals;
      var d = L(),
          e = Ad(a),
          f = sd(d, e);
      f.tag = 1;
      f.payload = b;
      void 0 !== c && null !== c && (f.callback = c);
      td(a, f);
      Bd(a, e, d);
    },
    enqueueForceUpdate: function enqueueForceUpdate(a, b) {
      a = a._reactInternals;
      var c = L(),
          d = Ad(a),
          e = sd(c, d);
      e.tag = 2;
      void 0 !== b && null !== b && (e.callback = b);
      td(a, e);
      Bd(a, d, c);
    }
  };

  function Dd(a, b, c, d, e, f, g) {
    a = a.stateNode;
    return "function" === typeof a.shouldComponentUpdate ? a.shouldComponentUpdate(d, f, g) : b.prototype && b.prototype.isPureReactComponent ? !bd(c, d) || !bd(e, f) : !0;
  }

  function Ed(a, b, c) {
    var d = !1,
        e = dc;
    var f = b.contextType;
    "object" === typeof f && null !== f ? f = nd(f) : (e = F(b) ? ec : D.current, d = b.contextTypes, f = (d = null !== d && void 0 !== d) ? fc(a, e) : dc);
    b = new b(c, f);
    a.memoizedState = null !== b.state && void 0 !== b.state ? b.state : null;
    b.updater = Cd;
    a.stateNode = b;
    b._reactInternals = a;
    d && (a = a.stateNode, a.__reactInternalMemoizedUnmaskedChildContext = e, a.__reactInternalMemoizedMaskedChildContext = f);
    return b;
  }

  function Fd(a, b, c, d) {
    a = b.state;
    "function" === typeof b.componentWillReceiveProps && b.componentWillReceiveProps(c, d);
    "function" === typeof b.UNSAFE_componentWillReceiveProps && b.UNSAFE_componentWillReceiveProps(c, d);
    b.state !== a && Cd.enqueueReplaceState(b, b.state, null);
  }

  function Gd(a, b, c, d) {
    var e = a.stateNode;
    e.props = c;
    e.state = a.memoizedState;
    e.refs = yd;
    pd(a);
    var f = b.contextType;
    "object" === typeof f && null !== f ? e.context = nd(f) : (f = F(b) ? ec : D.current, e.context = fc(a, f));
    vd(a, c, e, d);
    e.state = a.memoizedState;
    f = b.getDerivedStateFromProps;
    "function" === typeof f && (zd(a, b, f, c), e.state = a.memoizedState);
    "function" === typeof b.getDerivedStateFromProps || "function" === typeof e.getSnapshotBeforeUpdate || "function" !== typeof e.UNSAFE_componentWillMount && "function" !== typeof e.componentWillMount || (b = e.state, "function" === typeof e.componentWillMount && e.componentWillMount(), "function" === typeof e.UNSAFE_componentWillMount && e.UNSAFE_componentWillMount(), b !== e.state && Cd.enqueueReplaceState(e, e.state, null), vd(a, c, e, d), e.state = a.memoizedState);
    "function" === typeof e.componentDidMount && (a.flags |= 4);
  }

  var Hd = Array.isArray;

  function Id(a, b, c) {
    a = c.ref;

    if (null !== a && "function" !== typeof a && "object" !== typeof a) {
      if (c._owner) {
        c = c._owner;

        if (c) {
          if (1 !== c.tag) throw Error(q(309));
          var d = c.stateNode;
        }

        if (!d) throw Error(q(147, a));
        var e = "" + a;
        if (null !== b && null !== b.ref && "function" === typeof b.ref && b.ref._stringRef === e) return b.ref;

        b = function b(a) {
          var b = d.refs;
          b === yd && (b = d.refs = {});
          null === a ? delete b[e] : b[e] = a;
        };

        b._stringRef = e;
        return b;
      }

      if ("string" !== typeof a) throw Error(q(284));
      if (!c._owner) throw Error(q(290, a));
    }

    return a;
  }

  function Jd(a, b) {
    if ("textarea" !== a.type) throw Error(q(31, "[object Object]" === Object.prototype.toString.call(b) ? "object with keys {" + Object.keys(b).join(", ") + "}" : b));
  }

  function Kd(a) {
    try {
      var b = a._init;
      return b(a._payload);
    } catch (c) {
      return a;
    }
  }

  function Ld(a) {
    function b(b, c) {
      if (a) {
        var d = b.lastEffect;
        null !== d ? (d.nextEffect = c, b.lastEffect = c) : b.firstEffect = b.lastEffect = c;
        c.nextEffect = null;
        c.flags = 8;
      }
    }

    function c(c, d) {
      if (!a) return null;

      for (; null !== d;) {
        b(c, d), d = d.sibling;
      }

      return null;
    }

    function d(a, b) {
      for (a = new Map(); null !== b;) {
        null !== b.key ? a.set(b.key, b) : a.set(b.index, b), b = b.sibling;
      }

      return a;
    }

    function e(a, b) {
      a = Md(a, b);
      a.index = 0;
      a.sibling = null;
      return a;
    }

    function f(b, c, d) {
      b.index = d;
      if (!a) return c;
      d = b.alternate;
      if (null !== d) return d = d.index, d < c ? (b.flags = 2, c) : d;
      b.flags = 2;
      return c;
    }

    function g(b) {
      a && null === b.alternate && (b.flags = 2);
      return b;
    }

    function h(a, b, c, d) {
      if (null === b || 6 !== b.tag) return b = Nd(c, a.mode, d), b.return = a, b;
      b = e(b, c);
      b.return = a;
      return b;
    }

    function k(a, b, c, d) {
      if (null !== b) {
        if (b.elementType === c.type) {
          var f = e(b, c.props);
          f.ref = Id(a, b, c);
          f.return = a;
          return f;
        }

        if (22 === b.tag && (f = c.type, f.$$typeof === qa && (f = Kd(f)), f.$$typeof === ra && f._render === b.type._render)) return b = e(b, c.props), b.return = a, b.type = f, b;
      }

      f = Od(c.type, c.key, c.props, null, a.mode, d);
      f.ref = Id(a, b, c);
      f.return = a;
      return f;
    }

    function l(a, b, c, d) {
      if (null === b || 4 !== b.tag || b.stateNode.containerInfo !== c.containerInfo || b.stateNode.implementation !== c.implementation) return b = Pd(c, a.mode, d), b.return = a, b;
      b = e(b, c.children || []);
      b.return = a;
      return b;
    }

    function p(a, b, c, d, f) {
      if (null === b || 7 !== b.tag) return b = Qd(c, a.mode, d, f), b.return = a, b;
      b = e(b, c);
      b.return = a;
      return b;
    }

    function t(a, b, c) {
      if ("string" === typeof b || "number" === typeof b) return b = Nd("" + b, a.mode, c), b.return = a, b;

      if ("object" === typeof b && null !== b) {
        switch (b.$$typeof) {
          case ea:
            return c = Od(b.type, b.key, b.props, null, a.mode, c), c.ref = Id(a, null, b), c.return = a, c;

          case fa:
            return b = Pd(b, a.mode, c), b.return = a, b;

          case qa:
            var d = b._init;
            return t(a, d(b._payload), c);
        }

        if (Hd(b) || wa(b)) return b = Qd(b, a.mode, c, null), b.return = a, b;
        Jd(a, b);
      }

      return null;
    }

    function n(a, b, c, d) {
      var e = null !== b ? b.key : null;
      if ("string" === typeof c || "number" === typeof c) return null !== e ? null : h(a, b, "" + c, d);

      if ("object" === typeof c && null !== c) {
        switch (c.$$typeof) {
          case ea:
            return c.key === e ? c.type === ha ? p(a, b, c.props.children, d, e) : k(a, b, c, d) : null;

          case fa:
            return c.key === e ? l(a, b, c, d) : null;

          case qa:
            return e = c._init, n(a, b, e(c._payload), d);
        }

        if (Hd(c) || wa(c)) return null !== e ? null : p(a, b, c, d, null);
        Jd(a, c);
      }

      return null;
    }

    function z(a, b, c, d, e) {
      if ("string" === typeof d || "number" === typeof d) return a = a.get(c) || null, h(b, a, "" + d, e);

      if ("object" === typeof d && null !== d) {
        switch (d.$$typeof) {
          case ea:
            return a = a.get(null === d.key ? c : d.key) || null, d.type === ha ? p(b, a, d.props.children, e, d.key) : k(b, a, d, e);

          case fa:
            return a = a.get(null === d.key ? c : d.key) || null, l(b, a, d, e);

          case qa:
            var f = d._init;
            return z(a, b, c, f(d._payload), e);
        }

        if (Hd(d) || wa(d)) return a = a.get(c) || null, p(b, a, d, e, null);
        Jd(b, d);
      }

      return null;
    }

    function x(e, g, h, k) {
      for (var l = null, y = null, v = g, u = g = 0, p = null; null !== v && u < h.length; u++) {
        v.index > u ? (p = v, v = null) : p = v.sibling;
        var w = n(e, v, h[u], k);

        if (null === w) {
          null === v && (v = p);
          break;
        }

        a && v && null === w.alternate && b(e, v);
        g = f(w, g, u);
        null === y ? l = w : y.sibling = w;
        y = w;
        v = p;
      }

      if (u === h.length) return c(e, v), l;

      if (null === v) {
        for (; u < h.length; u++) {
          v = t(e, h[u], k), null !== v && (g = f(v, g, u), null === y ? l = v : y.sibling = v, y = v);
        }

        return l;
      }

      for (v = d(e, v); u < h.length; u++) {
        p = z(v, e, u, h[u], k), null !== p && (a && null !== p.alternate && v.delete(null === p.key ? u : p.key), g = f(p, g, u), null === y ? l = p : y.sibling = p, y = p);
      }

      a && v.forEach(function (a) {
        return b(e, a);
      });
      return l;
    }

    function ba(e, g, h, k) {
      var l = wa(h);
      if ("function" !== typeof l) throw Error(q(150));
      h = l.call(h);
      if (null == h) throw Error(q(151));

      for (var v = l = null, u = g, y = g = 0, p = null, w = h.next(); null !== u && !w.done; y++, w = h.next()) {
        u.index > y ? (p = u, u = null) : p = u.sibling;
        var x = n(e, u, w.value, k);

        if (null === x) {
          null === u && (u = p);
          break;
        }

        a && u && null === x.alternate && b(e, u);
        g = f(x, g, y);
        null === v ? l = x : v.sibling = x;
        v = x;
        u = p;
      }

      if (w.done) return c(e, u), l;

      if (null === u) {
        for (; !w.done; y++, w = h.next()) {
          w = t(e, w.value, k), null !== w && (g = f(w, g, y), null === v ? l = w : v.sibling = w, v = w);
        }

        return l;
      }

      for (u = d(e, u); !w.done; y++, w = h.next()) {
        w = z(u, e, y, w.value, k), null !== w && (a && null !== w.alternate && u.delete(null === w.key ? y : w.key), g = f(w, g, y), null === v ? l = w : v.sibling = w, v = w);
      }

      a && u.forEach(function (a) {
        return b(e, a);
      });
      return l;
    }

    function Ha(a, d, f, h) {
      var k = "object" === typeof f && null !== f && f.type === ha && null === f.key;
      k && (f = f.props.children);
      var l = "object" === typeof f && null !== f;
      if (l) switch (f.$$typeof) {
        case ea:
          a: {
            l = f.key;

            for (k = d; null !== k;) {
              if (k.key === l) {
                switch (k.tag) {
                  case 7:
                    if (f.type === ha) {
                      c(a, k.sibling);
                      d = e(k, f.props.children);
                      d.return = a;
                      a = d;
                      break a;
                    }

                    break;

                  case 22:
                    if (l = f.type, l.$$typeof === qa && (l = Kd(l)), l.$$typeof === ra && l._render === k.type._render) {
                      c(a, k.sibling);
                      d = e(k, f.props);
                      d.type = l;
                      d.return = a;
                      a = d;
                      break a;
                    }

                  default:
                    if (k.elementType === f.type) {
                      c(a, k.sibling);
                      d = e(k, f.props);
                      d.ref = Id(a, k, f);
                      d.return = a;
                      a = d;
                      break a;
                    }

                }

                c(a, k);
                break;
              } else b(a, k);

              k = k.sibling;
            }

            f.type === ha ? (d = Qd(f.props.children, a.mode, h, f.key), d.return = a, a = d) : (h = Od(f.type, f.key, f.props, null, a.mode, h), h.ref = Id(a, d, f), h.return = a, a = h);
          }

          return g(a);

        case fa:
          a: {
            for (k = f.key; null !== d;) {
              if (d.key === k) {
                if (4 === d.tag && d.stateNode.containerInfo === f.containerInfo && d.stateNode.implementation === f.implementation) {
                  c(a, d.sibling);
                  d = e(d, f.children || []);
                  d.return = a;
                  a = d;
                  break a;
                } else {
                  c(a, d);
                  break;
                }
              } else b(a, d);
              d = d.sibling;
            }

            d = Pd(f, a.mode, h);
            d.return = a;
            a = d;
          }

          return g(a);

        case qa:
          return k = f._init, Ha(a, d, k(f._payload), h);
      }
      if ("string" === typeof f || "number" === typeof f) return f = "" + f, null !== d && 6 === d.tag ? (c(a, d.sibling), d = e(d, f), d.return = a, a = d) : (c(a, d), d = Nd(f, a.mode, h), d.return = a, a = d), g(a);
      if (Hd(f)) return x(a, d, f, h);
      if (wa(f)) return ba(a, d, f, h);
      l && Jd(a, f);
      if ("undefined" === typeof f && !k) switch (a.tag) {
        case 1:
        case 22:
        case 0:
        case 11:
        case 15:
          throw Error(q(152, xa(a.type) || "Component"));
      }
      return c(a, d);
    }

    return Ha;
  }

  var Rd = Ld(!0),
      Sd = Ld(!1),
      Td = {},
      Ud = cc(Td),
      Vd = cc(Td),
      Wd = cc(Td);

  function Xd(a) {
    if (a === Td) throw Error(q(174));
    return a;
  }

  function Yd(a, b) {
    C(Wd, b);
    C(Vd, a);
    C(Ud, Td);
    a = Fa(b);
    B(Ud);
    C(Ud, a);
  }

  function Zd() {
    B(Ud);
    B(Vd);
    B(Wd);
  }

  function $d(a) {
    var b = Xd(Wd.current),
        c = Xd(Ud.current);
    b = Ga(c, a.type, b);
    c !== b && (C(Vd, a), C(Ud, b));
  }

  function ae(a) {
    Vd.current === a && (B(Ud), B(Vd));
  }

  var M = cc(0);

  function be(a) {
    for (var b = a; null !== b;) {
      if (13 === b.tag) {
        var c = b.memoizedState;
        if (null !== c && (c = c.dehydrated, null === c || Kb(c) || Lb(c))) return b;
      } else if (19 === b.tag && void 0 !== b.memoizedProps.revealOrder) {
        if (0 !== (b.flags & 64)) return b;
      } else if (null !== b.child) {
        b.child.return = b;
        b = b.child;
        continue;
      }

      if (b === a) break;

      for (; null === b.sibling;) {
        if (null === b.return || b.return === a) return null;
        b = b.return;
      }

      b.sibling.return = b.return;
      b = b.sibling;
    }

    return null;
  }

  var ce = null,
      de = null,
      ee = !1;

  function fe(a, b) {
    var c = N(5, null, null, 0);
    c.elementType = "DELETED";
    c.type = "DELETED";
    c.stateNode = b;
    c.return = a;
    c.flags = 8;
    null !== a.lastEffect ? (a.lastEffect.nextEffect = c, a.lastEffect = c) : a.firstEffect = a.lastEffect = c;
  }

  function ge(a, b) {
    switch (a.tag) {
      case 5:
        return b = Hb(b, a.type, a.pendingProps), null !== b ? (a.stateNode = b, !0) : !1;

      case 6:
        return b = Ib(b, a.pendingProps), null !== b ? (a.stateNode = b, !0) : !1;

      case 13:
        b = Jb(b);

        if (null !== b) {
          a.memoizedState = {
            dehydrated: b,
            retryLane: 1073741824
          };
          var c = N(18, null, null, 0);
          c.stateNode = b;
          c.return = a;
          a.child = c;
          return !0;
        }

        return !1;

      default:
        return !1;
    }
  }

  function he(a) {
    if (ee) {
      var b = de;

      if (b) {
        var c = b;

        if (!ge(a, b)) {
          b = Nb(c);

          if (!b || !ge(a, b)) {
            a.flags = a.flags & -1025 | 2;
            ee = !1;
            ce = a;
            return;
          }

          fe(ce, c);
        }

        ce = a;
        de = Ob(b);
      } else a.flags = a.flags & -1025 | 2, ee = !1, ce = a;
    }
  }

  function ie(a) {
    for (a = a.return; null !== a && 5 !== a.tag && 3 !== a.tag && 13 !== a.tag;) {
      a = a.return;
    }

    ce = a;
  }

  function je(a) {
    if (!A || a !== ce) return !1;
    if (!ee) return ie(a), ee = !0, !1;
    var b = a.type;
    if (5 !== a.tag || "head" !== b && "body" !== b && !Oa(b, a.memoizedProps)) for (b = de; b;) {
      fe(a, b), b = Nb(b);
    }
    ie(a);

    if (13 === a.tag) {
      if (!A) throw Error(q(316));
      a = a.memoizedState;
      a = null !== a ? a.dehydrated : null;
      if (!a) throw Error(q(317));
      de = Sb(a);
    } else de = ce ? Nb(a.stateNode) : null;

    return !0;
  }

  function ke() {
    A && (de = ce = null, ee = !1);
  }

  var le = [];

  function me() {
    for (var a = 0; a < le.length; a++) {
      var b = le[a];
      Ta ? b._workInProgressVersionPrimary = null : b._workInProgressVersionSecondary = null;
    }

    le.length = 0;
  }

  var ne = da.ReactCurrentDispatcher,
      oe = da.ReactCurrentBatchConfig,
      pe = 0,
      O = null,
      P = null,
      Q = null,
      qe = !1,
      re = !1;

  function R() {
    throw Error(q(321));
  }

  function se(a, b) {
    if (null === b) return !1;

    for (var c = 0; c < b.length && c < a.length; c++) {
      if (!J(a[c], b[c])) return !1;
    }

    return !0;
  }

  function te(a, b, c, d, e, f) {
    pe = f;
    O = b;
    b.memoizedState = null;
    b.updateQueue = null;
    b.lanes = 0;
    ne.current = null === a || null === a.memoizedState ? ue : ve;
    a = c(d, e);

    if (re) {
      f = 0;

      do {
        re = !1;
        if (!(25 > f)) throw Error(q(301));
        f += 1;
        Q = P = null;
        b.updateQueue = null;
        ne.current = we;
        a = c(d, e);
      } while (re);
    }

    ne.current = xe;
    b = null !== P && null !== P.next;
    pe = 0;
    Q = P = O = null;
    qe = !1;
    if (b) throw Error(q(300));
    return a;
  }

  function ye(a, b, c) {
    b.updateQueue = a.updateQueue;
    b.flags &= -517;
    a.lanes &= ~c;
  }

  function ze() {
    var a = {
      memoizedState: null,
      baseState: null,
      baseQueue: null,
      queue: null,
      next: null
    };
    null === Q ? O.memoizedState = Q = a : Q = Q.next = a;
    return Q;
  }

  function Ae() {
    if (null === P) {
      var a = O.alternate;
      a = null !== a ? a.memoizedState : null;
    } else a = P.next;

    var b = null === Q ? O.memoizedState : Q.next;
    if (null !== b) Q = b, P = a;else {
      if (null === a) throw Error(q(310));
      P = a;
      a = {
        memoizedState: P.memoizedState,
        baseState: P.baseState,
        baseQueue: P.baseQueue,
        queue: P.queue,
        next: null
      };
      null === Q ? O.memoizedState = Q = a : Q = Q.next = a;
    }
    return Q;
  }

  function Be(a, b) {
    return "function" === typeof b ? b(a) : b;
  }

  function Ce(a) {
    var b = Ae(),
        c = b.queue;
    if (null === c) throw Error(q(311));
    c.lastRenderedReducer = a;
    var d = P,
        e = d.baseQueue,
        f = c.pending;

    if (null !== f) {
      if (null !== e) {
        var g = e.next;
        e.next = f.next;
        f.next = g;
      }

      d.baseQueue = e = f;
      c.pending = null;
    }

    if (null !== e) {
      e = e.next;
      d = d.baseState;
      var h = g = f = null,
          k = e;

      do {
        var l = k.lane;
        if ((pe & l) === l) null !== h && (h = h.next = {
          lane: 0,
          action: k.action,
          eagerReducer: k.eagerReducer,
          eagerState: k.eagerState,
          next: null
        }), d = k.eagerReducer === a ? k.eagerState : a(d, k.action);else {
          var p = {
            lane: l,
            action: k.action,
            eagerReducer: k.eagerReducer,
            eagerState: k.eagerState,
            next: null
          };
          null === h ? (g = h = p, f = d) : h = h.next = p;
          O.lanes |= l;
          wd |= l;
        }
        k = k.next;
      } while (null !== k && k !== e);

      null === h ? f = d : h.next = g;
      J(d, b.memoizedState) || (K = !0);
      b.memoizedState = d;
      b.baseState = f;
      b.baseQueue = h;
      c.lastRenderedState = d;
    }

    return [b.memoizedState, c.dispatch];
  }

  function De(a) {
    var b = Ae(),
        c = b.queue;
    if (null === c) throw Error(q(311));
    c.lastRenderedReducer = a;
    var d = c.dispatch,
        e = c.pending,
        f = b.memoizedState;

    if (null !== e) {
      c.pending = null;
      var g = e = e.next;

      do {
        f = a(f, g.action), g = g.next;
      } while (g !== e);

      J(f, b.memoizedState) || (K = !0);
      b.memoizedState = f;
      null === b.baseQueue && (b.baseState = f);
      c.lastRenderedState = f;
    }

    return [f, d];
  }

  function Ee(a, b, c) {
    var d = b._getVersion;
    d = d(b._source);
    var e = Ta ? b._workInProgressVersionPrimary : b._workInProgressVersionSecondary;
    if (null !== e) a = e === d;else if (a = a.mutableReadLanes, a = (pe & a) === a) Ta ? b._workInProgressVersionPrimary = d : b._workInProgressVersionSecondary = d, le.push(b);
    if (a) return c(b._source);
    le.push(b);
    throw Error(q(350));
  }

  function Fe(a, b, c, d) {
    var e = S;
    if (null === e) throw Error(q(349));
    var f = b._getVersion,
        g = f(b._source),
        h = ne.current,
        k = h.useState(function () {
      return Ee(e, b, c);
    }),
        l = k[1],
        p = k[0];
    k = Q;
    var t = a.memoizedState,
        n = t.refs,
        z = n.getSnapshot,
        x = t.source;
    t = t.subscribe;
    var ba = O;
    a.memoizedState = {
      refs: n,
      source: b,
      subscribe: d
    };
    h.useEffect(function () {
      n.getSnapshot = c;
      n.setSnapshot = l;
      var a = f(b._source);

      if (!J(g, a)) {
        a = c(b._source);
        J(p, a) || (l(a), a = Ad(ba), e.mutableReadLanes |= a & e.pendingLanes);
        a = e.mutableReadLanes;
        e.entangledLanes |= a;

        for (var d = e.entanglements, h = a; 0 < h;) {
          var k = 31 - tc(h),
              t = 1 << k;
          d[k] |= a;
          h &= ~t;
        }
      }
    }, [c, b, d]);
    h.useEffect(function () {
      return d(b._source, function () {
        var a = n.getSnapshot,
            c = n.setSnapshot;

        try {
          c(a(b._source));
          var d = Ad(ba);
          e.mutableReadLanes |= d & e.pendingLanes;
        } catch (u) {
          c(function () {
            throw u;
          });
        }
      });
    }, [b, d]);
    J(z, c) && J(x, b) && J(t, d) || (a = {
      pending: null,
      dispatch: null,
      lastRenderedReducer: Be,
      lastRenderedState: p
    }, a.dispatch = l = Ge.bind(null, O, a), k.queue = a, k.baseQueue = null, p = Ee(e, b, c), k.memoizedState = k.baseState = p);
    return p;
  }

  function He(a, b, c) {
    var d = Ae();
    return Fe(d, a, b, c);
  }

  function Ie(a) {
    var b = ze();
    "function" === typeof a && (a = a());
    b.memoizedState = b.baseState = a;
    a = b.queue = {
      pending: null,
      dispatch: null,
      lastRenderedReducer: Be,
      lastRenderedState: a
    };
    a = a.dispatch = Ge.bind(null, O, a);
    return [b.memoizedState, a];
  }

  function Je(a, b, c, d) {
    a = {
      tag: a,
      create: b,
      destroy: c,
      deps: d,
      next: null
    };
    b = O.updateQueue;
    null === b ? (b = {
      lastEffect: null
    }, O.updateQueue = b, b.lastEffect = a.next = a) : (c = b.lastEffect, null === c ? b.lastEffect = a.next = a : (d = c.next, c.next = a, a.next = d, b.lastEffect = a));
    return a;
  }

  function Ke(a) {
    var b = ze();
    a = {
      current: a
    };
    return b.memoizedState = a;
  }

  function Le() {
    return Ae().memoizedState;
  }

  function Me(a, b, c, d) {
    var e = ze();
    O.flags |= a;
    e.memoizedState = Je(1 | b, c, void 0, void 0 === d ? null : d);
  }

  function Ne(a, b, c, d) {
    var e = Ae();
    d = void 0 === d ? null : d;
    var f = void 0;

    if (null !== P) {
      var g = P.memoizedState;
      f = g.destroy;

      if (null !== d && se(d, g.deps)) {
        Je(b, c, f, d);
        return;
      }
    }

    O.flags |= a;
    e.memoizedState = Je(1 | b, c, f, d);
  }

  function Oe(a, b) {
    return Me(516, 4, a, b);
  }

  function Pe(a, b) {
    return Ne(516, 4, a, b);
  }

  function Qe(a, b) {
    return Ne(4, 2, a, b);
  }

  function Re(a, b) {
    if ("function" === typeof b) return a = a(), b(a), function () {
      b(null);
    };
    if (null !== b && void 0 !== b) return a = a(), b.current = a, function () {
      b.current = null;
    };
  }

  function Se(a, b, c) {
    c = null !== c && void 0 !== c ? c.concat([a]) : null;
    return Ne(4, 2, Re.bind(null, b, a), c);
  }

  function Te() {}

  function Ue(a, b) {
    var c = Ae();
    b = void 0 === b ? null : b;
    var d = c.memoizedState;
    if (null !== d && null !== b && se(b, d[1])) return d[0];
    c.memoizedState = [a, b];
    return a;
  }

  function Ve(a, b) {
    var c = Ae();
    b = void 0 === b ? null : b;
    var d = c.memoizedState;
    if (null !== d && null !== b && se(b, d[1])) return d[0];
    a = a();
    c.memoizedState = [a, b];
    return a;
  }

  function We(a, b) {
    var c = Uc();
    Wc(98 > c ? 98 : c, function () {
      a(!0);
    });
    Wc(97 < c ? 97 : c, function () {
      var c = oe.transition;
      oe.transition = 1;

      try {
        a(!1), b();
      } finally {
        oe.transition = c;
      }
    });
  }

  function Ge(a, b, c) {
    var d = L(),
        e = Ad(a),
        f = {
      lane: e,
      action: c,
      eagerReducer: null,
      eagerState: null,
      next: null
    },
        g = b.pending;
    null === g ? f.next = f : (f.next = g.next, g.next = f);
    b.pending = f;
    g = a.alternate;
    if (a === O || null !== g && g === O) re = qe = !0;else {
      if (0 === a.lanes && (null === g || 0 === g.lanes) && (g = b.lastRenderedReducer, null !== g)) try {
        var h = b.lastRenderedState,
            k = g(h, c);
        f.eagerReducer = g;
        f.eagerState = k;
        if (J(k, h)) return;
      } catch (l) {} finally {}
      Bd(a, e, d);
    }
  }

  var xe = {
    readContext: nd,
    useCallback: R,
    useContext: R,
    useEffect: R,
    useImperativeHandle: R,
    useLayoutEffect: R,
    useMemo: R,
    useReducer: R,
    useRef: R,
    useState: R,
    useDebugValue: R,
    useDeferredValue: R,
    useTransition: R,
    useMutableSource: R,
    useOpaqueIdentifier: R,
    unstable_isNewReconciler: !1
  },
      ue = {
    readContext: nd,
    useCallback: function useCallback(a, b) {
      ze().memoizedState = [a, void 0 === b ? null : b];
      return a;
    },
    useContext: nd,
    useEffect: Oe,
    useImperativeHandle: function useImperativeHandle(a, b, c) {
      c = null !== c && void 0 !== c ? c.concat([a]) : null;
      return Me(4, 2, Re.bind(null, b, a), c);
    },
    useLayoutEffect: function useLayoutEffect(a, b) {
      return Me(4, 2, a, b);
    },
    useMemo: function useMemo(a, b) {
      var c = ze();
      b = void 0 === b ? null : b;
      a = a();
      c.memoizedState = [a, b];
      return a;
    },
    useReducer: function useReducer(a, b, c) {
      var d = ze();
      b = void 0 !== c ? c(b) : b;
      d.memoizedState = d.baseState = b;
      a = d.queue = {
        pending: null,
        dispatch: null,
        lastRenderedReducer: a,
        lastRenderedState: b
      };
      a = a.dispatch = Ge.bind(null, O, a);
      return [d.memoizedState, a];
    },
    useRef: Ke,
    useState: Ie,
    useDebugValue: Te,
    useDeferredValue: function useDeferredValue(a) {
      var b = Ie(a),
          c = b[0],
          d = b[1];
      Oe(function () {
        var b = oe.transition;
        oe.transition = 1;

        try {
          d(a);
        } finally {
          oe.transition = b;
        }
      }, [a]);
      return c;
    },
    useTransition: function useTransition() {
      var a = Ie(!1),
          b = a[0];
      a = We.bind(null, a[1]);
      Ke(a);
      return [a, b];
    },
    useMutableSource: function useMutableSource(a, b, c) {
      var d = ze();
      d.memoizedState = {
        refs: {
          getSnapshot: b,
          setSnapshot: null
        },
        source: a,
        subscribe: c
      };
      return Fe(d, a, b, c);
    },
    useOpaqueIdentifier: function useOpaqueIdentifier() {
      if (ee) {
        var a = !1,
            b = Xa(function () {
          a || (a = !0, c(Ya()));
          throw Error(q(355));
        }),
            c = Ie(b)[1];
        0 === (O.mode & 2) && (O.flags |= 516, Je(5, function () {
          c(Ya());
        }, void 0, null));
        return b;
      }

      b = Ya();
      Ie(b);
      return b;
    },
    unstable_isNewReconciler: !1
  },
      ve = {
    readContext: nd,
    useCallback: Ue,
    useContext: nd,
    useEffect: Pe,
    useImperativeHandle: Se,
    useLayoutEffect: Qe,
    useMemo: Ve,
    useReducer: Ce,
    useRef: Le,
    useState: function useState() {
      return Ce(Be);
    },
    useDebugValue: Te,
    useDeferredValue: function useDeferredValue(a) {
      var b = Ce(Be),
          c = b[0],
          d = b[1];
      Pe(function () {
        var b = oe.transition;
        oe.transition = 1;

        try {
          d(a);
        } finally {
          oe.transition = b;
        }
      }, [a]);
      return c;
    },
    useTransition: function useTransition() {
      var a = Ce(Be)[0];
      return [Le().current, a];
    },
    useMutableSource: He,
    useOpaqueIdentifier: function useOpaqueIdentifier() {
      return Ce(Be)[0];
    },
    unstable_isNewReconciler: !1
  },
      we = {
    readContext: nd,
    useCallback: Ue,
    useContext: nd,
    useEffect: Pe,
    useImperativeHandle: Se,
    useLayoutEffect: Qe,
    useMemo: Ve,
    useReducer: De,
    useRef: Le,
    useState: function useState() {
      return De(Be);
    },
    useDebugValue: Te,
    useDeferredValue: function useDeferredValue(a) {
      var b = De(Be),
          c = b[0],
          d = b[1];
      Pe(function () {
        var b = oe.transition;
        oe.transition = 1;

        try {
          d(a);
        } finally {
          oe.transition = b;
        }
      }, [a]);
      return c;
    },
    useTransition: function useTransition() {
      var a = De(Be)[0];
      return [Le().current, a];
    },
    useMutableSource: He,
    useOpaqueIdentifier: function useOpaqueIdentifier() {
      return De(Be)[0];
    },
    unstable_isNewReconciler: !1
  },
      Xe = da.ReactCurrentOwner,
      K = !1;

  function T(a, b, c, d) {
    b.child = null === a ? Sd(b, null, c, d) : Rd(b, a.child, c, d);
  }

  function Ze(a, b, c, d, e) {
    c = c.render;
    var f = b.ref;
    md(b, e);
    d = te(a, b, c, d, f, e);
    if (null !== a && !K) return ye(a, b, e), $e(a, b, e);
    b.flags |= 1;
    T(a, b, d, e);
    return b.child;
  }

  function af(a, b, c, d, e, f) {
    if (null === a) {
      var g = c.type;
      if ("function" === typeof g && !bf(g) && void 0 === g.defaultProps && null === c.compare && void 0 === c.defaultProps) return b.tag = 15, b.type = g, cf(a, b, g, d, e, f);
      a = Od(c.type, null, d, b, b.mode, f);
      a.ref = b.ref;
      a.return = b;
      return b.child = a;
    }

    g = a.child;
    if (0 === (e & f) && (e = g.memoizedProps, c = c.compare, c = null !== c ? c : bd, c(e, d) && a.ref === b.ref)) return $e(a, b, f);
    b.flags |= 1;
    a = Md(g, d);
    a.ref = b.ref;
    a.return = b;
    return b.child = a;
  }

  function cf(a, b, c, d, e, f) {
    if (null !== a && bd(a.memoizedProps, d) && a.ref === b.ref) if (K = !1, 0 !== (f & e)) 0 !== (a.flags & 16384) && (K = !0);else return b.lanes = a.lanes, $e(a, b, f);
    return df(a, b, c, d, f);
  }

  function ef(a, b, c) {
    var d = b.pendingProps,
        e = d.children,
        f = null !== a ? a.memoizedState : null;
    if ("hidden" === d.mode || "unstable-defer-without-hiding" === d.mode) {
      if (0 === (b.mode & 4)) b.memoizedState = {
        baseLanes: 0
      }, ff(b, c);else if (0 !== (c & 1073741824)) b.memoizedState = {
        baseLanes: 0
      }, ff(b, null !== f ? f.baseLanes : c);else return a = null !== f ? f.baseLanes | c : c, b.lanes = b.childLanes = 1073741824, b.memoizedState = {
        baseLanes: a
      }, ff(b, a), null;
    } else null !== f ? (d = f.baseLanes | c, b.memoizedState = null) : d = c, ff(b, d);
    T(a, b, e, c);
    return b.child;
  }

  function gf(a, b) {
    var c = b.ref;
    if (null === a && null !== c || null !== a && a.ref !== c) b.flags |= 128;
  }

  function df(a, b, c, d, e) {
    var f = F(c) ? ec : D.current;
    f = fc(b, f);
    md(b, e);
    c = te(a, b, c, d, f, e);
    if (null !== a && !K) return ye(a, b, e), $e(a, b, e);
    b.flags |= 1;
    T(a, b, c, e);
    return b.child;
  }

  function hf(a, b, c, d, e) {
    var f = c._render;
    c = c._data;
    md(b, e);
    d = te(a, b, f, d, c, e);
    if (null !== a && !K) return ye(a, b, e), $e(a, b, e);
    b.flags |= 1;
    T(a, b, d, e);
    return b.child;
  }

  function jf(a, b, c, d, e) {
    if (F(c)) {
      var f = !0;
      jc(b);
    } else f = !1;

    md(b, e);
    if (null === b.stateNode) null !== a && (a.alternate = null, b.alternate = null, b.flags |= 2), Ed(b, c, d), Gd(b, c, d, e), d = !0;else if (null === a) {
      var g = b.stateNode,
          h = b.memoizedProps;
      g.props = h;
      var k = g.context,
          l = c.contextType;
      "object" === typeof l && null !== l ? l = nd(l) : (l = F(c) ? ec : D.current, l = fc(b, l));
      var p = c.getDerivedStateFromProps,
          t = "function" === typeof p || "function" === typeof g.getSnapshotBeforeUpdate;
      t || "function" !== typeof g.UNSAFE_componentWillReceiveProps && "function" !== typeof g.componentWillReceiveProps || (h !== d || k !== l) && Fd(b, g, d, l);
      od = !1;
      var n = b.memoizedState;
      g.state = n;
      vd(b, d, g, e);
      k = b.memoizedState;
      h !== d || n !== k || E.current || od ? ("function" === typeof p && (zd(b, c, p, d), k = b.memoizedState), (h = od || Dd(b, c, h, d, n, k, l)) ? (t || "function" !== typeof g.UNSAFE_componentWillMount && "function" !== typeof g.componentWillMount || ("function" === typeof g.componentWillMount && g.componentWillMount(), "function" === typeof g.UNSAFE_componentWillMount && g.UNSAFE_componentWillMount()), "function" === typeof g.componentDidMount && (b.flags |= 4)) : ("function" === typeof g.componentDidMount && (b.flags |= 4), b.memoizedProps = d, b.memoizedState = k), g.props = d, g.state = k, g.context = l, d = h) : ("function" === typeof g.componentDidMount && (b.flags |= 4), d = !1);
    } else {
      g = b.stateNode;
      rd(a, b);
      h = b.memoizedProps;
      l = b.type === b.elementType ? h : dd(b.type, h);
      g.props = l;
      t = b.pendingProps;
      n = g.context;
      k = c.contextType;
      "object" === typeof k && null !== k ? k = nd(k) : (k = F(c) ? ec : D.current, k = fc(b, k));
      var z = c.getDerivedStateFromProps;
      (p = "function" === typeof z || "function" === typeof g.getSnapshotBeforeUpdate) || "function" !== typeof g.UNSAFE_componentWillReceiveProps && "function" !== typeof g.componentWillReceiveProps || (h !== t || n !== k) && Fd(b, g, d, k);
      od = !1;
      n = b.memoizedState;
      g.state = n;
      vd(b, d, g, e);
      var x = b.memoizedState;
      h !== t || n !== x || E.current || od ? ("function" === typeof z && (zd(b, c, z, d), x = b.memoizedState), (l = od || Dd(b, c, l, d, n, x, k)) ? (p || "function" !== typeof g.UNSAFE_componentWillUpdate && "function" !== typeof g.componentWillUpdate || ("function" === typeof g.componentWillUpdate && g.componentWillUpdate(d, x, k), "function" === typeof g.UNSAFE_componentWillUpdate && g.UNSAFE_componentWillUpdate(d, x, k)), "function" === typeof g.componentDidUpdate && (b.flags |= 4), "function" === typeof g.getSnapshotBeforeUpdate && (b.flags |= 256)) : ("function" !== typeof g.componentDidUpdate || h === a.memoizedProps && n === a.memoizedState || (b.flags |= 4), "function" !== typeof g.getSnapshotBeforeUpdate || h === a.memoizedProps && n === a.memoizedState || (b.flags |= 256), b.memoizedProps = d, b.memoizedState = x), g.props = d, g.state = x, g.context = k, d = l) : ("function" !== typeof g.componentDidUpdate || h === a.memoizedProps && n === a.memoizedState || (b.flags |= 4), "function" !== typeof g.getSnapshotBeforeUpdate || h === a.memoizedProps && n === a.memoizedState || (b.flags |= 256), d = !1);
    }
    return kf(a, b, c, d, f, e);
  }

  function kf(a, b, c, d, e, f) {
    gf(a, b);
    var g = 0 !== (b.flags & 64);
    if (!d && !g) return e && kc(b, c, !1), $e(a, b, f);
    d = b.stateNode;
    Xe.current = b;
    var h = g && "function" !== typeof c.getDerivedStateFromError ? null : d.render();
    b.flags |= 1;
    null !== a && g ? (b.child = Rd(b, a.child, null, f), b.child = Rd(b, null, h, f)) : T(a, b, h, f);
    b.memoizedState = d.state;
    e && kc(b, c, !0);
    return b.child;
  }

  function lf(a) {
    var b = a.stateNode;
    b.pendingContext ? hc(a, b.pendingContext, b.pendingContext !== b.context) : b.context && hc(a, b.context, !1);
    Yd(a, b.containerInfo);
  }

  var mf = {
    dehydrated: null,
    retryLane: 0
  };

  function nf(a, b, c) {
    var d = b.pendingProps,
        e = M.current,
        f = !1,
        g = 0 !== (b.flags & 64),
        h;
    (h = g) || (h = null !== a && null === a.memoizedState ? !1 : 0 !== (e & 2));
    h ? (f = !0, b.flags &= -65) : null !== a && null === a.memoizedState || void 0 === d.fallback || !0 === d.unstable_avoidThisFallback || (e |= 1);
    C(M, e & 1);

    if (null === a) {
      if (void 0 !== d.fallback && (he(b), a = b.memoizedState, null !== a && (a = a.dehydrated, null !== a))) return 0 === (b.mode & 2) ? b.lanes = 1 : Lb(a) ? b.lanes = 256 : b.lanes = 1073741824, null;
      a = d.children;
      var k = d.fallback;
      return f ? (a = of(b, a, k, c), b.child.memoizedState = {
        baseLanes: c
      }, b.memoizedState = mf, a) : "number" === typeof d.unstable_expectedLoadTime ? (a = of(b, a, k, c), b.child.memoizedState = {
        baseLanes: c
      }, b.memoizedState = mf, b.lanes = 33554432, a) : pf(b, a, c);
    }

    e = a.memoizedState;

    if (null !== e) {
      h = e.dehydrated;

      if (null !== h) {
        if (g) {
          if (null !== b.memoizedState) return b.child = a.child, b.flags |= 64, null;
          f = d.fallback;
          k = b.mode;
          d = qf(d.children, k, 0, null);
          f = Qd(f, k, c, null);
          f.flags |= 2;
          d.return = b;
          f.return = b;
          d.sibling = f;
          b.child = d;
          0 !== (b.mode & 2) && Rd(b, a.child, null, c);
          b.child.memoizedState = {
            baseLanes: c
          };
          b.memoizedState = mf;
          return f;
        }

        if (0 !== (U & 64) || 0 === (b.mode & 2) || Lb(h)) b = rf(a, b, c);else if (d = 0 !== (c & a.childLanes), K || d) {
          d = S;

          if (null !== d) {
            pc(c);

            switch (G) {
              case 15:
              case 14:
                k = 0;
                break;

              case 13:
              case 12:
                k = 4;
                break;

              case 11:
              case 10:
                k = 32;
                break;

              case 9:
              case 8:
                k = 256;
                break;

              case 7:
              case 6:
                k = 4096;
                break;

              case 5:
                k = 4096;
                break;

              case 4:
                k = 67108864;
                break;

              case 3:
              case 2:
                k = 134217728;
                break;

              case 1:
              case 0:
                k = 0;
                break;

              default:
                throw Error(q(360, k));
            }

            d = 0 !== (k & (d.suspendedLanes | c)) ? 0 : k;
            0 !== d && d !== e.retryLane && (e.retryLane = d, Bd(a, d, -1));
          }

          sf();
          b = rf(a, b, c);
        } else Kb(h) ? (b.flags |= 64, b.child = a.child, b = tf.bind(null, a), Mb(h, b), b = null) : (A && (de = Nb(h), ie(b), ee = !0), b = pf(b, b.pendingProps.children, c), b.flags |= 1024);
        return b;
      }

      if (f) return d = uf(a, b, d.children, d.fallback, c), f = b.child, k = a.child.memoizedState, f.memoizedState = null === k ? {
        baseLanes: c
      } : {
        baseLanes: k.baseLanes | c
      }, f.childLanes = a.childLanes & ~c, b.memoizedState = mf, d;
      c = vf(a, b, d.children, c);
      b.memoizedState = null;
      return c;
    }

    if (f) return d = uf(a, b, d.children, d.fallback, c), f = b.child, k = a.child.memoizedState, f.memoizedState = null === k ? {
      baseLanes: c
    } : {
      baseLanes: k.baseLanes | c
    }, f.childLanes = a.childLanes & ~c, b.memoizedState = mf, d;
    c = vf(a, b, d.children, c);
    b.memoizedState = null;
    return c;
  }

  function pf(a, b, c) {
    b = qf({
      mode: "visible",
      children: b
    }, a.mode, c, null);
    b.return = a;
    return a.child = b;
  }

  function of(a, b, c, d) {
    var e = a.mode,
        f = a.child;
    b = {
      mode: "hidden",
      children: b
    };
    0 === (e & 2) && null !== f ? (f.childLanes = 0, f.pendingProps = b) : f = qf(b, e, 0, null);
    c = Qd(c, e, d, null);
    f.return = a;
    c.return = a;
    f.sibling = c;
    a.child = f;
    return c;
  }

  function vf(a, b, c, d) {
    var e = a.child;
    a = e.sibling;
    c = Md(e, {
      mode: "visible",
      children: c
    });
    0 === (b.mode & 2) && (c.lanes = d);
    c.return = b;
    c.sibling = null;
    null !== a && (a.nextEffect = null, a.flags = 8, b.firstEffect = b.lastEffect = a);
    return b.child = c;
  }

  function uf(a, b, c, d, e) {
    var f = b.mode,
        g = a.child;
    a = g.sibling;
    var h = {
      mode: "hidden",
      children: c
    };
    0 === (f & 2) && b.child !== g ? (c = b.child, c.childLanes = 0, c.pendingProps = h, g = c.lastEffect, null !== g ? (b.firstEffect = c.firstEffect, b.lastEffect = g, g.nextEffect = null) : b.firstEffect = b.lastEffect = null) : c = Md(g, h);
    null !== a ? d = Md(a, d) : (d = Qd(d, f, e, null), d.flags |= 2);
    d.return = b;
    c.return = b;
    c.sibling = d;
    b.child = c;
    return d;
  }

  function rf(a, b, c) {
    Rd(b, a.child, null, c);
    a = pf(b, b.pendingProps.children, c);
    a.flags |= 2;
    b.memoizedState = null;
    return a;
  }

  function wf(a, b) {
    a.lanes |= b;
    var c = a.alternate;
    null !== c && (c.lanes |= b);
    ld(a.return, b);
  }

  function xf(a, b, c, d, e, f) {
    var g = a.memoizedState;
    null === g ? a.memoizedState = {
      isBackwards: b,
      rendering: null,
      renderingStartTime: 0,
      last: d,
      tail: c,
      tailMode: e,
      lastEffect: f
    } : (g.isBackwards = b, g.rendering = null, g.renderingStartTime = 0, g.last = d, g.tail = c, g.tailMode = e, g.lastEffect = f);
  }

  function yf(a, b, c) {
    var d = b.pendingProps,
        e = d.revealOrder,
        f = d.tail;
    T(a, b, d.children, c);
    d = M.current;
    if (0 !== (d & 2)) d = d & 1 | 2, b.flags |= 64;else {
      if (null !== a && 0 !== (a.flags & 64)) a: for (a = b.child; null !== a;) {
        if (13 === a.tag) null !== a.memoizedState && wf(a, c);else if (19 === a.tag) wf(a, c);else if (null !== a.child) {
          a.child.return = a;
          a = a.child;
          continue;
        }
        if (a === b) break a;

        for (; null === a.sibling;) {
          if (null === a.return || a.return === b) break a;
          a = a.return;
        }

        a.sibling.return = a.return;
        a = a.sibling;
      }
      d &= 1;
    }
    C(M, d);
    if (0 === (b.mode & 2)) b.memoizedState = null;else switch (e) {
      case "forwards":
        c = b.child;

        for (e = null; null !== c;) {
          a = c.alternate, null !== a && null === be(a) && (e = c), c = c.sibling;
        }

        c = e;
        null === c ? (e = b.child, b.child = null) : (e = c.sibling, c.sibling = null);
        xf(b, !1, e, c, f, b.lastEffect);
        break;

      case "backwards":
        c = null;
        e = b.child;

        for (b.child = null; null !== e;) {
          a = e.alternate;

          if (null !== a && null === be(a)) {
            b.child = e;
            break;
          }

          a = e.sibling;
          e.sibling = c;
          c = e;
          e = a;
        }

        xf(b, !0, c, null, f, b.lastEffect);
        break;

      case "together":
        xf(b, !1, null, null, void 0, b.lastEffect);
        break;

      default:
        b.memoizedState = null;
    }
    return b.child;
  }

  function $e(a, b, c) {
    null !== a && (b.dependencies = a.dependencies);
    wd |= b.lanes;

    if (0 !== (c & b.childLanes)) {
      if (null !== a && b.child !== a.child) throw Error(q(153));

      if (null !== b.child) {
        a = b.child;
        c = Md(a, a.pendingProps);
        b.child = c;

        for (c.return = b; null !== a.sibling;) {
          a = a.sibling, c = c.sibling = Md(a, a.pendingProps), c.return = b;
        }

        c.sibling = null;
      }

      return b.child;
    }

    return null;
  }

  function zf(a) {
    a.flags |= 4;
  }

  var _Af, Bf, Cf, Df;

  if (Ua) _Af = function Af(a, b) {
    for (var c = b.child; null !== c;) {
      if (5 === c.tag || 6 === c.tag) La(a, c.stateNode);else if (4 !== c.tag && null !== c.child) {
        c.child.return = c;
        c = c.child;
        continue;
      }
      if (c === b) break;

      for (; null === c.sibling;) {
        if (null === c.return || c.return === b) return;
        c = c.return;
      }

      c.sibling.return = c.return;
      c = c.sibling;
    }
  }, Bf = function Bf() {}, Cf = function Cf(a, b, c, d, e) {
    a = a.memoizedProps;

    if (a !== d) {
      var f = b.stateNode,
          g = Xd(Ud.current);
      c = Na(f, c, a, d, e, g);
      (b.updateQueue = c) && zf(b);
    }
  }, Df = function Df(a, b, c, d) {
    c !== d && zf(b);
  };else if (Va) {
    _Af = function Af(a, b, c, d) {
      for (var e = b.child; null !== e;) {
        if (5 === e.tag) {
          var f = e.stateNode;
          c && d && (f = Fb(f, e.type, e.memoizedProps, e));
          La(a, f);
        } else if (6 === e.tag) f = e.stateNode, c && d && (f = Gb(f, e.memoizedProps, e)), La(a, f);else if (4 !== e.tag) {
          if (13 === e.tag && 0 !== (e.flags & 4) && (f = null !== e.memoizedState)) {
            var g = e.child;

            if (null !== g && (null !== g.child && (g.child.return = g, _Af(a, g, !0, f)), f = g.sibling, null !== f)) {
              f.return = e;
              e = f;
              continue;
            }
          }

          if (null !== e.child) {
            e.child.return = e;
            e = e.child;
            continue;
          }
        }

        if (e === b) break;

        for (; null === e.sibling;) {
          if (null === e.return || e.return === b) return;
          e = e.return;
        }

        e.sibling.return = e.return;
        e = e.sibling;
      }
    };

    var Ef = function Ef(a, b, c, d) {
      for (var e = b.child; null !== e;) {
        if (5 === e.tag) {
          var f = e.stateNode;
          c && d && (f = Fb(f, e.type, e.memoizedProps, e));
          Cb(a, f);
        } else if (6 === e.tag) f = e.stateNode, c && d && (f = Gb(f, e.memoizedProps, e)), Cb(a, f);else if (4 !== e.tag) {
          if (13 === e.tag && 0 !== (e.flags & 4) && (f = null !== e.memoizedState)) {
            var g = e.child;

            if (null !== g && (null !== g.child && (g.child.return = g, Ef(a, g, !0, f)), f = g.sibling, null !== f)) {
              f.return = e;
              e = f;
              continue;
            }
          }

          if (null !== e.child) {
            e.child.return = e;
            e = e.child;
            continue;
          }
        }

        if (e === b) break;

        for (; null === e.sibling;) {
          if (null === e.return || e.return === b) return;
          e = e.return;
        }

        e.sibling.return = e.return;
        e = e.sibling;
      }
    };

    Bf = function Bf(a) {
      var b = a.stateNode;

      if (null !== a.firstEffect) {
        var c = b.containerInfo,
            d = Bb(c);
        Ef(d, a, !1, !1);
        b.pendingChildren = d;
        zf(a);
        Db(c, d);
      }
    };

    Cf = function Cf(a, b, c, d, e) {
      var f = a.stateNode,
          g = a.memoizedProps;
      if ((a = null === b.firstEffect) && g === d) b.stateNode = f;else {
        var h = b.stateNode,
            k = Xd(Ud.current),
            l = null;
        g !== d && (l = Na(h, c, g, d, e, k));
        a && null === l ? b.stateNode = f : (f = Ab(f, l, c, g, d, b, a, h), Ma(f, c, d, e, k) && zf(b), b.stateNode = f, a ? zf(b) : _Af(f, b, !1, !1));
      }
    };

    Df = function Df(a, b, c, d) {
      c !== d ? (a = Xd(Wd.current), c = Xd(Ud.current), b.stateNode = Pa(d, a, c, b), zf(b)) : b.stateNode = a.stateNode;
    };
  } else Bf = function Bf() {}, Cf = function Cf() {}, Df = function Df() {};

  function Ff(a, b) {
    if (!ee) switch (a.tailMode) {
      case "hidden":
        b = a.tail;

        for (var c = null; null !== b;) {
          null !== b.alternate && (c = b), b = b.sibling;
        }

        null === c ? a.tail = null : c.sibling = null;
        break;

      case "collapsed":
        c = a.tail;

        for (var d = null; null !== c;) {
          null !== c.alternate && (d = c), c = c.sibling;
        }

        null === d ? b || null === a.tail ? a.tail = null : a.tail.sibling = null : d.sibling = null;
    }
  }

  function Gf(a, b, c) {
    var d = b.pendingProps;

    switch (b.tag) {
      case 2:
      case 16:
      case 15:
      case 0:
      case 11:
      case 7:
      case 8:
      case 12:
      case 9:
      case 14:
        return null;

      case 1:
        return F(b.type) && gc(), null;

      case 3:
        Zd();
        B(E);
        B(D);
        me();
        d = b.stateNode;
        d.pendingContext && (d.context = d.pendingContext, d.pendingContext = null);
        if (null === a || null === a.child) je(b) ? zf(b) : d.hydrate || (b.flags |= 256);
        Bf(b);
        return null;

      case 5:
        ae(b);
        var e = Xd(Wd.current);
        c = b.type;
        if (null !== a && null != b.stateNode) Cf(a, b, c, d, e), a.ref !== b.ref && (b.flags |= 128);else {
          if (!d) {
            if (null === b.stateNode) throw Error(q(166));
            return null;
          }

          a = Xd(Ud.current);

          if (je(b)) {
            if (!A) throw Error(q(175));
            a = Pb(b.stateNode, b.type, b.memoizedProps, e, a, b);
            b.updateQueue = a;
            null !== a && zf(b);
          } else {
            var f = Ka(c, d, e, a, b);

            _Af(f, b, !1, !1);

            b.stateNode = f;
            Ma(f, c, d, e, a) && zf(b);
          }

          null !== b.ref && (b.flags |= 128);
        }
        return null;

      case 6:
        if (a && null != b.stateNode) Df(a, b, a.memoizedProps, d);else {
          if ("string" !== typeof d && null === b.stateNode) throw Error(q(166));
          a = Xd(Wd.current);
          e = Xd(Ud.current);

          if (je(b)) {
            if (!A) throw Error(q(176));
            Qb(b.stateNode, b.memoizedProps, b) && zf(b);
          } else b.stateNode = Pa(d, a, e, b);
        }
        return null;

      case 13:
        B(M);
        d = b.memoizedState;

        if (null !== d && null !== d.dehydrated) {
          if (null === a) {
            if (!je(b)) throw Error(q(318));
            if (!A) throw Error(q(344));
            a = b.memoizedState;
            a = null !== a ? a.dehydrated : null;
            if (!a) throw Error(q(317));
            Rb(a, b);
          } else ke(), 0 === (b.flags & 64) && (b.memoizedState = null), b.flags |= 4;

          return null;
        }

        if (0 !== (b.flags & 64)) return b.lanes = c, b;
        d = null !== d;
        e = !1;
        null === a ? void 0 !== b.memoizedProps.fallback && je(b) : e = null !== a.memoizedState;
        d && !e && 0 !== (b.mode & 2) && (null === a && !0 !== b.memoizedProps.unstable_avoidThisFallback || 0 !== (M.current & 1) ? 0 === V && (V = 3) : sf());
        Va && d && (b.flags |= 4);
        Ua && (d || e) && (b.flags |= 4);
        return null;

      case 4:
        return Zd(), Bf(b), null === a && ab(b.stateNode.containerInfo), null;

      case 10:
        return kd(b), null;

      case 17:
        return F(b.type) && gc(), null;

      case 19:
        B(M);
        d = b.memoizedState;
        if (null === d) return null;
        e = 0 !== (b.flags & 64);
        f = d.rendering;
        if (null === f) {
          if (e) Ff(d, !1);else {
            if (0 !== V || null !== a && 0 !== (a.flags & 64)) for (a = b.child; null !== a;) {
              f = be(a);

              if (null !== f) {
                b.flags |= 64;
                Ff(d, !1);
                a = f.updateQueue;
                null !== a && (b.updateQueue = a, b.flags |= 4);
                null === d.lastEffect && (b.firstEffect = null);
                b.lastEffect = d.lastEffect;
                a = c;

                for (d = b.child; null !== d;) {
                  e = d, c = a, e.flags &= 2, e.nextEffect = null, e.firstEffect = null, e.lastEffect = null, f = e.alternate, null === f ? (e.childLanes = 0, e.lanes = c, e.child = null, e.memoizedProps = null, e.memoizedState = null, e.updateQueue = null, e.dependencies = null, e.stateNode = null) : (e.childLanes = f.childLanes, e.lanes = f.lanes, e.child = f.child, e.memoizedProps = f.memoizedProps, e.memoizedState = f.memoizedState, e.updateQueue = f.updateQueue, e.type = f.type, c = f.dependencies, e.dependencies = null === c ? null : {
                    lanes: c.lanes,
                    firstContext: c.firstContext
                  }), d = d.sibling;
                }

                C(M, M.current & 1 | 2);
                return b.child;
              }

              a = a.sibling;
            }
            null !== d.tail && H() > Hf && (b.flags |= 64, e = !0, Ff(d, !1), b.lanes = 33554432);
          }
        } else {
          if (!e) if (a = be(f), null !== a) {
            if (b.flags |= 64, e = !0, a = a.updateQueue, null !== a && (b.updateQueue = a, b.flags |= 4), Ff(d, !0), null === d.tail && "hidden" === d.tailMode && !f.alternate && !ee) return b = b.lastEffect = d.lastEffect, null !== b && (b.nextEffect = null), null;
          } else 2 * H() - d.renderingStartTime > Hf && 1073741824 !== c && (b.flags |= 64, e = !0, Ff(d, !1), b.lanes = 33554432);
          d.isBackwards ? (f.sibling = b.child, b.child = f) : (a = d.last, null !== a ? a.sibling = f : b.child = f, d.last = f);
        }
        return null !== d.tail ? (a = d.tail, d.rendering = a, d.tail = a.sibling, d.lastEffect = b.lastEffect, d.renderingStartTime = H(), a.sibling = null, b = M.current, C(M, e ? b & 1 | 2 : b & 1), a) : null;

      case 22:
        return null;

      case 23:
      case 24:
        return If(), null !== a && null !== a.memoizedState !== (null !== b.memoizedState) && "unstable-defer-without-hiding" !== d.mode && (b.flags |= 4), null;
    }

    throw Error(q(156, b.tag));
  }

  function Jf(a) {
    switch (a.tag) {
      case 1:
        F(a.type) && gc();
        var b = a.flags;
        return b & 4096 ? (a.flags = b & -4097 | 64, a) : null;

      case 3:
        Zd();
        B(E);
        B(D);
        me();
        b = a.flags;
        if (0 !== (b & 64)) throw Error(q(285));
        a.flags = b & -4097 | 64;
        return a;

      case 5:
        return ae(a), null;

      case 13:
        B(M);
        b = a.memoizedState;

        if (null !== b && null !== b.dehydrated) {
          if (null === a.alternate) throw Error(q(340));
          ke();
        }

        b = a.flags;
        return b & 4096 ? (a.flags = b & -4097 | 64, a) : null;

      case 19:
        return B(M), null;

      case 4:
        return Zd(), null;

      case 10:
        return kd(a), null;

      case 23:
      case 24:
        return If(), null;

      default:
        return null;
    }
  }

  function Kf(a, b) {
    try {
      var c = "",
          d = b;

      do {
        c += cd(d), d = d.return;
      } while (d);

      var e = c;
    } catch (f) {
      e = "\nError generating stack: " + f.message + "\n" + f.stack;
    }

    return {
      value: a,
      source: b,
      stack: e
    };
  }

  function Lf(a, b) {
    try {
      console.error(b.value);
    } catch (c) {
      setTimeout(function () {
        throw c;
      });
    }
  }

  var Mf = "function" === typeof WeakMap ? WeakMap : Map;

  function Nf(a, b, c) {
    c = sd(-1, c);
    c.tag = 3;
    c.payload = {
      element: null
    };
    var d = b.value;

    c.callback = function () {
      Of || (Of = !0, Pf = d);
      Lf(a, b);
    };

    return c;
  }

  function Qf(a, b, c) {
    c = sd(-1, c);
    c.tag = 3;
    var d = a.type.getDerivedStateFromError;

    if ("function" === typeof d) {
      var e = b.value;

      c.payload = function () {
        Lf(a, b);
        return d(e);
      };
    }

    var f = a.stateNode;
    null !== f && "function" === typeof f.componentDidCatch && (c.callback = function () {
      "function" !== typeof d && (null === Rf ? Rf = new Set([this]) : Rf.add(this), Lf(a, b));
      var c = b.stack;
      this.componentDidCatch(b.value, {
        componentStack: null !== c ? c : ""
      });
    });
    return c;
  }

  var Sf = "function" === typeof WeakSet ? WeakSet : Set;

  function Tf(a) {
    var b = a.ref;
    if (null !== b) if ("function" === typeof b) try {
      b(null);
    } catch (c) {
      Uf(a, c);
    } else b.current = null;
  }

  function Vf(a, b) {
    switch (b.tag) {
      case 0:
      case 11:
      case 15:
      case 22:
        return;

      case 1:
        if (b.flags & 256 && null !== a) {
          var c = a.memoizedProps,
              d = a.memoizedState;
          a = b.stateNode;
          b = a.getSnapshotBeforeUpdate(b.elementType === b.type ? c : dd(b.type, c), d);
          a.__reactInternalSnapshotBeforeUpdate = b;
        }

        return;

      case 3:
        Ua && b.flags & 256 && xb(b.stateNode.containerInfo);
        return;

      case 5:
      case 6:
      case 4:
      case 17:
        return;
    }

    throw Error(q(163));
  }

  function Wf(a, b) {
    b = b.updateQueue;
    b = null !== b ? b.lastEffect : null;

    if (null !== b) {
      var c = b = b.next;

      do {
        if ((c.tag & a) === a) {
          var d = c.destroy;
          c.destroy = void 0;
          void 0 !== d && d();
        }

        c = c.next;
      } while (c !== b);
    }
  }

  function Xf(a, b, c) {
    switch (c.tag) {
      case 0:
      case 11:
      case 15:
      case 22:
        b = c.updateQueue;
        b = null !== b ? b.lastEffect : null;

        if (null !== b) {
          a = b = b.next;

          do {
            if (3 === (a.tag & 3)) {
              var d = a.create;
              a.destroy = d();
            }

            a = a.next;
          } while (a !== b);
        }

        b = c.updateQueue;
        b = null !== b ? b.lastEffect : null;

        if (null !== b) {
          a = b = b.next;

          do {
            var e = a;
            d = e.next;
            e = e.tag;
            0 !== (e & 4) && 0 !== (e & 1) && (Yf(c, a), Zf(c, a));
            a = d;
          } while (a !== b);
        }

        return;

      case 1:
        a = c.stateNode;
        c.flags & 4 && (null === b ? a.componentDidMount() : (d = c.elementType === c.type ? b.memoizedProps : dd(c.type, b.memoizedProps), a.componentDidUpdate(d, b.memoizedState, a.__reactInternalSnapshotBeforeUpdate)));
        b = c.updateQueue;
        null !== b && xd(c, b, a);
        return;

      case 3:
        b = c.updateQueue;

        if (null !== b) {
          a = null;
          if (null !== c.child) switch (c.child.tag) {
            case 5:
              a = Ea(c.child.stateNode);
              break;

            case 1:
              a = c.child.stateNode;
          }
          xd(c, b, a);
        }

        return;

      case 5:
        a = c.stateNode;
        null === b && c.flags & 4 && mb(a, c.type, c.memoizedProps, c);
        return;

      case 6:
        return;

      case 4:
        return;

      case 12:
        return;

      case 13:
        A && null === c.memoizedState && (c = c.alternate, null !== c && (c = c.memoizedState, null !== c && (c = c.dehydrated, null !== c && Ub(c))));
        return;

      case 19:
      case 17:
      case 20:
      case 21:
      case 23:
      case 24:
        return;
    }

    throw Error(q(163));
  }

  function $f(a, b) {
    if (Ua) for (var c = a;;) {
      if (5 === c.tag) {
        var d = c.stateNode;
        b ? tb(d) : vb(c.stateNode, c.memoizedProps);
      } else if (6 === c.tag) d = c.stateNode, b ? ub(d) : wb(d, c.memoizedProps);else if ((23 !== c.tag && 24 !== c.tag || null === c.memoizedState || c === a) && null !== c.child) {
        c.child.return = c;
        c = c.child;
        continue;
      }

      if (c === a) break;

      for (; null === c.sibling;) {
        if (null === c.return || c.return === a) return;
        c = c.return;
      }

      c.sibling.return = c.return;
      c = c.sibling;
    }
  }

  function ag(a, b) {
    if (mc && "function" === typeof mc.onCommitFiberUnmount) try {
      mc.onCommitFiberUnmount(lc, b);
    } catch (f) {}

    switch (b.tag) {
      case 0:
      case 11:
      case 14:
      case 15:
      case 22:
        a = b.updateQueue;

        if (null !== a && (a = a.lastEffect, null !== a)) {
          var c = a = a.next;

          do {
            var d = c,
                e = d.destroy;
            d = d.tag;
            if (void 0 !== e) if (0 !== (d & 4)) Yf(b, c);else {
              d = b;

              try {
                e();
              } catch (f) {
                Uf(d, f);
              }
            }
            c = c.next;
          } while (c !== a);
        }

        break;

      case 1:
        Tf(b);
        a = b.stateNode;
        if ("function" === typeof a.componentWillUnmount) try {
          a.props = b.memoizedProps, a.state = b.memoizedState, a.componentWillUnmount();
        } catch (f) {
          Uf(b, f);
        }
        break;

      case 5:
        Tf(b);
        break;

      case 4:
        Ua ? bg(a, b) : Va && Va && (b = b.stateNode.containerInfo, a = Bb(b), Eb(b, a));
    }
  }

  function cg(a, b) {
    for (var c = b;;) {
      if (ag(a, c), null === c.child || Ua && 4 === c.tag) {
        if (c === b) break;

        for (; null === c.sibling;) {
          if (null === c.return || c.return === b) return;
          c = c.return;
        }

        c.sibling.return = c.return;
        c = c.sibling;
      } else c.child.return = c, c = c.child;
    }
  }

  function dg(a) {
    a.alternate = null;
    a.child = null;
    a.dependencies = null;
    a.firstEffect = null;
    a.lastEffect = null;
    a.memoizedProps = null;
    a.memoizedState = null;
    a.pendingProps = null;
    a.return = null;
    a.updateQueue = null;
  }

  function eg(a) {
    return 5 === a.tag || 3 === a.tag || 4 === a.tag;
  }

  function fg(a) {
    if (Ua) {
      a: {
        for (var b = a.return; null !== b;) {
          if (eg(b)) break a;
          b = b.return;
        }

        throw Error(q(160));
      }

      var c = b;
      b = c.stateNode;

      switch (c.tag) {
        case 5:
          var d = !1;
          break;

        case 3:
          b = b.containerInfo;
          d = !0;
          break;

        case 4:
          b = b.containerInfo;
          d = !0;
          break;

        default:
          throw Error(q(161));
      }

      c.flags & 16 && (sb(b), c.flags &= -17);

      a: b: for (c = a;;) {
        for (; null === c.sibling;) {
          if (null === c.return || eg(c.return)) {
            c = null;
            break a;
          }

          c = c.return;
        }

        c.sibling.return = c.return;

        for (c = c.sibling; 5 !== c.tag && 6 !== c.tag && 18 !== c.tag;) {
          if (c.flags & 2) continue b;
          if (null === c.child || 4 === c.tag) continue b;else c.child.return = c, c = c.child;
        }

        if (!(c.flags & 2)) {
          c = c.stateNode;
          break a;
        }
      }

      d ? gg(a, c, b) : hg(a, c, b);
    }
  }

  function gg(a, b, c) {
    var d = a.tag,
        e = 5 === d || 6 === d;
    if (e) a = e ? a.stateNode : a.stateNode.instance, b ? pb(c, a, b) : kb(c, a);else if (4 !== d && (a = a.child, null !== a)) for (gg(a, b, c), a = a.sibling; null !== a;) {
      gg(a, b, c), a = a.sibling;
    }
  }

  function hg(a, b, c) {
    var d = a.tag,
        e = 5 === d || 6 === d;
    if (e) a = e ? a.stateNode : a.stateNode.instance, b ? ob(c, a, b) : jb(c, a);else if (4 !== d && (a = a.child, null !== a)) for (hg(a, b, c), a = a.sibling; null !== a;) {
      hg(a, b, c), a = a.sibling;
    }
  }

  function bg(a, b) {
    for (var c = b, d = !1, e, f;;) {
      if (!d) {
        d = c.return;

        a: for (;;) {
          if (null === d) throw Error(q(160));
          e = d.stateNode;

          switch (d.tag) {
            case 5:
              f = !1;
              break a;

            case 3:
              e = e.containerInfo;
              f = !0;
              break a;

            case 4:
              e = e.containerInfo;
              f = !0;
              break a;
          }

          d = d.return;
        }

        d = !0;
      }

      if (5 === c.tag || 6 === c.tag) cg(a, c), f ? rb(e, c.stateNode) : qb(e, c.stateNode);else if (18 === c.tag) f ? Wb(e, c.stateNode) : Vb(e, c.stateNode);else if (4 === c.tag) {
        if (null !== c.child) {
          e = c.stateNode.containerInfo;
          f = !0;
          c.child.return = c;
          c = c.child;
          continue;
        }
      } else if (ag(a, c), null !== c.child) {
        c.child.return = c;
        c = c.child;
        continue;
      }
      if (c === b) break;

      for (; null === c.sibling;) {
        if (null === c.return || c.return === b) return;
        c = c.return;
        4 === c.tag && (d = !1);
      }

      c.sibling.return = c.return;
      c = c.sibling;
    }
  }

  function ig(a, b) {
    if (Ua) {
      switch (b.tag) {
        case 0:
        case 11:
        case 14:
        case 15:
        case 22:
          Wf(3, b);
          return;

        case 1:
          return;

        case 5:
          var c = b.stateNode;

          if (null != c) {
            var d = b.memoizedProps;
            a = null !== a ? a.memoizedProps : d;
            var e = b.type,
                f = b.updateQueue;
            b.updateQueue = null;
            null !== f && nb(c, f, e, a, d, b);
          }

          return;

        case 6:
          if (null === b.stateNode) throw Error(q(162));
          c = b.memoizedProps;
          lb(b.stateNode, null !== a ? a.memoizedProps : c, c);
          return;

        case 3:
          A && (b = b.stateNode, b.hydrate && (b.hydrate = !1, Tb(b.containerInfo)));
          return;

        case 12:
          return;

        case 13:
          jg(b);
          kg(b);
          return;

        case 19:
          kg(b);
          return;

        case 17:
          return;

        case 23:
        case 24:
          $f(b, null !== b.memoizedState);
          return;
      }

      throw Error(q(163));
    }

    switch (b.tag) {
      case 0:
      case 11:
      case 14:
      case 15:
      case 22:
        Wf(3, b);
        return;

      case 12:
        return;

      case 13:
        jg(b);
        kg(b);
        return;

      case 19:
        kg(b);
        return;

      case 3:
        A && (c = b.stateNode, c.hydrate && (c.hydrate = !1, Tb(c.containerInfo)));
        break;

      case 23:
      case 24:
        return;
    }

    a: if (Va) {
      switch (b.tag) {
        case 1:
        case 5:
        case 6:
        case 20:
          break a;

        case 3:
        case 4:
          b = b.stateNode;
          Eb(b.containerInfo, b.pendingChildren);
          break a;
      }

      throw Error(q(163));
    }
  }

  function jg(a) {
    null !== a.memoizedState && (lg = H(), Ua && $f(a.child, !0));
  }

  function kg(a) {
    var b = a.updateQueue;

    if (null !== b) {
      a.updateQueue = null;
      var c = a.stateNode;
      null === c && (c = a.stateNode = new Sf());
      b.forEach(function (b) {
        var d = mg.bind(null, a, b);
        c.has(b) || (c.add(b), b.then(d, d));
      });
    }
  }

  function ng(a, b) {
    return null !== a && (a = a.memoizedState, null === a || null !== a.dehydrated) ? (b = b.memoizedState, null !== b && null === b.dehydrated) : !1;
  }

  var og = 0,
      pg = 1,
      qg = 2,
      rg = 3,
      sg = 4;

  if ("function" === typeof Symbol && Symbol.for) {
    var tg = Symbol.for;
    og = tg("selector.component");
    pg = tg("selector.has_pseudo_class");
    qg = tg("selector.role");
    rg = tg("selector.test_id");
    sg = tg("selector.text");
  }

  function ug(a) {
    var b = Wa(a);

    if (null != b) {
      if ("string" !== typeof b.memoizedProps["data-testname"]) throw Error(q(364));
      return b;
    }

    a = cb(a);
    if (null === a) throw Error(q(362));
    return a.stateNode.current;
  }

  function vg(a, b) {
    switch (b.$$typeof) {
      case og:
        if (a.type === b.value) return !0;
        break;

      case pg:
        a: {
          b = b.value;
          a = [a, 0];

          for (var c = 0; c < a.length;) {
            var d = a[c++],
                e = a[c++],
                f = b[e];

            if (5 !== d.tag || !fb(d)) {
              for (; null != f && vg(d, f);) {
                e++, f = b[e];
              }

              if (e === b.length) {
                b = !0;
                break a;
              } else for (d = d.child; null !== d;) {
                a.push(d, e), d = d.sibling;
              }
            }
          }

          b = !1;
        }

        return b;

      case qg:
        if (5 === a.tag && gb(a.stateNode, b.value)) return !0;
        break;

      case sg:
        if (5 === a.tag || 6 === a.tag) if (a = eb(a), null !== a && 0 <= a.indexOf(b.value)) return !0;
        break;

      case rg:
        if (5 === a.tag && (a = a.memoizedProps["data-testname"], "string" === typeof a && a.toLowerCase() === b.value.toLowerCase())) return !0;
        break;

      default:
        throw Error(q(365, b));
    }

    return !1;
  }

  function wg(a) {
    switch (a.$$typeof) {
      case og:
        return "<" + (xa(a.value) || "Unknown") + ">";

      case pg:
        return ":has(" + (wg(a) || "") + ")";

      case qg:
        return '[role="' + a.value + '"]';

      case sg:
        return '"' + a.value + '"';

      case rg:
        return '[data-testname="' + a.value + '"]';

      default:
        throw Error(q(365, a));
    }
  }

  function xg(a, b) {
    var c = [];
    a = [a, 0];

    for (var d = 0; d < a.length;) {
      var e = a[d++],
          f = a[d++],
          g = b[f];

      if (5 !== e.tag || !fb(e)) {
        for (; null != g && vg(e, g);) {
          f++, g = b[f];
        }

        if (f === b.length) c.push(e);else for (e = e.child; null !== e;) {
          a.push(e, f), e = e.sibling;
        }
      }
    }

    return c;
  }

  function yg(a, b) {
    if (!bb) throw Error(q(363));
    a = ug(a);
    a = xg(a, b);
    b = [];
    a = Array.from(a);

    for (var c = 0; c < a.length;) {
      var d = a[c++];
      if (5 === d.tag) fb(d) || b.push(d.stateNode);else for (d = d.child; null !== d;) {
        a.push(d), d = d.sibling;
      }
    }

    return b;
  }

  var zg = null;

  function Ag(a) {
    if (null === zg) try {
      var b = ("require" + Math.random()).slice(0, 7);
      zg = (module && module[b]).call(module, "timers").setImmediate;
    } catch (c) {
      zg = function zg(a) {
        var b = new MessageChannel();
        b.port1.onmessage = a;
        b.port2.postMessage(void 0);
      };
    }
    return zg(a);
  }

  var Bg = Math.ceil,
      Cg = da.ReactCurrentDispatcher,
      Dg = da.ReactCurrentOwner,
      Eg = da.IsSomeRendererActing,
      U = 0,
      S = null,
      W = null,
      X = 0,
      Fg = 0,
      Gg = cc(0),
      V = 0,
      Hg = null,
      Ig = 0,
      wd = 0,
      Jg = 0,
      Kg = 0,
      Lg = null,
      lg = 0,
      Hf = Infinity;

  function Mg() {
    Hf = H() + 500;
  }

  var Y = null,
      Of = !1,
      Pf = null,
      Rf = null,
      Ng = !1,
      Og = null,
      Pg = 90,
      Qg = [],
      Rg = [],
      Sg = null,
      Tg = 0,
      Ug = null,
      Vg = -1,
      Wg = 0,
      Xg = 0,
      Yg = null,
      Zg = !1;

  function L() {
    return 0 !== (U & 48) ? H() : -1 !== Vg ? Vg : Vg = H();
  }

  function Ad(a) {
    a = a.mode;
    if (0 === (a & 2)) return 1;
    if (0 === (a & 4)) return 99 === Uc() ? 1 : 2;
    0 === Wg && (Wg = Ig);

    if (0 !== Zc.transition) {
      0 !== Xg && (Xg = null !== Lg ? Lg.pendingLanes : 0);
      a = Wg;
      var b = 4186112 & ~Xg;
      b &= -b;
      0 === b && (a = 4186112 & ~a, b = a & -a, 0 === b && (b = 8192));
      return b;
    }

    a = Uc();
    0 !== (U & 4) && 98 === a ? a = vc(12, Wg) : (a = qc(a), a = vc(a, Wg));
    return a;
  }

  function Bd(a, b, c) {
    if (50 < Tg) throw Tg = 0, Ug = null, Error(q(185));
    a = $g(a, b);
    if (null === a) return null;
    yc(a, b, c);
    a === S && (Jg |= b, 4 === V && ah(a, X));
    var d = Uc();
    1 === b ? 0 !== (U & 8) && 0 === (U & 48) ? bh(a) : (Z(a, c), 0 === U && (Mg(), I())) : (0 === (U & 4) || 98 !== d && 99 !== d || (null === Sg ? Sg = new Set([a]) : Sg.add(a)), Z(a, c));
    Lg = a;
  }

  function $g(a, b) {
    a.lanes |= b;
    var c = a.alternate;
    null !== c && (c.lanes |= b);
    c = a;

    for (a = a.return; null !== a;) {
      a.childLanes |= b, c = a.alternate, null !== c && (c.childLanes |= b), c = a, a = a.return;
    }

    return 3 === c.tag ? c.stateNode : null;
  }

  function Z(a, b) {
    for (var c = a.callbackNode, d = a.suspendedLanes, e = a.pingedLanes, f = a.expirationTimes, g = a.pendingLanes; 0 < g;) {
      var h = 31 - tc(g),
          k = 1 << h,
          l = f[h];

      if (-1 === l) {
        if (0 === (k & d) || 0 !== (k & e)) {
          l = b;
          pc(k);
          var p = G;
          f[h] = 10 <= p ? l + 250 : 6 <= p ? l + 5E3 : -1;
        }
      } else l <= b && (a.expiredLanes |= k);

      g &= ~k;
    }

    d = sc(a, a === S ? X : 0);
    b = G;
    if (0 === d) null !== c && (c !== Oc && Ec(c), a.callbackNode = null, a.callbackPriority = 0);else {
      if (null !== c) {
        if (a.callbackPriority === b) return;
        c !== Oc && Ec(c);
      }

      15 === b ? (c = bh.bind(null, a), null === Qc ? (Qc = [c], Rc = Dc(Jc, Yc)) : Qc.push(c), c = Oc) : 14 === b ? c = Xc(99, bh.bind(null, a)) : (c = rc(b), c = Xc(c, ch.bind(null, a)));
      a.callbackPriority = b;
      a.callbackNode = c;
    }
  }

  function ch(a) {
    Vg = -1;
    Xg = Wg = 0;
    if (0 !== (U & 48)) throw Error(q(327));
    var b = a.callbackNode;
    if (dh() && a.callbackNode !== b) return null;
    var c = sc(a, a === S ? X : 0);
    if (0 === c) return null;
    var d = c;
    var e = U;
    U |= 16;
    var f = eh();
    if (S !== a || X !== d) Mg(), fh(a, d);

    do {
      try {
        gh();
        break;
      } catch (h) {
        hh(a, h);
      }
    } while (1);

    id();
    Cg.current = f;
    U = e;
    null !== W ? d = 0 : (S = null, X = 0, d = V);
    if (0 !== (Ig & Jg)) fh(a, 0);else if (0 !== d) {
      2 === d && (U |= 64, a.hydrate && (a.hydrate = !1, xb(a.containerInfo)), c = uc(a), 0 !== c && (d = ih(a, c)));
      if (1 === d) throw b = Hg, fh(a, 0), ah(a, c), Z(a, H()), b;
      a.finishedWork = a.current.alternate;
      a.finishedLanes = c;

      switch (d) {
        case 0:
        case 1:
          throw Error(q(345));

        case 2:
          nh(a);
          break;

        case 3:
          ah(a, c);

          if ((c & 62914560) === c && (d = lg + 500 - H(), 10 < d)) {
            if (0 !== sc(a, 0)) break;
            e = a.suspendedLanes;

            if ((e & c) !== c) {
              L();
              a.pingedLanes |= a.suspendedLanes & e;
              break;
            }

            a.timeoutHandle = Qa(nh.bind(null, a), d);
            break;
          }

          nh(a);
          break;

        case 4:
          ah(a, c);
          if ((c & 4186112) === c) break;
          d = a.eventTimes;

          for (e = -1; 0 < c;) {
            var g = 31 - tc(c);
            f = 1 << g;
            g = d[g];
            g > e && (e = g);
            c &= ~f;
          }

          c = e;
          c = H() - c;
          c = (120 > c ? 120 : 480 > c ? 480 : 1080 > c ? 1080 : 1920 > c ? 1920 : 3E3 > c ? 3E3 : 4320 > c ? 4320 : 1960 * Bg(c / 1960)) - c;

          if (10 < c) {
            a.timeoutHandle = Qa(nh.bind(null, a), c);
            break;
          }

          nh(a);
          break;

        case 5:
          nh(a);
          break;

        default:
          throw Error(q(329));
      }
    }
    Z(a, H());
    return a.callbackNode === b ? ch.bind(null, a) : null;
  }

  function ah(a, b) {
    b &= ~Kg;
    b &= ~Jg;
    a.suspendedLanes |= b;
    a.pingedLanes &= ~b;

    for (a = a.expirationTimes; 0 < b;) {
      var c = 31 - tc(b),
          d = 1 << c;
      a[c] = -1;
      b &= ~d;
    }
  }

  function bh(a) {
    if (0 !== (U & 48)) throw Error(q(327));
    dh();

    if (a === S && 0 !== (a.expiredLanes & X)) {
      var b = X;
      var c = ih(a, b);
      0 !== (Ig & Jg) && (b = sc(a, b), c = ih(a, b));
    } else b = sc(a, 0), c = ih(a, b);

    0 !== a.tag && 2 === c && (U |= 64, a.hydrate && (a.hydrate = !1, xb(a.containerInfo)), b = uc(a), 0 !== b && (c = ih(a, b)));
    if (1 === c) throw c = Hg, fh(a, 0), ah(a, b), Z(a, H()), c;
    a.finishedWork = a.current.alternate;
    a.finishedLanes = b;
    nh(a);
    Z(a, H());
    return null;
  }

  function oh() {
    if (null !== Sg) {
      var a = Sg;
      Sg = null;
      a.forEach(function (a) {
        a.expiredLanes |= 24 & a.pendingLanes;
        Z(a, H());
      });
    }

    I();
  }

  function ph(a, b) {
    var c = U;
    U |= 1;

    try {
      return a(b);
    } finally {
      U = c, 0 === U && (Mg(), I());
    }
  }

  function qh(a, b) {
    var c = U;
    if (0 !== (c & 48)) return a(b);
    U |= 1;

    try {
      if (a) return Wc(99, a.bind(null, b));
    } finally {
      U = c, I();
    }
  }

  function ff(a, b) {
    C(Gg, Fg);
    Fg |= b;
    Ig |= b;
  }

  function If() {
    Fg = Gg.current;
    B(Gg);
  }

  function fh(a, b) {
    a.finishedWork = null;
    a.finishedLanes = 0;
    var c = a.timeoutHandle;
    c !== Sa && (a.timeoutHandle = Sa, Ra(c));
    if (null !== W) for (c = W.return; null !== c;) {
      var d = c;

      switch (d.tag) {
        case 1:
          d = d.type.childContextTypes;
          null !== d && void 0 !== d && gc();
          break;

        case 3:
          Zd();
          B(E);
          B(D);
          me();
          break;

        case 5:
          ae(d);
          break;

        case 4:
          Zd();
          break;

        case 13:
          B(M);
          break;

        case 19:
          B(M);
          break;

        case 10:
          kd(d);
          break;

        case 23:
        case 24:
          If();
      }

      c = c.return;
    }
    S = a;
    W = Md(a.current, null);
    X = Fg = Ig = b;
    V = 0;
    Hg = null;
    Kg = Jg = wd = 0;
  }

  function hh(a, b) {
    do {
      var c = W;

      try {
        id();
        ne.current = xe;

        if (qe) {
          for (var d = O.memoizedState; null !== d;) {
            var e = d.queue;
            null !== e && (e.pending = null);
            d = d.next;
          }

          qe = !1;
        }

        pe = 0;
        Q = P = O = null;
        re = !1;
        Dg.current = null;

        if (null === c || null === c.return) {
          V = 1;
          Hg = b;
          W = null;
          break;
        }

        a: {
          var f = a,
              g = c.return,
              h = c,
              k = b;
          b = X;
          h.flags |= 2048;
          h.firstEffect = h.lastEffect = null;

          if (null !== k && "object" === typeof k && "function" === typeof k.then) {
            var l = k;

            if (0 === (h.mode & 2)) {
              var p = h.alternate;
              p ? (h.updateQueue = p.updateQueue, h.memoizedState = p.memoizedState, h.lanes = p.lanes) : (h.updateQueue = null, h.memoizedState = null);
            }

            var t = 0 !== (M.current & 1),
                n = g;

            do {
              var z;

              if (z = 13 === n.tag) {
                var x = n.memoizedState;
                if (null !== x) z = null !== x.dehydrated ? !0 : !1;else {
                  var ba = n.memoizedProps;
                  z = void 0 === ba.fallback ? !1 : !0 !== ba.unstable_avoidThisFallback ? !0 : t ? !1 : !0;
                }
              }

              if (z) {
                var Ha = n.updateQueue;

                if (null === Ha) {
                  var y = new Set();
                  y.add(l);
                  n.updateQueue = y;
                } else Ha.add(l);

                if (0 === (n.mode & 2)) {
                  n.flags |= 64;
                  h.flags |= 16384;
                  h.flags &= -2981;
                  if (1 === h.tag) if (null === h.alternate) h.tag = 17;else {
                    var v = sd(-1, 1);
                    v.tag = 2;
                    td(h, v);
                  }
                  h.lanes |= 1;
                  break a;
                }

                k = void 0;
                h = b;
                var u = f.pingCache;
                null === u ? (u = f.pingCache = new Mf(), k = new Set(), u.set(l, k)) : (k = u.get(l), void 0 === k && (k = new Set(), u.set(l, k)));

                if (!k.has(h)) {
                  k.add(h);
                  var Ye = rh.bind(null, f, l, h);
                  l.then(Ye, Ye);
                }

                n.flags |= 4096;
                n.lanes = b;
                break a;
              }

              n = n.return;
            } while (null !== n);

            k = Error((xa(h.type) || "A React component") + " suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display.");
          }

          5 !== V && (V = 2);
          k = Kf(k, h);
          n = g;

          do {
            switch (n.tag) {
              case 3:
                f = k;
                n.flags |= 4096;
                b &= -b;
                n.lanes |= b;
                var jh = Nf(n, f, b);
                ud(n, jh);
                break a;

              case 1:
                f = k;
                var kh = n.type,
                    qd = n.stateNode;

                if (0 === (n.flags & 64) && ("function" === typeof kh.getDerivedStateFromError || null !== qd && "function" === typeof qd.componentDidCatch && (null === Rf || !Rf.has(qd)))) {
                  n.flags |= 4096;
                  b &= -b;
                  n.lanes |= b;
                  var lh = Qf(n, f, b);
                  ud(n, lh);
                  break a;
                }

            }

            n = n.return;
          } while (null !== n);
        }

        sh(c);
      } catch (mh) {
        b = mh;
        W === c && null !== c && (W = c = c.return);
        continue;
      }

      break;
    } while (1);
  }

  function eh() {
    var a = Cg.current;
    Cg.current = xe;
    return null === a ? xe : a;
  }

  function sf() {
    if (0 === V || 3 === V) V = 4;
    null === S || 0 === (wd & 134217727) && 0 === (Jg & 134217727) || ah(S, X);
  }

  function ih(a, b) {
    var c = U;
    U |= 16;
    var d = eh();
    S === a && X === b || fh(a, b);

    do {
      try {
        th();
        break;
      } catch (e) {
        hh(a, e);
      }
    } while (1);

    id();
    U = c;
    Cg.current = d;
    if (null !== W) throw Error(q(261));
    S = null;
    X = 0;
    return V;
  }

  function th() {
    for (; null !== W;) {
      uh(W);
    }
  }

  function gh() {
    for (; null !== W && !Fc();) {
      uh(W);
    }
  }

  function uh(a) {
    var b = vh(a.alternate, a, Fg);
    a.memoizedProps = a.pendingProps;
    null === b ? sh(a) : W = b;
    Dg.current = null;
  }

  function sh(a) {
    var b = a;

    do {
      var c = b.alternate;
      a = b.return;

      if (0 === (b.flags & 2048)) {
        c = Gf(c, b, Fg);

        if (null !== c) {
          W = c;
          return;
        }

        c = b;

        if (24 !== c.tag && 23 !== c.tag || null === c.memoizedState || 0 !== (Fg & 1073741824) || 0 === (c.mode & 4)) {
          for (var d = 0, e = c.child; null !== e;) {
            d |= e.lanes | e.childLanes, e = e.sibling;
          }

          c.childLanes = d;
        }

        null !== a && 0 === (a.flags & 2048) && (null === a.firstEffect && (a.firstEffect = b.firstEffect), null !== b.lastEffect && (null !== a.lastEffect && (a.lastEffect.nextEffect = b.firstEffect), a.lastEffect = b.lastEffect), 1 < b.flags && (null !== a.lastEffect ? a.lastEffect.nextEffect = b : a.firstEffect = b, a.lastEffect = b));
      } else {
        c = Jf(b);

        if (null !== c) {
          c.flags &= 2047;
          W = c;
          return;
        }

        null !== a && (a.firstEffect = a.lastEffect = null, a.flags |= 2048);
      }

      b = b.sibling;

      if (null !== b) {
        W = b;
        return;
      }

      W = b = a;
    } while (null !== b);

    0 === V && (V = 5);
  }

  function nh(a) {
    var b = Uc();
    Wc(99, wh.bind(null, a, b));
    return null;
  }

  function wh(a, b) {
    do {
      dh();
    } while (null !== Og);

    if (0 !== (U & 48)) throw Error(q(327));
    var c = a.finishedWork;
    if (null === c) return null;
    a.finishedWork = null;
    a.finishedLanes = 0;
    if (c === a.current) throw Error(q(177));
    a.callbackNode = null;
    var d = c.lanes | c.childLanes,
        e = d,
        f = a.pendingLanes & ~e;
    a.pendingLanes = e;
    a.suspendedLanes = 0;
    a.pingedLanes = 0;
    a.expiredLanes &= e;
    a.mutableReadLanes &= e;
    a.entangledLanes &= e;
    e = a.entanglements;

    for (var g = a.eventTimes, h = a.expirationTimes; 0 < f;) {
      var k = 31 - tc(f),
          l = 1 << k;
      e[k] = 0;
      g[k] = -1;
      h[k] = -1;
      f &= ~l;
    }

    null !== Sg && 0 === (d & 24) && Sg.has(a) && Sg.delete(a);
    a === S && (W = S = null, X = 0);
    1 < c.flags ? null !== c.lastEffect ? (c.lastEffect.nextEffect = c, d = c.firstEffect) : d = c : d = c.firstEffect;

    if (null !== d) {
      e = U;
      U |= 32;
      Dg.current = null;
      Yg = Ia(a.containerInfo);
      Zg = !1;
      Y = d;

      do {
        try {
          xh();
        } catch (y) {
          if (null === Y) throw Error(q(330));
          Uf(Y, y);
          Y = Y.nextEffect;
        }
      } while (null !== Y);

      Yg = null;
      Y = d;

      do {
        try {
          g = a;

          for ("function" === typeof yb && yb(); null !== Y;) {
            var p = Y.flags;
            p & 16 && Ua && sb(Y.stateNode);

            if (p & 128) {
              var t = Y.alternate;

              if (null !== t) {
                var n = t.ref;
                null !== n && ("function" === typeof n ? n(null) : n.current = null);
              }
            }

            switch (p & 1038) {
              case 2:
                fg(Y);
                Y.flags &= -3;
                break;

              case 6:
                fg(Y);
                Y.flags &= -3;
                ig(Y.alternate, Y);
                break;

              case 1024:
                Y.flags &= -1025;
                break;

              case 1028:
                Y.flags &= -1025;
                ig(Y.alternate, Y);
                break;

              case 4:
                ig(Y.alternate, Y);
                break;

              case 8:
                h = g;
                f = Y;
                Ua ? bg(h, f) : cg(h, f);
                var z = f.alternate;
                dg(f);
                null !== z && dg(z);
            }

            Y = Y.nextEffect;
          }

          "function" === typeof zb && zb();
        } catch (y) {
          if (null === Y) throw Error(q(330));
          Uf(Y, y);
          Y = Y.nextEffect;
        }
      } while (null !== Y);

      Zg && $a();
      Ja(a.containerInfo);
      a.current = c;
      Y = d;

      do {
        try {
          for (p = a; null !== Y;) {
            var x = Y.flags;
            x & 36 && Xf(p, Y.alternate, Y);

            if (x & 128) {
              t = void 0;
              var ba = Y.ref;

              if (null !== ba) {
                var Ha = Y.stateNode;

                switch (Y.tag) {
                  case 5:
                    t = Ea(Ha);
                    break;

                  default:
                    t = Ha;
                }

                "function" === typeof ba ? ba(t) : ba.current = t;
              }
            }

            Y = Y.nextEffect;
          }
        } catch (y) {
          if (null === Y) throw Error(q(330));
          Uf(Y, y);
          Y = Y.nextEffect;
        }
      } while (null !== Y);

      Y = null;
      Pc();
      U = e;
    } else a.current = c;

    if (Ng) Ng = !1, Og = a, Pg = b;else for (Y = d; null !== Y;) {
      b = Y.nextEffect, Y.nextEffect = null, Y.flags & 8 && (x = Y, x.sibling = null, x.stateNode = null), Y = b;
    }
    d = a.pendingLanes;
    0 === d && (Rf = null);
    1 === d ? a === Ug ? Tg++ : (Tg = 0, Ug = a) : Tg = 0;
    c = c.stateNode;
    if (mc && "function" === typeof mc.onCommitFiberRoot) try {
      mc.onCommitFiberRoot(lc, c, void 0, 64 === (c.current.flags & 64));
    } catch (y) {}
    Z(a, H());
    if (Of) throw Of = !1, a = Pf, Pf = null, a;
    if (0 !== (U & 8)) return null;
    I();
    return null;
  }

  function xh() {
    for (; null !== Y;) {
      var a = Y.alternate;
      Zg || null === Yg || (0 !== (Y.flags & 8) ? Da(Y, Yg) && (Zg = !0, Za()) : 13 === Y.tag && ng(a, Y) && Da(Y, Yg) && (Zg = !0, Za()));
      var b = Y.flags;
      0 !== (b & 256) && Vf(a, Y);
      0 === (b & 512) || Ng || (Ng = !0, Xc(97, function () {
        dh();
        return null;
      }));
      Y = Y.nextEffect;
    }
  }

  function dh() {
    if (90 !== Pg) {
      var a = 97 < Pg ? 97 : Pg;
      Pg = 90;
      return Wc(a, yh);
    }

    return !1;
  }

  function Zf(a, b) {
    Qg.push(b, a);
    Ng || (Ng = !0, Xc(97, function () {
      dh();
      return null;
    }));
  }

  function Yf(a, b) {
    Rg.push(b, a);
    Ng || (Ng = !0, Xc(97, function () {
      dh();
      return null;
    }));
  }

  function yh() {
    if (null === Og) return !1;
    var a = Og;
    Og = null;
    if (0 !== (U & 48)) throw Error(q(331));
    var b = U;
    U |= 32;
    var c = Rg;
    Rg = [];

    for (var d = 0; d < c.length; d += 2) {
      var e = c[d],
          f = c[d + 1],
          g = e.destroy;
      e.destroy = void 0;
      if ("function" === typeof g) try {
        g();
      } catch (k) {
        if (null === f) throw Error(q(330));
        Uf(f, k);
      }
    }

    c = Qg;
    Qg = [];

    for (d = 0; d < c.length; d += 2) {
      e = c[d];
      f = c[d + 1];

      try {
        var h = e.create;
        e.destroy = h();
      } catch (k) {
        if (null === f) throw Error(q(330));
        Uf(f, k);
      }
    }

    for (h = a.current.firstEffect; null !== h;) {
      a = h.nextEffect, h.nextEffect = null, h.flags & 8 && (h.sibling = null, h.stateNode = null), h = a;
    }

    U = b;
    I();
    return !0;
  }

  function zh(a, b, c) {
    b = Kf(c, b);
    b = Nf(a, b, 1);
    td(a, b);
    b = L();
    a = $g(a, 1);
    null !== a && (yc(a, 1, b), Z(a, b));
  }

  function Uf(a, b) {
    if (3 === a.tag) zh(a, a, b);else for (var c = a.return; null !== c;) {
      if (3 === c.tag) {
        zh(c, a, b);
        break;
      } else if (1 === c.tag) {
        var d = c.stateNode;

        if ("function" === typeof c.type.getDerivedStateFromError || "function" === typeof d.componentDidCatch && (null === Rf || !Rf.has(d))) {
          a = Kf(b, a);
          var e = Qf(c, a, 1);
          td(c, e);
          e = L();
          c = $g(c, 1);
          if (null !== c) yc(c, 1, e), Z(c, e);else if ("function" === typeof d.componentDidCatch && (null === Rf || !Rf.has(d))) try {
            d.componentDidCatch(b, a);
          } catch (f) {}
          break;
        }
      }

      c = c.return;
    }
  }

  function rh(a, b, c) {
    var d = a.pingCache;
    null !== d && d.delete(b);
    b = L();
    a.pingedLanes |= a.suspendedLanes & c;
    S === a && (X & c) === c && (4 === V || 3 === V && (X & 62914560) === X && 500 > H() - lg ? fh(a, 0) : Kg |= c);
    Z(a, b);
  }

  function Ah(a, b) {
    0 === b && (b = a.mode, 0 === (b & 2) ? b = 1 : 0 === (b & 4) ? b = 99 === Uc() ? 1 : 2 : (0 === Wg && (Wg = Ig), b = wc(62914560 & ~Wg), 0 === b && (b = 4194304)));
    var c = L();
    a = $g(a, b);
    null !== a && (yc(a, b, c), Z(a, c));
  }

  function tf(a) {
    var b = a.memoizedState,
        c = 0;
    null !== b && (c = b.retryLane);
    Ah(a, c);
  }

  function mg(a, b) {
    var c = 0;

    switch (a.tag) {
      case 13:
        var d = a.stateNode;
        var e = a.memoizedState;
        null !== e && (c = e.retryLane);
        break;

      case 19:
        d = a.stateNode;
        break;

      default:
        throw Error(q(314));
    }

    null !== d && d.delete(b);
    Ah(a, c);
  }

  var vh;

  vh = function vh(a, b, c) {
    var d = b.lanes;
    if (null !== a) {
      if (a.memoizedProps !== b.pendingProps || E.current) K = !0;else if (0 !== (c & d)) K = 0 !== (a.flags & 16384) ? !0 : !1;else {
        K = !1;

        switch (b.tag) {
          case 3:
            lf(b);
            ke();
            break;

          case 5:
            $d(b);
            break;

          case 1:
            F(b.type) && jc(b);
            break;

          case 4:
            Yd(b, b.stateNode.containerInfo);
            break;

          case 10:
            jd(b, b.memoizedProps.value);
            break;

          case 13:
            d = b.memoizedState;

            if (null !== d) {
              if (null !== d.dehydrated) return C(M, M.current & 1), b.flags |= 64, null;
              if (0 !== (c & b.child.childLanes)) return nf(a, b, c);
              C(M, M.current & 1);
              b = $e(a, b, c);
              return null !== b ? b.sibling : null;
            }

            C(M, M.current & 1);
            break;

          case 19:
            d = 0 !== (c & b.childLanes);

            if (0 !== (a.flags & 64)) {
              if (d) return yf(a, b, c);
              b.flags |= 64;
            }

            var e = b.memoizedState;
            null !== e && (e.rendering = null, e.tail = null, e.lastEffect = null);
            C(M, M.current);
            if (d) break;else return null;

          case 23:
          case 24:
            return b.lanes = 0, ef(a, b, c);
        }

        return $e(a, b, c);
      }
    } else K = !1;
    b.lanes = 0;

    switch (b.tag) {
      case 2:
        d = b.type;
        null !== a && (a.alternate = null, b.alternate = null, b.flags |= 2);
        a = b.pendingProps;
        e = fc(b, D.current);
        md(b, c);
        e = te(null, b, d, a, e, c);
        b.flags |= 1;

        if ("object" === typeof e && null !== e && "function" === typeof e.render && void 0 === e.$$typeof) {
          b.tag = 1;
          b.memoizedState = null;
          b.updateQueue = null;

          if (F(d)) {
            var f = !0;
            jc(b);
          } else f = !1;

          b.memoizedState = null !== e.state && void 0 !== e.state ? e.state : null;
          pd(b);
          var g = d.getDerivedStateFromProps;
          "function" === typeof g && zd(b, d, g, a);
          e.updater = Cd;
          b.stateNode = e;
          e._reactInternals = b;
          Gd(b, d, a, c);
          b = kf(null, b, d, !0, f, c);
        } else b.tag = 0, T(null, b, e, c), b = b.child;

        return b;

      case 16:
        e = b.elementType;

        a: {
          null !== a && (a.alternate = null, b.alternate = null, b.flags |= 2);
          a = b.pendingProps;
          f = e._init;
          e = f(e._payload);
          b.type = e;
          f = b.tag = Bh(e);
          g = dd(e, a);

          switch (f) {
            case 0:
              b = df(null, b, e, g, c);
              break a;

            case 1:
              b = jf(null, b, e, g, c);
              break a;

            case 11:
              b = Ze(null, b, e, g, c);
              break a;

            case 14:
              b = af(null, b, e, dd(e.type, g), d, c);
              break a;

            case 22:
              b = hf(null, b, e, a, c);
              break a;
          }

          throw Error(q(306, e, ""));
        }

        return b;

      case 0:
        return d = b.type, e = b.pendingProps, e = b.elementType === d ? e : dd(d, e), df(a, b, d, e, c);

      case 1:
        return d = b.type, e = b.pendingProps, e = b.elementType === d ? e : dd(d, e), jf(a, b, d, e, c);

      case 3:
        lf(b);
        d = b.updateQueue;
        if (null === a || null === d) throw Error(q(282));
        d = b.pendingProps;
        e = b.memoizedState;
        e = null !== e ? e.element : null;
        rd(a, b);
        vd(b, d, null, c);
        d = b.memoizedState.element;
        if (d === e) ke(), b = $e(a, b, c);else {
          e = b.stateNode;
          if (f = e.hydrate) A ? (de = Ob(b.stateNode.containerInfo), ce = b, f = ee = !0) : f = !1;

          if (f) {
            if (A && (a = e.mutableSourceEagerHydrationData, null != a)) for (e = 0; e < a.length; e += 2) {
              f = a[e], g = a[e + 1], Ta ? f._workInProgressVersionPrimary = g : f._workInProgressVersionSecondary = g, le.push(f);
            }
            c = Sd(b, null, d, c);

            for (b.child = c; c;) {
              c.flags = c.flags & -3 | 1024, c = c.sibling;
            }
          } else T(a, b, d, c), ke();

          b = b.child;
        }
        return b;

      case 5:
        return $d(b), null === a && he(b), d = b.type, e = b.pendingProps, f = null !== a ? a.memoizedProps : null, g = e.children, Oa(d, e) ? g = null : null !== f && Oa(d, f) && (b.flags |= 16), gf(a, b), T(a, b, g, c), b.child;

      case 6:
        return null === a && he(b), null;

      case 13:
        return nf(a, b, c);

      case 4:
        return Yd(b, b.stateNode.containerInfo), d = b.pendingProps, null === a ? b.child = Rd(b, null, d, c) : T(a, b, d, c), b.child;

      case 11:
        return d = b.type, e = b.pendingProps, e = b.elementType === d ? e : dd(d, e), Ze(a, b, d, e, c);

      case 7:
        return T(a, b, b.pendingProps, c), b.child;

      case 8:
        return T(a, b, b.pendingProps.children, c), b.child;

      case 12:
        return T(a, b, b.pendingProps.children, c), b.child;

      case 10:
        a: {
          d = b.type._context;
          e = b.pendingProps;
          g = b.memoizedProps;
          f = e.value;
          jd(b, f);

          if (null !== g) {
            var h = g.value;
            f = J(h, f) ? 0 : ("function" === typeof d._calculateChangedBits ? d._calculateChangedBits(h, f) : 1073741823) | 0;

            if (0 === f) {
              if (g.children === e.children && !E.current) {
                b = $e(a, b, c);
                break a;
              }
            } else for (g = b.child, null !== g && (g.return = b); null !== g;) {
              var k = g.dependencies;

              if (null !== k) {
                h = g.child;

                for (var l = k.firstContext; null !== l;) {
                  if (l.context === d && 0 !== (l.observedBits & f)) {
                    1 === g.tag && (l = sd(-1, c & -c), l.tag = 2, td(g, l));
                    g.lanes |= c;
                    l = g.alternate;
                    null !== l && (l.lanes |= c);
                    ld(g.return, c);
                    k.lanes |= c;
                    break;
                  }

                  l = l.next;
                }
              } else if (10 === g.tag) h = g.type === b.type ? null : g.child;else if (18 === g.tag) {
                h = g.return;
                if (null === h) throw Error(q(341));
                h.lanes |= c;
                k = h.alternate;
                null !== k && (k.lanes |= c);
                ld(h, c);
                h = g.sibling;
              } else h = g.child;

              if (null !== h) h.return = g;else for (h = g; null !== h;) {
                if (h === b) {
                  h = null;
                  break;
                }

                g = h.sibling;

                if (null !== g) {
                  g.return = h.return;
                  h = g;
                  break;
                }

                h = h.return;
              }
              g = h;
            }
          }

          T(a, b, e.children, c);
          b = b.child;
        }

        return b;

      case 9:
        return e = b.type, f = b.pendingProps, d = f.children, md(b, c), e = nd(e, f.unstable_observedBits), d = d(e), b.flags |= 1, T(a, b, d, c), b.child;

      case 14:
        return e = b.type, f = dd(e, b.pendingProps), f = dd(e.type, f), af(a, b, e, f, d, c);

      case 15:
        return cf(a, b, b.type, b.pendingProps, d, c);

      case 17:
        return d = b.type, e = b.pendingProps, e = b.elementType === d ? e : dd(d, e), null !== a && (a.alternate = null, b.alternate = null, b.flags |= 2), b.tag = 1, F(d) ? (a = !0, jc(b)) : a = !1, md(b, c), Ed(b, d, e), Gd(b, d, e, c), kf(null, b, d, !0, a, c);

      case 19:
        return yf(a, b, c);

      case 22:
        return hf(a, b, b.type, b.pendingProps, c);

      case 23:
        return ef(a, b, c);

      case 24:
        return ef(a, b, c);
    }

    throw Error(q(156, b.tag));
  };

  var Ch = {
    current: !1
  },
      Dh = m.unstable_flushAllWithoutAsserting,
      Eh = "function" === typeof Dh;

  function Fh() {
    if (void 0 !== Dh) return Dh();

    for (var a = !1; dh();) {
      a = !0;
    }

    return a;
  }

  function Gh(a) {
    try {
      Fh(), Ag(function () {
        Fh() ? Gh(a) : a();
      });
    } catch (b) {
      a(b);
    }
  }

  var Hh = 0,
      Ih = !1;

  function Jh(a, b, c, d) {
    this.tag = a;
    this.key = c;
    this.sibling = this.child = this.return = this.stateNode = this.type = this.elementType = null;
    this.index = 0;
    this.ref = null;
    this.pendingProps = b;
    this.dependencies = this.memoizedState = this.updateQueue = this.memoizedProps = null;
    this.mode = d;
    this.flags = 0;
    this.lastEffect = this.firstEffect = this.nextEffect = null;
    this.childLanes = this.lanes = 0;
    this.alternate = null;
  }

  function N(a, b, c, d) {
    return new Jh(a, b, c, d);
  }

  function bf(a) {
    a = a.prototype;
    return !(!a || !a.isReactComponent);
  }

  function Bh(a) {
    if ("function" === typeof a) return bf(a) ? 1 : 0;

    if (void 0 !== a && null !== a) {
      a = a.$$typeof;
      if (a === ma) return 11;
      if (a === pa) return 14;
      if (a === ra) return 22;
    }

    return 2;
  }

  function Md(a, b) {
    var c = a.alternate;
    null === c ? (c = N(a.tag, b, a.key, a.mode), c.elementType = a.elementType, c.type = a.type, c.stateNode = a.stateNode, c.alternate = a, a.alternate = c) : (c.pendingProps = b, c.type = a.type, c.flags = 0, c.nextEffect = null, c.firstEffect = null, c.lastEffect = null);
    c.childLanes = a.childLanes;
    c.lanes = a.lanes;
    c.child = a.child;
    c.memoizedProps = a.memoizedProps;
    c.memoizedState = a.memoizedState;
    c.updateQueue = a.updateQueue;
    b = a.dependencies;
    c.dependencies = null === b ? null : {
      lanes: b.lanes,
      firstContext: b.firstContext
    };
    c.sibling = a.sibling;
    c.index = a.index;
    c.ref = a.ref;
    return c;
  }

  function Od(a, b, c, d, e, f) {
    var g = 2;
    d = a;
    if ("function" === typeof a) bf(a) && (g = 1);else if ("string" === typeof a) g = 5;else a: switch (a) {
      case ha:
        return Qd(c.children, e, f, b);

      case sa:
        g = 8;
        e |= 16;
        break;

      case ia:
        g = 8;
        e |= 1;
        break;

      case ja:
        return a = N(12, c, b, e | 8), a.elementType = ja, a.type = ja, a.lanes = f, a;

      case na:
        return a = N(13, c, b, e), a.type = na, a.elementType = na, a.lanes = f, a;

      case oa:
        return a = N(19, c, b, e), a.elementType = oa, a.lanes = f, a;

      case ta:
        return qf(c, e, f, b);

      case ua:
        return a = N(24, c, b, e), a.elementType = ua, a.lanes = f, a;

      default:
        if ("object" === typeof a && null !== a) switch (a.$$typeof) {
          case ka:
            g = 10;
            break a;

          case la:
            g = 9;
            break a;

          case ma:
            g = 11;
            break a;

          case pa:
            g = 14;
            break a;

          case qa:
            g = 16;
            d = null;
            break a;

          case ra:
            g = 22;
            break a;
        }
        throw Error(q(130, null == a ? a : typeof a, ""));
    }
    b = N(g, c, b, e);
    b.elementType = a;
    b.type = d;
    b.lanes = f;
    return b;
  }

  function Qd(a, b, c, d) {
    a = N(7, a, d, b);
    a.lanes = c;
    return a;
  }

  function qf(a, b, c, d) {
    a = N(23, a, d, b);
    a.elementType = ta;
    a.lanes = c;
    return a;
  }

  function Nd(a, b, c) {
    a = N(6, a, null, b);
    a.lanes = c;
    return a;
  }

  function Pd(a, b, c) {
    b = N(4, null !== a.children ? a.children : [], a.key, b);
    b.lanes = c;
    b.stateNode = {
      containerInfo: a.containerInfo,
      pendingChildren: null,
      implementation: a.implementation
    };
    return b;
  }

  function Kh(a, b, c) {
    this.tag = b;
    this.containerInfo = a;
    this.finishedWork = this.pingCache = this.current = this.pendingChildren = null;
    this.timeoutHandle = Sa;
    this.pendingContext = this.context = null;
    this.hydrate = c;
    this.callbackNode = null;
    this.callbackPriority = 0;
    this.eventTimes = xc(0);
    this.expirationTimes = xc(-1);
    this.entangledLanes = this.finishedLanes = this.mutableReadLanes = this.expiredLanes = this.pingedLanes = this.suspendedLanes = this.pendingLanes = 0;
    this.entanglements = xc(0);
    A && (this.mutableSourceEagerHydrationData = null);
  }

  function Lh(a) {
    var b = a._reactInternals;

    if (void 0 === b) {
      if ("function" === typeof a.render) throw Error(q(188));
      throw Error(q(268, Object.keys(a)));
    }

    a = Ba(b);
    return null === a ? null : a.stateNode;
  }

  function Mh(a, b) {
    a = a.memoizedState;

    if (null !== a && null !== a.dehydrated) {
      var c = a.retryLane;
      a.retryLane = 0 !== c && c < b ? c : b;
    }
  }

  function Nh(a, b) {
    Mh(a, b);
    (a = a.alternate) && Mh(a, b);
  }

  function Oh(a) {
    a = Ba(a);
    return null === a ? null : a.stateNode;
  }

  function Ph() {
    return null;
  }

  exports.IsThisRendererActing = Ch;

  exports.act = function (a) {
    function b() {
      Hh--;
      Eg.current = c;
      Ch.current = d;
    }

    !1 === Ih && (Ih = !0, console.error("act(...) is not supported in production builds of React, and might not behave as expected."));
    Hh++;
    var c = Eg.current,
        d = Ch.current;
    Eg.current = !0;
    Ch.current = !0;

    try {
      var e = ph(a);
    } catch (f) {
      throw b(), f;
    }

    if (null !== e && "object" === typeof e && "function" === typeof e.then) return {
      then: function then(a, d) {
        e.then(function () {
          1 < Hh || !0 === Eh && !0 === c ? (b(), a()) : Gh(function (c) {
            b();
            c ? d(c) : a();
          });
        }, function (a) {
          b();
          d(a);
        });
      }
    };

    try {
      1 !== Hh || !1 !== Eh && !1 !== c || Fh(), b();
    } catch (f) {
      throw b(), f;
    }

    return {
      then: function then(a) {
        a();
      }
    };
  };

  exports.attemptContinuousHydration = function (a) {
    if (13 === a.tag) {
      var b = L();
      Bd(a, 67108864, b);
      Nh(a, 67108864);
    }
  };

  exports.attemptHydrationAtCurrentPriority = function (a) {
    if (13 === a.tag) {
      var b = L(),
          c = Ad(a);
      Bd(a, c, b);
      Nh(a, c);
    }
  };

  exports.attemptSynchronousHydration = function (a) {
    switch (a.tag) {
      case 3:
        var b = a.stateNode;

        if (b.hydrate) {
          var c = pc(b.pendingLanes);
          b.expiredLanes |= c & b.pendingLanes;
          Z(b, H());
          0 === (U & 48) && (Mg(), I());
        }

        break;

      case 13:
        var d = L();
        qh(function () {
          return Bd(a, 1, d);
        });
        Nh(a, 4);
    }
  };

  exports.attemptUserBlockingHydration = function (a) {
    if (13 === a.tag) {
      var b = L();
      Bd(a, 4, b);
      Nh(a, 4);
    }
  };

  exports.batchedEventUpdates = function (a, b) {
    var c = U;
    U |= 2;

    try {
      return a(b);
    } finally {
      U = c, 0 === U && (Mg(), I());
    }
  };

  exports.batchedUpdates = ph;

  exports.createComponentSelector = function (a) {
    return {
      $$typeof: og,
      value: a
    };
  };

  exports.createContainer = function (a, b, c) {
    a = new Kh(a, b, c);
    b = N(3, null, null, 2 === b ? 7 : 1 === b ? 3 : 0);
    a.current = b;
    b.stateNode = a;
    pd(b);
    return a;
  };

  exports.createHasPsuedoClassSelector = function (a) {
    return {
      $$typeof: pg,
      value: a
    };
  };

  exports.createPortal = function (a, b, c) {
    var d = 3 < arguments.length && void 0 !== arguments[3] ? arguments[3] : null;
    return {
      $$typeof: fa,
      key: null == d ? null : "" + d,
      children: a,
      containerInfo: b,
      implementation: c
    };
  };

  exports.createRoleSelector = function (a) {
    return {
      $$typeof: qg,
      value: a
    };
  };

  exports.createTestNameSelector = function (a) {
    return {
      $$typeof: rg,
      value: a
    };
  };

  exports.createTextSelector = function (a) {
    return {
      $$typeof: sg,
      value: a
    };
  };

  exports.deferredUpdates = function (a) {
    return Wc(97, a);
  };

  exports.discreteUpdates = function (a, b, c, d, e) {
    var f = U;
    U |= 4;

    try {
      return Wc(98, a.bind(null, b, c, d, e));
    } finally {
      U = f, 0 === U && (Mg(), I());
    }
  };

  exports.findAllNodes = yg;

  exports.findBoundingRects = function (a, b) {
    if (!bb) throw Error(q(363));
    b = yg(a, b);
    a = [];

    for (var c = 0; c < b.length; c++) {
      a.push(db(b[c]));
    }

    for (b = a.length - 1; 0 < b; b--) {
      c = a[b];

      for (var d = c.x, e = d + c.width, f = c.y, g = f + c.height, h = b - 1; 0 <= h; h--) {
        if (b !== h) {
          var k = a[h],
              l = k.x,
              p = l + k.width,
              t = k.y,
              n = t + k.height;

          if (d >= l && f >= t && e <= p && g <= n) {
            a.splice(b, 1);
            break;
          } else if (!(d !== l || c.width !== k.width || n < f || t > g)) {
            t > f && (k.height += t - f, k.y = f);
            n < g && (k.height = g - t);
            a.splice(b, 1);
            break;
          } else if (!(f !== t || c.height !== k.height || p < d || l > e)) {
            l > d && (k.width += l - d, k.x = d);
            p < e && (k.width = e - l);
            a.splice(b, 1);
            break;
          }
        }
      }
    }

    return a;
  };

  exports.findHostInstance = Lh;

  exports.findHostInstanceWithNoPortals = function (a) {
    a = Ca(a);
    return null === a ? null : 20 === a.tag ? a.stateNode.instance : a.stateNode;
  };

  exports.findHostInstanceWithWarning = function (a) {
    return Lh(a);
  };

  exports.flushControlled = function (a) {
    var b = U;
    U |= 1;

    try {
      Wc(99, a);
    } finally {
      U = b, 0 === U && (Mg(), I());
    }
  };

  exports.flushDiscreteUpdates = function () {
    0 === (U & 49) && (oh(), dh());
  };

  exports.flushPassiveEffects = dh;
  exports.flushSync = qh;

  exports.focusWithin = function (a, b) {
    if (!bb) throw Error(q(363));
    a = ug(a);
    b = xg(a, b);
    b = Array.from(b);

    for (a = 0; a < b.length;) {
      var c = b[a++];

      if (!fb(c)) {
        if (5 === c.tag && hb(c.stateNode)) return !0;

        for (c = c.child; null !== c;) {
          b.push(c), c = c.sibling;
        }
      }
    }

    return !1;
  };

  exports.getCurrentUpdateLanePriority = function () {
    return oc;
  };

  exports.getFindAllNodesFailureDescription = function (a, b) {
    if (!bb) throw Error(q(363));
    var c = 0,
        d = [];
    a = [ug(a), 0];

    for (var e = 0; e < a.length;) {
      var f = a[e++],
          g = a[e++],
          h = b[g];
      if (5 !== f.tag || !fb(f)) if (vg(f, h) && (d.push(wg(h)), g++, g > c && (c = g)), g < b.length) for (f = f.child; null !== f;) {
        a.push(f, g), f = f.sibling;
      }
    }

    if (c < b.length) {
      for (a = []; c < b.length; c++) {
        a.push(wg(b[c]));
      }

      return "findAllNodes was able to match part of the selector:\n  " + (d.join(" > ") + "\n\nNo matching component was found for:\n  ") + a.join(" > ");
    }

    return null;
  };

  exports.getPublicRootInstance = function (a) {
    a = a.current;
    if (!a.child) return null;

    switch (a.child.tag) {
      case 5:
        return Ea(a.child.stateNode);

      default:
        return a.child.stateNode;
    }
  };

  exports.injectIntoDevTools = function (a) {
    a = {
      bundleType: a.bundleType,
      version: a.version,
      rendererPackageName: a.rendererPackageName,
      rendererConfig: a.rendererConfig,
      overrideHookState: null,
      overrideHookStateDeletePath: null,
      overrideHookStateRenamePath: null,
      overrideProps: null,
      overridePropsDeletePath: null,
      overridePropsRenamePath: null,
      setSuspenseHandler: null,
      scheduleUpdate: null,
      currentDispatcherRef: da.ReactCurrentDispatcher,
      findHostInstanceByFiber: Oh,
      findFiberByHostInstance: a.findFiberByHostInstance || Ph,
      findHostInstancesForRefresh: null,
      scheduleRefresh: null,
      scheduleRoot: null,
      setRefreshHandler: null,
      getCurrentFiber: null
    };
    if ("undefined" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__) a = !1;else {
      var b = __REACT_DEVTOOLS_GLOBAL_HOOK__;
      if (!b.isDisabled && b.supportsFiber) try {
        lc = b.inject(a), mc = b;
      } catch (c) {}
      a = !0;
    }
    return a;
  };

  exports.observeVisibleRects = function (a, b, c, d) {
    if (!bb) throw Error(q(363));
    a = yg(a, b);
    var e = ib(a, c, d).disconnect;
    return {
      disconnect: function disconnect() {
        e();
      }
    };
  };

  exports.registerMutableSourceForHydration = function (a, b) {
    var c = b._getVersion;
    c = c(b._source);
    null == a.mutableSourceEagerHydrationData ? a.mutableSourceEagerHydrationData = [b, c] : a.mutableSourceEagerHydrationData.push(b, c);
  };

  exports.runWithPriority = function (a, b) {
    var c = oc;

    try {
      return oc = a, b();
    } finally {
      oc = c;
    }
  };

  exports.shouldSuspend = function () {
    return !1;
  };

  exports.unbatchedUpdates = function (a, b) {
    var c = U;
    U &= -2;
    U |= 8;

    try {
      return a(b);
    } finally {
      U = c, 0 === U && (Mg(), I());
    }
  };

  exports.updateContainer = function (a, b, c, d) {
    var e = b.current,
        f = L(),
        g = Ad(e);

    a: if (c) {
      c = c._reactInternals;

      b: {
        if (ya(c) !== c || 1 !== c.tag) throw Error(q(170));
        var h = c;

        do {
          switch (h.tag) {
            case 3:
              h = h.stateNode.context;
              break b;

            case 1:
              if (F(h.type)) {
                h = h.stateNode.__reactInternalMemoizedMergedChildContext;
                break b;
              }

          }

          h = h.return;
        } while (null !== h);

        throw Error(q(171));
      }

      if (1 === c.tag) {
        var k = c.type;

        if (F(k)) {
          c = ic(c, k, h);
          break a;
        }
      }

      c = h;
    } else c = dc;

    null === b.context ? b.context = c : b.pendingContext = c;
    b = sd(f, g);
    b.payload = {
      element: a
    };
    d = void 0 === d ? null : d;
    null !== d && (b.callback = d);
    td(e, b);
    Bd(e, g, f);
    return g;
  };

  return exports;
};
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__("./node_modules/webpack/buildin/module.js")(module)))

/***/ }),

/***/ "./third-party/react-reconciler/index.js":
/***/ (function(module, exports, __webpack_require__) {

"use strict";


if (true) {
  module.exports = __webpack_require__("./third-party/react-reconciler/cjs/react-reconciler.production.min.js");
} else {}

/***/ }),

/***/ 0:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__;

/***/ })

/******/ });