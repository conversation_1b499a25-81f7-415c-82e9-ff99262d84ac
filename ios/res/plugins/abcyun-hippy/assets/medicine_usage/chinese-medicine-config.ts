/**
 * create by dengjie
 * desc:
 * create date 2020/4/24
 */

const ChineseMedicine = {
    freq: [
        {
            id: 1,
            name: "1日1次",
            namePY: "1ri1ci",
            namePYFirst: "1R1C",
            time: 24,
        },
        {
            id: 2,
            name: "1日2次",
            namePY: "1ri2ci",
            namePYFirst: "1R2C",
            time: 12,
        },
        {
            id: 3,
            name: "1日3次",
            namePY: "1ri3ci",
            namePYFirst: "1R3C",
            time: 8,
        },
        {
            id: 4,
            name: "1日4次",
            namePY: "1ri4ci",
            namePYFirst: "1R4C",
            time: 6,
        },
        {
            id: 5,
            name: "1日5次",
            namePY: "1ri5ci",
            namePYFirst: "1R5C",
            time: 5,
        },
        {
            id: 6,
            name: "1日6次",
            namePY: "1ri6ci",
            namePYFirst: "1R6C",
            time: 4,
        },
        {
            id: 7,
            name: "2日1次",
            namePY: "liangri1ci",
            namePYFirst: "lr1c",
        },
        {
            id: 8,
            name: "隔日1次",
            namePY: "getian1ci",
            namePYFirst: "gt1c",
        },
        {
            id: 9,
            name: "1周1次",
            namePY: "1zhou1ci",
            namePYFirst: "yz1c",
        },
        {
            id: 10,
            name: "1周2次",
            namePY: "1zhou2ci",
            namePYFirst: "1z2c",
        },
        {
            id: 11,
            name: "1周3次",
            namePY: "1zhou3ci",
            namePYFirst: "1z3c",
        },
        {
            id: 12,
            name: "隔周1次",
            namePY: "gezhou1ci",
            namePYFirst: "gz1c",
        },
        {
            id: 13,
            name: "必要时",
            namePY: "liji",
            namePYFirst: "lj",
        },
        {
            id: 14,
            name: "顿服",
            namePY: "dunfu",
            namePYFirst: "df",
        },
        {
            id: 15,
            name: "少量频服",
            namePY: "shaoliangpinfu",
            namePYFirst: "slpf",
        },
        {
            id: 16,
            name: "随时服用",
            namePY: "suishifuyong",
            namePYFirst: "ssfy",
        },
    ],
    freqWithZhiGao: [
        {
            id: 1,
            name: "1日1次",
            namePY: "meitianyici",
            namePYFirst: "MT1C",
        },
        {
            id: 2,
            name: "1日2次",
            namePY: "meitianliangci",
            namePYFirst: "MTEC",
        },
        {
            id: 3,
            name: "1日3次",
            namePY: "meitiansanci",
            namePYFirst: "MTSC",
        },
        {
            id: 4,
            name: "1日4次",
            namePY: "meitiansici",
            namePYFirst: "MTSC",
        },
        {
            id: 5,
            name: "1日5次",
            namePY: "meitianwuci",
            namePYFirst: "MTWC",
        },
        {
            id: 10,
            name: "必要时",
            namePY: "biyaoshi",
            namePYFirst: "bys",
        },
        {
            id: 11,
            name: "少量频服",
            namePY: "liji",
            namePYFirst: "lj",
        },
        {
            id: 12,
            name: "随时服用",
            namePY: "suishiyinyong",
            namePYFirst: "ssyy",
        },
    ],
    freqWithZhiGaoAir: [
        {
            id: 1,
            name: "1日1次",
            namePY: "meitianyici",
            namePYFirst: "MT1C",
        },
        {
            id: 2,
            name: "1日2次",
            namePY: "meitianliangci",
            namePYFirst: "MTEC",
        },
        {
            id: 3,
            name: "1日3次",
            namePY: "meitiansanci",
            namePYFirst: "MTSC",
        },
        {
            id: 4,
            name: "1日4次",
            namePY: "meitiansici",
            namePYFirst: "MTSC",
        },
        {
            id: 5,
            name: "1日5次",
            namePY: "meitianwuci",
            namePYFirst: "MTWC",
        },
    ],
    dailyDosage: [
        {
            id: 1,
            name: "1日1剂",
            namePY: "meitianyiji",
            namePYFirst: "MTYJ",
            daysCount: 1,
            dosageCount: 1,
        },
        {
            id: 2,
            name: "1日2剂",
            namePY: "meitianliangji",
            namePYFirst: "MTLJ",
            daysCount: 1,
            dosageCount: 2,
        },
        {
            id: 3,
            name: "2日1剂",
            namePY: "meiliangtianyiji",
            namePYFirst: "MLTYJ",
            daysCount: 2,
            dosageCount: 1,
        },
        {
            id: 4,
            name: "1日半1剂",
            namePY: "meiyitianbanyiji",
            namePYFirst: "MYTBYJ",
            daysCount: 1.5,
            dosageCount: 1,
        },
    ],
    specialDailyDosage: [
        {
            id: 1,
            name: "1日1次",
            namePY: "1ri1ci",
            namePYFirst: "1R1C",
            time: 24,
        },
        {
            id: 2,
            name: "1日2次",
            namePY: "1ri2ci",
            namePYFirst: "1R2C",
            time: 12,
        },
        {
            id: 3,
            name: "1日3次",
            namePY: "1ri3ci",
            namePYFirst: "1R3C",
            time: 8,
        },
        {
            id: 4,
            name: "1日4次",
            namePY: "1ri4ci",
            namePYFirst: "1R4C",
            time: 6,
        },
        {
            id: 5,
            name: "1日5次",
            namePY: "1ri5ci",
            namePYFirst: "1R5C",
            time: 5,
        },
        {
            id: 6,
            name: "1日6次",
            namePY: "1ri6ci",
            namePYFirst: "1R6C",
            time: 4,
        },
        {
            id: 7,
            name: "2日1次",
            namePY: "liangri1ci",
            namePYFirst: "lr1c",
            time: null,
        },
        {
            id: 8,
            name: "隔日1次",
            namePY: "getian1ci",
            namePYFirst: "gt1c",
            time: null,
        },
        {
            id: 9,
            name: "1周1次",
            namePY: "1zhou1ci",
            namePYFirst: "yz1c",
            time: null,
        },
        {
            id: 10,
            name: "1周2次",
            namePY: "1zhou2ci",
            namePYFirst: "1z2c",
            time: null,
        },
        {
            id: 11,
            name: "1周3次",
            namePY: "1zhou3ci",
            namePYFirst: "1z3c",
            time: null,
        },
        {
            id: 12,
            name: "隔周1次",
            namePY: "gezhou1ci",
            namePYFirst: "gz1c",
            time: null,
        },
        {
            id: 13,
            name: "必要时",
            namePY: "liji",
            namePYFirst: "lj",
            time: null,
        },
        {
            id: 14,
            name: "顿服",
            namePY: "dunfu",
            namePYFirst: "df",
            time: null,
        },
        {
            id: 15,
            name: "少量频服",
            namePY: "shaoliangpinfu",
            namePYFirst: "slpf",
            time: null,
        },
        {
            id: 16,
            name: "随时服用",
            namePY: "suishifuyong",
            namePYFirst: "ssfy",
            time: null,
        },
    ],
    specialRequirement: [
        {
            id: 1,
            name: "先煎半小时",
            namePY: "xianjianbanxiaoshi",
            namePYFirst: "XJBXS",
        },
        {
            id: 2,
            name: "先煎一小时",
            namePY: "xianjianyixiaoshi",
            namePYFirst: "XJYXS",
        },
        {
            id: 3,
            name: "先煎",
            namePY: "xianjian",
            namePYFirst: "XJ",
        },
        {
            id: 4,
            name: "后下",
            namePY: "houxia",
            namePYFirst: "HX",
        },
        {
            id: 5,
            name: "包煎",
            namePY: "baojian",
            namePYFirst: "BJ",
        },
        {
            id: 6,
            name: "另煎",
            namePY: "lingjian",
            namePYFirst: "LJ",
        },
        {
            id: 7,
            name: "先炒",
            namePY: "yanghua",
            namePYFirst: "YH",
        },
        {
            id: 8,
            name: "烊化",
            namePY: "yanghua",
            namePYFirst: "YH",
        },
        {
            id: 9,
            name: "冲服",
            namePY: "chongfu",
            namePYFirst: "CF",
        },
        {
            id: 10,
            name: "捣碎",
            namePY: "daosui",
            namePYFirst: "DS",
        },
        {
            id: 11,
            name: "打粉",
            namePY: "daosui",
            namePYFirst: "DF",
        },
        {
            id: 12,
            name: "另包",
            namePY: "lingbao",
            namePYFirst: "LB",
        },
        {
            id: 13,
            name: "【自备】",
            namePY: "zibei",
            namePYFirst: "ZB",
        },
    ],
    usages: [
        {
            id: 1,
            name: "煎服",
            namePY: "jianfu",
            namePYFirst: "jf",
        },
        {
            id: 2,
            name: "冲服",
            namePY: "chongfu",
            namePYFirst: "cf",
        },
        {
            id: 3,
            name: "制膏",
            namePY: "zhigao",
            namePYFirst: "zg",
        },
        {
            id: 4,
            name: "制丸",
            namePY: "zhiwan",
            namePYFirst: "zw",
        },
        {
            id: 5,
            name: "外用",
            namePY: "waiyong",
            namePYFirst: "wy",
        },
        {
            id: 6,
            name: "贴敷",
            namePY: "tiefu",
            namePYFirst: "tf",
        },
        {
            id: 7,
            name: "打粉",
            namePY: "waiyong",
            namePYFirst: "wy",
        },
        {
            id: 8,
            name: "泡饮",
            namePY: "paoyin",
            namePYFirst: "py",
        },
        {
            id: 9,
            name: "泡酒",
            namePY: "paojiu",
            namePYFirst: "pj",
        },
        {
            id: 10,
            name: "泡浴",
            namePY: "paoyu",
            namePYFirst: "py",
        },
        {
            id: 11,
            name: "泡脚",
            namePY: "paojiao",
            namePYFirst: "pj",
        },
        {
            id: 12,
            name: "擦拭",
            namePY: "cashi",
            namePYFirst: "cs",
        },
    ],
    usageLevel: [
        {
            id: 1,
            name: "每次10ml",
            namePY: "meici10ml",
            namePYFirst: "mc10ml",
            isLiquid: true,
        },
        {
            id: 2,
            name: "每次20ml",
            namePY: "meici20ml",
            namePYFirst: "mc20ml",
            isLiquid: true,
        },
        {
            id: 3,
            name: "每次30ml",
            namePY: "meici30ml",
            namePYFirst: "mc30ml",
            isLiquid: true,
        },
        {
            id: 4,
            name: "每次40ml",
            namePY: "meici40ml",
            namePYFirst: "mc40ml",
            isLiquid: true,
        },
        {
            id: 5,
            name: "每次50ml",
            namePY: "meici50ml",
            namePYFirst: "mc50ml",
            isLiquid: true,
        },
        {
            id: 6,
            name: "每次60ml",
            namePY: "meici60ml",
            namePYFirst: "mc60ml",
            isLiquid: true,
        },
        {
            id: 7,
            name: "每次70ml",
            namePY: "meici70ml",
            namePYFirst: "mc70ml",
            isLiquid: true,
        },
        {
            id: 8,
            name: "每次80ml",
            namePY: "meici80ml",
            namePYFirst: "mc80ml",
            isLiquid: true,
        },
        {
            id: 9,
            name: "每次90ml",
            namePY: "meici90ml",
            namePYFirst: "mc90ml",
            isLiquid: true,
        },
        {
            id: 10,
            name: "每次100ml",
            namePY: "meici100ml",
            namePYFirst: "mc100ml",
            isLiquid: true,
        },
        {
            id: 11,
            name: "每次120ml",
            namePY: "meici120ml",
            namePYFirst: "mc120ml",
            isLiquid: true,
        },
        {
            id: 12,
            name: "每次150ml",
            namePY: "meici150ml",
            namePYFirst: "mc150ml",
            isLiquid: true,
        },
        {
            id: 13,
            name: "每次180ml",
            namePY: "meici180ml",
            namePYFirst: "mc180ml",
            isLiquid: true,
        },
        {
            id: 14,
            name: "每次200ml",
            namePY: "meici200ml",
            namePYFirst: "mc200ml",
        },
        {
            id: 15,
            name: "每次250ml",
            namePY: "meici250ml",
            namePYFirst: "mc250ml",
        },
        {
            id: 16,
            name: "每次300ml",
            namePY: "meici300ml",
            namePYFirst: "mc300ml",
        },
    ],
    zhiGaoUsageLevel: [
        // 制膏用量推荐
        {
            id: 1,
            name: "每次5g",
            namePY: "meici5g",
            namePYFirst: "mc5g",
        },
        {
            id: 2,
            name: "每次10g",
            namePY: "meici10g",
            namePYFirst: "mc10g",
        },
        {
            id: 3,
            name: "每次15g",
            namePY: "meici15g",
            namePYFirst: "mc15g",
        },
        {
            id: 4,
            name: "每次20g",
            namePY: "meici10g",
            namePYFirst: "mc20g",
        },
        {
            id: 5,
            name: "每次25g",
            namePY: "meici25g",
            namePYFirst: "mc25g",
        },
        {
            id: 6,
            name: "每次30g",
            namePY: "meici30g",
            namePYFirst: "mc30g",
        },
        {
            id: 7,
            name: "每次35g",
            namePY: "meici35g",
            namePYFirst: "mc35g",
        },
        {
            id: 8,
            name: "每次40g",
            namePY: "meici40g",
            namePYFirst: "mc40g",
        },
        {
            id: 9,
            name: "1汤匙",
            namePY: "1tangchi",
            namePYFirst: "1tc",
        },
        {
            id: 10,
            name: "2汤匙",
            namePY: "2tangchi",
            namePYFirst: "2tc",
        },
        {
            id: 11,
            name: "3汤匙",
            namePY: "3tangchi",
            namePYFirst: "3tc",
        },
        {
            id: 12,
            name: "适量",
            namePY: "shiliang",
            namePYFirst: "sl",
        },
    ],
    zhiWanUsageLevel: [
        // 制丸用量推荐
        {
            id: 1,
            name: "每次3g",
            namePY: "meici3g",
            namePYFirst: "mc3g",
        },
        {
            id: 2,
            name: "每次6g",
            namePY: "meici6g",
            namePYFirst: "mc6g",
        },
        {
            id: 3,
            name: "每次9g",
            namePY: "meici9g",
            namePYFirst: "mc9g",
        },
        {
            id: 4,
            name: "每次10g",
            namePY: "meici10g",
            namePYFirst: "mc10g",
        },
        {
            id: 5,
            name: "每次12g",
            namePY: "meici12g",
            namePYFirst: "mc12g",
        },
        {
            id: 6,
            name: "每次15g",
            namePY: "meici15g",
            namePYFirst: "mc15g",
        },
        {
            id: 7,
            name: "1丸",
            namePY: "1wan",
            namePYFirst: "1w",
        },
        {
            id: 8,
            name: "2丸",
            namePY: "2wan",
            namePYFirst: "2w",
        },
        {
            id: 9,
            name: "5丸",
            namePY: "5wan",
            namePYFirst: "5w",
        },
        {
            id: 10,
            name: "10丸",
            namePY: "10wan",
            namePYFirst: "10w",
        },
        {
            id: 11,
            name: "20丸",
            namePY: "20wan",
            namePYFirst: "20w",
        },
        {
            id: 12,
            name: "30丸",
            namePY: "30wan",
            namePYFirst: "30w",
        },
        {
            id: 13,
            name: "适量",
            namePY: "shiliang",
            namePYFirst: "sl",
        },
    ],
    daFenUsageLevel: [
        // 打粉用量推荐
        {
            id: 1,
            name: "每次3g",
            namePY: "meici3g",
            namePYFirst: "mc3g",
        },
        {
            id: 2,
            name: "每次4g",
            namePY: "meici4g",
            namePYFirst: "mc4g",
        },
        {
            id: 3,
            name: "每次5g",
            namePY: "meici5g",
            namePYFirst: "mc5g",
        },
        {
            id: 4,
            name: "每次6g",
            namePY: "meici6g",
            namePYFirst: "mc6g",
        },
        {
            id: 5,
            name: "每次7g",
            namePY: "meici7g",
            namePYFirst: "mc7g",
        },
        {
            id: 6,
            name: "每次8g",
            namePY: "meici8g",
            namePYFirst: "mc8g",
        },
        {
            id: 7,
            name: "每次9g",
            namePY: "meici9g",
            namePYFirst: "mc9g",
        },
        {
            id: 8,
            name: "每次10g",
            namePY: "meici10g",
            namePYFirst: "mc10g",
        },
        {
            id: 9,
            name: "每次11g",
            namePY: "meici11g",
            namePYFirst: "mc11g",
        },
        {
            id: 10,
            name: "每次12g",
            namePY: "meici12g",
            namePYFirst: "mc12g",
        },
        {
            id: 11,
            name: "每次13g",
            namePY: "meici13g",
            namePYFirst: "mc13g",
        },
        {
            id: 12,
            name: "每次14g",
            namePY: "meici14g",
            namePYFirst: "mc14g",
        },
        {
            id: 13,
            name: "每次15g",
            namePY: "meici15g",
            namePYFirst: "mc15g",
        },
        {
            id: 14,
            name: "适量",
            namePY: "shiliang",
            namePYFirst: "sl",
        },
    ],
    keLiChongFuUsageLevel: [
        // 颗粒冲服用量推荐
        {
            id: 1,
            name: "每次半袋",
            namePY: "meicibandai",
            namePYFirst: "mcbd",
        },
        {
            id: 2,
            name: "每次1袋",
            namePY: "meici1dai",
            namePYFirst: "mc1d",
        },
        {
            id: 3,
            name: "每次2袋",
            namePY: "meici2dai",
            namePYFirst: "mc2d",
        },
        {
            id: 4,
            name: "每次3袋",
            namePY: "meici3dai",
            namePYFirst: "mc3d",
        },
        {
            id: 5,
            name: "每次半格",
            namePY: "meicibange",
            namePYFirst: "mcbg",
        },
        {
            id: 6,
            name: "每次1格",
            namePY: "meici1ge",
            namePYFirst: "mc1g",
        },
        {
            id: 7,
            name: "每次2格",
            namePY: "meici2ge",
            namePYFirst: "mc2g",
        },
        {
            id: 8,
            name: "每次3格",
            namePY: "meici3ge",
            namePYFirst: "mc3g",
        },
    ],
    usageLevelWithZhiGao: [
        { id: 1, name: "每次5g", namePY: "meici5g", namePYFirst: "mc5g" },
        { id: 2, name: "每次10g", namePY: "meici10g", namePYFirst: "mc10g" },
        { id: 3, name: "每次15g", namePY: "meici15g", namePYFirst: "mc15g" },
        { id: 4, name: "每次20g", namePY: "meici20g", namePYFirst: "mc20g" },
        { id: 5, name: "每次25g", namePY: "meici25g", namePYFirst: "mc25g" },
        { id: 6, name: "每次30g", namePY: "meici30g", namePYFirst: "mc30g" },
        { id: 7, name: "每次35g", namePY: "meici35g", namePYFirst: "mc35g" },
        { id: 8, name: "每次40g", namePY: "meici40g", namePYFirst: "mc40g" },
    ],
    usageLevelWithZhiGaoAirBag: [{ id: 1, name: "每次20g", namePY: "meici20g", namePYFirst: "mc20g" }],
    usageLevelWithZhiWan: [
        { id: 1, name: "每次4g", namePY: "meici4g", namePYFirst: "mc4g" },
        { id: 2, name: "每次5g", namePY: "meici5g", namePYFirst: "mc5g" },
        { id: 3, name: "每次6g", namePY: "meici6g", namePYFirst: "mc6g" },
        { id: 4, name: "每次7g", namePY: "meici7g", namePYFirst: "mc7g" },
        { id: 5, name: "每次8g", namePY: "meici8g", namePYFirst: "mc8g" },
        { id: 6, name: "每次9g", namePY: "meici9g", namePYFirst: "mc9g" },
        { id: 7, name: "每次10g", namePY: "meici10g", namePYFirst: "mc10g" },
        { id: 8, name: "每次11g", namePY: "meici11g", namePYFirst: "mc11g" },
        { id: 9, name: "每次12g", namePY: "meici12g", namePYFirst: "mc12g" },
        { id: 10, name: "每次13g", namePY: "meici13g", namePYFirst: "mc13g" },
        { id: 11, name: "每次14g", namePY: "meici14g", namePYFirst: "mc14g" },
        { id: 12, name: "每次15g", namePY: "meici15g", namePYFirst: "mc15g" },
    ],
    usageLevelWithZhiWanAir: [
        { id: 1, name: "每次8g", namePY: "meici8g", namePYFirst: "mc8g" },
        { id: 2, name: "每次9g", namePY: "meici9g", namePYFirst: "mc9g" },
        { id: 3, name: "每次10g", namePY: "meici10g", namePYFirst: "mc10g" },
        { id: 4, name: "每次11g", namePY: "meici11g", namePYFirst: "mc11g" },
        { id: 5, name: "每次12g", namePY: "meici12g", namePYFirst: "mc12g" },
    ],
    usageLevelWithDaFen: [
        { id: 1, name: "每次4g", namePY: "meici4g", namePYFirst: "mc4g" },
        { id: 2, name: "每次5g", namePY: "meici5g", namePYFirst: "mc5g" },
        { id: 3, name: "每次6g", namePY: "meici6g", namePYFirst: "mc6g" },
        { id: 4, name: "每次7g", namePY: "meici7g", namePYFirst: "mc7g" },
        { id: 5, name: "每次8g", namePY: "meici8g", namePYFirst: "mc8g" },
        { id: 6, name: "每次9g", namePY: "meici9g", namePYFirst: "mc9g" },
        { id: 7, name: "每次10g", namePY: "meici10g", namePYFirst: "mc10g" },
        { id: 8, name: "每次11g", namePY: "meici11g", namePYFirst: "mc11g" },
        { id: 9, name: "每次12g", namePY: "meici12g", namePYFirst: "mc12g" },
        { id: 10, name: "每次13g", namePY: "meici13g", namePYFirst: "mc13g" },
        { id: 11, name: "每次14g", namePY: "meici14g", namePYFirst: "mc14g" },
        { id: 12, name: "每次15g", namePY: "meici15g", namePYFirst: "mc15g" },
        { id: 13, name: "每次16g", namePY: "meici16g", namePYFirst: "mc16g" },
        { id: 14, name: "每次17g", namePY: "meici17g", namePYFirst: "mc17g" },
        { id: 15, name: "每次18g", namePY: "meici18g", namePYFirst: "mc18g" },
    ],
    usageLevelWithDaFenAir: [
        { id: 1, name: "每次9g", namePY: "meici9g", namePYFirst: "mc9g" },
        { id: 2, name: "每次10g", namePY: "meici10g", namePYFirst: "mc10g" },
        { id: 3, name: "每次11g", namePY: "meici11g", namePYFirst: "mc11g" },
        { id: 4, name: "每次12g", namePY: "meici12g", namePYFirst: "mc12g" },
        { id: 5, name: "每次13g", namePY: "meici13g", namePYFirst: "mc13g" },
        { id: 6, name: "每次14g", namePY: "meici14g", namePYFirst: "mc14g" },
        { id: 7, name: "每次15g", namePY: "meici15g", namePYFirst: "mc15g" },
    ],
    requirement: [
        {
            id: 1,
            name: "饭前服用",
        },
        {
            id: 2,
            name: "饭后服用",
        },
        {
            id: 3,
            name: "空腹服用",
        },
        {
            id: 4,
            name: "睡前服用",
        },
        {
            id: 5,
            name: "调和送服",
        },
        {
            id: 6,
            name: "定时服用",
        },
        {
            id: 7,
            name: "少量频服",
        },
        {
            id: 8,
            name: "随时服用",
        },
    ],
    requirements: [
        {
            id: 1,
            name: "水煎400ml，分早晚2次空腹温服",
            namePY: "shuijian400mlfenzaowanliangcikongfuwenfu",
            namePYFirst: "SJ400mlFZWLCKFWF",
        },
        {
            id: 2,
            name: "水煎400ml，分早晚2次饭前温服",
            namePY: "shuijian400mlfenzaowanliangcifanqianwenfu",
            namePYFirst: "SJ400mlFZWLCFQWF",
        },
        {
            id: 3,
            name: "水煎400ml，分早晚2次饭后温服",
            namePY: "shuijian400mlfenzaowanliangcifanhouwenfu",
            namePYFirst: "SJ400mlFZWLCFHWF",
        },
        {
            id: 4,
            name: "水煎400ml，分早晚2次凉服",
            namePY: "shuijian400mlfenzaowanliangciliangfu",
            namePYFirst: "SJ400mlFZWLCLF",
        },
        {
            id: 5,
            name: "水煎400ml，一日3次空腹温服",
            namePY: "shuijian400mlyirisancikongfuwenfu",
            namePYFirst: "SJ400mlYRSCKFWF",
        },
        {
            id: 6,
            name: "水煎400ml，一日3次饭前温服",
            namePY: "shuijian400mlyirisancifanqianwenfu",
            namePYFirst: "SJ400mlYRSCFQWF",
        },
        {
            id: 7,
            name: "水煎400ml，一日3次饭后温服",
            namePY: "shuijian400mlyirisancifanhouwenfu",
            namePYFirst: "SJ400mlYRSCFHWF",
        },
        {
            id: 8,
            name: "水煎800ml，分6次饭前温服",
            namePY: "shuijian800mlfenliucifanqianwenfu",
            namePYFirst: "SJ800MLF6CFQWF",
        },
        {
            id: 9,
            name: "水煎800ml，分6次饭后温服",
            namePY: "shuijian800mlfenliucifanhouwenfu",
            namePYFirst: "SJ800MLF6CFHWF",
        },
    ],
    grade: [
        {
            name: "特等",
            value: "特等",
        },
        {
            name: "一等",
            value: "一等",
        },
        {
            name: "二等",
            value: "二等",
        },
        {
            name: "三等",
            value: "三等",
        },
        {
            name: "四等",
            value: "四等",
        },
        {
            name: "五等",
            value: "五等",
        },
        {
            name: "六等",
            value: "六等",
        },
        {
            name: "七等",
            value: "七等",
        },
        {
            name: "选货",
            value: "选货",
        },
        {
            name: "统货",
            value: "统货",
        },
    ],
    specification: ["中药饮片", "中药颗粒"],
    prescriptionDefaultValues: {
        dailyDosage: "1日1剂",
        usage: "煎服",
        freq: "1日3次",
        usageLevel: "每次200ml",
        specification: "中药饮片",
    },
    usageDays: [
        {
            id: 1,
            name: "约服10天",
            namePY: "yuefu10tian",
            namePYFirst: "yf10t",
        },
        {
            id: 2,
            name: "约服15天",
            namePY: "yuefu15tian",
            namePYFirst: "yf15t",
        },
        {
            id: 3,
            name: "约服20天",
            namePY: "yuefu20tian",
            namePYFirst: "yf20t",
        },
        {
            id: 4,
            name: "约服25天",
            namePY: "yuefu25tian",
            namePYFirst: "yf25t",
        },
        {
            id: 5,
            name: "约服30天",
            namePY: "yuefu30tian",
            namePYFirst: "yf30t",
        },
        {
            id: 6,
            name: "约服35天",
            namePY: "yuefu35tian",
            namePYFirst: "yf35t",
        },
        {
            id: 7,
            name: "约服40天",
            namePY: "yuefu40tian",
            namePYFirst: "yf40t",
        },
        {
            id: 8,
            name: "约服45天",
            namePY: "yuefu45tian",
            namePYFirst: "yf45t",
        },
        {
            id: 9,
            name: "约服50天",
            namePY: "yuefu50tian",
            namePYFirst: "yf50t",
        },
        {
            id: 10,
            name: "约服2月",
            namePY: "yuefu2yue",
            namePYFirst: "yf2y",
        },
        {
            id: 11,
            name: "约服3月",
            namePY: "yuefu3yue",
            namePYFirst: "yf3y",
        },
        {
            id: 12,
            name: "约服4月",
            namePY: "yuefu4yue",
            namePYFirst: "yf4y",
        },
    ],
    chinesePRUsageDefault: new Map([
        [
            "煎服",
            {
                usage: "煎服",
                dailyDosage: "1日1剂",
                freq: "1日3次",
                usageLevel: "每次150ml",
                usageDays: "",
            },
        ],
        [
            "煎药",
            {
                usage: "煎服",
                dailyDosage: "1日1剂",
                freq: "1日3次",
                usageLevel: "每次150ml",
                usageDays: "",
            },
        ],
        [
            "冲服",
            {
                usage: "冲服",
                dailyDosage: "1日1剂",
                freq: "1日3次",
                usageLevel: "每次150ml",
                usageDays: "",
            },
        ],
        [
            "颗粒剂",
            {
                usage: "冲服",
                dailyDosage: "1日1剂",
                freq: "1日3次",
                usageLevel: "每次1袋",
                usageDays: "",
            },
        ],
        [
            "制膏",
            {
                usage: "制膏",
                dailyDosage: "",
                freq: "1日2次",
                usageLevel: "每次20g",
                usageDays: "",
            },
        ],
        [
            "制丸",
            {
                usage: "制丸",
                dailyDosage: "",
                freq: "1日2次",
                usageLevel: "每次10g",
                usageDays: "",
            },
        ],
        [
            "外用",
            {
                usage: "外用",
                dailyDosage: "1日1剂",
                freq: "1日2次",
                usageLevel: "",
                usageDays: "",
            },
        ],
        [
            "贴敷",
            {
                usage: "贴敷",
                dailyDosage: "1日1剂",
                freq: "1日1次",
                usageLevel: "",
                usageDays: "",
            },
        ],
        [
            "打粉",
            {
                usage: "打粉",
                dailyDosage: "",
                freq: "1日2次",
                usageLevel: "每次6g",
                usageDays: "",
            },
        ],
        [
            "泡饮",
            {
                usage: "泡饮",
                dailyDosage: "",
                freq: "1日3次",
                usageLevel: "每次100ml",
                usageDays: "",
            },
        ],
        [
            "泡酒",
            {
                usage: "泡酒",
                dailyDosage: "",
                freq: "1日1次",
                usageLevel: "每次100ml",
                usageDays: "",
            },
        ],
        [
            "泡浴",
            {
                usage: "泡浴",
                dailyDosage: "1日1剂",
                freq: "1日1次",
                usageLevel: "",
                usageDays: "",
            },
        ],
        [
            "泡脚",
            {
                usage: "泡脚",
                dailyDosage: "1日1剂",
                freq: "1日1次",
                usageLevel: "",
                usageDays: "",
            },
        ],
        [
            "擦拭",
            {
                usage: "擦拭",
                dailyDosage: "1日1剂",
                freq: "1日1次",
                usageLevel: "",
                usageDays: "",
            },
        ],
    ]),
    chineseScoped: [
        //中药制法
        {
            medicineScopeId: "3",
            name: "煎药",
        },
        {
            medicineScopeId: "4",
            name: "制膏",
        },
        {
            medicineScopeId: "5",
            name: "打粉",
        },
        {
            medicineScopeId: "8",
            name: "颗粒剂",
        },
        {
            medicineScopeId: "9",
            name: "自煎",
        },
        {
            medicineScopeId: "10",
            name: "代煎",
        },
        {
            medicineScopeId: "11",
            name: "瓶装",
        },
        {
            medicineScopeId: "12",
            name: "袋装",
        },
        {
            medicineScopeId: "13",
            name: "普通打粉",
        },
        {
            medicineScopeId: "14",
            name: "精品打粉",
        },
        {
            medicineScopeId: "15",
            name: "水丸",
        },
        {
            medicineScopeId: "16",
            name: "小蜜丸",
        },
        {
            medicineScopeId: "17",
            name: "大蜜丸",
        },
        {
            medicineScopeId: "18",
            name: "浓缩水蜜丸",
        },
        {
            medicineScopeId: "19",
            name: "水蜜丸",
        },
        {
            medicineScopeId: "20",
            name: "精品水蜜丸",
        },
        {
            medicineScopeId: "21",
            name: "精品大蜜丸",
        },
        {
            medicineScopeId: "22",
            name: "精品水丸",
        },
        {
            medicineScopeId: "23",
            name: "浓缩水丸",
        },
    ],
    chineseUsageScopeId: [
        {
            id: "3",
            name: "煎药",
        },
        {
            id: "4",
            name: "制膏",
        },
        {
            id: "5",
            name: "打粉",
        },
        {
            id: "6",
            name: "制丸",
        },
        {
            id: "67",
            name: "颗粒",
        },
    ],
};

export default ChineseMedicine;
