const Intellisense = {
    mainComplain: {
        symptomList1: ["咳嗽", "干咳", "咳痰", "夜咳", "咽干", "咽痒", "咽痛", "咽部异物感", "反复感冒"],
        symptomList2: ["发热", "喷嚏", "流涕", "鼻塞", "头痛", "头晕", "耳鸣", "多汗", "少汗", "盗汗", "自汗"],
        symptomList3: ["胃胀", "胃痛", "腹胀", "腹痛", "腹泻", "恶心", "呕吐", "冒酸", "嗳气", "纳差", "吞咽困难"],
        symptomList4: ["便秘", "便溏", "便血", "里急后重", "排便不爽", "夜尿多", "尿频", "尿急", "尿痛", "尿不尽"],
        symptomList5: ["胸闷", "胸痛", "心悸", "气短", "气喘", "气促", "呼吸困难", "水肿", "失眠", "嗜睡"],
        symptomList6: ["肩痛", "肋痛", "背痛", "腰痛", "颈椎痛", "关节痛", "关节僵强", "四肢无力", "四肢麻木"],
        symptomList7: ["眼干", "口干", "口苦", "牙痛", "牙龈出血", "口腔溃疡", "口腔溃疡", "皮疹", "皮肤红斑", "皮肤瘙痒", "疖疔痈"],
        symptomList8: ["闭经", "痛经", "崩漏", "月经不调", "白带量多", "白带黄", "白带异味", "乳房肿块", "阳痿", "早泄"],
        extentCommonList: ["偶尔", "1天", "2天", "3天", "4天", "5天", "1周", "2周", "3周", "1个月", "2个月", "3个月", "半年", "1年"],
    },
    history: {
        symptomList1: ["既往体健", "未见明显异常"],
        symptomList2: ["否认药物过敏史", "否认慢性疾病", "否认传染病史", "否认遗传病史", "否认备孕", "否认怀孕"],
        symptomList3: ["胃胀", "胃痛", "腹胀", "腹痛", "恶心呕吐", "发酸", "嗳气", "消化不良", "食欲不振"],
        symptomList4: ["便秘", "腹泻", "溏泄", "便血", "里急后重", "排便不爽", "尿频", "尿急", "尿痛", "尿血"],
        symptomList5: ["胸闷", "胸痛", "心悸", "气短", "气喘", "乏力", "晕厥", "发绀", "水肿", "呼吸困难"],
        symptomList6: ["肩痛", "肋痛", "背痛", "腰痛", "颈椎痛", "关节痛", "牙痛", "牙龈出血", "口腔溃疡"],
        symptomList7: ["口干", "口苦", "口臭", "口渴", "渴不多饮", "厌食", "皮疹", "疱疹", "皮肤瘙痒", "痤疮"],
        symptomList8: ["失眠", "嗜睡", "痛经", "月经不调", "带下色白", "带下色黄", "赤白带", "阳痿", "早泄"],
        extentCommonList: ["偶尔", "1天", "2天", "3天", "4天", "5天", "1周", "2周", "3周", "1个月", "2个月", "3个月", "半年", "1年"],
        pasts: ["高血压", "心脏病", "糖尿病", "精神疾病", "肝炎", "肺结核", "哮喘", "甲亢"],
        allergic1: ["青霉素", "链霉素", "卡那霉素", "林可霉素", "头孢类", "磺胺类", "酒精", "碘伏"],
        allergic2: ["去痛片", "扑热息痛", "安痛定", "安定", "鲁米那", "阿司匹林", "普鲁卡因"],
        allergic3: ["花粉", "霉菌", "尘螨", "毛发皮屑", "牛奶", "鸡蛋", "大豆", "小麦", "花生", "鱼虾", "坚果"],
        personal1: ["不吸烟", "偶尔吸烟", "长期吸烟", "不饮酒", "偶尔饮酒", "长期饮酒"],
        personal2: ["未婚", "已婚", "未孕", "闭经", "有早产史", "有流产史", "有痛经史"],
        personal3: [
            "否认疫区旅居史",
            "否认传染病接触史",
            "否认粉尘/毒物/放射性物质接触史",
            "否认冶游史",
            "生活起居规律",
            "饮食习惯均衡",
            "无烟酒等不良嗜好",
            "否认外伤及手术史",
            "预防接种史不详",
        ],
    },
    symptomList: ["否认药物过敏史", "否认食物过敏史"],
    physicalExaminations: [
        {
            key: "sign",
            label: "体征",
            unit: "",
            list: [
                {
                    label: "",
                    value: ["生命体征平稳", "神志清楚", "精神反应尚可", "未见皮疹", "浅表淋巴结未扪及肿大"],
                },
                {
                    label: "",
                    value: ["双肺呼吸音清晰", "双肺叩诊呈清音", "未闻及异常呼吸音及干湿啰音"],
                },
                {
                    label: "",
                    value: ["心前区无异常搏动", "心音有力心律齐", "各瓣膜区未闻及明显杂音", "未闻及心包摩擦音"],
                },
                {
                    label: "",
                    value: ["腹部柔软", "无腹肌紧张", "全腹部无压痛", "肠鸣音活跃", "双肾区无叩痛"],
                },
            ],
        },
        {
            key: "chineseExamination",
            label: "望闻切",
            unit: "",
            list: [
                {
                    label: "",
                    mergeWithNextGroup: true,
                    value: ["形体壮实", "形体羸弱", "形体中等", "形体肥胖", "形体较瘦"],
                },
                {
                    label: "",
                    mergeWithNextGroup: true,
                    value: ["精神良好", "精神一般", "精神不振", "精神萎靡", "神志清楚", "意识模糊"],
                },
                {
                    label: "",
                    value: ["面色淡白", "面色苍白", "面色泛黄", "面色赤红", "面色青", "面色黑"],
                },
                {
                    label: "tongue1",
                    displayPrefix: "舌",
                    joinDelimiter: "而",
                    joinNum: 2,
                    joinAnotherGroup: "tongue2",
                    mergeWithNextGroup: true,
                    value: ["淡红", "淡白", "红", "绛", "紫"],
                },
                {
                    label: "tongue2",
                    displayPrefix: "舌",
                    mergeWithNextGroup: true,
                    joinDelimiter: "而",
                    joinAnotherGroup: "tongue1",
                    joinNum: 2,
                    value: ["胖大", "瘦薄", "裂纹", "齿痕"],
                },
                {
                    label: "moss1",
                    displayPrefix: "苔",
                    joinDelimiter: "而",
                    joinNum: 2,
                    mergeWithNextGroup: true,
                    joinAnotherGroup: "moss2",
                    value: ["白", "黄", "灰", "黑"],
                },
                {
                    label: "moss2",
                    displayPrefix: "苔",
                    joinDelimiter: "而",
                    joinAnotherGroup: "moss1",
                    joinNum: 2,
                    value: ["厚", "薄", "腐", "腻", "润", "燥", "剥落"],
                },
                {
                    label: "pulse1",
                    displayPrefix: "脉",
                    joinNum: 3,
                    mergeWithNextGroup: true,
                    joinAnotherGroup: "pulse2",
                    value: ["浮", "沉", "迟", "数", "虚", "实", "洪", "细"],
                },
                {
                    label: "pulse2",
                    displayPrefix: "脉",
                    joinAnotherGroup: "pulse1",
                    joinNum: 3,
                    value: ["滑", "涩", "弦", "紧", "促", "结", "代", "濡"],
                },
            ],
        },
        {
            key: "temperature",
            label: "体温",
            unit: "℃",
            list: [
                {
                    label: "",
                    value: ["36", "36.5", "37", "37.5", "38", "38.5", "39", "39.5", "40", "40.5", "41", "41.5", "42"],
                },
            ],
        },
        {
            key: "heartRate",
            label: "脉搏",
            unit: "bpm",
            list: [
                {
                    label: "",
                    value: [
                        "30",
                        "35",
                        "40",
                        "45",
                        "50",
                        "55",
                        "60",
                        "65",
                        "70",
                        "75",
                        "80",
                        "85",
                        "90",
                        "95",
                        "100",
                        "105",
                        "110",
                        "115",
                        "120",
                        "125",
                        "130",
                        "135",
                        "140",
                        "145",
                        "150",
                        "155",
                        "160",
                        "165",
                        "170",
                        "175",
                        "180",
                    ],
                },
            ],
        },
        {
            key: "breathe",
            label: "呼吸",
            unit: "次/分",
            list: [
                {
                    value: [
                        "10",
                        "11",
                        "12",
                        "13",
                        "14",
                        "15",
                        "16",
                        "17",
                        "18",
                        "19",
                        "20",
                        "21",
                        "22",
                        "23",
                        "24",
                        "25",
                        "26",
                        "27",
                        "28",
                        "29",
                        "30",
                        "31",
                        "32",
                        "33",
                        "34",
                        "35",
                        "36",
                        "37",
                        "38",
                        "39",
                    ],
                },
            ],
        },
        {
            key: "bloodPressure",
            label: "血压",
            unit: "mmHg",
            list: [
                {
                    label: "收缩压",
                    showLabel: true,
                    value: [
                        "70",
                        "75",
                        "80",
                        "85",
                        "90",
                        "95",
                        "100",
                        "105",
                        "110",
                        "115",
                        "120",
                        "125",
                        "130",
                        "135",
                        "140",
                        "145",
                        "150",
                        "150",
                        "160",
                        "165",
                        "170",
                        "175",
                        "180",
                        "185",
                        "190",
                    ],
                },
                {
                    label: "舒张压",
                    showLabel: true,
                    value: ["50", "55", "60", "65", "70", "75", "80", "85", "90", "95", "100", "105", "110"],
                },
            ],
        },
        {
            key: "weight",
            label: "体重",
            unit: "kg",
            list: [
                {
                    label: "",
                    value: [
                        "5",
                        "10",
                        "20",
                        "25",
                        "30",
                        "35",
                        "40",
                        "45",
                        "50",
                        "55",
                        "60",
                        "65",
                        "70",
                        "75",
                        "80",
                        "85",
                        "90",
                        "95",
                        "100",
                    ],
                },
            ],
        },
        {
            key: "height",
            label: "身高",
            unit: "cm",
            list: [
                {
                    label: "婴儿童(45~100)",
                    showLabel: true,
                    value: [
                        "45",
                        "46",
                        "47",
                        "48",
                        "49",
                        "50",
                        "51",
                        "52",
                        "53",
                        "54",
                        "55",
                        "56",
                        "57",
                        "58",
                        "59",
                        "60",
                        "61",
                        "62",
                        "63",
                        "64",
                        "65",
                        "66",
                        "67",
                        "68",
                        "69",
                        "70",
                        "71",
                        "72",
                        "73",
                        "74",
                        "75",
                        "76",
                        "77",
                        "78",
                        "79",
                        "80",
                        "81",
                        "82",
                        "83",
                        "84",
                        "85",
                        "86",
                        "87",
                        "88",
                        "89",
                        "90",
                        "91",
                        "92",
                        "93",
                        "94",
                        "95",
                        "96",
                        "97",
                        "98",
                        "99",
                        "100",
                    ],
                },
                {
                    label: "青少年(100~150)",
                    showLabel: true,
                    value: [
                        "100",
                        "101",
                        "102",
                        "103",
                        "104",
                        "105",
                        "106",
                        "107",
                        "108",
                        "109",
                        "110",
                        "111",
                        "112",
                        "113",
                        "114",
                        "115",
                        "116",
                        "117",
                        "118",
                        "119",
                        "120",
                        "121",
                        "122",
                        "123",
                        "124",
                        "125",
                        "126",
                        "127",
                        "128",
                        "129",
                        "130",
                        "131",
                        "132",
                        "133",
                        "134",
                        "135",
                        "136",
                        "137",
                        "138",
                        "139",
                        "140",
                        "141",
                        "142",
                        "143",
                        "144",
                        "145",
                        "146",
                        "147",
                        "148",
                        "149",
                        "150",
                    ],
                },
                {
                    label: "成年人(150~200)",
                    showLabel: true,
                    value: [
                        "150",
                        "151",
                        "152",
                        "153",
                        "154",
                        "155",
                        "156",
                        "157",
                        "158",
                        "159",
                        "160",
                        "161",
                        "162",
                        "163",
                        "164",
                        "165",
                        "166",
                        "167",
                        "168",
                        "169",
                        "170",
                        "171",
                        "172",
                        "173",
                        "174",
                        "175",
                        "176",
                        "177",
                        "178",
                        "179",
                        "180",
                        "181",
                        "182",
                        "183",
                        "184",
                        "185",
                        "186",
                        "187",
                        "188",
                        "189",
                        "190",
                        "191",
                        "192",
                        "193",
                        "194",
                        "195",
                        "196",
                        "197",
                        "198",
                        "199",
                        "200",
                    ],
                },
            ],
        },
    ],
    obstetricalHistory: {
        maritalStatusList: ["未婚", "已婚未育", "已婚已育", "适龄婚育", "配偶体健", "离异", "丧偶"],
        fertilitySituationList: [
            "孕0",
            "孕1",
            "孕2",
            "孕3",
            "孕4",
            "孕5",
            "孕6",
            "孕7",
            "孕8",
            "孕9",
            "产0",
            "产1",
            "产2",
            "产3",
            "产4",
            "产5",
            "产6",
            "产7",
            "产8",
            "产9",
        ],
        menarcheAgeList: ["10", "11", "12", "13", "14", "15", "16", "17", "18", "19"],
        menstrualDaysList: ["2", "3", "4", "5", "6", "7", "8", "9", "10", "11"],
        menstrualCycleList: ["21", "22", "23", "24", "25", "26", "27", "28", "29", "30"],
        meridionalChromaList: [
            "经量正常",
            "量多",
            "量少",
            "闭经",
            "经色正常",
            "色淡红",
            "色鲜红",
            "色深红",
            "色暗紫",
            "经质正常",
            "质稀",
            "质稠",
            "夹有血块",
            "痛经",
            "无痛经",
            "绝经",
        ],
        leukorrheaSituationList: [
            "白带正常",
            "白带量多",
            "白带质稀",
            "白带质稠",
            "白带异味",
            "白带色黄",
            "白带色灰",
            "脓性白带",
            "血色白带",
            "水样白带",
            "豆渣样白带",
            "泡沫状白带",
        ],
    },

    //口腔相关数据
    dentistry: {
        toothPos: ["上", "下", "左", "右", "左上", "左下", "右上", "右下"],
        toothLabel: ["前牙", "后牙", "半口牙", "全口牙", "乳牙"],
        symptoms: [
            "松动",
            "疼痛",
            "有洞",
            "缺失",
            "折断",
            "咀嚼痛",
            "冷热刺激痛",
            "冷热酸甜痛",
            "刷牙出血",
            "咬合不适",
            "牙列不齐",
            "食物嵌塞",
            "牙体变色",
            "牙齿黑斑",
            "填充物脱落",
        ],
        timeList: ["1天", "2天", "3天", "4天", "5天", "1周", "1个月", "2周", "3周", "2个月", "半年", "1年"],
        requirementsList: ["要求洗牙", "要求拔牙", "要求补牙", "要求镶牙", "要求矫正", "要求美白"],
    },
};

export { Intellisense };
