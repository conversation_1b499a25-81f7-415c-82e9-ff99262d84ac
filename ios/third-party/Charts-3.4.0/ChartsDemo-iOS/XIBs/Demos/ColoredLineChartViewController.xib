<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="13196" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13196"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="ColoredLineChartViewController" customModule="ChartsDemo" customModuleProvider="target">
            <connections>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
                <outletCollection property="chartViews" destination="Oqd-Ej-1xl" collectionClass="NSMutableArray" id="3DD-X7-Fb3"/>
                <outletCollection property="chartViews" destination="njk-0O-Wam" collectionClass="NSMutableArray" id="n3r-Fb-eC4"/>
                <outletCollection property="chartViews" destination="9Lp-os-3Vt" collectionClass="NSMutableArray" id="vcK-0L-VT1"/>
                <outletCollection property="chartViews" destination="nGo-AX-wvC" collectionClass="NSMutableArray" id="EH0-k2-fX4"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Oqd-Ej-1xl" customClass="LineChartView" customModule="Charts">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="167"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="njk-0O-Wam" customClass="LineChartView" customModule="Charts">
                    <rect key="frame" x="0.0" y="167" width="375" height="166.5"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9Lp-os-3Vt" customClass="LineChartView" customModule="Charts">
                    <rect key="frame" x="0.0" y="333.5" width="375" height="167"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nGo-AX-wvC" customClass="LineChartView" customModule="Charts">
                    <rect key="frame" x="0.0" y="500.5" width="375" height="166.5"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.94117647059999998" green="0.94117647059999998" blue="0.94117647059999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="YgW-AE-vxr" firstAttribute="trailing" secondItem="nGo-AX-wvC" secondAttribute="trailing" id="0yE-ju-Lag"/>
                <constraint firstItem="njk-0O-Wam" firstAttribute="top" secondItem="Oqd-Ej-1xl" secondAttribute="bottom" id="145-vk-wcD"/>
                <constraint firstItem="Oqd-Ej-1xl" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="3NA-if-rAO"/>
                <constraint firstItem="Oqd-Ej-1xl" firstAttribute="leading" secondItem="YgW-AE-vxr" secondAttribute="leading" id="6Mc-iO-BuY"/>
                <constraint firstItem="nGo-AX-wvC" firstAttribute="leading" secondItem="YgW-AE-vxr" secondAttribute="leading" id="7QR-kC-1Ia"/>
                <constraint firstItem="YgW-AE-vxr" firstAttribute="trailing" secondItem="njk-0O-Wam" secondAttribute="trailing" id="BeR-he-87z"/>
                <constraint firstItem="nGo-AX-wvC" firstAttribute="height" secondItem="9Lp-os-3Vt" secondAttribute="height" id="MSX-1T-Hcp"/>
                <constraint firstItem="njk-0O-Wam" firstAttribute="height" secondItem="9Lp-os-3Vt" secondAttribute="height" id="Sfa-tz-95q"/>
                <constraint firstItem="9Lp-os-3Vt" firstAttribute="top" secondItem="njk-0O-Wam" secondAttribute="bottom" id="V1f-0d-jnp"/>
                <constraint firstItem="9Lp-os-3Vt" firstAttribute="leading" secondItem="YgW-AE-vxr" secondAttribute="leading" id="Yea-IJ-jo4"/>
                <constraint firstItem="YgW-AE-vxr" firstAttribute="trailing" secondItem="9Lp-os-3Vt" secondAttribute="trailing" id="hwF-Vk-0Oa"/>
                <constraint firstItem="njk-0O-Wam" firstAttribute="leading" secondItem="YgW-AE-vxr" secondAttribute="leading" id="iEb-Yv-zMg"/>
                <constraint firstItem="YgW-AE-vxr" firstAttribute="trailing" secondItem="Oqd-Ej-1xl" secondAttribute="trailing" id="mC3-xy-2CS"/>
                <constraint firstItem="YgW-AE-vxr" firstAttribute="bottom" secondItem="nGo-AX-wvC" secondAttribute="bottom" id="tGu-Tb-gOn"/>
                <constraint firstItem="njk-0O-Wam" firstAttribute="height" secondItem="Oqd-Ej-1xl" secondAttribute="height" id="wRR-If-cA5"/>
                <constraint firstItem="nGo-AX-wvC" firstAttribute="top" secondItem="9Lp-os-3Vt" secondAttribute="bottom" id="zNN-fl-qjC"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="YgW-AE-vxr"/>
            <point key="canvasLocation" x="157.5" y="222.5"/>
        </view>
    </objects>
</document>
