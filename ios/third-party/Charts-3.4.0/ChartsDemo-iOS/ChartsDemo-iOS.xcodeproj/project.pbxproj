// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0439A3541C9FF95F00496F83 /* PiePolylineChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0439A3521C9FF95F00496F83 /* PiePolylineChartViewController.m */; };
		0471CBFC1CA1090A00E52DBC /* PiePolylineChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 0471CBFB1CA1090A00E52DBC /* PiePolylineChartViewController.xib */; };
		0630AE511D81271B008859B0 /* Charts.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0630AE411D8126C0008859B0 /* Charts.framework */; };
		0630AE521D81271B008859B0 /* Charts.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 0630AE411D8126C0008859B0 /* Charts.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		068A9B191FBBF351003CF1AD /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 5B8EAF2F1AB32E15009697AA /* Images.xcassets */; };
		068A9B1A1FBBF355003CF1AD /* Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = B64D06EA1FB199130067418D /* Launch Screen.storyboard */; };
		068A9B1B1FBBF366003CF1AD /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 5B613DA31D526AD5000F1E98 /* <EMAIL> */; };
		225B36201F6EB9A50005B3D5 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B361F1F6EB9A50005B3D5 /* AppDelegate.swift */; };
		225B36311F6EB9EE0005B3D5 /* DemoBaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B362F1F6EB9EE0005B3D5 /* DemoBaseViewController.swift */; };
		225B36321F6EB9EE0005B3D5 /* DemoListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36301F6EB9EE0005B3D5 /* DemoListViewController.swift */; };
		225B36381F6EBA040005B3D5 /* IntAxisValueFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36341F6EB9FE0005B3D5 /* IntAxisValueFormatter.swift */; };
		225B36391F6EBA040005B3D5 /* DayAxisValueFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36351F6EB9FE0005B3D5 /* DayAxisValueFormatter.swift */; };
		225B363A1F6EBA040005B3D5 /* DateValueFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36361F6EB9FE0005B3D5 /* DateValueFormatter.swift */; };
		225B363B1F6EBA040005B3D5 /* LargeValueFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36371F6EB9FE0005B3D5 /* LargeValueFormatter.swift */; };
		225B36401F6EBA1D0005B3D5 /* XYMarkerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B363D1F6EBA180005B3D5 /* XYMarkerView.swift */; };
		225B36411F6EBA1D0005B3D5 /* BalloonMarker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B363E1F6EBA180005B3D5 /* BalloonMarker.swift */; };
		225B36421F6EBA1D0005B3D5 /* RadarMarkerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B363F1F6EBA180005B3D5 /* RadarMarkerView.swift */; };
		225B365B1F6EBA470005B3D5 /* MultipleLinesChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36441F6EBA410005B3D5 /* MultipleLinesChartViewController.swift */; };
		225B365C1F6EBA470005B3D5 /* MultipleBarChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36451F6EBA410005B3D5 /* MultipleBarChartViewController.swift */; };
		225B365D1F6EBA470005B3D5 /* HalfPieChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36461F6EBA410005B3D5 /* HalfPieChartViewController.swift */; };
		225B365E1F6EBA470005B3D5 /* LineChartTimeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36471F6EBA410005B3D5 /* LineChartTimeViewController.swift */; };
		225B365F1F6EBA470005B3D5 /* ColoredLineChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36481F6EBA410005B3D5 /* ColoredLineChartViewController.swift */; };
		225B36601F6EBA470005B3D5 /* LineChart2ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36491F6EBA410005B3D5 /* LineChart2ViewController.swift */; };
		225B36611F6EBA470005B3D5 /* LineChart1ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B364A1F6EBA410005B3D5 /* LineChart1ViewController.swift */; };
		225B36621F6EBA470005B3D5 /* NegativeStackedBarChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B364B1F6EBA410005B3D5 /* NegativeStackedBarChartViewController.swift */; };
		225B36631F6EBA470005B3D5 /* ScatterChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B364C1F6EBA410005B3D5 /* ScatterChartViewController.swift */; };
		225B36641F6EBA470005B3D5 /* StackedBarChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B364D1F6EBA410005B3D5 /* StackedBarChartViewController.swift */; };
		225B36651F6EBA470005B3D5 /* BubbleChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B364E1F6EBA410005B3D5 /* BubbleChartViewController.swift */; };
		225B36661F6EBA470005B3D5 /* LineChartFilledViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B364F1F6EBA410005B3D5 /* LineChartFilledViewController.swift */; };
		225B36671F6EBA470005B3D5 /* PositiveNegativeBarChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36501F6EBA410005B3D5 /* PositiveNegativeBarChartViewController.swift */; };
		225B36681F6EBA470005B3D5 /* AnotherBarChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36511F6EBA410005B3D5 /* AnotherBarChartViewController.swift */; };
		225B36691F6EBA470005B3D5 /* PieChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36521F6EBA410005B3D5 /* PieChartViewController.swift */; };
		225B366A1F6EBA470005B3D5 /* CubicLineChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36531F6EBA410005B3D5 /* CubicLineChartViewController.swift */; };
		225B366B1F6EBA470005B3D5 /* RadarChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36541F6EBA410005B3D5 /* RadarChartViewController.swift */; };
		225B366C1F6EBA470005B3D5 /* HorizontalBarChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36551F6EBA410005B3D5 /* HorizontalBarChartViewController.swift */; };
		225B366D1F6EBA470005B3D5 /* PiePolylineChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36561F6EBA410005B3D5 /* PiePolylineChartViewController.swift */; };
		225B366E1F6EBA470005B3D5 /* CombinedChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36571F6EBA410005B3D5 /* CombinedChartViewController.swift */; };
		225B366F1F6EBA470005B3D5 /* BarChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36581F6EBA410005B3D5 /* BarChartViewController.swift */; };
		225B36701F6EBA470005B3D5 /* SinusBarChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B36591F6EBA410005B3D5 /* SinusBarChartViewController.swift */; };
		225B36711F6EBA470005B3D5 /* CandleStickChartViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225B365A1F6EBA410005B3D5 /* CandleStickChartViewController.swift */; };
		225B36721F6EBA5E0005B3D5 /* DemoListViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5B8EAF231AB3271B009697AA /* DemoListViewController.xib */; };
		225B36731F6EBA5E0005B3D5 /* RadarMarkerView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5B613DB41D526EA8000F1E98 /* RadarMarkerView.xib */; };
		225B36741F6EBA640005B3D5 /* AnotherBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED111ABBFB2B0013F194 /* AnotherBarChartViewController.xib */; };
		225B36751F6EBA640005B3D5 /* BarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BD47E641ABB424E008FCEC6 /* BarChartViewController.xib */; };
		225B36761F6EBA640005B3D5 /* BubbleChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 55E3564D1ADC638F00A57971 /* BubbleChartViewController.xib */; };
		225B36771F6EBA640005B3D5 /* CandleStickChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED2B1ABC160F0013F194 /* CandleStickChartViewController.xib */; };
		225B36781F6EBA640005B3D5 /* ColoredLineChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED3A1ABC199F0013F194 /* ColoredLineChartViewController.xib */; };
		225B36791F6EBA640005B3D5 /* CombinedChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BDEDC461ABB871E007D3A60 /* CombinedChartViewController.xib */; };
		225B367A1F6EBA640005B3D5 /* CubicLineChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED301ABC18F00013F194 /* CubicLineChartViewController.xib */; };
		225B367B1F6EBA640005B3D5 /* HalfPieChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BE6737F1D5B496500A87BA2 /* HalfPieChartViewController.xib */; };
		225B367C1F6EBA640005B3D5 /* HorizontalBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BDEDC401ABB7F73007D3A60 /* HorizontalBarChartViewController.xib */; };
		225B367D1F6EBA640005B3D5 /* LineChart1ViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BD8F0731AB89CE500566E05 /* LineChart1ViewController.xib */; };
		225B367E1F6EBA640005B3D5 /* LineChart2ViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BD47E5F1ABB3C91008FCEC6 /* LineChart2ViewController.xib */; };
		225B367F1F6EBA640005B3D5 /* LineChartFilledViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BE6738E1D5B4E0900A87BA2 /* LineChartFilledViewController.xib */; };
		225B36801F6EBA640005B3D5 /* LineChartTimeViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BE673911D5B4E0900A87BA2 /* LineChartTimeViewController.xib */; };
		225B36811F6EBA640005B3D5 /* MultipleBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED211ABC0BE20013F194 /* MultipleBarChartViewController.xib */; };
		225B36821F6EBA640005B3D5 /* MultipleLinesChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED241ABC0BE20013F194 /* MultipleLinesChartViewController.xib */; };
		225B36831F6EBA640005B3D5 /* NegativeStackedBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5B9624401B38608C007763E2 /* NegativeStackedBarChartViewController.xib */; };
		225B36841F6EBA640005B3D5 /* PieChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5B0CC7841ABB875400665592 /* PieChartViewController.xib */; };
		225B36851F6EBA640005B3D5 /* PiePolylineChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 0471CBFB1CA1090A00E52DBC /* PiePolylineChartViewController.xib */; };
		225B36861F6EBA640005B3D5 /* PositiveNegativeBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BE7E7611C693098000A0377 /* PositiveNegativeBarChartViewController.xib */; };
		225B36871F6EBA640005B3D5 /* RadarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED351ABC192F0013F194 /* RadarChartViewController.xib */; };
		225B36881F6EBA640005B3D5 /* RealmDemosViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5B1B5DA91E911A15006D1375 /* RealmDemosViewController.xib */; };
		225B36891F6EBA640005B3D5 /* ScatterChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED171ABBFB340013F194 /* ScatterChartViewController.xib */; };
		225B368A1F6EBA640005B3D5 /* SinusBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED3F1ABC1AC60013F194 /* SinusBarChartViewController.xib */; };
		225B368B1F6EBA640005B3D5 /* StackedBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED1A1ABBFB340013F194 /* StackedBarChartViewController.xib */; };
		225B368C1F6EBBB00005B3D5 /* Charts.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0630AE411D8126C0008859B0 /* Charts.framework */; };
		225B368D1F6EBBB00005B3D5 /* Charts.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 0630AE411D8126C0008859B0 /* Charts.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		55E356501ADC638F00A57971 /* BubbleChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 55E3564D1ADC638F00A57971 /* BubbleChartViewController.xib */; };
		55E356511ADC638F00A57971 /* BubbleChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 55E3564F1ADC638F00A57971 /* BubbleChartViewController.m */; };
		5B0CC7851ABB875400665592 /* PieChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5B0CC7831ABB875400665592 /* PieChartViewController.m */; };
		5B0CC7861ABB875400665592 /* PieChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5B0CC7841ABB875400665592 /* PieChartViewController.xib */; };
		5B1B5DAB1E911A15006D1375 /* RealmDemosViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5B1B5DA91E911A15006D1375 /* RealmDemosViewController.xib */; };
		5B57BBB51A9B26AA0036A6CC /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 5B57BBB41A9B26AA0036A6CC /* main.m */; };
		5B57BBB81A9B26AA0036A6CC /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 5B57BBB71A9B26AA0036A6CC /* AppDelegate.m */; };
		5B57BBBB1A9B26AA0036A6CC /* DemoListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5B57BBBA1A9B26AA0036A6CC /* DemoListViewController.m */; };
		5B613DA41D526AD5000F1E98 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 5B613DA31D526AD5000F1E98 /* <EMAIL> */; };
		5B613DB31D526DED000F1E98 /* RadarMarkerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5B613DB21D526DED000F1E98 /* RadarMarkerView.swift */; };
		5B613DB51D526EA8000F1E98 /* RadarMarkerView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5B613DB41D526EA8000F1E98 /* RadarMarkerView.xib */; };
		5B613DF01D5A50B6000F1E98 /* XYMarkerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5B613DEF1D5A50B6000F1E98 /* XYMarkerView.swift */; };
		5B613DF21D5A60DF000F1E98 /* LargeValueFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5B613DF11D5A60DF000F1E98 /* LargeValueFormatter.swift */; };
		5B8EAF241AB3271B009697AA /* DemoListViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5B8EAF231AB3271B009697AA /* DemoListViewController.xib */; };
		5B8EAF281AB32CF5009697AA /* DemoBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5B8EAF261AB32CF5009697AA /* DemoBaseViewController.m */; };
		5B8EAF301AB32E15009697AA /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 5B8EAF2F1AB32E15009697AA /* Images.xcassets */; };
		5B9624411B38608C007763E2 /* NegativeStackedBarChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5B96243F1B38608C007763E2 /* NegativeStackedBarChartViewController.m */; };
		5B9624421B38608C007763E2 /* NegativeStackedBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5B9624401B38608C007763E2 /* NegativeStackedBarChartViewController.xib */; };
		5BD47E5B1ABB0263008FCEC6 /* BalloonMarker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5BD47E5A1ABB0263008FCEC6 /* BalloonMarker.swift */; };
		5BD47E601ABB3C91008FCEC6 /* LineChart2ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BD47E5E1ABB3C91008FCEC6 /* LineChart2ViewController.m */; };
		5BD47E611ABB3C91008FCEC6 /* LineChart2ViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BD47E5F1ABB3C91008FCEC6 /* LineChart2ViewController.xib */; };
		5BD47E651ABB424E008FCEC6 /* BarChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BD47E631ABB424E008FCEC6 /* BarChartViewController.m */; };
		5BD47E661ABB424E008FCEC6 /* BarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BD47E641ABB424E008FCEC6 /* BarChartViewController.xib */; };
		5BD8F0741AB89CE500566E05 /* LineChart1ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BD8F0721AB89CE500566E05 /* LineChart1ViewController.m */; };
		5BD8F0751AB89CE500566E05 /* LineChart1ViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BD8F0731AB89CE500566E05 /* LineChart1ViewController.xib */; };
		5BDEDC411ABB7F73007D3A60 /* HorizontalBarChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BDEDC3F1ABB7F73007D3A60 /* HorizontalBarChartViewController.m */; };
		5BDEDC421ABB7F73007D3A60 /* HorizontalBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BDEDC401ABB7F73007D3A60 /* HorizontalBarChartViewController.xib */; };
		5BDEDC471ABB871E007D3A60 /* CombinedChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BDEDC451ABB871E007D3A60 /* CombinedChartViewController.m */; };
		5BDEDC481ABB871E007D3A60 /* CombinedChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BDEDC461ABB871E007D3A60 /* CombinedChartViewController.xib */; };
		5BE377DE1D425151006EB34F /* DayAxisValueFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BE377DD1D425151006EB34F /* DayAxisValueFormatter.m */; };
		5BE377F21D47FDF1006EB34F /* IntAxisValueFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BE377F11D47FDF1006EB34F /* IntAxisValueFormatter.m */; };
		5BE673801D5B496500A87BA2 /* HalfPieChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BE6737E1D5B496500A87BA2 /* HalfPieChartViewController.m */; };
		5BE673811D5B496500A87BA2 /* HalfPieChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BE6737F1D5B496500A87BA2 /* HalfPieChartViewController.xib */; };
		5BE673921D5B4E0900A87BA2 /* LineChartFilledViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BE6738D1D5B4E0900A87BA2 /* LineChartFilledViewController.m */; };
		5BE673931D5B4E0900A87BA2 /* LineChartFilledViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BE6738E1D5B4E0900A87BA2 /* LineChartFilledViewController.xib */; };
		5BE673941D5B4E0900A87BA2 /* LineChartTimeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BE673901D5B4E0900A87BA2 /* LineChartTimeViewController.m */; };
		5BE673951D5B4E0900A87BA2 /* LineChartTimeViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BE673911D5B4E0900A87BA2 /* LineChartTimeViewController.xib */; };
		5BE6739A1D5BAD7E00A87BA2 /* DateValueFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BE673991D5BAD7E00A87BA2 /* DateValueFormatter.m */; };
		5BE7E7621C693098000A0377 /* PositiveNegativeBarChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BE7E7601C693098000A0377 /* PositiveNegativeBarChartViewController.m */; };
		5BE7E7631C693098000A0377 /* PositiveNegativeBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BE7E7611C693098000A0377 /* PositiveNegativeBarChartViewController.xib */; };
		5BEAED121ABBFB2B0013F194 /* AnotherBarChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BEAED101ABBFB2B0013F194 /* AnotherBarChartViewController.m */; };
		5BEAED131ABBFB2B0013F194 /* AnotherBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED111ABBFB2B0013F194 /* AnotherBarChartViewController.xib */; };
		5BEAED1B1ABBFB340013F194 /* ScatterChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BEAED161ABBFB340013F194 /* ScatterChartViewController.m */; };
		5BEAED1C1ABBFB340013F194 /* ScatterChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED171ABBFB340013F194 /* ScatterChartViewController.xib */; };
		5BEAED1D1ABBFB340013F194 /* StackedBarChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BEAED191ABBFB340013F194 /* StackedBarChartViewController.m */; };
		5BEAED1E1ABBFB340013F194 /* StackedBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED1A1ABBFB340013F194 /* StackedBarChartViewController.xib */; };
		5BEAED251ABC0BE20013F194 /* MultipleBarChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BEAED201ABC0BE20013F194 /* MultipleBarChartViewController.m */; };
		5BEAED261ABC0BE20013F194 /* MultipleBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED211ABC0BE20013F194 /* MultipleBarChartViewController.xib */; };
		5BEAED271ABC0BE20013F194 /* MultipleLinesChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BEAED231ABC0BE20013F194 /* MultipleLinesChartViewController.m */; };
		5BEAED281ABC0BE20013F194 /* MultipleLinesChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED241ABC0BE20013F194 /* MultipleLinesChartViewController.xib */; };
		5BEAED2C1ABC160F0013F194 /* CandleStickChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BEAED2A1ABC160F0013F194 /* CandleStickChartViewController.m */; };
		5BEAED2D1ABC160F0013F194 /* CandleStickChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED2B1ABC160F0013F194 /* CandleStickChartViewController.xib */; };
		5BEAED311ABC18F00013F194 /* CubicLineChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BEAED2F1ABC18F00013F194 /* CubicLineChartViewController.m */; };
		5BEAED321ABC18F00013F194 /* CubicLineChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED301ABC18F00013F194 /* CubicLineChartViewController.xib */; };
		5BEAED361ABC192F0013F194 /* RadarChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BEAED341ABC192F0013F194 /* RadarChartViewController.m */; };
		5BEAED371ABC192F0013F194 /* RadarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED351ABC192F0013F194 /* RadarChartViewController.xib */; };
		5BEAED3B1ABC199F0013F194 /* ColoredLineChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BEAED391ABC199F0013F194 /* ColoredLineChartViewController.m */; };
		5BEAED3C1ABC199F0013F194 /* ColoredLineChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED3A1ABC199F0013F194 /* ColoredLineChartViewController.xib */; };
		5BEAED401ABC1AC60013F194 /* SinusBarChartViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BEAED3E1ABC1AC60013F194 /* SinusBarChartViewController.m */; };
		5BEAED411ABC1AC60013F194 /* SinusBarChartViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 5BEAED3F1ABC1AC60013F194 /* SinusBarChartViewController.xib */; };
		B64D06EB1FB199130067418D /* Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = B64D06EA1FB199130067418D /* Launch Screen.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		0630AE401D8126C0008859B0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0630AE391D8126C0008859B0 /* Charts.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 06165F241D8110E600722320;
			remoteInfo = Charts;
		};
		0630AE421D8126C0008859B0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0630AE391D8126C0008859B0 /* Charts.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 06165F2E1D8110E600722320;
			remoteInfo = ChartsTests;
		};
		0630AE531D81271B008859B0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0630AE391D8126C0008859B0 /* Charts.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = A58A4ED274A941CA248EA921;
			remoteInfo = Charts;
		};
		225B368E1F6EBBB00005B3D5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0630AE391D8126C0008859B0 /* Charts.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = A58A4ED274A941CA248EA921;
			remoteInfo = Charts;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		225B36901F6EBBB10005B3D5 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				225B368D1F6EBBB00005B3D5 /* Charts.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		5BB4B0751ACA710D00E2EF4D /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				0630AE521D81271B008859B0 /* Charts.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0439A3511C9FF95F00496F83 /* PiePolylineChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PiePolylineChartViewController.h; sourceTree = "<group>"; };
		0439A3521C9FF95F00496F83 /* PiePolylineChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PiePolylineChartViewController.m; sourceTree = "<group>"; };
		0471CBFB1CA1090A00E52DBC /* PiePolylineChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = PiePolylineChartViewController.xib; sourceTree = "<group>"; };
		0630AE391D8126C0008859B0 /* Charts.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = Charts.xcodeproj; path = ../Charts.xcodeproj; sourceTree = "<group>"; };
		225B361D1F6EB9A50005B3D5 /* ChartsDemo-iOS-Swift.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "ChartsDemo-iOS-Swift.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		225B361F1F6EB9A50005B3D5 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		225B362F1F6EB9EE0005B3D5 /* DemoBaseViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DemoBaseViewController.swift; sourceTree = "<group>"; };
		225B36301F6EB9EE0005B3D5 /* DemoListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DemoListViewController.swift; sourceTree = "<group>"; };
		225B36341F6EB9FE0005B3D5 /* IntAxisValueFormatter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IntAxisValueFormatter.swift; sourceTree = "<group>"; };
		225B36351F6EB9FE0005B3D5 /* DayAxisValueFormatter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DayAxisValueFormatter.swift; sourceTree = "<group>"; };
		225B36361F6EB9FE0005B3D5 /* DateValueFormatter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateValueFormatter.swift; sourceTree = "<group>"; };
		225B36371F6EB9FE0005B3D5 /* LargeValueFormatter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LargeValueFormatter.swift; sourceTree = "<group>"; };
		225B363D1F6EBA180005B3D5 /* XYMarkerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XYMarkerView.swift; sourceTree = "<group>"; };
		225B363E1F6EBA180005B3D5 /* BalloonMarker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BalloonMarker.swift; sourceTree = "<group>"; };
		225B363F1F6EBA180005B3D5 /* RadarMarkerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RadarMarkerView.swift; sourceTree = "<group>"; };
		225B36441F6EBA410005B3D5 /* MultipleLinesChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultipleLinesChartViewController.swift; sourceTree = "<group>"; };
		225B36451F6EBA410005B3D5 /* MultipleBarChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultipleBarChartViewController.swift; sourceTree = "<group>"; };
		225B36461F6EBA410005B3D5 /* HalfPieChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HalfPieChartViewController.swift; sourceTree = "<group>"; };
		225B36471F6EBA410005B3D5 /* LineChartTimeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LineChartTimeViewController.swift; sourceTree = "<group>"; };
		225B36481F6EBA410005B3D5 /* ColoredLineChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColoredLineChartViewController.swift; sourceTree = "<group>"; };
		225B36491F6EBA410005B3D5 /* LineChart2ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LineChart2ViewController.swift; sourceTree = "<group>"; };
		225B364A1F6EBA410005B3D5 /* LineChart1ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LineChart1ViewController.swift; sourceTree = "<group>"; };
		225B364B1F6EBA410005B3D5 /* NegativeStackedBarChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NegativeStackedBarChartViewController.swift; sourceTree = "<group>"; };
		225B364C1F6EBA410005B3D5 /* ScatterChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScatterChartViewController.swift; sourceTree = "<group>"; };
		225B364D1F6EBA410005B3D5 /* StackedBarChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StackedBarChartViewController.swift; sourceTree = "<group>"; };
		225B364E1F6EBA410005B3D5 /* BubbleChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BubbleChartViewController.swift; sourceTree = "<group>"; };
		225B364F1F6EBA410005B3D5 /* LineChartFilledViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LineChartFilledViewController.swift; sourceTree = "<group>"; };
		225B36501F6EBA410005B3D5 /* PositiveNegativeBarChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PositiveNegativeBarChartViewController.swift; sourceTree = "<group>"; };
		225B36511F6EBA410005B3D5 /* AnotherBarChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnotherBarChartViewController.swift; sourceTree = "<group>"; };
		225B36521F6EBA410005B3D5 /* PieChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PieChartViewController.swift; sourceTree = "<group>"; };
		225B36531F6EBA410005B3D5 /* CubicLineChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CubicLineChartViewController.swift; sourceTree = "<group>"; };
		225B36541F6EBA410005B3D5 /* RadarChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RadarChartViewController.swift; sourceTree = "<group>"; };
		225B36551F6EBA410005B3D5 /* HorizontalBarChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HorizontalBarChartViewController.swift; sourceTree = "<group>"; };
		225B36561F6EBA410005B3D5 /* PiePolylineChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PiePolylineChartViewController.swift; sourceTree = "<group>"; };
		225B36571F6EBA410005B3D5 /* CombinedChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CombinedChartViewController.swift; sourceTree = "<group>"; };
		225B36581F6EBA410005B3D5 /* BarChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BarChartViewController.swift; sourceTree = "<group>"; };
		225B36591F6EBA410005B3D5 /* SinusBarChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SinusBarChartViewController.swift; sourceTree = "<group>"; };
		225B365A1F6EBA410005B3D5 /* CandleStickChartViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandleStickChartViewController.swift; sourceTree = "<group>"; };
		55E3564D1ADC638F00A57971 /* BubbleChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BubbleChartViewController.xib; sourceTree = "<group>"; };
		55E3564E1ADC638F00A57971 /* BubbleChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BubbleChartViewController.h; sourceTree = "<group>"; };
		55E3564F1ADC638F00A57971 /* BubbleChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BubbleChartViewController.m; sourceTree = "<group>"; };
		5B0CC7821ABB875400665592 /* PieChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PieChartViewController.h; sourceTree = "<group>"; };
		5B0CC7831ABB875400665592 /* PieChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PieChartViewController.m; sourceTree = "<group>"; };
		5B0CC7841ABB875400665592 /* PieChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = PieChartViewController.xib; sourceTree = "<group>"; };
		5B1B5DA91E911A15006D1375 /* RealmDemosViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; name = RealmDemosViewController.xib; path = XIBs/Demos/RealmDemosViewController.xib; sourceTree = SOURCE_ROOT; };
		5B57BBAF1A9B26AA0036A6CC /* ChartsDemo-iOS.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "ChartsDemo-iOS.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		5B57BBB31A9B26AA0036A6CC /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		5B57BBB41A9B26AA0036A6CC /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		5B57BBB61A9B26AA0036A6CC /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		5B57BBB71A9B26AA0036A6CC /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		5B57BBB91A9B26AA0036A6CC /* DemoListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DemoListViewController.h; sourceTree = "<group>"; };
		5B57BBBA1A9B26AA0036A6CC /* DemoListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DemoListViewController.m; sourceTree = "<group>"; };
		5B613DA31D526AD5000F1E98 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		5B613DB21D526DED000F1E98 /* RadarMarkerView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RadarMarkerView.swift; sourceTree = "<group>"; };
		5B613DB41D526EA8000F1E98 /* RadarMarkerView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = RadarMarkerView.xib; sourceTree = "<group>"; };
		5B613DEF1D5A50B6000F1E98 /* XYMarkerView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XYMarkerView.swift; sourceTree = "<group>"; };
		5B613DF11D5A60DF000F1E98 /* LargeValueFormatter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LargeValueFormatter.swift; sourceTree = "<group>"; };
		5B8EAF231AB3271B009697AA /* DemoListViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = DemoListViewController.xib; sourceTree = "<group>"; };
		5B8EAF251AB32CF5009697AA /* DemoBaseViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DemoBaseViewController.h; sourceTree = "<group>"; };
		5B8EAF261AB32CF5009697AA /* DemoBaseViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DemoBaseViewController.m; sourceTree = "<group>"; };
		5B8EAF2F1AB32E15009697AA /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		5B96243E1B38608C007763E2 /* NegativeStackedBarChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NegativeStackedBarChartViewController.h; sourceTree = "<group>"; };
		5B96243F1B38608C007763E2 /* NegativeStackedBarChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NegativeStackedBarChartViewController.m; sourceTree = "<group>"; };
		5B9624401B38608C007763E2 /* NegativeStackedBarChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = NegativeStackedBarChartViewController.xib; sourceTree = "<group>"; };
		5BD47E5A1ABB0263008FCEC6 /* BalloonMarker.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BalloonMarker.swift; sourceTree = "<group>"; };
		5BD47E5C1ABB0273008FCEC6 /* ChartsDemo-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ChartsDemo-Bridging-Header.h"; sourceTree = "<group>"; };
		5BD47E5D1ABB3C91008FCEC6 /* LineChart2ViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LineChart2ViewController.h; sourceTree = "<group>"; };
		5BD47E5E1ABB3C91008FCEC6 /* LineChart2ViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LineChart2ViewController.m; sourceTree = "<group>"; };
		5BD47E5F1ABB3C91008FCEC6 /* LineChart2ViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = LineChart2ViewController.xib; sourceTree = "<group>"; };
		5BD47E621ABB424E008FCEC6 /* BarChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BarChartViewController.h; sourceTree = "<group>"; };
		5BD47E631ABB424E008FCEC6 /* BarChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BarChartViewController.m; sourceTree = "<group>"; };
		5BD47E641ABB424E008FCEC6 /* BarChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BarChartViewController.xib; sourceTree = "<group>"; };
		5BD8F0711AB89CE500566E05 /* LineChart1ViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LineChart1ViewController.h; sourceTree = "<group>"; };
		5BD8F0721AB89CE500566E05 /* LineChart1ViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LineChart1ViewController.m; sourceTree = "<group>"; };
		5BD8F0731AB89CE500566E05 /* LineChart1ViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = LineChart1ViewController.xib; sourceTree = "<group>"; };
		5BDEDC3E1ABB7F73007D3A60 /* HorizontalBarChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HorizontalBarChartViewController.h; sourceTree = "<group>"; };
		5BDEDC3F1ABB7F73007D3A60 /* HorizontalBarChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HorizontalBarChartViewController.m; sourceTree = "<group>"; };
		5BDEDC401ABB7F73007D3A60 /* HorizontalBarChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = HorizontalBarChartViewController.xib; sourceTree = "<group>"; };
		5BDEDC441ABB871E007D3A60 /* CombinedChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CombinedChartViewController.h; sourceTree = "<group>"; };
		5BDEDC451ABB871E007D3A60 /* CombinedChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CombinedChartViewController.m; sourceTree = "<group>"; };
		5BDEDC461ABB871E007D3A60 /* CombinedChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = CombinedChartViewController.xib; sourceTree = "<group>"; };
		5BE377DC1D425151006EB34F /* DayAxisValueFormatter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DayAxisValueFormatter.h; sourceTree = "<group>"; };
		5BE377DD1D425151006EB34F /* DayAxisValueFormatter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DayAxisValueFormatter.m; sourceTree = "<group>"; };
		5BE377F01D47FDF1006EB34F /* IntAxisValueFormatter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IntAxisValueFormatter.h; sourceTree = "<group>"; };
		5BE377F11D47FDF1006EB34F /* IntAxisValueFormatter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IntAxisValueFormatter.m; sourceTree = "<group>"; };
		5BE6737D1D5B496500A87BA2 /* HalfPieChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HalfPieChartViewController.h; sourceTree = "<group>"; };
		5BE6737E1D5B496500A87BA2 /* HalfPieChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HalfPieChartViewController.m; sourceTree = "<group>"; };
		5BE6737F1D5B496500A87BA2 /* HalfPieChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = HalfPieChartViewController.xib; sourceTree = "<group>"; };
		5BE6738C1D5B4E0900A87BA2 /* LineChartFilledViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LineChartFilledViewController.h; sourceTree = "<group>"; };
		5BE6738D1D5B4E0900A87BA2 /* LineChartFilledViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LineChartFilledViewController.m; sourceTree = "<group>"; };
		5BE6738E1D5B4E0900A87BA2 /* LineChartFilledViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = LineChartFilledViewController.xib; sourceTree = "<group>"; };
		5BE6738F1D5B4E0900A87BA2 /* LineChartTimeViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LineChartTimeViewController.h; sourceTree = "<group>"; };
		5BE673901D5B4E0900A87BA2 /* LineChartTimeViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LineChartTimeViewController.m; sourceTree = "<group>"; };
		5BE673911D5B4E0900A87BA2 /* LineChartTimeViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = LineChartTimeViewController.xib; sourceTree = "<group>"; };
		5BE673981D5BAD7E00A87BA2 /* DateValueFormatter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DateValueFormatter.h; sourceTree = "<group>"; };
		5BE673991D5BAD7E00A87BA2 /* DateValueFormatter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DateValueFormatter.m; sourceTree = "<group>"; };
		5BE7E75F1C693098000A0377 /* PositiveNegativeBarChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PositiveNegativeBarChartViewController.h; sourceTree = "<group>"; };
		5BE7E7601C693098000A0377 /* PositiveNegativeBarChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PositiveNegativeBarChartViewController.m; sourceTree = "<group>"; };
		5BE7E7611C693098000A0377 /* PositiveNegativeBarChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = PositiveNegativeBarChartViewController.xib; sourceTree = "<group>"; };
		5BEAED0F1ABBFB2B0013F194 /* AnotherBarChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AnotherBarChartViewController.h; sourceTree = "<group>"; };
		5BEAED101ABBFB2B0013F194 /* AnotherBarChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AnotherBarChartViewController.m; sourceTree = "<group>"; };
		5BEAED111ABBFB2B0013F194 /* AnotherBarChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = AnotherBarChartViewController.xib; sourceTree = "<group>"; };
		5BEAED151ABBFB340013F194 /* ScatterChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ScatterChartViewController.h; sourceTree = "<group>"; };
		5BEAED161ABBFB340013F194 /* ScatterChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ScatterChartViewController.m; sourceTree = "<group>"; };
		5BEAED171ABBFB340013F194 /* ScatterChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ScatterChartViewController.xib; sourceTree = "<group>"; };
		5BEAED181ABBFB340013F194 /* StackedBarChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StackedBarChartViewController.h; sourceTree = "<group>"; };
		5BEAED191ABBFB340013F194 /* StackedBarChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StackedBarChartViewController.m; sourceTree = "<group>"; };
		5BEAED1A1ABBFB340013F194 /* StackedBarChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = StackedBarChartViewController.xib; sourceTree = "<group>"; };
		5BEAED1F1ABC0BE20013F194 /* MultipleBarChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MultipleBarChartViewController.h; sourceTree = "<group>"; };
		5BEAED201ABC0BE20013F194 /* MultipleBarChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MultipleBarChartViewController.m; sourceTree = "<group>"; };
		5BEAED211ABC0BE20013F194 /* MultipleBarChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = MultipleBarChartViewController.xib; sourceTree = "<group>"; };
		5BEAED221ABC0BE20013F194 /* MultipleLinesChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MultipleLinesChartViewController.h; sourceTree = "<group>"; };
		5BEAED231ABC0BE20013F194 /* MultipleLinesChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MultipleLinesChartViewController.m; sourceTree = "<group>"; };
		5BEAED241ABC0BE20013F194 /* MultipleLinesChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = MultipleLinesChartViewController.xib; sourceTree = "<group>"; };
		5BEAED291ABC160F0013F194 /* CandleStickChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CandleStickChartViewController.h; sourceTree = "<group>"; };
		5BEAED2A1ABC160F0013F194 /* CandleStickChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CandleStickChartViewController.m; sourceTree = "<group>"; };
		5BEAED2B1ABC160F0013F194 /* CandleStickChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = CandleStickChartViewController.xib; sourceTree = "<group>"; };
		5BEAED2E1ABC18F00013F194 /* CubicLineChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CubicLineChartViewController.h; sourceTree = "<group>"; };
		5BEAED2F1ABC18F00013F194 /* CubicLineChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CubicLineChartViewController.m; sourceTree = "<group>"; };
		5BEAED301ABC18F00013F194 /* CubicLineChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = CubicLineChartViewController.xib; sourceTree = "<group>"; };
		5BEAED331ABC192F0013F194 /* RadarChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RadarChartViewController.h; sourceTree = "<group>"; };
		5BEAED341ABC192F0013F194 /* RadarChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RadarChartViewController.m; sourceTree = "<group>"; };
		5BEAED351ABC192F0013F194 /* RadarChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = RadarChartViewController.xib; sourceTree = "<group>"; };
		5BEAED381ABC199F0013F194 /* ColoredLineChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ColoredLineChartViewController.h; sourceTree = "<group>"; };
		5BEAED391ABC199F0013F194 /* ColoredLineChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ColoredLineChartViewController.m; sourceTree = "<group>"; };
		5BEAED3A1ABC199F0013F194 /* ColoredLineChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ColoredLineChartViewController.xib; sourceTree = "<group>"; };
		5BEAED3D1ABC1AC60013F194 /* SinusBarChartViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SinusBarChartViewController.h; sourceTree = "<group>"; };
		5BEAED3E1ABC1AC60013F194 /* SinusBarChartViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SinusBarChartViewController.m; sourceTree = "<group>"; };
		5BEAED3F1ABC1AC60013F194 /* SinusBarChartViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = SinusBarChartViewController.xib; sourceTree = "<group>"; };
		B64D06EA1FB199130067418D /* Launch Screen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "Launch Screen.storyboard"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		225B361A1F6EB9A50005B3D5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				225B368C1F6EBBB00005B3D5 /* Charts.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5B57BBAC1A9B26AA0036A6CC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0630AE511D81271B008859B0 /* Charts.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0630AE3A1D8126C0008859B0 /* Products */ = {
			isa = PBXGroup;
			children = (
				0630AE411D8126C0008859B0 /* Charts.framework */,
				0630AE431D8126C0008859B0 /* ChartsTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		225B361E1F6EB9A50005B3D5 /* Swift */ = {
			isa = PBXGroup;
			children = (
				225B361F1F6EB9A50005B3D5 /* AppDelegate.swift */,
				225B362F1F6EB9EE0005B3D5 /* DemoBaseViewController.swift */,
				225B36301F6EB9EE0005B3D5 /* DemoListViewController.swift */,
				225B363C1F6EBA180005B3D5 /* Components */,
				225B36331F6EB9FE0005B3D5 /* Formatters */,
				225B36431F6EBA410005B3D5 /* Demos */,
			);
			path = Swift;
			sourceTree = "<group>";
		};
		225B36331F6EB9FE0005B3D5 /* Formatters */ = {
			isa = PBXGroup;
			children = (
				225B36351F6EB9FE0005B3D5 /* DayAxisValueFormatter.swift */,
				225B36361F6EB9FE0005B3D5 /* DateValueFormatter.swift */,
				225B36341F6EB9FE0005B3D5 /* IntAxisValueFormatter.swift */,
				225B36371F6EB9FE0005B3D5 /* LargeValueFormatter.swift */,
			);
			path = Formatters;
			sourceTree = "<group>";
		};
		225B363C1F6EBA180005B3D5 /* Components */ = {
			isa = PBXGroup;
			children = (
				225B363E1F6EBA180005B3D5 /* BalloonMarker.swift */,
				225B363F1F6EBA180005B3D5 /* RadarMarkerView.swift */,
				225B363D1F6EBA180005B3D5 /* XYMarkerView.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		225B36431F6EBA410005B3D5 /* Demos */ = {
			isa = PBXGroup;
			children = (
				225B36511F6EBA410005B3D5 /* AnotherBarChartViewController.swift */,
				225B36581F6EBA410005B3D5 /* BarChartViewController.swift */,
				225B364E1F6EBA410005B3D5 /* BubbleChartViewController.swift */,
				225B365A1F6EBA410005B3D5 /* CandleStickChartViewController.swift */,
				225B36481F6EBA410005B3D5 /* ColoredLineChartViewController.swift */,
				225B36571F6EBA410005B3D5 /* CombinedChartViewController.swift */,
				225B36531F6EBA410005B3D5 /* CubicLineChartViewController.swift */,
				225B36461F6EBA410005B3D5 /* HalfPieChartViewController.swift */,
				225B36551F6EBA410005B3D5 /* HorizontalBarChartViewController.swift */,
				225B364A1F6EBA410005B3D5 /* LineChart1ViewController.swift */,
				225B36491F6EBA410005B3D5 /* LineChart2ViewController.swift */,
				225B364F1F6EBA410005B3D5 /* LineChartFilledViewController.swift */,
				225B36471F6EBA410005B3D5 /* LineChartTimeViewController.swift */,
				225B36441F6EBA410005B3D5 /* MultipleLinesChartViewController.swift */,
				225B36451F6EBA410005B3D5 /* MultipleBarChartViewController.swift */,
				225B364B1F6EBA410005B3D5 /* NegativeStackedBarChartViewController.swift */,
				225B36521F6EBA410005B3D5 /* PieChartViewController.swift */,
				225B36561F6EBA410005B3D5 /* PiePolylineChartViewController.swift */,
				225B36501F6EBA410005B3D5 /* PositiveNegativeBarChartViewController.swift */,
				225B36541F6EBA410005B3D5 /* RadarChartViewController.swift */,
				225B364C1F6EBA410005B3D5 /* ScatterChartViewController.swift */,
				225B36591F6EBA410005B3D5 /* SinusBarChartViewController.swift */,
				225B364D1F6EBA410005B3D5 /* StackedBarChartViewController.swift */,
			);
			path = Demos;
			sourceTree = "<group>";
		};
		227136F41F6EB665006D2A11 /* XIBs */ = {
			isa = PBXGroup;
			children = (
				5B8EAF231AB3271B009697AA /* DemoListViewController.xib */,
				5B613DB41D526EA8000F1E98 /* RadarMarkerView.xib */,
				227136F61F6EB69D006D2A11 /* Demos */,
			);
			path = XIBs;
			sourceTree = "<group>";
		};
		227136F61F6EB69D006D2A11 /* Demos */ = {
			isa = PBXGroup;
			children = (
				5BEAED111ABBFB2B0013F194 /* AnotherBarChartViewController.xib */,
				5BD47E641ABB424E008FCEC6 /* BarChartViewController.xib */,
				55E3564D1ADC638F00A57971 /* BubbleChartViewController.xib */,
				5BEAED2B1ABC160F0013F194 /* CandleStickChartViewController.xib */,
				5BEAED3A1ABC199F0013F194 /* ColoredLineChartViewController.xib */,
				5BDEDC461ABB871E007D3A60 /* CombinedChartViewController.xib */,
				5BEAED301ABC18F00013F194 /* CubicLineChartViewController.xib */,
				5BE6737F1D5B496500A87BA2 /* HalfPieChartViewController.xib */,
				5BDEDC401ABB7F73007D3A60 /* HorizontalBarChartViewController.xib */,
				5BD8F0731AB89CE500566E05 /* LineChart1ViewController.xib */,
				5BD47E5F1ABB3C91008FCEC6 /* LineChart2ViewController.xib */,
				5BE6738E1D5B4E0900A87BA2 /* LineChartFilledViewController.xib */,
				5BE673911D5B4E0900A87BA2 /* LineChartTimeViewController.xib */,
				5BEAED211ABC0BE20013F194 /* MultipleBarChartViewController.xib */,
				5BEAED241ABC0BE20013F194 /* MultipleLinesChartViewController.xib */,
				5B9624401B38608C007763E2 /* NegativeStackedBarChartViewController.xib */,
				5B0CC7841ABB875400665592 /* PieChartViewController.xib */,
				0471CBFB1CA1090A00E52DBC /* PiePolylineChartViewController.xib */,
				5BE7E7611C693098000A0377 /* PositiveNegativeBarChartViewController.xib */,
				5BEAED351ABC192F0013F194 /* RadarChartViewController.xib */,
				5B1B5DA91E911A15006D1375 /* RealmDemosViewController.xib */,
				5BEAED171ABBFB340013F194 /* ScatterChartViewController.xib */,
				5BEAED3F1ABC1AC60013F194 /* SinusBarChartViewController.xib */,
				5BEAED1A1ABBFB340013F194 /* StackedBarChartViewController.xib */,
			);
			path = Demos;
			sourceTree = "<group>";
		};
		5B57BBA61A9B26AA0036A6CC = {
			isa = PBXGroup;
			children = (
				5B57BBB11A9B26AA0036A6CC /* Objective-C */,
				225B361E1F6EB9A50005B3D5 /* Swift */,
				227136F41F6EB665006D2A11 /* XIBs */,
				5B8EAF2E1AB32E15009697AA /* Resources */,
				5B57BBB21A9B26AA0036A6CC /* Supporting Files */,
				5B57BBB01A9B26AA0036A6CC /* Products */,
				0630AE391D8126C0008859B0 /* Charts.xcodeproj */,
			);
			sourceTree = "<group>";
		};
		5B57BBB01A9B26AA0036A6CC /* Products */ = {
			isa = PBXGroup;
			children = (
				5B57BBAF1A9B26AA0036A6CC /* ChartsDemo-iOS.app */,
				225B361D1F6EB9A50005B3D5 /* ChartsDemo-iOS-Swift.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		5B57BBB11A9B26AA0036A6CC /* Objective-C */ = {
			isa = PBXGroup;
			children = (
				5B57BBB61A9B26AA0036A6CC /* AppDelegate.h */,
				5B57BBB71A9B26AA0036A6CC /* AppDelegate.m */,
				5B57BBB91A9B26AA0036A6CC /* DemoListViewController.h */,
				5B57BBBA1A9B26AA0036A6CC /* DemoListViewController.m */,
				5B8EAF251AB32CF5009697AA /* DemoBaseViewController.h */,
				5B8EAF261AB32CF5009697AA /* DemoBaseViewController.m */,
				5BE377D41D42511A006EB34F /* Formatters */,
				5BD47E541ABB0177008FCEC6 /* Components */,
				5BD8F06F1AB89C7100566E05 /* Demos */,
			);
			path = "Objective-C";
			sourceTree = "<group>";
		};
		5B57BBB21A9B26AA0036A6CC /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				5B57BBB31A9B26AA0036A6CC /* Info.plist */,
				5B57BBB41A9B26AA0036A6CC /* main.m */,
				5BD47E5C1ABB0273008FCEC6 /* ChartsDemo-Bridging-Header.h */,
			);
			path = "Supporting Files";
			sourceTree = "<group>";
		};
		5B613DA21D526AD5000F1E98 /* markers */ = {
			isa = PBXGroup;
			children = (
				5B613DA31D526AD5000F1E98 /* <EMAIL> */,
			);
			path = markers;
			sourceTree = "<group>";
		};
		5B8EAF2E1AB32E15009697AA /* Resources */ = {
			isa = PBXGroup;
			children = (
				B64D06EA1FB199130067418D /* Launch Screen.storyboard */,
				5B8EAF2F1AB32E15009697AA /* Images.xcassets */,
				5B613DA21D526AD5000F1E98 /* markers */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		5BD47E541ABB0177008FCEC6 /* Components */ = {
			isa = PBXGroup;
			children = (
				5BD47E5A1ABB0263008FCEC6 /* BalloonMarker.swift */,
				5B613DB21D526DED000F1E98 /* RadarMarkerView.swift */,
				5B613DEF1D5A50B6000F1E98 /* XYMarkerView.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		5BD8F06F1AB89C7100566E05 /* Demos */ = {
			isa = PBXGroup;
			children = (
				5BEAED0F1ABBFB2B0013F194 /* AnotherBarChartViewController.h */,
				5BEAED101ABBFB2B0013F194 /* AnotherBarChartViewController.m */,
				5BD47E621ABB424E008FCEC6 /* BarChartViewController.h */,
				5BD47E631ABB424E008FCEC6 /* BarChartViewController.m */,
				55E3564E1ADC638F00A57971 /* BubbleChartViewController.h */,
				55E3564F1ADC638F00A57971 /* BubbleChartViewController.m */,
				5BEAED291ABC160F0013F194 /* CandleStickChartViewController.h */,
				5BEAED2A1ABC160F0013F194 /* CandleStickChartViewController.m */,
				5BEAED381ABC199F0013F194 /* ColoredLineChartViewController.h */,
				5BEAED391ABC199F0013F194 /* ColoredLineChartViewController.m */,
				5BDEDC441ABB871E007D3A60 /* CombinedChartViewController.h */,
				5BDEDC451ABB871E007D3A60 /* CombinedChartViewController.m */,
				5BEAED2E1ABC18F00013F194 /* CubicLineChartViewController.h */,
				5BEAED2F1ABC18F00013F194 /* CubicLineChartViewController.m */,
				5BE6737D1D5B496500A87BA2 /* HalfPieChartViewController.h */,
				5BE6737E1D5B496500A87BA2 /* HalfPieChartViewController.m */,
				5BDEDC3E1ABB7F73007D3A60 /* HorizontalBarChartViewController.h */,
				5BDEDC3F1ABB7F73007D3A60 /* HorizontalBarChartViewController.m */,
				5BD8F0711AB89CE500566E05 /* LineChart1ViewController.h */,
				5BD8F0721AB89CE500566E05 /* LineChart1ViewController.m */,
				5BD47E5D1ABB3C91008FCEC6 /* LineChart2ViewController.h */,
				5BD47E5E1ABB3C91008FCEC6 /* LineChart2ViewController.m */,
				5BE6738C1D5B4E0900A87BA2 /* LineChartFilledViewController.h */,
				5BE6738D1D5B4E0900A87BA2 /* LineChartFilledViewController.m */,
				5BE6738F1D5B4E0900A87BA2 /* LineChartTimeViewController.h */,
				5BE673901D5B4E0900A87BA2 /* LineChartTimeViewController.m */,
				5BEAED1F1ABC0BE20013F194 /* MultipleBarChartViewController.h */,
				5BEAED201ABC0BE20013F194 /* MultipleBarChartViewController.m */,
				5BEAED221ABC0BE20013F194 /* MultipleLinesChartViewController.h */,
				5BEAED231ABC0BE20013F194 /* MultipleLinesChartViewController.m */,
				5B96243E1B38608C007763E2 /* NegativeStackedBarChartViewController.h */,
				5B96243F1B38608C007763E2 /* NegativeStackedBarChartViewController.m */,
				5B0CC7821ABB875400665592 /* PieChartViewController.h */,
				5B0CC7831ABB875400665592 /* PieChartViewController.m */,
				0439A3511C9FF95F00496F83 /* PiePolylineChartViewController.h */,
				0439A3521C9FF95F00496F83 /* PiePolylineChartViewController.m */,
				5BE7E75F1C693098000A0377 /* PositiveNegativeBarChartViewController.h */,
				5BE7E7601C693098000A0377 /* PositiveNegativeBarChartViewController.m */,
				5BEAED331ABC192F0013F194 /* RadarChartViewController.h */,
				5BEAED341ABC192F0013F194 /* RadarChartViewController.m */,
				5BEAED151ABBFB340013F194 /* ScatterChartViewController.h */,
				5BEAED161ABBFB340013F194 /* ScatterChartViewController.m */,
				5BEAED3D1ABC1AC60013F194 /* SinusBarChartViewController.h */,
				5BEAED3E1ABC1AC60013F194 /* SinusBarChartViewController.m */,
				5BEAED181ABBFB340013F194 /* StackedBarChartViewController.h */,
				5BEAED191ABBFB340013F194 /* StackedBarChartViewController.m */,
			);
			path = Demos;
			sourceTree = "<group>";
		};
		5BE377D41D42511A006EB34F /* Formatters */ = {
			isa = PBXGroup;
			children = (
				5BE377DC1D425151006EB34F /* DayAxisValueFormatter.h */,
				5BE377DD1D425151006EB34F /* DayAxisValueFormatter.m */,
				5BE377F01D47FDF1006EB34F /* IntAxisValueFormatter.h */,
				5BE377F11D47FDF1006EB34F /* IntAxisValueFormatter.m */,
				5B613DF11D5A60DF000F1E98 /* LargeValueFormatter.swift */,
				5BE673981D5BAD7E00A87BA2 /* DateValueFormatter.h */,
				5BE673991D5BAD7E00A87BA2 /* DateValueFormatter.m */,
			);
			path = Formatters;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		225B361C1F6EB9A50005B3D5 /* ChartsDemo-iOS-Swift */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 225B362C1F6EB9A50005B3D5 /* Build configuration list for PBXNativeTarget "ChartsDemo-iOS-Swift" */;
			buildPhases = (
				225B36191F6EB9A50005B3D5 /* Sources */,
				225B361A1F6EB9A50005B3D5 /* Frameworks */,
				225B361B1F6EB9A50005B3D5 /* Resources */,
				225B36901F6EBBB10005B3D5 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				225B368F1F6EBBB00005B3D5 /* PBXTargetDependency */,
			);
			name = "ChartsDemo-iOS-Swift";
			productName = "ChartsDemo-Swift";
			productReference = 225B361D1F6EB9A50005B3D5 /* ChartsDemo-iOS-Swift.app */;
			productType = "com.apple.product-type.application";
		};
		5B57BBAE1A9B26AA0036A6CC /* ChartsDemo-iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5B57BBD21A9B26AA0036A6CC /* Build configuration list for PBXNativeTarget "ChartsDemo-iOS" */;
			buildPhases = (
				5B57BBAB1A9B26AA0036A6CC /* Sources */,
				5B57BBAC1A9B26AA0036A6CC /* Frameworks */,
				5B57BBAD1A9B26AA0036A6CC /* Resources */,
				5BB4B0751ACA710D00E2EF4D /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				0630AE541D81271B008859B0 /* PBXTargetDependency */,
			);
			name = "ChartsDemo-iOS";
			productName = chartstest;
			productReference = 5B57BBAF1A9B26AA0036A6CC /* ChartsDemo-iOS.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		5B57BBA71A9B26AA0036A6CC /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftMigration = 0700;
				LastSwiftUpdateCheck = 0700;
				LastUpgradeCheck = 1020;
				ORGANIZATIONNAME = dcg;
				TargetAttributes = {
					225B361C1F6EB9A50005B3D5 = {
						CreatedOnToolsVersion = 9.0;
						LastSwiftMigration = 1000;
					};
					5B57BBAE1A9B26AA0036A6CC = {
						CreatedOnToolsVersion = 6.1.1;
						LastSwiftMigration = 0900;
						ProvisioningStyle = Manual;
					};
				};
			};
			buildConfigurationList = 5B57BBAA1A9B26AA0036A6CC /* Build configuration list for PBXProject "ChartsDemo-iOS" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = 5B57BBA61A9B26AA0036A6CC;
			productRefGroup = 5B57BBB01A9B26AA0036A6CC /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 0630AE3A1D8126C0008859B0 /* Products */;
					ProjectRef = 0630AE391D8126C0008859B0 /* Charts.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				5B57BBAE1A9B26AA0036A6CC /* ChartsDemo-iOS */,
				225B361C1F6EB9A50005B3D5 /* ChartsDemo-iOS-Swift */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		0630AE411D8126C0008859B0 /* Charts.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = Charts.framework;
			remoteRef = 0630AE401D8126C0008859B0 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		0630AE431D8126C0008859B0 /* ChartsTests.xctest */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.cfbundle;
			path = ChartsTests.xctest;
			remoteRef = 0630AE421D8126C0008859B0 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		225B361B1F6EB9A50005B3D5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				225B367C1F6EBA640005B3D5 /* HorizontalBarChartViewController.xib in Resources */,
				225B36841F6EBA640005B3D5 /* PieChartViewController.xib in Resources */,
				225B36741F6EBA640005B3D5 /* AnotherBarChartViewController.xib in Resources */,
				225B36781F6EBA640005B3D5 /* ColoredLineChartViewController.xib in Resources */,
				225B36791F6EBA640005B3D5 /* CombinedChartViewController.xib in Resources */,
				225B367B1F6EBA640005B3D5 /* HalfPieChartViewController.xib in Resources */,
				068A9B1A1FBBF355003CF1AD /* Launch Screen.storyboard in Resources */,
				225B367A1F6EBA640005B3D5 /* CubicLineChartViewController.xib in Resources */,
				225B368B1F6EBA640005B3D5 /* StackedBarChartViewController.xib in Resources */,
				225B36821F6EBA640005B3D5 /* MultipleLinesChartViewController.xib in Resources */,
				068A9B191FBBF351003CF1AD /* Images.xcassets in Resources */,
				225B36751F6EBA640005B3D5 /* BarChartViewController.xib in Resources */,
				225B367F1F6EBA640005B3D5 /* LineChartFilledViewController.xib in Resources */,
				225B36801F6EBA640005B3D5 /* LineChartTimeViewController.xib in Resources */,
				225B36811F6EBA640005B3D5 /* MultipleBarChartViewController.xib in Resources */,
				225B367D1F6EBA640005B3D5 /* LineChart1ViewController.xib in Resources */,
				068A9B1B1FBBF366003CF1AD /* <EMAIL> in Resources */,
				225B36721F6EBA5E0005B3D5 /* DemoListViewController.xib in Resources */,
				225B36891F6EBA640005B3D5 /* ScatterChartViewController.xib in Resources */,
				225B36851F6EBA640005B3D5 /* PiePolylineChartViewController.xib in Resources */,
				225B36771F6EBA640005B3D5 /* CandleStickChartViewController.xib in Resources */,
				225B36871F6EBA640005B3D5 /* RadarChartViewController.xib in Resources */,
				225B368A1F6EBA640005B3D5 /* SinusBarChartViewController.xib in Resources */,
				225B36731F6EBA5E0005B3D5 /* RadarMarkerView.xib in Resources */,
				225B367E1F6EBA640005B3D5 /* LineChart2ViewController.xib in Resources */,
				225B36881F6EBA640005B3D5 /* RealmDemosViewController.xib in Resources */,
				225B36861F6EBA640005B3D5 /* PositiveNegativeBarChartViewController.xib in Resources */,
				225B36831F6EBA640005B3D5 /* NegativeStackedBarChartViewController.xib in Resources */,
				225B36761F6EBA640005B3D5 /* BubbleChartViewController.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5B57BBAD1A9B26AA0036A6CC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5B8EAF301AB32E15009697AA /* Images.xcassets in Resources */,
				5BE673931D5B4E0900A87BA2 /* LineChartFilledViewController.xib in Resources */,
				5BEAED2D1ABC160F0013F194 /* CandleStickChartViewController.xib in Resources */,
				5B613DB51D526EA8000F1E98 /* RadarMarkerView.xib in Resources */,
				5BD47E611ABB3C91008FCEC6 /* LineChart2ViewController.xib in Resources */,
				5BEAED131ABBFB2B0013F194 /* AnotherBarChartViewController.xib in Resources */,
				5BEAED411ABC1AC60013F194 /* SinusBarChartViewController.xib in Resources */,
				5BEAED371ABC192F0013F194 /* RadarChartViewController.xib in Resources */,
				5B8EAF241AB3271B009697AA /* DemoListViewController.xib in Resources */,
				5BEAED261ABC0BE20013F194 /* MultipleBarChartViewController.xib in Resources */,
				5BEAED3C1ABC199F0013F194 /* ColoredLineChartViewController.xib in Resources */,
				5BEAED321ABC18F00013F194 /* CubicLineChartViewController.xib in Resources */,
				5BEAED281ABC0BE20013F194 /* MultipleLinesChartViewController.xib in Resources */,
				5B9624421B38608C007763E2 /* NegativeStackedBarChartViewController.xib in Resources */,
				5B613DA41D526AD5000F1E98 /* <EMAIL> in Resources */,
				5BE7E7631C693098000A0377 /* PositiveNegativeBarChartViewController.xib in Resources */,
				5B0CC7861ABB875400665592 /* PieChartViewController.xib in Resources */,
				5BEAED1C1ABBFB340013F194 /* ScatterChartViewController.xib in Resources */,
				0471CBFC1CA1090A00E52DBC /* PiePolylineChartViewController.xib in Resources */,
				5BD8F0751AB89CE500566E05 /* LineChart1ViewController.xib in Resources */,
				5BE673811D5B496500A87BA2 /* HalfPieChartViewController.xib in Resources */,
				B64D06EB1FB199130067418D /* Launch Screen.storyboard in Resources */,
				5BD47E661ABB424E008FCEC6 /* BarChartViewController.xib in Resources */,
				5BDEDC421ABB7F73007D3A60 /* HorizontalBarChartViewController.xib in Resources */,
				5BDEDC481ABB871E007D3A60 /* CombinedChartViewController.xib in Resources */,
				5BE673951D5B4E0900A87BA2 /* LineChartTimeViewController.xib in Resources */,
				5BEAED1E1ABBFB340013F194 /* StackedBarChartViewController.xib in Resources */,
				5B1B5DAB1E911A15006D1375 /* RealmDemosViewController.xib in Resources */,
				55E356501ADC638F00A57971 /* BubbleChartViewController.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		225B36191F6EB9A50005B3D5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				225B365E1F6EBA470005B3D5 /* LineChartTimeViewController.swift in Sources */,
				225B36311F6EB9EE0005B3D5 /* DemoBaseViewController.swift in Sources */,
				225B36391F6EBA040005B3D5 /* DayAxisValueFormatter.swift in Sources */,
				225B36661F6EBA470005B3D5 /* LineChartFilledViewController.swift in Sources */,
				225B366A1F6EBA470005B3D5 /* CubicLineChartViewController.swift in Sources */,
				225B36671F6EBA470005B3D5 /* PositiveNegativeBarChartViewController.swift in Sources */,
				225B363A1F6EBA040005B3D5 /* DateValueFormatter.swift in Sources */,
				225B366D1F6EBA470005B3D5 /* PiePolylineChartViewController.swift in Sources */,
				225B366F1F6EBA470005B3D5 /* BarChartViewController.swift in Sources */,
				225B36601F6EBA470005B3D5 /* LineChart2ViewController.swift in Sources */,
				225B36411F6EBA1D0005B3D5 /* BalloonMarker.swift in Sources */,
				225B366C1F6EBA470005B3D5 /* HorizontalBarChartViewController.swift in Sources */,
				225B36401F6EBA1D0005B3D5 /* XYMarkerView.swift in Sources */,
				225B36421F6EBA1D0005B3D5 /* RadarMarkerView.swift in Sources */,
				225B366B1F6EBA470005B3D5 /* RadarChartViewController.swift in Sources */,
				225B36611F6EBA470005B3D5 /* LineChart1ViewController.swift in Sources */,
				225B36691F6EBA470005B3D5 /* PieChartViewController.swift in Sources */,
				225B365B1F6EBA470005B3D5 /* MultipleLinesChartViewController.swift in Sources */,
				225B365F1F6EBA470005B3D5 /* ColoredLineChartViewController.swift in Sources */,
				225B36201F6EB9A50005B3D5 /* AppDelegate.swift in Sources */,
				225B36321F6EB9EE0005B3D5 /* DemoListViewController.swift in Sources */,
				225B36711F6EBA470005B3D5 /* CandleStickChartViewController.swift in Sources */,
				225B36631F6EBA470005B3D5 /* ScatterChartViewController.swift in Sources */,
				225B365C1F6EBA470005B3D5 /* MultipleBarChartViewController.swift in Sources */,
				225B366E1F6EBA470005B3D5 /* CombinedChartViewController.swift in Sources */,
				225B36641F6EBA470005B3D5 /* StackedBarChartViewController.swift in Sources */,
				225B36651F6EBA470005B3D5 /* BubbleChartViewController.swift in Sources */,
				225B36381F6EBA040005B3D5 /* IntAxisValueFormatter.swift in Sources */,
				225B365D1F6EBA470005B3D5 /* HalfPieChartViewController.swift in Sources */,
				225B36701F6EBA470005B3D5 /* SinusBarChartViewController.swift in Sources */,
				225B36681F6EBA470005B3D5 /* AnotherBarChartViewController.swift in Sources */,
				225B36621F6EBA470005B3D5 /* NegativeStackedBarChartViewController.swift in Sources */,
				225B363B1F6EBA040005B3D5 /* LargeValueFormatter.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5B57BBAB1A9B26AA0036A6CC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5B613DF21D5A60DF000F1E98 /* LargeValueFormatter.swift in Sources */,
				5BEAED1B1ABBFB340013F194 /* ScatterChartViewController.m in Sources */,
				5B0CC7851ABB875400665592 /* PieChartViewController.m in Sources */,
				0439A3541C9FF95F00496F83 /* PiePolylineChartViewController.m in Sources */,
				5B57BBBB1A9B26AA0036A6CC /* DemoListViewController.m in Sources */,
				5BD47E651ABB424E008FCEC6 /* BarChartViewController.m in Sources */,
				5BDEDC471ABB871E007D3A60 /* CombinedChartViewController.m in Sources */,
				5BE377F21D47FDF1006EB34F /* IntAxisValueFormatter.m in Sources */,
				5BD8F0741AB89CE500566E05 /* LineChart1ViewController.m in Sources */,
				5BE377DE1D425151006EB34F /* DayAxisValueFormatter.m in Sources */,
				5BEAED401ABC1AC60013F194 /* SinusBarChartViewController.m in Sources */,
				5BE7E7621C693098000A0377 /* PositiveNegativeBarChartViewController.m in Sources */,
				5B613DB31D526DED000F1E98 /* RadarMarkerView.swift in Sources */,
				5BEAED251ABC0BE20013F194 /* MultipleBarChartViewController.m in Sources */,
				5B57BBB81A9B26AA0036A6CC /* AppDelegate.m in Sources */,
				55E356511ADC638F00A57971 /* BubbleChartViewController.m in Sources */,
				5BD47E5B1ABB0263008FCEC6 /* BalloonMarker.swift in Sources */,
				5BEAED2C1ABC160F0013F194 /* CandleStickChartViewController.m in Sources */,
				5BEAED271ABC0BE20013F194 /* MultipleLinesChartViewController.m in Sources */,
				5BE6739A1D5BAD7E00A87BA2 /* DateValueFormatter.m in Sources */,
				5BE673801D5B496500A87BA2 /* HalfPieChartViewController.m in Sources */,
				5BE673921D5B4E0900A87BA2 /* LineChartFilledViewController.m in Sources */,
				5B8EAF281AB32CF5009697AA /* DemoBaseViewController.m in Sources */,
				5BE673941D5B4E0900A87BA2 /* LineChartTimeViewController.m in Sources */,
				5B613DF01D5A50B6000F1E98 /* XYMarkerView.swift in Sources */,
				5BEAED3B1ABC199F0013F194 /* ColoredLineChartViewController.m in Sources */,
				5BDEDC411ABB7F73007D3A60 /* HorizontalBarChartViewController.m in Sources */,
				5BEAED121ABBFB2B0013F194 /* AnotherBarChartViewController.m in Sources */,
				5BEAED311ABC18F00013F194 /* CubicLineChartViewController.m in Sources */,
				5BEAED1D1ABBFB340013F194 /* StackedBarChartViewController.m in Sources */,
				5BD47E601ABB3C91008FCEC6 /* LineChart2ViewController.m in Sources */,
				5B57BBB51A9B26AA0036A6CC /* main.m in Sources */,
				5BEAED361ABC192F0013F194 /* RadarChartViewController.m in Sources */,
				5B9624411B38608C007763E2 /* NegativeStackedBarChartViewController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0630AE541D81271B008859B0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Charts;
			targetProxy = 0630AE531D81271B008859B0 /* PBXContainerItemProxy */;
		};
		225B368F1F6EBBB00005B3D5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Charts;
			targetProxy = 225B368E1F6EBBB00005B3D5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		225B362D1F6EB9A50005B3D5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				DEBUG_INFORMATION_FORMAT = dwarf;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = "$(SRCROOT)/Supporting Files/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.dcg.ChartsDemo-Swift";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		225B362E1F6EB9A50005B3D5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = "$(SRCROOT)/Supporting Files/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.dcg.ChartsDemo-Swift";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		5B57BBD01A9B26AA0036A6CC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		5B57BBD11A9B26AA0036A6CC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		5B57BBD31A9B26AA0036A6CC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				FRAMEWORK_SEARCH_PATHS = "$(SOURCE_ROOT)/../Carthage/Build/iOS";
				INFOPLIST_FILE = "Supporting Files/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.dcg.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "ChartsDemo-iOS";
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator appletvos";
				SWIFT_OBJC_BRIDGING_HEADER = "Supporting Files/ChartsDemo-Bridging-Header.h";
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(SWIFT_MODULE_NAME)-Swift.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		5B57BBD41A9B26AA0036A6CC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				FRAMEWORK_SEARCH_PATHS = "$(SOURCE_ROOT)/../Carthage/Build/iOS";
				INFOPLIST_FILE = "Supporting Files/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.dcg.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "ChartsDemo-iOS";
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos appletvsimulator appletvos";
				SWIFT_OBJC_BRIDGING_HEADER = "Supporting Files/ChartsDemo-Bridging-Header.h";
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(SWIFT_MODULE_NAME)-Swift.h";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		225B362C1F6EB9A50005B3D5 /* Build configuration list for PBXNativeTarget "ChartsDemo-iOS-Swift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				225B362D1F6EB9A50005B3D5 /* Debug */,
				225B362E1F6EB9A50005B3D5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5B57BBAA1A9B26AA0036A6CC /* Build configuration list for PBXProject "ChartsDemo-iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5B57BBD01A9B26AA0036A6CC /* Debug */,
				5B57BBD11A9B26AA0036A6CC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5B57BBD21A9B26AA0036A6CC /* Build configuration list for PBXNativeTarget "ChartsDemo-iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5B57BBD31A9B26AA0036A6CC /* Debug */,
				5B57BBD41A9B26AA0036A6CC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 5B57BBA71A9B26AA0036A6CC /* Project object */;
}
