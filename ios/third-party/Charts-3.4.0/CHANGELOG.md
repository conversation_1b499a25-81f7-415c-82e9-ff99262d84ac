# Change Log

## [v3.4.0](https://github.com/danielgindi/Charts/tree/v3.4.0) (2019-10-09)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.3.0...v3.4.0)

**Fixed bugs:**

- Line charts: Line sections disappear when zoomed in \(has PR\). [\#4099](https://github.com/danielgindi/Charts/issues/4099)
- Line chart x axis animation is broken [\#4093](https://github.com/danielgindi/Charts/issues/4093)
- LineChartView.highlightValue causes CoreGraphics API errors [\#4043](https://github.com/danielgindi/Charts/issues/4043)
- Horizontal Bezier Line Graph Not Drawing Through All Points [\#3960](https://github.com/danielgindi/Charts/issues/3960)
- Index out of bounds exception in v3.2 in createAccessibleElement [\#3659](https://github.com/danielgindi/Charts/issues/3659)

**Closed issues:**

- Xcode asking for constants instead of variables in Charts code [\#4161](https://github.com/danielgindi/Charts/issues/4161)
- Blank space should not exist in BarChartView [\#4157](https://github.com/danielgindi/Charts/issues/4157)
- Could not find module 'Charts' for target 'arm64-apple-ios'; found: x86\_64-apple-ios-simulator, x86\_64 [\#4154](https://github.com/danielgindi/Charts/issues/4154)
- 指定哪个swift版本都报语法错误 [\#4145](https://github.com/danielgindi/Charts/issues/4145)
- lineChart  customize the color or style of the selected point [\#4144](https://github.com/danielgindi/Charts/issues/4144)
-   How do I customize the color or style of the selected point in a polygraph [\#4143](https://github.com/danielgindi/Charts/issues/4143)
- Help, How to achieve this style? [\#4138](https://github.com/danielgindi/Charts/issues/4138)
- AutoScaleMinMax doesn't work in candlestick iOS [\#4135](https://github.com/danielgindi/Charts/issues/4135)
- 怎么手动设定滚动到指定x坐标 [\#4130](https://github.com/danielgindi/Charts/issues/4130)
- The reuse of charts on tableviewcell [\#4127](https://github.com/danielgindi/Charts/issues/4127)
- LineChartView decrease height if add many LineChartDataSets [\#4122](https://github.com/danielgindi/Charts/issues/4122)
- 堆叠柱状图不同数据之间的间距 [\#4120](https://github.com/danielgindi/Charts/issues/4120)
- Removing LineChartView to save memory? [\#4114](https://github.com/danielgindi/Charts/issues/4114)
- h [\#4110](https://github.com/danielgindi/Charts/issues/4110)
- swift 5 [\#4107](https://github.com/danielgindi/Charts/issues/4107)
- Scatter charts dots needs to be shown even it is less than half [\#4096](https://github.com/danielgindi/Charts/issues/4096)
- barchart 1st bar offset [\#4092](https://github.com/danielgindi/Charts/issues/4092)
- Compiling Chart with Swift 5.1 raises compilation error [\#4090](https://github.com/danielgindi/Charts/issues/4090)
- How to change color for Key's Label ? [\#4088](https://github.com/danielgindi/Charts/issues/4088)
- y axis  [\#4087](https://github.com/danielgindi/Charts/issues/4087)
- No chart data available \(displaying large data sets\) [\#4075](https://github.com/danielgindi/Charts/issues/4075)
- 3.2.2 xBounds starts from 1 [\#4073](https://github.com/danielgindi/Charts/issues/4073)
- Build error on Xcode 11 beta 3 - macOS app [\#4071](https://github.com/danielgindi/Charts/issues/4071)
- Why not fix \#3865 in swift4.2 as well? [\#4067](https://github.com/danielgindi/Charts/issues/4067)
- Pod install failed - Could not found branch v3.3 issue [\#4063](https://github.com/danielgindi/Charts/issues/4063)
- Potential bug highlighting Bars in Bar chart  [\#4062](https://github.com/danielgindi/Charts/issues/4062)
- PieChart slices with different height/thick [\#4061](https://github.com/danielgindi/Charts/issues/4061)
- Line Chart crashes when upgrading from 3.2.1 to 3.2.2 or greater [\#4060](https://github.com/danielgindi/Charts/issues/4060)
- How to set the shadow width? [\#4058](https://github.com/danielgindi/Charts/issues/4058)
- iOS13 port [\#4056](https://github.com/danielgindi/Charts/issues/4056)
- Embedding in Objective C++ project [\#4054](https://github.com/danielgindi/Charts/issues/4054)
- Example project does not work for latest swift  [\#4053](https://github.com/danielgindi/Charts/issues/4053)
- Line chart line color array issue [\#4052](https://github.com/danielgindi/Charts/issues/4052)
- I set the display is double but turned into an integer [\#4051](https://github.com/danielgindi/Charts/issues/4051)
- xAxis Renderer label [\#4039](https://github.com/danielgindi/Charts/issues/4039)
- Highlight midPoint of visible area [\#4032](https://github.com/danielgindi/Charts/issues/4032)
- Snap to position for x values \(paging\) [\#4030](https://github.com/danielgindi/Charts/issues/4030)
- how can I use both chart in one view like video? [\#4026](https://github.com/danielgindi/Charts/issues/4026)
- How to Parse xValue and yValue to LineChartData ? [\#4025](https://github.com/danielgindi/Charts/issues/4025)
- Fatal error: Index out of range from subscript\(position: Index\) -\> Element [\#4024](https://github.com/danielgindi/Charts/issues/4024)
- Pie chart label  [\#4022](https://github.com/danielgindi/Charts/issues/4022)
- Cannot find "@import Charts;" in -Swift.h, I can only @import Charts in OC files, anything I missed? [\#4021](https://github.com/danielgindi/Charts/issues/4021)
- Cannot subclass LineChartRenderer with Clang 11 [\#4018](https://github.com/danielgindi/Charts/issues/4018)
- Unable to compile with Swift Package Manager due to missing dependencies [\#4016](https://github.com/danielgindi/Charts/issues/4016)
- Show values in marker when you click a circle in LineChart [\#4015](https://github.com/danielgindi/Charts/issues/4015)
- Is it possible to show only part of series on full XAxis labels? [\#4014](https://github.com/danielgindi/Charts/issues/4014)
- BarChartRenderer FatalError: Index out of range [\#4013](https://github.com/danielgindi/Charts/issues/4013)
- how to set max zoom scale for y-axis [\#4011](https://github.com/danielgindi/Charts/issues/4011)
- ScatterChartDataSet basic initializer causes crashes [\#4010](https://github.com/danielgindi/Charts/issues/4010)
- oc project could't use [\#4008](https://github.com/danielgindi/Charts/issues/4008)
- Line chart issue [\#4005](https://github.com/danielgindi/Charts/issues/4005)
- Crash on PieChartRenderer line 833 [\#4001](https://github.com/danielgindi/Charts/issues/4001)
- BarChartView shows on UI when y value is zero [\#4000](https://github.com/danielgindi/Charts/issues/4000)
- can make excel [\#3999](https://github.com/danielgindi/Charts/issues/3999)
- Cannot remove an observer for the key path "bounds" because it is not registered as an observer [\#3995](https://github.com/danielgindi/Charts/issues/3995)
- How to set y axis value with larger difference because my candles are showing very small in size please provide any way  [\#3993](https://github.com/danielgindi/Charts/issues/3993)
- compiling for iOS 8.0, but module 'Charts' has a minimum deployment target of iOS 8.4: [\#3992](https://github.com/danielgindi/Charts/issues/3992)
- SIGABRT [\#3986](https://github.com/danielgindi/Charts/issues/3986)
- Getting The below error on Xcode update: Error Group :-1: Undefined symbol: \_OBJC\_CLASS\_$\_XBarChart :-1: Undefined symbol: \_OBJC\_CLASS\_$\_XBarChartConfiguration :-1: Undefined symbol: \_OBJC\_CLASS\_$\_XBarItem, When I run on real device. [\#3985](https://github.com/danielgindi/Charts/issues/3985)
- Second to last value in stacked BarChart not drawn when the last value is zero [\#3984](https://github.com/danielgindi/Charts/issues/3984)
- Value labels are not always showing in horizontal bar chart [\#3981](https://github.com/danielgindi/Charts/issues/3981)
- When I manually integrated "charts", running the iOS9.0 emulator crashed. [\#3980](https://github.com/danielgindi/Charts/issues/3980)
- LineChartDataSet function setColors is unavailable? [\#3979](https://github.com/danielgindi/Charts/issues/3979)
- notifyDataSetChanged\(\) crashes with CGAffineTransformInvert: singular matrix. [\#3978](https://github.com/danielgindi/Charts/issues/3978)
- this version is 3.2.2 in pods, this version has problem [\#3977](https://github.com/danielgindi/Charts/issues/3977)
- Undefined symbol: method descriptor for Charts.ChartViewBase.initialize\(\) -\> \(\) [\#3976](https://github.com/danielgindi/Charts/issues/3976)
- PieChart highlightEnabled flag operation is reverse of setting [\#3975](https://github.com/danielgindi/Charts/issues/3975)
- I am not getting the last value  of x axis in the group chart [\#3974](https://github.com/danielgindi/Charts/issues/3974)
- Charts 3.3.0 not running on Xcode 10.2 [\#3972](https://github.com/danielgindi/Charts/issues/3972)
- change line chart legend text color [\#3971](https://github.com/danielgindi/Charts/issues/3971)
- drawCenterTextEnable [\#3969](https://github.com/danielgindi/Charts/issues/3969)
- Installing version 3.3  from Pods not found [\#3968](https://github.com/danielgindi/Charts/issues/3968)
- zero value in bar chart [\#3967](https://github.com/danielgindi/Charts/issues/3967)
- \[Question\] Line Chart Filled Circle Hole when highlighted [\#3966](https://github.com/danielgindi/Charts/issues/3966)
- How to change pie chart boarder color and width size ? and separator line width size and curve? in swift 4 [\#3959](https://github.com/danielgindi/Charts/issues/3959)
- Charts Pagination [\#3957](https://github.com/danielgindi/Charts/issues/3957)
- button press [\#3956](https://github.com/danielgindi/Charts/issues/3956)
- Edit leading Trailing and width of bar [\#3954](https://github.com/danielgindi/Charts/issues/3954)
- Can't suspend the ongoing drag  [\#3953](https://github.com/danielgindi/Charts/issues/3953)
- incorrect display in LineChartView When all yAxis data being 0 [\#3950](https://github.com/danielgindi/Charts/issues/3950)
- Balloon Marker Swift errors [\#3947](https://github.com/danielgindi/Charts/issues/3947)
- Linhas de sobreposição PieChart  [\#3942](https://github.com/danielgindi/Charts/issues/3942)
- Display Attributed String on X-Axis [\#3941](https://github.com/danielgindi/Charts/issues/3941)
- getHighlightByTouchPoint will get nil [\#3940](https://github.com/danielgindi/Charts/issues/3940)
- Fatal error: removeEntry is not implemented in ChartBaseDataSet: [\#3937](https://github.com/danielgindi/Charts/issues/3937)
- Line graph points create lines to other points [\#3936](https://github.com/danielgindi/Charts/issues/3936)
- CombinedChart and data [\#3930](https://github.com/danielgindi/Charts/issues/3930)
- Gradient and round corner in bar chart [\#3928](https://github.com/danielgindi/Charts/issues/3928)
- Slide space color pie-chart  [\#3926](https://github.com/danielgindi/Charts/issues/3926)
- Cannot build enterprise with Charts [\#3924](https://github.com/danielgindi/Charts/issues/3924)
- ChartUtils.swift line 225 [\#3922](https://github.com/danielgindi/Charts/issues/3922)
- Not Drawing Chart With Exact Axis [\#3921](https://github.com/danielgindi/Charts/issues/3921)
- LineChartView with a line that goes backwards [\#3919](https://github.com/danielgindi/Charts/issues/3919)
- Remove slices text in pieChart [\#3918](https://github.com/danielgindi/Charts/issues/3918)
- Crash at BarChartDataSet as! IBarChartDataSet [\#3917](https://github.com/danielgindi/Charts/issues/3917)
- carthage support lagging - needs updating for Xcode 10.2 & Swift5 [\#3914](https://github.com/danielgindi/Charts/issues/3914)
- The first dot doesn't show up when I use lineChart [\#3912](https://github.com/danielgindi/Charts/issues/3912)
- How to listen to the end of a chart swipe to call a method [\#3911](https://github.com/danielgindi/Charts/issues/3911)
- How to display single data when using LineChartView [\#3910](https://github.com/danielgindi/Charts/issues/3910)
- Straight Line in third quadrant does't show up [\#3898](https://github.com/danielgindi/Charts/issues/3898)
- Horizontal scrolling in long vertical tableView causes the outer table to jump [\#3866](https://github.com/danielgindi/Charts/issues/3866)
- PieChart with value lines with very small values render values on top of each other [\#3613](https://github.com/danielgindi/Charts/issues/3613)
- Line Chart do not draw line for Data Set [\#2567](https://github.com/danielgindi/Charts/issues/2567)
- Stacked bar marker returns entry.y of entire stack [\#2173](https://github.com/danielgindi/Charts/issues/2173)
- Repeating Xaxis values [\#2143](https://github.com/danielgindi/Charts/issues/2143)

**Merged pull requests:**

- Apply Xcode11 changes [\#4153](https://github.com/danielgindi/Charts/pull/4153) ([liuxuan30](https://github.com/liuxuan30))
- Fixes \#4099: Line renderer did not render lines if their coordinates fell outside of the viewport. [\#4100](https://github.com/danielgindi/Charts/pull/4100) ([4np](https://github.com/4np))
- Fix line chart x axis animation \#4093, also close \#3960 [\#4094](https://github.com/danielgindi/Charts/pull/4094) ([liuxuan30](https://github.com/liuxuan30))
- Update License [\#4055](https://github.com/danielgindi/Charts/pull/4055) ([jobinsjohn](https://github.com/jobinsjohn))
- fixed stacked chart bug when there are different stacks on columns. [\#4029](https://github.com/danielgindi/Charts/pull/4029) ([Scalman](https://github.com/Scalman))
- Fix Swift Package Manager compile issue [\#4017](https://github.com/danielgindi/Charts/pull/4017) ([rynecheow](https://github.com/rynecheow))
- Added a safety check before an unsafe array operation [\#4006](https://github.com/danielgindi/Charts/pull/4006) ([UberNick](https://github.com/UberNick))
- fix \#3975 \(pie chart highlight disabled will lead to empty slice\) [\#3996](https://github.com/danielgindi/Charts/pull/3996) ([liuxuan30](https://github.com/liuxuan30))
- For \#3917. make init\(label: String?\) convenient initializer [\#3973](https://github.com/danielgindi/Charts/pull/3973) ([liuxuan30](https://github.com/liuxuan30))
- Avoid passing NaN to CoreGraphics API \(Fixes \#1626\) [\#2568](https://github.com/danielgindi/Charts/pull/2568) ([chiahan1123](https://github.com/chiahan1123))

## [v3.3.0](https://github.com/danielgindi/Charts/tree/v3.3.0) (2019-04-24)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.2.2...v3.3.0)

**Implemented enhancements:**

- Provide pre-compiled universal binary [\#3867](https://github.com/danielgindi/Charts/issues/3867)
- Renamed `values` to `entries` to reflect the property's type [\#3847](https://github.com/danielgindi/Charts/pull/3847) ([jjatie](https://github.com/jjatie))

**Fixed bugs:**

- Pie chart with vertical orientation clips last legend entry [\#3860](https://github.com/danielgindi/Charts/issues/3860)

**Closed issues:**

- 雷达图的拐点是怎么设置的 [\#3964](https://github.com/danielgindi/Charts/issues/3964)
- BarChartView没有从0开始 [\#3963](https://github.com/danielgindi/Charts/issues/3963)
- version 3.3 using spm results invalid string error [\#3962](https://github.com/danielgindi/Charts/issues/3962)
- 折线图能分页吗 就是滑动到最左边 网络请求再加载数据 [\#3961](https://github.com/danielgindi/Charts/issues/3961)
- BarChartView problem on dataset count changed [\#3958](https://github.com/danielgindi/Charts/issues/3958)
- No Such Module 'Charts' [\#3955](https://github.com/danielgindi/Charts/issues/3955)
- drawing asynchronous [\#3952](https://github.com/danielgindi/Charts/issues/3952)
- demo can not run [\#3951](https://github.com/danielgindi/Charts/issues/3951)
- BarChartView extends all bars to bottom when all values are negative [\#3949](https://github.com/danielgindi/Charts/issues/3949)
- xcode10.2,swift5,chart version 3.3.0 [\#3948](https://github.com/danielgindi/Charts/issues/3948)
- Compiling for iOS 8.1, but module 'Charts' has a minimum deployment target of iOS 8.4 [\#3946](https://github.com/danielgindi/Charts/issues/3946)
- X轴的Label显示间隔会随着值的增加而变大, 能控制显示间隔吗? [\#3943](https://github.com/danielgindi/Charts/issues/3943)
-  the first point of LineChartView is not displayed, and so is demo [\#3938](https://github.com/danielgindi/Charts/issues/3938)
- why？？？？？ [\#3934](https://github.com/danielgindi/Charts/issues/3934)
- The “Swift Language Version” \(SWIFT\_VERSION\) build setting must be set to a supported value for targets which use Swift. Supported values are: 4.0, 4.2, 5.0. This setting can be set in the build settings editor. [\#3932](https://github.com/danielgindi/Charts/issues/3932)
- error: SWIFT\_VERSION '3.0' is unsupported, supported versions are: 4.0, 4.2, 5.0. \(in target 'Charts'\) [\#3929](https://github.com/danielgindi/Charts/issues/3929)
- 最新版更新后的问题 [\#3927](https://github.com/danielgindi/Charts/issues/3927)
- How can i get CenterEntry in the View when i setted the MaxVisibleRange? [\#3925](https://github.com/danielgindi/Charts/issues/3925)
- EXC\_BAD\_ACCESS error on PieChart [\#3923](https://github.com/danielgindi/Charts/issues/3923)
- The first dot doesn't show up when I use lineChart [\#3915](https://github.com/danielgindi/Charts/issues/3915)
- Swift 5 Support w/ Cocoapods [\#3913](https://github.com/danielgindi/Charts/issues/3913)
- HOW to create pie chart like below image . https://user-images.githubusercontent.com/20434573/54804603-1d31fa80-4c9a-11e9-9960-00cf7c7d117d.png [\#3908](https://github.com/danielgindi/Charts/issues/3908)
- x Axis grid line origin is wrong !  maybe  a bug,  anxious!!! [\#3904](https://github.com/danielgindi/Charts/issues/3904)
- why not use CAShapeLayer? [\#3903](https://github.com/danielgindi/Charts/issues/3903)
- Chart legend  [\#3896](https://github.com/danielgindi/Charts/issues/3896)
- Wrong values from pixelForValues [\#3895](https://github.com/danielgindi/Charts/issues/3895)
- Cannot set labels for RadarChart [\#3893](https://github.com/danielgindi/Charts/issues/3893)
- I can't migrate the swift files\(combined chart\) in objective c project. Could please share the objective c code fully. Thanks in advance. [\#3890](https://github.com/danielgindi/Charts/issues/3890)
- Draw circle only on last value [\#3887](https://github.com/danielgindi/Charts/issues/3887)
- Mismatch left axis maximum and minimum values  [\#3886](https://github.com/danielgindi/Charts/issues/3886)
- How to animate? start poisiton is not from zero. [\#3885](https://github.com/danielgindi/Charts/issues/3885)
- I want to change line chart fill colours  [\#3882](https://github.com/danielgindi/Charts/issues/3882)
- Circle view is not showing for first point of line chart [\#3881](https://github.com/danielgindi/Charts/issues/3881)
- support ECG demo? [\#3880](https://github.com/danielgindi/Charts/issues/3880)
- How to remove the values given outside the circle in piechart [\#3878](https://github.com/danielgindi/Charts/issues/3878)
-  file was built for x86\_64 which is not the architecture being linked \(arm64\):error: linker command failed with exit code 1 \(use -v to see invocation\) [\#3877](https://github.com/danielgindi/Charts/issues/3877)
- Clipping first and last circle from line chart IOS. [\#3873](https://github.com/danielgindi/Charts/issues/3873)
- Smoothly moving a slowly changing linechart [\#3859](https://github.com/danielgindi/Charts/issues/3859)
- Monthly X-Axis labels \(corresponding values\) in my LineChart are not at the right position [\#3858](https://github.com/danielgindi/Charts/issues/3858)
- CandleChart do not render the first candle in CombineChartView [\#3857](https://github.com/danielgindi/Charts/issues/3857)
- Have 2 or more tresholds for one graph [\#3856](https://github.com/danielgindi/Charts/issues/3856)
- Unexpected crash on 3.2.2 [\#3855](https://github.com/danielgindi/Charts/issues/3855)
- How to change the display of legends? [\#3853](https://github.com/danielgindi/Charts/issues/3853)
- I want to call a method after the chart has been dragged and stopped. How can I detect it and the chart has stopped? [\#3851](https://github.com/danielgindi/Charts/issues/3851)
- HorizontalBarChartView bar negative values position  [\#3850](https://github.com/danielgindi/Charts/issues/3850)
- charts 有数据创建图表后，点击按钮将数据置为空则崩溃 [\#3849](https://github.com/danielgindi/Charts/issues/3849)
- No circle is drawn on a line chart when there is only one data. \(version 3.2.2 only\) [\#3848](https://github.com/danielgindi/Charts/issues/3848)
- slice color [\#3844](https://github.com/danielgindi/Charts/issues/3844)
- BarChartView showing consecutive values in xAxis [\#3843](https://github.com/danielgindi/Charts/issues/3843)
- How to hide those icons?  [\#3842](https://github.com/danielgindi/Charts/issues/3842)
- Swift 4.2: 'values' is deprecated Warnings [\#3840](https://github.com/danielgindi/Charts/issues/3840)
- Y-axis values draw error [\#3837](https://github.com/danielgindi/Charts/issues/3837)
- Horizontal Bar Chart Value Labels Cut Off [\#3836](https://github.com/danielgindi/Charts/issues/3836)
- The right Y axis displays incorrect values that does not fit the chart [\#3835](https://github.com/danielgindi/Charts/issues/3835)
-  How to align labels on left side and put values inside bars on HorizontalBarChartView? \[HELP WANTED\] [\#3833](https://github.com/danielgindi/Charts/issues/3833)
- How to show the tooltip like value when clicked on candle of candleStick chart [\#3832](https://github.com/danielgindi/Charts/issues/3832)
- In line chart, can I make all labels in Chart area even with changing yOffset? [\#3829](https://github.com/danielgindi/Charts/issues/3829)
- Multiline value labels on BarChart. [\#3826](https://github.com/danielgindi/Charts/issues/3826)
- how can I spin the selected slice of piechart to top. [\#3825](https://github.com/danielgindi/Charts/issues/3825)
- Update Cocoapods [\#3773](https://github.com/danielgindi/Charts/issues/3773)
- Select circles on line chart [\#3307](https://github.com/danielgindi/Charts/issues/3307)

**Merged pull requests:**

- Fix horizontal bar chart not drawing values and add unit tests [\#3906](https://github.com/danielgindi/Charts/pull/3906) ([liuxuan30](https://github.com/liuxuan30))
- fix \#3860. maxHeight didn't count the last label [\#3900](https://github.com/danielgindi/Charts/pull/3900) ([liuxuan30](https://github.com/liuxuan30))
- Migrating to built-in algorithms [\#3892](https://github.com/danielgindi/Charts/pull/3892) ([jjatie](https://github.com/jjatie))
- Use a stock iterator instead of a custom one. [\#3891](https://github.com/danielgindi/Charts/pull/3891) ([phughes](https://github.com/phughes))
- Removed unnecessary \#if statements and unified style to align with Xc… [\#3884](https://github.com/danielgindi/Charts/pull/3884) ([jjatie](https://github.com/jjatie))
- Velocity samples calculation [\#3883](https://github.com/danielgindi/Charts/pull/3883) ([jjatie](https://github.com/jjatie))
- Minor updates for Swift 5 [\#3874](https://github.com/danielgindi/Charts/pull/3874) ([jjatie](https://github.com/jjatie))
- Replace AnyObject with Any [\#3864](https://github.com/danielgindi/Charts/pull/3864) ([jjatie](https://github.com/jjatie))
- Data as any [\#3863](https://github.com/danielgindi/Charts/pull/3863) ([jjatie](https://github.com/jjatie))
- Reassess convenience initializers [\#3862](https://github.com/danielgindi/Charts/pull/3862) ([jjatie](https://github.com/jjatie))
- HorizontalBarChar value label offset calculation  [\#3854](https://github.com/danielgindi/Charts/pull/3854) ([chaaarly](https://github.com/chaaarly))
- Create `chartViewDidEndAnimate` in ChartViewDelegate [\#3852](https://github.com/danielgindi/Charts/pull/3852) ([Lcsmarcal](https://github.com/Lcsmarcal))
- Align `ChartLimit.LabelPosition` naming with `UIRectCorner` [\#3846](https://github.com/danielgindi/Charts/pull/3846) ([jjatie](https://github.com/jjatie))

## [v3.2.2](https://github.com/danielgindi/Charts/tree/v3.2.2) (2019-02-13)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.2.1...v3.2.2)

**Implemented enhancements:**

- 【PieChart】Please support multiple colors for valueline. [\#3480](https://github.com/danielgindi/Charts/issues/3480)

**Fixed bugs:**

- CombinedChartView's  candleData don't display in v3.2.0 [\#3662](https://github.com/danielgindi/Charts/issues/3662)
- Incorrect behavior for AxisBase.axisMaxLabels [\#3656](https://github.com/danielgindi/Charts/issues/3656)
- Horizontal Bar Chart legend issue [\#3301](https://github.com/danielgindi/Charts/issues/3301)

**Closed issues:**

- barchartview draw value in bottom of chart [\#3827](https://github.com/danielgindi/Charts/issues/3827)
- Regarding to label of axis, I think it displays .03 instead of 0.03 [\#3824](https://github.com/danielgindi/Charts/issues/3824)
- Bar Chart Not Showing Colours Correctly [\#3823](https://github.com/danielgindi/Charts/issues/3823)
- Pie Chart on click show value on label. Does anyone know how to do it? [\#3822](https://github.com/danielgindi/Charts/issues/3822)
- chartTranslated in a Pie Chart is not called [\#3821](https://github.com/danielgindi/Charts/issues/3821)
- Issue creating a simple line chart [\#3820](https://github.com/danielgindi/Charts/issues/3820)
- How to save image of scrollable graph [\#3819](https://github.com/danielgindi/Charts/issues/3819)
- Candle charts not showing candles on swift 4.2 [\#3818](https://github.com/danielgindi/Charts/issues/3818)
- How to get a value from line graph if there is more than one lines [\#3817](https://github.com/danielgindi/Charts/issues/3817)
- Multiple colors for the same line [\#3816](https://github.com/danielgindi/Charts/issues/3816)
- Memory Leaks in subclass of LineChartView [\#3813](https://github.com/danielgindi/Charts/issues/3813)
- Distortion When Animating Height of Auto Layout `LineChartView` [\#3811](https://github.com/danielgindi/Charts/issues/3811)
- How to show break in line chart for more than one graph? [\#3810](https://github.com/danielgindi/Charts/issues/3810)
- PIE Chart Colors not properly set [\#3809](https://github.com/danielgindi/Charts/issues/3809)
- A Question to contribution [\#3808](https://github.com/danielgindi/Charts/issues/3808)
- When y value is equal ，HorizontalBarChartView only show integer [\#3806](https://github.com/danielgindi/Charts/issues/3806)
- Highlight per drag not working! [\#3805](https://github.com/danielgindi/Charts/issues/3805)
- chartTranslated is called when no translation happened [\#3803](https://github.com/danielgindi/Charts/issues/3803)
- Xcode10 real machine operation error！ [\#3802](https://github.com/danielgindi/Charts/issues/3802)
- Bar Width issue after reset the data [\#3801](https://github.com/danielgindi/Charts/issues/3801)
- Performance issue:  drawCircles\(context: CGContext\) in LineChart [\#3798](https://github.com/danielgindi/Charts/issues/3798)
- LineChartView Y-Aixs label'number inaccuracy [\#3794](https://github.com/danielgindi/Charts/issues/3794)
- charts在特定范围内的参考坐标精度问题 [\#3793](https://github.com/danielgindi/Charts/issues/3793)
- Change chart legend show order [\#3791](https://github.com/danielgindi/Charts/issues/3791)
- hello,I don't want to display the pie chart by zero percent. How can I do this [\#3790](https://github.com/danielgindi/Charts/issues/3790)
- how to center legend text of LineChartView? [\#3789](https://github.com/danielgindi/Charts/issues/3789)
- How to Plot  only for the available x axis and leave the rest of the x axis blank? [\#3788](https://github.com/danielgindi/Charts/issues/3788)
- Custom chart marker [\#3787](https://github.com/danielgindi/Charts/issues/3787)
- How to shift  xAxis values to right?  [\#3785](https://github.com/danielgindi/Charts/issues/3785)
- How to disable Accessibility support? [\#3782](https://github.com/danielgindi/Charts/issues/3782)
- Feature: Add option to draw limit lines on top of data [\#3781](https://github.com/danielgindi/Charts/issues/3781)
- Legend spacing issue with LineChart and colors\[\] [\#3780](https://github.com/danielgindi/Charts/issues/3780)
- How to change value line length for each value in pieChartView [\#3776](https://github.com/danielgindi/Charts/issues/3776)
- Crash on the function isDrawingValuesAllowed [\#3772](https://github.com/danielgindi/Charts/issues/3772)
- How to let y axis line show same space when axisMinimum = 0? [\#3771](https://github.com/danielgindi/Charts/issues/3771)
- Xcode 10.1  pod install Charts  open project error!!! [\#3769](https://github.com/danielgindi/Charts/issues/3769)
- Is it possible to make some gaps on a same line plot? [\#3765](https://github.com/danielgindi/Charts/issues/3765)
- Is it possible to draw custom shape on chart and handle touch on it [\#3763](https://github.com/danielgindi/Charts/issues/3763)
- Start slice automatically seleted in pie chart [\#3762](https://github.com/danielgindi/Charts/issues/3762)
- How to change color when press down on pie chart? [\#3761](https://github.com/danielgindi/Charts/issues/3761)
- notifyDataSetChanged\(\) crash when datapoint is highlighted in LineChartView [\#3759](https://github.com/danielgindi/Charts/issues/3759)
- BarChartView display half in first bar when change from group bar to bar only [\#3757](https://github.com/danielgindi/Charts/issues/3757)
- Description of Bar doesn't appear in Horizontal BarChartView [\#3756](https://github.com/danielgindi/Charts/issues/3756)
- Chart spacing between xAxis Labels [\#3755](https://github.com/danielgindi/Charts/issues/3755)
- Granularity makes my app crash because require index from zero [\#3753](https://github.com/danielgindi/Charts/issues/3753)
- this counter will make your app being crash in particular case [\#3752](https://github.com/danielgindi/Charts/issues/3752)
- Bar Chart Drawing is wrong without setting axisminimum [\#3751](https://github.com/danielgindi/Charts/issues/3751)
- Help needed - in changing the position  for Piechart in SWIFT [\#3749](https://github.com/danielgindi/Charts/issues/3749)
- When using CombinedChartView, CandleChartData cannot be displayed [\#3748](https://github.com/danielgindi/Charts/issues/3748)
- Linear chart line with different thickness of direction [\#3746](https://github.com/danielgindi/Charts/issues/3746)
- I want to try a lot of line charts corresponding to the X axis is different, how can I achieve it. [\#3745](https://github.com/danielgindi/Charts/issues/3745)
- 折线图-具体时间的数据绘制进图表问题 [\#3743](https://github.com/danielgindi/Charts/issues/3743)
- Changing lineCap doesn't work when mode is not linear/stepped [\#3739](https://github.com/danielgindi/Charts/issues/3739)
- Reverse Pie Chart animation [\#3738](https://github.com/danielgindi/Charts/issues/3738)
- How to create y-axis for empty label ? [\#3735](https://github.com/danielgindi/Charts/issues/3735)
- Different colors for above and below 0 when filling a Line Chart [\#3733](https://github.com/danielgindi/Charts/issues/3733)
- How to adding multiple lines to Line Chart for swift 4.0? [\#3732](https://github.com/danielgindi/Charts/issues/3732)
- How to format y-values drawn on bar? [\#3731](https://github.com/danielgindi/Charts/issues/3731)
- how to moveViewToX and not calling setNeedsDisplay\(\) [\#3730](https://github.com/danielgindi/Charts/issues/3730)
- When using CombinedChartView, CandleChartData cannot be displayed [\#3729](https://github.com/danielgindi/Charts/issues/3729)
- Need Help. How to show labels in xAxis for GroupedBarChart? [\#3728](https://github.com/danielgindi/Charts/issues/3728)
- Force axis granularity [\#3727](https://github.com/danielgindi/Charts/issues/3727)
- Y decimal values error [\#3725](https://github.com/danielgindi/Charts/issues/3725)
-  if i have 840entrys how can i set the xaxis what i want .e.g i only want to show the 0 and 840 [\#3723](https://github.com/danielgindi/Charts/issues/3723)
- if i have 840entrys how can i set the xaxis what i want .e.g i only want to show the 0 and 840 [\#3722](https://github.com/danielgindi/Charts/issues/3722)
- Align both right and left axis in the same line [\#3720](https://github.com/danielgindi/Charts/issues/3720)
- The `open var noDataTextAlignment: NSTextAlignment = .left` is missing the `@objc` so it's not exposed in Objective C. [\#3719](https://github.com/danielgindi/Charts/issues/3719)
- How to hide bottom colors & label section in Bar Chat [\#3718](https://github.com/danielgindi/Charts/issues/3718)
- Draw Line chart for non linear X-axis [\#3717](https://github.com/danielgindi/Charts/issues/3717)
- Any way to adjust the candle bar width? [\#3716](https://github.com/danielgindi/Charts/issues/3716)
- xAxis grid line in-between bar rather than center of bar [\#3713](https://github.com/danielgindi/Charts/issues/3713)
- Bubble Chart is not rendering [\#3711](https://github.com/danielgindi/Charts/issues/3711)
- Bubble Chart is not rendering [\#3710](https://github.com/danielgindi/Charts/issues/3710)
- \[Feature request\] Continue line chart beyond x-axis limits [\#3708](https://github.com/danielgindi/Charts/issues/3708)
- Use of unresolved identifier 'UIAccessibility' & Type 'UIAccessibilityTraits' \(aka 'UInt64'\) has no member 'header' [\#3707](https://github.com/danielgindi/Charts/issues/3707)
- How to draw a line chart in sections? [\#3706](https://github.com/danielgindi/Charts/issues/3706)
- \[Feature / Help\] Resize Chart based on data visibility [\#3705](https://github.com/danielgindi/Charts/issues/3705)
- \[Request / Help\] Resize Chart based on hidden / shown lines [\#3704](https://github.com/danielgindi/Charts/issues/3704)
- listening for click events in pieChartView [\#3703](https://github.com/danielgindi/Charts/issues/3703)
- How to add Strings on Left Axis in iOS-charts? [\#3702](https://github.com/danielgindi/Charts/issues/3702)
- \[Charts.BarChartDataSet setDrawIconsEnabled:\]: unrecognized selector sent to instance [\#3700](https://github.com/danielgindi/Charts/issues/3700)
- How can I set xAxis to second line? [\#3698](https://github.com/danielgindi/Charts/issues/3698)
- Add to OC project and the api is so big, How to solve this problem [\#3697](https://github.com/danielgindi/Charts/issues/3697)
- How can I call func stringForValue\(\_ value: Double, axis: AxisBase?\) -\> String [\#3696](https://github.com/danielgindi/Charts/issues/3696)
- Line chart, slide left and right to see more data? How to set it up [\#3693](https://github.com/danielgindi/Charts/issues/3693)
- Value for SWIFT\_VERSION cannot be empty. \(in target 'Charts'\) [\#3692](https://github.com/danielgindi/Charts/issues/3692)
- how to make a combine chart \(line chart +  bar chart\) with leftAxis taking the top half area of the combined graph and right axis taking the bottom half  [\#3690](https://github.com/danielgindi/Charts/issues/3690)
- is there any library for Gantt chart in ios swift ? please suggest me a solution..... [\#3688](https://github.com/danielgindi/Charts/issues/3688)
- Integrated to xcode10 crash [\#3687](https://github.com/danielgindi/Charts/issues/3687)
- XCode 10 app crash. [\#3686](https://github.com/danielgindi/Charts/issues/3686)
- Getting Errors in Xcode 9.2 [\#3684](https://github.com/danielgindi/Charts/issues/3684)
- How to show double vale with string in y-axis [\#3683](https://github.com/danielgindi/Charts/issues/3683)
- When I have a lot of data, how can I slide to the far right by default? [\#3682](https://github.com/danielgindi/Charts/issues/3682)
- Pie Chart - Slices are not drawn and values are overlapping [\#3679](https://github.com/danielgindi/Charts/issues/3679)
- setVisibleXRangeMaximum is behaving unexpectedly [\#3678](https://github.com/danielgindi/Charts/issues/3678)
- Getting issues after pod update. [\#3677](https://github.com/danielgindi/Charts/issues/3677)
- Pie chart and gradient [\#3674](https://github.com/danielgindi/Charts/issues/3674)
- CandleStickChartRenderer drawDataSet method does not work with CombinedChartView [\#3673](https://github.com/danielgindi/Charts/issues/3673)
- How to Hide Text? [\#3672](https://github.com/danielgindi/Charts/issues/3672)
- Memory leaks were detected also in the demo project [\#3671](https://github.com/danielgindi/Charts/issues/3671)
- Can't add two pie charts in a uiviewcontroller at same time [\#3670](https://github.com/danielgindi/Charts/issues/3670)
- ChartData ValueTextColor can not achieve a variety of colors [\#3669](https://github.com/danielgindi/Charts/issues/3669)
- PieChartView settings color is Invalid  [\#3668](https://github.com/danielgindi/Charts/issues/3668)
- DrawValuesEnabled variable unreachable \(Swift 4.2\) [\#3665](https://github.com/danielgindi/Charts/issues/3665)
- Failed to verify bitcode in Charts.framework [\#3663](https://github.com/danielgindi/Charts/issues/3663)
- \[3.2.0\] Excessive Memory leaks in Swift 4.2 related to LineChartView, not present in 3.1.1 w/Swift 4 [\#3649](https://github.com/danielgindi/Charts/issues/3649)
- BarChart - xAxis Labels disappear if not a min of 2 on the screen [\#2854](https://github.com/danielgindi/Charts/issues/2854)
- 柱状图一直横向拉伸， 持续差不多 15s 就崩溃了  [\#2642](https://github.com/danielgindi/Charts/issues/2642)
- Any one try to integrate with SpriteKit/SKScene ? [\#2129](https://github.com/danielgindi/Charts/issues/2129)

**Merged pull requests:**

- Add Collection conformances to ChartDataSet types [\#3815](https://github.com/danielgindi/Charts/pull/3815) ([jjatie](https://github.com/jjatie))
- Fix condition that is checked before `chartTranslated` delegate method call [\#3804](https://github.com/danielgindi/Charts/pull/3804) ([anton-filimonov](https://github.com/anton-filimonov))
- fix \#3719 [\#3778](https://github.com/danielgindi/Charts/pull/3778) ([liuxuan30](https://github.com/liuxuan30))
- Turned gradient components and locations into constants [\#3775](https://github.com/danielgindi/Charts/pull/3775) ([jjatie](https://github.com/jjatie))
- add chartScaled\(\) call after double tap in BarLineChartViewBase [\#3770](https://github.com/danielgindi/Charts/pull/3770) ([artemiusmk](https://github.com/artemiusmk))
- Fixes sharp edges on the line chart [\#3764](https://github.com/danielgindi/Charts/pull/3764) ([stokatyan](https://github.com/stokatyan))
- Fix applying lineCap value for line chart data sets \(Fixes \#3739\) [\#3740](https://github.com/danielgindi/Charts/pull/3740) ([anton-filimonov](https://github.com/anton-filimonov))
- Update README.md [\#3737](https://github.com/danielgindi/Charts/pull/3737) ([justinlew](https://github.com/justinlew))
- Fix legend offset bug for horizontal bar chart \(Fixes \#3301\) [\#3736](https://github.com/danielgindi/Charts/pull/3736) ([SvenMuc](https://github.com/SvenMuc))
- Fix wrong assignment to axisMaxLabels property [\#3721](https://github.com/danielgindi/Charts/pull/3721) ([ggirotto](https://github.com/ggirotto))
- Add missing properties to copy\(with:\) methods [\#3715](https://github.com/danielgindi/Charts/pull/3715) ([dstranz](https://github.com/dstranz))
- Multiple colors for valueline \(Fixes \#3480\) [\#3709](https://github.com/danielgindi/Charts/pull/3709) ([AlexeiGitH](https://github.com/AlexeiGitH))
- Fix memory leak after rendering [\#3680](https://github.com/danielgindi/Charts/pull/3680) ([YusukeOba](https://github.com/YusukeOba))
- fix issue \#3662 [\#3664](https://github.com/danielgindi/Charts/pull/3664) ([Michael-Du](https://github.com/Michael-Du))
- Make NSUIAccessibilityElement initializer public. [\#3654](https://github.com/danielgindi/Charts/pull/3654) ([417-72KI](https://github.com/417-72KI))
- improvements in barRect height calculation  [\#3650](https://github.com/danielgindi/Charts/pull/3650) ([potato04](https://github.com/potato04))
- Update document to latest format [\#3621](https://github.com/danielgindi/Charts/pull/3621) ([kemchenj](https://github.com/kemchenj))
- Feature - ChartView Pan Ended Delegate Call [\#3612](https://github.com/danielgindi/Charts/pull/3612) ([AntonTheDev](https://github.com/AntonTheDev))
- Axis Renderers Cleanup [\#3164](https://github.com/danielgindi/Charts/pull/3164) ([jjatie](https://github.com/jjatie))

## [v3.2.1](https://github.com/danielgindi/Charts/tree/v3.2.1) (2018-10-08)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.2.0...v3.2.1)

**Closed issues:**

- Getting error after updating XCode to 10 from 9.4  [\#3661](https://github.com/danielgindi/Charts/issues/3661)
- Bar Chart with horizondal scroll on clicking button. [\#3660](https://github.com/danielgindi/Charts/issues/3660)
- How to change the piechart' valueLineColor and valueTextColor color individually [\#3658](https://github.com/danielgindi/Charts/issues/3658)
- Update to Swift 4.2 [\#3655](https://github.com/danielgindi/Charts/issues/3655)
- I am trying to use charts. for ios app. I am using cocoa pod for dependency setup. After setup i am trying to build the code it throwing compile errors. [\#3653](https://github.com/danielgindi/Charts/issues/3653)
- Type 'RunLoop' has no member 'Mode'  [\#3652](https://github.com/danielgindi/Charts/issues/3652)
- Module compiled with Swift 4.1.2 cannot be imported by the Swift 4.2  [\#3651](https://github.com/danielgindi/Charts/issues/3651)
- Type 'RunLoop' has no member 'Mode' [\#3648](https://github.com/danielgindi/Charts/issues/3648)
- As of todays update, I am running into this issue. 'common' renamed RunLoopMode.commonModes [\#3647](https://github.com/danielgindi/Charts/issues/3647)
- Change circleHoleColor for one point  [\#3646](https://github.com/danielgindi/Charts/issues/3646)
- This API is not working with Swift 4.1.. even after taking V3.2 of Charts API also.. I fixed it myself :\( [\#3645](https://github.com/danielgindi/Charts/issues/3645)
- Help building Charts Demo  [\#3643](https://github.com/danielgindi/Charts/issues/3643)
- Limit line label orientation [\#3641](https://github.com/danielgindi/Charts/issues/3641)
- Is it possible to zoom into a range of values? [\#3639](https://github.com/danielgindi/Charts/issues/3639)
- Not working with SDK-Swift 4.2 [\#3635](https://github.com/danielgindi/Charts/issues/3635)
- getting the error that let is implicitly final please change from open to public in Xcode10 GM Seed [\#3634](https://github.com/danielgindi/Charts/issues/3634)
- How to optimize when the amount of data is large [\#3633](https://github.com/danielgindi/Charts/issues/3633)
- Adding mathematical function plotting [\#3632](https://github.com/danielgindi/Charts/issues/3632)
- Color shadow inside piechart [\#3631](https://github.com/danielgindi/Charts/issues/3631)
- I can not set one label on y axis. [\#3630](https://github.com/danielgindi/Charts/issues/3630)
- where is the code for draw the xAxis and yAxis indicateLine [\#3628](https://github.com/danielgindi/Charts/issues/3628)
- Is it possible to add a vertical label for Y-Axis? Or anyone working on it? [\#3627](https://github.com/danielgindi/Charts/issues/3627)
- Adding dashed lines in between bars in bar graph [\#3626](https://github.com/danielgindi/Charts/issues/3626)
- Create Line chart and/or Bar chart from String Array [\#3625](https://github.com/danielgindi/Charts/issues/3625)
- Change data if button press [\#3624](https://github.com/danielgindi/Charts/issues/3624)
- Pie Chart and Legend [\#3622](https://github.com/danielgindi/Charts/issues/3622)
- Add labels to marker or create custom marker view [\#3620](https://github.com/danielgindi/Charts/issues/3620)
- is there any way we can change the values font size for ipad in the same code?  [\#3618](https://github.com/danielgindi/Charts/issues/3618)
- Reg: Display Min and Max Point in a PopUp [\#3617](https://github.com/danielgindi/Charts/issues/3617)
- Charts ScaleX problem [\#3616](https://github.com/danielgindi/Charts/issues/3616)
- CombinedChartView - EXC\_BAD\_ACCESS with doubleTapToZoomEnabled [\#3614](https://github.com/danielgindi/Charts/issues/3614)
- BarChartView  How to set the fixed width and spacing？ [\#3609](https://github.com/danielgindi/Charts/issues/3609)
- How can I combine two lineCharts？ [\#3607](https://github.com/danielgindi/Charts/issues/3607)
-  'Charts/Charts-Swift.h' file not found [\#3603](https://github.com/danielgindi/Charts/issues/3603)
- Combined Chart  with Horizontal Bar chart and a Line chart [\#3600](https://github.com/danielgindi/Charts/issues/3600)
- Design customization [\#3597](https://github.com/danielgindi/Charts/issues/3597)
- Move pie chart to specific location in the view [\#3595](https://github.com/danielgindi/Charts/issues/3595)
- Adding text data points to axis [\#3592](https://github.com/danielgindi/Charts/issues/3592)
- Performance hit [\#3585](https://github.com/danielgindi/Charts/issues/3585)
- Multiple colours for setValueTextColor and xAxis label with NSAttributedString [\#3566](https://github.com/danielgindi/Charts/issues/3566)
- Can't set a fixed width for YAxis with outside style. [\#3565](https://github.com/danielgindi/Charts/issues/3565)
- Switch from Grouped Bar chart back to normal [\#3551](https://github.com/danielgindi/Charts/issues/3551)
- X or Y axis interval of the labels  [\#3547](https://github.com/danielgindi/Charts/issues/3547)
- How can I plot all Axis Value? [\#3540](https://github.com/danielgindi/Charts/issues/3540)
- When the number of numeric numbers on the Y axis does not agree, the right of the multiple charts will not be aligned. [\#3347](https://github.com/danielgindi/Charts/issues/3347)

## [v3.2.0](https://github.com/danielgindi/Charts/tree/v3.2.0) (2018-09-17)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.1.1...v3.2.0)

**Fixed bugs:**

- Remove noDataText [\#3396](https://github.com/danielgindi/Charts/issues/3396)

**Closed issues:**

- Xcode 10 beta & Swift 4.2 support [\#3623](https://github.com/danielgindi/Charts/issues/3623)
- CombinedChartView not population correctly  [\#3619](https://github.com/danielgindi/Charts/issues/3619)
- How to remove Cubic Line chart value? [\#3615](https://github.com/danielgindi/Charts/issues/3615)
- How to set Y axis to display different colors of label [\#3611](https://github.com/danielgindi/Charts/issues/3611)
- Installation with carthage error!!! [\#3610](https://github.com/danielgindi/Charts/issues/3610)
- xcodebuild archive error [\#3606](https://github.com/danielgindi/Charts/issues/3606)
- swift library added successfully, but can't reach any of the interfaces [\#3605](https://github.com/danielgindi/Charts/issues/3605)
- BarChart draw sum of values above bar  [\#3604](https://github.com/danielgindi/Charts/issues/3604)
- iOS 12 [\#3601](https://github.com/danielgindi/Charts/issues/3601)
- v [\#3598](https://github.com/danielgindi/Charts/issues/3598)
- Compilation errors with Swift 4.1 [\#3596](https://github.com/danielgindi/Charts/issues/3596)
- Bar Chart highlights last clicked bar [\#3594](https://github.com/danielgindi/Charts/issues/3594)
- Remove hightlight, when swipe ends. Or just catch event when swipe ends [\#3588](https://github.com/danielgindi/Charts/issues/3588)
- bars did not displayed as expect [\#3586](https://github.com/danielgindi/Charts/issues/3586)
- what about the property 'gradientPositions' on branch 4.0.0, and how to use [\#3584](https://github.com/danielgindi/Charts/issues/3584)
- Creating markers for Radar Charts [\#3582](https://github.com/danielgindi/Charts/issues/3582)
- How to display value on touch? [\#3581](https://github.com/danielgindi/Charts/issues/3581)
- LineChart \(gradient fill\) is not working in Swift 4 [\#3580](https://github.com/danielgindi/Charts/issues/3580)
- Is there a way to use Line Chart with custom xAxis? [\#3579](https://github.com/danielgindi/Charts/issues/3579)
- Xaxis MutliLine and Value Multiline [\#3575](https://github.com/danielgindi/Charts/issues/3575)
- REACT NATIVE CHART ISSUE [\#3574](https://github.com/danielgindi/Charts/issues/3574)
- Data labels not shown when mutable array of entries used.  [\#3573](https://github.com/danielgindi/Charts/issues/3573)
- Grid background color extends beyond data sets [\#3572](https://github.com/danielgindi/Charts/issues/3572)
- real time plot [\#3571](https://github.com/danielgindi/Charts/issues/3571)
- Add shadow effect for pie chart [\#3570](https://github.com/danielgindi/Charts/issues/3570)
- The target “Charts” contains source code developed with Swift 2.x. Xcode 9 does not support building or migrating Swift 2.x targets. [\#3569](https://github.com/danielgindi/Charts/issues/3569)
- What is the best way to show around 100'000 values? [\#3568](https://github.com/danielgindi/Charts/issues/3568)
- CandleChart In CombinedChartView not work [\#3567](https://github.com/danielgindi/Charts/issues/3567)
- the x axis label is not in the right position when I used groupBarChart [\#3564](https://github.com/danielgindi/Charts/issues/3564)
- Bar Charts with only top outline like line chart [\#3562](https://github.com/danielgindi/Charts/issues/3562)
- How to fill the area between two lines on LineChart [\#3561](https://github.com/danielgindi/Charts/issues/3561)
- How to support rose chart? [\#3560](https://github.com/danielgindi/Charts/issues/3560)
- How I can get unselected entry [\#3559](https://github.com/danielgindi/Charts/issues/3559)
- Custom x-axis labels won't show without backing data [\#3557](https://github.com/danielgindi/Charts/issues/3557)
- Cannot build Charts project using Carthage [\#3555](https://github.com/danielgindi/Charts/issues/3555)
- Customizing bar chart [\#3554](https://github.com/danielgindi/Charts/issues/3554)
- Line Chart: Can't display icons unless data labels are also displayed [\#3553](https://github.com/danielgindi/Charts/issues/3553)
- How to enable highlight to some datapoints only when chart is zoomed  [\#3552](https://github.com/danielgindi/Charts/issues/3552)
- 关于OC项目引入Charts IPA体积过大的问题 [\#3549](https://github.com/danielgindi/Charts/issues/3549)
- X or Y unit？How to do? [\#3545](https://github.com/danielgindi/Charts/issues/3545)
- Why my chart have line on zero on yAXis  [\#3544](https://github.com/danielgindi/Charts/issues/3544)
- Value of type 'EnumeratedSequence\<\[CGPoint\]\>' has no member 'compactMap' [\#3543](https://github.com/danielgindi/Charts/issues/3543)
- Show Axis unit in last label [\#3542](https://github.com/danielgindi/Charts/issues/3542)
- In iOS-Charts how to hide one of Y axis grid lines.  [\#3541](https://github.com/danielgindi/Charts/issues/3541)
- Highlighting on a scrollable line graph [\#3539](https://github.com/danielgindi/Charts/issues/3539)
- disable gradient fill Linechart [\#3537](https://github.com/danielgindi/Charts/issues/3537)
- Undefined symbols for architecture - import issues [\#3536](https://github.com/danielgindi/Charts/issues/3536)
- Horizontal Bar Chart Bar Collapsing [\#3535](https://github.com/danielgindi/Charts/issues/3535)
- How to remove space between line chart and top legend [\#3534](https://github.com/danielgindi/Charts/issues/3534)
- Scrolling of bar chart is not working initially.  [\#3532](https://github.com/danielgindi/Charts/issues/3532)
- Remove DataSet label [\#3531](https://github.com/danielgindi/Charts/issues/3531)
- HorizontalBarChartView 横向柱状图Y轴问题 [\#3530](https://github.com/danielgindi/Charts/issues/3530)
- 怎么设置柱状图的宽度不变 [\#3529](https://github.com/danielgindi/Charts/issues/3529)
- Is it possible to show the values of a horizontal stacked bar chart centered? [\#3526](https://github.com/danielgindi/Charts/issues/3526)
- how to change LineChartDataSet without strange redraw issues? [\#3525](https://github.com/danielgindi/Charts/issues/3525)
- I want to add image in left side of line graph when we scroll the image is also scroll  [\#3521](https://github.com/danielgindi/Charts/issues/3521)
- Fixed label at axis origin [\#3519](https://github.com/danielgindi/Charts/issues/3519)
- Horizontal bar charts labels max out too early [\#3517](https://github.com/danielgindi/Charts/issues/3517)
- multiple PieCharts in on Screen [\#3516](https://github.com/danielgindi/Charts/issues/3516)
- First and Last Bars are not displaying full bars in CombinedChartView [\#3515](https://github.com/danielgindi/Charts/issues/3515)
- Line chart does not render correctly [\#3514](https://github.com/danielgindi/Charts/issues/3514)
- Line chart with LineChartView the line is lower than the minimum values [\#3513](https://github.com/danielgindi/Charts/issues/3513)
- Bar chart is plotting for empty value and Line chart is not appeared in CombinedChartView [\#3511](https://github.com/danielgindi/Charts/issues/3511)
- The shape of graph becomes smaller and smaller in RadarChartGraph [\#3510](https://github.com/danielgindi/Charts/issues/3510)
- not able to display x-axis labels like \(jun, july\) in line chart [\#3509](https://github.com/danielgindi/Charts/issues/3509)
- Move points on LineChart with Pan Gesture [\#3508](https://github.com/danielgindi/Charts/issues/3508)
- 32 bit Device not handling high x-values [\#3507](https://github.com/danielgindi/Charts/issues/3507)
- How do you override the getFormattedValue\(\) method when it doesn't exist? [\#3506](https://github.com/danielgindi/Charts/issues/3506)
- drawBarShadowEnabled is crashing in swift 4 [\#3505](https://github.com/danielgindi/Charts/issues/3505)
- x,y axis and guidelines hide on grah reload [\#3503](https://github.com/danielgindi/Charts/issues/3503)
- 显示float问题 [\#3501](https://github.com/danielgindi/Charts/issues/3501)
- App Crash in release mode not in debug mode  [\#3499](https://github.com/danielgindi/Charts/issues/3499)
- Arrow on axis end [\#3498](https://github.com/danielgindi/Charts/issues/3498)
- LineChartView setup  "set.mode = LineChartModeCubicBezier",The wave-peak trough of the smooth chart shows no value and deviates from these points [\#3497](https://github.com/danielgindi/Charts/issues/3497)
- Errors when trying to compile a project using Charts [\#3496](https://github.com/danielgindi/Charts/issues/3496)
- Need Help I want to Make 2 lines of X axis label and x axis label background color [\#3495](https://github.com/danielgindi/Charts/issues/3495)
- Getting current xAxis value [\#3494](https://github.com/danielgindi/Charts/issues/3494)
- where to catch the function about marker disappears or appears with dataEntrance [\#3493](https://github.com/danielgindi/Charts/issues/3493)
- Get chart image without rendering chart [\#3492](https://github.com/danielgindi/Charts/issues/3492)
- Cant able to set XAxis Range for custom DateTime formatter [\#3491](https://github.com/danielgindi/Charts/issues/3491)
- How to set the accuracy of data, such as two decimal places, e.g 2.32, 3.21. [\#3490](https://github.com/danielgindi/Charts/issues/3490)
- FFT graphs? [\#3489](https://github.com/danielgindi/Charts/issues/3489)
- Is there a better way to easily add highest and lowest value in candlestick chart [\#3488](https://github.com/danielgindi/Charts/issues/3488)
- Remove piechart center white area [\#3487](https://github.com/danielgindi/Charts/issues/3487)
- xAxis label is croped  when using custom valueFormatter [\#3485](https://github.com/danielgindi/Charts/issues/3485)
- Change location of left axis labels [\#3484](https://github.com/danielgindi/Charts/issues/3484)
- How to plot multiple yValue but display selected xValue in xAxis Label? [\#3483](https://github.com/danielgindi/Charts/issues/3483)
- Bubble Charts Choose not jump into the method “chartValueSelected...”? [\#3482](https://github.com/danielgindi/Charts/issues/3482)
- How to plot time value format on x-axis in real time graph [\#3481](https://github.com/danielgindi/Charts/issues/3481)
- How to remove Right side vertical line and values in LineChartView [\#3478](https://github.com/danielgindi/Charts/issues/3478)
- Bar Chart View acting weird \(zooming\) [\#3477](https://github.com/danielgindi/Charts/issues/3477)
- Is their anyway to show legend in rows and columns format? [\#3476](https://github.com/danielgindi/Charts/issues/3476)
- Duplicate Y-axis values while pitching the graph moving up [\#3475](https://github.com/danielgindi/Charts/issues/3475)
- getValuesByTouchPoint / UIView coords to graph coords [\#3474](https://github.com/danielgindi/Charts/issues/3474)
- Pie Chart label not show if its value 0 [\#3473](https://github.com/danielgindi/Charts/issues/3473)
- Build failed [\#3472](https://github.com/danielgindi/Charts/issues/3472)
- What will be the version of pod if I am running on swift 2.3 ? [\#3471](https://github.com/danielgindi/Charts/issues/3471)
- Using SPM results in a "dependency graph is unresolvable" error. [\#3470](https://github.com/danielgindi/Charts/issues/3470)
- Demo is not compiling [\#3469](https://github.com/danielgindi/Charts/issues/3469)
- Question [\#3466](https://github.com/danielgindi/Charts/issues/3466)
- need help [\#3464](https://github.com/danielgindi/Charts/issues/3464)
- bar Chart legend always show one label [\#3462](https://github.com/danielgindi/Charts/issues/3462)
- Monitor rotation angle [\#3461](https://github.com/danielgindi/Charts/issues/3461)
-  when clicking chartView,How to prompt multiple markerView [\#3460](https://github.com/danielgindi/Charts/issues/3460)
- The chart will draw incorrectly if LineChart leftAxis.axisMinimum does not start at zero [\#3458](https://github.com/danielgindi/Charts/issues/3458)
- Grouped bar chart plotting wrong !! [\#3457](https://github.com/danielgindi/Charts/issues/3457)
- 为什么我设置X轴不显示 [\#3456](https://github.com/danielgindi/Charts/issues/3456)
- how to set string values on x axis in bar chart? please help [\#3455](https://github.com/danielgindi/Charts/issues/3455)
- Left axis and right axis not same zero line [\#3454](https://github.com/danielgindi/Charts/issues/3454)
- How can make HorizontalBarChart dont start at zero? [\#3453](https://github.com/danielgindi/Charts/issues/3453)
- Can't customise X-axis grid line labels in time value line chart. [\#3452](https://github.com/danielgindi/Charts/issues/3452)
- xAxis gridlines are missing [\#3451](https://github.com/danielgindi/Charts/issues/3451)
- How to add attributed string in Xaxis labels [\#3450](https://github.com/danielgindi/Charts/issues/3450)
- 点击0这一列，所有的柱状图都高亮，可以吗 [\#3449](https://github.com/danielgindi/Charts/issues/3449)
- how to use 'LineChartView' to draw a line between nonzero values? [\#3448](https://github.com/danielgindi/Charts/issues/3448)
- The maximum value of the histogram is not shown. [\#3447](https://github.com/danielgindi/Charts/issues/3447)
- The Y-axis numerical display problem. [\#3446](https://github.com/danielgindi/Charts/issues/3446)
- Is it possible to show x-Axis gridlines when there's only one ChartDataEntry? [\#3444](https://github.com/danielgindi/Charts/issues/3444)
- Chart callback events [\#3443](https://github.com/danielgindi/Charts/issues/3443)
- Help needed with Grouped Horizontal Bar Chart [\#3442](https://github.com/danielgindi/Charts/issues/3442)
- Candlestick doesn't work when x does not start at 0 [\#3441](https://github.com/danielgindi/Charts/issues/3441)
- Is there a callback method? [\#3439](https://github.com/danielgindi/Charts/issues/3439)
- oc项目集成Charts，上架打包失败，提示ERROR ITMS-90171: "Invalid Bundle Structure - The binary file '时间炼.app/Frameworks/Charts.framework/Charts' is not permitted. Your app can’t contain standalone executables or libraries, other than a valid CFBundleExecutable of supported bundles. Refer to the Bundle Programming Guide at https://developer.apple.com/go/?id=bundle-structure for information on the iOS app bundle structure."。新手，求解答！Thanks [\#3438](https://github.com/danielgindi/Charts/issues/3438)
- Axis label alignment with labelRotationAngle [\#3437](https://github.com/danielgindi/Charts/issues/3437)
- Show grid with axis deactivated [\#3436](https://github.com/danielgindi/Charts/issues/3436)
- Error: cannot use instance member '\_viewPortHandler' within property initializer; property initializers run before 'self' is available [\#3434](https://github.com/danielgindi/Charts/issues/3434)
- remove labels [\#3433](https://github.com/danielgindi/Charts/issues/3433)
- How to set the number of lines for the label of xAxis of HorizontalBarChartView ? [\#3431](https://github.com/danielgindi/Charts/issues/3431)
- xAxis labelText too long, it can not draw complete. [\#3430](https://github.com/danielgindi/Charts/issues/3430)
- Weird crash [\#3427](https://github.com/danielgindi/Charts/issues/3427)
- Why does the default value of the Y label take the decimal point? [\#3426](https://github.com/danielgindi/Charts/issues/3426)
- Why won't the viewport move to the last X value of my line chart? [\#3425](https://github.com/danielgindi/Charts/issues/3425)
- How to disable highlight for some specific values [\#3423](https://github.com/danielgindi/Charts/issues/3423)
- IAxisValueFormatter Error:  Index out of range [\#3422](https://github.com/danielgindi/Charts/issues/3422)
- can we animate SelectionShift when selecting a segment of pie chart? [\#3420](https://github.com/danielgindi/Charts/issues/3420)
- Y-axis minimum scale deviation problem. [\#3419](https://github.com/danielgindi/Charts/issues/3419)
- How do you set the scale of the Y-axis? [\#3418](https://github.com/danielgindi/Charts/issues/3418)
- BarLineChartViewBase doubleTapGestureRecognized  [\#3417](https://github.com/danielgindi/Charts/issues/3417)
- Remove the box from the pie chart. [\#3416](https://github.com/danielgindi/Charts/issues/3416)
- Unable to set transparent color or clear color to the graphs background. [\#3414](https://github.com/danielgindi/Charts/issues/3414)
- BarChartView has no TouchUpInside event, only TouchDown event. [\#3411](https://github.com/danielgindi/Charts/issues/3411)
- YAxis,the integer bit is missing, showing.0 or.00 [\#3410](https://github.com/danielgindi/Charts/issues/3410)
- touchesEnded can't be called in chart view? [\#3409](https://github.com/danielgindi/Charts/issues/3409)
- Is it possible to show date values in the Stacked BarChart? [\#3408](https://github.com/danielgindi/Charts/issues/3408)
- Passing STRING values to X-Axis currently the SetDataCount is accepting only INT and Double data types [\#3407](https://github.com/danielgindi/Charts/issues/3407)
- compactMap build error [\#3405](https://github.com/danielgindi/Charts/issues/3405)
- Marker in CombinedChart is displayed incorrectly. [\#3404](https://github.com/danielgindi/Charts/issues/3404)
-  I need to customize the column chart [\#3401](https://github.com/danielgindi/Charts/issues/3401)
- YAxis show the value .0 or .00 [\#3400](https://github.com/danielgindi/Charts/issues/3400)
- How to do BarChart \(grouped DataSets\)\(OC\)? [\#3399](https://github.com/danielgindi/Charts/issues/3399)
- Can't build success with xcode toolchains swift 4.0.3 release [\#3398](https://github.com/danielgindi/Charts/issues/3398)
- pod not install in xcode 9.3 version [\#3397](https://github.com/danielgindi/Charts/issues/3397)
- Lag during scroll [\#3395](https://github.com/danielgindi/Charts/issues/3395)
- Hide percents on PieChartDiagram when it's so small [\#3394](https://github.com/danielgindi/Charts/issues/3394)
- Value of type 'BarChartDataEntries' has no member 'unit' [\#3393](https://github.com/danielgindi/Charts/issues/3393)
- BarChart 1st bar offset  [\#3392](https://github.com/danielgindi/Charts/issues/3392)
- Open up Access Modifiers? [\#3391](https://github.com/danielgindi/Charts/issues/3391)
- ld: warning: directory not found for option '-F/Users/<USER>/Library/Developer/Xcode/DerivedData/CS\_PROJECT\_-\_ALPHA-hglwyidqwcstjvejzxikmednefgv/Build/Products/Debug-iphonesimulator/Charts' ld: framework not found Charts clang: error: linker command failed with exit code 1 \(use -v to see invocation\) [\#3390](https://github.com/danielgindi/Charts/issues/3390)
- How to install charts Framework using Xcode 9.1  [\#3388](https://github.com/danielgindi/Charts/issues/3388)
- Unable to compile swift 3.2 code from xcode  9.3. I am using 3.1.1 version of charts. [\#3387](https://github.com/danielgindi/Charts/issues/3387)
- why there is flexible space between the BarChatView and the xAxis ? sometimes the space are nil but sometimes the space exist . when it happens the xAxis will draw rect inside the PilaView . it doesn't connected with the XAxisLabelPosition attribute . i cann't figured it out [\#3386](https://github.com/danielgindi/Charts/issues/3386)
- Error building project [\#3385](https://github.com/danielgindi/Charts/issues/3385)
- missing property in latest LineChartView  [\#3383](https://github.com/danielgindi/Charts/issues/3383)
- minOffset not working [\#3382](https://github.com/danielgindi/Charts/issues/3382)
- Display Real time data like pulseRate. [\#3381](https://github.com/danielgindi/Charts/issues/3381)
- Grouped bar chart shows up as stacked bar chart after migration. [\#3380](https://github.com/danielgindi/Charts/issues/3380)
- Candle stick chart cds.setBarSpace\(\) not working [\#3379](https://github.com/danielgindi/Charts/issues/3379)
- Line chart fill color goes above/below zeroline [\#3377](https://github.com/danielgindi/Charts/issues/3377)
- Charts Pod not working with Xcode 9.3 \(Swift 4.1\) [\#3376](https://github.com/danielgindi/Charts/issues/3376)
- Can any one know how to draw this charts  [\#3375](https://github.com/danielgindi/Charts/issues/3375)
- Compile error on newest ChartsDemo-iOS project [\#3373](https://github.com/danielgindi/Charts/issues/3373)
- ChartIndexAxisValueFormatter Not working in line charts [\#3371](https://github.com/danielgindi/Charts/issues/3371)
- Line Graph xValue [\#3367](https://github.com/danielgindi/Charts/issues/3367)
- xAxis labels are not aligned with grouped bar,  when data is more [\#3364](https://github.com/danielgindi/Charts/issues/3364)
- Select the data point programmatically and the highLight is NAN    [\#3363](https://github.com/danielgindi/Charts/issues/3363)
- Layout issue with chart legend  [\#3359](https://github.com/danielgindi/Charts/issues/3359)
- Is it possible to draw the line on top of a custom XAxisRenderer? [\#3348](https://github.com/danielgindi/Charts/issues/3348)
- How to set x-axis labels with selected round with color. [\#3345](https://github.com/danielgindi/Charts/issues/3345)
- How to hide the grid background on some bar  [\#3342](https://github.com/danielgindi/Charts/issues/3342)
- Space between axis line and axis label [\#3336](https://github.com/danielgindi/Charts/issues/3336)
- Can't manage to align plots with x axis values [\#3332](https://github.com/danielgindi/Charts/issues/3332)
- Show the last x axis label on bar chart  [\#3324](https://github.com/danielgindi/Charts/issues/3324)
- HorizontalBarChartView  display partial data [\#3320](https://github.com/danielgindi/Charts/issues/3320)
- 如何在滑动代理中获取当前中心位置所对应的数据源的索引 [\#3318](https://github.com/danielgindi/Charts/issues/3318)
- Visible max and min Y Values [\#2600](https://github.com/danielgindi/Charts/issues/2600)
- Invert xAxis label [\#2504](https://github.com/danielgindi/Charts/issues/2504)
- @junito1209 please explain what are you doing on Charts wiki page [\#2261](https://github.com/danielgindi/Charts/issues/2261)
- Crash Observed when AxisRenderer interval is becoming NaN [\#2168](https://github.com/danielgindi/Charts/issues/2168)
- Problem selecting bubbles on the same xIndex [\#2060](https://github.com/danielgindi/Charts/issues/2060)

**Merged pull requests:**

- Add Swift version 4.1 to podspec [\#3608](https://github.com/danielgindi/Charts/pull/3608) ([larryonoff](https://github.com/larryonoff))
- update barRect.size.height calculation [\#3587](https://github.com/danielgindi/Charts/pull/3587) ([potato04](https://github.com/potato04))
- Add label colors to legend entries [\#3558](https://github.com/danielgindi/Charts/pull/3558) ([petester42](https://github.com/petester42))
- Support inlune bubble viz selection [\#3548](https://github.com/danielgindi/Charts/pull/3548) ([chuynadamas](https://github.com/chuynadamas))
- fix the error title for demo [\#3528](https://github.com/danielgindi/Charts/pull/3528) ([yangasahi](https://github.com/yangasahi))
- Changes for Swift 4.2, Xcode 10 and iOS 12 [\#3522](https://github.com/danielgindi/Charts/pull/3522) ([jlcanale](https://github.com/jlcanale))
- Accessibility Support for \(most\) Chart types [\#3520](https://github.com/danielgindi/Charts/pull/3520) ([mathewa6](https://github.com/mathewa6))
- Changed comment that referenced getFormattedValue\(\) method in IValueFormatter [\#3518](https://github.com/danielgindi/Charts/pull/3518) ([JCMcLovin](https://github.com/JCMcLovin))
- Fix merge conflicts in \#3218 [\#3500](https://github.com/danielgindi/Charts/pull/3500) ([petester42](https://github.com/petester42))
- Make legendRenderer property public in order to be externally customizable [\#3445](https://github.com/danielgindi/Charts/pull/3445) ([nagykatalin](https://github.com/nagykatalin))
- Fix broken demo link in readme [\#3440](https://github.com/danielgindi/Charts/pull/3440) ([robert-cronin](https://github.com/robert-cronin))
- Added clamping function for `Comparable` [\#3435](https://github.com/danielgindi/Charts/pull/3435) ([jjatie](https://github.com/jjatie))
- Fix CocoaPods compilation [\#3432](https://github.com/danielgindi/Charts/pull/3432) ([larryonoff](https://github.com/larryonoff))
- update candle chart view options in demo project [\#3424](https://github.com/danielgindi/Charts/pull/3424) ([cuong1112035](https://github.com/cuong1112035))
- Add Objective-c compatible for turning off drag in X and Y Axis separately [\#3421](https://github.com/danielgindi/Charts/pull/3421) ([lennonhe](https://github.com/lennonhe))
- Added gradient line drawing to LineChartRenderer. based on PR \#3142 [\#3415](https://github.com/danielgindi/Charts/pull/3415) ([larryonoff](https://github.com/larryonoff))
- Add more render options for y axis labels [\#3406](https://github.com/danielgindi/Charts/pull/3406) ([alexrepty](https://github.com/alexrepty))
- Refactored ChartData [\#3169](https://github.com/danielgindi/Charts/pull/3169) ([jjatie](https://github.com/jjatie))
- Dataset logic cleanup [\#3001](https://github.com/danielgindi/Charts/pull/3001) ([jjatie](https://github.com/jjatie))
- Added value text rotation [\#2200](https://github.com/danielgindi/Charts/pull/2200) ([chinh-tran](https://github.com/chinh-tran))

## [v3.1.1](https://github.com/danielgindi/Charts/tree/v3.1.1) (2018-04-02)
[Full Changelog](https://github.com/danielgindi/Charts/compare/3.1.1...v3.1.1)

## [3.1.1](https://github.com/danielgindi/Charts/tree/3.1.1) (2018-04-02)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.1.0...3.1.1)

**Fixed bugs:**

- ChartsDemo-Swift crashes [\#3327](https://github.com/danielgindi/Charts/issues/3327)

**Closed issues:**

- Why does yVals start with a negative number when the data is empty? [\#3374](https://github.com/danielgindi/Charts/issues/3374)
- Compile Error [\#3372](https://github.com/danielgindi/Charts/issues/3372)
- Y Axis need to add Time slots for current day [\#3368](https://github.com/danielgindi/Charts/issues/3368)
- inconsistent include of UIKit vs AppKit [\#3362](https://github.com/danielgindi/Charts/issues/3362)
- Crash Issue in BarChartData [\#3361](https://github.com/danielgindi/Charts/issues/3361)
- How can we format the LABEL used for values? [\#3354](https://github.com/danielgindi/Charts/issues/3354)
- Remove value labels padding [\#3350](https://github.com/danielgindi/Charts/issues/3350)
- Does Charts support real-time drawing [\#3344](https://github.com/danielgindi/Charts/issues/3344)
- Constant for Bar width and space [\#3343](https://github.com/danielgindi/Charts/issues/3343)
- Bar Graph groupBars function issue [\#1966](https://github.com/danielgindi/Charts/issues/1966)
- bar chart from dates [\#1963](https://github.com/danielgindi/Charts/issues/1963)
- A crash of CombinedChartView,when touch on the chart view\(chas\) [\#1957](https://github.com/danielgindi/Charts/issues/1957)
- Graph doesn't display on older iPads [\#1954](https://github.com/danielgindi/Charts/issues/1954)
- Regarding deprecated xValues [\#1947](https://github.com/danielgindi/Charts/issues/1947)
- Charts being overlaid inside UICollectionView using dequeueReusableCell [\#1943](https://github.com/danielgindi/Charts/issues/1943)
- We are unable to split the label in two lines. [\#1941](https://github.com/danielgindi/Charts/issues/1941)
- Make releases so they follow the Semantic Versioning \(SemVer\) [\#1930](https://github.com/danielgindi/Charts/issues/1930)
- Marker Position [\#1876](https://github.com/danielgindi/Charts/issues/1876)
- Charts 3.0 unable to draw discontinuous line chart [\#1866](https://github.com/danielgindi/Charts/issues/1866)
- Radar charts custom labels [\#1840](https://github.com/danielgindi/Charts/issues/1840)
- Unable to subclass [\#1838](https://github.com/danielgindi/Charts/issues/1838)
- offset goes wrong when using mutiple bar chart in combined chart [\#1813](https://github.com/danielgindi/Charts/issues/1813)
- Histogram Example? [\#1792](https://github.com/danielgindi/Charts/issues/1792)
- Sunburst Chart [\#1715](https://github.com/danielgindi/Charts/issues/1715)
- Line chart with preview [\#1520](https://github.com/danielgindi/Charts/issues/1520)
- Get scroll position of dragable line chart [\#1272](https://github.com/danielgindi/Charts/issues/1272)
- Trend line in scatter chart? [\#1263](https://github.com/danielgindi/Charts/issues/1263)
- Can we use custom image instead of Circle in line chart? [\#1206](https://github.com/danielgindi/Charts/issues/1206)
- Legend with NSAttributedString and multiple lines on Vertical [\#1193](https://github.com/danielgindi/Charts/issues/1193)
- Trim the legend view  [\#1192](https://github.com/danielgindi/Charts/issues/1192)
- Chart auto min/max does not take into account second line [\#1136](https://github.com/danielgindi/Charts/issues/1136)
- Add different colorHoleColor for different datapoints in a LineChart iOS Charts [\#1104](https://github.com/danielgindi/Charts/issues/1104)
- How to remove marker programatically [\#1097](https://github.com/danielgindi/Charts/issues/1097)
- Overlapping issues in x and y axis values [\#1048](https://github.com/danielgindi/Charts/issues/1048)
- Enhancement: Horizontal Combined Chart [\#1041](https://github.com/danielgindi/Charts/issues/1041)
- Enhancement: Linked x-axis or stacked y-axis. [\#1022](https://github.com/danielgindi/Charts/issues/1022)
- Scale or zoom to particular axis or point in Combined chart view [\#993](https://github.com/danielgindi/Charts/issues/993)
- Max Value Y - Axis = 1  , How to set Y-Axis increment Y- Value ? [\#989](https://github.com/danielgindi/Charts/issues/989)
- Drawing value for the WholeStack only [\#926](https://github.com/danielgindi/Charts/issues/926)
- Pie chart Y values overlap [\#779](https://github.com/danielgindi/Charts/issues/779)
- Swift Package Manager [\#753](https://github.com/danielgindi/Charts/issues/753)
- Stacked bar chart with different number of rects [\#744](https://github.com/danielgindi/Charts/issues/744)
- The label value position with the same yAxis and different value. [\#724](https://github.com/danielgindi/Charts/issues/724)
- Minimum / maximum scale causes the chart to slide sideways when trying to pinch zoom further [\#437](https://github.com/danielgindi/Charts/issues/437)
- Top and bottom grid line diverging widths [\#411](https://github.com/danielgindi/Charts/issues/411)

**Merged pull requests:**

- Swift 4.1 [\#3370](https://github.com/danielgindi/Charts/pull/3370) ([jjatie](https://github.com/jjatie))
- Update ILineRadarChartDataSet.swift [\#3366](https://github.com/danielgindi/Charts/pull/3366) ([Ewg777](https://github.com/Ewg777))
- Add option to disable clipping data to contentRect [\#3360](https://github.com/danielgindi/Charts/pull/3360) ([wtmoose](https://github.com/wtmoose))

## [v3.1.0](https://github.com/danielgindi/Charts/tree/v3.1.0) (2018-03-22)
[Full Changelog](https://github.com/danielgindi/Charts/compare/3.1.0...v3.1.0)

## [3.1.0](https://github.com/danielgindi/Charts/tree/3.1.0) (2018-03-22)
[Full Changelog](https://github.com/danielgindi/Charts/compare/3.0.5...3.1.0)

**Fixed bugs:**

- Bug in ZoomViewJob? [\#3299](https://github.com/danielgindi/Charts/issues/3299)
- BarChartView bug if set barWidth was 1.0? [\#3213](https://github.com/danielgindi/Charts/issues/3213)
- Scatter circle disappear when chart scaled [\#3185](https://github.com/danielgindi/Charts/issues/3185)
- PieChart `highlightValue\(x:y:dataSetIndex:\)` should provide error for dataSetIndex \> 1 [\#3146](https://github.com/danielgindi/Charts/issues/3146)
- PieChart size is extremely small in a UITableViewCell [\#3108](https://github.com/danielgindi/Charts/issues/3108)
- Handling critical Y values with autoScaleMinMaxEnabled [\#2053](https://github.com/danielgindi/Charts/issues/2053)
- Charts 3.0 - Error: this application, or a library it uses, has passed an invalid numeric value \(NaN, or not-a-number\) to CoreGraphics API and this value is being ignored [\#1626](https://github.com/danielgindi/Charts/issues/1626)

**Closed issues:**

- Programatically passed renderer is corrupting animations [\#3349](https://github.com/danielgindi/Charts/issues/3349)
- How to show Integer value [\#3346](https://github.com/danielgindi/Charts/issues/3346)
- Does not build on Xcode 8.3.3 & Swift 3 [\#3341](https://github.com/danielgindi/Charts/issues/3341)
- how to use pod 'Charts' into objective c project , i create header file to use swift , but what should i  do after create header file ?  [\#3338](https://github.com/danielgindi/Charts/issues/3338)
- How to show all labels in xAxis ?  [\#3337](https://github.com/danielgindi/Charts/issues/3337)
- How to set the interval in X axis label [\#3335](https://github.com/danielgindi/Charts/issues/3335)
- Line chart with solid and dashed line [\#3334](https://github.com/danielgindi/Charts/issues/3334)
- Code signing is required for product type 'Application' in SDK 'iOS 11.2' [\#3333](https://github.com/danielgindi/Charts/issues/3333)
- My project within framework target and pod Charts into my framework target can't load Charts framework [\#3328](https://github.com/danielgindi/Charts/issues/3328)
- Swift 4.1 compilation warnings [\#3323](https://github.com/danielgindi/Charts/issues/3323)
- Two Horizontal grid lines always appear above Base Axis line [\#3322](https://github.com/danielgindi/Charts/issues/3322)
- BalloonMarker support swift 3 ? [\#3321](https://github.com/danielgindi/Charts/issues/3321)
- Unable to get horizontal scrolling in bar chart [\#3319](https://github.com/danielgindi/Charts/issues/3319)
- Horizontal Bar Chart xAxis formatted label textColor [\#3317](https://github.com/danielgindi/Charts/issues/3317)
- Wrong dataSetIndex in stringForValue delegate method [\#3314](https://github.com/danielgindi/Charts/issues/3314)
- Limit line in the left and right drag or zoom can be fixed [\#3313](https://github.com/danielgindi/Charts/issues/3313)
- Save picture crash in demos on iOS 11+ [\#3311](https://github.com/danielgindi/Charts/issues/3311)
- How to display reload text in iOS-Charts? [\#3310](https://github.com/danielgindi/Charts/issues/3310)
- Can't show Label text in XAxis [\#3308](https://github.com/danielgindi/Charts/issues/3308)
- Straight lines [\#3306](https://github.com/danielgindi/Charts/issues/3306)
- Values on X axis issue [\#3304](https://github.com/danielgindi/Charts/issues/3304)
- when setInverted\(true\) in the leftAxis of CombinedChart, the candleStickData doesnot show ? [\#3303](https://github.com/danielgindi/Charts/issues/3303)
- HorizontalBarChart X axe labels duplicated [\#3300](https://github.com/danielgindi/Charts/issues/3300)
- how can i change label color? [\#3298](https://github.com/danielgindi/Charts/issues/3298)
- value granularity [\#3296](https://github.com/danielgindi/Charts/issues/3296)
- Better way to manage Stacked Bar Chart Value labels? [\#3295](https://github.com/danielgindi/Charts/issues/3295)
- Remove Y axis border line in Line Graph [\#3294](https://github.com/danielgindi/Charts/issues/3294)
- XAxisRenderer not called [\#3293](https://github.com/danielgindi/Charts/issues/3293)
- How to display string value on xAxis label in line chart  [\#3292](https://github.com/danielgindi/Charts/issues/3292)
- how to change the color of text that displayed below the chart ios swift 3 [\#3288](https://github.com/danielgindi/Charts/issues/3288)
- color in BalloonMarker is never used [\#3287](https://github.com/danielgindi/Charts/issues/3287)
- Question: LineChart variable line thickness? [\#3285](https://github.com/danielgindi/Charts/issues/3285)
- Modify selected slice [\#3284](https://github.com/danielgindi/Charts/issues/3284)
- Put shadow under slice [\#3283](https://github.com/danielgindi/Charts/issues/3283)
- How can I take selected value index in Pie Chart [\#3282](https://github.com/danielgindi/Charts/issues/3282)
- hide other label when one slice of pie chart was selected [\#3281](https://github.com/danielgindi/Charts/issues/3281)
- How to show custom x and y axis details in LineChart in objective c ??? [\#3280](https://github.com/danielgindi/Charts/issues/3280)
- I need to put dates from an array on xAxis [\#3277](https://github.com/danielgindi/Charts/issues/3277)
- Is there a way to round the corners of the BalloonMarker? [\#3276](https://github.com/danielgindi/Charts/issues/3276)
- Set values not showing until chart is zoomed [\#3275](https://github.com/danielgindi/Charts/issues/3275)
- How to add gap between chart and legend? [\#3274](https://github.com/danielgindi/Charts/issues/3274)
- LineChart : How to show the grid in right axis [\#3271](https://github.com/danielgindi/Charts/issues/3271)
- How to custom axis' label in horizontal bar chart [\#3270](https://github.com/danielgindi/Charts/issues/3270)
- LineChart: How to fix space between point on  xAxis [\#3269](https://github.com/danielgindi/Charts/issues/3269)
- Line Chart Issue in CombinedChartView [\#3268](https://github.com/danielgindi/Charts/issues/3268)
- LineChartRenderer - drawHighlighted:context:indices: [\#3267](https://github.com/danielgindi/Charts/issues/3267)
- Scatter Bar Char - Plots on axis \(0,0\) partially visible [\#3266](https://github.com/danielgindi/Charts/issues/3266)
- is it support the ios 7??? [\#3263](https://github.com/danielgindi/Charts/issues/3263)
- Combined chart for candlestick + bar [\#3262](https://github.com/danielgindi/Charts/issues/3262)
- setScatterShape applied to Legend also [\#3261](https://github.com/danielgindi/Charts/issues/3261)
- ChartDataSet min and max values not recalculated when calling clear\(\)  [\#3260](https://github.com/danielgindi/Charts/issues/3260)
- How to realize the Line and the bar Chart in the same one Chart? [\#3259](https://github.com/danielgindi/Charts/issues/3259)
- '\_xBounds' and other variables inaccessible due to 'internal' protection level workaround [\#3258](https://github.com/danielgindi/Charts/issues/3258)
- //view\_line\_graph.centerViewTo\(xValue: Double\(dataEntries\[pos\].x\)  , yValue:   Double\(dataEntries\[pos\].y\), axis: YAxis.AxisDependency.right\) [\#3257](https://github.com/danielgindi/Charts/issues/3257)
- dyld: Library not loaded: @rpath/Charts.framework/Charts [\#3250](https://github.com/danielgindi/Charts/issues/3250)
- X Axis disapeared [\#3249](https://github.com/danielgindi/Charts/issues/3249)
- Save zoom state [\#3248](https://github.com/danielgindi/Charts/issues/3248)
- leftAxis label bug? [\#3246](https://github.com/danielgindi/Charts/issues/3246)
- x-axis label jumps when zoomed in and side scrolling. [\#3245](https://github.com/danielgindi/Charts/issues/3245)
- avoidFirstLastClippingEnabled not working and set last circleColor a different value [\#3244](https://github.com/danielgindi/Charts/issues/3244)
- Got totally 50 errors after install CocoaPods ..! I am using Swift 4.0 [\#3243](https://github.com/danielgindi/Charts/issues/3243)
- Error in Library When added pod file [\#3242](https://github.com/danielgindi/Charts/issues/3242)
- How to do  multiple  data on x axis? [\#3241](https://github.com/danielgindi/Charts/issues/3241)
- Missing entries in PieChartDataEntry legends. [\#3240](https://github.com/danielgindi/Charts/issues/3240)
- Candle Stick does not show [\#3239](https://github.com/danielgindi/Charts/issues/3239)
- How i give LineChart Entry using x index and y value in Charts '3.0.4'? help wanted [\#3238](https://github.com/danielgindi/Charts/issues/3238)
- ChartDataEntry with icons shows scatter shape at same time [\#3237](https://github.com/danielgindi/Charts/issues/3237)
- HorizontalBarChart - Right axis label starts with the highest value up to the same value [\#3235](https://github.com/danielgindi/Charts/issues/3235)
- Carthage Charts [\#3234](https://github.com/danielgindi/Charts/issues/3234)
- 3.0.5 Bar chart xAxis valueFormatter regression - no data points shown. [\#3233](https://github.com/danielgindi/Charts/issues/3233)
- Pin the graph on one side while zooming [\#3232](https://github.com/danielgindi/Charts/issues/3232)
- yAxisMaximum auto-increment RadarChartView [\#3231](https://github.com/danielgindi/Charts/issues/3231)
- Compile issue after install pod 'Chart' [\#3230](https://github.com/danielgindi/Charts/issues/3230)
- corner line chart [\#3227](https://github.com/danielgindi/Charts/issues/3227)
- Make specific pieChart segment stick out and go back to normal size, animated [\#3224](https://github.com/danielgindi/Charts/issues/3224)
- Dash line and highlighted top value [\#3221](https://github.com/danielgindi/Charts/issues/3221)
- Why is the xAxis Value Formater so often called? [\#3220](https://github.com/danielgindi/Charts/issues/3220)
- How easy is to add some technical indicators? [\#3219](https://github.com/danielgindi/Charts/issues/3219)
- How to set X-Axis data to bottom of chart with strings data? [\#3212](https://github.com/danielgindi/Charts/issues/3212)
- how to drag the LineChartView [\#3211](https://github.com/danielgindi/Charts/issues/3211)
- Release 3.0.5 contains version number inconsistencies [\#3210](https://github.com/danielgindi/Charts/issues/3210)
- Unable to Hide PieChart Legends.  [\#3209](https://github.com/danielgindi/Charts/issues/3209)
- Recent release with my Swift3 project [\#3208](https://github.com/danielgindi/Charts/issues/3208)
- Cannot set double value in Combined chart line chart. [\#3205](https://github.com/danielgindi/Charts/issues/3205)
- Bar charts with different column widths [\#3204](https://github.com/danielgindi/Charts/issues/3204)
- Labels on x-axes not linked to chart data points [\#3203](https://github.com/danielgindi/Charts/issues/3203)
- Remark: The AppCoda tutorial is outdated [\#3196](https://github.com/danielgindi/Charts/issues/3196)
- Unable to set strings as values in x axis of linechart [\#3195](https://github.com/danielgindi/Charts/issues/3195)
- drag event issie [\#3194](https://github.com/danielgindi/Charts/issues/3194)
- Custom XAxisRenderer labels are shifting around when scrolling [\#3193](https://github.com/danielgindi/Charts/issues/3193)
- How to asynchronous drawings LineChart? [\#3192](https://github.com/danielgindi/Charts/issues/3192)
- How do I listen to the left or right to the end of the event when I zoom in on the lineCharts? [\#3184](https://github.com/danielgindi/Charts/issues/3184)
- Want to delete color set beside legend label [\#3180](https://github.com/danielgindi/Charts/issues/3180)
- How to show/hide Stacked Bar Chart yValue? [\#3175](https://github.com/danielgindi/Charts/issues/3175)
- Readme needs to be updated for 3.0.5 [\#3170](https://github.com/danielgindi/Charts/issues/3170)
- Add Axis description to Charts Framework [\#3168](https://github.com/danielgindi/Charts/issues/3168)
- ChartViewDelegate not called when barChart is out of screen [\#3165](https://github.com/danielgindi/Charts/issues/3165)
- dash line \(separators\) in between in bars. [\#3163](https://github.com/danielgindi/Charts/issues/3163)
- StackedBarChart Value on middle of each stacked bar chart  [\#3162](https://github.com/danielgindi/Charts/issues/3162)
- ChartLimitLine Label Position [\#3161](https://github.com/danielgindi/Charts/issues/3161)
- bar chat  [\#3158](https://github.com/danielgindi/Charts/issues/3158)
- Bar coloring [\#3157](https://github.com/danielgindi/Charts/issues/3157)
- HorizontalBarChart doesn't display bar draw values when bar is partially off ViewPort [\#3155](https://github.com/danielgindi/Charts/issues/3155)
- X-Axis Labels not showing [\#3154](https://github.com/danielgindi/Charts/issues/3154)
- How to make highlight scrolling on chart not trigger parent scrollView? \[help\] [\#3153](https://github.com/danielgindi/Charts/issues/3153)
- How to fill the color like the marked part of this following picture? Thanks a lot! [\#3144](https://github.com/danielgindi/Charts/issues/3144)
- finger leaves the chart area [\#3128](https://github.com/danielgindi/Charts/issues/3128)
- Technical Support in Github [\#3126](https://github.com/danielgindi/Charts/issues/3126)
- Plotting the bar from top to bottom \(in reverse manner\) [\#3125](https://github.com/danielgindi/Charts/issues/3125)
- pieChart Data name overlap [\#3109](https://github.com/danielgindi/Charts/issues/3109)
- LineChart with different colors based on values. [\#3107](https://github.com/danielgindi/Charts/issues/3107)
- AxisBase Value Formatter [\#3105](https://github.com/danielgindi/Charts/issues/3105)
- Multiple charts in same view [\#3103](https://github.com/danielgindi/Charts/issues/3103)
- Draw dotted x-axis timeline [\#3102](https://github.com/danielgindi/Charts/issues/3102)
- PieChart value labels are overlaping when values are between 0 to 9 [\#2948](https://github.com/danielgindi/Charts/issues/2948)
- xValues on X-Axis for non-continuous dates [\#2398](https://github.com/danielgindi/Charts/issues/2398)
- y-labels position is wrong，when i set autoScaleMinMaxEnabled = YES [\#2379](https://github.com/danielgindi/Charts/issues/2379)
- Distance between chart legend and the horizontal bar chart is to big in case of rotated labels [\#2138](https://github.com/danielgindi/Charts/issues/2138)
- How to set LabelCount bigger than 25 in HorizatalBarView [\#2085](https://github.com/danielgindi/Charts/issues/2085)

**Merged pull requests:**

- bump to 3.1 release [\#3357](https://github.com/danielgindi/Charts/pull/3357) ([liuxuan30](https://github.com/liuxuan30))
- Refactors -\[tableView:cellForRowAtIndexPath:\] [\#3326](https://github.com/danielgindi/Charts/pull/3326) ([valeriyvan](https://github.com/valeriyvan))
- minor bug fix in favor of 3.1 release [\#3312](https://github.com/danielgindi/Charts/pull/3312) ([liuxuan30](https://github.com/liuxuan30))
- add pie chart unit tests [\#3297](https://github.com/danielgindi/Charts/pull/3297) ([liuxuan30](https://github.com/liuxuan30))
- Align Objc and Swift demos balloon marker [\#3291](https://github.com/danielgindi/Charts/pull/3291) ([liuxuan30](https://github.com/liuxuan30))
- for \#3146. add a warning message if pie chart has more than one data set [\#3286](https://github.com/danielgindi/Charts/pull/3286) ([liuxuan30](https://github.com/liuxuan30))
- Issue templates [\#3278](https://github.com/danielgindi/Charts/pull/3278) ([jjatie](https://github.com/jjatie))
- Min and Max reset when clearing ChartDataSet \(Fixes \#3260\) [\#3265](https://github.com/danielgindi/Charts/pull/3265) ([carlo-](https://github.com/carlo-))
- Restored old performance in ChartDataSet [\#3216](https://github.com/danielgindi/Charts/pull/3216) ([jjatie](https://github.com/jjatie))
- Support other bundle than main MarkerView.viewFromXib\(\) [\#3215](https://github.com/danielgindi/Charts/pull/3215) ([charlymr](https://github.com/charlymr))
- BubbleChart uses correct colour for index now. [\#3202](https://github.com/danielgindi/Charts/pull/3202) ([jjatie](https://github.com/jjatie))
- Added custom text alignment for noData [\#3199](https://github.com/danielgindi/Charts/pull/3199) ([jjatie](https://github.com/jjatie))
- Call setNeedsDisplay\(\) to trigger render noDataText [\#3198](https://github.com/danielgindi/Charts/pull/3198) ([liuxuan30](https://github.com/liuxuan30))
- Updated README for 3.0.5 [\#3183](https://github.com/danielgindi/Charts/pull/3183) ([jjatie](https://github.com/jjatie))
- Balloon Marker indicates position of data [\#3181](https://github.com/danielgindi/Charts/pull/3181) ([jjatie](https://github.com/jjatie))
- Fixed a duplicated assignment compared with obj-c code. [\#3179](https://github.com/danielgindi/Charts/pull/3179) ([canapio](https://github.com/canapio))
- Fixed X-Axis Labels Not Showing \(\#3154\) [\#3174](https://github.com/danielgindi/Charts/pull/3174) ([leedsalex](https://github.com/leedsalex))
- fix programatical unhighlighting for BarCharView [\#3159](https://github.com/danielgindi/Charts/pull/3159) ([jekahy](https://github.com/jekahy))
- Removed optionality from valueFormatter where appropriate [\#3106](https://github.com/danielgindi/Charts/pull/3106) ([jjatie](https://github.com/jjatie))
- Moved the default value formatter [\#3088](https://github.com/danielgindi/Charts/pull/3088) ([jjatie](https://github.com/jjatie))
- Utils Cleanup [\#3054](https://github.com/danielgindi/Charts/pull/3054) ([jjatie](https://github.com/jjatie))
- weak -\> unowned [\#3039](https://github.com/danielgindi/Charts/pull/3039) ([jjatie](https://github.com/jjatie))
- Fix BalloonMarker's text position calculation, consider insets [\#3035](https://github.com/danielgindi/Charts/pull/3035) ([yangcaimu](https://github.com/yangcaimu))
- Chartdata collection refactor [\#3024](https://github.com/danielgindi/Charts/pull/3024) ([jjatie](https://github.com/jjatie))
- Chartdata collection conformance [\#3023](https://github.com/danielgindi/Charts/pull/3023) ([jjatie](https://github.com/jjatie))
- Give the users customizable axis label limits \(Fixes \#2085\) [\#2894](https://github.com/danielgindi/Charts/pull/2894) ([igzrobertoestrada](https://github.com/igzrobertoestrada))
- For \#2840. add dataIndex parameter in `highlightValue\(\)` calls [\#2852](https://github.com/danielgindi/Charts/pull/2852) ([liuxuan30](https://github.com/liuxuan30))
- fix \#2356 crash if floor\(10.0 \* intervalMagnitude\) is 0.0 [\#2377](https://github.com/danielgindi/Charts/pull/2377) ([liuxuan30](https://github.com/liuxuan30))
- Fixes the distance issue between the legend and the horizontal bar chart \(Fixes \#2138\) [\#2214](https://github.com/danielgindi/Charts/pull/2214) ([SvenMuc](https://github.com/SvenMuc))

## [3.0.5](https://github.com/danielgindi/Charts/tree/3.0.5) (2018-01-08)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.0.5...3.0.5)

## [v3.0.5](https://github.com/danielgindi/Charts/tree/v3.0.5) (2018-01-08)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.0.4...v3.0.5)

**Implemented enhancements:**

- range, and therefore interval, is NaN in computeAxisValues [\#2845](https://github.com/danielgindi/Charts/issues/2845)
- Crash when calling `func highlightValue\(x: Double, dataSetIndex: Int, callDelegate: Bool\)` [\#2840](https://github.com/danielgindi/Charts/issues/2840)

**Fixed bugs:**

- Combined Chart, when highlight is active, bars hides line [\#3091](https://github.com/danielgindi/Charts/issues/3091)
- A problem of CombinedChartView's YAxis [\#1830](https://github.com/danielgindi/Charts/issues/1830)

**Closed issues:**

- Command /bin/sh failed with exit code 1 [\#3156](https://github.com/danielgindi/Charts/issues/3156)
- Xcode 9 / Swift 4 issue with current master [\#3152](https://github.com/danielgindi/Charts/issues/3152)
- Is it possible to show value on touch next to highlight position? [\#3151](https://github.com/danielgindi/Charts/issues/3151)
- Remove legend [\#3150](https://github.com/danielgindi/Charts/issues/3150)
- Add a shadow to the line chart [\#3148](https://github.com/danielgindi/Charts/issues/3148)
- Missing alternate x-axis labels on line chart [\#3139](https://github.com/danielgindi/Charts/issues/3139)
- Can the histogram set the corner？ [\#3138](https://github.com/danielgindi/Charts/issues/3138)
- Charts demo ipa file request [\#3137](https://github.com/danielgindi/Charts/issues/3137)
- Main Thread Checker: UI API called on background thread. [\#3129](https://github.com/danielgindi/Charts/issues/3129)
- How to get X\_axis touch points coordinates in radar graph [\#3127](https://github.com/danielgindi/Charts/issues/3127)
- Division by zero crash in AxisRendererBase [\#3124](https://github.com/danielgindi/Charts/issues/3124)
- why bar chart non start at zero [\#3123](https://github.com/danielgindi/Charts/issues/3123)
- BarChart - Show X-Axis with Step as 1 [\#3122](https://github.com/danielgindi/Charts/issues/3122)
- Have Line Graph Value Labels Above/Below Independently [\#3120](https://github.com/danielgindi/Charts/issues/3120)
- Corner Radius on Grid Background [\#3119](https://github.com/danielgindi/Charts/issues/3119)
- ChartsDemo doesn't compile [\#3116](https://github.com/danielgindi/Charts/issues/3116)
- Rounded line chart [\#3115](https://github.com/danielgindi/Charts/issues/3115)
- value of barChart covered by title [\#3113](https://github.com/danielgindi/Charts/issues/3113)
- PieChartView Can not highlight which i want to highlighted [\#3110](https://github.com/danielgindi/Charts/issues/3110)
- How to scroll bar chart if max values to show? [\#3101](https://github.com/danielgindi/Charts/issues/3101)
- Is it possible to draw a chart like this? [\#3097](https://github.com/danielgindi/Charts/issues/3097)
- How to disable the rotation option of pie charts in iOS charts? [\#3096](https://github.com/danielgindi/Charts/issues/3096)
- How to disable the rotation option of pie charts in iOS charts? [\#3095](https://github.com/danielgindi/Charts/issues/3095)
- 数据空值处理问题 [\#3094](https://github.com/danielgindi/Charts/issues/3094)
- how to display the integer values on pie chart instead of float? [\#3093](https://github.com/danielgindi/Charts/issues/3093)
- Line Chart point colours depend on value at each point [\#3090](https://github.com/danielgindi/Charts/issues/3090)
- how to cancel highlights when the drag gesture ends [\#3089](https://github.com/danielgindi/Charts/issues/3089)
- why my value is .50 ？Why not 0.5？ How to do? [\#3083](https://github.com/danielgindi/Charts/issues/3083)
- how to use String in YAxis， rather than Double? [\#3082](https://github.com/danielgindi/Charts/issues/3082)
- Scrolling axis [\#3081](https://github.com/danielgindi/Charts/issues/3081)
- Clipping of values in line graph [\#3077](https://github.com/danielgindi/Charts/issues/3077)
- \[LineChart\] Ignore / don't plot first or end value [\#3075](https://github.com/danielgindi/Charts/issues/3075)
- Which attribute control the yellow line，like the picture？ [\#3074](https://github.com/danielgindi/Charts/issues/3074)
- Archive error using swift 4.0 [\#3072](https://github.com/danielgindi/Charts/issues/3072)
- How can I have a chart like this one? [\#3070](https://github.com/danielgindi/Charts/issues/3070)
- Module compiled with Swift 4.0 cannot be imported in Swift 3.2.3 [\#3068](https://github.com/danielgindi/Charts/issues/3068)
- Unable to make chart look like IOS Stock App after trying every mode \(LineChartModeLinear = 0,   LineChartModeStepped = 1,   LineChartModeCubicBezier = 2,   LineChartModeHorizontalBezier = 3\) of LineChartDataSet [\#3066](https://github.com/danielgindi/Charts/issues/3066)
- Cannot Select barChar column [\#3065](https://github.com/danielgindi/Charts/issues/3065)
- Updating data in LineChart takes up to 30 seconds [\#3064](https://github.com/danielgindi/Charts/issues/3064)
- How to sync the scrolling of 2 charts  [\#3063](https://github.com/danielgindi/Charts/issues/3063)
- Line chart with bottom spacing and custom position y-labels at min and max values [\#3059](https://github.com/danielgindi/Charts/issues/3059)
- 'characters' is deprecated: Please use String or Substring directly [\#3057](https://github.com/danielgindi/Charts/issues/3057)
- Pod to OC error [\#3053](https://github.com/danielgindi/Charts/issues/3053)
- How to represent a null value，It's like the picture? not 0 [\#3052](https://github.com/danielgindi/Charts/issues/3052)
- dyld library not loaded @rpath/charts.framework/charts reason : image not found XCode 9.1 MacOS version 10.12.6 [\#3050](https://github.com/danielgindi/Charts/issues/3050)
- Can't change the border width of circle point in line chart. [\#3049](https://github.com/danielgindi/Charts/issues/3049)
- Charts library is not working in Swift 4 Xcode 9 [\#3048](https://github.com/danielgindi/Charts/issues/3048)
- Create an image of the entire chart [\#3037](https://github.com/danielgindi/Charts/issues/3037)
- lineChartView only enables one data set to be with icons [\#3036](https://github.com/danielgindi/Charts/issues/3036)
- Add charts to UIView programatically [\#3033](https://github.com/danielgindi/Charts/issues/3033)
- Only disable animation for adding values [\#3031](https://github.com/danielgindi/Charts/issues/3031)
- BarChartDataSet with only negative values doesn't call stringForValue on formatter [\#3030](https://github.com/danielgindi/Charts/issues/3030)
- Please provide an example for pieradarchart [\#3029](https://github.com/danielgindi/Charts/issues/3029)
- Multiple charts in a collection view [\#3028](https://github.com/danielgindi/Charts/issues/3028)
- Bubbles chart   [\#3027](https://github.com/danielgindi/Charts/issues/3027)
- delete [\#3025](https://github.com/danielgindi/Charts/issues/3025)
- How to change position xAxis Label on the Top Bar Chart ? [\#3022](https://github.com/danielgindi/Charts/issues/3022)
- How to decrease space between chartData ? Is there any parameter ? [\#3021](https://github.com/danielgindi/Charts/issues/3021)
- 已解决 [\#3020](https://github.com/danielgindi/Charts/issues/3020)
- Drawing min mix median lines [\#3017](https://github.com/danielgindi/Charts/issues/3017)
- Installation error through cocapods [\#3016](https://github.com/danielgindi/Charts/issues/3016)
- Swift 4.0.2 not compatible [\#3015](https://github.com/danielgindi/Charts/issues/3015)
- Error NSAttributedStringKey.font on Xcode 9.1 Swift 4 with Charts \(3.0.4\) [\#3014](https://github.com/danielgindi/Charts/issues/3014)
- Double Bar Lines [\#3013](https://github.com/danielgindi/Charts/issues/3013)
- Same zero line for left and right axis [\#3011](https://github.com/danielgindi/Charts/issues/3011)
- How to get version 3.0.2 ? [\#3009](https://github.com/danielgindi/Charts/issues/3009)
- How can I scroll to previous data? [\#3006](https://github.com/danielgindi/Charts/issues/3006)
- Crashing in X-axis render method. [\#3004](https://github.com/danielgindi/Charts/issues/3004)
- How to align yAxis at zero point? [\#2989](https://github.com/danielgindi/Charts/issues/2989)
- Line Chart View not drawn because Date values in xAxis \(not sorted Ascending/Descending\) ? [\#2988](https://github.com/danielgindi/Charts/issues/2988)
- Showing .0, .00 accoding the the decimal places insted of 0 in graphs [\#2987](https://github.com/danielgindi/Charts/issues/2987)
- How to select one of them in the first time。 [\#2985](https://github.com/danielgindi/Charts/issues/2985)
- 当饼状图的数据为0%的时候,左下角有个多余的字母N,问题已解决,有遇到这个问题的请私聊我 [\#2984](https://github.com/danielgindi/Charts/issues/2984)
- Scale to particular x-axis range in line chart [\#2979](https://github.com/danielgindi/Charts/issues/2979)
- ChartViewDelegate [\#2978](https://github.com/danielgindi/Charts/issues/2978)
- What is the compatible version with Xcode 8.3.3 ? [\#2977](https://github.com/danielgindi/Charts/issues/2977)
- ScatterChartDataSet troubles with alignments. [\#2976](https://github.com/danielgindi/Charts/issues/2976)
- Uneven X-Axis values for a line chart [\#2975](https://github.com/danielgindi/Charts/issues/2975)
- Stacked bar chart value label clipped  [\#2974](https://github.com/danielgindi/Charts/issues/2974)
- Pie chart monochrome or gradient fill not support  [\#2973](https://github.com/danielgindi/Charts/issues/2973)
- PieChart:- PieChart not completely show.  [\#2972](https://github.com/danielgindi/Charts/issues/2972)
- Error after updating to Xcode 9.1 [\#2970](https://github.com/danielgindi/Charts/issues/2970)
- Add cornerRadius to vertical highlighter line and draw circle only on highlight [\#2969](https://github.com/danielgindi/Charts/issues/2969)
- How can I don't draw lines when data absent at some point? [\#2968](https://github.com/danielgindi/Charts/issues/2968)
- How to use your file directly to my project without pod installation? [\#2964](https://github.com/danielgindi/Charts/issues/2964)
- Line chart lines are clipped [\#2963](https://github.com/danielgindi/Charts/issues/2963)
- Marker with multiple datasets [\#2962](https://github.com/danielgindi/Charts/issues/2962)
- how do i run it in xcode 9??  [\#2958](https://github.com/danielgindi/Charts/issues/2958)
- Regarding Xcode version [\#2957](https://github.com/danielgindi/Charts/issues/2957)
- Cannot align chart description on left instead of right [\#2956](https://github.com/danielgindi/Charts/issues/2956)
- change color of bar chart programmatically ?  [\#2954](https://github.com/danielgindi/Charts/issues/2954)
- Remove or change line color in chart [\#2953](https://github.com/danielgindi/Charts/issues/2953)
- Show line bar position per seconds  [\#2952](https://github.com/danielgindi/Charts/issues/2952)
- Add Two Custom Marker views on linechart graph [\#2951](https://github.com/danielgindi/Charts/issues/2951)
- Display high and low value in chart but Displayed one only [\#2947](https://github.com/danielgindi/Charts/issues/2947)
- Change LineChartDataSet label color not value color. [\#2944](https://github.com/danielgindi/Charts/issues/2944)
- when dataset value is increases then the graph value is misplace . and i am showing three bar in one group. and also show the  integer value in particular bar above. [\#2938](https://github.com/danielgindi/Charts/issues/2938)
- Errors after conversion to Swift4 NSAttributedStringKey [\#2937](https://github.com/danielgindi/Charts/issues/2937)
- xAxis setLabelsToSkip and valuesObjc want to have replace api [\#2936](https://github.com/danielgindi/Charts/issues/2936)
- xAxis value is started with 0 but it not coordinate with y axis 0 value [\#2935](https://github.com/danielgindi/Charts/issues/2935)
- line charts with many gradients according the value [\#2934](https://github.com/danielgindi/Charts/issues/2934)
- Xcode 9.1 - Characters is deprecated [\#2933](https://github.com/danielgindi/Charts/issues/2933)
- How to add corner radius to balloonmarker [\#2932](https://github.com/danielgindi/Charts/issues/2932)
- leftAxis and rightAxis didn't show up [\#2931](https://github.com/danielgindi/Charts/issues/2931)
- Errors when importing BalloonMarker [\#2928](https://github.com/danielgindi/Charts/issues/2928)
- Bar chart does not start at zero. [\#2927](https://github.com/danielgindi/Charts/issues/2927)
- The following binaries use incompatible versions of Swift [\#2926](https://github.com/danielgindi/Charts/issues/2926)
-  Change the color of a specific X-Axis label and the corresponding data value in a line chart using Charts framework [\#2925](https://github.com/danielgindi/Charts/issues/2925)
- Adding corner radius on top of Bar [\#2924](https://github.com/danielgindi/Charts/issues/2924)
- I want to change colour of the label text on the x-axis where it's highlighted. [\#2923](https://github.com/danielgindi/Charts/issues/2923)
- How to give Gradient colour for limit line in linechartviewDataSet  [\#2922](https://github.com/danielgindi/Charts/issues/2922)
- Gradient colour for stroke line horizantally .It is possible in  android  [\#2921](https://github.com/danielgindi/Charts/issues/2921)
- y axis range not automatically recalculated when adding entry to data set [\#2920](https://github.com/danielgindi/Charts/issues/2920)
- Build framework from project [\#2919](https://github.com/danielgindi/Charts/issues/2919)
- How to draw a diagram above the axis labels? [\#2918](https://github.com/danielgindi/Charts/issues/2918)
- - [\#2917](https://github.com/danielgindi/Charts/issues/2917)
- How to use IValueFormatter in stacked bar chart [\#2916](https://github.com/danielgindi/Charts/issues/2916)
- Use of unresolved identifier NSAttributedStringKey in Xcode 8.3.2 for swift 3 [\#2915](https://github.com/danielgindi/Charts/issues/2915)
- Barchart with rounded bar style [\#2912](https://github.com/danielgindi/Charts/issues/2912)
- Animate adding single point [\#2911](https://github.com/danielgindi/Charts/issues/2911)
- Crash [\#2910](https://github.com/danielgindi/Charts/issues/2910)
- Multi Window [\#2909](https://github.com/danielgindi/Charts/issues/2909)
- how can I unhighlight any slice selected before in PieChart ? [\#2908](https://github.com/danielgindi/Charts/issues/2908)
- Barchart DefaultValueFormatter dataSetIndex always 0 [\#2907](https://github.com/danielgindi/Charts/issues/2907)
- LineChartDataSet Multi color [\#2906](https://github.com/danielgindi/Charts/issues/2906)
- How to disconnect data points?  [\#2905](https://github.com/danielgindi/Charts/issues/2905)
- Licensing question [\#2904](https://github.com/danielgindi/Charts/issues/2904)
- A way to add values to labels on x axis? [\#2903](https://github.com/danielgindi/Charts/issues/2903)
- Update cocoapods.org version [\#2902](https://github.com/danielgindi/Charts/issues/2902)
- noDataText for PieChartView isn't working. [\#2901](https://github.com/danielgindi/Charts/issues/2901)
- xcode9 alert swift question [\#2900](https://github.com/danielgindi/Charts/issues/2900)
- Dynamically set the bar width [\#2898](https://github.com/danielgindi/Charts/issues/2898)
- Marker action [\#2897](https://github.com/danielgindi/Charts/issues/2897)
- Filler line for acceptable range of values in LineChart [\#2896](https://github.com/danielgindi/Charts/issues/2896)
- Pie chart full view [\#2895](https://github.com/danielgindi/Charts/issues/2895)
- Problem with BalloonMarker in lineChart [\#2892](https://github.com/danielgindi/Charts/issues/2892)
- OS X Bar chart Demo highlight and stacked color are inconsistent [\#2890](https://github.com/danielgindi/Charts/issues/2890)
- Problem with charts in UITableView cells [\#2888](https://github.com/danielgindi/Charts/issues/2888)
- drawing xAxis ratio values [\#2885](https://github.com/danielgindi/Charts/issues/2885)
- Xcode 9 compatibility [\#2883](https://github.com/danielgindi/Charts/issues/2883)
- Large Whitespace Between X-Axis Labels and Legend [\#2882](https://github.com/danielgindi/Charts/issues/2882)
- help about performance issues about line charts [\#2881](https://github.com/danielgindi/Charts/issues/2881)
- Animation on PieChartView [\#2880](https://github.com/danielgindi/Charts/issues/2880)
- Scrolling chart after setting lineChart.xAxis.axisMaximum = someValue [\#2879](https://github.com/danielgindi/Charts/issues/2879)
- "chartValueSelected" is not getting called in customView [\#2878](https://github.com/danielgindi/Charts/issues/2878)
- curve line [\#2877](https://github.com/danielgindi/Charts/issues/2877)
- Swift 4 issue [\#2876](https://github.com/danielgindi/Charts/issues/2876)
- 不显示百分号了 [\#2873](https://github.com/danielgindi/Charts/issues/2873)
- LineChartView: Using two different colors in one dataset [\#2871](https://github.com/danielgindi/Charts/issues/2871)
- xcode 9.0 errors after installing pod 'Charts' [\#2869](https://github.com/danielgindi/Charts/issues/2869)
- Change scroll direction HorizontalBarChartView x-axis from top to bottom chart [\#2868](https://github.com/danielgindi/Charts/issues/2868)
- Set names of X-axis and Y-axis itself \(not their labels\) [\#2867](https://github.com/danielgindi/Charts/issues/2867)
- 'characters' is deprecated: Please use String or Substring directly [\#2866](https://github.com/danielgindi/Charts/issues/2866)
- Syntax error in pod [\#2865](https://github.com/danielgindi/Charts/issues/2865)
- Filled Line Chart [\#2863](https://github.com/danielgindi/Charts/issues/2863)
- How can I highlight a specific slice of the PieChart? [\#2862](https://github.com/danielgindi/Charts/issues/2862)
- How can I make the chart rotate completely, 360, 720 on a button click ? [\#2861](https://github.com/danielgindi/Charts/issues/2861)
- How can I make the chart rotate completely, 360, 720 on a button click ? [\#2860](https://github.com/danielgindi/Charts/issues/2860)
- xcode9 pod install have a problem [\#2856](https://github.com/danielgindi/Charts/issues/2856)
- can I implement stacked bar with rounded corners ? [\#2855](https://github.com/danielgindi/Charts/issues/2855)
- Updating Charts to 3.0.4 and Swift 4 doesn't work [\#2851](https://github.com/danielgindi/Charts/issues/2851)
- How to add string labels to XAxis horizontal bar in Charts framework\(swift 3\) [\#2850](https://github.com/danielgindi/Charts/issues/2850)
- How to show off numbers in HorizontalBar [\#2849](https://github.com/danielgindi/Charts/issues/2849)
- In Multiple bar chart Unable to set Values more than 4 bar in under given year.. [\#2847](https://github.com/danielgindi/Charts/issues/2847)
- Type 'NSAttributedStringKey' \(aka 'NSString'\) has no member 'font' [\#2844](https://github.com/danielgindi/Charts/issues/2844)
- Install charts via cocapods pulls back old version 3.0.2 [\#2843](https://github.com/danielgindi/Charts/issues/2843)
- xcode 9.0 compile ERROR: "static var 'defaultFormatter' is not public" [\#2842](https://github.com/danielgindi/Charts/issues/2842)
- Pass String to LineChartData [\#2841](https://github.com/danielgindi/Charts/issues/2841)
- Customised graph with ranges\(Normal-blue and Warning-yellow\) also with it's legend [\#2838](https://github.com/danielgindi/Charts/issues/2838)
- using UIGraphicsGetCurrentContext in MarkerImage subclass [\#2835](https://github.com/danielgindi/Charts/issues/2835)
- How to show BarChartDataSet with descending x values? [\#2834](https://github.com/danielgindi/Charts/issues/2834)
- getTransformer function doesn't work properly when AxisDependency is set to right [\#2833](https://github.com/danielgindi/Charts/issues/2833)
- Grouped Bar chart X-axis and Bar Alignment [\#2832](https://github.com/danielgindi/Charts/issues/2832)
- How to create a custom Marker from Xib - Swift [\#2831](https://github.com/danielgindi/Charts/issues/2831)
- does not support new Objective-C project with Xcode 9 [\#2830](https://github.com/danielgindi/Charts/issues/2830)
- radarChart default labelCount [\#2829](https://github.com/danielgindi/Charts/issues/2829)
- NSDecimalNumber instead of Double [\#2828](https://github.com/danielgindi/Charts/issues/2828)
- LineChart 填充 [\#2825](https://github.com/danielgindi/Charts/issues/2825)
- Swift: Different colours for circles in one LineChartDataSet [\#2824](https://github.com/danielgindi/Charts/issues/2824)
- Unable to update Chart library  [\#2823](https://github.com/danielgindi/Charts/issues/2823)
- xMin, xMax, yMin, yMax not recalculated to zero on .clear\(\) [\#2822](https://github.com/danielgindi/Charts/issues/2822)
- OS X errors with swift 4  [\#2819](https://github.com/danielgindi/Charts/issues/2819)
- I want to add marker image from line chart in swift 4.0 [\#2818](https://github.com/danielgindi/Charts/issues/2818)
- ChartMarkerView not able to subclass of swift class  [\#2817](https://github.com/danielgindi/Charts/issues/2817)
- Type 'NSAttributedStringKey' \(aka 'NSString'\) has no member 'font' [\#2816](https://github.com/danielgindi/Charts/issues/2816)
- StackedBarChart isn't stacking bars [\#2815](https://github.com/danielgindi/Charts/issues/2815)
- Cocoapod 3.0.4 [\#2814](https://github.com/danielgindi/Charts/issues/2814)
- Error aplication Thread in ios 11 with version 3.0.4 [\#2812](https://github.com/danielgindi/Charts/issues/2812)
- Cannot compile with version 3.0.3 and Xcode 9 due to incompatible Swift version [\#2810](https://github.com/danielgindi/Charts/issues/2810)
- I have the version 3.0.4 but when compile project I has an error [\#2809](https://github.com/danielgindi/Charts/issues/2809)
- 拖动的时候 会强烈抖动 [\#2808](https://github.com/danielgindi/Charts/issues/2808)
- Version 3.0.4 doesn't appear via Cocoapods [\#2807](https://github.com/danielgindi/Charts/issues/2807)
- Bump Pod Version [\#2805](https://github.com/danielgindi/Charts/issues/2805)
- module 'Charts' not found when dray it into target [\#2803](https://github.com/danielgindi/Charts/issues/2803)
- How to get y-axis values "Int" to "Double" [\#2799](https://github.com/danielgindi/Charts/issues/2799)
- The line chart enlarged is crash [\#2797](https://github.com/danielgindi/Charts/issues/2797)
- How to change the entrylabel\(i.e, 90%\) color in PieChart [\#2794](https://github.com/danielgindi/Charts/issues/2794)
- Chart Grid Lines Don't Always Draw [\#2791](https://github.com/danielgindi/Charts/issues/2791)
- Charts.framework: No such file or directory [\#2789](https://github.com/danielgindi/Charts/issues/2789)
- Memory Leak in Horizontal Bar Chart [\#2745](https://github.com/danielgindi/Charts/issues/2745)
- axisDependency does not work [\#2258](https://github.com/danielgindi/Charts/issues/2258)
- HorizontalBarChart does not update labels correctly [\#2257](https://github.com/danielgindi/Charts/issues/2257)

**Merged pull requests:**

- Syncing 4.0.0 with master [\#3160](https://github.com/danielgindi/Charts/pull/3160) ([jjatie](https://github.com/jjatie))
- Subclassing of LegendRenderer didn't take any effect [\#3149](https://github.com/danielgindi/Charts/pull/3149) ([l-lemesev](https://github.com/l-lemesev))
- Update ViewPortHandler.swift [\#3143](https://github.com/danielgindi/Charts/pull/3143) ([ParkinWu](https://github.com/ParkinWu))
- Renderer protocols [\#3136](https://github.com/danielgindi/Charts/pull/3136) ([jjatie](https://github.com/jjatie))
- Update 4.0.0 with master [\#3135](https://github.com/danielgindi/Charts/pull/3135) ([jjatie](https://github.com/jjatie))
- Fix axis label disappear when zooming in deep enough [\#3132](https://github.com/danielgindi/Charts/pull/3132) ([liuxuan30](https://github.com/liuxuan30))
- Updating 4.0.0 with latest changes in master [\#3130](https://github.com/danielgindi/Charts/pull/3130) ([jjatie](https://github.com/jjatie))
- add option to build demo projects unit tests on iOS [\#3121](https://github.com/danielgindi/Charts/pull/3121) ([liuxuan30](https://github.com/liuxuan30))
- Makes ChartsDemo compiling again [\#3117](https://github.com/danielgindi/Charts/pull/3117) ([valeriyvan](https://github.com/valeriyvan))
- Fixed using wrong axis \(Issue \#2257\) [\#3114](https://github.com/danielgindi/Charts/pull/3114) ([defranke](https://github.com/defranke))
- for \#3061 fix animation crash [\#3098](https://github.com/danielgindi/Charts/pull/3098) ([liuxuan30](https://github.com/liuxuan30))
- Refactored ChartUtils method into CGPoint extension [\#3087](https://github.com/danielgindi/Charts/pull/3087) ([jjatie](https://github.com/jjatie))
- Moved ChartUtils drawing methods into CGContext extension [\#3086](https://github.com/danielgindi/Charts/pull/3086) ([jjatie](https://github.com/jjatie))
- for \#2745. chart should be weak. [\#3078](https://github.com/danielgindi/Charts/pull/3078) ([liuxuan30](https://github.com/liuxuan30))
- Fix a bug may cause infinite loop. [\#3073](https://github.com/danielgindi/Charts/pull/3073) ([JyHu](https://github.com/JyHu))
- Chartviewbase redundant ivar [\#3045](https://github.com/danielgindi/Charts/pull/3045) ([jjatie](https://github.com/jjatie))
- Removed `isKind\(of:\)` [\#3044](https://github.com/danielgindi/Charts/pull/3044) ([jjatie](https://github.com/jjatie))
- Removed redundant ivars in BarLineChartViewBase [\#3043](https://github.com/danielgindi/Charts/pull/3043) ([jjatie](https://github.com/jjatie))
- fileprivate -\> private [\#3042](https://github.com/danielgindi/Charts/pull/3042) ([jjatie](https://github.com/jjatie))
- Viewportjob minor cleanup [\#3041](https://github.com/danielgindi/Charts/pull/3041) ([jjatie](https://github.com/jjatie))
- Removed @objc from internal properties [\#3038](https://github.com/danielgindi/Charts/pull/3038) ([jjatie](https://github.com/jjatie))
- Minor changes to BubbleChartRenderer logic [\#3010](https://github.com/danielgindi/Charts/pull/3010) ([jjatie](https://github.com/jjatie))
- BarChartRenderer Logic cleanup [\#3008](https://github.com/danielgindi/Charts/pull/3008) ([jjatie](https://github.com/jjatie))
- Minor changes to Animator [\#3005](https://github.com/danielgindi/Charts/pull/3005) ([jjatie](https://github.com/jjatie))
- Minor cleanup to Highlighter types [\#3003](https://github.com/danielgindi/Charts/pull/3003) ([jjatie](https://github.com/jjatie))
- Resubmit of \#2730 [\#3002](https://github.com/danielgindi/Charts/pull/3002) ([jjatie](https://github.com/jjatie))
- The backing var is not necessary. [\#3000](https://github.com/danielgindi/Charts/pull/3000) ([jjatie](https://github.com/jjatie))
- Minor refactoring of Formatter logic [\#2998](https://github.com/danielgindi/Charts/pull/2998) ([jjatie](https://github.com/jjatie))
- Remove java interface convention [\#2997](https://github.com/danielgindi/Charts/pull/2997) ([jjatie](https://github.com/jjatie))
- Removed methods and properties deprecated in 1.0 [\#2996](https://github.com/danielgindi/Charts/pull/2996) ([jjatie](https://github.com/jjatie))
- Replaced `ChartUtils` methods with `CGSize` extensions [\#2995](https://github.com/danielgindi/Charts/pull/2995) ([jjatie](https://github.com/jjatie))
- Replaced relevant `ChartUtils` methods with `Double` extensions [\#2994](https://github.com/danielgindi/Charts/pull/2994) ([jjatie](https://github.com/jjatie))
- Replaced `ChartUtils.Math` in favour of an extension on `FloatingPoint` [\#2993](https://github.com/danielgindi/Charts/pull/2993) ([jjatie](https://github.com/jjatie))
- Minor changes to logic in `ViewPortJob` subclasses. [\#2992](https://github.com/danielgindi/Charts/pull/2992) ([jjatie](https://github.com/jjatie))
- `ChartRenderer`'s must be initialized with a chart [\#2982](https://github.com/danielgindi/Charts/pull/2982) ([jjatie](https://github.com/jjatie))
- Animator non nil [\#2981](https://github.com/danielgindi/Charts/pull/2981) ([jjatie](https://github.com/jjatie))
- View port handler nonnil [\#2980](https://github.com/danielgindi/Charts/pull/2980) ([jjatie](https://github.com/jjatie))
- Add support for iPhone X [\#2967](https://github.com/danielgindi/Charts/pull/2967) ([liuxuan30](https://github.com/liuxuan30))
- added highlightColor parameter for pie charts [\#2961](https://github.com/danielgindi/Charts/pull/2961) ([pascalherrmann](https://github.com/pascalherrmann))
- Add Swift Package Manager support. [\#2950](https://github.com/danielgindi/Charts/pull/2950) ([BrianDoig](https://github.com/BrianDoig))
- Fix turning off drag in X and Y axes separately. [\#2949](https://github.com/danielgindi/Charts/pull/2949) ([maciejtrybilo](https://github.com/maciejtrybilo))
- modify for Character Alert: characters is deprecated [\#2942](https://github.com/danielgindi/Charts/pull/2942) ([suzuhiroruri](https://github.com/suzuhiroruri))
- fix \#2890. Turned out it's multiple bar chart but not grouped [\#2891](https://github.com/danielgindi/Charts/pull/2891) ([liuxuan30](https://github.com/liuxuan30))
- Update LICENSE [\#2887](https://github.com/danielgindi/Charts/pull/2887) ([sDaniel](https://github.com/sDaniel))
- fix \#1830. credit from https://github.com/danielgindi/Charts/pull/2049 [\#2874](https://github.com/danielgindi/Charts/pull/2874) ([liuxuan30](https://github.com/liuxuan30))
- duplicated code for set1 in set2 section [\#2872](https://github.com/danielgindi/Charts/pull/2872) ([liuxuan30](https://github.com/liuxuan30))
- added DataApproximator+N extension [\#2848](https://github.com/danielgindi/Charts/pull/2848) ([666tos](https://github.com/666tos))
- Bumped pod version [\#2806](https://github.com/danielgindi/Charts/pull/2806) ([mohpor](https://github.com/mohpor))
- unwrap optionals [\#2698](https://github.com/danielgindi/Charts/pull/2698) ([russellbstephens](https://github.com/russellbstephens))
- Replaced unnecessary NSObjectProtocol [\#2629](https://github.com/danielgindi/Charts/pull/2629) ([jjatie](https://github.com/jjatie))
- Swift iOS Demos [\#2628](https://github.com/danielgindi/Charts/pull/2628) ([jjatie](https://github.com/jjatie))
- add example playground [\#2364](https://github.com/danielgindi/Charts/pull/2364) ([thierryH91200](https://github.com/thierryH91200))
- Compatibility with swift playgrounds [\#2335](https://github.com/danielgindi/Charts/pull/2335) ([macteo](https://github.com/macteo))

## [v3.0.4](https://github.com/danielgindi/Charts/tree/v3.0.4) (2017-09-21)
[Full Changelog](https://github.com/danielgindi/Charts/compare/3.0.4...v3.0.4)

## [3.0.4](https://github.com/danielgindi/Charts/tree/3.0.4) (2017-09-21)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.0.3...3.0.4)

**Closed issues:**

- cocoaPod can not install branch of swift4 [\#2802](https://github.com/danielgindi/Charts/issues/2802)
- Swift 4 [\#2801](https://github.com/danielgindi/Charts/issues/2801)
- how tomake leftAxis.inverted and LineChartDataSet.fillColor not inverted [\#2798](https://github.com/danielgindi/Charts/issues/2798)
- Bar Chart Demo One Label One Color [\#2792](https://github.com/danielgindi/Charts/issues/2792)
- BarChart draw vertical dotted lines on specific bars [\#2788](https://github.com/danielgindi/Charts/issues/2788)
- How can I control the number of decimals? [\#2787](https://github.com/danielgindi/Charts/issues/2787)
- Charts “3.0.2” cannot find "BalloonMarker"  [\#2786](https://github.com/danielgindi/Charts/issues/2786)
- Graphs gets cut from both ends [\#2785](https://github.com/danielgindi/Charts/issues/2785)
- Is it possible to draw multiple lineCharts with different starting points on same view? [\#2783](https://github.com/danielgindi/Charts/issues/2783)
- getFormattedLabel Index Out of Range [\#2782](https://github.com/danielgindi/Charts/issues/2782)
- Line chart right axis inset [\#2781](https://github.com/danielgindi/Charts/issues/2781)
- No type or protocol named 'IChartAxisValueFormatter' [\#2780](https://github.com/danielgindi/Charts/issues/2780)
- Zero Value is under BarChart [\#2779](https://github.com/danielgindi/Charts/issues/2779)
- App crashes while using Charts swift library in Objective C Project [\#2778](https://github.com/danielgindi/Charts/issues/2778)
- Cannot subscript a value of type '\[String : AnyObject\]' with an index of type 'NSAttributedStringKey' [\#2777](https://github.com/danielgindi/Charts/issues/2777)
- How to drag data entries to change their y value in real time.  [\#2776](https://github.com/danielgindi/Charts/issues/2776)
- How to break line the legends ?  [\#2775](https://github.com/danielgindi/Charts/issues/2775)
- How can I get current viewport shown x range and scale value [\#2770](https://github.com/danielgindi/Charts/issues/2770)
- How to disable scale of left YAxis? [\#2748](https://github.com/danielgindi/Charts/issues/2748)
- Bar Chart remove x if y is 0 [\#2747](https://github.com/danielgindi/Charts/issues/2747)
- How to display Negative X-Axis on LineChart [\#797](https://github.com/danielgindi/Charts/issues/797)

**Merged pull requests:**

- Changes for Swift 4 [\#2507](https://github.com/danielgindi/Charts/pull/2507) ([liuxuan30](https://github.com/liuxuan30))

## [v3.0.3](https://github.com/danielgindi/Charts/tree/v3.0.3) (2017-09-08)
[Full Changelog](https://github.com/danielgindi/Charts/compare/3.0.3...v3.0.3)

## [3.0.3](https://github.com/danielgindi/Charts/tree/3.0.3) (2017-09-08)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.0.2...3.0.3)

**Implemented enhancements:**

- fatal error: Index out of range [\#2109](https://github.com/danielgindi/Charts/issues/2109)

**Fixed bugs:**

- Crash in YAxisRendererRadarChart [\#2356](https://github.com/danielgindi/Charts/issues/2356)
- Fatal error when providing multiple data sets in PieChartData [\#2099](https://github.com/danielgindi/Charts/issues/2099)

**Closed issues:**

- X Values Hidden On Bar Graph [\#2774](https://github.com/danielgindi/Charts/issues/2774)
- How to add new real-time data to lineChart ? [\#2773](https://github.com/danielgindi/Charts/issues/2773)
- Avoid xAxis limit-labels display in a one-bar BarChart. [\#2771](https://github.com/danielgindi/Charts/issues/2771)
- zoom at particular position via code [\#2769](https://github.com/danielgindi/Charts/issues/2769)
- how to use HorizontalBarChart with decimal number as xAxis  [\#2768](https://github.com/danielgindi/Charts/issues/2768)
- Cannot rescale Y-axis even if changing axisMinimum and axisMaximum [\#2766](https://github.com/danielgindi/Charts/issues/2766)
- I want to change ValueFormat of BarChartDataSet. I can't change it. [\#2764](https://github.com/danielgindi/Charts/issues/2764)
- Changing x-label formats from one line to word-wrapped is awkward [\#2763](https://github.com/danielgindi/Charts/issues/2763)
- chartValueSelected not getting value from Max Value [\#2762](https://github.com/danielgindi/Charts/issues/2762)
- Dual YAxis  [\#2761](https://github.com/danielgindi/Charts/issues/2761)
- How to set the position of the values top on BarChartView?  [\#2759](https://github.com/danielgindi/Charts/issues/2759)
- Line chart xAxis cut left or right in iphone 5 [\#2758](https://github.com/danielgindi/Charts/issues/2758)
- TableView does not scroll vertically after graph is rendered to cells [\#2757](https://github.com/danielgindi/Charts/issues/2757)
- Fill the color between two line charts using IFillFormatter [\#2756](https://github.com/danielgindi/Charts/issues/2756)
- How to use custom view for labels of xAxis? [\#2755](https://github.com/danielgindi/Charts/issues/2755)
- Chart not consistently showing data [\#2753](https://github.com/danielgindi/Charts/issues/2753)
- Obtaining current number of points being rendered in zoomed viewing window [\#2752](https://github.com/danielgindi/Charts/issues/2752)
- Barchart starting above X-Axis [\#2751](https://github.com/danielgindi/Charts/issues/2751)
- Getting the position of Data array of entry: ChartDataEntry via chartValueSelected\(args\) method [\#2750](https://github.com/danielgindi/Charts/issues/2750)
- Create Simple Bar Chart [\#2749](https://github.com/danielgindi/Charts/issues/2749)
- iOS8 simulator crashed iOS 8模拟器崩溃在画Y轴线的地方 [\#2746](https://github.com/danielgindi/Charts/issues/2746)
- Chart displays values, but no bars [\#2744](https://github.com/danielgindi/Charts/issues/2744)
- Can not inherit Charts to subclass some cutomize charts [\#2743](https://github.com/danielgindi/Charts/issues/2743)
- CandleStick fill colors not showing [\#2742](https://github.com/danielgindi/Charts/issues/2742)
- Bar chart click on outside of bar calling delegate  [\#2741](https://github.com/danielgindi/Charts/issues/2741)
- Resize chart after adding limit line [\#2739](https://github.com/danielgindi/Charts/issues/2739)
- Pie chart entry icon get cut [\#2738](https://github.com/danielgindi/Charts/issues/2738)
- How can I set ValuePosition for individual slices? [\#2736](https://github.com/danielgindi/Charts/issues/2736)
- You can't slide along the X-axis [\#2735](https://github.com/danielgindi/Charts/issues/2735)
- Candlechart zooming disable interaction with viewcontroller [\#2734](https://github.com/danielgindi/Charts/issues/2734)
- 2 datasets, one should only display one label at the top [\#2733](https://github.com/danielgindi/Charts/issues/2733)
- How do I change the color of the text on top of the bars? [\#2732](https://github.com/danielgindi/Charts/issues/2732)
- Can't subclass BarChartRenderer due to 'fileprivate' status of many properties [\#2731](https://github.com/danielgindi/Charts/issues/2731)
- Left axis axisMinimum inside a TableviewCell does not work [\#2729](https://github.com/danielgindi/Charts/issues/2729)
- How to set minimum displayed Y axis value [\#2728](https://github.com/danielgindi/Charts/issues/2728)
- Custom Text Axis X Horizontal Bar Chart [\#2726](https://github.com/danielgindi/Charts/issues/2726)
- Crash when use in Notification Content Extension [\#2725](https://github.com/danielgindi/Charts/issues/2725)
- Problems encountered with charts [\#2724](https://github.com/danielgindi/Charts/issues/2724)
- Can I use Charts in Swift 4? [\#2723](https://github.com/danielgindi/Charts/issues/2723)
- Add gradient to bar chart [\#2722](https://github.com/danielgindi/Charts/issues/2722)
- Draw bar chart from axisMinimum instead of 0 [\#2721](https://github.com/danielgindi/Charts/issues/2721)
- Different Charts-Swifg.h when simulator and iphone [\#2720](https://github.com/danielgindi/Charts/issues/2720)
- Viewport transX jumping when zooming Y scale \(dynamic/realtime data\) [\#2719](https://github.com/danielgindi/Charts/issues/2719)
- Align X labels with Charts [\#2718](https://github.com/danielgindi/Charts/issues/2718)
- iOS swift3 Chart xaxis duplicate value issue for linechartview [\#2715](https://github.com/danielgindi/Charts/issues/2715)
- Label Position xAxis Radar Chart [\#2714](https://github.com/danielgindi/Charts/issues/2714)
- Showing actual value IN ADDITION to percentage, in a pie slice? [\#2713](https://github.com/danielgindi/Charts/issues/2713)
- Fail to display any thing in line chart. [\#2712](https://github.com/danielgindi/Charts/issues/2712)
- How to decrees column width size in LineChart [\#2710](https://github.com/danielgindi/Charts/issues/2710)
- Runtime EXC\_BAD\_ACCESS with barchart whiles zoom in  [\#2709](https://github.com/danielgindi/Charts/issues/2709)
- How to format values on barchart [\#2708](https://github.com/danielgindi/Charts/issues/2708)
- where do I find ioscharts.framework? [\#2707](https://github.com/danielgindi/Charts/issues/2707)
- Add horizontal line at certain value on Y-Axis? [\#2706](https://github.com/danielgindi/Charts/issues/2706)
- BarChart does not render with multiple data sets [\#2705](https://github.com/danielgindi/Charts/issues/2705)
- Balloon Marker: making it work in line chart inside combined view [\#2703](https://github.com/danielgindi/Charts/issues/2703)
- IValueFormatter text showing More than one Time  [\#2700](https://github.com/danielgindi/Charts/issues/2700)
- Disable bar selection or highlight? [\#2699](https://github.com/danielgindi/Charts/issues/2699)
- Increase Yaxis Labes According to the max values. [\#2696](https://github.com/danielgindi/Charts/issues/2696)
- xAxis scrolling in combineChartView [\#2695](https://github.com/danielgindi/Charts/issues/2695)
- How to highlight at specific entry ? [\#2693](https://github.com/danielgindi/Charts/issues/2693)
- Horizontal bar charts 'Y' values are getting overlapped with '0' [\#2691](https://github.com/danielgindi/Charts/issues/2691)
- Two ChartViews own the same Xaxis [\#2690](https://github.com/danielgindi/Charts/issues/2690)
- issues about BarChartView [\#2689](https://github.com/danielgindi/Charts/issues/2689)
- Tap Gesture Handler callback method [\#2686](https://github.com/danielgindi/Charts/issues/2686)
- How to draw dashed circles for values? [\#2685](https://github.com/danielgindi/Charts/issues/2685)
- How to Redraw the charts? [\#2684](https://github.com/danielgindi/Charts/issues/2684)
- BarChartView top border not showing [\#2682](https://github.com/danielgindi/Charts/issues/2682)
- Drawing horizontal dashed lines except on x-axis line? [\#2681](https://github.com/danielgindi/Charts/issues/2681)
- X-Axis Date values not displaying properly? [\#2680](https://github.com/danielgindi/Charts/issues/2680)
- Dashed border on highlighted bar - BarChart [\#2675](https://github.com/danielgindi/Charts/issues/2675)
- ChartFill - I would like to fill above the line [\#2674](https://github.com/danielgindi/Charts/issues/2674)
- Anyone have iOS charts working with Xcode 9 Beta? [\#2673](https://github.com/danielgindi/Charts/issues/2673)
- Change Scale on Y Axis for CombinedChart [\#2672](https://github.com/danielgindi/Charts/issues/2672)
- Y-axis with text [\#2671](https://github.com/danielgindi/Charts/issues/2671)
- Show only 2 labels on xAxis in CombinedChartView [\#2670](https://github.com/danielgindi/Charts/issues/2670)
- Graphing just a part of the data [\#2669](https://github.com/danielgindi/Charts/issues/2669)
- How to add texts/image into a chart? [\#2667](https://github.com/danielgindi/Charts/issues/2667)
- Is it possible to scroll without Zooming/ Scaling ? [\#2666](https://github.com/danielgindi/Charts/issues/2666)
- Use Charts in Swift3,Error [\#2664](https://github.com/danielgindi/Charts/issues/2664)
- Custom highlighter/marker [\#2663](https://github.com/danielgindi/Charts/issues/2663)
- Display programatically a marker on a chart [\#2662](https://github.com/danielgindi/Charts/issues/2662)
- Scrollview inside Marker [\#2661](https://github.com/danielgindi/Charts/issues/2661)
- Bitcode Problem [\#2660](https://github.com/danielgindi/Charts/issues/2660)
- Fix Marker on center even when chart is scaled or dragged [\#2659](https://github.com/danielgindi/Charts/issues/2659)
- How to add marker to center position in ios charts while scrolling horizontally with increasing scaleX and enabling drag [\#2658](https://github.com/danielgindi/Charts/issues/2658)
- Y Axis scales display format [\#2657](https://github.com/danielgindi/Charts/issues/2657)
- Marker and long press gesture... [\#2656](https://github.com/danielgindi/Charts/issues/2656)
- Pie chart Crash [\#2655](https://github.com/danielgindi/Charts/issues/2655)
- YAxisValues:"Zero" of the integer part is not displayed when the YAxis coordinate is zero [\#2653](https://github.com/danielgindi/Charts/issues/2653)
- Memory leak when valueFormatter is set [\#2652](https://github.com/danielgindi/Charts/issues/2652)
- autoScaleMinMaxEnabled, axisMaximum and axisMinimum [\#2651](https://github.com/danielgindi/Charts/issues/2651)
- Draw X-Axis labels with dates keeping index of x-axis independent like before [\#2648](https://github.com/danielgindi/Charts/issues/2648)
- About ChartHighLight [\#2647](https://github.com/danielgindi/Charts/issues/2647)
- Start value from X-Axis [\#2646](https://github.com/danielgindi/Charts/issues/2646)
- Combined chart with horizontal bar and bubble  [\#2644](https://github.com/danielgindi/Charts/issues/2644)
- I have a problem with compiling [\#2643](https://github.com/danielgindi/Charts/issues/2643)
- How to implement custom dataset in objective-c? [\#2639](https://github.com/danielgindi/Charts/issues/2639)
- I am not able to set values on the bar in simple bar chart. [\#2638](https://github.com/danielgindi/Charts/issues/2638)
- Swift based Charts library increased application size when used for objective c based project [\#2637](https://github.com/danielgindi/Charts/issues/2637)
- 使用CombinedChartView绘制柱形图，如何只让当前bar视图显示灰色背影。 [\#2636](https://github.com/danielgindi/Charts/issues/2636)
- OHLC charts... [\#2635](https://github.com/danielgindi/Charts/issues/2635)
- unable to set string values on x-axis in grouped bar chart . [\#2632](https://github.com/danielgindi/Charts/issues/2632)
- How to set group width in multiple bar chart? [\#2631](https://github.com/danielgindi/Charts/issues/2631)
- override renderLegend\(context: CGContext\) [\#2630](https://github.com/danielgindi/Charts/issues/2630)
- Changing chart's type without creating a new object... [\#2627](https://github.com/danielgindi/Charts/issues/2627)
- Using Charts in NSStackView [\#2626](https://github.com/danielgindi/Charts/issues/2626)
- Does PieChart support multiple columns of vertical legend? [\#2625](https://github.com/danielgindi/Charts/issues/2625)
- I need this method: setShowOnlyMinMax [\#2624](https://github.com/danielgindi/Charts/issues/2624)
- Unable to use this library in Objective-c . [\#2623](https://github.com/danielgindi/Charts/issues/2623)
- 计算柱形图的宽度和设定间隙 [\#2622](https://github.com/danielgindi/Charts/issues/2622)
- How to adjust RadarChartView text from the bottom of the height？ [\#2621](https://github.com/danielgindi/Charts/issues/2621)
- How to give all pie-slices a rounded edge? [\#2620](https://github.com/danielgindi/Charts/issues/2620)
- Alternative color to fill in radar chart  [\#2617](https://github.com/danielgindi/Charts/issues/2617)
- About gradient color for lineChartView [\#2615](https://github.com/danielgindi/Charts/issues/2615)
- Invalid bundle  [\#2614](https://github.com/danielgindi/Charts/issues/2614)
- Want to display circles at data points on a line graph only if the data has changed or is about to change [\#2613](https://github.com/danielgindi/Charts/issues/2613)
- Swift language Version.  [\#2612](https://github.com/danielgindi/Charts/issues/2612)
- Linker Command Fail error  [\#2611](https://github.com/danielgindi/Charts/issues/2611)
- xAxis's first lable always on the left [\#2610](https://github.com/danielgindi/Charts/issues/2610)
- when i  Creat one LineChartData objc. it's draws Three points  [\#2609](https://github.com/danielgindi/Charts/issues/2609)
- i wanna display Char form with no data. but with Y Values.  [\#2608](https://github.com/danielgindi/Charts/issues/2608)
- Is there any way to get the xaxis value when chart scroll end specific area? [\#2607](https://github.com/danielgindi/Charts/issues/2607)
- Memory Leak in Legend and ArrayBufferProtocol [\#2606](https://github.com/danielgindi/Charts/issues/2606)
- fatal error due to call to computeAxisValues during call to notifyDataSetChanged [\#2605](https://github.com/danielgindi/Charts/issues/2605)
- Bar Chart & label displayed wrongly when dataset count changes [\#2603](https://github.com/danielgindi/Charts/issues/2603)
- BarChart.Axis label change starting index when .rightAxix is enabled [\#2602](https://github.com/danielgindi/Charts/issues/2602)
- Spacing between left axis labels [\#2601](https://github.com/danielgindi/Charts/issues/2601)
- React Native support [\#2598](https://github.com/danielgindi/Charts/issues/2598)
- Not able to see yAxis label while changing position of that label [\#2596](https://github.com/danielgindi/Charts/issues/2596)
- About stacked bar value label postion  [\#2595](https://github.com/danielgindi/Charts/issues/2595)
- Invalid Bundle - Xcode 9 Beta 2 [\#2593](https://github.com/danielgindi/Charts/issues/2593)
- Stacked bar always show last value [\#2592](https://github.com/danielgindi/Charts/issues/2592)
- Use Charts in App Extension [\#2590](https://github.com/danielgindi/Charts/issues/2590)
- About bar and line combined [\#2589](https://github.com/danielgindi/Charts/issues/2589)
- Draw graph of a function  [\#2588](https://github.com/danielgindi/Charts/issues/2588)
- Scrolling chart [\#2587](https://github.com/danielgindi/Charts/issues/2587)
- How change value double to value int format? [\#2586](https://github.com/danielgindi/Charts/issues/2586)
- stringForValue value  [\#2585](https://github.com/danielgindi/Charts/issues/2585)
- Invalid architecture... support arm64. [\#2584](https://github.com/danielgindi/Charts/issues/2584)
- Line chart with blank data show Y axis label incorrect on iPhone 5 [\#2583](https://github.com/danielgindi/Charts/issues/2583)
- 'M\_PI' is deprecated: Please use 'Double.pi' or '.pi' to get the value of correct type and avoid casting.  [\#2580](https://github.com/danielgindi/Charts/issues/2580)
- 0 lost when the value less than 1.0，why? [\#2579](https://github.com/danielgindi/Charts/issues/2579)
- Error Library not loaded: @rpath/libswiftAppKit.dylib [\#2578](https://github.com/danielgindi/Charts/issues/2578)
- CenterText/Description Text overruns onto PieChart [\#2577](https://github.com/danielgindi/Charts/issues/2577)
- Hide specific legend not all [\#2576](https://github.com/danielgindi/Charts/issues/2576)
- horizontal barchart each bar have different maximum value [\#2575](https://github.com/danielgindi/Charts/issues/2575)
- Barchart fixed interval in left axis [\#2574](https://github.com/danielgindi/Charts/issues/2574)
- Change color of Bar chart data label [\#2573](https://github.com/danielgindi/Charts/issues/2573)
- Visible ChartDataEntry when zoomed in? [\#2572](https://github.com/danielgindi/Charts/issues/2572)
- Changing color of the bar graph [\#2571](https://github.com/danielgindi/Charts/issues/2571)
- Custom bar chart highlight style [\#2570](https://github.com/danielgindi/Charts/issues/2570)
- How to show LineChart with icon [\#2566](https://github.com/danielgindi/Charts/issues/2566)
- how to do Paging  [\#2565](https://github.com/danielgindi/Charts/issues/2565)
- Range float bars [\#2564](https://github.com/danielgindi/Charts/issues/2564)
- Cropping left axis labels and missing some values [\#2563](https://github.com/danielgindi/Charts/issues/2563)
- How to create a custom marker view in line chart for showing x values as well [\#2562](https://github.com/danielgindi/Charts/issues/2562)
- HorizontalBarChartView：when i set Xvalue = 0，1，2，3.....       HorizontalBarChartView some X dont show [\#2559](https://github.com/danielgindi/Charts/issues/2559)
- But I hope also have the valueFonts property set for each font size, like valueColors [\#2558](https://github.com/danielgindi/Charts/issues/2558)
- charts uses iphone show "No architectures to compile for \(ONLY\_ACTIVE\_ARCH=YES, active arch=arm64, VALID\_ARCHS=i386 x86\_64 \)". [\#2557](https://github.com/danielgindi/Charts/issues/2557)
- Caching charts [\#2555](https://github.com/danielgindi/Charts/issues/2555)
- How to add X axis values in the balloon marker view in IOS Charts? [\#2554](https://github.com/danielgindi/Charts/issues/2554)
- One label for each data point - Line Chart - SOLUTION FOUND [\#2553](https://github.com/danielgindi/Charts/issues/2553)
- PieChart legend.yOffset bug [\#2552](https://github.com/danielgindi/Charts/issues/2552)
- PieChart legend.yOffset bug [\#2551](https://github.com/danielgindi/Charts/issues/2551)
- Duplicates x-axis labels are plotted separately in graph? [\#2549](https://github.com/danielgindi/Charts/issues/2549)
- How to set the label count for X axis ? [\#2548](https://github.com/danielgindi/Charts/issues/2548)
- Hide graph label if value fall below a certain threshold/minimum [\#2547](https://github.com/danielgindi/Charts/issues/2547)
- BarChar Position Label [\#2546](https://github.com/danielgindi/Charts/issues/2546)
- Swift language upgrade required  [\#2544](https://github.com/danielgindi/Charts/issues/2544)
- BarChartDataEntry different value than value  display for each BarChartDataSet [\#2543](https://github.com/danielgindi/Charts/issues/2543)
- LineChartDataSet why there is no valueFonts [\#2542](https://github.com/danielgindi/Charts/issues/2542)
- Need swift combined chart demo. [\#2540](https://github.com/danielgindi/Charts/issues/2540)
- Pie Chart Highlighted region  [\#2536](https://github.com/danielgindi/Charts/issues/2536)
- Replace the value label without redraw the bar. [\#2535](https://github.com/danielgindi/Charts/issues/2535)
- Lines on Horizontal Bar Chart [\#2532](https://github.com/danielgindi/Charts/issues/2532)
- Cannot convert value of type 'AutoreleasingUnsafeMutablePointer\<NSArray\>' to expected argument type 'AutoreleasingUnsafeMutablePointer\<NSArray?\>?' [\#2530](https://github.com/danielgindi/Charts/issues/2530)
- zoom programatically, reset the zoom, set the highlight and moving the X-Axis and the Y-Axis to the middle [\#2529](https://github.com/danielgindi/Charts/issues/2529)
- BarGraph - 0 values - Bar Offset [\#2528](https://github.com/danielgindi/Charts/issues/2528)
- Chart displays intermittently [\#2527](https://github.com/danielgindi/Charts/issues/2527)
- Changing BarChart xAxis label position [\#2526](https://github.com/danielgindi/Charts/issues/2526)
- Create PDF Of Charts [\#2525](https://github.com/danielgindi/Charts/issues/2525)
- What is the equivalent of "onChartDoubleTapped"? [\#2524](https://github.com/danielgindi/Charts/issues/2524)
- Delegate Memory Leak [\#2523](https://github.com/danielgindi/Charts/issues/2523)
- Chart is sometimes not showing data on Plus devices [\#2521](https://github.com/danielgindi/Charts/issues/2521)
- How to run in Xcode 9 \(beta\) [\#2520](https://github.com/danielgindi/Charts/issues/2520)
- HorizontalBarChartView offset X  [\#2519](https://github.com/danielgindi/Charts/issues/2519)
- Line not showing in LineChart [\#2518](https://github.com/danielgindi/Charts/issues/2518)
- Displaying float values in bar chart label. [\#2517](https://github.com/danielgindi/Charts/issues/2517)
- Not working with Xcode 9 Beta1 [\#2514](https://github.com/danielgindi/Charts/issues/2514)
- PieChart: Identify univocally a specific entry [\#2513](https://github.com/danielgindi/Charts/issues/2513)
- Right Align Y Value Labels for Horizontal Bar Chart [\#2512](https://github.com/danielgindi/Charts/issues/2512)
- Swift 3 Cubic line chart  [\#2510](https://github.com/danielgindi/Charts/issues/2510)
- Inserting dataPoint "labels" in Radar Chart [\#2508](https://github.com/danielgindi/Charts/issues/2508)
- How to remove decimals from y values in iOS Charts? [\#2506](https://github.com/danielgindi/Charts/issues/2506)
- Add background to chart's legend [\#2505](https://github.com/danielgindi/Charts/issues/2505)
- Combine Bar, Line and Pie chart [\#2503](https://github.com/danielgindi/Charts/issues/2503)
- Chart not in centre of view [\#2502](https://github.com/danielgindi/Charts/issues/2502)
- Can't use different Charts Framework with different versions of Xcode [\#2499](https://github.com/danielgindi/Charts/issues/2499)
- How to change the label colors of certain XAxis values? [\#2498](https://github.com/danielgindi/Charts/issues/2498)
- Chart points are being clipped out in ScatterChart [\#2496](https://github.com/danielgindi/Charts/issues/2496)
- PieChart render very small [\#2495](https://github.com/danielgindi/Charts/issues/2495)
- Bar values inside bar horizontally? [\#2494](https://github.com/danielgindi/Charts/issues/2494)
- I used Charts in my iOS project and when I start to zoom the chart labels on xAxis get repeated again and again. How can I fix my problem? [\#2493](https://github.com/danielgindi/Charts/issues/2493)
- linechart shadow [\#2492](https://github.com/danielgindi/Charts/issues/2492)
- Set a fixed bar width in BarChartView [\#2491](https://github.com/danielgindi/Charts/issues/2491)
- Linechart inverted [\#2490](https://github.com/danielgindi/Charts/issues/2490)
- Pie chart not accepting more than one dataSet [\#2489](https://github.com/danielgindi/Charts/issues/2489)
- Graph x-axis values not displaying in swift 3.0 [\#2488](https://github.com/danielgindi/Charts/issues/2488)
- How to round corner in Radar Charts? [\#2487](https://github.com/danielgindi/Charts/issues/2487)
- Display header label on xAxis and yAxis [\#2485](https://github.com/danielgindi/Charts/issues/2485)
- XAxis label issue with multiple/grouped bar chart [\#2481](https://github.com/danielgindi/Charts/issues/2481)
- After integration, the project package becomes bigger.  [\#2480](https://github.com/danielgindi/Charts/issues/2480)
- Pinch to Zoom is not working as expected over Bar Chart [\#2479](https://github.com/danielgindi/Charts/issues/2479)
- Two markers on one line \[LineChartView\] [\#2476](https://github.com/danielgindi/Charts/issues/2476)
- Adjust the spacing between bars. \(not grouped\) [\#2475](https://github.com/danielgindi/Charts/issues/2475)
- Issue with order of x-values and Multi LineChart [\#2474](https://github.com/danielgindi/Charts/issues/2474)
- Show Integer values on LineChart [\#2473](https://github.com/danielgindi/Charts/issues/2473)
- Loading a blank chart [\#2472](https://github.com/danielgindi/Charts/issues/2472)
- LineChart isn't shown correct [\#2471](https://github.com/danielgindi/Charts/issues/2471)
- Device Orientation not detected when using Charts module [\#2469](https://github.com/danielgindi/Charts/issues/2469)
- How to set spacing between bars? [\#2467](https://github.com/danielgindi/Charts/issues/2467)
- what happened to sliceSpace function in piechartdataset? [\#2466](https://github.com/danielgindi/Charts/issues/2466)
- How to scroll between charts on carousel [\#2465](https://github.com/danielgindi/Charts/issues/2465)
- PieChartData and NSNumberFormatter memory leak about demo project [\#2464](https://github.com/danielgindi/Charts/issues/2464)
- Custom View within the PieChart Center [\#2463](https://github.com/danielgindi/Charts/issues/2463)
- toggle highlight for pie chart slice [\#2462](https://github.com/danielgindi/Charts/issues/2462)
- HorizontalBarChartView [\#2461](https://github.com/danielgindi/Charts/issues/2461)
- Cubic Line Distorted  [\#2459](https://github.com/danielgindi/Charts/issues/2459)
- Plotband support [\#2458](https://github.com/danielgindi/Charts/issues/2458)
- Bubble charts: Can i set the size same to all the bubbles?  [\#2456](https://github.com/danielgindi/Charts/issues/2456)
- Single point on line chart not showing - Swift 3 [\#2455](https://github.com/danielgindi/Charts/issues/2455)
- Plotting large amount of data slows down the Graph. [\#2454](https://github.com/danielgindi/Charts/issues/2454)
- Error on Manually adding on my project [\#2453](https://github.com/danielgindi/Charts/issues/2453)
- How to implement Gantt charts using iOS-Charts? [\#2451](https://github.com/danielgindi/Charts/issues/2451)
- How come I can't set a default color theme in ChartColorTemplate.swift? [\#2450](https://github.com/danielgindi/Charts/issues/2450)
- What is relation between the BubbleSize and the axis value?Can we control the size of Bubble in Bubble Chart? [\#2448](https://github.com/danielgindi/Charts/issues/2448)
- chart 3.0 xAxis labels not mapped [\#2447](https://github.com/danielgindi/Charts/issues/2447)
- fatal error: Index out of range while subclassing renderer [\#2446](https://github.com/danielgindi/Charts/issues/2446)
- Bug in line chart yAxis when range are less than 1 [\#2445](https://github.com/danielgindi/Charts/issues/2445)
- Help with custom graphic/chart [\#2442](https://github.com/danielgindi/Charts/issues/2442)
- PieChart center Attributed Text [\#2441](https://github.com/danielgindi/Charts/issues/2441)
- PieChart center Attributed Text [\#2440](https://github.com/danielgindi/Charts/issues/2440)
- show tooltip on click [\#2438](https://github.com/danielgindi/Charts/issues/2438)
- Got Error : “\_OBJC\_CLASS\_$\_\_TtC10ChartsDemo12XYMarkerView” in Charts [\#2437](https://github.com/danielgindi/Charts/issues/2437)
- Change color label over circle [\#2436](https://github.com/danielgindi/Charts/issues/2436)
- How to set labels based on max and min value of the plot? [\#2434](https://github.com/danielgindi/Charts/issues/2434)
- How to make the pie chart selected by default？ [\#2431](https://github.com/danielgindi/Charts/issues/2431)
- Animate 3 dataSets one by one [\#2428](https://github.com/danielgindi/Charts/issues/2428)
- Marker points on wrong position [\#2426](https://github.com/danielgindi/Charts/issues/2426)
- Legend leak in version 3.0.1 [\#2425](https://github.com/danielgindi/Charts/issues/2425)
- Show Two Circles in Pie Chart [\#2424](https://github.com/danielgindi/Charts/issues/2424)
- restrainViewPort is not working. Chart stays the same! [\#2423](https://github.com/danielgindi/Charts/issues/2423)
- Line Chart show value on specific coordinate [\#2420](https://github.com/danielgindi/Charts/issues/2420)
- Is it possible to extend the gesture handler? [\#2419](https://github.com/danielgindi/Charts/issues/2419)
- Memory Leak in BarChartView Data  [\#2416](https://github.com/danielgindi/Charts/issues/2416)
- Track Scroll Left or Right [\#2415](https://github.com/danielgindi/Charts/issues/2415)
- How can I add the xVals to LineChartData object? [\#2412](https://github.com/danielgindi/Charts/issues/2412)
- Line chart doesn't redraw correctly after removeEntry\(\) [\#2411](https://github.com/danielgindi/Charts/issues/2411)
- Bar width [\#2410](https://github.com/danielgindi/Charts/issues/2410)
- There is no way to restrict zoom level after certain zoom level [\#2409](https://github.com/danielgindi/Charts/issues/2409)
- Half Pie Chart no full view [\#2408](https://github.com/danielgindi/Charts/issues/2408)
- How to use gradient colors for each column in a histogram? [\#2407](https://github.com/danielgindi/Charts/issues/2407)
- Fill line chart when drag with two fingers [\#2405](https://github.com/danielgindi/Charts/issues/2405)
- chartValueSelected Delegate Method Not Called [\#2404](https://github.com/danielgindi/Charts/issues/2404)
- Draw bar line even though the value is zero [\#2403](https://github.com/danielgindi/Charts/issues/2403)
- Adding line/dataset on line chart that already has a line [\#2401](https://github.com/danielgindi/Charts/issues/2401)
- help with labels [\#2400](https://github.com/danielgindi/Charts/issues/2400)
-  'Charts/Charts.h' file not found when tried build using "XCODEBUILD" command in terminal [\#2397](https://github.com/danielgindi/Charts/issues/2397)
- moveViewToX\(\) has no affect on a chart whose frame has not been set yet [\#2395](https://github.com/danielgindi/Charts/issues/2395)
- Appending Units\(i.e. m/s, mpg, etc\) to yaxis?? [\#2394](https://github.com/danielgindi/Charts/issues/2394)
- Chart Top label Issue [\#2392](https://github.com/danielgindi/Charts/issues/2392)
- How to just draw one circle value for LineChartView [\#2391](https://github.com/danielgindi/Charts/issues/2391)
- Support for 3D charts [\#2389](https://github.com/danielgindi/Charts/issues/2389)
- Y Axis range calculation is incorrect for Bar Charts [\#2386](https://github.com/danielgindi/Charts/issues/2386)
- Is having a ChartViewBase inside a custom UIView even possible? [\#2384](https://github.com/danielgindi/Charts/issues/2384)
- "chartValueSelected" is not getting called.  [\#2383](https://github.com/danielgindi/Charts/issues/2383)
- Does it support XO line\(American CandleStick\)? [\#2382](https://github.com/danielgindi/Charts/issues/2382)
- Chart Won't Display [\#2381](https://github.com/danielgindi/Charts/issues/2381)
- autoScaleMinMaxEnabled Not Working [\#2374](https://github.com/danielgindi/Charts/issues/2374)
- Graph is being drawn outside of visible area [\#2373](https://github.com/danielgindi/Charts/issues/2373)
- Compile error on Release build configuration [\#2372](https://github.com/danielgindi/Charts/issues/2372)
- moveViewToX clips bar [\#2370](https://github.com/danielgindi/Charts/issues/2370)
- swift 3.x -\> New ChartMarker? [\#2369](https://github.com/danielgindi/Charts/issues/2369)
- Crash on computeAxisValues of Bar Chart [\#2368](https://github.com/danielgindi/Charts/issues/2368)
- How to properly reset zoom on data refresh? [\#2367](https://github.com/danielgindi/Charts/issues/2367)
- What is the meaning of xValue in moveViewToAnimated? [\#2366](https://github.com/danielgindi/Charts/issues/2366)
- Horizontal Bar Chart rightAxis default maximum space [\#2363](https://github.com/danielgindi/Charts/issues/2363)
- Why my LineChartDataSet's line is behind the bar? [\#2354](https://github.com/danielgindi/Charts/issues/2354)
- charts2.5 bottom line render issue [\#2352](https://github.com/danielgindi/Charts/issues/2352)
- Limit the number of bars to 10 when the x-axis is made of strings [\#2351](https://github.com/danielgindi/Charts/issues/2351)
- Not able to plot values in linechart  [\#2349](https://github.com/danielgindi/Charts/issues/2349)
- Text in Marker [\#2348](https://github.com/danielgindi/Charts/issues/2348)
- PieChart overlay lines or dots for select slices in the pie chart [\#2347](https://github.com/danielgindi/Charts/issues/2347)
- Multiple colors grid background [\#2346](https://github.com/danielgindi/Charts/issues/2346)
- The direction of the two BarChart [\#2345](https://github.com/danielgindi/Charts/issues/2345)
- how to format y-axix values [\#2343](https://github.com/danielgindi/Charts/issues/2343)
- different Bar color based on values [\#2342](https://github.com/danielgindi/Charts/issues/2342)
- XCode wants to convert Chart  [\#2341](https://github.com/danielgindi/Charts/issues/2341)
- Population Pyramid [\#2340](https://github.com/danielgindi/Charts/issues/2340)
- Not all labels shown on horizontal bar chart with large datasets [\#2339](https://github.com/danielgindi/Charts/issues/2339)
- Using ChartViewBase as the type and instantiating the chart later as a specific type [\#2338](https://github.com/danielgindi/Charts/issues/2338)
- Remove Highlight from bar graph [\#2337](https://github.com/danielgindi/Charts/issues/2337)
- Set diffent style to single line graph  [\#2336](https://github.com/danielgindi/Charts/issues/2336)
- change the color of the highlight point [\#2334](https://github.com/danielgindi/Charts/issues/2334)
- Extra space to the right of chart [\#2333](https://github.com/danielgindi/Charts/issues/2333)
- xcode 8.3 swift 3.1 [\#2332](https://github.com/danielgindi/Charts/issues/2332)
- AxisDependencyRight  Question~~ [\#2331](https://github.com/danielgindi/Charts/issues/2331)
- Module Charts not found XCODE 8.1 Error [\#2330](https://github.com/danielgindi/Charts/issues/2330)
- How to remove Grid  [\#2329](https://github.com/danielgindi/Charts/issues/2329)
- The spacing between the two points is wider than the distance between the labels [\#2327](https://github.com/danielgindi/Charts/issues/2327)
- Skipped installing realm-cocoa.framework binary due to the error: [\#2326](https://github.com/danielgindi/Charts/issues/2326)
- Pie Chart view as Int instead of double on segment? [\#2325](https://github.com/danielgindi/Charts/issues/2325)
- Not able to give X axis Values [\#2324](https://github.com/danielgindi/Charts/issues/2324)
- Labels to PieChart? v 3.0.2 [\#2323](https://github.com/danielgindi/Charts/issues/2323)
- How to prevent static data sets from redrawing and hogging CPU [\#2322](https://github.com/danielgindi/Charts/issues/2322)
- BarChart xAxis labels a moving [\#2321](https://github.com/danielgindi/Charts/issues/2321)
- How to add a fade background for data values [\#2320](https://github.com/danielgindi/Charts/issues/2320)
- Limit Line after update to 3.0.2 no longer shown [\#2319](https://github.com/danielgindi/Charts/issues/2319)
- Charts v3.0.2 does not build using Carthage [\#2317](https://github.com/danielgindi/Charts/issues/2317)
- spaceMin and spaceMax are not percentages [\#2314](https://github.com/danielgindi/Charts/issues/2314)
- API for increasing or decreasing condition for CandleStick Chart [\#2311](https://github.com/danielgindi/Charts/issues/2311)
- Create Tag to include 'fix for Xcode 8.3' [\#2309](https://github.com/danielgindi/Charts/issues/2309)
- Ambiguous use of 'data' [\#2304](https://github.com/danielgindi/Charts/issues/2304)
- Default backgroundColor \(nil\) displays as black instead of clear. [\#2222](https://github.com/danielgindi/Charts/issues/2222)
- pdf report with Line Chart blurry from iphone 7 [\#2204](https://github.com/danielgindi/Charts/issues/2204)
- how to add Values to Pie Chart? and how to show complete pie chart instead of doughnut [\#2150](https://github.com/danielgindi/Charts/issues/2150)
- 'LineChartView' is unavailable: cannot find Swift declaration for this class [\#2145](https://github.com/danielgindi/Charts/issues/2145)
- Display label MMyyyy for barChart [\#2142](https://github.com/danielgindi/Charts/issues/2142)
- barSpace  attribute [\#2137](https://github.com/danielgindi/Charts/issues/2137)
- Bar Chart, Shifting Y Position [\#2134](https://github.com/danielgindi/Charts/issues/2134)
- highlightValue is throwing "fatal error: Index out of range" in Combined Chart [\#2076](https://github.com/danielgindi/Charts/issues/2076)

**Merged pull requests:**

- Update xcode project for xcode 9 [\#2767](https://github.com/danielgindi/Charts/pull/2767) ([petester42](https://github.com/petester42))
- Fixed value setter on PieChartDataEntry [\#2754](https://github.com/danielgindi/Charts/pull/2754) ([martnst](https://github.com/martnst))
- Conform to macOS api changes in swift 3.2 [\#2717](https://github.com/danielgindi/Charts/pull/2717) ([ohbargain](https://github.com/ohbargain))
- Fix CombinedChartView not draw markers [\#2702](https://github.com/danielgindi/Charts/pull/2702) ([xzysun](https://github.com/xzysun))
- Reduce build time with minor reference refactor [\#2679](https://github.com/danielgindi/Charts/pull/2679) ([xinranw](https://github.com/xinranw))
- Fix Typo: Probider -\> Provider [\#2650](https://github.com/danielgindi/Charts/pull/2650) ([russellbstephens](https://github.com/russellbstephens))
- Adding a third party tutorial [\#2604](https://github.com/danielgindi/Charts/pull/2604) ([osianSmith](https://github.com/osianSmith))
- fix \#2099, avoid crash when some chart only allow 1 data set [\#2500](https://github.com/danielgindi/Charts/pull/2500) ([liuxuan30](https://github.com/liuxuan30))
- tutorial link added to readme [\#2484](https://github.com/danielgindi/Charts/pull/2484) ([annalizhaz](https://github.com/annalizhaz))
- Allow turning off drag in X and Y axes separately. [\#2413](https://github.com/danielgindi/Charts/pull/2413) ([maciejtrybilo](https://github.com/maciejtrybilo))
- Run view port jobs afterwards \(Fixes \#2395\) [\#2396](https://github.com/danielgindi/Charts/pull/2396) ([feosuna1](https://github.com/feosuna1))
- Minor improvements in BalloonMarker.swift [\#2393](https://github.com/danielgindi/Charts/pull/2393) ([valeriyvan](https://github.com/valeriyvan))
- remove build for ci tests procedure, use `clean test` directly [\#2388](https://github.com/danielgindi/Charts/pull/2388) ([liuxuan30](https://github.com/liuxuan30))
- Update Travis config for Xcode 8.3 and fix test failures [\#2378](https://github.com/danielgindi/Charts/pull/2378) ([liuxuan30](https://github.com/liuxuan30))
- Fix Simple Bar Chart Demo, switch use of x and y values [\#2365](https://github.com/danielgindi/Charts/pull/2365) ([franqueli](https://github.com/franqueli))
- Bug fixing with one line, updating ChartViewBase.swift [\#2355](https://github.com/danielgindi/Charts/pull/2355) ([Eric0625](https://github.com/Eric0625))
- Fixed, If the last value is the max or min, the range will be wrong [\#2229](https://github.com/danielgindi/Charts/pull/2229) ([aelam](https://github.com/aelam))
- fix \#2222 move default backgroundColor to initialize\(\) [\#2228](https://github.com/danielgindi/Charts/pull/2228) ([liuxuan30](https://github.com/liuxuan30))
- Fix \#1879. Similar cut in half issue in scatter chart like others [\#1891](https://github.com/danielgindi/Charts/pull/1891) ([liuxuan30](https://github.com/liuxuan30))

## [v3.0.2](https://github.com/danielgindi/Charts/tree/v3.0.2) (2017-04-02)
[Full Changelog](https://github.com/danielgindi/Charts/compare/3.0.2...v3.0.2)

## [3.0.2](https://github.com/danielgindi/Charts/tree/3.0.2) (2017-04-02)
[Full Changelog](https://github.com/danielgindi/Charts/compare/3.0.1...3.0.2)

**Implemented enhancements:**

- Plotting Realm data in Swift. RLMResults not available [\#898](https://github.com/danielgindi/Charts/issues/898)

**Fixed bugs:**

- \[Bug\] Double BarChart second value text mispaced [\#1191](https://github.com/danielgindi/Charts/issues/1191)
- \<Error\>: CGAffineTransformInvert: singular matrix [\#802](https://github.com/danielgindi/Charts/issues/802)

**Closed issues:**

- Please give option for installing via Swift Package Manager too [\#2313](https://github.com/danielgindi/Charts/issues/2313)
- Adjust spacing between axis labels and axes? [\#2310](https://github.com/danielgindi/Charts/issues/2310)
- \*PieChart\* Get color of top 3 largest percent [\#2308](https://github.com/danielgindi/Charts/issues/2308)
- Set the x axis data problems, and 2.3.0 apis are quite different.Need your help [\#2307](https://github.com/danielgindi/Charts/issues/2307)
- little issue with chart custoemView size & chart data  [\#2305](https://github.com/danielgindi/Charts/issues/2305)
- hello，A small problem. [\#2303](https://github.com/danielgindi/Charts/issues/2303)
- Running 1 of 1 custom shell scripts [\#2302](https://github.com/danielgindi/Charts/issues/2302)
- Multiple warnings xCode 8.3 [\#2298](https://github.com/danielgindi/Charts/issues/2298)
- Remove color label in pie-chart [\#2297](https://github.com/danielgindi/Charts/issues/2297)
- Charts 2.3 with Swift 3 \(xCode 8.3\) [\#2295](https://github.com/danielgindi/Charts/issues/2295)
- Modify Marker before showing value [\#2294](https://github.com/danielgindi/Charts/issues/2294)
- Xcode 8.3: 62 deprecation warnings [\#2293](https://github.com/danielgindi/Charts/issues/2293)
- Fix warning for Xcode8.3 [\#2292](https://github.com/danielgindi/Charts/issues/2292)
- Chart Selected function is not called on click of every Bar. [\#2291](https://github.com/danielgindi/Charts/issues/2291)
- “Swift Language Version” [\#2290](https://github.com/danielgindi/Charts/issues/2290)
- Swift 3.1 support [\#2288](https://github.com/danielgindi/Charts/issues/2288)
- Swift 3.1 Build [\#2287](https://github.com/danielgindi/Charts/issues/2287)
- ChartTouchDelegate never called [\#2286](https://github.com/danielgindi/Charts/issues/2286)
- Radar map size setting [\#2284](https://github.com/danielgindi/Charts/issues/2284)
- How do I set the radar map size? [\#2283](https://github.com/danielgindi/Charts/issues/2283)
- Adjust circle radius for individual line chart data point [\#2282](https://github.com/danielgindi/Charts/issues/2282)
- Get current axis interval between labels [\#2281](https://github.com/danielgindi/Charts/issues/2281)
- Only allow slice space for the selected item in a pie chart [\#2278](https://github.com/danielgindi/Charts/issues/2278)
- PieChartDataSet [\#2277](https://github.com/danielgindi/Charts/issues/2277)
- I want to un-highlight a line chart when dragging has stopped, among other things. Am I missing it?  Thanks [\#2276](https://github.com/danielgindi/Charts/issues/2276)
- Demo for Swift 3 [\#2274](https://github.com/danielgindi/Charts/issues/2274)
- Getting the percentage and label for a slice [\#2273](https://github.com/danielgindi/Charts/issues/2273)
- Line chart - LineChartDataSet Background color. [\#2272](https://github.com/danielgindi/Charts/issues/2272)
- place image at top of each bar in bar graph [\#2271](https://github.com/danielgindi/Charts/issues/2271)
- How to draw circle at the end of a data set on line chart  [\#2270](https://github.com/danielgindi/Charts/issues/2270)
- when i install pod 'Charts'. its install Old Version Of library 2.3 and give me error convert code into latest swift. [\#2269](https://github.com/danielgindi/Charts/issues/2269)
- ChartsDemo Buildtime Error: No such module 'RealmSwift' [\#2268](https://github.com/danielgindi/Charts/issues/2268)
- Total Data [\#2267](https://github.com/danielgindi/Charts/issues/2267)
- Line Chart Highlight, Remove X axis line and display label [\#2266](https://github.com/danielgindi/Charts/issues/2266)
- Increasing bar width in grouped sets of BarChartView [\#2263](https://github.com/danielgindi/Charts/issues/2263)
- lineChart.leftAxis.startAtZeroEnabled is not available in Swift 3 library [\#2262](https://github.com/danielgindi/Charts/issues/2262)
- Can't find highlightcolor [\#2256](https://github.com/danielgindi/Charts/issues/2256)
- Custom yAxis values [\#2254](https://github.com/danielgindi/Charts/issues/2254)
- Dynamic generate Int values for axis [\#2253](https://github.com/danielgindi/Charts/issues/2253)
- viewController can't be dealloc [\#2252](https://github.com/danielgindi/Charts/issues/2252)
- How to include a gap between data points & lines [\#2251](https://github.com/danielgindi/Charts/issues/2251)
- Highlight particular X and Y coordinations in the iOS charts [\#2250](https://github.com/danielgindi/Charts/issues/2250)
- Bar shapes are overlapped with other bars  [\#2248](https://github.com/danielgindi/Charts/issues/2248)
- Xaxis Label value in range \(2.5-5.0,5.0-7.5\) [\#2246](https://github.com/danielgindi/Charts/issues/2246)
- Multiline axis labels are cut off [\#2244](https://github.com/danielgindi/Charts/issues/2244)
- Problems with creating CombinedChart \(Line + grouped BarChart\) in Swift 3.0 [\#2243](https://github.com/danielgindi/Charts/issues/2243)
- Convert to Current Swift Syntax [\#2242](https://github.com/danielgindi/Charts/issues/2242)
- Line chart real time data - memory leak [\#2241](https://github.com/danielgindi/Charts/issues/2241)
- Radar chart bug - grey view shown on 6 plus and 7 plus [\#2238](https://github.com/danielgindi/Charts/issues/2238)
- How to set dual axis in line chart  [\#2237](https://github.com/danielgindi/Charts/issues/2237)
- Tableview stuttering when rendering a pie chart [\#2234](https://github.com/danielgindi/Charts/issues/2234)
- Set vertical legend [\#2233](https://github.com/danielgindi/Charts/issues/2233)
- how to join XAxisLabel in MultipleBarChart under every Bar [\#2232](https://github.com/danielgindi/Charts/issues/2232)
- Custom xAxisRenderer [\#2230](https://github.com/danielgindi/Charts/issues/2230)
- demo can not run [\#2227](https://github.com/danielgindi/Charts/issues/2227)
- Change color of lineChartDataSet when chartValueSelected [\#2226](https://github.com/danielgindi/Charts/issues/2226)
- Legend Label problem when the device changes orientation [\#2224](https://github.com/danielgindi/Charts/issues/2224)
- Legend vertical offset for horizontally aligned legends [\#2223](https://github.com/danielgindi/Charts/issues/2223)
- Don't display or load all Variables on the Left of the Bar \(iOS - Swift 3\) [\#2221](https://github.com/danielgindi/Charts/issues/2221)
- Horizontal Bar Chart lines not visible with two BarChartDataSet in chart data [\#2220](https://github.com/danielgindi/Charts/issues/2220)
- Bar chart with limit line for each Bar [\#2219](https://github.com/danielgindi/Charts/issues/2219)
- why the zero can't show at the first [\#2218](https://github.com/danielgindi/Charts/issues/2218)
- About BalloonMarker [\#2216](https://github.com/danielgindi/Charts/issues/2216)
- How can I have these values in positive? Thanks. [\#2212](https://github.com/danielgindi/Charts/issues/2212)
- Line Graph is not showing line on the Chart [\#2209](https://github.com/danielgindi/Charts/issues/2209)
- Issue of enum description [\#2208](https://github.com/danielgindi/Charts/issues/2208)
- I find a issues in HorizontalBarChartView  [\#2205](https://github.com/danielgindi/Charts/issues/2205)
- My ipa\(used Charts\) is 40M ;another ipa\(unused Charts\) is 13M,how to be sliming??? [\#2203](https://github.com/danielgindi/Charts/issues/2203)
- Legend vertical offset [\#2202](https://github.com/danielgindi/Charts/issues/2202)
- Charts not showing on tvOS [\#2201](https://github.com/danielgindi/Charts/issues/2201)
- Is it enable to keep the origin visible X Range when I appended more data  [\#2198](https://github.com/danielgindi/Charts/issues/2198)
- Issue with Realm [\#2195](https://github.com/danielgindi/Charts/issues/2195)
- External lines - Pie Chart [\#2194](https://github.com/danielgindi/Charts/issues/2194)
- Hide labels - Pie chart [\#2193](https://github.com/danielgindi/Charts/issues/2193)
- is two sided bar graph possible ? [\#2192](https://github.com/danielgindi/Charts/issues/2192)
- Optimisation of drawing many points on line graph [\#2190](https://github.com/danielgindi/Charts/issues/2190)
- Bar Chart with dual axis [\#2188](https://github.com/danielgindi/Charts/issues/2188)
- Customize Text inside Ballon Marker [\#2186](https://github.com/danielgindi/Charts/issues/2186)
- "Shell Script Invocation Error: Command /bin/sh failed with exit code 1'  [\#2185](https://github.com/danielgindi/Charts/issues/2185)
- Increase space between bars with only one dataset and assign font for specific value and xAxis label [\#2184](https://github.com/danielgindi/Charts/issues/2184)
- "noDataText" does not work [\#2179](https://github.com/danielgindi/Charts/issues/2179)
- Change color of one label [\#2178](https://github.com/danielgindi/Charts/issues/2178)
- Line Chart not show empty values - Charts 3.0 [\#2176](https://github.com/danielgindi/Charts/issues/2176)
- Bottom Label is not printing whole array list. [\#2175](https://github.com/danielgindi/Charts/issues/2175)
- Stacked Horizontal Chart Scaling [\#2174](https://github.com/danielgindi/Charts/issues/2174)
- How to set different colors for intervals on LineChart? [\#2172](https://github.com/danielgindi/Charts/issues/2172)
- Could you tell me how to forbidden line chart scroll up and down? [\#2171](https://github.com/danielgindi/Charts/issues/2171)
- Fixed BarWidth based on Pixel for BarChart [\#2170](https://github.com/danielgindi/Charts/issues/2170)
- Branch Chart3.0-Swift2.3 not found. [\#2166](https://github.com/danielgindi/Charts/issues/2166)
- "clipValuesToContentEnabled" does not work [\#2163](https://github.com/danielgindi/Charts/issues/2163)
- BarChartView with TableView [\#2161](https://github.com/danielgindi/Charts/issues/2161)
- How to show label on x-axis at regular interval and tail end on the graph line [\#2160](https://github.com/danielgindi/Charts/issues/2160)
- Set line cap to CGMutablePath [\#2159](https://github.com/danielgindi/Charts/issues/2159)
- why we set the y value 2.57,but the chart show 3,if i set y value2.44,the chart show 2.how to do the problem. [\#2157](https://github.com/danielgindi/Charts/issues/2157)
- Demo crush !!! [\#2154](https://github.com/danielgindi/Charts/issues/2154)
- Fixing the Segmentation fault issue after Convert to Swift 3.0 [\#2151](https://github.com/danielgindi/Charts/issues/2151)
- GitHub API request failed [\#2149](https://github.com/danielgindi/Charts/issues/2149)
- y-Values and x-Values as string in barchart, [\#2147](https://github.com/danielgindi/Charts/issues/2147)
- Piechart with smooth edges? [\#2146](https://github.com/danielgindi/Charts/issues/2146)
- Pie chart highlighted slice offset [\#2144](https://github.com/danielgindi/Charts/issues/2144)
- Formatter data on OS X [\#2141](https://github.com/danielgindi/Charts/issues/2141)
- How to add dates to line chart? [\#2136](https://github.com/danielgindi/Charts/issues/2136)
- 'backgroundColor' is inaccessible due to 'internal' protection level on mac OS X [\#2135](https://github.com/danielgindi/Charts/issues/2135)
- Some object files have incompatible Objective-C category definitions. Some category metadata may be lost. All files containing Objective-C categories should be built using the same compiler. [\#2133](https://github.com/danielgindi/Charts/issues/2133)
- Any Demo of all charts in Swift 3 [\#2132](https://github.com/danielgindi/Charts/issues/2132)
- How to access the Position of labels on x axis of Combined Line and Bar chart on swift\(3.0.1\) [\#2131](https://github.com/danielgindi/Charts/issues/2131)
- Wrong Limit line positioning when axisMinimum is set to 0 [\#2128](https://github.com/danielgindi/Charts/issues/2128)
- Extremely Slow compile Times on XCode 8.2.1 and swift 3.0.2 [\#2127](https://github.com/danielgindi/Charts/issues/2127)
- How to add TrackBall and edit label above BarCharts in Charts swift2 and swift 3?? [\#2126](https://github.com/danielgindi/Charts/issues/2126)
- Xcode 8.3 Compatible [\#2125](https://github.com/danielgindi/Charts/issues/2125)
- LineChartData\(dataSets: dataSets\)  Missing xVals [\#2124](https://github.com/danielgindi/Charts/issues/2124)
- How to assign custom String values to XAxis? [\#2122](https://github.com/danielgindi/Charts/issues/2122)
- Simple Radar Chart causing hang / cpu 99% [\#2121](https://github.com/danielgindi/Charts/issues/2121)
- Can i use the framework without including the whole project ? [\#2118](https://github.com/danielgindi/Charts/issues/2118)
- Undefined symbols for architecture arm64 - Charts 3.0 CocoaPods [\#2117](https://github.com/danielgindi/Charts/issues/2117)
- method setlabelstoskip missing from XAxis.swift [\#2115](https://github.com/danielgindi/Charts/issues/2115)
- SocketIOClientConfiguration.swift wont compile [\#2113](https://github.com/danielgindi/Charts/issues/2113)
- No code signing identities found: No valid signing identities \(i.e. certificate and private key pair\) were found [\#2112](https://github.com/danielgindi/Charts/issues/2112)
- Can I custom draw circle in LineChart in version 3.0 ?\[help\] [\#2104](https://github.com/danielgindi/Charts/issues/2104)
- App crash XCode 8.2 Chart version 3.0 [\#2103](https://github.com/danielgindi/Charts/issues/2103)
- Charts 3.0.1 - Align x labels \(dates\) with plots [\#2094](https://github.com/danielgindi/Charts/issues/2094)
- Demo in Swift 3 [\#2092](https://github.com/danielgindi/Charts/issues/2092)
- How to set xAxis values from array in Swift 3.0 [\#2090](https://github.com/danielgindi/Charts/issues/2090)
- error to Methode  [\#2089](https://github.com/danielgindi/Charts/issues/2089)
- fitBars doesnt seems to work [\#2088](https://github.com/danielgindi/Charts/issues/2088)
- oh, no!!!!!!  Demo code can't run   [\#2087](https://github.com/danielgindi/Charts/issues/2087)
- In BarChartView leftAxis labels cutdown, does-not fit on screen.  [\#2086](https://github.com/danielgindi/Charts/issues/2086)
- accessing .rightAxis gives EXC\_BAD\_ACCESS [\#2082](https://github.com/danielgindi/Charts/issues/2082)
- leftAxis and value overlap in lineChartView [\#2080](https://github.com/danielgindi/Charts/issues/2080)
- I want to add 4 colours to the background of the chart. The colour zones depend on the y values. [\#2079](https://github.com/danielgindi/Charts/issues/2079)
- Value labels for multiple ChartDataSets all aligned at the beginning \(left\) of each bar in HorizontalBarChart [\#2077](https://github.com/danielgindi/Charts/issues/2077)
- How to Resize Labels in Pie Chart Legend [\#2075](https://github.com/danielgindi/Charts/issues/2075)
- Bar chart data entry [\#2074](https://github.com/danielgindi/Charts/issues/2074)
- How can I have a limit zone \(in android library mpandroidchart\) like feature in this iOS library [\#2073](https://github.com/danielgindi/Charts/issues/2073)
- BarChartView is unavailable, cannot find swift declaration [\#2072](https://github.com/danielgindi/Charts/issues/2072)
- Not enable to draw custom shape which is based on the XValue or YValue ?  [\#2071](https://github.com/danielgindi/Charts/issues/2071)
- Can horizontalBarChartView invert the Axis ? [\#2070](https://github.com/danielgindi/Charts/issues/2070)
- can't set different padding of each side  [\#2068](https://github.com/danielgindi/Charts/issues/2068)
- Area chart with missing points [\#2067](https://github.com/danielgindi/Charts/issues/2067)
- Demo code can't run [\#2066](https://github.com/danielgindi/Charts/issues/2066)
- Positioning the XAxis inconsistent with Y-axis and the Wiki in Swift 3 [\#2065](https://github.com/danielgindi/Charts/issues/2065)
- Radar chart title [\#2061](https://github.com/danielgindi/Charts/issues/2061)
- fixed increment [\#2059](https://github.com/danielgindi/Charts/issues/2059)
- Line Chart with callouts [\#2058](https://github.com/danielgindi/Charts/issues/2058)
- Delegate ChartValueSelected Not Called. [\#2057](https://github.com/danielgindi/Charts/issues/2057)
- IValueFormatter protocol not working [\#2055](https://github.com/danielgindi/Charts/issues/2055)
- "BalloonMarker“ cannot be find in my project [\#2051](https://github.com/danielgindi/Charts/issues/2051)
- How to use marker when I use CombinedChart?  [\#2046](https://github.com/danielgindi/Charts/issues/2046)
- yVals have decimal point [\#2045](https://github.com/danielgindi/Charts/issues/2045)
- LeftAxis Labels will disappear [\#2044](https://github.com/danielgindi/Charts/issues/2044)
- Can't addEntry to a set in Swift 3 [\#2043](https://github.com/danielgindi/Charts/issues/2043)
- Will not compile in existing Objective-C Project. [\#2041](https://github.com/danielgindi/Charts/issues/2041)
- labels on xAxis not showing up and incorrect values on yAxis [\#2040](https://github.com/danielgindi/Charts/issues/2040)
- xAxis.valueFormatter = self  create retain cycle [\#2039](https://github.com/danielgindi/Charts/issues/2039)
- Label's point of view [\#2038](https://github.com/danielgindi/Charts/issues/2038)
- How to show limited visible xValues labels? [\#2037](https://github.com/danielgindi/Charts/issues/2037)
- BarChart XAxis between Positive & Negative Values [\#2036](https://github.com/danielgindi/Charts/issues/2036)
- Error when trying to run ChartsDemo on iOS Simulator \(image not found\) [\#2035](https://github.com/danielgindi/Charts/issues/2035)
- big snap shot [\#2034](https://github.com/danielgindi/Charts/issues/2034)
- module file's minimum deployment target is ios9.3 [\#2031](https://github.com/danielgindi/Charts/issues/2031)
- Date formatter doesn't show up properly when using multiple bars in a chart. [\#2030](https://github.com/danielgindi/Charts/issues/2030)
- Y axis dynamic sampling ? [\#2029](https://github.com/danielgindi/Charts/issues/2029)
- Convert line chart entry to take dictionary [\#2027](https://github.com/danielgindi/Charts/issues/2027)
- How to set string value line \(27 Oct\) in xAxis [\#2025](https://github.com/danielgindi/Charts/issues/2025)
- Build \[ChartsDemo\]  Error : 「ChartsRealm  Reason: image not found 」alaway [\#2024](https://github.com/danielgindi/Charts/issues/2024)
- \[LineChartView\] setting xAxis.axisMinimum affects xAxis.spaceMin [\#2022](https://github.com/danielgindi/Charts/issues/2022)
- c   vcgffrgfffgffgfgffffrfrfrrfdfdfdfdfdfdfggkjhghghghghjhjhjhjkjkjkkkklklklklikii0987654321poijkl;';lk;l; [\#2021](https://github.com/danielgindi/Charts/issues/2021)
- How to make the highlight line work with UIPanGestureRecognizer [\#2019](https://github.com/danielgindi/Charts/issues/2019)
- BarChart Issue for plotting the value on x-axis serially  [\#2017](https://github.com/danielgindi/Charts/issues/2017)
- Please help me with the Pie Charts in Charts 3.0.1. [\#2016](https://github.com/danielgindi/Charts/issues/2016)
- ipa becomes larger after using Charts [\#2015](https://github.com/danielgindi/Charts/issues/2015)
- BarChartView highlight issues [\#2014](https://github.com/danielgindi/Charts/issues/2014)
- How to set up a line chart, the starting point of the distance from the left margin, border distance end points [\#2013](https://github.com/danielgindi/Charts/issues/2013)
- mult bars not show all [\#2012](https://github.com/danielgindi/Charts/issues/2012)
- how to change legend font size? [\#2011](https://github.com/danielgindi/Charts/issues/2011)
- image instead of text in CenterText in pie chart [\#2010](https://github.com/danielgindi/Charts/issues/2010)
- Change PieChart Center Color [\#2009](https://github.com/danielgindi/Charts/issues/2009)
- Demo issues [\#2008](https://github.com/danielgindi/Charts/issues/2008)
- How to display last label in X-Axis [\#2007](https://github.com/danielgindi/Charts/issues/2007)
- One point different values on the axis X [\#2005](https://github.com/danielgindi/Charts/issues/2005)
- Hide all text/labels/values in pieChart [\#2004](https://github.com/danielgindi/Charts/issues/2004)
- it is not an issue. it is related to OHLC chart  [\#2003](https://github.com/danielgindi/Charts/issues/2003)
- Disable PieChart Label in Chart [\#2002](https://github.com/danielgindi/Charts/issues/2002)
- \[LineChartViewController stringForValue:axis:\] [\#2001](https://github.com/danielgindi/Charts/issues/2001)
- Values are cut out of the window [\#2000](https://github.com/danielgindi/Charts/issues/2000)
- XAxis in top AND bottom position [\#1999](https://github.com/danielgindi/Charts/issues/1999)
- Can't compile under Xcode 8.1 with Swift 3.0 [\#1998](https://github.com/danielgindi/Charts/issues/1998)
- Always display max 5 visible bars in BarChartView [\#1997](https://github.com/danielgindi/Charts/issues/1997)
- I want touchpoint in the bar to do something [\#1996](https://github.com/danielgindi/Charts/issues/1996)
- Is there any way to change chart data label from bottom of table to top? [\#1995](https://github.com/danielgindi/Charts/issues/1995)
- Installation not clear, for me [\#1994](https://github.com/danielgindi/Charts/issues/1994)
- Add space between bar chart [\#1993](https://github.com/danielgindi/Charts/issues/1993)
- How to fix the distance between each point on the X-coordinate? [\#1992](https://github.com/danielgindi/Charts/issues/1992)
-  How to add tips to RadarChartView [\#1991](https://github.com/danielgindi/Charts/issues/1991)
- Piechart issue [\#1990](https://github.com/danielgindi/Charts/issues/1990)
- Multiple colors issue in stacked bar line charts swift3 [\#1989](https://github.com/danielgindi/Charts/issues/1989)
- ChartViewDelegate is not called. [\#1988](https://github.com/danielgindi/Charts/issues/1988)
- Always display vertical scrolling indicator on horizontal bar chart [\#1987](https://github.com/danielgindi/Charts/issues/1987)
- Can't compile DemoProject [\#1986](https://github.com/danielgindi/Charts/issues/1986)
- My pie chart legends still not showing [\#1985](https://github.com/danielgindi/Charts/issues/1985)
- no yValCount in ChartData [\#1982](https://github.com/danielgindi/Charts/issues/1982)
- no limitLineSegmentsBuffer in YAxisRenderer [\#1981](https://github.com/danielgindi/Charts/issues/1981)
- Legend label missing in pie chart [\#1980](https://github.com/danielgindi/Charts/issues/1980)
- For X axis the values are not adding [\#1979](https://github.com/danielgindi/Charts/issues/1979)
- When I have two data, charts shows the three data automatically [\#1978](https://github.com/danielgindi/Charts/issues/1978)
- Two legends for stacked bar chart [\#1977](https://github.com/danielgindi/Charts/issues/1977)
- X axis coordinate point spacing [\#1975](https://github.com/danielgindi/Charts/issues/1975)
- Carthage was not found [\#1974](https://github.com/danielgindi/Charts/issues/1974)
- How to Show XAxis Label On YAxis Bar In BarView Charts? [\#1973](https://github.com/danielgindi/Charts/issues/1973)
- Labels on Mac OS X application : place and title [\#1972](https://github.com/danielgindi/Charts/issues/1972)
- I am gettign @import AppKit is missing in Charts-Swift.h under charts.framework [\#1971](https://github.com/danielgindi/Charts/issues/1971)
- Multiline chart with common data point [\#1970](https://github.com/danielgindi/Charts/issues/1970)
- Version 3.0.1: Fill gradient fills out the wrong side of the chart [\#1968](https://github.com/danielgindi/Charts/issues/1968)
- How to pass data to the X axis?? [\#1967](https://github.com/danielgindi/Charts/issues/1967)
- How do I implement a legend click event? [\#1965](https://github.com/danielgindi/Charts/issues/1965)
- can't set the XVals [\#1964](https://github.com/danielgindi/Charts/issues/1964)
- Color for Legend label [\#1962](https://github.com/danielgindi/Charts/issues/1962)
- Two y axis plot on a side? [\#1961](https://github.com/danielgindi/Charts/issues/1961)
- Scrolling feature? [\#1960](https://github.com/danielgindi/Charts/issues/1960)
- Can you add the chart Of the triangle？ [\#1958](https://github.com/danielgindi/Charts/issues/1958)
- Multiple y-data for single x-index [\#1955](https://github.com/danielgindi/Charts/issues/1955)
- LineChartView: axisMaximum and axisMinimum work wrong in rightAxis [\#1953](https://github.com/danielgindi/Charts/issues/1953)
- Radar Chart axis maximum [\#1952](https://github.com/danielgindi/Charts/issues/1952)
- Adding a single x-axis label for two data points. [\#1951](https://github.com/danielgindi/Charts/issues/1951)
- Adding title to X-axis and Y-axis [\#1950](https://github.com/danielgindi/Charts/issues/1950)
- Find screen location for bars in a bar chart [\#1949](https://github.com/danielgindi/Charts/issues/1949)
- \(3.0 Regression\) Scatter chart cuts off data at the edges [\#1946](https://github.com/danielgindi/Charts/issues/1946)
- How automatic showing marker？ [\#1944](https://github.com/danielgindi/Charts/issues/1944)
- Right and left margins [\#1942](https://github.com/danielgindi/Charts/issues/1942)
- Displaying an overlay with extra / more detailed information on mouse hover [\#1940](https://github.com/danielgindi/Charts/issues/1940)
- Can we have flexible y Axis scale [\#1939](https://github.com/danielgindi/Charts/issues/1939)
- Charts3.0 - Can not show all the xAxis label if more than 8 value - Swift [\#1938](https://github.com/danielgindi/Charts/issues/1938)
- Barchart xAxis interval [\#1937](https://github.com/danielgindi/Charts/issues/1937)
- Avoiding chart legend [\#1936](https://github.com/danielgindi/Charts/issues/1936)
- Module compiled with Swift 2.3 cannot be imported in Swift 3.0.1 [\#1935](https://github.com/danielgindi/Charts/issues/1935)
- Charts-2.3.0/Source/ChartsRealm/Data/RealmPieData.swift:19:8: No such module 'Realm' [\#1934](https://github.com/danielgindi/Charts/issues/1934)
- iOS Demo Fails to build [\#1933](https://github.com/danielgindi/Charts/issues/1933)
- Pie chart Outside values, Inside Value and legend value [\#1932](https://github.com/danielgindi/Charts/issues/1932)
- Mermory Leak in "specialized ChartXAxisRenderer.drawLabels\(context : CGContext, pos : CGFloat, anchor : CGPoint\) -\> \(\)" [\#1928](https://github.com/danielgindi/Charts/issues/1928)
- stringForValue method return unexpected value when reload TableView data. [\#1927](https://github.com/danielgindi/Charts/issues/1927)
- Can't render CombinedChartView [\#1926](https://github.com/danielgindi/Charts/issues/1926)
- Migration to Chart 3.0, Problem with Stack BarChartView [\#1925](https://github.com/danielgindi/Charts/issues/1925)
- PieChartData issue in Swift 3 [\#1924](https://github.com/danielgindi/Charts/issues/1924)
- Values overlap for bar chart and labels of X-Axis [\#1923](https://github.com/danielgindi/Charts/issues/1923)
- Please Please Help, I can’t get Charts installed properly [\#1922](https://github.com/danielgindi/Charts/issues/1922)
- Chart bar finance \(OHLC Chart\) [\#1921](https://github.com/danielgindi/Charts/issues/1921)
- Double value cannot be converted to Int in AxisRendererBase.swift on line 125 [\#1920](https://github.com/danielgindi/Charts/issues/1920)
- Highlighted values on piechart too high? [\#1919](https://github.com/danielgindi/Charts/issues/1919)
- How to achieve this effect? [\#1918](https://github.com/danielgindi/Charts/issues/1918)
- SOLVED: Round number less than zero x values issue [\#1916](https://github.com/danielgindi/Charts/issues/1916)
- Charts does not install correctly [\#1915](https://github.com/danielgindi/Charts/issues/1915)
- Xcode 8.1 is reporting 'No such Module 'Charts' & Build/Compilation Errors [\#1914](https://github.com/danielgindi/Charts/issues/1914)
- Hide legend [\#1913](https://github.com/danielgindi/Charts/issues/1913)
- xAxis force show last label [\#1912](https://github.com/danielgindi/Charts/issues/1912)
- How to install in Xcode 7.3.1 [\#1911](https://github.com/danielgindi/Charts/issues/1911)
- Can we resurrect Chart2.2.5-Swift3.0?  [\#1910](https://github.com/danielgindi/Charts/issues/1910)
- Chart Swift files not running on Obj-C XCODE8.0 install [\#1908](https://github.com/danielgindi/Charts/issues/1908)
- How to draw two circles and compare？ [\#1906](https://github.com/danielgindi/Charts/issues/1906)
- custom xAxis for line chart I need an example [\#1905](https://github.com/danielgindi/Charts/issues/1905)
- QUESTION - How mask the ranges values in a Graph [\#1904](https://github.com/danielgindi/Charts/issues/1904)
- How to give xAxis customized labels for linechartview [\#1903](https://github.com/danielgindi/Charts/issues/1903)
- How to set the axis options of radar chart? [\#1902](https://github.com/danielgindi/Charts/issues/1902)
- no match color bar chart with label [\#1900](https://github.com/danielgindi/Charts/issues/1900)
- LineChartView  highlightEnabled no work [\#1899](https://github.com/danielgindi/Charts/issues/1899)
- Can i change the value Above bar from double to int? [\#1898](https://github.com/danielgindi/Charts/issues/1898)
- Scrolling x-axis labels [\#1897](https://github.com/danielgindi/Charts/issues/1897)
- noDataText doesn't work on CombinedChart \(works fine on PieChart and BarChart\) [\#1896](https://github.com/danielgindi/Charts/issues/1896)
- StringForValue in class IValueFormatter receives dataSetIndex as 0 [\#1895](https://github.com/danielgindi/Charts/issues/1895)
- Cannot build in big project using cocoapods [\#1894](https://github.com/danielgindi/Charts/issues/1894)
- Carthage build error with xcode 8.1 and  Charts 2.3.0  [\#1893](https://github.com/danielgindi/Charts/issues/1893)
- Unable to select BarChartView from storyboard \(iOS\) [\#1892](https://github.com/danielgindi/Charts/issues/1892)
- XAxis entry count is greater than it should be. [\#1890](https://github.com/danielgindi/Charts/issues/1890)
- pod update failed - ARC Semantic Issue [\#1888](https://github.com/danielgindi/Charts/issues/1888)
- How to show marker on y-Axis data? [\#1887](https://github.com/danielgindi/Charts/issues/1887)
- Can't building and run the ChartsDemo [\#1886](https://github.com/danielgindi/Charts/issues/1886)
- how to resolve label overlapping problem with stack bar chart [\#1885](https://github.com/danielgindi/Charts/issues/1885)
- PieChart values [\#1884](https://github.com/danielgindi/Charts/issues/1884)
- Custom marker gets displayed wrong on the latest value [\#1883](https://github.com/danielgindi/Charts/issues/1883)
- Tap gesture inside the candle bar [\#1882](https://github.com/danielgindi/Charts/issues/1882)
- rotating chartview [\#1881](https://github.com/danielgindi/Charts/issues/1881)
- \[Question\] Scatter Chart && Bubble Chart: How to show out whole radius instead of hiding half radius of first data entry? [\#1879](https://github.com/danielgindi/Charts/issues/1879)
- Horizontal bar chart dose not show all xAxis labels.  [\#1878](https://github.com/danielgindi/Charts/issues/1878)
- X axis values [\#1877](https://github.com/danielgindi/Charts/issues/1877)
- Pie Chart no xAxis [\#1875](https://github.com/danielgindi/Charts/issues/1875)
- want the demo application in swift 3.0 for Charts [\#1872](https://github.com/danielgindi/Charts/issues/1872)
- Pie Bar Chart issues [\#1870](https://github.com/danielgindi/Charts/issues/1870)
- PieChartView a strange arrow [\#1869](https://github.com/danielgindi/Charts/issues/1869)
- custom  gradient color of BarChatView [\#1868](https://github.com/danielgindi/Charts/issues/1868)
- Set space between labels in xAxis [\#1865](https://github.com/danielgindi/Charts/issues/1865)
- label over sample point [\#1864](https://github.com/danielgindi/Charts/issues/1864)
- Realtime Scatterplot crashes when each ChartDataEntry is added dynamically on ScatterView.data [\#1863](https://github.com/danielgindi/Charts/issues/1863)
- Assign Bar Colours Selectively [\#1862](https://github.com/danielgindi/Charts/issues/1862)
- Detect when finger leaves the chart area [\#1861](https://github.com/danielgindi/Charts/issues/1861)
- Pie chart touch event conflict [\#1860](https://github.com/danielgindi/Charts/issues/1860)
- Customize X-axis and grid lines [\#1859](https://github.com/danielgindi/Charts/issues/1859)
- Issue with candle stick when used in combined chart [\#1858](https://github.com/danielgindi/Charts/issues/1858)
- CandleStick Background Color [\#1857](https://github.com/danielgindi/Charts/issues/1857)
- Line Support Gradient Color? [\#1856](https://github.com/danielgindi/Charts/issues/1856)
- Piechart has no xAxis? [\#1855](https://github.com/danielgindi/Charts/issues/1855)
- Retain cycle setting delegate to valueFormatter [\#1854](https://github.com/danielgindi/Charts/issues/1854)
- how to show Xaxis label value as my array value [\#1852](https://github.com/danielgindi/Charts/issues/1852)
- Skip Drawing Circles for LineCharts [\#1795](https://github.com/danielgindi/Charts/issues/1795)
- Realm update [\#1789](https://github.com/danielgindi/Charts/issues/1789)
- "Module compiled with Swift 3.0 cannot be imported in Swift 3.0.1" [\#1788](https://github.com/danielgindi/Charts/issues/1788)
- Move Realm integration to another repository [\#1756](https://github.com/danielgindi/Charts/issues/1756)
- limit line real time update [\#1755](https://github.com/danielgindi/Charts/issues/1755)
- Travis does not use github token for pull request build [\#1734](https://github.com/danielgindi/Charts/issues/1734)
- custom string value at top of bar. [\#1109](https://github.com/danielgindi/Charts/issues/1109)

**Merged pull requests:**

- Minor typo fix in console alert message "it's" -\> "its" [\#2301](https://github.com/danielgindi/Charts/pull/2301) ([simonbromberg](https://github.com/simonbromberg))
- Fix Xcode 8.3 compiler warnings [\#2279](https://github.com/danielgindi/Charts/pull/2279) ([krbarnes](https://github.com/krbarnes))
- Updated to use Realm version 2.4.3 [\#2199](https://github.com/danielgindi/Charts/pull/2199) ([kimdv](https://github.com/kimdv))
- Fixed the inconsistency of AxisMax and AxisMin [\#2177](https://github.com/danielgindi/Charts/pull/2177) ([aelam](https://github.com/aelam))
- Fixes index out of range crash. [\#2167](https://github.com/danielgindi/Charts/pull/2167) ([kzaher](https://github.com/kzaher))
- remove deprecation warnings in XCode 8.3  [\#2162](https://github.com/danielgindi/Charts/pull/2162) ([LeviDahl](https://github.com/LeviDahl))
- 'backgroundColor' is inaccessible due to 'internal' protection level … [\#2156](https://github.com/danielgindi/Charts/pull/2156) ([thierryH91200](https://github.com/thierryH91200))
- Fixes Xcode 8.3 beta build issue [\#2116](https://github.com/danielgindi/Charts/pull/2116) ([davidarve](https://github.com/davidarve))
- Adds NSPhotoLibraryUsageDescription to plist of ChartsDemo [\#2101](https://github.com/danielgindi/Charts/pull/2101) ([valeriyvan](https://github.com/valeriyvan))
- Fix demo and test targets not running/testing [\#2084](https://github.com/danielgindi/Charts/pull/2084) ([petester42](https://github.com/petester42))
- fix a typo, as orientation is horizontal by default [\#2078](https://github.com/danielgindi/Charts/pull/2078) ([liuxuan30](https://github.com/liuxuan30))
- Update Podspec RealmSwift Dependency to 2.1.1 to be in line with Cartfile [\#2064](https://github.com/danielgindi/Charts/pull/2064) ([anttyc](https://github.com/anttyc))
- Fix NSCopying implementation in CandleChartDataEntry [\#2056](https://github.com/danielgindi/Charts/pull/2056) ([leo150](https://github.com/leo150))
- Add support for extensions [\#2048](https://github.com/danielgindi/Charts/pull/2048) ([raptorxcz](https://github.com/raptorxcz))
- Update "Usage" section of README [\#1984](https://github.com/danielgindi/Charts/pull/1984) ([elaewin](https://github.com/elaewin))
- All Charts Icons Support Swift3 \[Dub \#629, \#624, \#1261\] [\#1793](https://github.com/danielgindi/Charts/pull/1793) ([abjurato](https://github.com/abjurato))

## [3.0.1](https://github.com/danielgindi/Charts/tree/3.0.1) (2016-11-20)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.0.1...3.0.1)

## [v3.0.1](https://github.com/danielgindi/Charts/tree/v3.0.1) (2016-11-20)
[Full Changelog](https://github.com/danielgindi/Charts/compare/2.3.1...v3.0.1)

**Closed issues:**

- Legend under the bar in bar Chart [\#1850](https://github.com/danielgindi/Charts/issues/1850)
- MultipleBarChart barSpace does not work [\#1849](https://github.com/danielgindi/Charts/issues/1849)
- Can not show decimal in y value on BarChart [\#1848](https://github.com/danielgindi/Charts/issues/1848)
- Cannot add labels to a simple Bar Chart [\#1846](https://github.com/danielgindi/Charts/issues/1846)
- when running my project on iphone device but not simulator it has error"image not found" [\#1845](https://github.com/danielgindi/Charts/issues/1845)
- Removing red selector cross on Line Chart nodes [\#1844](https://github.com/danielgindi/Charts/issues/1844)
- Get screen\(x,y\) value from the plotted points [\#1843](https://github.com/danielgindi/Charts/issues/1843)
- Color between two limit lines [\#1842](https://github.com/danielgindi/Charts/issues/1842)
- Charts-umbrella.h [\#1841](https://github.com/danielgindi/Charts/issues/1841)
- Unable to call 'data' from lineChartView. [\#1839](https://github.com/danielgindi/Charts/issues/1839)
- How to use charts library in my objective c program?.. [\#1837](https://github.com/danielgindi/Charts/issues/1837)
- Migration to Swift 3.x [\#1836](https://github.com/danielgindi/Charts/issues/1836)
- How to hide BarChart hide bottom  description  color & text ? [\#1835](https://github.com/danielgindi/Charts/issues/1835)
-  please help Line Graph not working on Charts 2.3.0 \(for Xcode8 and Swift 2.3.0\) [\#1834](https://github.com/danielgindi/Charts/issues/1834)
- Error when adding embedded binaries [\#1832](https://github.com/danielgindi/Charts/issues/1832)
- X-Axis for different different charts [\#1831](https://github.com/danielgindi/Charts/issues/1831)
- Setting values label [\#1829](https://github.com/danielgindi/Charts/issues/1829)
- Adding background color between two limit lines [\#1828](https://github.com/danielgindi/Charts/issues/1828)
- Horizontal bar chart doesn't start at 0 [\#1827](https://github.com/danielgindi/Charts/issues/1827)
- Gradient Background for LineChart. \(Not Working\) [\#1826](https://github.com/danielgindi/Charts/issues/1826)
- y axis label show wiered value 1.11022302462516e- when passed 0 as value [\#1825](https://github.com/danielgindi/Charts/issues/1825)
- Pie chart specific colors [\#1824](https://github.com/danielgindi/Charts/issues/1824)
- Help  ?     [\#1823](https://github.com/danielgindi/Charts/issues/1823)
- Its possible change the thickness of the pie chart? [\#1822](https://github.com/danielgindi/Charts/issues/1822)
- BarChart moveToX wrong [\#1820](https://github.com/danielgindi/Charts/issues/1820)
- How to set multiple Stackbar with Charts 3 [\#1819](https://github.com/danielgindi/Charts/issues/1819)
- How do i set  [\#1818](https://github.com/danielgindi/Charts/issues/1818)
- LimitLine Label Value Overlap with BarChartData Label [\#1817](https://github.com/danielgindi/Charts/issues/1817)
- What is the replacement for  xVals for  BarChartData? in swift 3.0 [\#1816](https://github.com/danielgindi/Charts/issues/1816)
- Updated Demo Example for swift3 [\#1815](https://github.com/danielgindi/Charts/issues/1815)
- Line Chart: how to change background color through every odd label on YAxis? [\#1812](https://github.com/danielgindi/Charts/issues/1812)
- Can Radar Chart draw backgroud like this? [\#1811](https://github.com/danielgindi/Charts/issues/1811)
- Can i customize the X axis text a different color? [\#1810](https://github.com/danielgindi/Charts/issues/1810)
- Can we make a 3D Barchart with this Library?? [\#1809](https://github.com/danielgindi/Charts/issues/1809)
- 3.0.0 - adding X Values to LineChartData no longer supported? [\#1808](https://github.com/danielgindi/Charts/issues/1808)
- Swift 3 BarChart with image and title.  [\#1807](https://github.com/danielgindi/Charts/issues/1807)
- How to make horizontal dotted lines or remove it? [\#1806](https://github.com/danielgindi/Charts/issues/1806)
- Full control over x labels in 3.0.0 [\#1804](https://github.com/danielgindi/Charts/issues/1804)
- How to change stackbar according to order? [\#1803](https://github.com/danielgindi/Charts/issues/1803)
- How to change stackbar according to order [\#1802](https://github.com/danielgindi/Charts/issues/1802)
- How to set multiple StackBar ? [\#1801](https://github.com/danielgindi/Charts/issues/1801)
- \[Swift 3\] LineChartData without xVals on constructor [\#1800](https://github.com/danielgindi/Charts/issues/1800)
- Zooming X and Y axis independently? [\#1799](https://github.com/danielgindi/Charts/issues/1799)
- Auto scroll xAxis [\#1798](https://github.com/danielgindi/Charts/issues/1798)
- How to add “%” to data in ios-chart [\#1797](https://github.com/danielgindi/Charts/issues/1797)
- can we set y axis max value to chart on horizontal bar graphs?? [\#1794](https://github.com/danielgindi/Charts/issues/1794)
- Fix the xAxis to use the values from my dataset  [\#1791](https://github.com/danielgindi/Charts/issues/1791)
- PieChart values outside of chart? [\#1790](https://github.com/danielgindi/Charts/issues/1790)
- ChartLimitLine: How to customize label background and Position center of line [\#1787](https://github.com/danielgindi/Charts/issues/1787)
- With Xcode 8.1 can't build [\#1786](https://github.com/danielgindi/Charts/issues/1786)
- CandleStickChartView can not inherit [\#1784](https://github.com/danielgindi/Charts/issues/1784)
- Chart adds a -1 x-axis entry [\#1782](https://github.com/danielgindi/Charts/issues/1782)
- BarChart - Is It possible to limit the number of bars visible at a given time, and allow users to scroll through the rest horizontally ? [\#1781](https://github.com/danielgindi/Charts/issues/1781)
- Unable to Override some properties of LineChartView in Charts version 2.2.5 [\#1777](https://github.com/danielgindi/Charts/issues/1777)
- CandleStickChartView how to fixed-width, or based on the data dynamically calculate the zoom factor [\#1776](https://github.com/danielgindi/Charts/issues/1776)
- Scroll UIImageView on X-axes along with x-vals [\#1775](https://github.com/danielgindi/Charts/issues/1775)
- How to make BarChart Bar Sizes proportional? [\#1774](https://github.com/danielgindi/Charts/issues/1774)
- how can I set the spacing between bars ? [\#1773](https://github.com/danielgindi/Charts/issues/1773)
- Charts 3.0 labelCount bug? [\#1767](https://github.com/danielgindi/Charts/issues/1767)
- use lineCharts ,if i want show custom string at x, don't show int number, how to do it? [\#1760](https://github.com/danielgindi/Charts/issues/1760)
- ChartUtils Crash on 32 Bit Devices [\#1737](https://github.com/danielgindi/Charts/issues/1737)

**Merged pull requests:**

- Added a check against NaN [\#1733](https://github.com/danielgindi/Charts/pull/1733) ([Selficide](https://github.com/Selficide))

## [2.3.1](https://github.com/danielgindi/Charts/tree/2.3.1) (2016-11-04)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.3.1...2.3.1)

## [v2.3.1](https://github.com/danielgindi/Charts/tree/v2.3.1) (2016-11-04)
[Full Changelog](https://github.com/danielgindi/Charts/compare/3.0.0...v2.3.1)

**Fixed bugs:**

- \[BUG\] multiple bar chart in ChartsDemo not display full for last x value [\#1770](https://github.com/danielgindi/Charts/issues/1770)
- PieChartDataSet no slice space shown when an entry has 0 value [\#1730](https://github.com/danielgindi/Charts/issues/1730)
- Bar on BarChart and CombinedChart doesn't start one Zero line [\#1729](https://github.com/danielgindi/Charts/issues/1729)
- Weird highlight rect in Stacked Bar Chart Negative [\#1726](https://github.com/danielgindi/Charts/issues/1726)
- CombinedChartView with CandleData - first/last candlesticks are cut in half [\#1717](https://github.com/danielgindi/Charts/issues/1717)
- Bug with showing negative values and vertical zooming Horizontal Bar Chart [\#1702](https://github.com/danielgindi/Charts/issues/1702)
- Setting circleHoleColor to clear causes artifact [\#1691](https://github.com/danielgindi/Charts/issues/1691)
- Charts 3.0 Carthage update issue [\#1690](https://github.com/danielgindi/Charts/issues/1690)

**Closed issues:**

- Problem with Formatter [\#1779](https://github.com/danielgindi/Charts/issues/1779)
- Could not cast value of type 'Charts.BarChartData' \(0x109bd1010\) to 'Charts.PieChartData' \(0x109be0320\). [\#1778](https://github.com/danielgindi/Charts/issues/1778)
- BarChartView - The first column and last column show only half? [\#1772](https://github.com/danielgindi/Charts/issues/1772)
- Stacked Bar Chart Opacity of Some Colors [\#1769](https://github.com/danielgindi/Charts/issues/1769)
- Custom Format YAxis [\#1768](https://github.com/danielgindi/Charts/issues/1768)
- to run ChartsDemo [\#1765](https://github.com/danielgindi/Charts/issues/1765)
- There is something wrong with LineChartView,ChartDataEntry,LineChartData and ChartDataEntry [\#1764](https://github.com/danielgindi/Charts/issues/1764)
- Strange margin/space in a grouped BarChart [\#1763](https://github.com/danielgindi/Charts/issues/1763)
- How to get the chart data entry points [\#1762](https://github.com/danielgindi/Charts/issues/1762)
- the newest version ,it can't build successes in xcode8,it pause at half,no stop or fail. [\#1761](https://github.com/danielgindi/Charts/issues/1761)
- Sorry， I no speak English， 3.0版本的LineChartView [\#1759](https://github.com/danielgindi/Charts/issues/1759)
- How to set x-axis labels with ios charts [\#1758](https://github.com/danielgindi/Charts/issues/1758)
- After 3.0 Upgrade, Marker not automatically showing [\#1757](https://github.com/danielgindi/Charts/issues/1757)
- How to set horizontal Value with a string array at my code using Charts 3.0 [\#1754](https://github.com/danielgindi/Charts/issues/1754)
- Issue with centering xAxis labels [\#1750](https://github.com/danielgindi/Charts/issues/1750)
- Charts / Swift 3.0 - Fatal error: Index out of range [\#1749](https://github.com/danielgindi/Charts/issues/1749)
- ChartsDemo dependency on realm-cocoa ~\> 1.1 issue [\#1748](https://github.com/danielgindi/Charts/issues/1748)
- Carthage realm-cocoa ~\> 1.1 dependency conflict [\#1747](https://github.com/danielgindi/Charts/issues/1747)
- how to set a Array of String for X-axis  in swift 3? [\#1746](https://github.com/danielgindi/Charts/issues/1746)
- Xcode 8.1 is reporting 'No such Module 'Charts''  [\#1743](https://github.com/danielgindi/Charts/issues/1743)
- Swift: Custom Xaxis and Yaxis problem. [\#1740](https://github.com/danielgindi/Charts/issues/1740)
- xAxis value starting at "-1" instead of "0" in grouped barchart [\#1739](https://github.com/danielgindi/Charts/issues/1739)
- Compiling issue - xcode 8  [\#1736](https://github.com/danielgindi/Charts/issues/1736)
- How to make Radar five top angle tap enabled? [\#1735](https://github.com/danielgindi/Charts/issues/1735)
- Strings for Labels in Swift 3 [\#1732](https://github.com/danielgindi/Charts/issues/1732)
- HorizontalBarChart values not showing Charts 3.0 [\#1731](https://github.com/danielgindi/Charts/issues/1731)
- getMarkerPositionWithHighlight: always return CGPointZero [\#1728](https://github.com/danielgindi/Charts/issues/1728)
- Why drawValues is outside of clipping contentRect [\#1727](https://github.com/danielgindi/Charts/issues/1727)
- Set max highlight distance [\#1725](https://github.com/danielgindi/Charts/issues/1725)
- Trouble making custom labels along the X and Y Axis of my bar chart [\#1724](https://github.com/danielgindi/Charts/issues/1724)
- Swift 3 - BarChartData, resetLabelsToSkip\(\) and setLabelsToSkip\(0\) not working [\#1723](https://github.com/danielgindi/Charts/issues/1723)
- When zooming in, x-axis shows more labels than values [\#1722](https://github.com/danielgindi/Charts/issues/1722)
-  How to set the xValues to the BarchartData ? [\#1721](https://github.com/danielgindi/Charts/issues/1721)
- How to show custom text on Y-axis of Line Chart? [\#1720](https://github.com/danielgindi/Charts/issues/1720)
- Add UIButtons to data points in a line graph, plotting date and time in X-axis [\#1719](https://github.com/danielgindi/Charts/issues/1719)
- How to set the xValues to the BarchartData ? [\#1718](https://github.com/danielgindi/Charts/issues/1718)
- \<Error\>: Error: this application, or a library it uses, has passed an invalid numeric value \(NaN, or not-a-number\) to CoreGraphics API and this value is being ignored.Please fix this problem. [\#1714](https://github.com/danielgindi/Charts/issues/1714)
- cancel group display in BarChart [\#1713](https://github.com/danielgindi/Charts/issues/1713)
- PieChart, label is a little of values ,labels is overlap [\#1710](https://github.com/danielgindi/Charts/issues/1710)
- how to port calls to getEntriesAtIndex [\#1709](https://github.com/danielgindi/Charts/issues/1709)
- Exception upon calling \[ChartDataEntry initWithX:y:\] via ObjC + Charts using Pods as Framework [\#1708](https://github.com/danielgindi/Charts/issues/1708)
- Multiple bar Chart Xvalues Doesn't come centre to the grouped bars... [\#1707](https://github.com/danielgindi/Charts/issues/1707)
- \[BarChartDataEntry\] axis to accept NSString [\#1706](https://github.com/danielgindi/Charts/issues/1706)
- Conflict: Charts's Range conflict with Apple NSRange\(Swift 3: Range\). It makes me can not use the Apple's Range [\#1705](https://github.com/danielgindi/Charts/issues/1705)
- Excuse me, BarChartData the " initWithXVals:\(NSArray \*data\) dataSets:\(NSArray \*data\)"  in Swift 3.0 V  "XVals"  is what method is the？I can't find the way，Help me。 [\#1704](https://github.com/danielgindi/Charts/issues/1704)
- Excuse me,BarChartData the initWithXVals:\(NSArray \*data\)  dataSets:\(NSArray \*data\) in Swift 3.0 V What method is the？I can't find the way，Help me。 [\#1703](https://github.com/danielgindi/Charts/issues/1703)
- X-Axis and Y-Axis is not shown in swift 3 [\#1701](https://github.com/danielgindi/Charts/issues/1701)
- Can i use 2.2.5 version with Swift3 [\#1700](https://github.com/danielgindi/Charts/issues/1700)
- I update the pod to 3.0.0 but Xcode still wants to convert to Swift 3 [\#1699](https://github.com/danielgindi/Charts/issues/1699)
- \[Question\] Multiple lines with different units [\#1698](https://github.com/danielgindi/Charts/issues/1698)
- \[Question\] Missing intermediate values [\#1697](https://github.com/danielgindi/Charts/issues/1697)
- What happened to X-Axis labels in 3.0? [\#1696](https://github.com/danielgindi/Charts/issues/1696)
- What is the new delegate for selection of values in the chart swift 3.0 [\#1695](https://github.com/danielgindi/Charts/issues/1695)
- Can I have two different Yaxis scale on combined chart [\#1694](https://github.com/danielgindi/Charts/issues/1694)
- Zooming LineChart xAxis on 32bit device doesn't work correctly. [\#1693](https://github.com/danielgindi/Charts/issues/1693)
- How to show xAxis All value and Rotate Label? v3.0 [\#1692](https://github.com/danielgindi/Charts/issues/1692)
- \[LineChartView\] Wrong position Highlight and xAxis [\#1689](https://github.com/danielgindi/Charts/issues/1689)
- How to get back x-values for BarChart\[Charts 3 migration problem\] [\#1688](https://github.com/danielgindi/Charts/issues/1688)
- How to move data labels outside the chart. [\#1687](https://github.com/danielgindi/Charts/issues/1687)
- RadarChartView implementation [\#1686](https://github.com/danielgindi/Charts/issues/1686)
- problem with custom xaxis labels [\#1685](https://github.com/danielgindi/Charts/issues/1685)
- Update spec on cocoapods to 3.0 [\#1683](https://github.com/danielgindi/Charts/issues/1683)
- Pod install: SSL: certificate verification failed [\#1682](https://github.com/danielgindi/Charts/issues/1682)
- Setting a pieChartDataSet on 3.0 [\#1681](https://github.com/danielgindi/Charts/issues/1681)
- String value at x Axis of BarChart [\#1680](https://github.com/danielgindi/Charts/issues/1680)
- Zooming LineChart x axis on 32bit processor doesn't work as expected [\#1679](https://github.com/danielgindi/Charts/issues/1679)
- ChartLimitLine background color [\#1678](https://github.com/danielgindi/Charts/issues/1678)
- Left and right slide bar chart only half and remove background grid [\#1654](https://github.com/danielgindi/Charts/issues/1654)
- Setting centerAxisLabelsEnabled with a granularity set does not centre [\#1652](https://github.com/danielgindi/Charts/issues/1652)
- Realm and Charts 3.0 tutorial [\#1651](https://github.com/danielgindi/Charts/issues/1651)
- HorizontalBarChartView text for xValues -- charts 3.0 [\#1646](https://github.com/danielgindi/Charts/issues/1646)
- 'spaceBetweenLabels' not found on object XAxis [\#1643](https://github.com/danielgindi/Charts/issues/1643)
- this func -nsuiTouchesEnded does not work [\#1642](https://github.com/danielgindi/Charts/issues/1642)
- Adjust circle for selected point [\#1597](https://github.com/danielgindi/Charts/issues/1597)
- CombinedChartView fitBars property [\#1569](https://github.com/danielgindi/Charts/issues/1569)
- How to show x-axis labels only when value is a DataEntry in Line Chart? [\#1536](https://github.com/danielgindi/Charts/issues/1536)
- Why the coordinate points and the X axis the above data is not aligned? [\#1492](https://github.com/danielgindi/Charts/issues/1492)
- \*\*\*\*\*\*\*\*\*\*\*\*\* Migrating to Charts 3.0 \*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\* [\#1474](https://github.com/danielgindi/Charts/issues/1474)
- How to show ChartMarker in CombinedChart? [\#1454](https://github.com/danielgindi/Charts/issues/1454)
- How to set the data  margin Align right？ [\#1372](https://github.com/danielgindi/Charts/issues/1372)
- Correct date on x axis [\#1325](https://github.com/danielgindi/Charts/issues/1325)
- The chart above and the chart below how to align [\#1276](https://github.com/danielgindi/Charts/issues/1276)
- Unable to select BarChartView from storyboard \(OS X\)  [\#1197](https://github.com/danielgindi/Charts/issues/1197)

**Merged pull requests:**

- Fix png image using JPEG type when being saved on macOS [\#1783](https://github.com/danielgindi/Charts/pull/1783) ([petester42](https://github.com/petester42))
- Updated Width Constraints - Fixes \#1770 [\#1771](https://github.com/danielgindi/Charts/pull/1771) ([SumoSimo](https://github.com/SumoSimo))
- update cocoapods [\#1684](https://github.com/danielgindi/Charts/pull/1684) ([petester42](https://github.com/petester42))

## [3.0.0](https://github.com/danielgindi/Charts/tree/3.0.0) (2016-10-19)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v3.0.0...3.0.0)

## [v3.0.0](https://github.com/danielgindi/Charts/tree/v3.0.0) (2016-10-19)
[Full Changelog](https://github.com/danielgindi/Charts/compare/2.3.0...v3.0.0)

**Implemented enhancements:**

- Chart description is drawn on top of markers [\#1344](https://github.com/danielgindi/Charts/issues/1344)

**Fixed bugs:**

- CandleStickChart - last CandleStick cut in half [\#1668](https://github.com/danielgindi/Charts/issues/1668)
- X labels Issues [\#1639](https://github.com/danielgindi/Charts/issues/1639)
- Marker display is abnormal [\#1575](https://github.com/danielgindi/Charts/issues/1575)
- Misplaced  Radar Marker  [\#1556](https://github.com/danielgindi/Charts/issues/1556)
- fatal error: Double value cannot be converted to Int because it is either infinite or NaN [\#1550](https://github.com/danielgindi/Charts/issues/1550)
- Problem with the xValue begins at 0 [\#1547](https://github.com/danielgindi/Charts/issues/1547)
- \(v3.0 + Swift3.0\)LineChart crash when dataSets is a empty array [\#1491](https://github.com/danielgindi/Charts/issues/1491)
- \(v3.0 + swift3.0\) charts' top are cropped/overlapped in ChartsDemo [\#1489](https://github.com/danielgindi/Charts/issues/1489)
- \(v3.0 + Swift3.0\) Time Line Chart Demo crash [\#1488](https://github.com/danielgindi/Charts/issues/1488)
- Last Value doesn't appear in barCharts and lineCharts [\#1384](https://github.com/danielgindi/Charts/issues/1384)
- Distance between chart legend and the chart is to big in case of rotated labels [\#1360](https://github.com/danielgindi/Charts/issues/1360)
- xAxis labels not align with values in Multiple Bar Chart [\#1334](https://github.com/danielgindi/Charts/issues/1334)
- text value on the last entry is missing from line chart [\#1323](https://github.com/danielgindi/Charts/issues/1323)

**Closed issues:**

- Finding the RadarChartView center position in a view [\#1677](https://github.com/danielgindi/Charts/issues/1677)
- How do I Use IValueFormatter to format XAxis labels in swift? [\#1676](https://github.com/danielgindi/Charts/issues/1676)
- How to set lineChart type to cubic lines [\#1674](https://github.com/danielgindi/Charts/issues/1674)
- In IOS 10 Xcode 8,I got some issues [\#1673](https://github.com/danielgindi/Charts/issues/1673)
- the first and last point have a line  [\#1672](https://github.com/danielgindi/Charts/issues/1672)
- Last value on line graph has no text [\#1671](https://github.com/danielgindi/Charts/issues/1671)
- can I fix number of overlapping this problem？ [\#1670](https://github.com/danielgindi/Charts/issues/1670)
- chartValueSelected delegate not called [\#1669](https://github.com/danielgindi/Charts/issues/1669)
- Charts.frameworkiOS did not show up [\#1667](https://github.com/danielgindi/Charts/issues/1667)
- Half a  bar the first one of chart [\#1666](https://github.com/danielgindi/Charts/issues/1666)
- Charts : iOS 10, Xcode 8 - Embedded Framework  iTunes Store operation failed while uploading on App Store [\#1665](https://github.com/danielgindi/Charts/issues/1665)
- We need an old version for Xcode 7.0  [\#1664](https://github.com/danielgindi/Charts/issues/1664)
- LineChartView is rotating LineChartData in 3.0 [\#1663](https://github.com/danielgindi/Charts/issues/1663)
- Showing all xAxis labels in a BarChart no matter the number of bars [\#1661](https://github.com/danielgindi/Charts/issues/1661)
- Can I set different color  Label in lineChart？ForExample [\#1660](https://github.com/danielgindi/Charts/issues/1660)
- Combined Charts - Negative values for Line Chart are not rendered [\#1659](https://github.com/danielgindi/Charts/issues/1659)
- Swift 2.2 : How to show custom labels on x-axis ? [\#1658](https://github.com/danielgindi/Charts/issues/1658)
- Legends for multiple dataSets and multiple bar charts allignment [\#1657](https://github.com/danielgindi/Charts/issues/1657)
- Multi chart setup xAxis value format [\#1656](https://github.com/danielgindi/Charts/issues/1656)
- xAxis when zooming [\#1655](https://github.com/danielgindi/Charts/issues/1655)
- Request to solve a sort of bug [\#1653](https://github.com/danielgindi/Charts/issues/1653)
- Updating the last Candlestick entry. [\#1649](https://github.com/danielgindi/Charts/issues/1649)
- Use of '@import' when modules are disabled [\#1647](https://github.com/danielgindi/Charts/issues/1647)
- Didn't show last Y Axis [\#1645](https://github.com/danielgindi/Charts/issues/1645)
- noDataText is not working [\#1644](https://github.com/danielgindi/Charts/issues/1644)
- Custom background color [\#1641](https://github.com/danielgindi/Charts/issues/1641)
- Cannot get my Linechart to work since Xcode 8 update [\#1640](https://github.com/danielgindi/Charts/issues/1640)
- Horizontal Highlight at touch/click point... ?? [\#1636](https://github.com/danielgindi/Charts/issues/1636)
- Some acute questions about ZDLineChartView [\#1635](https://github.com/danielgindi/Charts/issues/1635)
- \[Travis\] Travis won't build issue [\#1633](https://github.com/danielgindi/Charts/issues/1633)
- PieChartView cannot show values name . [\#1632](https://github.com/danielgindi/Charts/issues/1632)
- How to make points interactive/draggable [\#1631](https://github.com/danielgindi/Charts/issues/1631)
- Charts 3.0 with Swift 3.0 [\#1630](https://github.com/danielgindi/Charts/issues/1630)
- LineChartData is nolonger allow initWithXVals, what should I do? [\#1629](https://github.com/danielgindi/Charts/issues/1629)
- BarChartData do not find this method initWithXVals: dataSets: [\#1628](https://github.com/danielgindi/Charts/issues/1628)
- Charts 3.0: How to set x-values for a line chart? [\#1627](https://github.com/danielgindi/Charts/issues/1627)
- How can I display the x Axis values on a LineChartView? [\#1625](https://github.com/danielgindi/Charts/issues/1625)
- Xcode - 8.0, Working Fine in Xcode 7.3  -up to iOS\(9.3\) Crash in Xcode 8 - iOS 10   [\#1624](https://github.com/danielgindi/Charts/issues/1624)
- I can't see the final number on the chart. [\#1623](https://github.com/danielgindi/Charts/issues/1623)
- can I Achieve this? Using this framework [\#1622](https://github.com/danielgindi/Charts/issues/1622)
- When Will Charts 3.0 Be Version Tagged? [\#1620](https://github.com/danielgindi/Charts/issues/1620)
- RadarChartData xVals cant use [\#1619](https://github.com/danielgindi/Charts/issues/1619)
- Extract ChartsRealm and Realm from the Charts project [\#1618](https://github.com/danielgindi/Charts/issues/1618)
- Crashing xcode 7.3.1  [\#1617](https://github.com/danielgindi/Charts/issues/1617)
- Method does not override any method from its superclass [\#1616](https://github.com/danielgindi/Charts/issues/1616)
- warning: Swift Charts 2.3.0 - error in module ChartsDebug info from this module will be unavailable in the debugger. [\#1615](https://github.com/danielgindi/Charts/issues/1615)
- ChartViewDelegate's chartValueSelected is tied up with Highlights [\#1614](https://github.com/danielgindi/Charts/issues/1614)
- 'ChartViewDelegate' is unavailable: cannot find Swift declaration for this class. [\#1613](https://github.com/danielgindi/Charts/issues/1613)
- \[NOT AN ISSUE\] Release for Charts 3.0 [\#1612](https://github.com/danielgindi/Charts/issues/1612)
- Charts/Classes/Utils/ChartUtils.swift error [\#1611](https://github.com/danielgindi/Charts/issues/1611)
- Issue with YAxis max and min in the RadarChart [\#1610](https://github.com/danielgindi/Charts/issues/1610)
- App Crash: could not cast to 'Charts.PieChartData': in 2.3.0 when upgrading to XCode 8.0 [\#1609](https://github.com/danielgindi/Charts/issues/1609)
- customAxisMin and customAxisMax not found on object ChartYAxis [\#1608](https://github.com/danielgindi/Charts/issues/1608)
- Using version 3.0 with Carthage? [\#1607](https://github.com/danielgindi/Charts/issues/1607)
- Chart 3.0 swift 3.0 [\#1606](https://github.com/danielgindi/Charts/issues/1606)
- Chart not centered/middled when having less values [\#1605](https://github.com/danielgindi/Charts/issues/1605)
- How to use a date/string for the X value [\#1604](https://github.com/danielgindi/Charts/issues/1604)
- Setting AxisBase.axisMaximum results in an axisRange of zero [\#1603](https://github.com/danielgindi/Charts/issues/1603)
- Pods failing for swift3.0 [\#1602](https://github.com/danielgindi/Charts/issues/1602)
- BarChart View issues [\#1601](https://github.com/danielgindi/Charts/issues/1601)
- Support for swift 3.0 [\#1600](https://github.com/danielgindi/Charts/issues/1600)
- Pod pointing to master branch code created issue in BarChartDataEntry [\#1599](https://github.com/danielgindi/Charts/issues/1599)
- Swift based library, but OC based Demo. Have you ever thought of provide a Swifty Demo? [\#1598](https://github.com/danielgindi/Charts/issues/1598)
- Grid and  xaxis label issues. [\#1596](https://github.com/danielgindi/Charts/issues/1596)
- LeftAxis above bars in BarChartView [\#1595](https://github.com/danielgindi/Charts/issues/1595)
- cannot convert value of type\<\> to expected arguments RLMResults\<RLMObjects\> [\#1594](https://github.com/danielgindi/Charts/issues/1594)
- Member 'index' cannot be used on value of protocol type 'collection'; use a generic constraint instead [\#1593](https://github.com/danielgindi/Charts/issues/1593)
- Scrollable Graph [\#1592](https://github.com/danielgindi/Charts/issues/1592)
- charts 2.3 unsupported architectures i386 [\#1591](https://github.com/danielgindi/Charts/issues/1591)
- BarChart doesn't refresh after new dataSet [\#1590](https://github.com/danielgindi/Charts/issues/1590)
- Label of ChartLimitLine overlaps limit line a little [\#1589](https://github.com/danielgindi/Charts/issues/1589)
- IChartAxisValueFormatter cound't found [\#1588](https://github.com/danielgindi/Charts/issues/1588)
- How to use v2.2.5 swift3.0 in OC Project? [\#1587](https://github.com/danielgindi/Charts/issues/1587)
- Last LineChart y-label not being rendered [\#1585](https://github.com/danielgindi/Charts/issues/1585)
- Charts 3.0: Custom xAxis labels with gaps in data? [\#1584](https://github.com/danielgindi/Charts/issues/1584)
- BarChart xAxis labels disappear after zooming [\#1583](https://github.com/danielgindi/Charts/issues/1583)
- Add space between bars [\#1582](https://github.com/danielgindi/Charts/issues/1582)
- How to use v2.2.5 swift3.0 in OC Project? [\#1581](https://github.com/danielgindi/Charts/issues/1581)
- malloc: \*\*\* error for object : Invalid pointer dequeued from free list\*\*\* set a breakpoint in malloc\_error\_break to debug [\#1580](https://github.com/danielgindi/Charts/issues/1580)
- i want to add % in value [\#1579](https://github.com/danielgindi/Charts/issues/1579)
- Update the code to Swift 3.0 compliance  [\#1578](https://github.com/danielgindi/Charts/issues/1578)
- Can I set a text label underneath each bar of a bar chart? [\#1577](https://github.com/danielgindi/Charts/issues/1577)
- Line Chart X-Axis values as a string [\#1576](https://github.com/danielgindi/Charts/issues/1576)
- Hide values in Combines chart [\#1574](https://github.com/danielgindi/Charts/issues/1574)
- Method does not override any method from its superclass [\#1573](https://github.com/danielgindi/Charts/issues/1573)
-  Method does not override any method from its superclass [\#1572](https://github.com/danielgindi/Charts/issues/1572)
- Crash in Charts v2.2.5-Swift3.0 [\#1570](https://github.com/danielgindi/Charts/issues/1570)
- Cocoapods integration with objective c projects - can't resolve [\#1568](https://github.com/danielgindi/Charts/issues/1568)
- How to change x value [\#1567](https://github.com/danielgindi/Charts/issues/1567)
- \[Charts 3.0\] Issue with centerAxisLabelsEnabled [\#1566](https://github.com/danielgindi/Charts/issues/1566)
- \[Chart 3.0 BUG\] xAxis labels disappear after a deep zoom in [\#1564](https://github.com/danielgindi/Charts/issues/1564)
- Extraneous space between axis and bars when there is data with value of 0 [\#1557](https://github.com/danielgindi/Charts/issues/1557)
- Chart clip the Ballon Marker [\#1555](https://github.com/danielgindi/Charts/issues/1555)
- Argument labels '\(value:, xIndex:\)' do not match any available overloads, Cannot invoke initializer for type 'BarChartData' with an argument list of type '\(xVals: \[String\], dataSets: \[BarChartDataSet\]\)' [\#1554](https://github.com/danielgindi/Charts/issues/1554)
- Chart clip the Ballon Marker [\#1553](https://github.com/danielgindi/Charts/issues/1553)
- horizontal bar graphs with different colors and offset values [\#1552](https://github.com/danielgindi/Charts/issues/1552)
- valueFormatter API changes in Swift 3 [\#1551](https://github.com/danielgindi/Charts/issues/1551)
- Calling `setAxisMaxValue` leads to transform error. [\#1549](https://github.com/danielgindi/Charts/issues/1549)
- Demo in Swift? [\#1548](https://github.com/danielgindi/Charts/issues/1548)
- The LineChart is not working swift 3 [\#1546](https://github.com/danielgindi/Charts/issues/1546)
- Charts framework not usable in a Swift class added to an Objective C project [\#1545](https://github.com/danielgindi/Charts/issues/1545)
- How to add charts cocoapods dependency for Swift 3 project? [\#1544](https://github.com/danielgindi/Charts/issues/1544)
- Grouped bars in Combined chart [\#1543](https://github.com/danielgindi/Charts/issues/1543)
- I want to control  width between bar  [\#1542](https://github.com/danielgindi/Charts/issues/1542)
- Crash while using Combined Chart [\#1540](https://github.com/danielgindi/Charts/issues/1540)
- how can i draw only bar chart without x-axis, y-axis , grid line [\#1539](https://github.com/danielgindi/Charts/issues/1539)
- Charts.framework iOS not exist [\#1538](https://github.com/danielgindi/Charts/issues/1538)
- can i set data's starting point? [\#1537](https://github.com/danielgindi/Charts/issues/1537)
- Scatter Chart View V3.0 an another problem with balloon [\#1535](https://github.com/danielgindi/Charts/issues/1535)
- Granularity in line chart not working \(Chart3.0-Swift2.3 \) [\#1534](https://github.com/danielgindi/Charts/issues/1534)
- Error in ChartsPlatform.swift [\#1533](https://github.com/danielgindi/Charts/issues/1533)
- Still got an error when building Charts\(2.3.0\) with carthage [\#1532](https://github.com/danielgindi/Charts/issues/1532)
- Horizontal bar chart xAxis values missing [\#1531](https://github.com/danielgindi/Charts/issues/1531)
- Selection in a bar chart [\#1530](https://github.com/danielgindi/Charts/issues/1530)
- Multiple bar is not working [\#1529](https://github.com/danielgindi/Charts/issues/1529)
- X -axis - time , sometimes incorrectly displayed \( many of the data points \) [\#1528](https://github.com/danielgindi/Charts/issues/1528)
- Convert x value labels to String? from Double? \[Horizontal Barchart\] \(Swift v3\) [\#1527](https://github.com/danielgindi/Charts/issues/1527)
- Method doesn't override any method from its superclass [\#1526](https://github.com/danielgindi/Charts/issues/1526)
- Bar charts X-Axis labels repeating if the number of bars reduce [\#1524](https://github.com/danielgindi/Charts/issues/1524)
- The latest version can not run [\#1523](https://github.com/danielgindi/Charts/issues/1523)
- how to show the Chart without data? [\#1522](https://github.com/danielgindi/Charts/issues/1522)
- The usage is not correct [\#1521](https://github.com/danielgindi/Charts/issues/1521)
- Charts classes not found in swift class in my Objc Xcode project [\#1519](https://github.com/danielgindi/Charts/issues/1519)
- ChartUtils methods are marked internal and cannot be use to create custom charts  [\#1518](https://github.com/danielgindi/Charts/issues/1518)
- Re: Fatal error while loading Bar Chart \#1511 [\#1517](https://github.com/danielgindi/Charts/issues/1517)
- I can't run this demo! [\#1516](https://github.com/danielgindi/Charts/issues/1516)
- Charts.framework iOS [\#1515](https://github.com/danielgindi/Charts/issues/1515)
- Setting x-values of BarChartView in Chart 2.2.6 [\#1514](https://github.com/danielgindi/Charts/issues/1514)
- Xcode 8.0,many errors...... [\#1513](https://github.com/danielgindi/Charts/issues/1513)
- Fatal error while loading Bar Chart [\#1511](https://github.com/danielgindi/Charts/issues/1511)
- Fix Intermittent CI Failures [\#1508](https://github.com/danielgindi/Charts/issues/1508)
- Info.plist Utility Error [\#1507](https://github.com/danielgindi/Charts/issues/1507)
- Bar chart "floats" above x-axis when axisMinValue set to 0 [\#1506](https://github.com/danielgindi/Charts/issues/1506)
- Issue with BarDataEntry Method [\#1504](https://github.com/danielgindi/Charts/issues/1504)
- \(v3.0 + Swift3.0\)IChartAxisValueFormatter's protocol function pass on a unexpected value\(double value discard\) [\#1503](https://github.com/danielgindi/Charts/issues/1503)
- Cannot invoke initializer for type 'LineChartData' [\#1502](https://github.com/danielgindi/Charts/issues/1502)
- when click node call chartValueSelected funtion [\#1499](https://github.com/danielgindi/Charts/issues/1499)
- \[BUG\] LineChart--- about "LineChartModeCubicBezier" [\#1490](https://github.com/danielgindi/Charts/issues/1490)
- Unneeded deprecated warning for Legend.position [\#1483](https://github.com/danielgindi/Charts/issues/1483)
- Scatter Chart View V3.0 bad point displayed with BaloonMaker [\#1482](https://github.com/danielgindi/Charts/issues/1482)
- How to set the  LineChart disconnect style, I add NAN to Entry, isInBoundsRight function is crashed. [\#1471](https://github.com/danielgindi/Charts/issues/1471)
- \(v3.0\)Demo: no such module realm  [\#1460](https://github.com/danielgindi/Charts/issues/1460)
- Please provide swift 2 version for this project [\#1457](https://github.com/danielgindi/Charts/issues/1457)
- Distinguish LineGraph callback. [\#1448](https://github.com/danielgindi/Charts/issues/1448)
- BarChart right/left bar cut in half [\#1446](https://github.com/danielgindi/Charts/issues/1446)
- Graph clipping  while setting Y-axis min ,max Value [\#1444](https://github.com/danielgindi/Charts/issues/1444)
- ChartV3 autoScaleMinMaxEnabled not scaling candle charts correctly. [\#1442](https://github.com/danielgindi/Charts/issues/1442)
- conflict with scrollView [\#1438](https://github.com/danielgindi/Charts/issues/1438)
- Getting error, while assigning xVals to PieChartData.  [\#1433](https://github.com/danielgindi/Charts/issues/1433)
- HorizontalBarChartView not show value at bar [\#1396](https://github.com/danielgindi/Charts/issues/1396)
- HorizontalBarCharView incorrect results for bounding box query [\#1382](https://github.com/danielgindi/Charts/issues/1382)
- Data entries are missing on the chart [\#1348](https://github.com/danielgindi/Charts/issues/1348)
- \[HEADS UP\] Realm swift 3.0 support is blocked due to Xcode 8 beta 3 swift compiler bug [\#1269](https://github.com/danielgindi/Charts/issues/1269)
- When the X-Axis's text is too long, it will cover the radar chart. [\#1143](https://github.com/danielgindi/Charts/issues/1143)
- Extracting ChartsRealm to a separate project [\#1119](https://github.com/danielgindi/Charts/issues/1119)
- Disable scrolling parent scrollview when readed end of the chart [\#1115](https://github.com/danielgindi/Charts/issues/1115)
- Pie Chart : Multiline label on legend is cutting off [\#1084](https://github.com/danielgindi/Charts/issues/1084)

**Merged pull requests:**

- Enter the matrix [\#1650](https://github.com/danielgindi/Charts/pull/1650) ([petester42](https://github.com/petester42))
- fix bar chart in demo that date starts at 0 [\#1648](https://github.com/danielgindi/Charts/pull/1648) ([liuxuan30](https://github.com/liuxuan30))
- fix \#1603 and API comment [\#1621](https://github.com/danielgindi/Charts/pull/1621) ([liuxuan30](https://github.com/liuxuan30))
- Bugfix for fix \#1488, \#1564 [\#1565](https://github.com/danielgindi/Charts/pull/1565) ([liuxuan30](https://github.com/liuxuan30))
- Single test target to make coverage easier [\#1563](https://github.com/danielgindi/Charts/pull/1563) ([petester42](https://github.com/petester42))
- Fix codecov [\#1560](https://github.com/danielgindi/Charts/pull/1560) ([petester42](https://github.com/petester42))
- Adds Codecov [\#1559](https://github.com/danielgindi/Charts/pull/1559) ([petester42](https://github.com/petester42))
- Fix decimals crash in ChartsUtil [\#1558](https://github.com/danielgindi/Charts/pull/1558) ([petester42](https://github.com/petester42))
- Fixes messaging issues with charts needing carthage [\#1525](https://github.com/danielgindi/Charts/pull/1525) ([petester42](https://github.com/petester42))
- Attempt to make CI more stable [\#1510](https://github.com/danielgindi/Charts/pull/1510) ([petester42](https://github.com/petester42))
- Fix Cocoapods setup being broken [\#1509](https://github.com/danielgindi/Charts/pull/1509) ([petester42](https://github.com/petester42))

## [2.3.0](https://github.com/danielgindi/Charts/tree/2.3.0) (2016-09-21)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.3.0...2.3.0)

## [v2.3.0](https://github.com/danielgindi/Charts/tree/v2.3.0) (2016-09-21)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.2.5...v2.3.0)

**Implemented enhancements:**

- Seek help with CandleStickChartView [\#1297](https://github.com/danielgindi/Charts/issues/1297)
- Support Realm 1.0.1 \(Objective-C\) [\#1201](https://github.com/danielgindi/Charts/issues/1201)

**Fixed bugs:**

- macOS demo bar chart's first and last bar got half cut [\#1468](https://github.com/danielgindi/Charts/issues/1468)
- Label drawing glitch when changing data [\#1347](https://github.com/danielgindi/Charts/issues/1347)
- HorizontalBarChartView with multiple data set crashes on tap [\#1339](https://github.com/danielgindi/Charts/issues/1339)
- weird behavior resizing the pie. [\#1286](https://github.com/danielgindi/Charts/issues/1286)
- Line Chart\(Dual YAis\):if i drag the slider,double line only leave the one line \#bug [\#1250](https://github.com/danielgindi/Charts/issues/1250)
- only Have a X-Value,the PieChart too bigger, twinkle [\#1239](https://github.com/danielgindi/Charts/issues/1239)
- Chart always crash when marker added and highlightFullBarEnabled is true [\#1185](https://github.com/danielgindi/Charts/issues/1185)
- App is crashing for certain symbols when granularityEnabled is false [\#1181](https://github.com/danielgindi/Charts/issues/1181)
- Changing chart data + autoScaleMinMaxEnabled causes crash [\#1151](https://github.com/danielgindi/Charts/issues/1151)
- using ChartXAxisValueFormatter doesn't enable 3 lines x axis labels [\#1148](https://github.com/danielgindi/Charts/issues/1148)
- ChartMaker work wrong in a Combined Chart [\#737](https://github.com/danielgindi/Charts/issues/737)
- CombinedChart with grouped bars support [\#169](https://github.com/danielgindi/Charts/issues/169)

**Closed issues:**

- Typo in CONTRIBUTING.md [\#1501](https://github.com/danielgindi/Charts/issues/1501)
- noDataText overflows [\#1500](https://github.com/danielgindi/Charts/issues/1500)
- How can I get frame of Line chart? \(with out labels, chart only\) [\#1498](https://github.com/danielgindi/Charts/issues/1498)
- Last label on xAxis is not showing [\#1497](https://github.com/danielgindi/Charts/issues/1497)
- How do I set the value of the x-axis after update ? [\#1496](https://github.com/danielgindi/Charts/issues/1496)
- Xcode 8 swift 3  No Such Module Charts? [\#1494](https://github.com/danielgindi/Charts/issues/1494)
- Give id for node [\#1493](https://github.com/danielgindi/Charts/issues/1493)
- \(v3.0-Swift3.0\)crash because of image not found [\#1487](https://github.com/danielgindi/Charts/issues/1487)
- \(v3.0 LineChart\) xAxis with String/double label issue [\#1481](https://github.com/danielgindi/Charts/issues/1481)
- LineChart. Custom LineChartRenderer does not cross chart border [\#1480](https://github.com/danielgindi/Charts/issues/1480)
- LineChart. Labels for Y axis and Double format [\#1479](https://github.com/danielgindi/Charts/issues/1479)
- stringForValue value argument [\#1478](https://github.com/danielgindi/Charts/issues/1478)
- \[LineChart\] How to Darw "NSString" on xAxis? without Realm.io [\#1477](https://github.com/danielgindi/Charts/issues/1477)
- code 65 result after getting a Carthage update failure [\#1476](https://github.com/danielgindi/Charts/issues/1476)
- How to show xAxis All value and Rotate Label? [\#1475](https://github.com/danielgindi/Charts/issues/1475)
- Compiled with older version of Swift language \(2.0\)  [\#1473](https://github.com/danielgindi/Charts/issues/1473)
- Some missing methods in swift 3 version [\#1472](https://github.com/danielgindi/Charts/issues/1472)
- v2.2.5 not support swift2.3 [\#1470](https://github.com/danielgindi/Charts/issues/1470)
- balloon marker [\#1469](https://github.com/danielgindi/Charts/issues/1469)
- How can I highlight single dot in lineChart? [\#1465](https://github.com/danielgindi/Charts/issues/1465)
- release new [\#1464](https://github.com/danielgindi/Charts/issues/1464)
-  After the upgrade iOS10  Charts-iOS  Change the case [\#1463](https://github.com/danielgindi/Charts/issues/1463)
- \(v3.0\)There is no Chart-iOS framework in Product [\#1462](https://github.com/danielgindi/Charts/issues/1462)
- ios10 the charts can not display! [\#1461](https://github.com/danielgindi/Charts/issues/1461)
- Swift 3.0 and Realm [\#1458](https://github.com/danielgindi/Charts/issues/1458)
- Unable to draw bars [\#1456](https://github.com/danielgindi/Charts/issues/1456)
- \[MC\] System group container issue [\#1455](https://github.com/danielgindi/Charts/issues/1455)
- iOS 10 and swift 3.0 issues [\#1453](https://github.com/danielgindi/Charts/issues/1453)
- Adding additional X index values without the initWithXVals [\#1451](https://github.com/danielgindi/Charts/issues/1451)
- Cannot convert Charts to Swift 3.0 [\#1449](https://github.com/danielgindi/Charts/issues/1449)
- Combine chart have a Bar color gray every bars? [\#1447](https://github.com/danielgindi/Charts/issues/1447)
- Compiling on New XCode 8 [\#1445](https://github.com/danielgindi/Charts/issues/1445)
- Customize scatter chart color for each data in dataSets [\#1443](https://github.com/danielgindi/Charts/issues/1443)
- XCode 8: Crash in xAxis Renderer after upgrade [\#1441](https://github.com/danielgindi/Charts/issues/1441)
- Xcode 8 and swift 3.0 [\#1440](https://github.com/danielgindi/Charts/issues/1440)
- How To use Swift3.0 In Xcode8 [\#1439](https://github.com/danielgindi/Charts/issues/1439)
- Error when running `pod lib lint` from another project [\#1436](https://github.com/danielgindi/Charts/issues/1436)
- Graph cutting off from axisLine while setting axis min ,max Value [\#1435](https://github.com/danielgindi/Charts/issues/1435)
- Cannot compile for targets below iPhone 5s [\#1434](https://github.com/danielgindi/Charts/issues/1434)
- Line chartView, I need a YAxis with the labelNum is inverted order [\#1432](https://github.com/danielgindi/Charts/issues/1432)
- PieChartDataEntry class not found - Swift/CocoaPods [\#1431](https://github.com/danielgindi/Charts/issues/1431)
- How to write a limit for each bar in horizontal bar chart? [\#1429](https://github.com/danielgindi/Charts/issues/1429)
- Bug with 2 candlestick datasets on one chart. [\#1428](https://github.com/danielgindi/Charts/issues/1428)
- getFillLinePosition not calling to create Area graph [\#1427](https://github.com/danielgindi/Charts/issues/1427)
- Lack of documentation [\#1426](https://github.com/danielgindi/Charts/issues/1426)
- Best method to use this in Objective c project [\#1425](https://github.com/danielgindi/Charts/issues/1425)
- Highlight sync between combined and line views [\#1424](https://github.com/danielgindi/Charts/issues/1424)
- How to create two bars one over another for Horizontal bar charts? [\#1423](https://github.com/danielgindi/Charts/issues/1423)
- \[Swift 3.0 specific\] linear line chart has additional line segments wile zooming in [\#1421](https://github.com/danielgindi/Charts/issues/1421)
- Is there a way to import PieChartDataEntry.swift in existing Pod?  [\#1419](https://github.com/danielgindi/Charts/issues/1419)
- implement the same behavior as Android api [\#1418](https://github.com/danielgindi/Charts/issues/1418)
- Unable to build project in Xcode 8 [\#1417](https://github.com/danielgindi/Charts/issues/1417)
- Synchronizing scrolling/pan/highlight of multiple views. [\#1415](https://github.com/danielgindi/Charts/issues/1415)
- Custom X-Axis Values [\#1413](https://github.com/danielgindi/Charts/issues/1413)
- Query : When it will be available in CocoaPods? [\#1412](https://github.com/danielgindi/Charts/issues/1412)
- How to set background color of bars [\#1411](https://github.com/danielgindi/Charts/issues/1411)
- Query: Enable gridelines at specific location points. [\#1410](https://github.com/danielgindi/Charts/issues/1410)
- Syntax errors on one of our testing devices [\#1409](https://github.com/danielgindi/Charts/issues/1409)
- how to hide the ValueFormatter [\#1408](https://github.com/danielgindi/Charts/issues/1408)
- I can't see the zero\(float Num\)  on ChartYAxis [\#1407](https://github.com/danielgindi/Charts/issues/1407)
- drawSliceTextEnabled = YES not working [\#1404](https://github.com/danielgindi/Charts/issues/1404)
- Customize popup on entry tap [\#1403](https://github.com/danielgindi/Charts/issues/1403)
- Query: How to create an area graph? [\#1402](https://github.com/danielgindi/Charts/issues/1402)
- Negative Bar Chart [\#1401](https://github.com/danielgindi/Charts/issues/1401)
- Cannot invoke initializer for type 'PieChartData' error in Swift 2.3 [\#1400](https://github.com/danielgindi/Charts/issues/1400)
- Get entry location in view when chart value is selected? [\#1399](https://github.com/danielgindi/Charts/issues/1399)
- Can you give me a Charts library wrote by OC. [\#1398](https://github.com/danielgindi/Charts/issues/1398)
- Barchart,,,,draw specific value [\#1397](https://github.com/danielgindi/Charts/issues/1397)
- How to customize left axis with string values? [\#1394](https://github.com/danielgindi/Charts/issues/1394)
- Swift & CocoaPods - Unable to Utilize Charts [\#1393](https://github.com/danielgindi/Charts/issues/1393)
- How to create a Pie chart, no 'how to' ?   .   The demo app sucks, doesn't compile. [\#1392](https://github.com/danielgindi/Charts/issues/1392)
- Need help in setDrawValues [\#1391](https://github.com/danielgindi/Charts/issues/1391)
- Display Integer value [\#1390](https://github.com/danielgindi/Charts/issues/1390)
- BarChartData Class is Missing InitWithXVals [\#1385](https://github.com/danielgindi/Charts/issues/1385)
- Undeclared or undefined identifiers while trying to compile project [\#1383](https://github.com/danielgindi/Charts/issues/1383)
- How to show/hide one of the line chart during runtime [\#1381](https://github.com/danielgindi/Charts/issues/1381)
- Charts-Swift.h is different [\#1380](https://github.com/danielgindi/Charts/issues/1380)
- infinite recursion in HorizontalBarChartHighlighter [\#1377](https://github.com/danielgindi/Charts/issues/1377)
- Charts errors in xcode beta 6 [\#1375](https://github.com/danielgindi/Charts/issues/1375)
- please  tell me how do i  handling these problems，thanks！ [\#1373](https://github.com/danielgindi/Charts/issues/1373)
- Charts 3.0 and Swift 2.3 branch? [\#1370](https://github.com/danielgindi/Charts/issues/1370)
- Legend. Position  is deprecated. [\#1369](https://github.com/danielgindi/Charts/issues/1369)
- App Crash: An instance 0xXXXXXXXX of class Charts.BarChartView was deallocated while key value observers were still registered with it. [\#1368](https://github.com/danielgindi/Charts/issues/1368)
- Unable to compile adding Charts to project via CocoaPods [\#1367](https://github.com/danielgindi/Charts/issues/1367)
- Cannot use PieChartDataSet or PieChartDataEntry when include with Cocoapod [\#1366](https://github.com/danielgindi/Charts/issues/1366)
- Charts is ~20M when built. Desired? [\#1365](https://github.com/danielgindi/Charts/issues/1365)
- swift 3 branch does not compile with Xcode 8 beta 6 [\#1364](https://github.com/danielgindi/Charts/issues/1364)
- There are two lines. The first number starts from scratch, but only one is from zero. The other one has a certain amount of cheap quantity. How do I get the other one from zero. [\#1363](https://github.com/danielgindi/Charts/issues/1363)
- missing class PieChartDataEntry \(macOS\) [\#1358](https://github.com/danielgindi/Charts/issues/1358)
- BarChartView can't show max y value's top label, only show half [\#1357](https://github.com/danielgindi/Charts/issues/1357)
- Horizontal Bar Chart not showing values [\#1356](https://github.com/danielgindi/Charts/issues/1356)
- rightAxis labels are not showing for value less then 1.0 in swift using cocoapod [\#1354](https://github.com/danielgindi/Charts/issues/1354)
- Chart's X-axis labels not resizing to accommodate multi-line text [\#1352](https://github.com/danielgindi/Charts/issues/1352)
- Pie Chart Graph Unusual Visual When Data Of Zero [\#1351](https://github.com/danielgindi/Charts/issues/1351)
- Chart render problem after double tap zoom [\#1350](https://github.com/danielgindi/Charts/issues/1350)
- Chart Bar drawing  [\#1349](https://github.com/danielgindi/Charts/issues/1349)
- How to dynamically scale up / down data depends on zoom level [\#1346](https://github.com/danielgindi/Charts/issues/1346)
- Cannot install in ios9.3.2  [\#1345](https://github.com/danielgindi/Charts/issues/1345)
- Where can I find the exact color value of vordiplom colors? [\#1343](https://github.com/danielgindi/Charts/issues/1343)
- How to Modify X Axis values Swift? [\#1340](https://github.com/danielgindi/Charts/issues/1340)
- Line Chart : Data Line Color Change [\#1338](https://github.com/danielgindi/Charts/issues/1338)
- Line Chart x axis and y axis Line Color change? [\#1337](https://github.com/danielgindi/Charts/issues/1337)
- Grouped Bar Charts  [\#1333](https://github.com/danielgindi/Charts/issues/1333)
- Integer for ChartDataEntry [\#1332](https://github.com/danielgindi/Charts/issues/1332)
- values from array for x axis on barchart [\#1331](https://github.com/danielgindi/Charts/issues/1331)
- Redundant conformance of 'RLMResults' to protocol 'SequenceType' on RealmChartsUtils [\#1329](https://github.com/danielgindi/Charts/issues/1329)
- Combined Chart Grouped Bar Width Not Changing [\#1328](https://github.com/danielgindi/Charts/issues/1328)
- Undefined symbols for architecture armv7: [\#1327](https://github.com/danielgindi/Charts/issues/1327)
- Error with carthage update ChartsRealm fails to build [\#1326](https://github.com/danielgindi/Charts/issues/1326)
- I am not able to design multicolor bar chart [\#1324](https://github.com/danielgindi/Charts/issues/1324)
- v2.2.5 demo code is different with pod installed version [\#1322](https://github.com/danielgindi/Charts/issues/1322)
- If the all value is 1 or 0, the ChartYAxis is error [\#1321](https://github.com/danielgindi/Charts/issues/1321)
- hide on select grid lines in line chart [\#1320](https://github.com/danielgindi/Charts/issues/1320)
- X data as a Date [\#1317](https://github.com/danielgindi/Charts/issues/1317)
- Also played well, before package, NO error, the recent packaging error, the error is "NO Scuh module 'charts", don't know is what reason to solve, use the xcode 7.1 \(7 b91b\) [\#1316](https://github.com/danielgindi/Charts/issues/1316)
- Crash with marker attached into CombinedChartView [\#1315](https://github.com/danielgindi/Charts/issues/1315)
- How to add more than 3 lines in chart  [\#1314](https://github.com/danielgindi/Charts/issues/1314)
- is v3 Ready for production ? [\#1313](https://github.com/danielgindi/Charts/issues/1313)
- xAxis.axisMaxValue not working [\#1312](https://github.com/danielgindi/Charts/issues/1312)
- Symbol not found: \_\_TMVVSS17UnicodeScalarView9Generator   Referenced from: Frameworks/Charts.framework/Charts [\#1311](https://github.com/danielgindi/Charts/issues/1311)
- how to named SWIFT\_CLASS\("....."\) [\#1310](https://github.com/danielgindi/Charts/issues/1310)
- How to set the length and color outside pie chart when a slice is highlighted? [\#1309](https://github.com/danielgindi/Charts/issues/1309)
- How to set the length and color outside pie chart when a slice is highlighted? [\#1308](https://github.com/danielgindi/Charts/issues/1308)
- I want set circle empty in Line Chart. Which properties i have to change? [\#1307](https://github.com/danielgindi/Charts/issues/1307)
- noDataText font and color [\#1305](https://github.com/danielgindi/Charts/issues/1305)
- how to let centerAttributedText line feed in bar chart ? [\#1304](https://github.com/danielgindi/Charts/issues/1304)
- Can Legend be selected or multi-selected? [\#1303](https://github.com/danielgindi/Charts/issues/1303)
- add financial bar chart \(Open-high-low-close chart\) [\#1302](https://github.com/danielgindi/Charts/issues/1302)
- How to select the first pie in bar chart? [\#1301](https://github.com/danielgindi/Charts/issues/1301)
- PieChartView Narrow with one value [\#1298](https://github.com/danielgindi/Charts/issues/1298)
- xAxis.axisMaxValue is never considered [\#1293](https://github.com/danielgindi/Charts/issues/1293)
- Please add Swift demo project also [\#1292](https://github.com/danielgindi/Charts/issues/1292)
- Swift 2.3 & 3.0 support: news, issues, PRs and state [\#1291](https://github.com/danielgindi/Charts/issues/1291)
- 2 lines on the charts with the same values [\#1289](https://github.com/danielgindi/Charts/issues/1289)
- How to set the y axis descript text [\#1285](https://github.com/danielgindi/Charts/issues/1285)
- how to setTouchEnabled to false [\#1283](https://github.com/danielgindi/Charts/issues/1283)
- legend set different form for different set [\#1282](https://github.com/danielgindi/Charts/issues/1282)
- Change Label Text Color PieChart [\#1280](https://github.com/danielgindi/Charts/issues/1280)
- Everyone welcome to try out `v3` branch before Charts 3.0 is released! [\#1279](https://github.com/danielgindi/Charts/issues/1279)
- \[FR\] make Marker a protocol [\#1278](https://github.com/danielgindi/Charts/issues/1278)
- Unable to convert charts to Swift3 [\#1277](https://github.com/danielgindi/Charts/issues/1277)
- Start at zero and draw cubic [\#1275](https://github.com/danielgindi/Charts/issues/1275)
- Customised size of x data points and Scatter Graph with Linear Trend Line [\#1274](https://github.com/danielgindi/Charts/issues/1274)
- How can i add null values to my chart? [\#1273](https://github.com/danielgindi/Charts/issues/1273)
- PIE-CHART how to get the index of the selected slice.??? [\#1267](https://github.com/danielgindi/Charts/issues/1267)
- How to select a value in line chart data point and same for bar chart [\#1265](https://github.com/danielgindi/Charts/issues/1265)
- update to the latest realm [\#1262](https://github.com/danielgindi/Charts/issues/1262)
- horizontal bar graph value different decimal places each time [\#1260](https://github.com/danielgindi/Charts/issues/1260)
- Charts vs. ShinobiCharts? [\#1259](https://github.com/danielgindi/Charts/issues/1259)
- LineChart overlap value label and y-Axis [\#1257](https://github.com/danielgindi/Charts/issues/1257)
- how to hide stacked bar chart label having 0 values [\#1256](https://github.com/danielgindi/Charts/issues/1256)
- fillColor for negative values [\#1255](https://github.com/danielgindi/Charts/issues/1255)
- Set custom x-value spacing in graph [\#1254](https://github.com/danielgindi/Charts/issues/1254)
- Hide specific labels on y axis [\#1253](https://github.com/danielgindi/Charts/issues/1253)
- Marker near edges got clipped [\#1251](https://github.com/danielgindi/Charts/issues/1251)
- Custom label for LineChart [\#1248](https://github.com/danielgindi/Charts/issues/1248)
- Chart in TableViewCell [\#1247](https://github.com/danielgindi/Charts/issues/1247)
- Line Chart : Can I drag a vertical line to any point on the screen and get values? Instead of individual touches [\#1246](https://github.com/danielgindi/Charts/issues/1246)
- Line Chart label count not calculating properly [\#1244](https://github.com/danielgindi/Charts/issues/1244)
- CandleChartView screen  show count limit [\#1243](https://github.com/danielgindi/Charts/issues/1243)
- addEntryOrdered behavior - clarification [\#1242](https://github.com/danielgindi/Charts/issues/1242)
- Display Max Value on X-axis [\#1241](https://github.com/danielgindi/Charts/issues/1241)
- Value not display for Multiple DataSet in LineChart [\#1240](https://github.com/danielgindi/Charts/issues/1240)
- rightAxis are rounded [\#1238](https://github.com/danielgindi/Charts/issues/1238)
- Now working if “Compile Sources As” option to “Objective C++” [\#1237](https://github.com/danielgindi/Charts/issues/1237)
- Line is drawn below the x axis when the drawCubicEnabled is YES. [\#1236](https://github.com/danielgindi/Charts/issues/1236)
- Label value is cut off in horizontal bar [\#1235](https://github.com/danielgindi/Charts/issues/1235)
- ChartValueSelected Only While Finger Pressed [\#1234](https://github.com/danielgindi/Charts/issues/1234)
- YAxis Labels are clipped with some settings [\#1233](https://github.com/danielgindi/Charts/issues/1233)
- xVals隐藏问题 [\#1232](https://github.com/danielgindi/Charts/issues/1232)
- About the Lable number of x and y [\#1231](https://github.com/danielgindi/Charts/issues/1231)
- How to control not draws a specified point [\#1229](https://github.com/danielgindi/Charts/issues/1229)
- Location have a little problem [\#1228](https://github.com/danielgindi/Charts/issues/1228)
- BarChartView bars do not start at the x-axis. [\#1225](https://github.com/danielgindi/Charts/issues/1225)
- Unable to run on iOS/tvOS 10.0 beta [\#1223](https://github.com/danielgindi/Charts/issues/1223)
- two CombinedChartView's in the same screen changes axis format in the first  [\#1222](https://github.com/danielgindi/Charts/issues/1222)
- Horizontal bar chart crashing after tap when more than one data set [\#1221](https://github.com/danielgindi/Charts/issues/1221)
-  no suitable image found.  [\#1220](https://github.com/danielgindi/Charts/issues/1220)
- wrong offset when using mutiple bar chart in combined chart [\#1219](https://github.com/danielgindi/Charts/issues/1219)
- UnsafeMutablePointer.moveInitializeFrom with negative count [\#1218](https://github.com/danielgindi/Charts/issues/1218)
- Is there any way to hide the label of a DataSet? [\#1217](https://github.com/danielgindi/Charts/issues/1217)
- How can I not be highlighted when I touch ended ?  [\#1216](https://github.com/danielgindi/Charts/issues/1216)
- poor styling documentation, how can i make my charts look beautiful? [\#1215](https://github.com/danielgindi/Charts/issues/1215)
- X Axis for Candle Sticks from Realm [\#1214](https://github.com/danielgindi/Charts/issues/1214)
- drawHighlighted\(\) not calling [\#1212](https://github.com/danielgindi/Charts/issues/1212)
- how to set marker for line chart [\#1211](https://github.com/danielgindi/Charts/issues/1211)
- Error when using Charts library with Xcode 8 bet and Swift 3.0 [\#1210](https://github.com/danielgindi/Charts/issues/1210)
- I have an immature small suggestion [\#1209](https://github.com/danielgindi/Charts/issues/1209)
- I have a question BarChart [\#1208](https://github.com/danielgindi/Charts/issues/1208)
- HorizontalBarChart in Combined Chart [\#1207](https://github.com/danielgindi/Charts/issues/1207)
- drawvalue when stack is selected [\#1200](https://github.com/danielgindi/Charts/issues/1200)
- Remove labels from Radar Chart [\#1199](https://github.com/danielgindi/Charts/issues/1199)
- reduce height of grids [\#1198](https://github.com/danielgindi/Charts/issues/1198)
- reduce grid size [\#1194](https://github.com/danielgindi/Charts/issues/1194)
- ChartsView [\#1189](https://github.com/danielgindi/Charts/issues/1189)
- Custom LineChart marker image [\#1188](https://github.com/danielgindi/Charts/issues/1188)
- moveViewToAnimatedWithXIndex high cpu usage [\#1186](https://github.com/danielgindi/Charts/issues/1186)
- can we implement group bar chart and line chart as combined chart [\#1184](https://github.com/danielgindi/Charts/issues/1184)
- Such a difficult chart, can be realized? [\#1183](https://github.com/danielgindi/Charts/issues/1183)
- How to achieve the effect of the picture? Please look carefully [\#1182](https://github.com/danielgindi/Charts/issues/1182)
- Designed initializer of BarChartDataSet crash [\#1180](https://github.com/danielgindi/Charts/issues/1180)
- To judge whether the initial state [\#1178](https://github.com/danielgindi/Charts/issues/1178)
- use cocoa pods mistakes [\#1177](https://github.com/danielgindi/Charts/issues/1177)
- hide description label on charts [\#1176](https://github.com/danielgindi/Charts/issues/1176)
- axis values are rounded [\#1175](https://github.com/danielgindi/Charts/issues/1175)
- how can i add a % on leftAxis [\#1174](https://github.com/danielgindi/Charts/issues/1174)
- Add percent label for pie chart [\#1172](https://github.com/danielgindi/Charts/issues/1172)
- can't add swipe gesture [\#1170](https://github.com/danielgindi/Charts/issues/1170)
- Chart not drawing completely left to right [\#1168](https://github.com/danielgindi/Charts/issues/1168)
-  Horizontal-BarChart: change color of bar's value [\#1167](https://github.com/danielgindi/Charts/issues/1167)
- ScatterChartData not working in Swift? [\#1166](https://github.com/danielgindi/Charts/issues/1166)
- Compile error on iPhone 4s simulator [\#1165](https://github.com/danielgindi/Charts/issues/1165)
- Adding an average line to a BarChartView [\#1162](https://github.com/danielgindi/Charts/issues/1162)
- About Swift3.0 [\#1161](https://github.com/danielgindi/Charts/issues/1161)
- How to add images on some points? [\#1160](https://github.com/danielgindi/Charts/issues/1160)
- Suggest combo type chart support gouped bar type [\#1159](https://github.com/danielgindi/Charts/issues/1159)
- Adding Entries dynamicly  [\#1157](https://github.com/danielgindi/Charts/issues/1157)
- how can i add a button in redmark and i can click .. [\#1156](https://github.com/danielgindi/Charts/issues/1156)
- How can I select the close value in the candle chart view? [\#1155](https://github.com/danielgindi/Charts/issues/1155)
- Charts setup in Obj C project question [\#1154](https://github.com/danielgindi/Charts/issues/1154)
- I want datasetvalues according leftAxis and RigthAxis different display [\#1153](https://github.com/danielgindi/Charts/issues/1153)
- Unable to build project in new environment and it shows build error [\#1152](https://github.com/danielgindi/Charts/issues/1152)
- Chart Clipping [\#1149](https://github.com/danielgindi/Charts/issues/1149)
- How to handle small interval [\#1147](https://github.com/danielgindi/Charts/issues/1147)
- How to show the label [\#1146](https://github.com/danielgindi/Charts/issues/1146)
- specify certain point custom color [\#1144](https://github.com/danielgindi/Charts/issues/1144)
- when scaled  touch move can't toggle highlight [\#1141](https://github.com/danielgindi/Charts/issues/1141)
- Support Swift 3.0 \(Xcode 8\) [\#1140](https://github.com/danielgindi/Charts/issues/1140)
- How to show bar chart value labels only on selection [\#1139](https://github.com/danielgindi/Charts/issues/1139)
- BalloonMarker overlapping issue [\#1137](https://github.com/danielgindi/Charts/issues/1137)
- Customize Axis visible distance [\#1135](https://github.com/danielgindi/Charts/issues/1135)
- Different width for stacked charts [\#1134](https://github.com/danielgindi/Charts/issues/1134)
- Properly setting up a fork of this project [\#1133](https://github.com/danielgindi/Charts/issues/1133)
- Issues with xcode 8.0 beta [\#1132](https://github.com/danielgindi/Charts/issues/1132)
- Disable touch events, so do not show Label [\#1129](https://github.com/danielgindi/Charts/issues/1129)
- custom chart label layout [\#1128](https://github.com/danielgindi/Charts/issues/1128)
- BarChartView yAxis origin y are not start from y:0.0? [\#1127](https://github.com/danielgindi/Charts/issues/1127)
- Space at the bottom of the Line Chart View in landscape view [\#1125](https://github.com/danielgindi/Charts/issues/1125)
- Failing to build the project [\#1124](https://github.com/danielgindi/Charts/issues/1124)
- Rounding error on Y-axis when all Y values are zeroes [\#1123](https://github.com/danielgindi/Charts/issues/1123)
- Can't hide small slice labels from pie chart. [\#1122](https://github.com/danielgindi/Charts/issues/1122)
- Customizing the x-axes labels. [\#1120](https://github.com/danielgindi/Charts/issues/1120)
- Using prebuilt frameworks is not recommended [\#1118](https://github.com/danielgindi/Charts/issues/1118)
- chartValueSelected on TVOS [\#1117](https://github.com/danielgindi/Charts/issues/1117)
- Single LineChartDataSet in LineChartView [\#1116](https://github.com/danielgindi/Charts/issues/1116)
- how to show only positive values in barchart? [\#1114](https://github.com/danielgindi/Charts/issues/1114)
- Slow Build Time  [\#1113](https://github.com/danielgindi/Charts/issues/1113)
- BarChartDataEntry with Int instead of Double [\#1112](https://github.com/danielgindi/Charts/issues/1112)
- Embedded Binaries charts.framewor But nothing happen! [\#1111](https://github.com/danielgindi/Charts/issues/1111)
- How to draw Combined Chart on the left [\#1110](https://github.com/danielgindi/Charts/issues/1110)
- BarChartDataSet.values  is a get-only [\#1108](https://github.com/danielgindi/Charts/issues/1108)
- 有没有 oc版的？ [\#1107](https://github.com/danielgindi/Charts/issues/1107)
- Change color of highlight in chart [\#1106](https://github.com/danielgindi/Charts/issues/1106)
- Sync selected value in 2 chart [\#1105](https://github.com/danielgindi/Charts/issues/1105)
- Are two charts can be linked ? [\#1103](https://github.com/danielgindi/Charts/issues/1103)
- BarChart bottom not on the Xaxis [\#1102](https://github.com/danielgindi/Charts/issues/1102)
- BUG::: leftAxis.axisMinValue !!!=== 0.0  [\#1101](https://github.com/danielgindi/Charts/issues/1101)
- CandleStickView Memory leak [\#1100](https://github.com/danielgindi/Charts/issues/1100)
- application doesn't compile for iphone 4s and iphone 5 device. [\#1098](https://github.com/danielgindi/Charts/issues/1098)
- ChartHighlight init data in function of highlightValue error [\#1096](https://github.com/danielgindi/Charts/issues/1096)
- improvement: custom labels on BarChart [\#1095](https://github.com/danielgindi/Charts/issues/1095)
- Missing alternate x-axis labels in vertical bar chart [\#1093](https://github.com/danielgindi/Charts/issues/1093)
- ChartHighlight class initialization is error in ChartViewBase [\#1090](https://github.com/danielgindi/Charts/issues/1090)
- how can I change the mark view [\#1086](https://github.com/danielgindi/Charts/issues/1086)
- Pie Chart with value lines [\#1085](https://github.com/danielgindi/Charts/issues/1085)
- Chart Demo not Compiling [\#1083](https://github.com/danielgindi/Charts/issues/1083)
- set the Y range  [\#1079](https://github.com/danielgindi/Charts/issues/1079)
- Sync zoom in 2 chart [\#1078](https://github.com/danielgindi/Charts/issues/1078)
- The rotated xAxis label out of the view [\#1068](https://github.com/danielgindi/Charts/issues/1068)
- visibleXRangeMaximum not refreshing [\#1037](https://github.com/danielgindi/Charts/issues/1037)
- Fill area between two line in Line chart [\#1032](https://github.com/danielgindi/Charts/issues/1032)
- Left Axis not showing labels after update to Swift 2.3 [\#997](https://github.com/danielgindi/Charts/issues/997)
- highestVisibleXIndex has regression issue [\#985](https://github.com/danielgindi/Charts/issues/985)
- Problem with bitcode [\#979](https://github.com/danielgindi/Charts/issues/979)
- BarChartView height is not reflecting the values assigned to it. [\#977](https://github.com/danielgindi/Charts/issues/977)
- I use the RadarChartView,when displaying two sets of data [\#974](https://github.com/danielgindi/Charts/issues/974)
- Overlapping with multiple data styles [\#972](https://github.com/danielgindi/Charts/issues/972)
- XAxis Values label rotation [\#953](https://github.com/danielgindi/Charts/issues/953)
- Custom Y Value Formatter [\#943](https://github.com/danielgindi/Charts/issues/943)
- Bar chart missing y axis label [\#858](https://github.com/danielgindi/Charts/issues/858)
- Overlapping bar chart [\#857](https://github.com/danielgindi/Charts/issues/857)
- iOS-charts Get Currently visible bars for MultipleBarChartViewController demo [\#782](https://github.com/danielgindi/Charts/issues/782)
- Live data Line Chart [\#754](https://github.com/danielgindi/Charts/issues/754)
- Support custom formatting of chart labels [\#750](https://github.com/danielgindi/Charts/issues/750)
- Horizontally flipped x-axis [\#738](https://github.com/danielgindi/Charts/issues/738)
- Annotations issues and asking for supporting more types of markers [\#722](https://github.com/danielgindi/Charts/issues/722)
- Drop iOS 7 support in readme [\#601](https://github.com/danielgindi/Charts/issues/601)
- HorizontalBarChartView displays bar shadow when left and right axis hidden [\#311](https://github.com/danielgindi/Charts/issues/311)
- Making X-Values Properly Scaled \(Not Equidistant\) [\#194](https://github.com/danielgindi/Charts/issues/194)
- LineChart Axis Inset? [\#46](https://github.com/danielgindi/Charts/issues/46)

**Merged pull requests:**

- bump Charts version to 3.0.0 [\#1505](https://github.com/danielgindi/Charts/pull/1505) ([liuxuan30](https://github.com/liuxuan30))
- porting \#1452 into master [\#1486](https://github.com/danielgindi/Charts/pull/1486) ([liuxuan30](https://github.com/liuxuan30))
- Don't override project settings in targets [\#1484](https://github.com/danielgindi/Charts/pull/1484) ([petester42](https://github.com/petester42))
- change Charts baseSDK to iOS 10 [\#1467](https://github.com/danielgindi/Charts/pull/1467) ([liuxuan30](https://github.com/liuxuan30))
- migrate more ChartsDemo project setting to swift 3.0 [\#1466](https://github.com/danielgindi/Charts/pull/1466) ([liuxuan30](https://github.com/liuxuan30))
- Fix bar layout bug, pie callback bug [\#1452](https://github.com/danielgindi/Charts/pull/1452) ([aoverholtzer](https://github.com/aoverholtzer))
- Update project structure for simplicity and fixing carthage [\#1422](https://github.com/danielgindi/Charts/pull/1422) ([petester42](https://github.com/petester42))
- \[swift 3.0 specific\]fix linear line chart additional line segments wile zooming in [\#1420](https://github.com/danielgindi/Charts/pull/1420) ([liuxuan30](https://github.com/liuxuan30))
- fix attempt for Realm support [\#1414](https://github.com/danielgindi/Charts/pull/1414) ([liuxuan30](https://github.com/liuxuan30))
- migrate Chart v3 code in master to swift 2.3 branch [\#1389](https://github.com/danielgindi/Charts/pull/1389) ([liuxuan30](https://github.com/liuxuan30))
- fix bridgedObjCGetStringArray warning and more copyWithZone\(\_ zone: NSZone?\) -\> AnyObject errors [\#1379](https://github.com/danielgindi/Charts/pull/1379) ([liuxuan30](https://github.com/liuxuan30))
- migrate `public` to `open` [\#1378](https://github.com/danielgindi/Charts/pull/1378) ([liuxuan30](https://github.com/liuxuan30))
- Swift 3.0 beta 6 \(acegreen's branch, but with fixes for OS X\) [\#1353](https://github.com/danielgindi/Charts/pull/1353) ([pixelspark](https://github.com/pixelspark))
- Conforms to Swift 3 - Xcode 8 beta 6 [\#1342](https://github.com/danielgindi/Charts/pull/1342) ([acegreen](https://github.com/acegreen))
- When only one of scaleXEnabled or scaleYEnabled is effective [\#1319](https://github.com/danielgindi/Charts/pull/1319) ([essoecc](https://github.com/essoecc))
- V3 [\#1318](https://github.com/danielgindi/Charts/pull/1318) ([vishaldeshai](https://github.com/vishaldeshai))
- convert RunLoopMode to swift 3.0 syntax [\#1296](https://github.com/danielgindi/Charts/pull/1296) ([liuxuan30](https://github.com/liuxuan30))
- fix more ChartsDemo complains [\#1295](https://github.com/danielgindi/Charts/pull/1295) ([liuxuan30](https://github.com/liuxuan30))
- just fix all Xcode beta 4 complains, except for Realm bug [\#1294](https://github.com/danielgindi/Charts/pull/1294) ([liuxuan30](https://github.com/liuxuan30))
- Swift 3.0 - Xcode 8 Beta 4 [\#1290](https://github.com/danielgindi/Charts/pull/1290) ([acegreen](https://github.com/acegreen))
- Few more changes needed to build with Swift 2.3 [\#1281](https://github.com/danielgindi/Charts/pull/1281) ([EpicDraws](https://github.com/EpicDraws))
- fix Realm pod spec typo [\#1271](https://github.com/danielgindi/Charts/pull/1271) ([liuxuan30](https://github.com/liuxuan30))
- support Realm 1.0.2 [\#1270](https://github.com/danielgindi/Charts/pull/1270) ([liuxuan30](https://github.com/liuxuan30))
- upgrade Realm to 1.0.2 [\#1268](https://github.com/danielgindi/Charts/pull/1268) ([liuxuan30](https://github.com/liuxuan30))
- Typo fix,  our of range =\> out of range [\#1264](https://github.com/danielgindi/Charts/pull/1264) ([chris-gunawardena](https://github.com/chris-gunawardena))
- Fix Swift 3 compilation errors for iOS [\#1258](https://github.com/danielgindi/Charts/pull/1258) ([puthirith](https://github.com/puthirith))
- remove brew upgrade carthage for travis and update ios-snapshot-test-case to 2.1.2 [\#1249](https://github.com/danielgindi/Charts/pull/1249) ([liuxuan30](https://github.com/liuxuan30))
- improve comment to warn users how to use setVisibleRange APIs [\#1245](https://github.com/danielgindi/Charts/pull/1245) ([liuxuan30](https://github.com/liuxuan30))
- for \#1208, seems drawBarShadowEnabled should be false by default [\#1226](https://github.com/danielgindi/Charts/pull/1226) ([liuxuan30](https://github.com/liuxuan30))
- BUGFIX: fix infinite recursive call of getXIndex\(\_:\) [\#1213](https://github.com/danielgindi/Charts/pull/1213) ([AntiMoron](https://github.com/AntiMoron))
- Add missing imports for iOS 7 support [\#1205](https://github.com/danielgindi/Charts/pull/1205) ([VincentSit](https://github.com/VincentSit))
- upgrade carthage, resolve test failure config [\#1203](https://github.com/danielgindi/Charts/pull/1203) ([liuxuan30](https://github.com/liuxuan30))
- update iso-snapshot-test-case to master to have the fix for now [\#1202](https://github.com/danielgindi/Charts/pull/1202) ([liuxuan30](https://github.com/liuxuan30))
- Migrate to Swift 3 \(now targeted at the Swift-3.0 branch\) [\#1171](https://github.com/danielgindi/Charts/pull/1171) ([pixelspark](https://github.com/pixelspark))
- Swift 2.3 [\#1163](https://github.com/danielgindi/Charts/pull/1163) ([liuxuan30](https://github.com/liuxuan30))
- Ignoring .DS\_Store files [\#1130](https://github.com/danielgindi/Charts/pull/1130) ([einsteinx2](https://github.com/einsteinx2))

## [v2.2.5](https://github.com/danielgindi/Charts/tree/v2.2.5) (2016-05-30)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.2.4...v2.2.5)

**Implemented enhancements:**

- Support for Xamarin.Forms [\#606](https://github.com/danielgindi/Charts/issues/606)

**Fixed bugs:**

- fatal error: stride size must not be zero [\#981](https://github.com/danielgindi/Charts/issues/981)
- highestVisibleXIndex is smaller than lowestVisibleXIndex [\#940](https://github.com/danielgindi/Charts/issues/940)
- Crash on zoom [\#931](https://github.com/danielgindi/Charts/issues/931)
- ChartLegend yEntrySpace not work [\#886](https://github.com/danielgindi/Charts/issues/886)
- Selection on bubble chart [\#654](https://github.com/danielgindi/Charts/issues/654)
- Cubic Line goes too much over the top or below bottom. Needs flattening. [\#407](https://github.com/danielgindi/Charts/issues/407)

**Closed issues:**

- Podspec prepare command fails on `pod install` [\#1080](https://github.com/danielgindi/Charts/issues/1080)
- Why not the same as Android and IOS parameters? RendererBase.calcXBounds\(\) [\#1077](https://github.com/danielgindi/Charts/issues/1077)
- Why not the same parameters ？RendererBase.calcXBounds\(\) [\#1076](https://github.com/danielgindi/Charts/issues/1076)
- Crash: fatal error: Can't form Range with end \< start [\#1075](https://github.com/danielgindi/Charts/issues/1075)
- automatically calculated labels with a custom roof \(axisMaxValue only if exceeding it\) [\#1074](https://github.com/danielgindi/Charts/issues/1074)
- Swift Version  [\#1073](https://github.com/danielgindi/Charts/issues/1073)
- CodeSign for "Framework" in SDK "iOS 9.3" [\#1072](https://github.com/danielgindi/Charts/issues/1072)
- Changing size of centre section - pie chart [\#1071](https://github.com/danielgindi/Charts/issues/1071)
- PieChartView does not draw chart ! [\#1070](https://github.com/danielgindi/Charts/issues/1070)
- Real-time Charts [\#1069](https://github.com/danielgindi/Charts/issues/1069)
- swift2.2, I have a pieChartView problem [\#1067](https://github.com/danielgindi/Charts/issues/1067)
- carthage compatibility [\#1064](https://github.com/danielgindi/Charts/issues/1064)
- LineChart set a great amount data to LineChartDataSet, cpu increase highly to 97% [\#1063](https://github.com/danielgindi/Charts/issues/1063)
- Realm.io Horizontal combine bar chart - iOS [\#1062](https://github.com/danielgindi/Charts/issues/1062)
- Line chart font size problem [\#1061](https://github.com/danielgindi/Charts/issues/1061)
- BarChart won't sit on bottom of screen for some datasets, Why? How to fix? [\#1059](https://github.com/danielgindi/Charts/issues/1059)
- Very odd line at top of graph [\#1058](https://github.com/danielgindi/Charts/issues/1058)
- After upgrading to Xcode 7.3.1, getting bridging issue [\#1057](https://github.com/danielgindi/Charts/issues/1057)
- How can I remove the gap between the bar [\#1056](https://github.com/danielgindi/Charts/issues/1056)
- Pie Chart label position [\#1055](https://github.com/danielgindi/Charts/issues/1055)
- Cant understand  .axisMaxValue [\#1054](https://github.com/danielgindi/Charts/issues/1054)
- Is there a way to get a different background colour for different values of the x axis? [\#1053](https://github.com/danielgindi/Charts/issues/1053)
- Without drawLimitLinesBehindDataEnabled, limit lines go outside chart area [\#1052](https://github.com/danielgindi/Charts/issues/1052)
- The horizontal line that forms the grid of the LineChartView are not the same color  [\#1051](https://github.com/danielgindi/Charts/issues/1051)
- Issue with coloring grouped datasets in barchart [\#1050](https://github.com/danielgindi/Charts/issues/1050)
- Issues with y axis values [\#1047](https://github.com/danielgindi/Charts/issues/1047)
- Marker out of view [\#1046](https://github.com/danielgindi/Charts/issues/1046)
- How to draw OHLC chart \( Open high low close \) ? [\#1045](https://github.com/danielgindi/Charts/issues/1045)
- How to achieve this effect [\#1044](https://github.com/danielgindi/Charts/issues/1044)
- Changing the size of the interception \(dots\) font in Line Chart ? [\#1043](https://github.com/danielgindi/Charts/issues/1043)
- Multiple entries of the same item in legend [\#1042](https://github.com/danielgindi/Charts/issues/1042)
- How to draw this bar Chart [\#1039](https://github.com/danielgindi/Charts/issues/1039)
- Right axis display decimals [\#1036](https://github.com/danielgindi/Charts/issues/1036)
- Crash on getMarkerPosition  [\#1034](https://github.com/danielgindi/Charts/issues/1034)
- Set the bar at the top of the number animation [\#1033](https://github.com/danielgindi/Charts/issues/1033)
- Bar height is calculated wrong [\#1031](https://github.com/danielgindi/Charts/issues/1031)
- How to draw gridBackground by Dashed Line [\#1030](https://github.com/danielgindi/Charts/issues/1030)
- setVisibleYRangeMaximum puts line data off screen  [\#1029](https://github.com/danielgindi/Charts/issues/1029)
- How to set max value of xAxis is last object of xVals [\#1028](https://github.com/danielgindi/Charts/issues/1028)
- Can't set text of Legend? [\#1027](https://github.com/danielgindi/Charts/issues/1027)
- conflict between auto scale and axis.axisMin/MaxValue [\#1026](https://github.com/danielgindi/Charts/issues/1026)
- Extend beyond values \(feature request\) [\#1025](https://github.com/danielgindi/Charts/issues/1025)
- Error: "Assignment to readonly property" when updating charts data [\#1024](https://github.com/danielgindi/Charts/issues/1024)
- Scrolling in a scroll view is blocked if the chart in the scroll view is not fully zoomed out.  [\#1023](https://github.com/danielgindi/Charts/issues/1023)
- Print statements \(Logs\)  [\#1021](https://github.com/danielgindi/Charts/issues/1021)
- How to get to the origin of the chartView.marker? [\#1020](https://github.com/danielgindi/Charts/issues/1020)
- Target Line for Bar Chart [\#1019](https://github.com/danielgindi/Charts/issues/1019)
- Different code between github and pod [\#1018](https://github.com/danielgindi/Charts/issues/1018)
- BarChart Can‘t display Markers  [\#1017](https://github.com/danielgindi/Charts/issues/1017)
-  Can't buliding in XCode7.1 [\#1016](https://github.com/danielgindi/Charts/issues/1016)
- Show Bar Borders in Combined Chart Demo Crash [\#1014](https://github.com/danielgindi/Charts/issues/1014)
- ChartDataEntry can't initialize with float value [\#1013](https://github.com/danielgindi/Charts/issues/1013)
- Override with Wrong Argument Label? [\#1009](https://github.com/danielgindi/Charts/issues/1009)
- On Y Axis "0" may be displayed as "-0" [\#1008](https://github.com/danielgindi/Charts/issues/1008)
- Change x-axis in bubble chart [\#1007](https://github.com/danielgindi/Charts/issues/1007)
- Change the width of chart [\#1005](https://github.com/danielgindi/Charts/issues/1005)
- OC project BalloonMarker.swift error [\#1004](https://github.com/danielgindi/Charts/issues/1004)
- How to let the Y axis shows is not only a number, but the custom string [\#1003](https://github.com/danielgindi/Charts/issues/1003)
- ask questions about CombinedChartViewController.h  [\#1001](https://github.com/danielgindi/Charts/issues/1001)
- Crash when debug build, works on release build [\#999](https://github.com/danielgindi/Charts/issues/999)
- Demo crashed when running on phone\( iOS 9.0\),but can run on iPhone \(iOS 7.0\) [\#998](https://github.com/danielgindi/Charts/issues/998)
- Emergency Problem [\#996](https://github.com/danielgindi/Charts/issues/996)
- Radar Chart- Setting the maximum Y-Axis value. [\#994](https://github.com/danielgindi/Charts/issues/994)
- Y-Axis labels zooming issue [\#991](https://github.com/danielgindi/Charts/issues/991)
- XY Bubble Chart [\#990](https://github.com/danielgindi/Charts/issues/990)
- Adding text labels to Y- Axis? [\#988](https://github.com/danielgindi/Charts/issues/988)
- Build Failed: expression in list of expression [\#987](https://github.com/danielgindi/Charts/issues/987)
- autoScaleMinMaxEnabled does not work with Candle Charts  [\#986](https://github.com/danielgindi/Charts/issues/986)
- gesture conflict when lineChartView in tableView [\#984](https://github.com/danielgindi/Charts/issues/984)
- Conflicting other pod depencies [\#983](https://github.com/danielgindi/Charts/issues/983)
- NaN makes highestVisibleXIndex crash [\#980](https://github.com/danielgindi/Charts/issues/980)
- Draw Horiztontal Line on Bar Chart [\#976](https://github.com/danielgindi/Charts/issues/976)
- Show empty Chart when there are no data [\#975](https://github.com/danielgindi/Charts/issues/975)
- Unexpected problem with Import Charts [\#973](https://github.com/danielgindi/Charts/issues/973)
- In which file can I edit the yaxis maximum??? [\#971](https://github.com/danielgindi/Charts/issues/971)
- Help with customizing  [\#970](https://github.com/danielgindi/Charts/issues/970)
- Where and how can I set a y axis custom max value? [\#968](https://github.com/danielgindi/Charts/issues/968)
- The horizontal bar graph data values are exceeding the graph space. [\#967](https://github.com/danielgindi/Charts/issues/967)
- Where can I add data to my bar chart? [\#966](https://github.com/danielgindi/Charts/issues/966)
- Getting Error cannot load underlying module XCTest [\#965](https://github.com/danielgindi/Charts/issues/965)
- Don't show the horizontal line [\#964](https://github.com/danielgindi/Charts/issues/964)
- Where do I use the import line? [\#963](https://github.com/danielgindi/Charts/issues/963)
- changing bottom 'key' label? [\#962](https://github.com/danielgindi/Charts/issues/962)
- move "{" to the right side of last line [\#961](https://github.com/danielgindi/Charts/issues/961)
- Overriding Highlight  [\#960](https://github.com/danielgindi/Charts/issues/960)
- hide values under 10% [\#959](https://github.com/danielgindi/Charts/issues/959)
- x axis label gets cut [\#958](https://github.com/danielgindi/Charts/issues/958)
- enlarge the spacing between the x axis labels to the y axis labels [\#957](https://github.com/danielgindi/Charts/issues/957)
- Cubic line chart is not full bleed  [\#956](https://github.com/danielgindi/Charts/issues/956)
- Several properties work in emulator but don't \(or crash\) on iPhone [\#955](https://github.com/danielgindi/Charts/issues/955)
- Cannot pass in struct to ChartDataEntry [\#954](https://github.com/danielgindi/Charts/issues/954)
- A "divide by 0" bug [\#952](https://github.com/danielgindi/Charts/issues/952)
- Line Chart Dynamic Updating [\#951](https://github.com/danielgindi/Charts/issues/951)
- Updating version alters bar chart layout [\#950](https://github.com/danielgindi/Charts/issues/950)
- Bar chart floating y position [\#947](https://github.com/danielgindi/Charts/issues/947)
- setVisibleXRangeMinimum cause crash [\#946](https://github.com/danielgindi/Charts/issues/946)
- Carthage  [\#942](https://github.com/danielgindi/Charts/issues/942)
- How to draw 2 lineCharts in a combinedChart? [\#939](https://github.com/danielgindi/Charts/issues/939)
- highlight last data in bar chart  [\#938](https://github.com/danielgindi/Charts/issues/938)
- 我想用charts显示的数据为'int'类型 [\#936](https://github.com/danielgindi/Charts/issues/936)
- Don't see updates in PieChartDataSet from 2.2.4 [\#933](https://github.com/danielgindi/Charts/issues/933)
- Bubbles size of bubble chart [\#930](https://github.com/danielgindi/Charts/issues/930)
- Binding for Xamarin [\#929](https://github.com/danielgindi/Charts/issues/929)
- Line chart with 2 colours [\#928](https://github.com/danielgindi/Charts/issues/928)
- Disable top/bottom YAxis line drawing [\#927](https://github.com/danielgindi/Charts/issues/927)
- Coloring by bars and not by stacks in a StackBar chart [\#925](https://github.com/danielgindi/Charts/issues/925)
- How to set the scaling for the left/right axis. [\#924](https://github.com/danielgindi/Charts/issues/924)
- Use of undeclared 'CGFloat' [\#923](https://github.com/danielgindi/Charts/issues/923)
- Diffrent X-axis label color  [\#921](https://github.com/danielgindi/Charts/issues/921)
- Vertical bar chart: capture of scrollView delegate callbacks and load additional data [\#920](https://github.com/danielgindi/Charts/issues/920)
- groupspace doesn't work  [\#919](https://github.com/danielgindi/Charts/issues/919)
- Could the label of the LineChartDataSet be hidden [\#918](https://github.com/danielgindi/Charts/issues/918)
- if use pod Charts/Realm ,than @import Charts: could not build module "Charts" [\#917](https://github.com/danielgindi/Charts/issues/917)
- @import Charts: could not build module "Charts" [\#916](https://github.com/danielgindi/Charts/issues/916)
- LineChard with different color for filling [\#915](https://github.com/danielgindi/Charts/issues/915)
- start animation at minimum visible y-Position [\#914](https://github.com/danielgindi/Charts/issues/914)
- Line chart yAxis data interval [\#912](https://github.com/danielgindi/Charts/issues/912)
- Unable to display empty bars in barcharts [\#911](https://github.com/danielgindi/Charts/issues/911)
- Bar chart y axis interval digits [\#910](https://github.com/danielgindi/Charts/issues/910)
- How to use any chart in My App. [\#909](https://github.com/danielgindi/Charts/issues/909)
- Specifiy axisMinimum and axisMaximum range [\#908](https://github.com/danielgindi/Charts/issues/908)
- Symbol not Found on Xcode 7.3 [\#907](https://github.com/danielgindi/Charts/issues/907)
- noDataTextDescription not display [\#906](https://github.com/danielgindi/Charts/issues/906)
- Bar chart starting point non zero [\#905](https://github.com/danielgindi/Charts/issues/905)
- Do pod install with cocoapods [\#904](https://github.com/danielgindi/Charts/issues/904)
- Feature request :\) blur shadow for line chart [\#903](https://github.com/danielgindi/Charts/issues/903)
- Add Lines between labels [\#902](https://github.com/danielgindi/Charts/issues/902)
- Real number x axis line for BarChartView [\#901](https://github.com/danielgindi/Charts/issues/901)
- Piechart with vertical legend [\#840](https://github.com/danielgindi/Charts/issues/840)
- ChartEasingOption causes entire background to be filled with color [\#826](https://github.com/danielgindi/Charts/issues/826)
- Could not cast value of type 'Charts.BarChartData' \(0x10bbd31f0\) to 'MYPROJ.BarChartData' \(0x10aeed700\). [\#825](https://github.com/danielgindi/Charts/issues/825)
- BarChartView: Rectangle with borders and without filling color  [\#822](https://github.com/danielgindi/Charts/issues/822)
- Is there any possibility for grouping x axis values in line chart? [\#798](https://github.com/danielgindi/Charts/issues/798)
- Being OSX too, should we rename repo to just `Charts`? [\#787](https://github.com/danielgindi/Charts/issues/787)
- Missing a piece of data to the left [\#785](https://github.com/danielgindi/Charts/issues/785)
- How to remove these white/gray lines [\#758](https://github.com/danielgindi/Charts/issues/758)
- If data is not set \(e.g. public var noDataText = "No chart data available."\) [\#756](https://github.com/danielgindi/Charts/issues/756)
- ChartEasingOption properties \(animate\) [\#726](https://github.com/danielgindi/Charts/issues/726)
- Moving pie chart location \(legend overlaps chart\) [\#707](https://github.com/danielgindi/Charts/issues/707)
- CombinedChartView  bubbleData size not working [\#692](https://github.com/danielgindi/Charts/issues/692)
- Stop axis labels duplicating. [\#315](https://github.com/danielgindi/Charts/issues/315)
- Can we show Pie chart Value out of slice instead showing inside the slice ? [\#90](https://github.com/danielgindi/Charts/issues/90)
- Double tap to zoom into the chart should center to Point that was tapped   [\#83](https://github.com/danielgindi/Charts/issues/83)

**Merged pull requests:**

- Revert "Simple changes to allow OS X 10.10 support" [\#1088](https://github.com/danielgindi/Charts/pull/1088) ([danielgindi](https://github.com/danielgindi))
- Simple changes to allow OS X 10.10 support [\#1087](https://github.com/danielgindi/Charts/pull/1087) ([einsteinx2](https://github.com/einsteinx2))
- Fix \#1014: fix combined chart crash while toggle bar borders [\#1015](https://github.com/danielgindi/Charts/pull/1015) ([liuxuan30](https://github.com/liuxuan30))
- Highlight enhancements \(Closes \#654, closes \#702\) [\#1012](https://github.com/danielgindi/Charts/pull/1012) ([danielgindi](https://github.com/danielgindi))
- Fix typo [\#949](https://github.com/danielgindi/Charts/pull/949) ([emiranda04](https://github.com/emiranda04))
- fix \#940. another loop bounds crash [\#941](https://github.com/danielgindi/Charts/pull/941) ([liuxuan30](https://github.com/liuxuan30))
- Fix a crash when using markers with a PieChart [\#937](https://github.com/danielgindi/Charts/pull/937) ([rofreg](https://github.com/rofreg))
- Horizontal cubic line [\#935](https://github.com/danielgindi/Charts/pull/935) ([danielgindi](https://github.com/danielgindi))
- Property circleHoleRadius added to ILineChartDataSet protocol.  [\#934](https://github.com/danielgindi/Charts/pull/934) ([olbartek](https://github.com/olbartek))
- replace old github link to latest https://github.com/danielgindi/Charts [\#932](https://github.com/danielgindi/Charts/pull/932) ([liuxuan30](https://github.com/liuxuan30))
- Some minor nits [\#913](https://github.com/danielgindi/Charts/pull/913) ([ruurd](https://github.com/ruurd))
- add a switch whether to draw limit line's labels. default is true [\#887](https://github.com/danielgindi/Charts/pull/887) ([liuxuan30](https://github.com/liuxuan30))

## [v2.2.4](https://github.com/danielgindi/Charts/tree/v2.2.4) (2016-03-31)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.2.3...v2.2.4)

**Fixed bugs:**

- Cubic line goes over the last point [\#683](https://github.com/danielgindi/Charts/issues/683)
- drawCubicEnabled on LineChartView is missing points when Y Values are missing [\#157](https://github.com/danielgindi/Charts/issues/157)

**Closed issues:**

- can not run ChartsDemo. [\#900](https://github.com/danielgindi/Charts/issues/900)
- Barchart xAxis always displaying [\#899](https://github.com/danielgindi/Charts/issues/899)
- Multiple 'Charts.framework'. Build fails due to dependencies. [\#897](https://github.com/danielgindi/Charts/issues/897)
- Custom X Axis for BarChartView [\#896](https://github.com/danielgindi/Charts/issues/896)
- UISwipeGestureRecognizer for BarChartView [\#895](https://github.com/danielgindi/Charts/issues/895)
- The view hierarchy is not prepared for the constraint [\#893](https://github.com/danielgindi/Charts/issues/893)
- Could not find `Charts.framework` in the "Embedded Binaries" [\#891](https://github.com/danielgindi/Charts/issues/891)
- Barchart with diffrent width bar [\#890](https://github.com/danielgindi/Charts/issues/890)
- Weird numbers on BarChart [\#889](https://github.com/danielgindi/Charts/issues/889)
- Two line in X-Axis [\#888](https://github.com/danielgindi/Charts/issues/888)
- Set the leftAxis label color differently [\#885](https://github.com/danielgindi/Charts/issues/885)
- Could leftAxis and rightAxis's border be hidden? [\#884](https://github.com/danielgindi/Charts/issues/884)
- The barchart data not filled the whole container [\#883](https://github.com/danielgindi/Charts/issues/883)
- Barchart positive negtive label and xAxis overlapping [\#882](https://github.com/danielgindi/Charts/issues/882)
- How to remove the edge's border of time line chart filled shaped [\#881](https://github.com/danielgindi/Charts/issues/881)
- Master branch is not compiling  [\#879](https://github.com/danielgindi/Charts/issues/879)
- App Submission - API analysis file too large [\#878](https://github.com/danielgindi/Charts/issues/878)
- Swift 2.2 [\#877](https://github.com/danielgindi/Charts/issues/877)
- avoidFirstLastClippingEnabled Still be clipped in CombinedChartView [\#876](https://github.com/danielgindi/Charts/issues/876)
- How to make the ChartXAxis has an offset between the zero point [\#875](https://github.com/danielgindi/Charts/issues/875)
- Carthage & Xcode 7.3 Pre-Built Binaries Error [\#874](https://github.com/danielgindi/Charts/issues/874)
- Scrolling glitch [\#873](https://github.com/danielgindi/Charts/issues/873)
- Request for enhancement: combined chart\(BarChart \(grouped DataSets\) and linechart\) [\#871](https://github.com/danielgindi/Charts/issues/871)
- CandleStickChartView.shadowColor [\#870](https://github.com/danielgindi/Charts/issues/870)
- Marker behind line limit [\#867](https://github.com/danielgindi/Charts/issues/867)
- Horizontal Chart Issue [\#866](https://github.com/danielgindi/Charts/issues/866)
- How to move data points with finger drag at run time. [\#865](https://github.com/danielgindi/Charts/issues/865)
- Chart customAxisMax and customAxisMin doesn't update correctly [\#864](https://github.com/danielgindi/Charts/issues/864)
- auto scale min/max option doesn't seem to change anything [\#863](https://github.com/danielgindi/Charts/issues/863)
- Confused as to how to install using CocoaPods [\#862](https://github.com/danielgindi/Charts/issues/862)
- Different spacing between same width bars in BarChartView [\#860](https://github.com/danielgindi/Charts/issues/860)
- Get color of the specify data? [\#859](https://github.com/danielgindi/Charts/issues/859)
- DrawCubicEnabled line starts from wrong position [\#855](https://github.com/danielgindi/Charts/issues/855)
- How to prevent xAxis values and dataset value get overlapped ? [\#854](https://github.com/danielgindi/Charts/issues/854)
- Program crash [\#853](https://github.com/danielgindi/Charts/issues/853)
- Zoomin Graphview [\#852](https://github.com/danielgindi/Charts/issues/852)
- Make ChartXAxisRenderer calculate labelsWidth based on string output by custom ChartXAxisValueFormatter [\#851](https://github.com/danielgindi/Charts/issues/851)
- Zooming an already zoomed view [\#850](https://github.com/danielgindi/Charts/issues/850)
- BarChart adds double x axis [\#849](https://github.com/danielgindi/Charts/issues/849)
- Remove text above dot in iOS Charts [\#848](https://github.com/danielgindi/Charts/issues/848)
- how to start graph from \(0,0\) points instead of 60 on y-Axis? [\#846](https://github.com/danielgindi/Charts/issues/846)
- Change dataSet axis dependency [\#845](https://github.com/danielgindi/Charts/issues/845)
- integrate native chart display incorrectly [\#842](https://github.com/danielgindi/Charts/issues/842)
- How can I draw bubble charts in swift project. [\#841](https://github.com/danielgindi/Charts/issues/841)
- Making HorizontalBarChartView scrolling vertically [\#839](https://github.com/danielgindi/Charts/issues/839)
- custom BallonMaker text [\#838](https://github.com/danielgindi/Charts/issues/838)
- Horizontal zoom in line chart [\#837](https://github.com/danielgindi/Charts/issues/837)
- ScatterChart - Is it possible to create a background color like this? [\#835](https://github.com/danielgindi/Charts/issues/835)
- How to create Stacked Bar Chart and Grouped Bar Chart [\#834](https://github.com/danielgindi/Charts/issues/834)
- fatal error: unexpectedly found nil while unwrapping an Optional value error when I set barChartView.noDataText [\#833](https://github.com/danielgindi/Charts/issues/833)
- Can I change fonts of labels on x And y axis in a bar graph. [\#832](https://github.com/danielgindi/Charts/issues/832)
- LineChartView avoid painting 0 values [\#830](https://github.com/danielgindi/Charts/issues/830)
- \[Feature\] Double-tap Gesture [\#829](https://github.com/danielgindi/Charts/issues/829)
- How to draw a rectangle,or how to get the last point's CGPoint? [\#827](https://github.com/danielgindi/Charts/issues/827)
- Changing width of the bar in bar chart [\#820](https://github.com/danielgindi/Charts/issues/820)
- The return value is a problem with the calculateMinimumRadiusForSpacedSlice method [\#819](https://github.com/danielgindi/Charts/issues/819)
- import Charts: Error No Such Module Charts? [\#818](https://github.com/danielgindi/Charts/issues/818)
- Can we define minor and major grid lines  with specification like \(line color, line thickness, on/off \)? [\#817](https://github.com/danielgindi/Charts/issues/817)
- LineChart Delegate for line values [\#816](https://github.com/danielgindi/Charts/issues/816)
- Linechart X Labels overlap feature [\#813](https://github.com/danielgindi/Charts/issues/813)
- Can we support 2 more than y scales/ multiple Y axis ? [\#812](https://github.com/danielgindi/Charts/issues/812)
- Codesign error to physical device on latest [\#810](https://github.com/danielgindi/Charts/issues/810)
- Programatically scroll to end of line chart [\#809](https://github.com/danielgindi/Charts/issues/809)
- BalloonMarker - display X data [\#808](https://github.com/danielgindi/Charts/issues/808)
- multiple datasets with different x-values in chart [\#807](https://github.com/danielgindi/Charts/issues/807)
- Will ios-charts supoprt non-uniform data [\#806](https://github.com/danielgindi/Charts/issues/806)
- Not able to build for iPhone 6 [\#805](https://github.com/danielgindi/Charts/issues/805)
- Gap issues on latest version [\#804](https://github.com/danielgindi/Charts/issues/804)
- Error in 'ChartPlatform.swift' while compiling : Expected \#else or \#endif at the end of configuration block  [\#803](https://github.com/danielgindi/Charts/issues/803)
- In iOS-Chart how to hide the x and y axis and all the grid lines. [\#801](https://github.com/danielgindi/Charts/issues/801)
- Create Chart from data in Firebase db [\#800](https://github.com/danielgindi/Charts/issues/800)
- How to make the chart and the other chart with the same translation [\#799](https://github.com/danielgindi/Charts/issues/799)
- Color of crosshairs? [\#796](https://github.com/danielgindi/Charts/issues/796)
- How to make horizontal dotted line to solid line same as vertical line. [\#793](https://github.com/danielgindi/Charts/issues/793)
- ios 8.1 fatal error:value failed to bridge from Swift type to a Objective-C type [\#792](https://github.com/danielgindi/Charts/issues/792)
- xAxis end label is cutting down in Line chart [\#791](https://github.com/danielgindi/Charts/issues/791)
- Is there a way to have multi line labels in x axis. [\#790](https://github.com/danielgindi/Charts/issues/790)
- help! [\#788](https://github.com/danielgindi/Charts/issues/788)
- Positioning on changing of the screen orientation [\#786](https://github.com/danielgindi/Charts/issues/786)
- Scatter chart data set color not working [\#784](https://github.com/danielgindi/Charts/issues/784)
- fatal error: value failed to bridge from Swift type to a Objective-C type [\#783](https://github.com/danielgindi/Charts/issues/783)
- Error on RadarChart \(drawFilledEnabled set to true\) [\#760](https://github.com/danielgindi/Charts/issues/760)
- Can't set x axis offset [\#677](https://github.com/danielgindi/Charts/issues/677)
- Right Y-Axis values displaying incorrectly in LineChartDemo [\#665](https://github.com/danielgindi/Charts/issues/665)
- Y - Axis Values stick together [\#608](https://github.com/danielgindi/Charts/issues/608)
- Charts over scrollview not scrolling in iPad only [\#550](https://github.com/danielgindi/Charts/issues/550)
- Stepped line charts [\#539](https://github.com/danielgindi/Charts/issues/539)
- Line chart circle positions [\#535](https://github.com/danielgindi/Charts/issues/535)
- Y-Axis values "stick together" occasionally [\#469](https://github.com/danielgindi/Charts/issues/469)
- Feature : initial display of the graph [\#468](https://github.com/danielgindi/Charts/issues/468)
- Candle Stick not filled make the stick and the gridline show within the candle [\#444](https://github.com/danielgindi/Charts/issues/444)
- Unit tests? ;\) [\#414](https://github.com/danielgindi/Charts/issues/414)
- How to draw a line only for the 5th value of dataset along x axis? [\#409](https://github.com/danielgindi/Charts/issues/409)
- Data Showing 0 and 1 only in y-axis if array contains only 0.0 double values. [\#338](https://github.com/danielgindi/Charts/issues/338)
- Extending lines between data points when zoomed and panning [\#330](https://github.com/danielgindi/Charts/issues/330)
- lowestVisibleXIndex never gets zero after zooming out [\#294](https://github.com/danielgindi/Charts/issues/294)
- on candle graph, when the candle is small then its not showing [\#281](https://github.com/danielgindi/Charts/issues/281)
- Bar Chart \> Range bar  [\#100](https://github.com/danielgindi/Charts/issues/100)
- General discussion & news [\#92](https://github.com/danielgindi/Charts/issues/92)
- Core Data interfacing? [\#15](https://github.com/danielgindi/Charts/issues/15)

**Merged pull requests:**

- remove duplicated statement [\#894](https://github.com/danielgindi/Charts/pull/894) ([liuxuan30](https://github.com/liuxuan30))
- Add new pie chart renderer with polyline indicate [\#869](https://github.com/danielgindi/Charts/pull/869) ([wjacker](https://github.com/wjacker))
- Add a Gitter chat badge to README.md [\#861](https://github.com/danielgindi/Charts/pull/861) ([gitter-badger](https://github.com/gitter-badger))
- Type bug in PieChartData [\#847](https://github.com/danielgindi/Charts/pull/847) ([leoMehlig](https://github.com/leoMehlig))
- Supporting borders on bars, Fixes issue \#822 [\#844](https://github.com/danielgindi/Charts/pull/844) ([AndreasIgelCC](https://github.com/AndreasIgelCC))
- Update Readme [\#828](https://github.com/danielgindi/Charts/pull/828) ([PhilJay](https://github.com/PhilJay))
- Keep position on rotation [\#824](https://github.com/danielgindi/Charts/pull/824) ([leoMehlig](https://github.com/leoMehlig))
- Set code signing identity for iOS targets [\#811](https://github.com/danielgindi/Charts/pull/811) ([krbarnes](https://github.com/krbarnes))
- Add trailing newline for preprocessor statement [\#795](https://github.com/danielgindi/Charts/pull/795) ([boourns](https://github.com/boourns))

## [v2.2.3](https://github.com/danielgindi/Charts/tree/v2.2.3) (2016-02-29)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.2.2...v2.2.3)

**Closed issues:**

- How to create the chartmarker with swift? [\#776](https://github.com/danielgindi/Charts/issues/776)
- CombinedChartView: Wrong positions of bars when using multiple Bar and Graph -Datasets [\#775](https://github.com/danielgindi/Charts/issues/775)
- Getting co-ordinates of all plotted points in LineChartView [\#774](https://github.com/danielgindi/Charts/issues/774)
- Multiline label on xAxis cutting off  [\#773](https://github.com/danielgindi/Charts/issues/773)
- Can't set ChartViewBase's data property back to nil to clear out data [\#771](https://github.com/danielgindi/Charts/issues/771)
- How to set position label radar chart. in Swift . [\#770](https://github.com/danielgindi/Charts/issues/770)
- How to control which entries on xAxis be shown? [\#768](https://github.com/danielgindi/Charts/issues/768)
- Tests failing after startAtZero was removed [\#767](https://github.com/danielgindi/Charts/issues/767)
- When a value repeats, bars are not showed [\#766](https://github.com/danielgindi/Charts/issues/766)
- Proposal: shall we unify yAxis.entries and yAxis.axisRange to have same max and min value? [\#763](https://github.com/danielgindi/Charts/issues/763)
- Ability to set linechart with fixed amount of x values shown and pan to other data points [\#762](https://github.com/danielgindi/Charts/issues/762)
- PieChart setMaxAngle [\#757](https://github.com/danielgindi/Charts/issues/757)
- Simply not installing [\#749](https://github.com/danielgindi/Charts/issues/749)
- Line charts have unintended breaks when next or previous point not visible. [\#748](https://github.com/danielgindi/Charts/issues/748)
- Remove border around linechart with gradient fill [\#747](https://github.com/danielgindi/Charts/issues/747)
- Old graphics not removed when adding new data [\#745](https://github.com/danielgindi/Charts/issues/745)
- Display values outside of the piechart [\#743](https://github.com/danielgindi/Charts/issues/743)
- Cubic line charts don't get filled correctly if data doesn't start at index 0 [\#711](https://github.com/danielgindi/Charts/issues/711)
- Center text vertically [\#682](https://github.com/danielgindi/Charts/issues/682)
- Animated moveViewToX\(\) [\#318](https://github.com/danielgindi/Charts/issues/318)
- Any chance of porting this to OSX? [\#43](https://github.com/danielgindi/Charts/issues/43)

**Merged pull requests:**

- Add "Toggle Data" option to demo charts. \(\#771 Support\) [\#781](https://github.com/danielgindi/Charts/pull/781) ([ospr](https://github.com/ospr))
- Add missing UIKit imports for iOS 7 [\#780](https://github.com/danielgindi/Charts/pull/780) ([asmarques](https://github.com/asmarques))
- Feature \#539 Stepped line charts [\#778](https://github.com/danielgindi/Charts/pull/778) ([ezamagni](https://github.com/ezamagni))
- Make ChartViewBase's \_data optional. \(Fixes \#771\) [\#772](https://github.com/danielgindi/Charts/pull/772) ([ospr](https://github.com/ospr))
- Add Carthage compatibility badge [\#769](https://github.com/danielgindi/Charts/pull/769) ([Bogidon](https://github.com/Bogidon))
- update cocoapods url [\#755](https://github.com/danielgindi/Charts/pull/755) ([stevenedds](https://github.com/stevenedds))
- add ci status [\#752](https://github.com/danielgindi/Charts/pull/752) ([petester42](https://github.com/petester42))
- Correct the spelling of CocoaPods in README [\#751](https://github.com/danielgindi/Charts/pull/751) ([ReadmeCritic](https://github.com/ReadmeCritic))
- LineChartRenderer context bug [\#746](https://github.com/danielgindi/Charts/pull/746) ([leoMehlig](https://github.com/leoMehlig))
- Fix for cubic line chart fill when charts that don't start at x-index 0 \#711 [\#712](https://github.com/danielgindi/Charts/pull/712) ([gunterhager](https://github.com/gunterhager))
- add support for lineCap setting for line chart [\#658](https://github.com/danielgindi/Charts/pull/658) ([liuxuan30](https://github.com/liuxuan30))

## [v2.2.2](https://github.com/danielgindi/Charts/tree/v2.2.2) (2016-02-09)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.2.1...v2.2.2)

**Fixed bugs:**

- Using BarChartDataSet default initializer causes a crash [\#734](https://github.com/danielgindi/Charts/issues/734)

**Closed issues:**

- Display dataSet labels on axis for horizontal bar [\#741](https://github.com/danielgindi/Charts/issues/741)
- Getting x,y values of points in LineChart [\#739](https://github.com/danielgindi/Charts/issues/739)
- Module 'Realm' not found in v2.2.1 [\#735](https://github.com/danielgindi/Charts/issues/735)
- cannot compile ios charts because of Ream environment missing. [\#733](https://github.com/danielgindi/Charts/issues/733)
- Issue in valueFormatter [\#732](https://github.com/danielgindi/Charts/issues/732)
- Multi colors for linechart's circles [\#730](https://github.com/danielgindi/Charts/issues/730)
- LineChartData omits and misaligns values when data isn't continuous [\#728](https://github.com/danielgindi/Charts/issues/728)
- Apache license and correct attribution in an iOS app? [\#678](https://github.com/danielgindi/Charts/issues/678)

## [v2.2.1](https://github.com/danielgindi/Charts/tree/v2.2.1) (2016-02-01)
[Full Changelog](https://github.com/danielgindi/Charts/compare/2.2.1...v2.2.1)

## [2.2.1](https://github.com/danielgindi/Charts/tree/2.2.1) (2016-02-01)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.2.0...2.2.1)

**Fixed bugs:**

- Can't set hole color to nil for Pie Chart [\#646](https://github.com/danielgindi/Charts/issues/646)

**Closed issues:**

- X-Axis at bottom of chart? [\#727](https://github.com/danielgindi/Charts/issues/727)
- 2.2.0 not published yet [\#723](https://github.com/danielgindi/Charts/issues/723)
- Highlight selected line by changing line width or line color [\#721](https://github.com/danielgindi/Charts/issues/721)
- Draw a Line that connects dots in Scatter Chart \(Simulate Line Chart not equidistant\) [\#720](https://github.com/danielgindi/Charts/issues/720)
- drawCubicEnabled performance issues [\#717](https://github.com/danielgindi/Charts/issues/717)
- how to disable zooming of graph on double click? [\#716](https://github.com/danielgindi/Charts/issues/716)
- Convert it to percent bar graph system [\#674](https://github.com/danielgindi/Charts/issues/674)
- Refresh charts view when the data change [\#672](https://github.com/danielgindi/Charts/issues/672)
- The most top label of ChartYAxis doesn't render which seems a bug. [\#647](https://github.com/danielgindi/Charts/issues/647)
- RadarChart animation [\#574](https://github.com/danielgindi/Charts/issues/574)
- Having the area below a line graph be gradient layer instead of background color [\#186](https://github.com/danielgindi/Charts/issues/186)

**Merged pull requests:**

- Update podspec for realm and 2.2.0 [\#725](https://github.com/danielgindi/Charts/pull/725) ([petester42](https://github.com/petester42))

## [v2.2.0](https://github.com/danielgindi/Charts/tree/v2.2.0) (2016-01-26)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.1.6...v2.2.0)

**Fixed bugs:**

- ChartDataSet calcYValueSum\(\) bug [\#604](https://github.com/danielgindi/Charts/issues/604)

**Closed issues:**

- Realm module not found when using a static library of Realm in my pod project in Objective-C [\#714](https://github.com/danielgindi/Charts/issues/714)
- how to get a custom legend [\#710](https://github.com/danielgindi/Charts/issues/710)
- I can't change the default grey background on the line chart  [\#706](https://github.com/danielgindi/Charts/issues/706)
- I couldn't find Charts.xcodeproj [\#705](https://github.com/danielgindi/Charts/issues/705)
- Background Colour Changes [\#701](https://github.com/danielgindi/Charts/issues/701)
- Continuing the last closed \#695 [\#700](https://github.com/danielgindi/Charts/issues/700)
- Realm error [\#699](https://github.com/danielgindi/Charts/issues/699)
- Grid with constant width cells [\#697](https://github.com/danielgindi/Charts/issues/697)
- Pie chart is too small in tableview header view  [\#696](https://github.com/danielgindi/Charts/issues/696)
- The delegate method is not getting called [\#695](https://github.com/danielgindi/Charts/issues/695)
- Not compatible with app extensions as a framework [\#693](https://github.com/danielgindi/Charts/issues/693)
- Set label for each column in group bar chart [\#691](https://github.com/danielgindi/Charts/issues/691)
- How to  custom XAxis [\#689](https://github.com/danielgindi/Charts/issues/689)
- Need Help to setup this library on application with Objective C\(No auto layout\) with deployment target iOS 7.0 [\#688](https://github.com/danielgindi/Charts/issues/688)
- RadarChartView - Unknown type name [\#687](https://github.com/danielgindi/Charts/issues/687)
- How to make CandleStickChart and BarChart do the same action [\#685](https://github.com/danielgindi/Charts/issues/685)
- Issue with x-axis fixed label width [\#684](https://github.com/danielgindi/Charts/issues/684)
- Can I package the project in a dynamic library \(.framework\)? [\#681](https://github.com/danielgindi/Charts/issues/681)
- Dynamic LineChartView [\#680](https://github.com/danielgindi/Charts/issues/680)
- Horizontal Bar Chart Rounding [\#676](https://github.com/danielgindi/Charts/issues/676)
- Line Chart Highlight Styling [\#671](https://github.com/danielgindi/Charts/issues/671)
- Allow control of circle line width in Line Chart. [\#669](https://github.com/danielgindi/Charts/issues/669)
- Programatically highlight a data point in line chart [\#668](https://github.com/danielgindi/Charts/issues/668)
- Question: X-Axis set range of displayed entries, LineChart [\#667](https://github.com/danielgindi/Charts/issues/667)
- "No such module" error for archive with custom configuration in objc project. [\#664](https://github.com/danielgindi/Charts/issues/664)
- How to run this chart in iOS7? [\#663](https://github.com/danielgindi/Charts/issues/663)
- draw only the maximum and minimum values on the LineChartView [\#662](https://github.com/danielgindi/Charts/issues/662)
- Combine horizontal bar chart and line chart  [\#661](https://github.com/danielgindi/Charts/issues/661)
- Can't import charts in Swift [\#659](https://github.com/danielgindi/Charts/issues/659)
- Realtime data [\#657](https://github.com/danielgindi/Charts/issues/657)
- xcode7.2 iOS 9 crash [\#656](https://github.com/danielgindi/Charts/issues/656)
- draw line chart with Gradient background [\#653](https://github.com/danielgindi/Charts/issues/653)
- Draw Straight Line on Line Chart with 2 fingers and show the Gradient of it [\#652](https://github.com/danielgindi/Charts/issues/652)
- Add To existing project and that too for iOS 7  [\#650](https://github.com/danielgindi/Charts/issues/650)
- xcode 7.1 error at xctest [\#644](https://github.com/danielgindi/Charts/issues/644)
- How to work out this in objective c [\#642](https://github.com/danielgindi/Charts/issues/642)
- 【StackedBarChartView】How to handle 0？ [\#637](https://github.com/danielgindi/Charts/issues/637)
- Not able to archive build : Command failed due to signal: Segmentation fault: 11 [\#634](https://github.com/danielgindi/Charts/issues/634)
- Xcode7.1.1 contains error [\#633](https://github.com/danielgindi/Charts/issues/633)
- ChartDataSet's entryCount computed property raises a signal when \_yVals is nil [\#631](https://github.com/danielgindi/Charts/issues/631)
- Not able to set custom class as a BarChartViewController in objective c  [\#628](https://github.com/danielgindi/Charts/issues/628)
- I want to know the project name - swift. h  class of how to generate [\#627](https://github.com/danielgindi/Charts/issues/627)
- LineChart  xAxis can't match the point [\#626](https://github.com/danielgindi/Charts/issues/626)
- set width of highlight in pie [\#625](https://github.com/danielgindi/Charts/issues/625)
- what's wrong with:\_OBJC\_CLASS\_$\_\_TtC6Charts13LineChartView [\#623](https://github.com/danielgindi/Charts/issues/623)
- How to draw a LineChartDataSet skipping certain data points [\#622](https://github.com/danielgindi/Charts/issues/622)
- Show % Percentage value with Legend data  [\#621](https://github.com/danielgindi/Charts/issues/621)
- \[Enhancement\] avoidFirstLastClipping should keep the labels centered [\#619](https://github.com/danielgindi/Charts/issues/619)
- yAxis label shows only when i touch the view [\#618](https://github.com/danielgindi/Charts/issues/618)
- Legend colors not correct [\#617](https://github.com/danielgindi/Charts/issues/617)
- \[suggestion\] Warning when wrong ChartDataSet is used for view [\#616](https://github.com/danielgindi/Charts/issues/616)
- How to make the chart and the other chart with the same action [\#615](https://github.com/danielgindi/Charts/issues/615)
- touchesEnded event in BarChartView not called [\#613](https://github.com/danielgindi/Charts/issues/613)
- I put the deployment of XCODE target to 8.0 is an error, but change to 8.3 won't go wrong, what's the matter? [\#612](https://github.com/danielgindi/Charts/issues/612)
- why drawValues has some condition which uses break, some uses continue? [\#611](https://github.com/danielgindi/Charts/issues/611)
- Real Time Data and Moving X happens automatically ? [\#610](https://github.com/danielgindi/Charts/issues/610)
- A way to add data to chart data entries in real time ?  [\#609](https://github.com/danielgindi/Charts/issues/609)
- Min and max number of horizontal lines [\#603](https://github.com/danielgindi/Charts/issues/603)
- Pie Chart only one Legend data is visible [\#602](https://github.com/danielgindi/Charts/issues/602)
- Myproject-swift.h  has not founded in my project why?? [\#599](https://github.com/danielgindi/Charts/issues/599)
- Charts pulled from cocoapods is still of version 2.1.3 and it has compile errors against Swift 2.0 [\#598](https://github.com/danielgindi/Charts/issues/598)
- Fetch Stack Label at the click of the stack bar in the bar chart [\#596](https://github.com/danielgindi/Charts/issues/596)
-  Cannot load underlying module for 'XCTest' when i am using charts in xcode7.1 [\#595](https://github.com/danielgindi/Charts/issues/595)
- setLabelsToSkip does not seem to function properly [\#594](https://github.com/danielgindi/Charts/issues/594)
- Work around for scalable xaxis for line chart [\#593](https://github.com/danielgindi/Charts/issues/593)
- unknown class Issue [\#592](https://github.com/danielgindi/Charts/issues/592)
- Selective fill colors on LineChartView [\#591](https://github.com/danielgindi/Charts/issues/591)
- how to set the YAxis or XAxis for custom value [\#590](https://github.com/danielgindi/Charts/issues/590)
- Finger drawing \(draw values into the chart with touch-gesture\) implemented? [\#589](https://github.com/danielgindi/Charts/issues/589)
- how to limit the YAxis Label's width? [\#588](https://github.com/danielgindi/Charts/issues/588)
- how to combine  chart  as follows [\#587](https://github.com/danielgindi/Charts/issues/587)
- no data available [\#585](https://github.com/danielgindi/Charts/issues/585)
- Is there a way to add arrows to highlight line? [\#584](https://github.com/danielgindi/Charts/issues/584)
- Getting question \(?\) mark while importing the library. How to get it resolved [\#583](https://github.com/danielgindi/Charts/issues/583)
- Mixed Font Style for Line Chart of X-Axis [\#581](https://github.com/danielgindi/Charts/issues/581)
- How to remove background gridlines from Bar Chart [\#579](https://github.com/danielgindi/Charts/issues/579)
- Question: Gesture Detection [\#578](https://github.com/danielgindi/Charts/issues/578)
- Crash originating in ChartViewPortHandler [\#576](https://github.com/danielgindi/Charts/issues/576)
- Add gaps in LineChart [\#572](https://github.com/danielgindi/Charts/issues/572)
- Cannot call getBarBounds from objective-c [\#570](https://github.com/danielgindi/Charts/issues/570)
- issue with Bar width  [\#569](https://github.com/danielgindi/Charts/issues/569)
- UIPanGesture blocked while zoomed out completely [\#566](https://github.com/danielgindi/Charts/issues/566)
- Using Charts library for real-time plotting [\#565](https://github.com/danielgindi/Charts/issues/565)
- iOS-Charts not working on Jenkins [\#564](https://github.com/danielgindi/Charts/issues/564)
- Swift 2 \(Xcode 7\) Bad address on accessing chart view [\#563](https://github.com/danielgindi/Charts/issues/563)
- Custom offsets [\#562](https://github.com/danielgindi/Charts/issues/562)
- iOS 7 && Xcode 7.1.1 build not success, 'Charts' Module no found [\#560](https://github.com/danielgindi/Charts/issues/560)
- ChartYAxisValueFormatter in iOS  [\#559](https://github.com/danielgindi/Charts/issues/559)
- can i add this？ [\#557](https://github.com/danielgindi/Charts/issues/557)
- Problem uploading the archive. [\#556](https://github.com/danielgindi/Charts/issues/556)
- A strange issue when the APP second launch in debug mode [\#554](https://github.com/danielgindi/Charts/issues/554)
- Auto-adjusting graph scale on moving [\#553](https://github.com/danielgindi/Charts/issues/553)
- Bar Chart view group in Swift [\#551](https://github.com/danielgindi/Charts/issues/551)
- how to change the bar width  [\#549](https://github.com/danielgindi/Charts/issues/549)
- Calculating space \(in px\) between two points [\#548](https://github.com/danielgindi/Charts/issues/548)
- CocoaPods can only install Charts with Version `2.1.3` [\#547](https://github.com/danielgindi/Charts/issues/547)
- App didn't started with Charts [\#546](https://github.com/danielgindi/Charts/issues/546)
- Setting custom values on top of bar chart [\#545](https://github.com/danielgindi/Charts/issues/545)
- Issues submitting app to Apple store [\#544](https://github.com/danielgindi/Charts/issues/544)
- Guidance on scrollable/pannable charts [\#543](https://github.com/danielgindi/Charts/issues/543)
- Line chart in tableview cell [\#542](https://github.com/danielgindi/Charts/issues/542)
- center lineChart value label to circle [\#541](https://github.com/danielgindi/Charts/issues/541)
- Use LineChartView create chartview will not slide,   [\#540](https://github.com/danielgindi/Charts/issues/540)
- Getting some interesting errors when trying to upload our project to ITC with the latest ios-charts. [\#538](https://github.com/danielgindi/Charts/issues/538)
- Running demo direct error, this is how it happened [\#536](https://github.com/danielgindi/Charts/issues/536)
- StackLabels [\#534](https://github.com/danielgindi/Charts/issues/534)
- Legend at bottom of graph show on more than one line. [\#532](https://github.com/danielgindi/Charts/issues/532)
- Do we have any static library for ios-charts? [\#531](https://github.com/danielgindi/Charts/issues/531)
- Highlight a label in xAxis [\#530](https://github.com/danielgindi/Charts/issues/530)
- BalloonMarker shows float number with many digits after '.' [\#529](https://github.com/danielgindi/Charts/issues/529)
- Get the visible max and min yAxis values [\#528](https://github.com/danielgindi/Charts/issues/528)
- Scale and displacement by code [\#527](https://github.com/danielgindi/Charts/issues/527)
- My own app is crashed  and got error is fatal error: value failed to bridge from Swift type to a Objective-C type [\#525](https://github.com/danielgindi/Charts/issues/525)
- if I want to set the RadarLabel like this! [\#524](https://github.com/danielgindi/Charts/issues/524)
- Pie Chart Legends Missing with the dynamically filled data. [\#518](https://github.com/danielgindi/Charts/issues/518)
- Resize chart due to data update   [\#517](https://github.com/danielgindi/Charts/issues/517)
- How to detect end of chart event? [\#508](https://github.com/danielgindi/Charts/issues/508)
- Cannot distribute inhouse build with ios-charts library embedded to my project [\#486](https://github.com/danielgindi/Charts/issues/486)
- Excessive Printing to Console [\#417](https://github.com/danielgindi/Charts/issues/417)
- Module is not extensible [\#357](https://github.com/danielgindi/Charts/issues/357)

**Merged pull requests:**

- Activate require app extension safe API to be able to use library inside an app extension [\#708](https://github.com/danielgindi/Charts/pull/708) ([ghost](https://github.com/ghost))
- fix code indent problem in ChartYAxisRendererRadarChart, ChartYAxisRenderer, BarChartDataSet, RadarChartView [\#675](https://github.com/danielgindi/Charts/pull/675) ([liuxuan30](https://github.com/liuxuan30))
- add an option to set line cap of axis grid line [\#660](https://github.com/danielgindi/Charts/pull/660) ([mconintet](https://github.com/mconintet))
- Fix minor typo in BarLineChartViewBase [\#651](https://github.com/danielgindi/Charts/pull/651) ([patrickreynolds](https://github.com/patrickreynolds))
- Stop axis labels duplicating. \#315  \[Pending squash and cleanup\] [\#648](https://github.com/danielgindi/Charts/pull/648) ([noais](https://github.com/noais))
- Adapted ChartLegendRenderer class to upcoming Swift 3 changes and improved code readability [\#643](https://github.com/danielgindi/Charts/pull/643) ([zntfdr](https://github.com/zntfdr))
- Remove verbose semicolons [\#639](https://github.com/danielgindi/Charts/pull/639) ([AntiMoron](https://github.com/AntiMoron))
- Adds CI [\#636](https://github.com/danielgindi/Charts/pull/636) ([petester42](https://github.com/petester42))
- Add missing images for bar chart tests [\#635](https://github.com/danielgindi/Charts/pull/635) ([petester42](https://github.com/petester42))
- Use nil coalescing in ChartDataSet's entryCount \(Fixes \#631\) [\#632](https://github.com/danielgindi/Charts/pull/632) ([aarondaub](https://github.com/aarondaub))
- add highlight circle for radar chart [\#630](https://github.com/danielgindi/Charts/pull/630) ([liuxuan30](https://github.com/liuxuan30))
- Remove useless parentheses causing swift build error [\#614](https://github.com/danielgindi/Charts/pull/614) ([chanil1218](https://github.com/chanil1218))
- Add change log file. [\#605](https://github.com/danielgindi/Charts/pull/605) ([skywinder](https://github.com/skywinder))
- add initialize dataSets in setter [\#600](https://github.com/danielgindi/Charts/pull/600) ([liuxuan30](https://github.com/liuxuan30))
- Bar chart tests [\#580](https://github.com/danielgindi/Charts/pull/580) ([alvesjtiago](https://github.com/alvesjtiago))
- Make getBarBounds callable from Objective-C code \(Fixes \#570\) [\#571](https://github.com/danielgindi/Charts/pull/571) ([ghost](https://github.com/ghost))
- round the float value before we cast to Int [\#558](https://github.com/danielgindi/Charts/pull/558) ([liuxuan30](https://github.com/liuxuan30))

## [v2.1.6](https://github.com/danielgindi/Charts/tree/v2.1.6) (2015-11-02)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.1.5...v2.1.6)

**Implemented enhancements:**

- X axis names visibility \(Rotation\) [\#61](https://github.com/danielgindi/Charts/issues/61)

**Closed issues:**

- hello,this library is very great!!! [\#523](https://github.com/danielgindi/Charts/issues/523)
- \[BUG\] highlight when triggering pan gesture in default scale not working [\#520](https://github.com/danielgindi/Charts/issues/520)
- Implementing Gestures [\#519](https://github.com/danielgindi/Charts/issues/519)
- Does ios-charts work for Mac Cocoa programming? [\#516](https://github.com/danielgindi/Charts/issues/516)
- No 'setHighlightPerTapEnabled\(boolean enabled\)' in iosCharts? [\#515](https://github.com/danielgindi/Charts/issues/515)
-  This application is modifying the autolayout engine from a background thread, which can lead to engine corruption and weird crashes.  This will cause an exception in a future release. [\#514](https://github.com/danielgindi/Charts/issues/514)
- How to set position of description? [\#512](https://github.com/danielgindi/Charts/issues/512)
- Can't update library to 2.1.5 by cocoapods [\#511](https://github.com/danielgindi/Charts/issues/511)
- How in your chart on the basis of the modified？ [\#510](https://github.com/danielgindi/Charts/issues/510)
- How can I reset bargraph total width ? [\#509](https://github.com/danielgindi/Charts/issues/509)
- ComboChartView highlight [\#507](https://github.com/danielgindi/Charts/issues/507)
- Making a line chart set with images on the chart [\#506](https://github.com/danielgindi/Charts/issues/506)
- the text rotation （Cry for help） [\#505](https://github.com/danielgindi/Charts/issues/505)
- StackedBar Chart highlighting complete single bar [\#504](https://github.com/danielgindi/Charts/issues/504)
- LineChart crashes if no entries [\#496](https://github.com/danielgindi/Charts/issues/496)
- Background color and floats on Y axis. [\#495](https://github.com/danielgindi/Charts/issues/495)
- Facing problem while achieving the build on Xcode7 [\#492](https://github.com/danielgindi/Charts/issues/492)
- About method setCircleColor & setCircleHoleColor [\#491](https://github.com/danielgindi/Charts/issues/491)
- inner web lines missing.  [\#490](https://github.com/danielgindi/Charts/issues/490)
- Positioning using screen points instead of xIndex [\#487](https://github.com/danielgindi/Charts/issues/487)
- Selectable marker view & images at Y axis [\#485](https://github.com/danielgindi/Charts/issues/485)
- Could you draw a discontinuous line? [\#484](https://github.com/danielgindi/Charts/issues/484)
- Using image as marker in the chart [\#483](https://github.com/danielgindi/Charts/issues/483)
- Line chart can set the position of the x axis, such as in the chart below [\#482](https://github.com/danielgindi/Charts/issues/482)
- ```xAxis.avoidFirstLastClippingEnabled=YES``` problem.Version 2.1.4a [\#479](https://github.com/danielgindi/Charts/issues/479)
- Zombie exception [\#477](https://github.com/danielgindi/Charts/issues/477)
- chartView does't overlay with dark color highlighted value in bar chart  [\#449](https://github.com/danielgindi/Charts/issues/449)
- viewPortHandler.setMaximumScaleX not working for horizontal bar chart [\#256](https://github.com/danielgindi/Charts/issues/256)

**Merged pull requests:**

- Implemented support for rotated labels on the x-axis [\#513](https://github.com/danielgindi/Charts/pull/513) ([danielgindi](https://github.com/danielgindi))
- update targets to build framework with same name [\#501](https://github.com/danielgindi/Charts/pull/501) ([petester42](https://github.com/petester42))
- Adds test support without cocoapods [\#500](https://github.com/danielgindi/Charts/pull/500) ([petester42](https://github.com/petester42))
- Fixed drag offset panning bug [\#498](https://github.com/danielgindi/Charts/pull/498) ([leoMehlig](https://github.com/leoMehlig))
- Revert "BUGFIX:fix xAxis labels of bar chart" [\#497](https://github.com/danielgindi/Charts/pull/497) ([danielgindi](https://github.com/danielgindi))
- if only line data exists and no other data, turn \_deltaX to 1.0 [\#493](https://github.com/danielgindi/Charts/pull/493) ([liuxuan30](https://github.com/liuxuan30))
- BUGFIX:fix xAxis labels of bar chart [\#489](https://github.com/danielgindi/Charts/pull/489) ([AntiMoron](https://github.com/AntiMoron))
- Fix issue related to PhilJay/MPAndroidChart\#1121 [\#488](https://github.com/danielgindi/Charts/pull/488) ([PhilJay](https://github.com/PhilJay))
- Approved, pending styling: Fix Scroll issue when the graph is in a UITableView [\#464](https://github.com/danielgindi/Charts/pull/464) ([coupgar](https://github.com/coupgar))
- Add ability to turn off antialias for grid lines [\#462](https://github.com/danielgindi/Charts/pull/462) ([vvit](https://github.com/vvit))

## [v2.1.5](https://github.com/danielgindi/Charts/tree/v2.1.5) (2015-10-15)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.1.4a...v2.1.5)

**Fixed bugs:**

- chart is scrolling to top when reach maxScaleY and try zoom in again [\#463](https://github.com/danielgindi/Charts/issues/463)
- Pie chart does not work well with separate animations on x and y axis simultaneously [\#456](https://github.com/danielgindi/Charts/issues/456)

**Closed issues:**

- YAxis size calculations [\#480](https://github.com/danielgindi/Charts/issues/480)
- Can't install app due to non matching architecture. [\#476](https://github.com/danielgindi/Charts/issues/476)
- compiling for iPhone4s gives error like \#344 [\#475](https://github.com/danielgindi/Charts/issues/475)
- Compilation issue [\#473](https://github.com/danielgindi/Charts/issues/473)
- Problem with marker in Swift [\#472](https://github.com/danielgindi/Charts/issues/472)
- Crash: function signature specialization \<Arg\[0\] = Owned To Guaranteed\> of Charts.BarLineChartViewBase.calcModulus \(Charts.BarLineChartViewBase\)\(\) -\> \(\), line 444 [\#471](https://github.com/danielgindi/Charts/issues/471)
- Pinch to Zoom is not being disabled properly. [\#470](https://github.com/danielgindi/Charts/issues/470)
- how to draw a graph having x value but no values in y  [\#467](https://github.com/danielgindi/Charts/issues/467)
- Why set barRect.size.height = bottom - top to be negative? [\#465](https://github.com/danielgindi/Charts/issues/465)
- swift 1.2 branch [\#459](https://github.com/danielgindi/Charts/issues/459)
- Has anyone meet the '\# has been removed from swift' problem under XCode version 7.0.1? [\#458](https://github.com/danielgindi/Charts/issues/458)
- Multiple negative-able values in Stack bar chart [\#455](https://github.com/danielgindi/Charts/issues/455)
- Limit Line Label Text Color [\#454](https://github.com/danielgindi/Charts/issues/454)
- problems using autoScaleMinMaxEnabled  [\#453](https://github.com/danielgindi/Charts/issues/453)
- how to update the ios-charts in existing projects. [\#452](https://github.com/danielgindi/Charts/issues/452)
- Provide callbacks on ChartViewDelegate to better handle dragging, touch, etc [\#451](https://github.com/danielgindi/Charts/issues/451)
- moveViewToX problem [\#450](https://github.com/danielgindi/Charts/issues/450)
- bar width [\#448](https://github.com/danielgindi/Charts/issues/448)
- Can we show title of Axis? [\#447](https://github.com/danielgindi/Charts/issues/447)
- Make \_chart in CombinedChartRenderer internal [\#446](https://github.com/danielgindi/Charts/issues/446)
- Dynamic graph display [\#445](https://github.com/danielgindi/Charts/issues/445)
- Demo project is not working  [\#443](https://github.com/danielgindi/Charts/issues/443)
- xcode 7 issue? -  non-existent file for the CFBundleExecutable key [\#442](https://github.com/danielgindi/Charts/issues/442)
- Custom Y Axis entries with String values [\#441](https://github.com/danielgindi/Charts/issues/441)
- Change size of the view that contain the chart [\#440](https://github.com/danielgindi/Charts/issues/440)
- Label lines with different style, larger and not for all lines in grid [\#439](https://github.com/danielgindi/Charts/issues/439)
-  Pie chart data labels not in correct position. [\#436](https://github.com/danielgindi/Charts/issues/436)
- Glitch while scrolling [\#430](https://github.com/danielgindi/Charts/issues/430)
- A field crash issue for highestVisibleXIndex [\#329](https://github.com/danielgindi/Charts/issues/329)
- Empty configuration over full configuration [\#308](https://github.com/danielgindi/Charts/issues/308)
- \[BarChart\] single dataSet + force groupSpace=0.8 leads to wrong xAxis label rendering and highlight [\#284](https://github.com/danielgindi/Charts/issues/284)
- Prebuilt Frameworks & Tagged Releases [\#217](https://github.com/danielgindi/Charts/issues/217)

**Merged pull requests:**

- Changed \_chart access modifier from private to internal [\#478](https://github.com/danielgindi/Charts/pull/478) ([AlBirdie](https://github.com/AlBirdie))
- fixed noDataText and NoDataTextDescription texts ovelapping issue [\#457](https://github.com/danielgindi/Charts/pull/457) ([zntfdr](https://github.com/zntfdr))
- Only alow scaling further if the user can still zoom \(Fixes \#437\) [\#438](https://github.com/danielgindi/Charts/pull/438) ([iangmaia](https://github.com/iangmaia))
- Make the ChartXAxisRenderer more flexible: now possible to overwrite drawing the line or label of the ChartLimitLine [\#432](https://github.com/danielgindi/Charts/pull/432) ([pajai](https://github.com/pajai))

## [v2.1.4a](https://github.com/danielgindi/Charts/tree/v2.1.4a) (2015-10-02)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.1.4...v2.1.4a)

**Fixed bugs:**

- CGContextAddLineToPoint no current point.  [\#419](https://github.com/danielgindi/Charts/issues/419)
- Crash by initialize\(\) of CombinedChartView [\#406](https://github.com/danielgindi/Charts/issues/406)
- Radar chart + Bar Line chart Base + startAtZeroEnabled = false + negative values [\#166](https://github.com/danielgindi/Charts/issues/166)

**Closed issues:**

- RadarChart \(spider web chart\) issue  [\#435](https://github.com/danielgindi/Charts/issues/435)
- dyld\_fatal\_error [\#434](https://github.com/danielgindi/Charts/issues/434)
- Xcode 6.4 compilation errors [\#433](https://github.com/danielgindi/Charts/issues/433)
- Error: Can't build demo project with XCode Version 6.4 \(6E35b\) [\#431](https://github.com/danielgindi/Charts/issues/431)
- Custom ChartXAxisValueFormatter not Being Called with Data containing NSObjects [\#429](https://github.com/danielgindi/Charts/issues/429)
- Error Swift 2 - ChartViewBase.swift [\#428](https://github.com/danielgindi/Charts/issues/428)
- How to return xIndex that be selected in chart? [\#427](https://github.com/danielgindi/Charts/issues/427)
- always return 0 in chartValueSelected function  [\#426](https://github.com/danielgindi/Charts/issues/426)
- Radar Chart can not open in Charts Demo project [\#424](https://github.com/danielgindi/Charts/issues/424)
- ScatterChartRenderer - create a custom shape [\#423](https://github.com/danielgindi/Charts/issues/423)
- How to add custom text to the diagram [\#421](https://github.com/danielgindi/Charts/issues/421)
- App/Xcode crashing when charts are loaded [\#410](https://github.com/danielgindi/Charts/issues/410)
- Max Y value sometimes get clipped [\#405](https://github.com/danielgindi/Charts/issues/405)
- Display X-axis and y-axis names and Can i display x axis values vertically [\#403](https://github.com/danielgindi/Charts/issues/403)
- How to show time values in horizontal Bar chart? [\#402](https://github.com/danielgindi/Charts/issues/402)
- ios 9 - xcode 7 [\#400](https://github.com/danielgindi/Charts/issues/400)
- bar chart with gradient [\#398](https://github.com/danielgindi/Charts/issues/398)
- Multiple Lines in LineChart. [\#397](https://github.com/danielgindi/Charts/issues/397)
- Line Graph error [\#396](https://github.com/danielgindi/Charts/issues/396)
- Charts running on Simulator only  [\#381](https://github.com/danielgindi/Charts/issues/381)
- Multiple axis - line charts drawing [\#366](https://github.com/danielgindi/Charts/issues/366)
- Marker frame adjustment according to bounds [\#364](https://github.com/danielgindi/Charts/issues/364)
- Sequencing of legend titles [\#363](https://github.com/danielgindi/Charts/issues/363)
- Legend has only one label [\#361](https://github.com/danielgindi/Charts/issues/361)
- Can add the missing value support [\#355](https://github.com/danielgindi/Charts/issues/355)
- CombinedChartView line + bar [\#353](https://github.com/danielgindi/Charts/issues/353)
- drawFillEnabled in line chart bug? [\#351](https://github.com/danielgindi/Charts/issues/351)
- \[BUG\] highlighted bar animation on Y Axis for all bar charts is wrong and not same speed [\#349](https://github.com/danielgindi/Charts/issues/349)
- HELP : some problem never met about iOS-charts [\#341](https://github.com/danielgindi/Charts/issues/341)
- Multiple line Chart, same view [\#340](https://github.com/danielgindi/Charts/issues/340)
- Position Pie Chart over the View [\#337](https://github.com/danielgindi/Charts/issues/337)
- Having multiple colour bands with an area that changes dynamically over time [\#336](https://github.com/danielgindi/Charts/issues/336)
- Adding Large number of data points -- addEntry\(\) [\#333](https://github.com/danielgindi/Charts/issues/333)
- Can I implement this kind of chart with this library? [\#328](https://github.com/danielgindi/Charts/issues/328)
- iOS Chart Y-Axis High Limit [\#317](https://github.com/danielgindi/Charts/issues/317)
- how to place the Y axis label at the bottom of the chart? [\#314](https://github.com/danielgindi/Charts/issues/314)
- Real-time Charts [\#263](https://github.com/danielgindi/Charts/issues/263)
- Drawing circles for values on the radar chart [\#138](https://github.com/danielgindi/Charts/issues/138)

**Merged pull requests:**

- Start of pan gesture should not be cancelled by no drag [\#420](https://github.com/danielgindi/Charts/pull/420) ([niraj-rayalla](https://github.com/niraj-rayalla))
- Allow the minimum offset to be customized [\#395](https://github.com/danielgindi/Charts/pull/395) ([icecrystal23](https://github.com/icecrystal23))
- Add support for a legend above the chart [\#393](https://github.com/danielgindi/Charts/pull/393) ([icecrystal23](https://github.com/icecrystal23))
- Add target for tvOS and get it to compile [\#392](https://github.com/danielgindi/Charts/pull/392) ([icecrystal23](https://github.com/icecrystal23))
- be explicit on how to install 'Charts' when using CocoaPods since [\#376](https://github.com/danielgindi/Charts/pull/376) ([codeHatcher](https://github.com/codeHatcher))

## [v2.1.4](https://github.com/danielgindi/Charts/tree/v2.1.4) (2015-09-21)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.1.3...v2.1.4)

**Fixed bugs:**

- UIPanGestureRecognizer causes index out of bounds crash [\#327](https://github.com/danielgindi/Charts/issues/327)
- Radar chart radius is still affected by label size when label drawing is disabled [\#153](https://github.com/danielgindi/Charts/issues/153)
- XGrid not in sync for identical x values but different chart types [\#99](https://github.com/danielgindi/Charts/issues/99)

**Closed issues:**

- Xcode 7 and iOS 9 Support \(Deployment Target iOS 7.0\) [\#404](https://github.com/danielgindi/Charts/issues/404)
- Legend titles aren't printing if there are more than 5 [\#401](https://github.com/danielgindi/Charts/issues/401)
- Swift 2.0 branch not found [\#399](https://github.com/danielgindi/Charts/issues/399)
- Values are not drawn in the correct location when the y-axis does not start at zero [\#391](https://github.com/danielgindi/Charts/issues/391)
- Swift-2.0 branch runtime crashes after distributed on the TestFlight [\#390](https://github.com/danielgindi/Charts/issues/390)
- ios-charts Integration problem [\#389](https://github.com/danielgindi/Charts/issues/389)
- Can't change X-axis label color [\#387](https://github.com/danielgindi/Charts/issues/387)
- StackedBarChart in horizontal manner [\#384](https://github.com/danielgindi/Charts/issues/384)
- Add ios-charts to existing project in XCode 7 [\#383](https://github.com/danielgindi/Charts/issues/383)
- Xcode7 Swift 2.0 issue \[ChartSelectionDetail.swift\] [\#380](https://github.com/danielgindi/Charts/issues/380)
- Installing ios-charts in an existing project [\#379](https://github.com/danielgindi/Charts/issues/379)
- I upgraded my UXcode 7,I can't use charts framework. [\#378](https://github.com/danielgindi/Charts/issues/378)
- How to achieve 0 spacing between bars? [\#365](https://github.com/danielgindi/Charts/issues/365)
- Xcode 7.0 and Swift 2.0 issue [\#358](https://github.com/danielgindi/Charts/issues/358)
- Unable to run a demo [\#356](https://github.com/danielgindi/Charts/issues/356)
- Using Embedded Binaries giving build failed error in xcode 7 ? [\#354](https://github.com/danielgindi/Charts/issues/354)
- is there a object-c version this project? [\#352](https://github.com/danielgindi/Charts/issues/352)
- adding cross platform feature [\#350](https://github.com/danielgindi/Charts/issues/350)
- Some problems about CandleStickChart [\#348](https://github.com/danielgindi/Charts/issues/348)
- Some problems about CandleStickChart [\#347](https://github.com/danielgindi/Charts/issues/347)
- Pie chart is getting drawn again and again on rotation in iOS  [\#346](https://github.com/danielgindi/Charts/issues/346)
- 对于高于限制线的点，我怎么改变点的样式和颜色呢？ [\#345](https://github.com/danielgindi/Charts/issues/345)
- Swift-2.0 Branch Error [\#344](https://github.com/danielgindi/Charts/issues/344)
- Multiple lines  [\#342](https://github.com/danielgindi/Charts/issues/342)
- Daniel are you really fit? [\#339](https://github.com/danielgindi/Charts/issues/339)
- For CandleStickChart, how can I limit according to the count of candles [\#334](https://github.com/danielgindi/Charts/issues/334)
- how can i disable the doubles? i only need the chat show int data [\#332](https://github.com/danielgindi/Charts/issues/332)
- Bug: Xcode 7 beta 5: dyld: Library not loaded: @rpath/Charts.framework/Charts [\#326](https://github.com/danielgindi/Charts/issues/326)
- Disappearing Line Segment when scrolling [\#325](https://github.com/danielgindi/Charts/issues/325)
- CombinedChart with BarChartData cuts the first and last bars in half [\#323](https://github.com/danielgindi/Charts/issues/323)
- Bug \(?\) in CandleStickChartRenderer when showing empty dataset among multiple datasets [\#320](https://github.com/danielgindi/Charts/issues/320)
- Xcode 7 Beta 6 print\(.. appendNewLine\) - Swift 2.0 branch [\#316](https://github.com/danielgindi/Charts/issues/316)
- Ability to disable legends completely [\#312](https://github.com/danielgindi/Charts/issues/312)
- BarChart draws additional horizontal grid lines when setting custom customAxisMax/labelCount [\#310](https://github.com/danielgindi/Charts/issues/310)
- Take selectionShift into account when displaying pie chart [\#309](https://github.com/danielgindi/Charts/issues/309)
- Line Chart Line is not drawing properly for the large yValues [\#307](https://github.com/danielgindi/Charts/issues/307)
- Zero valued slices overlap [\#305](https://github.com/danielgindi/Charts/issues/305)
- setVisibleXRangeMaximum working differently than expected [\#304](https://github.com/danielgindi/Charts/issues/304)
- MyProject-Swift.h header not found when embedding framework into ObjectiveC project [\#303](https://github.com/danielgindi/Charts/issues/303)
- Using NSNumberFormatter or .getBarBounds\(\) to Display Custom Values [\#302](https://github.com/danielgindi/Charts/issues/302)
- Unknown class \_TtC6Charts12BarChartView in Interface Builder file. [\#301](https://github.com/danielgindi/Charts/issues/301)
- Possible regression because of recent `startAtZeroEnabled` changes [\#300](https://github.com/danielgindi/Charts/issues/300)
- Axis titles? [\#299](https://github.com/danielgindi/Charts/issues/299)
- xcode 6.4 , Swift 1.2 build fail [\#298](https://github.com/danielgindi/Charts/issues/298)
- it has this problem "'\#' has been removed from Swift" in many places [\#296](https://github.com/danielgindi/Charts/issues/296)
- chartScaled not so useful without the touch location [\#295](https://github.com/danielgindi/Charts/issues/295)
- ChartMarker don't draw [\#293](https://github.com/danielgindi/Charts/issues/293)
- Multiple line charts on the same graph [\#292](https://github.com/danielgindi/Charts/issues/292)
- Release Version 2.1.3 on CocoaPods [\#289](https://github.com/danielgindi/Charts/issues/289)
- saveToCameraRoll strange behavior [\#288](https://github.com/danielgindi/Charts/issues/288)
- ios-charts for swift 2.0 cannot compile on XCode7 beta 5 - self.dynamicType.allocWithZone\(zone\) issue [\#287](https://github.com/danielgindi/Charts/issues/287)
- installing with cocoapods [\#283](https://github.com/danielgindi/Charts/issues/283)
- iOS 9 Won't Compile \(Also, no such module 'Charts'\) [\#282](https://github.com/danielgindi/Charts/issues/282)
- Allow to align the Legend! [\#279](https://github.com/danielgindi/Charts/issues/279)
- Demo chart for Swift? [\#278](https://github.com/danielgindi/Charts/issues/278)
- Cocoapods support [\#277](https://github.com/danielgindi/Charts/issues/277)
- ios-charts different plotting activity than MKAndroidChart [\#276](https://github.com/danielgindi/Charts/issues/276)
- Highlight Doest'n work properly on UIPageViewController in LineChart. [\#275](https://github.com/danielgindi/Charts/issues/275)
- command failed due to signal: Abort trap: 6 charts [\#271](https://github.com/danielgindi/Charts/issues/271)
- having hard time to put working on version iOS 7.1 [\#270](https://github.com/danielgindi/Charts/issues/270)
- Library not found while building application through distribution certificate [\#266](https://github.com/danielgindi/Charts/issues/266)
- Crash on one entry point in charts [\#265](https://github.com/danielgindi/Charts/issues/265)
- when add points dynamicly, scale  is changeing, [\#264](https://github.com/danielgindi/Charts/issues/264)
- About Auto-scaling on axis [\#259](https://github.com/danielgindi/Charts/issues/259)
- \[CombinedChart\] Grouped Bars + line chart [\#252](https://github.com/danielgindi/Charts/issues/252)
- \*\*\* Announcements \*\*\* [\#236](https://github.com/danielgindi/Charts/issues/236)
- \[BUG\] xAxis label too long causes radar chart cannot render correctly [\#205](https://github.com/danielgindi/Charts/issues/205)

**Merged pull requests:**

- Allow setting maximum y-scale factor [\#388](https://github.com/danielgindi/Charts/pull/388) ([noais](https://github.com/noais))
- Swift 2.0 [\#386](https://github.com/danielgindi/Charts/pull/386) ([danielgindi](https://github.com/danielgindi))
- Fix default value of forceLabelsEnabled [\#360](https://github.com/danielgindi/Charts/pull/360) ([yas375](https://github.com/yas375))
- Update BarLineChartViewBase.swift [\#359](https://github.com/danielgindi/Charts/pull/359) ([Ewg777](https://github.com/Ewg777))
- combined chart - seems we should use same chartXMin and chartXMax even there is no bubble data [\#324](https://github.com/danielgindi/Charts/pull/324) ([liuxuan30](https://github.com/liuxuan30))
- fix pie chart clipping [\#313](https://github.com/danielgindi/Charts/pull/313) ([petester42](https://github.com/petester42))
- Changed Release signing configuration to "iOS Developer" to improve Carthage compatibility [\#297](https://github.com/danielgindi/Charts/pull/297) ([JaviSoto](https://github.com/JaviSoto))
- Fixes xcode beta 5 compile errors [\#291](https://github.com/danielgindi/Charts/pull/291) ([petester42](https://github.com/petester42))
- bump podspec to 2.1.3 [\#290](https://github.com/danielgindi/Charts/pull/290) ([petester42](https://github.com/petester42))
- Minor refactor for BarLineChartViewBase [\#268](https://github.com/danielgindi/Charts/pull/268) ([liuxuan30](https://github.com/liuxuan30))
- Enhanced label positioning at limit lines \(enum ChartLimitLabelPosition\) [\#243](https://github.com/danielgindi/Charts/pull/243) ([SvenMuc](https://github.com/SvenMuc))

## [v2.1.3](https://github.com/danielgindi/Charts/tree/v2.1.3) (2015-08-05)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.1.2...v2.1.3)

**Closed issues:**

- Limit Legend for combined charts [\#272](https://github.com/danielgindi/Charts/issues/272)
- Default zoom level at a position. [\#267](https://github.com/danielgindi/Charts/issues/267)
- drawValuesForWholeStackEnabled deprecated? [\#261](https://github.com/danielgindi/Charts/issues/261)
- Another Command failed due to signal: Segmentation fault: 11 [\#260](https://github.com/danielgindi/Charts/issues/260)
- moveViewToX [\#258](https://github.com/danielgindi/Charts/issues/258)
- How to add point dynamicly [\#257](https://github.com/danielgindi/Charts/issues/257)
- Real time graph - X-axis moving? [\#255](https://github.com/danielgindi/Charts/issues/255)
- 2.1.2 Not Yet in CocoaPods Master Repo [\#251](https://github.com/danielgindi/Charts/issues/251)
- Heads up [\#250](https://github.com/danielgindi/Charts/issues/250)
- Disable HorizontalHighlightIndicator on LineChart. [\#249](https://github.com/danielgindi/Charts/issues/249)
- double vision in the chart [\#245](https://github.com/danielgindi/Charts/issues/245)
- Can't find anything like "isWordWrapEnabled" in PieChart [\#244](https://github.com/danielgindi/Charts/issues/244)
- Grouped columns misplaced [\#242](https://github.com/danielgindi/Charts/issues/242)
- Swift2 upgrade problem "Redundant conformance of 'ChartDataEntry' to protocol 'Equatable'" [\#241](https://github.com/danielgindi/Charts/issues/241)
- App created by Archive cannot run on devices. [\#240](https://github.com/danielgindi/Charts/issues/240)
- What's the differences between setAxisMaximum and customAxisMax [\#239](https://github.com/danielgindi/Charts/issues/239)
- Problem importing framework to Xcode 6.4 in Swift Project [\#233](https://github.com/danielgindi/Charts/issues/233)
- PieChartData initialisers ambiguous when xVals parameter is nil [\#224](https://github.com/danielgindi/Charts/issues/224)
- \[BUG\] all-negative Stacked horizontal Bar Chart size is not correct [\#222](https://github.com/danielgindi/Charts/issues/222)
- \[BUG\] Bar Chart incorrect rendering for special data [\#214](https://github.com/danielgindi/Charts/issues/214)
- Projects using Charts [\#145](https://github.com/danielgindi/Charts/issues/145)
- Legend Height Adjustment [\#81](https://github.com/danielgindi/Charts/issues/81)
- Multiple legend entrys will be rendered outside of the view [\#80](https://github.com/danielgindi/Charts/issues/80)

**Merged pull requests:**

- Add a Code Hunt vote badge to README.md [\#262](https://github.com/danielgindi/Charts/pull/262) ([CodeHuntIO](https://github.com/CodeHuntIO))
- Updated podspec [\#254](https://github.com/danielgindi/Charts/pull/254) ([petester42](https://github.com/petester42))
- try to fix bar chart + Horizontal Bar chart wrong render + highlight position bug for issue \#214 and \#242. [\#248](https://github.com/danielgindi/Charts/pull/248) ([liuxuan30](https://github.com/liuxuan30))

## [v2.1.2](https://github.com/danielgindi/Charts/tree/v2.1.2) (2015-07-26)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.1.1...v2.1.2)

## [v2.1.1](https://github.com/danielgindi/Charts/tree/v2.1.1) (2015-07-26)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.1.0...v2.1.1)

**Fixed bugs:**

- Render Crash Due to Rect NAN from rectValueToPixel in BarChartRenderer.swift [\#177](https://github.com/danielgindi/Charts/issues/177)
- Can we add nil values to a dataset yet? [\#113](https://github.com/danielgindi/Charts/issues/113)
- Incorrect chart view adjustment after moveViewtoX\(xIndex: Int\) [\#82](https://github.com/danielgindi/Charts/issues/82)
- Margin insets not changing during zoom when formatter changes output width [\#72](https://github.com/danielgindi/Charts/issues/72)
- Line chart crashes with a big float [\#62](https://github.com/danielgindi/Charts/issues/62)

**Closed issues:**

- App is crashing when line chart is scrolled into view and then back out. [\#238](https://github.com/danielgindi/Charts/issues/238)
- Demo Project: In BalloonMarker.swift : "Error: No such module 'charts' " XCode 6.4  [\#237](https://github.com/danielgindi/Charts/issues/237)
- How to draw Line segments in one Line Chart. [\#235](https://github.com/danielgindi/Charts/issues/235)
- How to disable the continuos selection in chart with swipe? [\#234](https://github.com/danielgindi/Charts/issues/234)
- Podspec 3 months old [\#231](https://github.com/danielgindi/Charts/issues/231)
- Setting background in Objective C [\#230](https://github.com/danielgindi/Charts/issues/230)
- ChartYAxis valueFormatter NSFormatter instead of NSNumberFormatter [\#228](https://github.com/danielgindi/Charts/issues/228)
- How to setup up y-axis with min, max value and a fixed step between the grid lines? [\#227](https://github.com/danielgindi/Charts/issues/227)
- What's the proper way to manually set chart's scale and scroll offset? [\#226](https://github.com/danielgindi/Charts/issues/226)
- How could you set the Y values at the bottom in LineChart? [\#223](https://github.com/danielgindi/Charts/issues/223)
- Is there guidance to beginner to use this? [\#220](https://github.com/danielgindi/Charts/issues/220)
- Chart's PanGesture conflicts with UITableView PanGesture [\#219](https://github.com/danielgindi/Charts/issues/219)
- \(swift-2.0 branch\) building Charts.xcodeproj fails \(exit code 65\) with Release configuration [\#218](https://github.com/danielgindi/Charts/issues/218)
- Crash on device - iOS 8.3 [\#216](https://github.com/danielgindi/Charts/issues/216)
- Remove dependencies warning [\#215](https://github.com/danielgindi/Charts/issues/215)
- Update Podspec to reflect commits since early May? [\#213](https://github.com/danielgindi/Charts/issues/213)
- \[BUG\]negative values rendering +highlight rect rendering not correct for stacked bar + stacked horizontal bar chart [\#212](https://github.com/danielgindi/Charts/issues/212)
- \[BUG\]postive bars in demo Stack BarChart Negative disappear after scrolling [\#211](https://github.com/danielgindi/Charts/issues/211)
- Horizontal bar chart in combined bar chart. [\#210](https://github.com/danielgindi/Charts/issues/210)
- Can I use this library in an objective-c project targeting iOS 7 & 8? [\#206](https://github.com/danielgindi/Charts/issues/206)
- When animating charts after reload, chart is shown fully drawn before animating. [\#204](https://github.com/danielgindi/Charts/issues/204)
- Array index out of range when removing entries from DataSet [\#203](https://github.com/danielgindi/Charts/issues/203)
- Issue while displaying the chart \(HorizontalBarChartView\) [\#202](https://github.com/danielgindi/Charts/issues/202)
- Charts stuttering when reloaded [\#201](https://github.com/danielgindi/Charts/issues/201)
- Scaling Yaxis [\#199](https://github.com/danielgindi/Charts/issues/199)
- Don't draw zero value label [\#197](https://github.com/danielgindi/Charts/issues/197)
- Library not loaded: @rpath/Charts.framework/Charts [\#196](https://github.com/danielgindi/Charts/issues/196)
- How can i get this chart? [\#195](https://github.com/danielgindi/Charts/issues/195)
- Crashing on adding the null value to and array  [\#193](https://github.com/danielgindi/Charts/issues/193)
- ChartDataEntry not drawn after certain xIndex [\#190](https://github.com/danielgindi/Charts/issues/190)
- Highlight multiple values [\#187](https://github.com/danielgindi/Charts/issues/187)
- ChartData.removeEntryByXIndex removes the wrong entry [\#182](https://github.com/danielgindi/Charts/issues/182)
- Label on Limit Lines cause crash [\#181](https://github.com/danielgindi/Charts/issues/181)
- \[feature request\] render xAxis like yAxis style Namely from discrete way to continuous way [\#176](https://github.com/danielgindi/Charts/issues/176)
- Annotation label support? [\#175](https://github.com/danielgindi/Charts/issues/175)
- Multiple DataSet Error [\#174](https://github.com/danielgindi/Charts/issues/174)
- AddEntry issue in ChartData [\#173](https://github.com/danielgindi/Charts/issues/173)
- Bar Chart Bars Disappears On Zoom And Highlight Bars Don't Match [\#172](https://github.com/danielgindi/Charts/issues/172)
- Clearing all highlighted values [\#171](https://github.com/danielgindi/Charts/issues/171)
- BarChartView work with TableView  [\#167](https://github.com/danielgindi/Charts/issues/167)
- Make scroll to graph without pinch [\#164](https://github.com/danielgindi/Charts/issues/164)
- When i set valueformatter , Have problem. [\#163](https://github.com/danielgindi/Charts/issues/163)
- Combined Chart With array of line charts [\#162](https://github.com/danielgindi/Charts/issues/162)
- Issues with chart rendering... [\#161](https://github.com/danielgindi/Charts/issues/161)
- How to set yAxis to normal number  [\#160](https://github.com/danielgindi/Charts/issues/160)
- How to set yValue display 0 2 3 5  [\#159](https://github.com/danielgindi/Charts/issues/159)
- \[BUG\] \_yAxis.entries will not be cleaned up in computeAxisValues in radar chart [\#158](https://github.com/danielgindi/Charts/issues/158)
- \[feature request\] support fixed yAxis interval and xAxis labelCount [\#155](https://github.com/danielgindi/Charts/issues/155)
- app crashes at launch as in house release build on iPhone - but debugging is fine [\#154](https://github.com/danielgindi/Charts/issues/154)
- \[feature request\] enable legend view scrollable [\#151](https://github.com/danielgindi/Charts/issues/151)
- \[feature request\] support custom handler for gestures [\#148](https://github.com/danielgindi/Charts/issues/148)
- unsuccessful at installing for ios 7.0 [\#144](https://github.com/danielgindi/Charts/issues/144)
- cannot po swift local variable in my Objective-C project [\#143](https://github.com/danielgindi/Charts/issues/143)
- Real time line chart  [\#142](https://github.com/danielgindi/Charts/issues/142)
- Bar chart some values \< 0 [\#141](https://github.com/danielgindi/Charts/issues/141)
- Any demo in swift ? [\#139](https://github.com/danielgindi/Charts/issues/139)
- compiling time costs 2-3 minutes after import swift code files, is it normal? [\#137](https://github.com/danielgindi/Charts/issues/137)
- iOS7 can't build success [\#136](https://github.com/danielgindi/Charts/issues/136)
- Warnings on the comments [\#135](https://github.com/danielgindi/Charts/issues/135)
- Still seeing undeclared UIColor, CGFloat error with latest code [\#133](https://github.com/danielgindi/Charts/issues/133)
- Error while drawing radar chart [\#132](https://github.com/danielgindi/Charts/issues/132)
- Cannot assign a value of type 'ChartLimitLabelPosition' to a value of type 'ChartLimitLine.ChartLimitLabelPosition' [\#131](https://github.com/danielgindi/Charts/issues/131)
- Data disappearing while zooming/panning when y-values are not ordered by x [\#130](https://github.com/danielgindi/Charts/issues/130)
- Cannot load underlying module for 'Charts' [\#129](https://github.com/danielgindi/Charts/issues/129)
- Any way to add/use image for xVals? [\#126](https://github.com/danielgindi/Charts/issues/126)
- Install with cocoapod into project supporting iOS 7 and later [\#125](https://github.com/danielgindi/Charts/issues/125)
- Lines clipping top and bottom with lineWidth \> 1 [\#123](https://github.com/danielgindi/Charts/issues/123)
- Candle chart - make the shadow same color as an increasing/decreasing candle color  [\#122](https://github.com/danielgindi/Charts/issues/122)
- Down-casted Array element failed to match the target type [\#121](https://github.com/danielgindi/Charts/issues/121)
- y axis spaces between labels are too wide [\#120](https://github.com/danielgindi/Charts/issues/120)
- Different colors for single data set in Bar Chart [\#118](https://github.com/danielgindi/Charts/issues/118)
- The new version demo has a problem. [\#117](https://github.com/danielgindi/Charts/issues/117)
- non-public selectors while submitting app [\#116](https://github.com/danielgindi/Charts/issues/116)
- Adding a minimum parameter to setVisibleXRange [\#115](https://github.com/danielgindi/Charts/issues/115)
- Add 'drawHighlightEnabled' to ChartDataSet [\#114](https://github.com/danielgindi/Charts/issues/114)
- Pull to refresh [\#112](https://github.com/danielgindi/Charts/issues/112)
- Can we create thumb for each chart? [\#111](https://github.com/danielgindi/Charts/issues/111)
- Issue setting the chartView.delegate to self. [\#110](https://github.com/danielgindi/Charts/issues/110)
- combine swift in Objective-C project [\#108](https://github.com/danielgindi/Charts/issues/108)
- get X translation distance on the x-axis [\#105](https://github.com/danielgindi/Charts/issues/105)
- Horizontal Bar Chart Screenshot on README is Broken [\#103](https://github.com/danielgindi/Charts/issues/103)
- Bars [\#102](https://github.com/danielgindi/Charts/issues/102)
- Crash when redraw graph from 2 lines to 1 line with click on highlight  [\#101](https://github.com/danielgindi/Charts/issues/101)
- Release Bug [\#98](https://github.com/danielgindi/Charts/issues/98)
- Add '@objc' to ChartViewPortHandler [\#97](https://github.com/danielgindi/Charts/issues/97)
- Bar Chart Delegate. [\#96](https://github.com/danielgindi/Charts/issues/96)
- Cannot turn off Barchart Zoom Propertry [\#91](https://github.com/danielgindi/Charts/issues/91)
- calcMinMax\(\) during scrolling / panning [\#89](https://github.com/danielgindi/Charts/issues/89)
- defaultTouchEventsEnabled [\#88](https://github.com/danielgindi/Charts/issues/88)
- 'chartsample-Swift.h' file not found [\#87](https://github.com/danielgindi/Charts/issues/87)
- Infinite loop when setting data on ChartDataEntry object [\#86](https://github.com/danielgindi/Charts/issues/86)
- Custom text over bar chart. [\#84](https://github.com/danielgindi/Charts/issues/84)
- Stop hiding of xAxis label. [\#79](https://github.com/danielgindi/Charts/issues/79)
- Consider draw map chart without location, navigation stuff? [\#78](https://github.com/danielgindi/Charts/issues/78)
- Line Chart - Simple Design [\#77](https://github.com/danielgindi/Charts/issues/77)
- Can you give me a swift's demo [\#75](https://github.com/danielgindi/Charts/issues/75)
- What files exactly should I drag to my project to make it work on iOS 7.0? [\#73](https://github.com/danielgindi/Charts/issues/73)
- How to check chart is zoomed in ? [\#71](https://github.com/danielgindi/Charts/issues/71)
- Changing descriptionTextColor not possible [\#70](https://github.com/danielgindi/Charts/issues/70)
- Confused by this snippet code [\#69](https://github.com/danielgindi/Charts/issues/69)
- XAxis Co-Ordination with Y Axis [\#68](https://github.com/danielgindi/Charts/issues/68)
- Is there a way can make LineDataSets not selected when their visible property is no? [\#67](https://github.com/danielgindi/Charts/issues/67)
- Multiple Chart View Within ScrollView [\#65](https://github.com/danielgindi/Charts/issues/65)

**Merged pull requests:**

- Fixes to allow the code to compile with Swift 2.0 [\#232](https://github.com/danielgindi/Charts/pull/232) ([jmacmullin](https://github.com/jmacmullin))
- fix radar chart negative value rendering bug if startAtZeroEnabled is false for issue \#166 [\#207](https://github.com/danielgindi/Charts/pull/207) ([liuxuan30](https://github.com/liuxuan30))
- Fixes for beta 3 [\#200](https://github.com/danielgindi/Charts/pull/200) ([petester42](https://github.com/petester42))
- Performance Enhancements \#29 - candle chart [\#192](https://github.com/danielgindi/Charts/pull/192) ([dorsoft](https://github.com/dorsoft))
- Candle chart - make the shadow same color as an candle color \#122 [\#191](https://github.com/danielgindi/Charts/pull/191) ([dorsoft](https://github.com/dorsoft))
- ChartData.removeEntryByXIndex removes the wrong entry \#182 [\#185](https://github.com/danielgindi/Charts/pull/185) ([dorsoft](https://github.com/dorsoft))
- The line charts have started to properly display balloon markers [\#179](https://github.com/danielgindi/Charts/pull/179) ([Maxim-38RUS-Zabelin](https://github.com/Maxim-38RUS-Zabelin))
- Fix a silly bug. should check if first is -0.0 [\#165](https://github.com/danielgindi/Charts/pull/165) ([liuxuan30](https://github.com/liuxuan30))
- add NaN check to allow non-digits handling for radar chart [\#152](https://github.com/danielgindi/Charts/pull/152) ([liuxuan30](https://github.com/liuxuan30))
- optional protocol method should not be force unwrapped [\#147](https://github.com/danielgindi/Charts/pull/147) ([liuxuan30](https://github.com/liuxuan30))
- add missing module CoreGraphics for BubbleChartView [\#146](https://github.com/danielgindi/Charts/pull/146) ([liuxuan30](https://github.com/liuxuan30))
- Adding a minimum parameter to setVisibleXRange [\#119](https://github.com/danielgindi/Charts/pull/119) ([dorsoft](https://github.com/dorsoft))
- Added support for setting a custom width that is wider than the longe… [\#107](https://github.com/danielgindi/Charts/pull/107) ([AlBirdie](https://github.com/AlBirdie))
- Offset adjustment when drawLabels on the x axis is disabled. [\#106](https://github.com/danielgindi/Charts/pull/106) ([AlBirdie](https://github.com/AlBirdie))
- AutoScaling yAxis during panning / zooming [\#95](https://github.com/danielgindi/Charts/pull/95) ([AlBirdie](https://github.com/AlBirdie))
- Allow access to setLabelsToSkip from Objective-C. [\#93](https://github.com/danielgindi/Charts/pull/93) ([mkubenka](https://github.com/mkubenka))
- Changing iOS deployment target to 8.0 from 8.1 [\#74](https://github.com/danielgindi/Charts/pull/74) ([michaelmcguire](https://github.com/michaelmcguire))

## [v2.1.0](https://github.com/danielgindi/Charts/tree/v2.1.0) (2015-05-05)
[Full Changelog](https://github.com/danielgindi/Charts/compare/v2.0.9...v2.1.0)

**Implemented enhancements:**

- Drawing CubicLineChart based on NSDates [\#38](https://github.com/danielgindi/Charts/issues/38)
- Skip first and last value [\#23](https://github.com/danielgindi/Charts/issues/23)

**Fixed bugs:**

- Chart Line vanishes on pinch to zoom [\#63](https://github.com/danielgindi/Charts/issues/63)

**Closed issues:**

- Expose chartYMin and chartYMax to rendererDelegates [\#64](https://github.com/danielgindi/Charts/issues/64)
- dyld\_fatal\_error [\#60](https://github.com/danielgindi/Charts/issues/60)
- Unknown class LineChartView in Interface Builder file [\#59](https://github.com/danielgindi/Charts/issues/59)
- Is there any inertia effect in the library? [\#58](https://github.com/danielgindi/Charts/issues/58)
- Library not loaded: @rpath/Charts.framework/Charts Xcode 6.3.1, iOS 8.3 [\#57](https://github.com/danielgindi/Charts/issues/57)
- i am getting this issue? [\#55](https://github.com/danielgindi/Charts/issues/55)
- sample swift project of  danielgindi/ios-charts? [\#54](https://github.com/danielgindi/Charts/issues/54)
- i want develop charts in swift? [\#53](https://github.com/danielgindi/Charts/issues/53)
- When I used iOS-charts in iOS 7 project [\#52](https://github.com/danielgindi/Charts/issues/52)
- Changing the specific data point \(circle color\) in Line Chart [\#51](https://github.com/danielgindi/Charts/issues/51)
- When i use ios-charts with swift .. [\#50](https://github.com/danielgindi/Charts/issues/50)
- Charts won't draw unless initialized with a frame [\#49](https://github.com/danielgindi/Charts/issues/49)
- PieChart chartValueSelected don't return the good entry [\#48](https://github.com/danielgindi/Charts/issues/48)
- Cannot find this repo through Cocoapods.org [\#47](https://github.com/danielgindi/Charts/issues/47)
- Use of undeclared type 'CGFloat' [\#44](https://github.com/danielgindi/Charts/issues/44)
- CandleChartData isn't combinable with other data types [\#42](https://github.com/danielgindi/Charts/issues/42)
- Two graphs overlaying each other [\#41](https://github.com/danielgindi/Charts/issues/41)
- Charts with long Title  [\#40](https://github.com/danielgindi/Charts/issues/40)
- piechart - yOffset [\#39](https://github.com/danielgindi/Charts/issues/39)
- Two different-scale axis support for different data sets [\#36](https://github.com/danielgindi/Charts/issues/36)
- IBInspectable support? [\#34](https://github.com/danielgindi/Charts/issues/34)
- ChartsDemo-swift.h  file not found  [\#31](https://github.com/danielgindi/Charts/issues/31)
- Pinch zoom release triggers pan [\#30](https://github.com/danielgindi/Charts/issues/30)
- UISwipeGestureRecognizer [\#28](https://github.com/danielgindi/Charts/issues/28)
- pod not support iOS 7.0 ??? [\#27](https://github.com/danielgindi/Charts/issues/27)
- How to add a line-chart view in UITableViewCell ? [\#26](https://github.com/danielgindi/Charts/issues/26)
- Charts/Charts.h file not found [\#24](https://github.com/danielgindi/Charts/issues/24)
- Carthage support [\#21](https://github.com/danielgindi/Charts/issues/21)
- Unresolved identifier `CGFloat` in iOS 7 project [\#19](https://github.com/danielgindi/Charts/issues/19)
- Last item's visibility issue when zooming [\#18](https://github.com/danielgindi/Charts/issues/18)
- Unable to hide Y values [\#17](https://github.com/danielgindi/Charts/issues/17)

**Merged pull requests:**

- Fix x-axis limit line render issue. [\#66](https://github.com/danielgindi/Charts/pull/66) ([mkubenka](https://github.com/mkubenka))
- Added possibility to set the axisLabelModulus manually. [\#56](https://github.com/danielgindi/Charts/pull/56) ([webventil](https://github.com/webventil))
- Add missing UIKit imports for iOS 7 support [\#45](https://github.com/danielgindi/Charts/pull/45) ([msanders](https://github.com/msanders))
- Add 'init' to PieChartData to be used from Swift [\#37](https://github.com/danielgindi/Charts/pull/37) ([jmnavarro](https://github.com/jmnavarro))
- Bubble chart work by @petester42 [\#32](https://github.com/danielgindi/Charts/pull/32) ([danielgindi](https://github.com/danielgindi))
- Added Bubble Chart Type [\#25](https://github.com/danielgindi/Charts/pull/25) ([petester42](https://github.com/petester42))
- Shared Charts.framework scheme [\#22](https://github.com/danielgindi/Charts/pull/22) ([zenkimoto](https://github.com/zenkimoto))
- Add missing UIKit [\#20](https://github.com/danielgindi/Charts/pull/20) ([mkalmes](https://github.com/mkalmes))

## [v2.0.9](https://github.com/danielgindi/Charts/tree/v2.0.9) (2015-04-08)
[Full Changelog](https://github.com/danielgindi/Charts/compare/0.0.1...v2.0.9)

**Closed issues:**

- Import with Swift\(not objc\) not working [\#14](https://github.com/danielgindi/Charts/issues/14)

**Merged pull requests:**

- Added a podspec [\#13](https://github.com/danielgindi/Charts/pull/13) ([petester42](https://github.com/petester42))

## [0.0.1](https://github.com/danielgindi/Charts/tree/0.0.1) (2015-04-07)
**Closed issues:**

- Need Version to be compatible with Xcode 6.2  [\#12](https://github.com/danielgindi/Charts/issues/12)
- Can't import "module-swift.h" [\#11](https://github.com/danielgindi/Charts/issues/11)
- A lot of compile errors. [\#10](https://github.com/danielgindi/Charts/issues/10)
- 357 Compile Errors on framework files [\#8](https://github.com/danielgindi/Charts/issues/8)
- demo crashes, and no swift examples? [\#7](https://github.com/danielgindi/Charts/issues/7)
- xcode complains ChartsDemo-Swift.h can't be found [\#6](https://github.com/danielgindi/Charts/issues/6)
- Cocoapod [\#4](https://github.com/danielgindi/Charts/issues/4)
- Trying to compile demo code，but tons of compiler errors [\#3](https://github.com/danielgindi/Charts/issues/3)
- Command failed due to signal: Segmentation fault: 11 [\#2](https://github.com/danielgindi/Charts/issues/2)

**Merged pull requests:**

- Fix README typo [\#5](https://github.com/danielgindi/Charts/pull/5) ([nwest](https://github.com/nwest))
- Add a Bitdeli Badge to README [\#1](https://github.com/danielgindi/Charts/pull/1) ([bitdeli-chef](https://github.com/bitdeli-chef))



\* *This Change Log was automatically generated by [github_changelog_generator](https://github.com/skywinder/Github-Changelog-Generator)*