//
//  PlayGround
//
//  Copyright 2015 <PERSON> & <PERSON>
//  Copyright © 2017 thierry <PERSON>.
//  A port of MPAndroidChart for iOS
//  Licensed under Apache License 2.0
//
//  https://github.com/danielgindi/ios-charts
/*:
 
 ![Playground icon](feature_graphic.png)
 
 
 # Table of contents
 
 * [Line Chart](LineChart)
 
 * [Bar Chart](BarChart)
 
 * [Pie Chart](PieChart)
 
 * [Radar Chart](RadarChart)
 
 * [Scatter Chart](ScatterChart)
 
 * [Combined Chart](CombinedChart)
 
 * [Horizontal Chart](HorizontalBarChart)
 
 * [Candle Chart](CandleChart)
 
 * [Stacked Bar Chart](StackedBarChart)
 
 * [Bubble Chart](BubbleChart)
 
 
 */
//: [Next Chart](@next)

