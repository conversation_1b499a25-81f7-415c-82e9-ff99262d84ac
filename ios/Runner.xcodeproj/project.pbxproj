// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		01245FDC083EF9A29D5422E6 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F0D9DBE547833A806402BC6D /* Pods_Runner.framework */; };
		112E28CC245C6D0100A5DF37 /* AudioView.m in Sources */ = {isa = PBXBuildFile; fileRef = 112E28C8245C6D0100A5DF37 /* AudioView.m */; };
		112E28CD245C6D0100A5DF37 /* AudioViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 112E28C9245C6D0100A5DF37 /* AudioViewManager.m */; };
		112E2920245D0C4E00A5DF37 /* AudioPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 112E291F245D0C4E00A5DF37 /* AudioPlayer.m */; };
		116CAF9E24DE56CD00DB4C50 /* Codec.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF7D24DE56CC00DB4C50 /* Codec.m */; };
		116CAF9F24DE56CD00DB4C50 /* PermissionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF8024DE56CC00DB4C50 /* PermissionManager.m */; };
		116CAFA024DE56CD00DB4C50 /* PermissionHandlerModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF8224DE56CC00DB4C50 /* PermissionHandlerModule.m */; };
		116CAFA124DE56CD00DB4C50 /* MediaLibraryPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF8424DE56CC00DB4C50 /* MediaLibraryPermissionStrategy.m */; };
		116CAFA224DE56CD00DB4C50 /* LocationPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF8524DE56CC00DB4C50 /* LocationPermissionStrategy.m */; };
		116CAFA324DE56CD00DB4C50 /* EventPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF8624DE56CC00DB4C50 /* EventPermissionStrategy.m */; };
		116CAFA424DE56CD00DB4C50 /* StoragePermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF8824DE56CC00DB4C50 /* StoragePermissionStrategy.m */; };
		116CAFA524DE56CD00DB4C50 /* SensorPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF8A24DE56CC00DB4C50 /* SensorPermissionStrategy.m */; };
		116CAFA624DE56CD00DB4C50 /* SpeechPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF8C24DE56CC00DB4C50 /* SpeechPermissionStrategy.m */; };
		116CAFA724DE56CD00DB4C50 /* PhotoPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF9424DE56CC00DB4C50 /* PhotoPermissionStrategy.m */; };
		116CAFA824DE56CD00DB4C50 /* PhonePermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF9624DE56CC00DB4C50 /* PhonePermissionStrategy.m */; };
		116CAFA924DE56CD00DB4C50 /* AudioVideoPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF9724DE56CC00DB4C50 /* AudioVideoPermissionStrategy.m */; };
		116CAFAA24DE56CD00DB4C50 /* UnknownPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF9824DE56CC00DB4C50 /* UnknownPermissionStrategy.m */; };
		116CAFAB24DE56CD00DB4C50 /* NotificationPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF9924DE56CC00DB4C50 /* NotificationPermissionStrategy.m */; };
		116CAFAC24DE56CD00DB4C50 /* ContactPermissionStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 116CAF9C24DE56CC00DB4C50 /* ContactPermissionStrategy.m */; };
		117A558724DED67700E424CA /* BarcodeScanModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 117A558624DED67700E424CA /* BarcodeScanModule.m */; };
		118E723028F2C27B0079006F /* SVGView.m in Sources */ = {isa = PBXBuildFile; fileRef = 118E722C28F2C27B0079006F /* SVGView.m */; };
		118E723128F2C27B0079006F /* SVGViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 118E722E28F2C27B0079006F /* SVGViewManager.m */; };
		118F0C8124E8121100EAAD89 /* ShareModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 118F0C8024E8121100EAAD89 /* ShareModule.m */; };
		1192AE76241CD5E9007D8797 /* HippyViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1192AE75241CD5E8007D8797 /* HippyViewController.m */; };
		1192B0E0241CF7AA007D8797 /* res in Resources */ = {isa = PBXBuildFile; fileRef = 1192B0DF241CF7AA007D8797 /* res */; };
		11B49487243997F80009856A /* ABCViewAnimationController.m in Sources */ = {isa = PBXBuildFile; fileRef = 11B49485243997F80009856A /* ABCViewAnimationController.m */; };
		11B4948A243998160009856A /* ABCViewControllerAnimationController.m in Sources */ = {isa = PBXBuildFile; fileRef = 11B49489243998160009856A /* ABCViewControllerAnimationController.m */; };
		11D552B82436187B0049D7ED /* ABCAnimationCenter.m in Sources */ = {isa = PBXBuildFile; fileRef = 11D552B72436187B0049D7ED /* ABCAnimationCenter.m */; };
		11D552BE243618DF0049D7ED /* ABCBaseAnimation.m in Sources */ = {isa = PBXBuildFile; fileRef = 11D552BA243618DE0049D7ED /* ABCBaseAnimation.m */; };
		11D552BF243618DF0049D7ED /* ABCBaseInteraction.m in Sources */ = {isa = PBXBuildFile; fileRef = 11D552BD243618DE0049D7ED /* ABCBaseInteraction.m */; };
		11D552C7243623060049D7ED /* ABCBaseNavigationController.m in Sources */ = {isa = PBXBuildFile; fileRef = 11D552C5243623060049D7ED /* ABCBaseNavigationController.m */; };
		11D552CA243625280049D7ED /* HostHippyMessageBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 11D552C9243625280049D7ED /* HostHippyMessageBridge.m */; };
		11DA640E2445FCE500DBA6C6 /* WheelViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 11DA64062445FCE500DBA6C6 /* WheelViewManager.m */; };
		11DA64112445FCE500DBA6C6 /* WheelView.m in Sources */ = {isa = PBXBuildFile; fileRef = 11DA640B2445FCE500DBA6C6 /* WheelView.m */; };
		35B64AD22689F9C000CF0190 /* AbcPieChartViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 35B64AD12689F9C000CF0190 /* AbcPieChartViewManager.m */; };
		35B64AD42689F9EA00CF0190 /* AbcPieChartView.m in Sources */ = {isa = PBXBuildFile; fileRef = 35B64AD32689F9EA00CF0190 /* AbcPieChartView.m */; };
		660081462481F3C000761AE3 /* WillPopListenerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 660081442481F3C000761AE3 /* WillPopListenerManager.m */; };
		660081492481F4C400761AE3 /* UIView+ABCExt.m in Sources */ = {isa = PBXBuildFile; fileRef = 660081482481F4C400761AE3 /* UIView+ABCExt.m */; };
		6601D12124AC417500BC67C0 /* AbcCustomKeyboardViewManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 6601D11F24AC417500BC67C0 /* AbcCustomKeyboardViewManager.mm */; };
		6601D12424AC41D300BC67C0 /* AbcCustomKeyboardView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6601D12224AC41D300BC67C0 /* AbcCustomKeyboardView.m */; };
		6608379624E4DA0400530B70 /* StatManagerModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 6608379424E4DA0400530B70 /* StatManagerModule.m */; };
		6608379A24E4DEC100530B70 /* BuglyModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 6608379924E4DEC100530B70 /* BuglyModule.m */; };
		660929AF256F3FDC00436093 /* UrlLauncherModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 660929AE256F3FDC00436093 /* UrlLauncherModule.m */; };
		6610A83324D3DE41006D24A0 /* BarcodeScanManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6610A83224D3DE41006D24A0 /* BarcodeScanManager.m */; };
		66137F0224D8E9C900E44F63 /* AbcFetchInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137EFE24D8E9C900E44F63 /* AbcFetchInfo.m */; };
		66137F0324D8E9C900E44F63 /* AbcNetwork.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137EFF24D8E9C900E44F63 /* AbcNetwork.m */; };
		66137F0724D94B6300E44F63 /* FileUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137F0624D94B6300E44F63 /* FileUtils.m */; };
		66137F8124D9808F00E44F63 /* PluginUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137F8024D9808F00E44F63 /* PluginUtils.m */; };
		66137F8524D9836E00E44F63 /* AppInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137F8424D9836E00E44F63 /* AppInfo.m */; };
		66137F8A24DA382D00E44F63 /* UrlLanucherUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137F8924DA382D00E44F63 /* UrlLanucherUtils.m */; };
		66137F8E24DA819D00E44F63 /* SocketIOModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137F8D24DA819C00E44F63 /* SocketIOModule.m */; };
		66137FAC24DBA9C900E44F63 /* NotificationAttachment.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137FA424DBA9C900E44F63 /* NotificationAttachment.m */; };
		66137FAD24DBA9C900E44F63 /* NotificationTime.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137FA524DBA9C900E44F63 /* NotificationTime.m */; };
		66137FAE24DBA9C900E44F63 /* NotificationDetails.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137FA824DBA9C900E44F63 /* NotificationDetails.m */; };
		66137FAF24DBA9C900E44F63 /* LocalNotificationModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137FAA24DBA9C900E44F63 /* LocalNotificationModule.m */; };
		66137FBF24DBE04000E44F63 /* AbcBaseAppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137FBE24DBE04000E44F63 /* AbcBaseAppDelegate.m */; };
		66137FC224DBF16800E44F63 /* XGPushModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 66137FC024DBF16700E44F63 /* XGPushModule.m */; };
		66220EB82447E96B00DA8A34 /* FileManagerModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 66220EB62447E96B00DA8A34 /* FileManagerModule.m */; };
		6625AA09246D20F200E55AE9 /* AbcHippyTextViewImpl.mm in Sources */ = {isa = PBXBuildFile; fileRef = 6625AA08246D20F200E55AE9 /* AbcHippyTextViewImpl.mm */; };
		6625AA0F246D222200E55AE9 /* AbcHippyTextViewImplManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 6625AA0E246D222200E55AE9 /* AbcHippyTextViewImplManager.mm */; };
		662AB36A24DCE30F00596BB6 /* OSSModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 662AB36824DCE30F00596BB6 /* OSSModule.m */; };
		662AB36F24DCE57F00596BB6 /* OssFileUploader.mm in Sources */ = {isa = PBXBuildFile; fileRef = 662AB36B24DCE57F00596BB6 /* OssFileUploader.mm */; };
		662AB37024DCE57F00596BB6 /* OssManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 662AB36D24DCE57F00596BB6 /* OssManager.mm */; };
		6632F0F824CEC14A00B1BC5C /* LICENSE.md in Resources */ = {isa = PBXBuildFile; fileRef = 6632F0D524CEC14A00B1BC5C /* LICENSE.md */; };
		6632F0F924CEC14A00B1BC5C /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = 6632F0D624CEC14A00B1BC5C /* README.md */; };
		66413FFA2448618A0029D998 /* TouchListenerViewTouchHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 66413FF82448618A0029D998 /* TouchListenerViewTouchHandler.m */; };
		6643498024D29C6400137935 /* AbcLineChartViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6643497D24D29C6400137935 /* AbcLineChartViewManager.m */; };
		6643498124D29C6400137935 /* AbcLineChartView.m in Sources */ = {isa = PBXBuildFile; fileRef = 6643497F24D29C6400137935 /* AbcLineChartView.m */; };
		6643498524D2C23D00137935 /* DateValueFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6643498324D2C23D00137935 /* DateValueFormatter.m */; };
		664AE9432435FEE10063FCC2 /* CAKeyframeAnimation+AHEasing.m in Sources */ = {isa = PBXBuildFile; fileRef = 664AE93D2435FEE00063FCC2 /* CAKeyframeAnimation+AHEasing.m */; };
		664AE9442435FEE10063FCC2 /* UIView+ABCAnimation.m in Sources */ = {isa = PBXBuildFile; fileRef = 664AE93E2435FEE00063FCC2 /* UIView+ABCAnimation.m */; };
		664AE9452435FEE10063FCC2 /* UIUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 664AE9412435FEE00063FCC2 /* UIUtils.m */; };
		664AE9872436003E0063FCC2 /* ABCBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 664AE9852436003E0063FCC2 /* ABCBaseViewController.m */; };
		664AE98A2436010A0063FCC2 /* easing.c in Sources */ = {isa = PBXBuildFile; fileRef = 664AE9882436010A0063FCC2 /* easing.c */; };
		664BC55D24D3A81D00825CF5 /* WXApiHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 664BC55C24D3A81D00825CF5 /* WXApiHandler.m */; };
		664BC56324D3C57400825CF5 /* HippyMixInvokeMethodModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 664BC56124D3C57400825CF5 /* HippyMixInvokeMethodModule.m */; };
		664BC57524D3C92100825CF5 /* ScannerOverlay.m in Sources */ = {isa = PBXBuildFile; fileRef = 664BC56C24D3C92100825CF5 /* ScannerOverlay.m */; };
		664BC57624D3C92100825CF5 /* BarcodeScannerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 664BC56D24D3C92100825CF5 /* BarcodeScannerViewController.m */; };
		664BC57824D3C92100825CF5 /* UIButton+TouchArea.m in Sources */ = {isa = PBXBuildFile; fileRef = 664BC57024D3C92100825CF5 /* UIButton+TouchArea.m */; };
		664BC57924D3C92100825CF5 /* TestFontViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 664BC57124D3C92100825CF5 /* TestFontViewController.m */; };
		665A72FB2468F7B3008C6C61 /* AbcHippyTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 665A72EF2468F7B3008C6C61 /* AbcHippyTextView.m */; };
		665A72FC2468F7B3008C6C61 /* AbcHippyBaseTextInput.m in Sources */ = {isa = PBXBuildFile; fileRef = 665A72F22468F7B3008C6C61 /* AbcHippyBaseTextInput.m */; };
		665A72FD2468F7B3008C6C61 /* AbcHippyShadowTextView.mm in Sources */ = {isa = PBXBuildFile; fileRef = 665A72F72468F7B3008C6C61 /* AbcHippyShadowTextView.mm */; };
		665A72FE2468F7B3008C6C61 /* AbcHippyTextSelection.m in Sources */ = {isa = PBXBuildFile; fileRef = 665A72F82468F7B3008C6C61 /* AbcHippyTextSelection.m */; };
		665A72FF2468F7B3008C6C61 /* AbcHippyTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 665A72F92468F7B3008C6C61 /* AbcHippyTextField.m */; };
		665A73002468F7B3008C6C61 /* AbcHippyTextViewManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 665A72FA2468F7B3008C6C61 /* AbcHippyTextViewManager.mm */; };
		665A7305246901AA008C6C61 /* AbcInputAccessoryView.m in Sources */ = {isa = PBXBuildFile; fileRef = 665A7301246901AA008C6C61 /* AbcInputAccessoryView.m */; };
		665A7306246901AA008C6C61 /* AbcInputAccessoryViewManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 665A7303246901AA008C6C61 /* AbcInputAccessoryViewManager.mm */; };
		665D782524F5E6B2000DF0F7 /* AliPayModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 665D782324F5E6B2000DF0F7 /* AliPayModule.m */; };
		6664093E2521DF720024188E /* libXG-SDK-Cloud.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 6664093D2521DF720024188E /* libXG-SDK-Cloud.a */; };
		666409412521DF7E0024188E /* XGMTACloud.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6664093F2521DF7E0024188E /* XGMTACloud.framework */; };
		666409442521DFA10024188E /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 666409432521DFA10024188E /* CoreTelephony.framework */; };
		666409462521DFB70024188E /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 666409452521DFB70024188E /* SystemConfiguration.framework */; };
		666409482521DFC20024188E /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 666409472521DFC20024188E /* UserNotifications.framework */; };
		6664094A2521DFD00024188E /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 666409492521DFD00024188E /* libz.tbd */; };
		6664094C2521DFD60024188E /* CoreData.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6664094B2521DFD50024188E /* CoreData.framework */; };
		6664094E2521DFDB0024188E /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6664094D2521DFDB0024188E /* CFNetwork.framework */; };
		666409502521DFE50024188E /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 6664094F2521DFE40024188E /* libc++.tbd */; };
		667A64BB24B725700090382A /* WxApiHostModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 667A64BA24B725700090382A /* WxApiHostModule.m */; };
		667A64C224B746B30090382A /* AbcConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 667A64C124B746B30090382A /* AbcConfig.m */; };
		667CE5D925A2C26600FF6C5A /* PinchImageViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 667CE5D625A2C26500FF6C5A /* PinchImageViewManager.m */; };
		667CE5DA25A2C26600FF6C5A /* PinchImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 667CE5D725A2C26500FF6C5A /* PinchImageView.m */; };
		668E759124370ABD002922A2 /* ABCNavigatorItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 668E758B24370ABD002922A2 /* ABCNavigatorItem.m */; };
		668E759224370ABD002922A2 /* ABCNavigatorManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 668E758D24370ABD002922A2 /* ABCNavigatorManager.m */; };
		668E759324370ABD002922A2 /* ABCNavigator.m in Sources */ = {isa = PBXBuildFile; fileRef = 668E758F24370ABD002922A2 /* ABCNavigator.m */; };
		668E759424370ABD002922A2 /* ABCNavigatorItemManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 668E759024370ABD002922A2 /* ABCNavigatorItemManager.m */; };
		6690F68E24F632F200F91E26 /* AudioPlayerMananger.m in Sources */ = {isa = PBXBuildFile; fileRef = 6690F68D24F632F200F91E26 /* AudioPlayerMananger.m */; };
		66942EC524E53E9F00DC1B3A /* CaptureView.m in Sources */ = {isa = PBXBuildFile; fileRef = 66942EC124E53E9E00DC1B3A /* CaptureView.m */; };
		66942EC624E53E9F00DC1B3A /* CaptureViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 66942EC424E53E9F00DC1B3A /* CaptureViewManager.m */; };
		66942ECA24E548FA00DC1B3A /* GallerySaverModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 66942EC924E548FA00DC1B3A /* GallerySaverModule.m */; };
		66942ECE24E622B700DC1B3A /* WebViewUtilsModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 66942ECC24E622B700DC1B3A /* WebViewUtilsModule.m */; };
		66942ED424E671C000DC1B3A /* AbcWebViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 66942ED024E671C000DC1B3A /* AbcWebViewManager.m */; };
		66942ED524E671C000DC1B3A /* AbcWebView.m in Sources */ = {isa = PBXBuildFile; fileRef = 66942ED224E671C000DC1B3A /* AbcWebView.m */; };
		66AFC5E424DD88F400BB0F83 /* AudioRecorderModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 66AFC5E224DD88F300BB0F83 /* AudioRecorderModule.m */; };
		66AFC5E824E2E7AA00BB0F83 /* ThirdCallModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 66AFC5E724E2E7AA00BB0F83 /* ThirdCallModule.m */; };
		66B3130624E50E8800F75F77 /* QRViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 66B3130224E50E8700F75F77 /* QRViewManager.m */; };
		66B3130724E50E8800F75F77 /* QRView.m in Sources */ = {isa = PBXBuildFile; fileRef = 66B3130424E50E8700F75F77 /* QRView.m */; };
		66BF71DC28F54B0A00EE457A /* SVGManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 66BF71DB28F54B0A00EE457A /* SVGManager.m */; };
		66BF71E028F54D1B00EE457A /* LruCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 66BF71DF28F54D1B00EE457A /* LruCache.m */; };
		66C17CD92435B3CC0009D385 /* ReverseListViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 66C17CCE2435B3CB0009D385 /* ReverseListViewManager.m */; };
		66C17CDA2435B3CC0009D385 /* ReverseListItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = 66C17CD12435B3CB0009D385 /* ReverseListItemView.m */; };
		66C17CDB2435B3CC0009D385 /* ReverseListItemViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 66C17CD52435B3CB0009D385 /* ReverseListItemViewManager.m */; };
		66C17CDC2435B3CC0009D385 /* ReverseListView.m in Sources */ = {isa = PBXBuildFile; fileRef = 66C17CD62435B3CB0009D385 /* ReverseListView.m */; };
		66C17CDD2435B3CC0009D385 /* ReverseListViewDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = 66C17CD72435B3CB0009D385 /* ReverseListViewDataSource.m */; };
		66C17D252435C1F40009D385 /* TouchListenerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 66C17D232435C1F40009D385 /* TouchListenerView.m */; };
		66C17D262435C1F40009D385 /* TouchListenerViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 66C17D242435C1F40009D385 /* TouchListenerViewManager.m */; };
		66C17D292435D43B0009D385 /* DebugView.m in Sources */ = {isa = PBXBuildFile; fileRef = 66C17D282435D43B0009D385 /* DebugView.m */; };
		66CBBE8624DD03D3003A9F78 /* ImagePickerMetaDataUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 66CBBE7C24DD03D3003A9F78 /* ImagePickerMetaDataUtil.m */; };
		66CBBE8724DD03D3003A9F78 /* ImagePickerPhotoAssetUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 66CBBE7D24DD03D3003A9F78 /* ImagePickerPhotoAssetUtil.m */; };
		66CBBE8924DD03D3003A9F78 /* ImagePickerImageUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 66CBBE8424DD03D3003A9F78 /* ImagePickerImageUtil.m */; };
		66CBBE8D24DD0422003A9F78 /* ImagePickerModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 66CBBE8B24DD0422003A9F78 /* ImagePickerModule.m */; };
		66D59D0C2481363700EC1077 /* AbcImageLoaderModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 66D59D0A2481363700EC1077 /* AbcImageLoaderModule.m */; };
		66DC5C132990C945000F7965 /* AbcScrollViewManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 66DC5C0E2990C944000F7965 /* AbcScrollViewManager.mm */; };
		66DC5C142990C945000F7965 /* AbcScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = 66DC5C112990C944000F7965 /* AbcScrollView.m */; };
		66E2335525418FD700135BE5 /* BlurEffectWithAmount.m in Sources */ = {isa = PBXBuildFile; fileRef = 66E2335025418FD700135BE5 /* BlurEffectWithAmount.m */; };
		66E2335625418FD700135BE5 /* BlurView.m in Sources */ = {isa = PBXBuildFile; fileRef = 66E2335125418FD700135BE5 /* BlurView.m */; };
		66E2335725418FD700135BE5 /* BlurViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 66E2335325418FD700135BE5 /* BlurViewManager.m */; };
		7D88C50A2E24E511003AFF5A /* AbcAudioRecorder.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D88C5082E24E511003AFF5A /* AbcAudioRecorder.m */; };
		7D88C50B2E24E511003AFF5A /* AbcAsrManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D88C5022E24E511003AFF5A /* AbcAsrManager.m */; };
		7D88C50C2E24E511003AFF5A /* AbcAsrWebSocketClient.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D88C5062E24E511003AFF5A /* AbcAsrWebSocketClient.m */; };
		7D88C50D2E24E511003AFF5A /* AbcAsrConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D88C4FD2E24E511003AFF5A /* AbcAsrConfig.m */; };
		7D88C50E2E24E511003AFF5A /* AbcAsrBackgroundHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D88C5002E24E511003AFF5A /* AbcAsrBackgroundHandler.m */; };
		7D88C50F2E24E511003AFF5A /* AbcAsrModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D88C5042E24E511003AFF5A /* AbcAsrModule.m */; };
		7DFEBCD52E12A6580024EE75 /* AbcSseEvent.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DFEBCD42E12A6580024EE75 /* AbcSseEvent.m */; };
		7DFEBCD62E12A6580024EE75 /* AbcSseConnection.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DFEBCD22E12A6580024EE75 /* AbcSseConnection.m */; };
		978B8F6F1D3862AE00F588F7 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 7AFFD8EE1D35381100E5BB4D /* AppDelegate.m */; };
		97C146F31CF9000F007C117D /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 97C146F21CF9000F007C117D /* main.m */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		B19A5CA421CF7AF1005E6B11 /* FluwxPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5C9E21CF7AF1005E6B11 /* FluwxPlugin.m */; };
		B19A5CCB21CF859D005E6B11 /* CallResults.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CCA21CF859D005E6B11 /* CallResults.m */; };
		B19A5CD721CF85C5005E6B11 /* StringToWeChatScene.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CCD21CF85C2005E6B11 /* StringToWeChatScene.m */; };
		B19A5CD921CF85C5005E6B11 /* ImageSchema.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CD121CF85C3005E6B11 /* ImageSchema.m */; };
		B19A5CDA21CF85C5005E6B11 /* StringUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CD321CF85C4005E6B11 /* StringUtil.m */; };
		B19A5CDB21CF85C5005E6B11 /* FluwxKeys.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CD421CF85C4005E6B11 /* FluwxKeys.m */; };
		B19A5CDC21CF85C5005E6B11 /* FluwxMethods.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CD521CF85C5005E6B11 /* FluwxMethods.m */; };
		B19A5CE521CF85D1005E6B11 /* FluwxLaunchMiniProgramHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CDD21CF85CE005E6B11 /* FluwxLaunchMiniProgramHandler.m */; };
		B19A5CE621CF85D1005E6B11 /* FluwxShareHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CDE21CF85CF005E6B11 /* FluwxShareHandler.m */; };
		B19A5CE821CF85D1005E6B11 /* FluwxAuthHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CE021CF85D0005E6B11 /* FluwxAuthHandler.m */; };
		B19A5CE921CF85D1005E6B11 /* FluwxSubscribeMsgHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CE121CF85D0005E6B11 /* FluwxSubscribeMsgHandler.m */; };
		B19A5CEA21CF85D1005E6B11 /* FluwxPaymentHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CE321CF85D0005E6B11 /* FluwxPaymentHandler.m */; };
		B19A5CEB21CF85D1005E6B11 /* NSStringWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CE421CF85D1005E6B11 /* NSStringWrapper.m */; };
		B19A5CFD21CF85EF005E6B11 /* WXApiRequestHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CF921CF85ED005E6B11 /* WXApiRequestHandler.m */; };
		B19A5CFE21CF85EF005E6B11 /* WXMediaMessage+messageConstruct.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CFA21CF85ED005E6B11 /* WXMediaMessage+messageConstruct.m */; };
		B19A5CFF21CF85EF005E6B11 /* FluwxResponseHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = B19A5CFC21CF85EF005E6B11 /* FluwxResponseHandler.m */; };
		B99770F42836169900D34173 /* .gitignore in Resources */ = {isa = PBXBuildFile; fileRef = B99770A22836169900D34173 /* .gitignore */; };
		B99770F52836169900D34173 /* .clang-format in Resources */ = {isa = PBXBuildFile; fileRef = B99770A32836169900D34173 /* .clang-format */; };
		B99770FA2836169900D34173 /* logging.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770AC2836169900D34173 /* logging.cc */; };
		B99770FB2836169900D34173 /* log_settings_state.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770AE2836169900D34173 /* log_settings_state.cc */; };
		B99770FD2836169900D34173 /* log_settings.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770B02836169900D34173 /* log_settings.cc */; };
		B99770FE2836169900D34173 /* unicode_string_view.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770B12836169900D34173 /* unicode_string_view.cc */; };
		B99771022836169900D34173 /* scope.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770B62836169900D34173 /* scope.cc */; };
		B99771032836169900D34173 /* engine.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770B72836169900D34173 /* engine.cc */; };
		B99771042836169900D34173 /* javascript_task_runner.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770B92836169900D34173 /* javascript_task_runner.cc */; };
		B99771052836169900D34173 /* javascript_task.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770BA2836169900D34173 /* javascript_task.cc */; };
		B99771062836169900D34173 /* worker_task_runner.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770BB2836169900D34173 /* worker_task_runner.cc */; };
		B99771072836169900D34173 /* common_task.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770BC2836169900D34173 /* common_task.cc */; };
		B99771082836169900D34173 /* module_register.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770BE2836169900D34173 /* module_register.cc */; };
		B99771092836169900D34173 /* console_module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770BF2836169900D34173 /* console_module.cc */; };
		B997710A2836169900D34173 /* timer_module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770C02836169900D34173 /* timer_module.cc */; };
		B997710B2836169900D34173 /* contextify_module.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770C12836169900D34173 /* contextify_module.cc */; };
		B997710C2836169900D34173 /* js_value_wrapper.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770C32836169900D34173 /* js_value_wrapper.cc */; };
		B997710D2836169900D34173 /* file.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770C42836169900D34173 /* file.cc */; };
		B997710E2836169900D34173 /* thread_id.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770C52836169900D34173 /* thread_id.cc */; };
		B997710F2836169900D34173 /* task_runner.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770C62836169900D34173 /* task_runner.cc */; };
		B99771102836169900D34173 /* task.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770C72836169900D34173 /* task.cc */; };
		B99771112836169900D34173 /* thread.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770C82836169900D34173 /* thread.cc */; };
		B99771152836169900D34173 /* callback_info.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770CE2836169900D34173 /* callback_info.cc */; };
		B99771162836169A00D34173 /* js_native_turbo.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770CF2836169900D34173 /* js_native_turbo.cc */; };
		B99771172836169A00D34173 /* js_native_jsc_helper.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770D12836169900D34173 /* js_native_jsc_helper.cc */; };
		B99771182836169A00D34173 /* js_native_turbo_jsc.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770D22836169900D34173 /* js_native_turbo_jsc.cc */; };
		B99771192836169A00D34173 /* native_source_code_ios.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770D32836169900D34173 /* native_source_code_ios.cc */; };
		B997711A2836169A00D34173 /* js_native_api_value_jsc.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770D42836169900D34173 /* js_native_api_value_jsc.cc */; };
		B997711B2836169A00D34173 /* js_native_api_jsc.cc in Sources */ = {isa = PBXBuildFile; fileRef = B99770D52836169900D34173 /* js_native_api_jsc.cc */; };
		B99772A0283616AA00D34173 /* x5LayoutUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977121283616AA00D34173 /* x5LayoutUtil.m */; };
		B99772A1283616AA00D34173 /* MTTLayout.cpp in Sources */ = {isa = PBXBuildFile; fileRef = B9977125283616AA00D34173 /* MTTLayout.cpp */; };
		B99772A2283616AA00D34173 /* MTTLayoutCache.cpp in Sources */ = {isa = PBXBuildFile; fileRef = B9977128283616AA00D34173 /* MTTLayoutCache.cpp */; };
		B99772A3283616AA00D34173 /* MTTTStyle.cpp in Sources */ = {isa = PBXBuildFile; fileRef = B9977129283616AA00D34173 /* MTTTStyle.cpp */; };
		B99772A4283616AA00D34173 /* MTTNode.cpp in Sources */ = {isa = PBXBuildFile; fileRef = B997712C283616AA00D34173 /* MTTNode.cpp */; };
		B99772A5283616AA00D34173 /* MTTFlexLine.cpp in Sources */ = {isa = PBXBuildFile; fileRef = B997712D283616AA00D34173 /* MTTFlexLine.cpp */; };
		B99772A6283616AA00D34173 /* MTTUtil.cpp in Sources */ = {isa = PBXBuildFile; fileRef = B997712E283616AA00D34173 /* MTTUtil.cpp */; };
		B99772A7283616AA00D34173 /* HippyOCTurboModule.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977136283616AA00D34173 /* HippyOCTurboModule.mm */; };
		B99772A8283616AA00D34173 /* HippyTurboModuleManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977137283616AA00D34173 /* HippyTurboModuleManager.mm */; };
		B99772A9283616AA00D34173 /* NSObject+HippyTurbo.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977138283616AA00D34173 /* NSObject+HippyTurbo.mm */; };
		B99772AA283616AA00D34173 /* HippyNetWork.m in Sources */ = {isa = PBXBuildFile; fileRef = B997713A283616AA00D34173 /* HippyNetWork.m */; };
		B99772AB283616AA00D34173 /* HippyFetchInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B997713B283616AA00D34173 /* HippyFetchInfo.m */; };
		B99772AC283616AA00D34173 /* HippyExtAnimation+Value.m in Sources */ = {isa = PBXBuildFile; fileRef = B997713F283616AA00D34173 /* HippyExtAnimation+Value.m */; };
		B99772AD283616AA00D34173 /* HippyExtAnimation.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977142283616AA00D34173 /* HippyExtAnimation.m */; };
		B99772AE283616AA00D34173 /* HippyExtAnimationModule.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977144283616AA00D34173 /* HippyExtAnimationModule.m */; };
		B99772AF283616AA00D34173 /* CALayer+HippyAnimation.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977148283616AA00D34173 /* CALayer+HippyAnimation.m */; };
		B99772B0283616AA00D34173 /* HippyExtAnimationGroup.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977149283616AA00D34173 /* HippyExtAnimationGroup.m */; };
		B99772B1283616AA00D34173 /* HippyExtAnimationViewParams.m in Sources */ = {isa = PBXBuildFile; fileRef = B997714A283616AA00D34173 /* HippyExtAnimationViewParams.m */; };
		B99772B2283616AB00D34173 /* HippyNetInfoIntenal.m in Sources */ = {isa = PBXBuildFile; fileRef = B997714E283616AA00D34173 /* HippyNetInfoIntenal.m */; };
		B99772B3283616AB00D34173 /* HippyNetInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977150283616AA00D34173 /* HippyNetInfo.m */; };
		B99772B4283616AB00D34173 /* HippyTiming.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977152283616AA00D34173 /* HippyTiming.mm */; };
		B99772B5283616AB00D34173 /* ios_loader.cc in Sources */ = {isa = PBXBuildFile; fileRef = B9977156283616AA00D34173 /* ios_loader.cc */; };
		B99772B6283616AB00D34173 /* HippyEventObserverModule.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977158283616AA00D34173 /* HippyEventObserverModule.m */; };
		B99772B7283616AB00D34173 /* HippyAsyncLocalStorage.m in Sources */ = {isa = PBXBuildFile; fileRef = B997715C283616AA00D34173 /* HippyAsyncLocalStorage.m */; };
		B99772B8283616AB00D34173 /* HippyDevMenu.mm in Sources */ = {isa = PBXBuildFile; fileRef = B997715E283616AA00D34173 /* HippyDevMenu.mm */; };
		B99772B9283616AB00D34173 /* HippyRedBox.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977160283616AA00D34173 /* HippyRedBox.m */; };
		B99772BA283616AB00D34173 /* HippyDevLoadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977162283616AA00D34173 /* HippyDevLoadingView.m */; };
		B99772BB283616AB00D34173 /* HippyExceptionModule.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977166283616AA00D34173 /* HippyExceptionModule.m */; };
		B99772BC283616AB00D34173 /* HippyImageLoaderModule.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977168283616AA00D34173 /* HippyImageLoaderModule.m */; };
		B99772BD283616AB00D34173 /* HippyImageCacheManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B997716B283616AA00D34173 /* HippyImageCacheManager.m */; };
		B99772BE283616AB00D34173 /* HippyUIManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = B997716F283616AA00D34173 /* HippyUIManager.mm */; };
		B99772BF283616AB00D34173 /* HippyClipboardModule.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977172283616AA00D34173 /* HippyClipboardModule.m */; };
		B99772C0283616AB00D34173 /* HippyScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977175283616AA00D34173 /* HippyScrollView.m */; };
		B99772C1283616AB00D34173 /* HippyScrollViewManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977179283616AA00D34173 /* HippyScrollViewManager.mm */; };
		B99772C2283616AB00D34173 /* HippyRefreshWrapperItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = B997717C283616AA00D34173 /* HippyRefreshWrapperItemView.m */; };
		B99772C3283616AB00D34173 /* HippyRefreshWrapperViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B997717D283616AA00D34173 /* HippyRefreshWrapperViewManager.m */; };
		B99772C4283616AB00D34173 /* HippyRefreshWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = B997717E283616AA00D34173 /* HippyRefreshWrapper.m */; };
		B99772C5283616AB00D34173 /* HippyRefreshWrapperItemViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977180283616AA00D34173 /* HippyRefreshWrapperItemViewManager.m */; };
		B99772C6283616AB00D34173 /* HippyRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977185283616AA00D34173 /* HippyRefresh.m */; };
		B99772C7283616AB00D34173 /* HippyViewPager.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977188283616AA00D34173 /* HippyViewPager.m */; };
		B99772C8283616AB00D34173 /* HippyViewPagerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B997718A283616AA00D34173 /* HippyViewPagerManager.m */; };
		B99772C9283616AB00D34173 /* HippyViewPagerItem.m in Sources */ = {isa = PBXBuildFile; fileRef = B997718B283616AA00D34173 /* HippyViewPagerItem.m */; };
		B99772CA283616AB00D34173 /* HippyViewPagerItemManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B997718D283616AA00D34173 /* HippyViewPagerItemManager.m */; };
		B99772CB283616AB00D34173 /* HippyTextView.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977191283616AA00D34173 /* HippyTextView.mm */; };
		B99772CC283616AB00D34173 /* HippyTextViewManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977192283616AA00D34173 /* HippyTextViewManager.mm */; };
		B99772CD283616AB00D34173 /* HippyShadowTextView.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977194283616AA00D34173 /* HippyShadowTextView.mm */; };
		B99772CE283616AB00D34173 /* HippyBaseTextInput.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977197283616AA00D34173 /* HippyBaseTextInput.m */; };
		B99772CF283616AB00D34173 /* HippyTextSelection.m in Sources */ = {isa = PBXBuildFile; fileRef = B997719A283616AA00D34173 /* HippyTextSelection.m */; };
		B99772D0283616AB00D34173 /* HippyTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = B997719B283616AA00D34173 /* HippyTextField.m */; };
		B99772D1283616AB00D34173 /* HippySimpleWebView.m in Sources */ = {isa = PBXBuildFile; fileRef = B997719F283616AA00D34173 /* HippySimpleWebView.m */; };
		B99772D2283616AB00D34173 /* HippySimpleWebViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771A0283616AA00D34173 /* HippySimpleWebViewManager.m */; };
		B99772D3283616AB00D34173 /* HippyHeaderRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771A3283616AA00D34173 /* HippyHeaderRefresh.m */; };
		B99772D4283616AB00D34173 /* HippyHeaderRefreshManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771A4283616AA00D34173 /* HippyHeaderRefreshManager.m */; };
		B99772D5283616AB00D34173 /* HippyReusableNodeCache.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771A7283616AA00D34173 /* HippyReusableNodeCache.m */; };
		B99772D6283616AB00D34173 /* HippyWaterfallView.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771AC283616AA00D34173 /* HippyWaterfallView.m */; };
		B99772D7283616AB00D34173 /* HippyWaterfallItemViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771AD283616AA00D34173 /* HippyWaterfallItemViewManager.m */; };
		B99772D8283616AB00D34173 /* HippyWaterfallItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771AF283616AA00D34173 /* HippyWaterfallItemView.m */; };
		B99772D9283616AB00D34173 /* HippyCollectionViewWaterfallLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771B0283616AA00D34173 /* HippyCollectionViewWaterfallLayout.m */; };
		B99772DA283616AB00D34173 /* HippyWaterfallViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771B1283616AA00D34173 /* HippyWaterfallViewManager.m */; };
		B99772DB283616AB00D34173 /* HippyImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771B6283616AA00D34173 /* HippyImageView.m */; };
		B99772DC283616AB00D34173 /* HippyImageProviderProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771B7283616AA00D34173 /* HippyImageProviderProtocol.m */; };
		B99772DD283616AB00D34173 /* UIImageView+Hippy.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771BA283616AA00D34173 /* UIImageView+Hippy.m */; };
		B99772DE283616AB00D34173 /* HippyAnimatedImage.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771BB283616AA00D34173 /* HippyAnimatedImage.m */; };
		B99772DF283616AB00D34173 /* HippyDefaultImageProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771BC283616AA00D34173 /* HippyDefaultImageProvider.m */; };
		B99772E0283616AB00D34173 /* HippyAnimatedImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771BE283616AA00D34173 /* HippyAnimatedImageView.m */; };
		B99772E1283616AB00D34173 /* HippyImageViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771C1283616AA00D34173 /* HippyImageViewManager.m */; };
		B99772E2283616AB00D34173 /* HippyImageCache.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771C4283616AA00D34173 /* HippyImageCache.m */; };
		B99772E3283616AB00D34173 /* HippyBaseListItemViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771C8283616AA00D34173 /* HippyBaseListItemViewManager.m */; };
		B99772E4283616AB00D34173 /* HippyBaseListViewDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771C9283616AA00D34173 /* HippyBaseListViewDataSource.m */; };
		B99772E5283616AB00D34173 /* HippyBaseListItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771CF283616AA00D34173 /* HippyBaseListItemView.m */; };
		B99772E6283616AB00D34173 /* HippyBaseListViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771D0283616AA00D34173 /* HippyBaseListViewManager.m */; };
		B99772E7283616AB00D34173 /* HippyListTableView.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771D1283616AA00D34173 /* HippyListTableView.m */; };
		B99772E8283616AB00D34173 /* HippyBaseListView.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771D3283616AA00D34173 /* HippyBaseListView.m */; };
		B99772E9283616AB00D34173 /* HippyBaseListViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771D4283616AA00D34173 /* HippyBaseListViewCell.m */; };
		B99772EA283616AB00D34173 /* UIView+AppearEvent.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771D6283616AA00D34173 /* UIView+AppearEvent.m */; };
		B99772EB283616AB00D34173 /* UIView+HippyAnimationProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771D7283616AA00D34173 /* UIView+HippyAnimationProtocol.m */; };
		B99772EC283616AB00D34173 /* HippyView+HippyViewAnimation.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771D8283616AA00D34173 /* HippyView+HippyViewAnimation.m */; };
		B99772ED283616AB00D34173 /* UIView+Hippy.mm in Sources */ = {isa = PBXBuildFile; fileRef = B99771DA283616AA00D34173 /* UIView+Hippy.mm */; };
		B99772EE283616AB00D34173 /* HippyViewManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = B99771DD283616AA00D34173 /* HippyViewManager.mm */; };
		B99772EF283616AB00D34173 /* HippyShadowView.mm in Sources */ = {isa = PBXBuildFile; fileRef = B99771E0283616AA00D34173 /* HippyShadowView.mm */; };
		B99772F0283616AB00D34173 /* HippyView.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771E1283616AA00D34173 /* HippyView.m */; };
		B99772F1283616AB00D34173 /* HippyBorderDrawing.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771E2283616AA00D34173 /* HippyBorderDrawing.m */; };
		B99772F2283616AB00D34173 /* HippyBackgroundImageCacheManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771E6283616AA00D34173 /* HippyBackgroundImageCacheManager.m */; };
		B99772F3283616AB00D34173 /* HippyNavigatorRootViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771ED283616AA00D34173 /* HippyNavigatorRootViewController.m */; };
		B99772F4283616AB00D34173 /* HippyNavigatorItemViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771EE283616AA00D34173 /* HippyNavigatorItemViewController.m */; };
		B99772F5283616AB00D34173 /* HippyNavigatorHostView.mm in Sources */ = {isa = PBXBuildFile; fileRef = B99771EF283616AA00D34173 /* HippyNavigatorHostView.mm */; };
		B99772F6283616AB00D34173 /* HippyNavigationControllerAnimator.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771F0283616AA00D34173 /* HippyNavigationControllerAnimator.m */; };
		B99772F7283616AB00D34173 /* HippyNavigatorViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771F4283616AA00D34173 /* HippyNavigatorViewManager.m */; };
		B99772F8283616AB00D34173 /* HippyFooterRefreshManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771F8283616AA00D34173 /* HippyFooterRefreshManager.m */; };
		B99772F9283616AB00D34173 /* HippyFooterRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = B99771F9283616AA00D34173 /* HippyFooterRefresh.m */; };
		B99772FA283616AB00D34173 /* HippyShadowText.mm in Sources */ = {isa = PBXBuildFile; fileRef = B99771FE283616AA00D34173 /* HippyShadowText.mm */; };
		B99772FB283616AB00D34173 /* HippyText.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977200283616AA00D34173 /* HippyText.mm */; };
		B99772FC283616AB00D34173 /* HippyVirtualTextNode.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977201283616AA00D34173 /* HippyVirtualTextNode.m */; };
		B99772FD283616AB00D34173 /* HippyTextManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977204283616AA00D34173 /* HippyTextManager.mm */; };
		B99772FE283616AB00D34173 /* HippyModalCustomPresentationController.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977208283616AA00D34173 /* HippyModalCustomPresentationController.m */; };
		B99772FF283616AB00D34173 /* HippyModalHostViewManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977209283616AA00D34173 /* HippyModalHostViewManager.mm */; };
		B9977300283616AB00D34173 /* HippyModalTransitioningDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = B997720A283616AA00D34173 /* HippyModalTransitioningDelegate.mm */; };
		B9977301283616AB00D34173 /* HippyModalHostViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B997720B283616AA00D34173 /* HippyModalHostViewController.m */; };
		B9977302283616AB00D34173 /* HippyModalHostView.m in Sources */ = {isa = PBXBuildFile; fileRef = B997720C283616AA00D34173 /* HippyModalHostView.m */; };
		B9977303283616AB00D34173 /* HippyModalCustomAnimationTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = B997720F283616AA00D34173 /* HippyModalCustomAnimationTransition.m */; };
		B9977304283616AB00D34173 /* HippyParserUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977215283616AA00D34173 /* HippyParserUtils.m */; };
		B9977305283616AB00D34173 /* HippyGradientObject.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977218283616AA00D34173 /* HippyGradientObject.m */; };
		B9977306283616AB00D34173 /* HippyLog.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977219283616AA00D34173 /* HippyLog.mm */; };
		B9977307283616AB00D34173 /* HippyConvert+Transform.m in Sources */ = {isa = PBXBuildFile; fileRef = B997721B283616AA00D34173 /* HippyConvert+Transform.m */; };
		B9977308283616AB00D34173 /* NSArray+HippyArrayDeepCopy.m in Sources */ = {isa = PBXBuildFile; fileRef = B997721D283616AA00D34173 /* NSArray+HippyArrayDeepCopy.m */; };
		B9977309283616AB00D34173 /* NSData+DataType.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977220283616AA00D34173 /* NSData+DataType.m */; };
		B997730A283616AB00D34173 /* HippyAssert.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977222283616AA00D34173 /* HippyAssert.m */; };
		B997730B283616AB00D34173 /* HippyI18nUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977224283616AA00D34173 /* HippyI18nUtils.m */; };
		B997730C283616AB00D34173 /* NSNumber+HippyNumberDeepCopy.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977225283616AA00D34173 /* NSNumber+HippyNumberDeepCopy.m */; };
		B997730D283616AB00D34173 /* HippyConvert.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977229283616AA00D34173 /* HippyConvert.mm */; };
		B997730E283616AB00D34173 /* HippyErrorInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B997722C283616AA00D34173 /* HippyErrorInfo.m */; };
		B997730F283616AB00D34173 /* HippyUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = B997722F283616AA00D34173 /* HippyUtils.m */; };
		B9977310283616AB00D34173 /* NSDictionary+HippyDictionaryDeepCopy.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977232283616AA00D34173 /* NSDictionary+HippyDictionaryDeepCopy.m */; };
		B9977312283616AB00D34173 /* HippyPerformanceLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977236283616AA00D34173 /* HippyPerformanceLogger.m */; };
		B9977313283616AB00D34173 /* HippyVirtualNode.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977239283616AA00D34173 /* HippyVirtualNode.m */; };
		B9977314283616AB00D34173 /* HippyVirtualList.m in Sources */ = {isa = PBXBuildFile; fileRef = B997723A283616AA00D34173 /* HippyVirtualList.m */; };
		B9977315283616AB00D34173 /* HippyComponentData.mm in Sources */ = {isa = PBXBuildFile; fileRef = B997723C283616AA00D34173 /* HippyComponentData.mm */; };
		B9977316283616AB00D34173 /* HippyTouchHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = B997723D283616AA00D34173 /* HippyTouchHandler.m */; };
		B9977317283616AB00D34173 /* HippyJSStackFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977242283616AA00D34173 /* HippyJSStackFrame.m */; };
		B9977318283616AB00D34173 /* HippyEventDispatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977244283616AA00D34173 /* HippyEventDispatcher.m */; };
		B9977319283616AB00D34173 /* HippyModuleMethod.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977245283616AA00D34173 /* HippyModuleMethod.mm */; };
		B997731A283616AB00D34173 /* HippyRootView.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977249283616AA00D34173 /* HippyRootView.mm */; };
		B997731B283616AB00D34173 /* HippyBundleURLProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977254283616AA00D34173 /* HippyBundleURLProvider.m */; };
		B997731C283616AB00D34173 /* HippyJSEnginesMapper.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977256283616AA00D34173 /* HippyJSEnginesMapper.mm */; };
		B997731D283616AB00D34173 /* HippyJavaScriptLoader.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977258283616AA00D34173 /* HippyJavaScriptLoader.mm */; };
		B997731E283616AB00D34173 /* HippyBatchedBridge.mm in Sources */ = {isa = PBXBuildFile; fileRef = B997725A283616AA00D34173 /* HippyBatchedBridge.mm */; };
		B997731F283616AB00D34173 /* HippyJSCExecutor.mm in Sources */ = {isa = PBXBuildFile; fileRef = B997725E283616AA00D34173 /* HippyJSCExecutor.mm */; };
		B9977320283616AB00D34173 /* HippyJSCErrorHandling.m in Sources */ = {isa = PBXBuildFile; fileRef = B997725F283616AA00D34173 /* HippyJSCErrorHandling.m */; };
		B9977321283616AB00D34173 /* HippyJSCWrapper.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977263283616AA00D34173 /* HippyJSCWrapper.mm */; };
		B9977322283616AB00D34173 /* HippyKeyCommands.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977264283616AA00D34173 /* HippyKeyCommands.m */; };
		B9977323283616AB00D34173 /* HippyFont.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977265283616AA00D34173 /* HippyFont.mm */; };
		B9977324283616AB00D34173 /* HippyBridge+LocalFileSource.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977269283616AA00D34173 /* HippyBridge+LocalFileSource.m */; };
		B9977325283616AB00D34173 /* HippyBridge.mm in Sources */ = {isa = PBXBuildFile; fileRef = B997726B283616AA00D34173 /* HippyBridge.mm */; };
		B9977326283616AB00D34173 /* HippyDeviceBaseInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B997726E283616AA00D34173 /* HippyDeviceBaseInfo.m */; };
		B9977327283616AB00D34173 /* HippyModuleData.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977272283616AA00D34173 /* HippyModuleData.mm */; };
		B9977328283616AB00D34173 /* HippyBridge+Mtt.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977273283616AA00D34173 /* HippyBridge+Mtt.mm */; };
		B9977329283616AB00D34173 /* HippyFrameUpdate.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977274283616AA00D34173 /* HippyFrameUpdate.m */; };
		B997732A283616AB00D34173 /* HippyDisplayLink.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977275283616AA00D34173 /* HippyDisplayLink.m */; };
		B997732B283616AB00D34173 /* HippyRootShadowView.mm in Sources */ = {isa = PBXBuildFile; fileRef = B9977277283616AA00D34173 /* HippyRootShadowView.mm */; };
		B997732C283616AB00D34173 /* HippySRWebSocket.m in Sources */ = {isa = PBXBuildFile; fileRef = B997727E283616AA00D34173 /* HippySRWebSocket.m */; };
		B997732D283616AB00D34173 /* HippySRSIMDHelpers.m in Sources */ = {isa = PBXBuildFile; fileRef = B997727F283616AA00D34173 /* HippySRSIMDHelpers.m */; };
		B997732E283616AB00D34173 /* HippyWebSocketManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977280283616AA00D34173 /* HippyWebSocketManager.m */; };
		B997732F283616AB00D34173 /* HippyCSSPropsDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = B997728C283616AA00D34173 /* HippyCSSPropsDefine.m */; };
		B9977330283616AB00D34173 /* HippyPageModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B997728D283616AA00D34173 /* HippyPageModel.m */; };
		B9977331283616AB00D34173 /* HippyCSSModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B997728E283616AA00D34173 /* HippyCSSModel.m */; };
		B9977332283616AB00D34173 /* HippyDomModel.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977290283616AA00D34173 /* HippyDomModel.m */; };
		B9977333283616AB00D34173 /* HippyPageDomain.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977292283616AA00D34173 /* HippyPageDomain.m */; };
		B9977334283616AB00D34173 /* HippyDomDomain.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977295283616AA00D34173 /* HippyDomDomain.m */; };
		B9977335283616AB00D34173 /* HippyCSSDomain.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977298283616AA00D34173 /* HippyCSSDomain.m */; };
		B9977336283616AB00D34173 /* HippyInspectorDomain.m in Sources */ = {isa = PBXBuildFile; fileRef = B9977299283616AA00D34173 /* HippyInspectorDomain.m */; };
		B9977337283616AB00D34173 /* HippyDevCommand.m in Sources */ = {isa = PBXBuildFile; fileRef = B997729A283616AA00D34173 /* HippyDevCommand.m */; };
		B9977338283616AB00D34173 /* HippyInspector.m in Sources */ = {isa = PBXBuildFile; fileRef = B997729B283616AA00D34173 /* HippyInspector.m */; };
		B9977339283616AB00D34173 /* HippyDevManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B997729C283616AA00D34173 /* HippyDevManager.m */; };
		B997733A283616AB00D34173 /* HippyDevInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B997729D283616AA00D34173 /* HippyDevInfo.m */; };
		B997733B283616AB00D34173 /* HippyDevWebSocketClient.m in Sources */ = {isa = PBXBuildFile; fileRef = B997729E283616AA00D34173 /* HippyDevWebSocketClient.m */; };
		B997733D2836268300D34173 /* WillPopListener.m in Sources */ = {isa = PBXBuildFile; fileRef = B997733C2836268300D34173 /* WillPopListener.m */; };
		B9ABD8EE283B1C1B00504F28 /* IQToolbar.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8D0283B1C1A00504F28 /* IQToolbar.m */; };
		B9ABD8EF283B1C1B00504F28 /* IQTitleBarButtonItem.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8D2283B1C1A00504F28 /* IQTitleBarButtonItem.m */; };
		B9ABD8F0283B1C1B00504F28 /* IQBarButtonItem.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8D3283B1C1A00504F28 /* IQBarButtonItem.m */; };
		B9ABD8F1283B1C1B00504F28 /* IQUIView+IQKeyboardToolbar.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8D4283B1C1A00504F28 /* IQUIView+IQKeyboardToolbar.m */; };
		B9ABD8F2283B1C1B00504F28 /* IQPreviousNextView.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8D5283B1C1A00504F28 /* IQPreviousNextView.m */; };
		B9ABD8F3283B1C1B00504F28 /* IQKeyboardReturnKeyHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8D9283B1C1A00504F28 /* IQKeyboardReturnKeyHandler.m */; };
		B9ABD8F4283B1C1B00504F28 /* IQKeyboardManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8DD283B1C1A00504F28 /* IQKeyboardManager.m */; };
		B9ABD8F5283B1C1B00504F28 /* IQNSArray+Sort.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8DF283B1C1B00504F28 /* IQNSArray+Sort.m */; };
		B9ABD8F6283B1C1B00504F28 /* IQUITextFieldView+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8E1283B1C1B00504F28 /* IQUITextFieldView+Additions.m */; };
		B9ABD8F7283B1C1B00504F28 /* IQUIScrollView+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8E2283B1C1B00504F28 /* IQUIScrollView+Additions.m */; };
		B9ABD8F8283B1C1B00504F28 /* IQUIView+Hierarchy.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8E3283B1C1B00504F28 /* IQUIView+Hierarchy.m */; };
		B9ABD8F9283B1C1B00504F28 /* IQUIViewController+Additions.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8E7283B1C1B00504F28 /* IQUIViewController+Additions.m */; };
		B9ABD8FA283B1C1B00504F28 /* IQTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = B9ABD8EC283B1C1B00504F28 /* IQTextView.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		9705A1C41CF9048500538489 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		112E28C8245C6D0100A5DF37 /* AudioView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AudioView.m; sourceTree = "<group>"; };
		112E28C9245C6D0100A5DF37 /* AudioViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AudioViewManager.m; sourceTree = "<group>"; };
		112E28CA245C6D0100A5DF37 /* AudioView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AudioView.h; sourceTree = "<group>"; };
		112E28CB245C6D0100A5DF37 /* AudioViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AudioViewManager.h; sourceTree = "<group>"; };
		112E291E245D0C4E00A5DF37 /* AudioPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AudioPlayer.h; sourceTree = "<group>"; };
		112E291F245D0C4E00A5DF37 /* AudioPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AudioPlayer.m; sourceTree = "<group>"; };
		116CAF7D24DE56CC00DB4C50 /* Codec.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Codec.m; sourceTree = "<group>"; };
		116CAF7E24DE56CC00DB4C50 /* Codec.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Codec.h; sourceTree = "<group>"; };
		116CAF7F24DE56CC00DB4C50 /* PermissionHandlerEnums.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PermissionHandlerEnums.h; sourceTree = "<group>"; };
		116CAF8024DE56CC00DB4C50 /* PermissionManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PermissionManager.m; sourceTree = "<group>"; };
		116CAF8124DE56CC00DB4C50 /* PermissionManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PermissionManager.h; sourceTree = "<group>"; };
		116CAF8224DE56CC00DB4C50 /* PermissionHandlerModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PermissionHandlerModule.m; sourceTree = "<group>"; };
		116CAF8424DE56CC00DB4C50 /* MediaLibraryPermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MediaLibraryPermissionStrategy.m; sourceTree = "<group>"; };
		116CAF8524DE56CC00DB4C50 /* LocationPermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LocationPermissionStrategy.m; sourceTree = "<group>"; };
		116CAF8624DE56CC00DB4C50 /* EventPermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = EventPermissionStrategy.m; sourceTree = "<group>"; };
		116CAF8724DE56CC00DB4C50 /* PhotoPermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PhotoPermissionStrategy.h; sourceTree = "<group>"; };
		116CAF8824DE56CC00DB4C50 /* StoragePermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StoragePermissionStrategy.m; sourceTree = "<group>"; };
		116CAF8924DE56CC00DB4C50 /* ContactPermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ContactPermissionStrategy.h; sourceTree = "<group>"; };
		116CAF8A24DE56CC00DB4C50 /* SensorPermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SensorPermissionStrategy.m; sourceTree = "<group>"; };
		116CAF8B24DE56CC00DB4C50 /* NotificationPermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NotificationPermissionStrategy.h; sourceTree = "<group>"; };
		116CAF8C24DE56CC00DB4C50 /* SpeechPermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SpeechPermissionStrategy.m; sourceTree = "<group>"; };
		116CAF8D24DE56CC00DB4C50 /* UnknownPermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UnknownPermissionStrategy.h; sourceTree = "<group>"; };
		116CAF8E24DE56CC00DB4C50 /* AudioVideoPermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AudioVideoPermissionStrategy.h; sourceTree = "<group>"; };
		116CAF8F24DE56CC00DB4C50 /* PhonePermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PhonePermissionStrategy.h; sourceTree = "<group>"; };
		116CAF9024DE56CC00DB4C50 /* PermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PermissionStrategy.h; sourceTree = "<group>"; };
		116CAF9124DE56CC00DB4C50 /* MediaLibraryPermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MediaLibraryPermissionStrategy.h; sourceTree = "<group>"; };
		116CAF9224DE56CC00DB4C50 /* LocationPermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LocationPermissionStrategy.h; sourceTree = "<group>"; };
		116CAF9324DE56CC00DB4C50 /* StoragePermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StoragePermissionStrategy.h; sourceTree = "<group>"; };
		116CAF9424DE56CC00DB4C50 /* PhotoPermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PhotoPermissionStrategy.m; sourceTree = "<group>"; };
		116CAF9524DE56CC00DB4C50 /* EventPermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = EventPermissionStrategy.h; sourceTree = "<group>"; };
		116CAF9624DE56CC00DB4C50 /* PhonePermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PhonePermissionStrategy.m; sourceTree = "<group>"; };
		116CAF9724DE56CC00DB4C50 /* AudioVideoPermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AudioVideoPermissionStrategy.m; sourceTree = "<group>"; };
		116CAF9824DE56CC00DB4C50 /* UnknownPermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UnknownPermissionStrategy.m; sourceTree = "<group>"; };
		116CAF9924DE56CC00DB4C50 /* NotificationPermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NotificationPermissionStrategy.m; sourceTree = "<group>"; };
		116CAF9A24DE56CC00DB4C50 /* SpeechPermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SpeechPermissionStrategy.h; sourceTree = "<group>"; };
		116CAF9B24DE56CC00DB4C50 /* SensorPermissionStrategy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SensorPermissionStrategy.h; sourceTree = "<group>"; };
		116CAF9C24DE56CC00DB4C50 /* ContactPermissionStrategy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ContactPermissionStrategy.m; sourceTree = "<group>"; };
		116CAF9D24DE56CC00DB4C50 /* PermissionHandlerModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PermissionHandlerModule.h; sourceTree = "<group>"; };
		117A558524DED67700E424CA /* BarcodeScanModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BarcodeScanModule.h; sourceTree = "<group>"; };
		117A558624DED67700E424CA /* BarcodeScanModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BarcodeScanModule.m; sourceTree = "<group>"; };
		118E722C28F2C27B0079006F /* SVGView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SVGView.m; sourceTree = "<group>"; };
		118E722D28F2C27B0079006F /* SVGViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SVGViewManager.h; sourceTree = "<group>"; };
		118E722E28F2C27B0079006F /* SVGViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SVGViewManager.m; sourceTree = "<group>"; };
		118E722F28F2C27B0079006F /* SVGView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SVGView.h; sourceTree = "<group>"; };
		118F0C7F24E8121100EAAD89 /* ShareModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ShareModule.h; sourceTree = "<group>"; };
		118F0C8024E8121100EAAD89 /* ShareModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ShareModule.m; sourceTree = "<group>"; };
		1192AE74241CD5E8007D8797 /* HippyViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyViewController.h; sourceTree = "<group>"; };
		1192AE75241CD5E8007D8797 /* HippyViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyViewController.m; sourceTree = "<group>"; };
		1192B0DF241CF7AA007D8797 /* res */ = {isa = PBXFileReference; lastKnownFileType = folder; path = res; sourceTree = "<group>"; };
		11B49485243997F80009856A /* ABCViewAnimationController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ABCViewAnimationController.m; sourceTree = "<group>"; };
		11B49486243997F80009856A /* ABCViewAnimationController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCViewAnimationController.h; sourceTree = "<group>"; };
		11B49488243998160009856A /* ABCViewControllerAnimationController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCViewControllerAnimationController.h; sourceTree = "<group>"; };
		11B49489243998160009856A /* ABCViewControllerAnimationController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ABCViewControllerAnimationController.m; sourceTree = "<group>"; };
		11D552B6243618740049D7ED /* ABCAnimationCenter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ABCAnimationCenter.h; sourceTree = "<group>"; };
		11D552B72436187B0049D7ED /* ABCAnimationCenter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ABCAnimationCenter.m; sourceTree = "<group>"; };
		11D552BA243618DE0049D7ED /* ABCBaseAnimation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ABCBaseAnimation.m; sourceTree = "<group>"; };
		11D552BB243618DE0049D7ED /* ABCBaseInteraction.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCBaseInteraction.h; sourceTree = "<group>"; };
		11D552BC243618DE0049D7ED /* ABCBaseAnimation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCBaseAnimation.h; sourceTree = "<group>"; };
		11D552BD243618DE0049D7ED /* ABCBaseInteraction.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ABCBaseInteraction.m; sourceTree = "<group>"; };
		11D552C1243619F30049D7ED /* ABCCGRectUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ABCCGRectUtil.h; sourceTree = "<group>"; };
		11D552C3243622D10049D7ED /* ABCBaseNavigationController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCBaseNavigationController.h; sourceTree = "<group>"; };
		11D552C5243623060049D7ED /* ABCBaseNavigationController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ABCBaseNavigationController.m; sourceTree = "<group>"; };
		11D552C8243625280049D7ED /* HostHippyMessageBridge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HostHippyMessageBridge.h; sourceTree = "<group>"; };
		11D552C9243625280049D7ED /* HostHippyMessageBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HostHippyMessageBridge.m; sourceTree = "<group>"; };
		11D6D9C3239E3B4B00E3D688 /* RunnerDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerDebug.entitlements; sourceTree = "<group>"; };
		11D6D9C4239E7D9600E3D688 /* RunnerRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerRelease.entitlements; sourceTree = "<group>"; };
		11DA64062445FCE500DBA6C6 /* WheelViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WheelViewManager.m; sourceTree = "<group>"; };
		11DA64072445FCE500DBA6C6 /* WheelView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WheelView.h; sourceTree = "<group>"; };
		11DA640B2445FCE500DBA6C6 /* WheelView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WheelView.m; sourceTree = "<group>"; };
		11DA640D2445FCE500DBA6C6 /* WheelViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WheelViewManager.h; sourceTree = "<group>"; };
		11EA45E024D85918009BD26E /* Preferences.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Preferences.h; sourceTree = "<group>"; };
		2E56DFACB575C483B1265A4E /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Pods/Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		35B64AD02689F97300CF0190 /* AbcPieChartView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcPieChartView.h; sourceTree = "<group>"; };
		35B64AD12689F9C000CF0190 /* AbcPieChartViewManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbcPieChartViewManager.m; sourceTree = "<group>"; };
		35B64AD32689F9EA00CF0190 /* AbcPieChartView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbcPieChartView.m; sourceTree = "<group>"; };
		35B64AD52689FA0800CF0190 /* AbcPieChartViewManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcPieChartViewManager.h; sourceTree = "<group>"; };
		41AC21A3B6B5805CB6B80A8C /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Pods/Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		6600813E2481F22700761AE3 /* WillPopListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WillPopListener.h; sourceTree = "<group>"; };
		660081442481F3C000761AE3 /* WillPopListenerManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WillPopListenerManager.m; sourceTree = "<group>"; };
		660081452481F3C000761AE3 /* WillPopListenerManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WillPopListenerManager.h; sourceTree = "<group>"; };
		660081472481F4C400761AE3 /* UIView+ABCExt.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+ABCExt.h"; sourceTree = "<group>"; };
		660081482481F4C400761AE3 /* UIView+ABCExt.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+ABCExt.m"; sourceTree = "<group>"; };
		6601D11F24AC417500BC67C0 /* AbcCustomKeyboardViewManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AbcCustomKeyboardViewManager.mm; sourceTree = "<group>"; };
		6601D12024AC417500BC67C0 /* AbcCustomKeyboardViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcCustomKeyboardViewManager.h; sourceTree = "<group>"; };
		6601D12224AC41D300BC67C0 /* AbcCustomKeyboardView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcCustomKeyboardView.m; sourceTree = "<group>"; };
		6601D12324AC41D300BC67C0 /* AbcCustomKeyboardView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcCustomKeyboardView.h; sourceTree = "<group>"; };
		6608379424E4DA0400530B70 /* StatManagerModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StatManagerModule.m; sourceTree = "<group>"; };
		6608379524E4DA0400530B70 /* StatManagerModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StatManagerModule.h; sourceTree = "<group>"; };
		6608379824E4DEC100530B70 /* BuglyModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BuglyModule.h; sourceTree = "<group>"; };
		6608379924E4DEC100530B70 /* BuglyModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BuglyModule.m; sourceTree = "<group>"; };
		660929AD256F3FDC00436093 /* UrlLauncherModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UrlLauncherModule.h; sourceTree = "<group>"; };
		660929AE256F3FDC00436093 /* UrlLauncherModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UrlLauncherModule.m; sourceTree = "<group>"; };
		6610A83124D3DE41006D24A0 /* BarcodeScanManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BarcodeScanManager.h; sourceTree = "<group>"; };
		6610A83224D3DE41006D24A0 /* BarcodeScanManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BarcodeScanManager.m; sourceTree = "<group>"; };
		66137EFE24D8E9C900E44F63 /* AbcFetchInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcFetchInfo.m; sourceTree = "<group>"; };
		66137EFF24D8E9C900E44F63 /* AbcNetwork.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcNetwork.m; sourceTree = "<group>"; };
		66137F0024D8E9C900E44F63 /* AbcFetchInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcFetchInfo.h; sourceTree = "<group>"; };
		66137F0124D8E9C900E44F63 /* AbcNetwork.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcNetwork.h; sourceTree = "<group>"; };
		66137F0524D94B6300E44F63 /* FileUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FileUtils.h; sourceTree = "<group>"; };
		66137F0624D94B6300E44F63 /* FileUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FileUtils.m; sourceTree = "<group>"; };
		66137F7F24D9808F00E44F63 /* PluginUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PluginUtils.h; sourceTree = "<group>"; };
		66137F8024D9808F00E44F63 /* PluginUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PluginUtils.m; sourceTree = "<group>"; };
		66137F8324D9836E00E44F63 /* AppInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppInfo.h; sourceTree = "<group>"; };
		66137F8424D9836E00E44F63 /* AppInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppInfo.m; sourceTree = "<group>"; };
		66137F8824DA382D00E44F63 /* UrlLanucherUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UrlLanucherUtils.h; sourceTree = "<group>"; };
		66137F8924DA382D00E44F63 /* UrlLanucherUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UrlLanucherUtils.m; sourceTree = "<group>"; };
		66137F8C24DA819C00E44F63 /* SocketIOModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SocketIOModule.h; sourceTree = "<group>"; };
		66137F8D24DA819C00E44F63 /* SocketIOModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SocketIOModule.m; sourceTree = "<group>"; };
		66137FA124DBA5D600E44F63 /* HippyMethodInvoideCallback.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HippyMethodInvoideCallback.h; sourceTree = "<group>"; };
		66137FA424DBA9C900E44F63 /* NotificationAttachment.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NotificationAttachment.m; sourceTree = "<group>"; };
		66137FA524DBA9C900E44F63 /* NotificationTime.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NotificationTime.m; sourceTree = "<group>"; };
		66137FA624DBA9C900E44F63 /* LocalNotificationModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LocalNotificationModule.h; sourceTree = "<group>"; };
		66137FA724DBA9C900E44F63 /* NotificationAttachment.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NotificationAttachment.h; sourceTree = "<group>"; };
		66137FA824DBA9C900E44F63 /* NotificationDetails.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NotificationDetails.m; sourceTree = "<group>"; };
		66137FA924DBA9C900E44F63 /* NotificationTime.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NotificationTime.h; sourceTree = "<group>"; };
		66137FAA24DBA9C900E44F63 /* LocalNotificationModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LocalNotificationModule.m; sourceTree = "<group>"; };
		66137FAB24DBA9C900E44F63 /* NotificationDetails.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NotificationDetails.h; sourceTree = "<group>"; };
		66137FBB24DBDF3200E44F63 /* AbcBaseAppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcBaseAppDelegate.h; sourceTree = "<group>"; };
		66137FBE24DBE04000E44F63 /* AbcBaseAppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcBaseAppDelegate.m; sourceTree = "<group>"; };
		66137FC024DBF16700E44F63 /* XGPushModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = XGPushModule.m; sourceTree = "<group>"; };
		66137FC124DBF16700E44F63 /* XGPushModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = XGPushModule.h; sourceTree = "<group>"; };
		66220EB62447E96B00DA8A34 /* FileManagerModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FileManagerModule.m; sourceTree = "<group>"; };
		66220EB72447E96B00DA8A34 /* FileManagerModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FileManagerModule.h; sourceTree = "<group>"; };
		6625AA06246D20EB00E55AE9 /* AbcHippyTextViewImpl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcHippyTextViewImpl.h; sourceTree = "<group>"; };
		6625AA08246D20F200E55AE9 /* AbcHippyTextViewImpl.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AbcHippyTextViewImpl.mm; sourceTree = "<group>"; };
		6625AA0D246D222200E55AE9 /* AbcHippyTextViewImplManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcHippyTextViewImplManager.h; sourceTree = "<group>"; };
		6625AA0E246D222200E55AE9 /* AbcHippyTextViewImplManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AbcHippyTextViewImplManager.mm; sourceTree = "<group>"; };
		662AB36824DCE30F00596BB6 /* OSSModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = OSSModule.m; sourceTree = "<group>"; };
		662AB36924DCE30F00596BB6 /* OSSModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OSSModule.h; sourceTree = "<group>"; };
		662AB36B24DCE57F00596BB6 /* OssFileUploader.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = OssFileUploader.mm; sourceTree = "<group>"; };
		662AB36C24DCE57F00596BB6 /* OssFileUploader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OssFileUploader.h; sourceTree = "<group>"; };
		662AB36D24DCE57F00596BB6 /* OssManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = OssManager.mm; sourceTree = "<group>"; };
		662AB36E24DCE57F00596BB6 /* OssManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = OssManager.h; sourceTree = "<group>"; };
		6632F0D524CEC14A00B1BC5C /* LICENSE.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = LICENSE.md; sourceTree = "<group>"; };
		6632F0D624CEC14A00B1BC5C /* README.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		66413FF82448618A0029D998 /* TouchListenerViewTouchHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TouchListenerViewTouchHandler.m; sourceTree = "<group>"; };
		66413FF92448618A0029D998 /* TouchListenerViewTouchHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TouchListenerViewTouchHandler.h; sourceTree = "<group>"; };
		6643497C24D29C6400137935 /* AbcLineChartView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcLineChartView.h; sourceTree = "<group>"; };
		6643497D24D29C6400137935 /* AbcLineChartViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcLineChartViewManager.m; sourceTree = "<group>"; };
		6643497E24D29C6400137935 /* AbcLineChartViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcLineChartViewManager.h; sourceTree = "<group>"; };
		6643497F24D29C6400137935 /* AbcLineChartView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcLineChartView.m; sourceTree = "<group>"; };
		6643498324D2C23D00137935 /* DateValueFormatter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DateValueFormatter.m; sourceTree = "<group>"; };
		6643498424D2C23D00137935 /* DateValueFormatter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DateValueFormatter.h; sourceTree = "<group>"; };
		664AE93A2435FEE00063FCC2 /* ABCAnimationDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCAnimationDefine.h; sourceTree = "<group>"; };
		664AE93B2435FEE00063FCC2 /* CAKeyframeAnimation+AHEasing.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "CAKeyframeAnimation+AHEasing.h"; sourceTree = "<group>"; };
		664AE93C2435FEE00063FCC2 /* UIView+ABCAnimation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+ABCAnimation.h"; sourceTree = "<group>"; };
		664AE93D2435FEE00063FCC2 /* CAKeyframeAnimation+AHEasing.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "CAKeyframeAnimation+AHEasing.m"; sourceTree = "<group>"; };
		664AE93E2435FEE00063FCC2 /* UIView+ABCAnimation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+ABCAnimation.m"; sourceTree = "<group>"; };
		664AE93F2435FEE00063FCC2 /* UIUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UIUtils.h; sourceTree = "<group>"; };
		664AE9412435FEE00063FCC2 /* UIUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UIUtils.m; sourceTree = "<group>"; };
		664AE9812435FF3D0063FCC2 /* ABCMacros.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ABCMacros.h; sourceTree = "<group>"; };
		664AE9852436003E0063FCC2 /* ABCBaseViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ABCBaseViewController.m; sourceTree = "<group>"; };
		664AE9862436003E0063FCC2 /* ABCBaseViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCBaseViewController.h; sourceTree = "<group>"; };
		664AE9882436010A0063FCC2 /* easing.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = easing.c; sourceTree = "<group>"; };
		664AE9892436010A0063FCC2 /* easing.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = easing.h; sourceTree = "<group>"; };
		664BC55B24D3A81D00825CF5 /* WXApiHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WXApiHandler.h; sourceTree = "<group>"; };
		664BC55C24D3A81D00825CF5 /* WXApiHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WXApiHandler.m; sourceTree = "<group>"; };
		664BC56124D3C57400825CF5 /* HippyMixInvokeMethodModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyMixInvokeMethodModule.m; sourceTree = "<group>"; };
		664BC56224D3C57400825CF5 /* HippyMixInvokeMethodModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyMixInvokeMethodModule.h; sourceTree = "<group>"; };
		664BC56524D3C92100825CF5 /* BarcodeScannerViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BarcodeScannerViewController.h; sourceTree = "<group>"; };
		664BC56624D3C92100825CF5 /* ScannerOverlay.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ScannerOverlay.h; sourceTree = "<group>"; };
		664BC56724D3C92100825CF5 /* UIButton+TouchArea.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIButton+TouchArea.h"; sourceTree = "<group>"; };
		664BC56924D3C92100825CF5 /* TestFontViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TestFontViewController.h; sourceTree = "<group>"; };
		664BC56B24D3C92100825CF5 /* BarcodeScannerViewControllerDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BarcodeScannerViewControllerDelegate.h; sourceTree = "<group>"; };
		664BC56C24D3C92100825CF5 /* ScannerOverlay.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ScannerOverlay.m; sourceTree = "<group>"; };
		664BC56D24D3C92100825CF5 /* BarcodeScannerViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BarcodeScannerViewController.m; sourceTree = "<group>"; };
		664BC57024D3C92100825CF5 /* UIButton+TouchArea.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIButton+TouchArea.m"; sourceTree = "<group>"; };
		664BC57124D3C92100825CF5 /* TestFontViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TestFontViewController.m; sourceTree = "<group>"; };
		665A72EF2468F7B3008C6C61 /* AbcHippyTextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcHippyTextView.m; sourceTree = "<group>"; };
		665A72F02468F7B3008C6C61 /* AbcHippyShadowTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcHippyShadowTextView.h; sourceTree = "<group>"; };
		665A72F12468F7B3008C6C61 /* AbcHippyTextViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcHippyTextViewManager.h; sourceTree = "<group>"; };
		665A72F22468F7B3008C6C61 /* AbcHippyBaseTextInput.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcHippyBaseTextInput.m; sourceTree = "<group>"; };
		665A72F32468F7B3008C6C61 /* AbcHippyTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcHippyTextView.h; sourceTree = "<group>"; };
		665A72F42468F7B3008C6C61 /* AbcHippyTextSelection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcHippyTextSelection.h; sourceTree = "<group>"; };
		665A72F52468F7B3008C6C61 /* AbcHippyTextField.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcHippyTextField.h; sourceTree = "<group>"; };
		665A72F62468F7B3008C6C61 /* AbcHippyBaseTextInput.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcHippyBaseTextInput.h; sourceTree = "<group>"; };
		665A72F72468F7B3008C6C61 /* AbcHippyShadowTextView.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AbcHippyShadowTextView.mm; sourceTree = "<group>"; };
		665A72F82468F7B3008C6C61 /* AbcHippyTextSelection.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcHippyTextSelection.m; sourceTree = "<group>"; };
		665A72F92468F7B3008C6C61 /* AbcHippyTextField.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcHippyTextField.m; sourceTree = "<group>"; };
		665A72FA2468F7B3008C6C61 /* AbcHippyTextViewManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AbcHippyTextViewManager.mm; sourceTree = "<group>"; };
		665A7301246901AA008C6C61 /* AbcInputAccessoryView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcInputAccessoryView.m; sourceTree = "<group>"; };
		665A7302246901AA008C6C61 /* AbcInputAccessoryViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcInputAccessoryViewManager.h; sourceTree = "<group>"; };
		665A7303246901AA008C6C61 /* AbcInputAccessoryViewManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AbcInputAccessoryViewManager.mm; sourceTree = "<group>"; };
		665A7304246901AA008C6C61 /* AbcInputAccessoryView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcInputAccessoryView.h; sourceTree = "<group>"; };
		665D782324F5E6B2000DF0F7 /* AliPayModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AliPayModule.m; sourceTree = "<group>"; };
		665D782424F5E6B2000DF0F7 /* AliPayModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AliPayModule.h; sourceTree = "<group>"; };
		6661F26C24581BD300ECEA46 /* TransitionType.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TransitionType.h; sourceTree = "<group>"; };
		6664093D2521DF720024188E /* libXG-SDK-Cloud.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = "libXG-SDK-Cloud.a"; path = "third-party/tpns/libXG-SDK-Cloud.a"; sourceTree = "<group>"; };
		6664093F2521DF7E0024188E /* XGMTACloud.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = XGMTACloud.framework; path = "third-party/tpns/XGMTACloud.framework"; sourceTree = "<group>"; };
		666409432521DFA10024188E /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		666409452521DFB70024188E /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		666409472521DFC20024188E /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		666409492521DFD00024188E /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		6664094B2521DFD50024188E /* CoreData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreData.framework; path = System/Library/Frameworks/CoreData.framework; sourceTree = SDKROOT; };
		6664094D2521DFDB0024188E /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		6664094F2521DFE40024188E /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		667A64B924B725700090382A /* WxApiHostModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WxApiHostModule.h; sourceTree = "<group>"; };
		667A64BA24B725700090382A /* WxApiHostModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WxApiHostModule.m; sourceTree = "<group>"; };
		667A64C024B746B30090382A /* AbcConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcConfig.h; sourceTree = "<group>"; };
		667A64C124B746B30090382A /* AbcConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbcConfig.m; sourceTree = "<group>"; };
		667CE5D525A2C26500FF6C5A /* PinchImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PinchImageView.h; sourceTree = "<group>"; };
		667CE5D625A2C26500FF6C5A /* PinchImageViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PinchImageViewManager.m; sourceTree = "<group>"; };
		667CE5D725A2C26500FF6C5A /* PinchImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PinchImageView.m; sourceTree = "<group>"; };
		667CE5D825A2C26500FF6C5A /* PinchImageViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PinchImageViewManager.h; sourceTree = "<group>"; };
		668E758924370ABD002922A2 /* ABCNavigatorManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCNavigatorManager.h; sourceTree = "<group>"; };
		668E758A24370ABD002922A2 /* ABCNavigator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCNavigator.h; sourceTree = "<group>"; };
		668E758B24370ABD002922A2 /* ABCNavigatorItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ABCNavigatorItem.m; sourceTree = "<group>"; };
		668E758C24370ABD002922A2 /* ABCNavigatorItemManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCNavigatorItemManager.h; sourceTree = "<group>"; };
		668E758D24370ABD002922A2 /* ABCNavigatorManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ABCNavigatorManager.m; sourceTree = "<group>"; };
		668E758E24370ABD002922A2 /* ABCNavigatorItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCNavigatorItem.h; sourceTree = "<group>"; };
		668E758F24370ABD002922A2 /* ABCNavigator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ABCNavigator.m; sourceTree = "<group>"; };
		668E759024370ABD002922A2 /* ABCNavigatorItemManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ABCNavigatorItemManager.m; sourceTree = "<group>"; };
		6690F68C24F632F200F91E26 /* AudioPlayerMananger.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AudioPlayerMananger.h; sourceTree = "<group>"; };
		6690F68D24F632F200F91E26 /* AudioPlayerMananger.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AudioPlayerMananger.m; sourceTree = "<group>"; };
		66942EC124E53E9E00DC1B3A /* CaptureView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CaptureView.m; sourceTree = "<group>"; };
		66942EC224E53E9E00DC1B3A /* CaptureViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CaptureViewManager.h; sourceTree = "<group>"; };
		66942EC324E53E9E00DC1B3A /* CaptureView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CaptureView.h; sourceTree = "<group>"; };
		66942EC424E53E9F00DC1B3A /* CaptureViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CaptureViewManager.m; sourceTree = "<group>"; };
		66942EC824E548FA00DC1B3A /* GallerySaverModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GallerySaverModule.h; sourceTree = "<group>"; };
		66942EC924E548FA00DC1B3A /* GallerySaverModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GallerySaverModule.m; sourceTree = "<group>"; };
		66942ECC24E622B700DC1B3A /* WebViewUtilsModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WebViewUtilsModule.m; sourceTree = "<group>"; };
		66942ECD24E622B700DC1B3A /* WebViewUtilsModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WebViewUtilsModule.h; sourceTree = "<group>"; };
		66942ED024E671C000DC1B3A /* AbcWebViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcWebViewManager.m; sourceTree = "<group>"; };
		66942ED124E671C000DC1B3A /* AbcWebViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcWebViewManager.h; sourceTree = "<group>"; };
		66942ED224E671C000DC1B3A /* AbcWebView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcWebView.m; sourceTree = "<group>"; };
		66942ED324E671C000DC1B3A /* AbcWebView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcWebView.h; sourceTree = "<group>"; };
		66AFC5E224DD88F300BB0F83 /* AudioRecorderModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AudioRecorderModule.m; sourceTree = "<group>"; };
		66AFC5E324DD88F300BB0F83 /* AudioRecorderModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AudioRecorderModule.h; sourceTree = "<group>"; };
		66AFC5E624E2E7AA00BB0F83 /* ThirdCallModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ThirdCallModule.h; sourceTree = "<group>"; };
		66AFC5E724E2E7AA00BB0F83 /* ThirdCallModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ThirdCallModule.m; sourceTree = "<group>"; };
		66B3130224E50E8700F75F77 /* QRViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QRViewManager.m; sourceTree = "<group>"; };
		66B3130324E50E8700F75F77 /* QRView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QRView.h; sourceTree = "<group>"; };
		66B3130424E50E8700F75F77 /* QRView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QRView.m; sourceTree = "<group>"; };
		66B3130524E50E8800F75F77 /* QRViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QRViewManager.h; sourceTree = "<group>"; };
		66BA7F5D24F3E4B600899127 /* Runner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		66BF71DA28F54B0A00EE457A /* SVGManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SVGManager.h; sourceTree = "<group>"; };
		66BF71DB28F54B0A00EE457A /* SVGManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SVGManager.m; sourceTree = "<group>"; };
		66BF71DE28F54D1B00EE457A /* LruCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LruCache.h; sourceTree = "<group>"; };
		66BF71DF28F54D1B00EE457A /* LruCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LruCache.m; sourceTree = "<group>"; };
		66C17CCE2435B3CB0009D385 /* ReverseListViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ReverseListViewManager.m; sourceTree = "<group>"; };
		66C17CD02435B3CB0009D385 /* ReverseListItemView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ReverseListItemView.h; sourceTree = "<group>"; };
		66C17CD12435B3CB0009D385 /* ReverseListItemView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ReverseListItemView.m; sourceTree = "<group>"; };
		66C17CD22435B3CB0009D385 /* ReverseListViewDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ReverseListViewDataSource.h; sourceTree = "<group>"; };
		66C17CD32435B3CB0009D385 /* ReverseListView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ReverseListView.h; sourceTree = "<group>"; };
		66C17CD42435B3CB0009D385 /* ReverseListItemViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ReverseListItemViewManager.h; sourceTree = "<group>"; };
		66C17CD52435B3CB0009D385 /* ReverseListItemViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ReverseListItemViewManager.m; sourceTree = "<group>"; };
		66C17CD62435B3CB0009D385 /* ReverseListView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ReverseListView.m; sourceTree = "<group>"; };
		66C17CD72435B3CB0009D385 /* ReverseListViewDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ReverseListViewDataSource.m; sourceTree = "<group>"; };
		66C17CD82435B3CB0009D385 /* ReverseListViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ReverseListViewManager.h; sourceTree = "<group>"; };
		66C17D212435C1F40009D385 /* TouchListenerViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TouchListenerViewManager.h; sourceTree = "<group>"; };
		66C17D222435C1F40009D385 /* TouchListenerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TouchListenerView.h; sourceTree = "<group>"; };
		66C17D232435C1F40009D385 /* TouchListenerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TouchListenerView.m; sourceTree = "<group>"; };
		66C17D242435C1F40009D385 /* TouchListenerViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TouchListenerViewManager.m; sourceTree = "<group>"; };
		66C17D272435D43B0009D385 /* DebugView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DebugView.h; sourceTree = "<group>"; };
		66C17D282435D43B0009D385 /* DebugView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DebugView.m; sourceTree = "<group>"; };
		66C17D2E2435DF0D0009D385 /* Singleton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Singleton.h; sourceTree = "<group>"; };
		66C17D2F2435DF180009D385 /* Log.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Log.h; sourceTree = "<group>"; };
		66CBBE7C24DD03D3003A9F78 /* ImagePickerMetaDataUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ImagePickerMetaDataUtil.m; sourceTree = "<group>"; };
		66CBBE7D24DD03D3003A9F78 /* ImagePickerPhotoAssetUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ImagePickerPhotoAssetUtil.m; sourceTree = "<group>"; };
		66CBBE7E24DD03D3003A9F78 /* ImagePickerPhotoAssetUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ImagePickerPhotoAssetUtil.h; sourceTree = "<group>"; };
		66CBBE8024DD03D3003A9F78 /* ImagePickerImageUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ImagePickerImageUtil.h; sourceTree = "<group>"; };
		66CBBE8324DD03D3003A9F78 /* ImagePickerMetaDataUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ImagePickerMetaDataUtil.h; sourceTree = "<group>"; };
		66CBBE8424DD03D3003A9F78 /* ImagePickerImageUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ImagePickerImageUtil.m; sourceTree = "<group>"; };
		66CBBE8B24DD0422003A9F78 /* ImagePickerModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ImagePickerModule.m; sourceTree = "<group>"; };
		66CBBE8C24DD0422003A9F78 /* ImagePickerModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ImagePickerModule.h; sourceTree = "<group>"; };
		66D36F0A250F63D200E8E232 /* IBackListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IBackListener.h; sourceTree = "<group>"; };
		66D59D0A2481363700EC1077 /* AbcImageLoaderModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcImageLoaderModule.m; sourceTree = "<group>"; };
		66D59D0B2481363700EC1077 /* AbcImageLoaderModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcImageLoaderModule.h; sourceTree = "<group>"; };
		66DC5C0E2990C944000F7965 /* AbcScrollViewManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = AbcScrollViewManager.mm; sourceTree = "<group>"; };
		66DC5C0F2990C944000F7965 /* AbcScrollableProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcScrollableProtocol.h; sourceTree = "<group>"; };
		66DC5C102990C944000F7965 /* AbcScrollViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcScrollViewManager.h; sourceTree = "<group>"; };
		66DC5C112990C944000F7965 /* AbcScrollView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AbcScrollView.m; sourceTree = "<group>"; };
		66DC5C122990C945000F7965 /* AbcScrollView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AbcScrollView.h; sourceTree = "<group>"; };
		66E2334F25418FD700135BE5 /* BlurViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BlurViewManager.h; sourceTree = "<group>"; };
		66E2335025418FD700135BE5 /* BlurEffectWithAmount.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BlurEffectWithAmount.m; sourceTree = "<group>"; };
		66E2335125418FD700135BE5 /* BlurView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BlurView.m; sourceTree = "<group>"; };
		66E2335225418FD700135BE5 /* BlurEffectWithAmount.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BlurEffectWithAmount.h; sourceTree = "<group>"; };
		66E2335325418FD700135BE5 /* BlurViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BlurViewManager.m; sourceTree = "<group>"; };
		66E2335425418FD700135BE5 /* BlurView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BlurView.h; sourceTree = "<group>"; };
		7AFFD8ED1D35381100E5BB4D /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		7AFFD8EE1D35381100E5BB4D /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 2; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; tabWidth = 2; };
		7D88C4FC2E24E511003AFF5A /* AbcAsrConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcAsrConfig.h; sourceTree = "<group>"; };
		7D88C4FD2E24E511003AFF5A /* AbcAsrConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbcAsrConfig.m; sourceTree = "<group>"; };
		7D88C4FF2E24E511003AFF5A /* AbcAsrBackgroundHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcAsrBackgroundHandler.h; sourceTree = "<group>"; };
		7D88C5002E24E511003AFF5A /* AbcAsrBackgroundHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbcAsrBackgroundHandler.m; sourceTree = "<group>"; };
		7D88C5012E24E511003AFF5A /* AbcAsrManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcAsrManager.h; sourceTree = "<group>"; };
		7D88C5022E24E511003AFF5A /* AbcAsrManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbcAsrManager.m; sourceTree = "<group>"; };
		7D88C5032E24E511003AFF5A /* AbcAsrModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcAsrModule.h; sourceTree = "<group>"; };
		7D88C5042E24E511003AFF5A /* AbcAsrModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbcAsrModule.m; sourceTree = "<group>"; };
		7D88C5052E24E511003AFF5A /* AbcAsrWebSocketClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcAsrWebSocketClient.h; sourceTree = "<group>"; };
		7D88C5062E24E511003AFF5A /* AbcAsrWebSocketClient.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbcAsrWebSocketClient.m; sourceTree = "<group>"; };
		7D88C5072E24E511003AFF5A /* AbcAudioRecorder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcAudioRecorder.h; sourceTree = "<group>"; };
		7D88C5082E24E511003AFF5A /* AbcAudioRecorder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbcAudioRecorder.m; sourceTree = "<group>"; };
		7DFEBCD12E12A6580024EE75 /* AbcSseConnection.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcSseConnection.h; sourceTree = "<group>"; };
		7DFEBCD22E12A6580024EE75 /* AbcSseConnection.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbcSseConnection.m; sourceTree = "<group>"; };
		7DFEBCD32E12A6580024EE75 /* AbcSseEvent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbcSseEvent.h; sourceTree = "<group>"; };
		7DFEBCD42E12A6580024EE75 /* AbcSseEvent.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbcSseEvent.m; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146F21CF9000F007C117D /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A18C85A7F05F610A7BC5E5ED /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Pods/Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		B19A5C9E21CF7AF1005E6B11 /* FluwxPlugin.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FluwxPlugin.m; sourceTree = "<group>"; };
		B19A5CC921CF859A005E6B11 /* CallResults.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CallResults.h; sourceTree = "<group>"; };
		B19A5CCA21CF859D005E6B11 /* CallResults.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CallResults.m; sourceTree = "<group>"; };
		B19A5CCD21CF85C2005E6B11 /* StringToWeChatScene.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = StringToWeChatScene.m; sourceTree = "<group>"; };
		B19A5CCE21CF85C2005E6B11 /* FluwxKeys.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FluwxKeys.h; sourceTree = "<group>"; };
		B19A5CCF21CF85C3005E6B11 /* StringUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = StringUtil.h; sourceTree = "<group>"; };
		B19A5CD121CF85C3005E6B11 /* ImageSchema.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ImageSchema.m; sourceTree = "<group>"; };
		B19A5CD221CF85C4005E6B11 /* FluwxMethods.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FluwxMethods.h; sourceTree = "<group>"; };
		B19A5CD321CF85C4005E6B11 /* StringUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = StringUtil.m; sourceTree = "<group>"; };
		B19A5CD421CF85C4005E6B11 /* FluwxKeys.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FluwxKeys.m; sourceTree = "<group>"; };
		B19A5CD521CF85C5005E6B11 /* FluwxMethods.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FluwxMethods.m; sourceTree = "<group>"; };
		B19A5CD621CF85C5005E6B11 /* StringToWeChatScene.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = StringToWeChatScene.h; sourceTree = "<group>"; };
		B19A5CDD21CF85CE005E6B11 /* FluwxLaunchMiniProgramHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FluwxLaunchMiniProgramHandler.m; sourceTree = "<group>"; };
		B19A5CDE21CF85CF005E6B11 /* FluwxShareHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FluwxShareHandler.m; sourceTree = "<group>"; };
		B19A5CE021CF85D0005E6B11 /* FluwxAuthHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FluwxAuthHandler.m; sourceTree = "<group>"; };
		B19A5CE121CF85D0005E6B11 /* FluwxSubscribeMsgHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FluwxSubscribeMsgHandler.m; sourceTree = "<group>"; };
		B19A5CE221CF85D0005E6B11 /* NSStringWrapper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NSStringWrapper.h; sourceTree = "<group>"; };
		B19A5CE321CF85D0005E6B11 /* FluwxPaymentHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FluwxPaymentHandler.m; sourceTree = "<group>"; };
		B19A5CE421CF85D1005E6B11 /* NSStringWrapper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NSStringWrapper.m; sourceTree = "<group>"; };
		B19A5CEC21CF85E0005E6B11 /* FluwxLaunchMiniProgramHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FluwxLaunchMiniProgramHandler.h; sourceTree = "<group>"; };
		B19A5CED21CF85E0005E6B11 /* FluwxPlugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FluwxPlugin.h; sourceTree = "<group>"; };
		B19A5CEE21CF85E1005E6B11 /* FluwxPaymentHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FluwxPaymentHandler.h; sourceTree = "<group>"; };
		B19A5CF021CF85E1005E6B11 /* FluwxResponseHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FluwxResponseHandler.h; sourceTree = "<group>"; };
		B19A5CF121CF85E2005E6B11 /* FluwxShareHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FluwxShareHandler.h; sourceTree = "<group>"; };
		B19A5CF221CF85E2005E6B11 /* FluwxSubscribeMsgHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FluwxSubscribeMsgHandler.h; sourceTree = "<group>"; };
		B19A5CF321CF85E2005E6B11 /* WXApiRequestHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WXApiRequestHandler.h; sourceTree = "<group>"; };
		B19A5CF521CF85E3005E6B11 /* FluwxAuthHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FluwxAuthHandler.h; sourceTree = "<group>"; };
		B19A5CF821CF85ED005E6B11 /* WXMediaMessage+messageConstruct.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "WXMediaMessage+messageConstruct.h"; sourceTree = "<group>"; };
		B19A5CF921CF85ED005E6B11 /* WXApiRequestHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WXApiRequestHandler.m; sourceTree = "<group>"; };
		B19A5CFA21CF85ED005E6B11 /* WXMediaMessage+messageConstruct.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "WXMediaMessage+messageConstruct.m"; sourceTree = "<group>"; };
		B19A5CFB21CF85EE005E6B11 /* SendMessageToWXReq+requestWithTextOrMediaMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SendMessageToWXReq+requestWithTextOrMediaMessage.h"; sourceTree = "<group>"; };
		B19A5CFC21CF85EF005E6B11 /* FluwxResponseHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FluwxResponseHandler.m; sourceTree = "<group>"; };
		B99770702836169900D34173 /* engine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = engine.h; sourceTree = "<group>"; };
		B99770712836169900D34173 /* core.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = core.h; sourceTree = "<group>"; };
		B99770732836169900D34173 /* javascript_task.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = javascript_task.h; sourceTree = "<group>"; };
		B99770742836169900D34173 /* worker_task_runner.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = worker_task_runner.h; sourceTree = "<group>"; };
		B99770752836169900D34173 /* common_task.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = common_task.h; sourceTree = "<group>"; };
		B99770762836169900D34173 /* javascript_task_runner.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = javascript_task_runner.h; sourceTree = "<group>"; };
		B99770782836169900D34173 /* module_register.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = module_register.h; sourceTree = "<group>"; };
		B99770792836169900D34173 /* console_module.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = console_module.h; sourceTree = "<group>"; };
		B997707A2836169900D34173 /* timer_module.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = timer_module.h; sourceTree = "<group>"; };
		B997707B2836169900D34173 /* module_base.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = module_base.h; sourceTree = "<group>"; };
		B997707C2836169900D34173 /* contextify_module.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = contextify_module.h; sourceTree = "<group>"; };
		B997707D2836169900D34173 /* scope.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = scope.h; sourceTree = "<group>"; };
		B997707F2836169900D34173 /* js_value_wrapper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = js_value_wrapper.h; sourceTree = "<group>"; };
		B99770802836169900D34173 /* uri_loader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = uri_loader.h; sourceTree = "<group>"; };
		B99770812836169900D34173 /* task.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = task.h; sourceTree = "<group>"; };
		B99770822836169900D34173 /* string_view_utils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = string_view_utils.h; sourceTree = "<group>"; };
		B99770832836169900D34173 /* file.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = file.h; sourceTree = "<group>"; };
		B99770842836169900D34173 /* task_runner.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = task_runner.h; sourceTree = "<group>"; };
		B99770852836169900D34173 /* thread.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = thread.h; sourceTree = "<group>"; };
		B99770862836169900D34173 /* common.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = common.h; sourceTree = "<group>"; };
		B99770872836169900D34173 /* macros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = macros.h; sourceTree = "<group>"; };
		B99770882836169900D34173 /* thread_id.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = thread_id.h; sourceTree = "<group>"; };
		B99770892836169900D34173 /* base_time.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = base_time.h; sourceTree = "<group>"; };
		B997708A2836169900D34173 /* hash.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hash.h; sourceTree = "<group>"; };
		B997708F2836169900D34173 /* callback_info.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = callback_info.h; sourceTree = "<group>"; };
		B99770902836169900D34173 /* js_native_api_types.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = js_native_api_types.h; sourceTree = "<group>"; };
		B99770912836169900D34173 /* js_native_turbo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = js_native_turbo.h; sourceTree = "<group>"; };
		B99770922836169900D34173 /* js_native_api.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = js_native_api.h; sourceTree = "<group>"; };
		B99770932836169900D34173 /* native_source_code.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = native_source_code.h; sourceTree = "<group>"; };
		B99770952836169900D34173 /* js_native_turbo_jsc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = js_native_turbo_jsc.h; sourceTree = "<group>"; };
		B99770962836169900D34173 /* js_native_jsc_helper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = js_native_jsc_helper.h; sourceTree = "<group>"; };
		B99770972836169900D34173 /* js_native_api_jsc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = js_native_api_jsc.h; sourceTree = "<group>"; };
		B997709D2836169900D34173 /* logging.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = logging.h; sourceTree = "<group>"; };
		B997709E2836169900D34173 /* log_level.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = log_level.h; sourceTree = "<group>"; };
		B997709F2836169900D34173 /* macros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = macros.h; sourceTree = "<group>"; };
		B99770A02836169900D34173 /* log_settings.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = log_settings.h; sourceTree = "<group>"; };
		B99770A12836169900D34173 /* unicode_string_view.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = unicode_string_view.h; sourceTree = "<group>"; };
		B99770A22836169900D34173 /* .gitignore */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = .gitignore; sourceTree = "<group>"; };
		B99770A32836169900D34173 /* .clang-format */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = ".clang-format"; sourceTree = "<group>"; };
		B99770AC2836169900D34173 /* logging.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = logging.cc; sourceTree = "<group>"; };
		B99770AE2836169900D34173 /* log_settings_state.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = log_settings_state.cc; sourceTree = "<group>"; };
		B99770B02836169900D34173 /* log_settings.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = log_settings.cc; sourceTree = "<group>"; };
		B99770B12836169900D34173 /* unicode_string_view.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = unicode_string_view.cc; sourceTree = "<group>"; };
		B99770B62836169900D34173 /* scope.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = scope.cc; sourceTree = "<group>"; };
		B99770B72836169900D34173 /* engine.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = engine.cc; sourceTree = "<group>"; };
		B99770B92836169900D34173 /* javascript_task_runner.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = javascript_task_runner.cc; sourceTree = "<group>"; };
		B99770BA2836169900D34173 /* javascript_task.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = javascript_task.cc; sourceTree = "<group>"; };
		B99770BB2836169900D34173 /* worker_task_runner.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = worker_task_runner.cc; sourceTree = "<group>"; };
		B99770BC2836169900D34173 /* common_task.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = common_task.cc; sourceTree = "<group>"; };
		B99770BE2836169900D34173 /* module_register.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = module_register.cc; sourceTree = "<group>"; };
		B99770BF2836169900D34173 /* console_module.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = console_module.cc; sourceTree = "<group>"; };
		B99770C02836169900D34173 /* timer_module.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = timer_module.cc; sourceTree = "<group>"; };
		B99770C12836169900D34173 /* contextify_module.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = contextify_module.cc; sourceTree = "<group>"; };
		B99770C32836169900D34173 /* js_value_wrapper.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = js_value_wrapper.cc; sourceTree = "<group>"; };
		B99770C42836169900D34173 /* file.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = file.cc; sourceTree = "<group>"; };
		B99770C52836169900D34173 /* thread_id.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = thread_id.cc; sourceTree = "<group>"; };
		B99770C62836169900D34173 /* task_runner.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = task_runner.cc; sourceTree = "<group>"; };
		B99770C72836169900D34173 /* task.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = task.cc; sourceTree = "<group>"; };
		B99770C82836169900D34173 /* thread.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = thread.cc; sourceTree = "<group>"; };
		B99770CE2836169900D34173 /* callback_info.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = callback_info.cc; sourceTree = "<group>"; };
		B99770CF2836169900D34173 /* js_native_turbo.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = js_native_turbo.cc; sourceTree = "<group>"; };
		B99770D12836169900D34173 /* js_native_jsc_helper.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = js_native_jsc_helper.cc; sourceTree = "<group>"; };
		B99770D22836169900D34173 /* js_native_turbo_jsc.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = js_native_turbo_jsc.cc; sourceTree = "<group>"; };
		B99770D32836169900D34173 /* native_source_code_ios.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = native_source_code_ios.cc; sourceTree = "<group>"; };
		B99770D42836169900D34173 /* js_native_api_value_jsc.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = js_native_api_value_jsc.cc; sourceTree = "<group>"; };
		B99770D52836169900D34173 /* js_native_api_jsc.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = js_native_api_jsc.cc; sourceTree = "<group>"; };
		B997711E283616AA00D34173 /* HippyCustomTouchHandlerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyCustomTouchHandlerProtocol.h; sourceTree = "<group>"; };
		B9977120283616AA00D34173 /* MTTLayoutCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTTLayoutCache.h; sourceTree = "<group>"; };
		B9977121283616AA00D34173 /* x5LayoutUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = x5LayoutUtil.m; sourceTree = "<group>"; };
		B9977122283616AA00D34173 /* MTTFlexLine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTTFlexLine.h; sourceTree = "<group>"; };
		B9977123283616AA00D34173 /* MTTFlex.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTTFlex.h; sourceTree = "<group>"; };
		B9977124283616AA00D34173 /* MTTNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTTNode.h; sourceTree = "<group>"; };
		B9977125283616AA00D34173 /* MTTLayout.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = MTTLayout.cpp; sourceTree = "<group>"; };
		B9977126283616AA00D34173 /* MTTLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTTLayout.h; sourceTree = "<group>"; };
		B9977127283616AA00D34173 /* MTTTStyle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTTTStyle.h; sourceTree = "<group>"; };
		B9977128283616AA00D34173 /* MTTLayoutCache.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = MTTLayoutCache.cpp; sourceTree = "<group>"; };
		B9977129283616AA00D34173 /* MTTTStyle.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = MTTTStyle.cpp; sourceTree = "<group>"; };
		B997712A283616AA00D34173 /* MTTUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MTTUtil.h; sourceTree = "<group>"; };
		B997712B283616AA00D34173 /* x5LayoutUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = x5LayoutUtil.h; sourceTree = "<group>"; };
		B997712C283616AA00D34173 /* MTTNode.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = MTTNode.cpp; sourceTree = "<group>"; };
		B997712D283616AA00D34173 /* MTTFlexLine.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = MTTFlexLine.cpp; sourceTree = "<group>"; };
		B997712E283616AA00D34173 /* MTTUtil.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = MTTUtil.cpp; sourceTree = "<group>"; };
		B9977131283616AA00D34173 /* HippyOCTurboModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyOCTurboModule.h; sourceTree = "<group>"; };
		B9977132283616AA00D34173 /* HippyTurboModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyTurboModule.h; sourceTree = "<group>"; };
		B9977133283616AA00D34173 /* HippyTurboModuleManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyTurboModuleManager.h; sourceTree = "<group>"; };
		B9977134283616AA00D34173 /* NSObject+HippyTurbo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSObject+HippyTurbo.h"; sourceTree = "<group>"; };
		B9977135283616AA00D34173 /* HippyOCTurboModule+Inner.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "HippyOCTurboModule+Inner.h"; sourceTree = "<group>"; };
		B9977136283616AA00D34173 /* HippyOCTurboModule.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyOCTurboModule.mm; sourceTree = "<group>"; };
		B9977137283616AA00D34173 /* HippyTurboModuleManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyTurboModuleManager.mm; sourceTree = "<group>"; };
		B9977138283616AA00D34173 /* NSObject+HippyTurbo.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "NSObject+HippyTurbo.mm"; sourceTree = "<group>"; };
		B997713A283616AA00D34173 /* HippyNetWork.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyNetWork.m; sourceTree = "<group>"; };
		B997713B283616AA00D34173 /* HippyFetchInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyFetchInfo.m; sourceTree = "<group>"; };
		B997713C283616AA00D34173 /* HippyNetWork.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyNetWork.h; sourceTree = "<group>"; };
		B997713D283616AA00D34173 /* HippyFetchInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyFetchInfo.h; sourceTree = "<group>"; };
		B997713F283616AA00D34173 /* HippyExtAnimation+Value.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "HippyExtAnimation+Value.m"; sourceTree = "<group>"; };
		B9977140283616AA00D34173 /* HippyExtAnimationGroup.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyExtAnimationGroup.h; sourceTree = "<group>"; };
		B9977141283616AA00D34173 /* CALayer+HippyAnimation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "CALayer+HippyAnimation.h"; sourceTree = "<group>"; };
		B9977142283616AA00D34173 /* HippyExtAnimation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyExtAnimation.m; sourceTree = "<group>"; };
		B9977143283616AA00D34173 /* HippyExtAnimation+Group.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "HippyExtAnimation+Group.h"; sourceTree = "<group>"; };
		B9977144283616AA00D34173 /* HippyExtAnimationModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyExtAnimationModule.m; sourceTree = "<group>"; };
		B9977145283616AA00D34173 /* HippyExtAnimationViewParams.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyExtAnimationViewParams.h; sourceTree = "<group>"; };
		B9977146283616AA00D34173 /* HippyExtAnimation+Value.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "HippyExtAnimation+Value.h"; sourceTree = "<group>"; };
		B9977147283616AA00D34173 /* HippyExtAnimation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyExtAnimation.h; sourceTree = "<group>"; };
		B9977148283616AA00D34173 /* CALayer+HippyAnimation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "CALayer+HippyAnimation.m"; sourceTree = "<group>"; };
		B9977149283616AA00D34173 /* HippyExtAnimationGroup.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyExtAnimationGroup.m; sourceTree = "<group>"; };
		B997714A283616AA00D34173 /* HippyExtAnimationViewParams.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyExtAnimationViewParams.m; sourceTree = "<group>"; };
		B997714B283616AA00D34173 /* HippyExtAnimationModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyExtAnimationModule.h; sourceTree = "<group>"; };
		B997714D283616AA00D34173 /* HippyNetInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyNetInfo.h; sourceTree = "<group>"; };
		B997714E283616AA00D34173 /* HippyNetInfoIntenal.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyNetInfoIntenal.m; sourceTree = "<group>"; };
		B997714F283616AA00D34173 /* HippyNetInfoIntenal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyNetInfoIntenal.h; sourceTree = "<group>"; };
		B9977150283616AA00D34173 /* HippyNetInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyNetInfo.m; sourceTree = "<group>"; };
		B9977152283616AA00D34173 /* HippyTiming.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyTiming.mm; sourceTree = "<group>"; };
		B9977153283616AA00D34173 /* HippyTiming.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyTiming.h; sourceTree = "<group>"; };
		B9977155283616AA00D34173 /* ios_loader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ios_loader.h; sourceTree = "<group>"; };
		B9977156283616AA00D34173 /* ios_loader.cc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = ios_loader.cc; sourceTree = "<group>"; };
		B9977158283616AA00D34173 /* HippyEventObserverModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyEventObserverModule.m; sourceTree = "<group>"; };
		B9977159283616AA00D34173 /* HippyEventObserverModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyEventObserverModule.h; sourceTree = "<group>"; };
		B997715B283616AA00D34173 /* HippyAsyncLocalStorage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyAsyncLocalStorage.h; sourceTree = "<group>"; };
		B997715C283616AA00D34173 /* HippyAsyncLocalStorage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyAsyncLocalStorage.m; sourceTree = "<group>"; };
		B997715E283616AA00D34173 /* HippyDevMenu.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyDevMenu.mm; sourceTree = "<group>"; };
		B997715F283616AA00D34173 /* HippyDevMenu.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDevMenu.h; sourceTree = "<group>"; };
		B9977160283616AA00D34173 /* HippyRedBox.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyRedBox.m; sourceTree = "<group>"; };
		B9977161283616AA00D34173 /* HippyDevLoadingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDevLoadingView.h; sourceTree = "<group>"; };
		B9977162283616AA00D34173 /* HippyDevLoadingView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyDevLoadingView.m; sourceTree = "<group>"; };
		B9977163283616AA00D34173 /* HippyRedBox.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyRedBox.h; sourceTree = "<group>"; };
		B9977165283616AA00D34173 /* HippyExceptionModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyExceptionModule.h; sourceTree = "<group>"; };
		B9977166283616AA00D34173 /* HippyExceptionModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyExceptionModule.m; sourceTree = "<group>"; };
		B9977168283616AA00D34173 /* HippyImageLoaderModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyImageLoaderModule.m; sourceTree = "<group>"; };
		B9977169283616AA00D34173 /* HippyImageCacheManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyImageCacheManager.h; sourceTree = "<group>"; };
		B997716A283616AA00D34173 /* HippyImageLoaderModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyImageLoaderModule.h; sourceTree = "<group>"; };
		B997716B283616AA00D34173 /* HippyImageCacheManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyImageCacheManager.m; sourceTree = "<group>"; };
		B997716D283616AA00D34173 /* HippyUIManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyUIManager.h; sourceTree = "<group>"; };
		B997716E283616AA00D34173 /* HippyUIManager+Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "HippyUIManager+Private.h"; sourceTree = "<group>"; };
		B997716F283616AA00D34173 /* HippyUIManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyUIManager.mm; sourceTree = "<group>"; };
		B9977171283616AA00D34173 /* HippyClipboardModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyClipboardModule.h; sourceTree = "<group>"; };
		B9977172283616AA00D34173 /* HippyClipboardModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyClipboardModule.m; sourceTree = "<group>"; };
		B9977175283616AA00D34173 /* HippyScrollView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyScrollView.m; sourceTree = "<group>"; };
		B9977176283616AA00D34173 /* HippyScrollableProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyScrollableProtocol.h; sourceTree = "<group>"; };
		B9977177283616AA00D34173 /* HippyScrollViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyScrollViewManager.h; sourceTree = "<group>"; };
		B9977178283616AA00D34173 /* HippyScrollView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyScrollView.h; sourceTree = "<group>"; };
		B9977179283616AA00D34173 /* HippyScrollViewManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyScrollViewManager.mm; sourceTree = "<group>"; };
		B997717B283616AA00D34173 /* HippyRefreshWrapperItemViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyRefreshWrapperItemViewManager.h; sourceTree = "<group>"; };
		B997717C283616AA00D34173 /* HippyRefreshWrapperItemView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyRefreshWrapperItemView.m; sourceTree = "<group>"; };
		B997717D283616AA00D34173 /* HippyRefreshWrapperViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyRefreshWrapperViewManager.m; sourceTree = "<group>"; };
		B997717E283616AA00D34173 /* HippyRefreshWrapper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyRefreshWrapper.m; sourceTree = "<group>"; };
		B997717F283616AA00D34173 /* HippyRefreshWrapperItemView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyRefreshWrapperItemView.h; sourceTree = "<group>"; };
		B9977180283616AA00D34173 /* HippyRefreshWrapperItemViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyRefreshWrapperItemViewManager.m; sourceTree = "<group>"; };
		B9977181283616AA00D34173 /* HippyRefreshWrapper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyRefreshWrapper.h; sourceTree = "<group>"; };
		B9977182283616AA00D34173 /* HippyRefreshWrapperViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyRefreshWrapperViewManager.h; sourceTree = "<group>"; };
		B9977184283616AA00D34173 /* HippyRefresh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyRefresh.h; sourceTree = "<group>"; };
		B9977185283616AA00D34173 /* HippyRefresh.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyRefresh.m; sourceTree = "<group>"; };
		B9977187283616AA00D34173 /* HippyViewPagerItemManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyViewPagerItemManager.h; sourceTree = "<group>"; };
		B9977188283616AA00D34173 /* HippyViewPager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyViewPager.m; sourceTree = "<group>"; };
		B9977189283616AA00D34173 /* HippyViewPagerItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyViewPagerItem.h; sourceTree = "<group>"; };
		B997718A283616AA00D34173 /* HippyViewPagerManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyViewPagerManager.m; sourceTree = "<group>"; };
		B997718B283616AA00D34173 /* HippyViewPagerItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyViewPagerItem.m; sourceTree = "<group>"; };
		B997718C283616AA00D34173 /* HippyViewPager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyViewPager.h; sourceTree = "<group>"; };
		B997718D283616AA00D34173 /* HippyViewPagerItemManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyViewPagerItemManager.m; sourceTree = "<group>"; };
		B997718E283616AA00D34173 /* HippyViewPagerManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyViewPagerManager.h; sourceTree = "<group>"; };
		B9977190283616AA00D34173 /* HippyBaseTextInput.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBaseTextInput.h; sourceTree = "<group>"; };
		B9977191283616AA00D34173 /* HippyTextView.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyTextView.mm; sourceTree = "<group>"; };
		B9977192283616AA00D34173 /* HippyTextViewManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyTextViewManager.mm; sourceTree = "<group>"; };
		B9977193283616AA00D34173 /* HippyTextField.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyTextField.h; sourceTree = "<group>"; };
		B9977194283616AA00D34173 /* HippyShadowTextView.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyShadowTextView.mm; sourceTree = "<group>"; };
		B9977195283616AA00D34173 /* HippyTextSelection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyTextSelection.h; sourceTree = "<group>"; };
		B9977196283616AA00D34173 /* HippyTextViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyTextViewManager.h; sourceTree = "<group>"; };
		B9977197283616AA00D34173 /* HippyBaseTextInput.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyBaseTextInput.m; sourceTree = "<group>"; };
		B9977198283616AA00D34173 /* HippyShadowTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyShadowTextView.h; sourceTree = "<group>"; };
		B9977199283616AA00D34173 /* HippyTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyTextView.h; sourceTree = "<group>"; };
		B997719A283616AA00D34173 /* HippyTextSelection.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyTextSelection.m; sourceTree = "<group>"; };
		B997719B283616AA00D34173 /* HippyTextField.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyTextField.m; sourceTree = "<group>"; };
		B997719D283616AA00D34173 /* HippySimpleWebView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippySimpleWebView.h; sourceTree = "<group>"; };
		B997719E283616AA00D34173 /* HippySimpleWebViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippySimpleWebViewManager.h; sourceTree = "<group>"; };
		B997719F283616AA00D34173 /* HippySimpleWebView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippySimpleWebView.m; sourceTree = "<group>"; };
		B99771A0283616AA00D34173 /* HippySimpleWebViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippySimpleWebViewManager.m; sourceTree = "<group>"; };
		B99771A2283616AA00D34173 /* HippyHeaderRefreshManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyHeaderRefreshManager.h; sourceTree = "<group>"; };
		B99771A3283616AA00D34173 /* HippyHeaderRefresh.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyHeaderRefresh.m; sourceTree = "<group>"; };
		B99771A4283616AA00D34173 /* HippyHeaderRefreshManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyHeaderRefreshManager.m; sourceTree = "<group>"; };
		B99771A5283616AA00D34173 /* HippyHeaderRefresh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyHeaderRefresh.h; sourceTree = "<group>"; };
		B99771A7283616AA00D34173 /* HippyReusableNodeCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyReusableNodeCache.m; sourceTree = "<group>"; };
		B99771A8283616AA00D34173 /* HippyWaterfallItemViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyWaterfallItemViewManager.h; sourceTree = "<group>"; };
		B99771A9283616AA00D34173 /* HippyCollectionViewWaterfallLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyCollectionViewWaterfallLayout.h; sourceTree = "<group>"; };
		B99771AA283616AA00D34173 /* HippyWaterfallItemView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyWaterfallItemView.h; sourceTree = "<group>"; };
		B99771AB283616AA00D34173 /* HippyWaterfallViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyWaterfallViewManager.h; sourceTree = "<group>"; };
		B99771AC283616AA00D34173 /* HippyWaterfallView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyWaterfallView.m; sourceTree = "<group>"; };
		B99771AD283616AA00D34173 /* HippyWaterfallItemViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyWaterfallItemViewManager.m; sourceTree = "<group>"; };
		B99771AE283616AA00D34173 /* HippyReusableNodeCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyReusableNodeCache.h; sourceTree = "<group>"; };
		B99771AF283616AA00D34173 /* HippyWaterfallItemView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyWaterfallItemView.m; sourceTree = "<group>"; };
		B99771B0283616AA00D34173 /* HippyCollectionViewWaterfallLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyCollectionViewWaterfallLayout.m; sourceTree = "<group>"; };
		B99771B1283616AA00D34173 /* HippyWaterfallViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyWaterfallViewManager.m; sourceTree = "<group>"; };
		B99771B2283616AA00D34173 /* HippyWaterfallView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyWaterfallView.h; sourceTree = "<group>"; };
		B99771B4283616AA00D34173 /* HippyDefaultImageProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDefaultImageProvider.h; sourceTree = "<group>"; };
		B99771B5283616AA00D34173 /* HippyImageViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyImageViewManager.h; sourceTree = "<group>"; };
		B99771B6283616AA00D34173 /* HippyImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyImageView.m; sourceTree = "<group>"; };
		B99771B7283616AA00D34173 /* HippyImageProviderProtocol.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyImageProviderProtocol.m; sourceTree = "<group>"; };
		B99771B8283616AA00D34173 /* HippyAnimatedImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyAnimatedImageView.h; sourceTree = "<group>"; };
		B99771B9283616AA00D34173 /* HippyImageCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyImageCache.h; sourceTree = "<group>"; };
		B99771BA283616AA00D34173 /* UIImageView+Hippy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+Hippy.m"; sourceTree = "<group>"; };
		B99771BB283616AA00D34173 /* HippyAnimatedImage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyAnimatedImage.m; sourceTree = "<group>"; };
		B99771BC283616AA00D34173 /* HippyDefaultImageProvider.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyDefaultImageProvider.m; sourceTree = "<group>"; };
		B99771BD283616AA00D34173 /* HippyImageProviderProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyImageProviderProtocol.h; sourceTree = "<group>"; };
		B99771BE283616AA00D34173 /* HippyAnimatedImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyAnimatedImageView.m; sourceTree = "<group>"; };
		B99771BF283616AA00D34173 /* HippyImageViewCustomLoader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyImageViewCustomLoader.h; sourceTree = "<group>"; };
		B99771C0283616AA00D34173 /* HippyImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyImageView.h; sourceTree = "<group>"; };
		B99771C1283616AA00D34173 /* HippyImageViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyImageViewManager.m; sourceTree = "<group>"; };
		B99771C2283616AA00D34173 /* HippyAnimatedImage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyAnimatedImage.h; sourceTree = "<group>"; };
		B99771C3283616AA00D34173 /* UIImageView+Hippy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+Hippy.h"; sourceTree = "<group>"; };
		B99771C4283616AA00D34173 /* HippyImageCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyImageCache.m; sourceTree = "<group>"; };
		B99771C6283616AA00D34173 /* HippyBaseListItemView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBaseListItemView.h; sourceTree = "<group>"; };
		B99771C7283616AA00D34173 /* HippyBaseListViewProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBaseListViewProtocol.h; sourceTree = "<group>"; };
		B99771C8283616AA00D34173 /* HippyBaseListItemViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyBaseListItemViewManager.m; sourceTree = "<group>"; };
		B99771C9283616AA00D34173 /* HippyBaseListViewDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyBaseListViewDataSource.m; sourceTree = "<group>"; };
		B99771CA283616AA00D34173 /* HippyListTableView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyListTableView.h; sourceTree = "<group>"; };
		B99771CB283616AA00D34173 /* HippyBaseListViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBaseListViewManager.h; sourceTree = "<group>"; };
		B99771CC283616AA00D34173 /* HippyBaseListView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBaseListView.h; sourceTree = "<group>"; };
		B99771CD283616AA00D34173 /* HippyBaseListViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBaseListViewCell.h; sourceTree = "<group>"; };
		B99771CE283616AA00D34173 /* HippyBaseListItemViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBaseListItemViewManager.h; sourceTree = "<group>"; };
		B99771CF283616AA00D34173 /* HippyBaseListItemView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyBaseListItemView.m; sourceTree = "<group>"; };
		B99771D0283616AA00D34173 /* HippyBaseListViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyBaseListViewManager.m; sourceTree = "<group>"; };
		B99771D1283616AA00D34173 /* HippyListTableView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyListTableView.m; sourceTree = "<group>"; };
		B99771D2283616AA00D34173 /* HippyBaseListViewDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBaseListViewDataSource.h; sourceTree = "<group>"; };
		B99771D3283616AA00D34173 /* HippyBaseListView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyBaseListView.m; sourceTree = "<group>"; };
		B99771D4283616AA00D34173 /* HippyBaseListViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyBaseListViewCell.m; sourceTree = "<group>"; };
		B99771D6283616AA00D34173 /* UIView+AppearEvent.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+AppearEvent.m"; sourceTree = "<group>"; };
		B99771D7283616AA00D34173 /* UIView+HippyAnimationProtocol.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+HippyAnimationProtocol.m"; sourceTree = "<group>"; };
		B99771D8283616AA00D34173 /* HippyView+HippyViewAnimation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "HippyView+HippyViewAnimation.m"; sourceTree = "<group>"; };
		B99771D9283616AA00D34173 /* HippyBackgroundImageCacheManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBackgroundImageCacheManager.h; sourceTree = "<group>"; };
		B99771DA283616AA00D34173 /* UIView+Hippy.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "UIView+Hippy.mm"; sourceTree = "<group>"; };
		B99771DB283616AA00D34173 /* HippyShadowView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyShadowView.h; sourceTree = "<group>"; };
		B99771DC283616AA00D34173 /* HippyPointerEvents.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyPointerEvents.h; sourceTree = "<group>"; };
		B99771DD283616AA00D34173 /* HippyViewManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyViewManager.mm; sourceTree = "<group>"; };
		B99771DE283616AA00D34173 /* HippyViewEventProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyViewEventProtocol.h; sourceTree = "<group>"; };
		B99771DF283616AA00D34173 /* HippyBorderStyle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBorderStyle.h; sourceTree = "<group>"; };
		B99771E0283616AA00D34173 /* HippyShadowView.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyShadowView.mm; sourceTree = "<group>"; };
		B99771E1283616AA00D34173 /* HippyView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyView.m; sourceTree = "<group>"; };
		B99771E2283616AA00D34173 /* HippyBorderDrawing.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyBorderDrawing.m; sourceTree = "<group>"; };
		B99771E3283616AA00D34173 /* HippyView+HippyViewAnimation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "HippyView+HippyViewAnimation.h"; sourceTree = "<group>"; };
		B99771E4283616AA00D34173 /* UIView+HippyAnimationProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+HippyAnimationProtocol.h"; sourceTree = "<group>"; };
		B99771E5283616AA00D34173 /* UIView+AppearEvent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+AppearEvent.h"; sourceTree = "<group>"; };
		B99771E6283616AA00D34173 /* HippyBackgroundImageCacheManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyBackgroundImageCacheManager.m; sourceTree = "<group>"; };
		B99771E7283616AA00D34173 /* HippyViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyViewManager.h; sourceTree = "<group>"; };
		B99771E8283616AA00D34173 /* UIView+Hippy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Hippy.h"; sourceTree = "<group>"; };
		B99771E9283616AA00D34173 /* UIView+Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Private.h"; sourceTree = "<group>"; };
		B99771EA283616AA00D34173 /* HippyBorderDrawing.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBorderDrawing.h; sourceTree = "<group>"; };
		B99771EB283616AA00D34173 /* HippyView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyView.h; sourceTree = "<group>"; };
		B99771ED283616AA00D34173 /* HippyNavigatorRootViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyNavigatorRootViewController.m; sourceTree = "<group>"; };
		B99771EE283616AA00D34173 /* HippyNavigatorItemViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyNavigatorItemViewController.m; sourceTree = "<group>"; };
		B99771EF283616AA00D34173 /* HippyNavigatorHostView.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyNavigatorHostView.mm; sourceTree = "<group>"; };
		B99771F0283616AA00D34173 /* HippyNavigationControllerAnimator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyNavigationControllerAnimator.m; sourceTree = "<group>"; };
		B99771F1283616AA00D34173 /* HippyNavigatorViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyNavigatorViewManager.h; sourceTree = "<group>"; };
		B99771F2283616AA00D34173 /* HippyNavigatorItemViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyNavigatorItemViewController.h; sourceTree = "<group>"; };
		B99771F3283616AA00D34173 /* HippyNavigatorRootViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyNavigatorRootViewController.h; sourceTree = "<group>"; };
		B99771F4283616AA00D34173 /* HippyNavigatorViewManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyNavigatorViewManager.m; sourceTree = "<group>"; };
		B99771F5283616AA00D34173 /* HippyNavigationControllerAnimator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyNavigationControllerAnimator.h; sourceTree = "<group>"; };
		B99771F6283616AA00D34173 /* HippyNavigatorHostView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyNavigatorHostView.h; sourceTree = "<group>"; };
		B99771F8283616AA00D34173 /* HippyFooterRefreshManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyFooterRefreshManager.m; sourceTree = "<group>"; };
		B99771F9283616AA00D34173 /* HippyFooterRefresh.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyFooterRefresh.m; sourceTree = "<group>"; };
		B99771FA283616AA00D34173 /* HippyFooterRefreshManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyFooterRefreshManager.h; sourceTree = "<group>"; };
		B99771FB283616AA00D34173 /* HippyFooterRefresh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyFooterRefresh.h; sourceTree = "<group>"; };
		B99771FD283616AA00D34173 /* HippyVirtualTextNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyVirtualTextNode.h; sourceTree = "<group>"; };
		B99771FE283616AA00D34173 /* HippyShadowText.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyShadowText.mm; sourceTree = "<group>"; };
		B99771FF283616AA00D34173 /* HippyShadowText.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyShadowText.h; sourceTree = "<group>"; };
		B9977200283616AA00D34173 /* HippyText.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyText.mm; sourceTree = "<group>"; };
		B9977201283616AA00D34173 /* HippyVirtualTextNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyVirtualTextNode.m; sourceTree = "<group>"; };
		B9977202283616AA00D34173 /* HippyText.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyText.h; sourceTree = "<group>"; };
		B9977203283616AA00D34173 /* HippyTextDecorationLineType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyTextDecorationLineType.h; sourceTree = "<group>"; };
		B9977204283616AA00D34173 /* HippyTextManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyTextManager.mm; sourceTree = "<group>"; };
		B9977205283616AA00D34173 /* HippyTextManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyTextManager.h; sourceTree = "<group>"; };
		B9977207283616AA00D34173 /* HippyModalCustomAnimationTransition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyModalCustomAnimationTransition.h; sourceTree = "<group>"; };
		B9977208283616AA00D34173 /* HippyModalCustomPresentationController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyModalCustomPresentationController.m; sourceTree = "<group>"; };
		B9977209283616AA00D34173 /* HippyModalHostViewManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyModalHostViewManager.mm; sourceTree = "<group>"; };
		B997720A283616AA00D34173 /* HippyModalTransitioningDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyModalTransitioningDelegate.mm; sourceTree = "<group>"; };
		B997720B283616AA00D34173 /* HippyModalHostViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyModalHostViewController.m; sourceTree = "<group>"; };
		B997720C283616AA00D34173 /* HippyModalHostView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyModalHostView.m; sourceTree = "<group>"; };
		B997720D283616AA00D34173 /* HippyModalHostViewInteractor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyModalHostViewInteractor.h; sourceTree = "<group>"; };
		B997720E283616AA00D34173 /* HippyModalCustomPresentationController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyModalCustomPresentationController.h; sourceTree = "<group>"; };
		B997720F283616AA00D34173 /* HippyModalCustomAnimationTransition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyModalCustomAnimationTransition.m; sourceTree = "<group>"; };
		B9977210283616AA00D34173 /* HippyModalHostViewManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyModalHostViewManager.h; sourceTree = "<group>"; };
		B9977211283616AA00D34173 /* HippyModalHostViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyModalHostViewController.h; sourceTree = "<group>"; };
		B9977212283616AA00D34173 /* HippyModalTransitioningDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyModalTransitioningDelegate.h; sourceTree = "<group>"; };
		B9977213283616AA00D34173 /* HippyModalHostView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyModalHostView.h; sourceTree = "<group>"; };
		B9977215283616AA00D34173 /* HippyParserUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyParserUtils.m; sourceTree = "<group>"; };
		B9977216283616AA00D34173 /* NSNumber+HippyNumberDeepCopy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSNumber+HippyNumberDeepCopy.h"; sourceTree = "<group>"; };
		B9977217283616AA00D34173 /* HippyI18nUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyI18nUtils.h; sourceTree = "<group>"; };
		B9977218283616AA00D34173 /* HippyGradientObject.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyGradientObject.m; sourceTree = "<group>"; };
		B9977219283616AA00D34173 /* HippyLog.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyLog.mm; sourceTree = "<group>"; };
		B997721A283616AA00D34173 /* HippyErrorCustomizer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyErrorCustomizer.h; sourceTree = "<group>"; };
		B997721B283616AA00D34173 /* HippyConvert+Transform.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "HippyConvert+Transform.m"; sourceTree = "<group>"; };
		B997721C283616AA00D34173 /* HippyErrorInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyErrorInfo.h; sourceTree = "<group>"; };
		B997721D283616AA00D34173 /* NSArray+HippyArrayDeepCopy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSArray+HippyArrayDeepCopy.m"; sourceTree = "<group>"; };
		B997721E283616AA00D34173 /* NSDictionary+HippyDictionaryDeepCopy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDictionary+HippyDictionaryDeepCopy.h"; sourceTree = "<group>"; };
		B997721F283616AA00D34173 /* HippyDefines.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDefines.h; sourceTree = "<group>"; };
		B9977220283616AA00D34173 /* NSData+DataType.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSData+DataType.m"; sourceTree = "<group>"; };
		B9977221283616AA00D34173 /* HippyUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyUtils.h; sourceTree = "<group>"; };
		B9977222283616AA00D34173 /* HippyAssert.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyAssert.m; sourceTree = "<group>"; };
		B9977223283616AA00D34173 /* HippyGradientObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyGradientObject.h; sourceTree = "<group>"; };
		B9977224283616AA00D34173 /* HippyI18nUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyI18nUtils.m; sourceTree = "<group>"; };
		B9977225283616AA00D34173 /* NSNumber+HippyNumberDeepCopy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSNumber+HippyNumberDeepCopy.m"; sourceTree = "<group>"; };
		B9977226283616AA00D34173 /* HippyParserUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyParserUtils.h; sourceTree = "<group>"; };
		B9977227283616AA00D34173 /* HippyConvert+Transform.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "HippyConvert+Transform.h"; sourceTree = "<group>"; };
		B9977228283616AA00D34173 /* HippyNullability.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyNullability.h; sourceTree = "<group>"; };
		B9977229283616AA00D34173 /* HippyConvert.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyConvert.mm; sourceTree = "<group>"; };
		B997722A283616AA00D34173 /* HippyConvert.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyConvert.h; sourceTree = "<group>"; };
		B997722B283616AA00D34173 /* NSArray+HippyArrayDeepCopy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSArray+HippyArrayDeepCopy.h"; sourceTree = "<group>"; };
		B997722C283616AA00D34173 /* HippyErrorInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyErrorInfo.m; sourceTree = "<group>"; };
		B997722D283616AA00D34173 /* HippyDeepCopyProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDeepCopyProtocol.h; sourceTree = "<group>"; };
		B997722E283616AA00D34173 /* HippyAssert.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyAssert.h; sourceTree = "<group>"; };
		B997722F283616AA00D34173 /* HippyUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyUtils.m; sourceTree = "<group>"; };
		B9977230283616AA00D34173 /* HippyLog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyLog.h; sourceTree = "<group>"; };
		B9977231283616AA00D34173 /* NSData+DataType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSData+DataType.h"; sourceTree = "<group>"; };
		B9977232283616AA00D34173 /* NSDictionary+HippyDictionaryDeepCopy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDictionary+HippyDictionaryDeepCopy.m"; sourceTree = "<group>"; };
		B9977235283616AA00D34173 /* HippyJSEnginesMapper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyJSEnginesMapper.h; sourceTree = "<group>"; };
		B9977236283616AA00D34173 /* HippyPerformanceLogger.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyPerformanceLogger.m; sourceTree = "<group>"; };
		B9977238283616AA00D34173 /* HippyVirtualList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyVirtualList.h; sourceTree = "<group>"; };
		B9977239283616AA00D34173 /* HippyVirtualNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyVirtualNode.m; sourceTree = "<group>"; };
		B997723A283616AA00D34173 /* HippyVirtualList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyVirtualList.m; sourceTree = "<group>"; };
		B997723B283616AA00D34173 /* HippyVirtualNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyVirtualNode.h; sourceTree = "<group>"; };
		B997723C283616AA00D34173 /* HippyComponentData.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyComponentData.mm; sourceTree = "<group>"; };
		B997723D283616AA00D34173 /* HippyTouchHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyTouchHandler.m; sourceTree = "<group>"; };
		B997723E283616AA00D34173 /* HippyComponent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyComponent.h; sourceTree = "<group>"; };
		B997723F283616AA00D34173 /* HippyAutoInsetsProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyAutoInsetsProtocol.h; sourceTree = "<group>"; };
		B9977240283616AA00D34173 /* HippyAnimationType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyAnimationType.h; sourceTree = "<group>"; };
		B9977241283616AA00D34173 /* HippyBridge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBridge.h; sourceTree = "<group>"; };
		B9977242283616AA00D34173 /* HippyJSStackFrame.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyJSStackFrame.m; sourceTree = "<group>"; };
		B9977243283616AA00D34173 /* HippyMethodInterceptorProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyMethodInterceptorProtocol.h; sourceTree = "<group>"; };
		B9977244283616AA00D34173 /* HippyEventDispatcher.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyEventDispatcher.m; sourceTree = "<group>"; };
		B9977245283616AA00D34173 /* HippyModuleMethod.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyModuleMethod.mm; sourceTree = "<group>"; };
		B9977246283616AA00D34173 /* HippyBridgeModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBridgeModule.h; sourceTree = "<group>"; };
		B9977247283616AA00D34173 /* HippyBridge+Mtt.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "HippyBridge+Mtt.h"; sourceTree = "<group>"; };
		B9977248283616AA00D34173 /* HippyKeyCommands.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyKeyCommands.h; sourceTree = "<group>"; };
		B9977249283616AA00D34173 /* HippyRootView.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyRootView.mm; sourceTree = "<group>"; };
		B997724A283616AA00D34173 /* HippyRootView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyRootView.h; sourceTree = "<group>"; };
		B997724B283616AA00D34173 /* HippyBridge+Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "HippyBridge+Private.h"; sourceTree = "<group>"; };
		B997724C283616AA00D34173 /* HippyFont.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyFont.h; sourceTree = "<group>"; };
		B997724D283616AA00D34173 /* HippyModuleMethod.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyModuleMethod.h; sourceTree = "<group>"; };
		B997724E283616AA00D34173 /* HippyModuleData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyModuleData.h; sourceTree = "<group>"; };
		B997724F283616AA00D34173 /* HippyBridge+LocalFileSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "HippyBridge+LocalFileSource.h"; sourceTree = "<group>"; };
		B9977250283616AA00D34173 /* HippyScrollProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyScrollProtocol.h; sourceTree = "<group>"; };
		B9977251283616AA00D34173 /* HippyDisplayLink.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDisplayLink.h; sourceTree = "<group>"; };
		B9977252283616AA00D34173 /* HippyFrameUpdate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyFrameUpdate.h; sourceTree = "<group>"; };
		B9977253283616AA00D34173 /* HippyDeviceBaseInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDeviceBaseInfo.h; sourceTree = "<group>"; };
		B9977254283616AA00D34173 /* HippyBundleURLProvider.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyBundleURLProvider.m; sourceTree = "<group>"; };
		B9977255283616AA00D34173 /* HippyTouchHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyTouchHandler.h; sourceTree = "<group>"; };
		B9977256283616AA00D34173 /* HippyJSEnginesMapper.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyJSEnginesMapper.mm; sourceTree = "<group>"; };
		B9977257283616AA00D34173 /* HippyRootShadowView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyRootShadowView.h; sourceTree = "<group>"; };
		B9977258283616AA00D34173 /* HippyJavaScriptLoader.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyJavaScriptLoader.mm; sourceTree = "<group>"; };
		B9977259283616AA00D34173 /* HippyPerformanceLogger.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyPerformanceLogger.h; sourceTree = "<group>"; };
		B997725A283616AA00D34173 /* HippyBatchedBridge.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyBatchedBridge.mm; sourceTree = "<group>"; };
		B997725B283616AA00D34173 /* HippyBridgeDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBridgeDelegate.h; sourceTree = "<group>"; };
		B997725C283616AA00D34173 /* HippyInvalidating.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyInvalidating.h; sourceTree = "<group>"; };
		B997725E283616AA00D34173 /* HippyJSCExecutor.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyJSCExecutor.mm; sourceTree = "<group>"; };
		B997725F283616AA00D34173 /* HippyJSCErrorHandling.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyJSCErrorHandling.m; sourceTree = "<group>"; };
		B9977260283616AA00D34173 /* HippyJSCWrapper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyJSCWrapper.h; sourceTree = "<group>"; };
		B9977261283616AA00D34173 /* HippyJSCExecutor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyJSCExecutor.h; sourceTree = "<group>"; };
		B9977262283616AA00D34173 /* HippyJSCErrorHandling.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyJSCErrorHandling.h; sourceTree = "<group>"; };
		B9977263283616AA00D34173 /* HippyJSCWrapper.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyJSCWrapper.mm; sourceTree = "<group>"; };
		B9977264283616AA00D34173 /* HippyKeyCommands.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyKeyCommands.m; sourceTree = "<group>"; };
		B9977265283616AA00D34173 /* HippyFont.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyFont.mm; sourceTree = "<group>"; };
		B9977266283616AA00D34173 /* HippyEventDispatcher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyEventDispatcher.h; sourceTree = "<group>"; };
		B9977267283616AA00D34173 /* HippyBridgeMethod.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBridgeMethod.h; sourceTree = "<group>"; };
		B9977268283616AA00D34173 /* HippyJSStackFrame.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyJSStackFrame.h; sourceTree = "<group>"; };
		B9977269283616AA00D34173 /* HippyBridge+LocalFileSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "HippyBridge+LocalFileSource.m"; sourceTree = "<group>"; };
		B997726A283616AA00D34173 /* HippyJavaScriptExecutor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyJavaScriptExecutor.h; sourceTree = "<group>"; };
		B997726B283616AA00D34173 /* HippyBridge.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyBridge.mm; sourceTree = "<group>"; };
		B997726C283616AA00D34173 /* HippyJavaScriptLoader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyJavaScriptLoader.h; sourceTree = "<group>"; };
		B997726D283616AA00D34173 /* HippyMemoryOpt.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyMemoryOpt.h; sourceTree = "<group>"; };
		B997726E283616AA00D34173 /* HippyDeviceBaseInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyDeviceBaseInfo.m; sourceTree = "<group>"; };
		B997726F283616AA00D34173 /* HippyBundleURLProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyBundleURLProvider.h; sourceTree = "<group>"; };
		B9977270283616AA00D34173 /* HippyComponentData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyComponentData.h; sourceTree = "<group>"; };
		B9977271283616AA00D34173 /* HippyRootViewDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyRootViewDelegate.h; sourceTree = "<group>"; };
		B9977272283616AA00D34173 /* HippyModuleData.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyModuleData.mm; sourceTree = "<group>"; };
		B9977273283616AA00D34173 /* HippyBridge+Mtt.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = "HippyBridge+Mtt.mm"; sourceTree = "<group>"; };
		B9977274283616AA00D34173 /* HippyFrameUpdate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyFrameUpdate.m; sourceTree = "<group>"; };
		B9977275283616AA00D34173 /* HippyDisplayLink.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyDisplayLink.m; sourceTree = "<group>"; };
		B9977276283616AA00D34173 /* HippyRootViewInternal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyRootViewInternal.h; sourceTree = "<group>"; };
		B9977277283616AA00D34173 /* HippyRootShadowView.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = HippyRootShadowView.mm; sourceTree = "<group>"; };
		B997727A283616AA00D34173 /* HippyWebSocketProxyDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyWebSocketProxyDelegate.h; sourceTree = "<group>"; };
		B997727B283616AA00D34173 /* HippySRSIMDHelpers.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippySRSIMDHelpers.h; sourceTree = "<group>"; };
		B997727C283616AA00D34173 /* HippyWebSocketManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyWebSocketManager.h; sourceTree = "<group>"; };
		B997727D283616AA00D34173 /* HippyWebSocketProxy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyWebSocketProxy.h; sourceTree = "<group>"; };
		B997727E283616AA00D34173 /* HippySRWebSocket.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippySRWebSocket.m; sourceTree = "<group>"; };
		B997727F283616AA00D34173 /* HippySRSIMDHelpers.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippySRSIMDHelpers.m; sourceTree = "<group>"; };
		B9977280283616AA00D34173 /* HippyWebSocketManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyWebSocketManager.m; sourceTree = "<group>"; };
		B9977281283616AA00D34173 /* HippySRWebSocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippySRWebSocket.h; sourceTree = "<group>"; };
		B9977283283616AA00D34173 /* HippyDevWebSocketClient.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDevWebSocketClient.h; sourceTree = "<group>"; };
		B9977284283616AA00D34173 /* HippyDevInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDevInfo.h; sourceTree = "<group>"; };
		B9977286283616AA00D34173 /* HippyInspector.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyInspector.h; sourceTree = "<group>"; };
		B9977287283616AA00D34173 /* HippyDevCommand.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDevCommand.h; sourceTree = "<group>"; };
		B9977289283616AA00D34173 /* HippyCSSModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyCSSModel.h; sourceTree = "<group>"; };
		B997728A283616AA00D34173 /* HippyPageModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyPageModel.h; sourceTree = "<group>"; };
		B997728B283616AA00D34173 /* HippyDomModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDomModel.h; sourceTree = "<group>"; };
		B997728C283616AA00D34173 /* HippyCSSPropsDefine.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyCSSPropsDefine.m; sourceTree = "<group>"; };
		B997728D283616AA00D34173 /* HippyPageModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyPageModel.m; sourceTree = "<group>"; };
		B997728E283616AA00D34173 /* HippyCSSModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyCSSModel.m; sourceTree = "<group>"; };
		B997728F283616AA00D34173 /* HippyCSSPropsDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyCSSPropsDefine.h; sourceTree = "<group>"; };
		B9977290283616AA00D34173 /* HippyDomModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyDomModel.m; sourceTree = "<group>"; };
		B9977292283616AA00D34173 /* HippyPageDomain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyPageDomain.m; sourceTree = "<group>"; };
		B9977293283616AA00D34173 /* HippyInspectorDomain.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyInspectorDomain.h; sourceTree = "<group>"; };
		B9977294283616AA00D34173 /* HippyCSSDomain.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyCSSDomain.h; sourceTree = "<group>"; };
		B9977295283616AA00D34173 /* HippyDomDomain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyDomDomain.m; sourceTree = "<group>"; };
		B9977296283616AA00D34173 /* HippyPageDomain.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyPageDomain.h; sourceTree = "<group>"; };
		B9977297283616AA00D34173 /* HippyDomDomain.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDomDomain.h; sourceTree = "<group>"; };
		B9977298283616AA00D34173 /* HippyCSSDomain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyCSSDomain.m; sourceTree = "<group>"; };
		B9977299283616AA00D34173 /* HippyInspectorDomain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyInspectorDomain.m; sourceTree = "<group>"; };
		B997729A283616AA00D34173 /* HippyDevCommand.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyDevCommand.m; sourceTree = "<group>"; };
		B997729B283616AA00D34173 /* HippyInspector.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyInspector.m; sourceTree = "<group>"; };
		B997729C283616AA00D34173 /* HippyDevManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyDevManager.m; sourceTree = "<group>"; };
		B997729D283616AA00D34173 /* HippyDevInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyDevInfo.m; sourceTree = "<group>"; };
		B997729E283616AA00D34173 /* HippyDevWebSocketClient.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HippyDevWebSocketClient.m; sourceTree = "<group>"; };
		B997729F283616AA00D34173 /* HippyDevManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HippyDevManager.h; sourceTree = "<group>"; };
		B997733C2836268300D34173 /* WillPopListener.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WillPopListener.m; sourceTree = "<group>"; };
		B9ABD8CF283B1C1A00504F28 /* IQUIView+IQKeyboardToolbar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIView+IQKeyboardToolbar.h"; sourceTree = "<group>"; };
		B9ABD8D0283B1C1A00504F28 /* IQToolbar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQToolbar.m; sourceTree = "<group>"; };
		B9ABD8D1283B1C1A00504F28 /* IQPreviousNextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQPreviousNextView.h; sourceTree = "<group>"; };
		B9ABD8D2283B1C1A00504F28 /* IQTitleBarButtonItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQTitleBarButtonItem.m; sourceTree = "<group>"; };
		B9ABD8D3283B1C1A00504F28 /* IQBarButtonItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQBarButtonItem.m; sourceTree = "<group>"; };
		B9ABD8D4283B1C1A00504F28 /* IQUIView+IQKeyboardToolbar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIView+IQKeyboardToolbar.m"; sourceTree = "<group>"; };
		B9ABD8D5283B1C1A00504F28 /* IQPreviousNextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQPreviousNextView.m; sourceTree = "<group>"; };
		B9ABD8D6283B1C1A00504F28 /* IQToolbar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQToolbar.h; sourceTree = "<group>"; };
		B9ABD8D7283B1C1A00504F28 /* IQBarButtonItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQBarButtonItem.h; sourceTree = "<group>"; };
		B9ABD8D8283B1C1A00504F28 /* IQTitleBarButtonItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQTitleBarButtonItem.h; sourceTree = "<group>"; };
		B9ABD8D9283B1C1A00504F28 /* IQKeyboardReturnKeyHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQKeyboardReturnKeyHandler.m; sourceTree = "<group>"; };
		B9ABD8DB283B1C1A00504F28 /* IQKeyboardManagerConstantsInternal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardManagerConstantsInternal.h; sourceTree = "<group>"; };
		B9ABD8DC283B1C1A00504F28 /* IQKeyboardManagerConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardManagerConstants.h; sourceTree = "<group>"; };
		B9ABD8DD283B1C1A00504F28 /* IQKeyboardManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQKeyboardManager.m; sourceTree = "<group>"; };
		B9ABD8DF283B1C1B00504F28 /* IQNSArray+Sort.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQNSArray+Sort.m"; sourceTree = "<group>"; };
		B9ABD8E0283B1C1B00504F28 /* IQUIViewController+Additions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIViewController+Additions.h"; sourceTree = "<group>"; };
		B9ABD8E1283B1C1B00504F28 /* IQUITextFieldView+Additions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUITextFieldView+Additions.m"; sourceTree = "<group>"; };
		B9ABD8E2283B1C1B00504F28 /* IQUIScrollView+Additions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIScrollView+Additions.m"; sourceTree = "<group>"; };
		B9ABD8E3283B1C1B00504F28 /* IQUIView+Hierarchy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIView+Hierarchy.m"; sourceTree = "<group>"; };
		B9ABD8E4283B1C1B00504F28 /* IQNSArray+Sort.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQNSArray+Sort.h"; sourceTree = "<group>"; };
		B9ABD8E5283B1C1B00504F28 /* IQUIScrollView+Additions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIScrollView+Additions.h"; sourceTree = "<group>"; };
		B9ABD8E6283B1C1B00504F28 /* IQUITextFieldView+Additions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUITextFieldView+Additions.h"; sourceTree = "<group>"; };
		B9ABD8E7283B1C1B00504F28 /* IQUIViewController+Additions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "IQUIViewController+Additions.m"; sourceTree = "<group>"; };
		B9ABD8E8283B1C1B00504F28 /* IQUIView+Hierarchy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IQUIView+Hierarchy.h"; sourceTree = "<group>"; };
		B9ABD8E9283B1C1B00504F28 /* IQKeyboardReturnKeyHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardReturnKeyHandler.h; sourceTree = "<group>"; };
		B9ABD8EA283B1C1B00504F28 /* IQKeyboardManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQKeyboardManager.h; sourceTree = "<group>"; };
		B9ABD8EC283B1C1B00504F28 /* IQTextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IQTextView.m; sourceTree = "<group>"; };
		B9ABD8ED283B1C1B00504F28 /* IQTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IQTextView.h; sourceTree = "<group>"; };
		F0D9DBE547833A806402BC6D /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				666409502521DFE50024188E /* libc++.tbd in Frameworks */,
				6664094E2521DFDB0024188E /* CFNetwork.framework in Frameworks */,
				6664094C2521DFD60024188E /* CoreData.framework in Frameworks */,
				6664094A2521DFD00024188E /* libz.tbd in Frameworks */,
				666409482521DFC20024188E /* UserNotifications.framework in Frameworks */,
				666409462521DFB70024188E /* SystemConfiguration.framework in Frameworks */,
				666409442521DFA10024188E /* CoreTelephony.framework in Frameworks */,
				01245FDC083EF9A29D5422E6 /* Pods_Runner.framework in Frameworks */,
				6664093E2521DF720024188E /* libXG-SDK-Cloud.a in Frameworks */,
				666409412521DF7E0024188E /* XGMTACloud.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0E0E6303618A65B9B8263CFB /* Pods */ = {
			isa = PBXGroup;
			children = (
				41AC21A3B6B5805CB6B80A8C /* Pods-Runner.debug.xcconfig */,
				2E56DFACB575C483B1265A4E /* Pods-Runner.release.xcconfig */,
				A18C85A7F05F610A7BC5E5ED /* Pods-Runner.profile.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		112E28C7245C6D0100A5DF37 /* audio */ = {
			isa = PBXGroup;
			children = (
				112E28CA245C6D0100A5DF37 /* AudioView.h */,
				112E28C8245C6D0100A5DF37 /* AudioView.m */,
				112E28CB245C6D0100A5DF37 /* AudioViewManager.h */,
				112E28C9245C6D0100A5DF37 /* AudioViewManager.m */,
				6690F68C24F632F200F91E26 /* AudioPlayerMananger.h */,
				6690F68D24F632F200F91E26 /* AudioPlayerMananger.m */,
			);
			path = audio;
			sourceTree = "<group>";
		};
		112E2909245CFF0800A5DF37 /* auido */ = {
			isa = PBXGroup;
			children = (
				112E291E245D0C4E00A5DF37 /* AudioPlayer.h */,
				112E291F245D0C4E00A5DF37 /* AudioPlayer.m */,
			);
			path = auido;
			sourceTree = "<group>";
		};
		116CAF7B24DE569600DB4C50 /* permissions */ = {
			isa = PBXGroup;
			children = (
				116CAF9D24DE56CC00DB4C50 /* PermissionHandlerModule.h */,
				116CAF8224DE56CC00DB4C50 /* PermissionHandlerModule.m */,
				116CAF7F24DE56CC00DB4C50 /* PermissionHandlerEnums.h */,
				116CAF8124DE56CC00DB4C50 /* PermissionManager.h */,
				116CAF8024DE56CC00DB4C50 /* PermissionManager.m */,
				116CAF8324DE56CC00DB4C50 /* strategies */,
				116CAF7C24DE56CC00DB4C50 /* util */,
			);
			path = permissions;
			sourceTree = "<group>";
		};
		116CAF7C24DE56CC00DB4C50 /* util */ = {
			isa = PBXGroup;
			children = (
				116CAF7D24DE56CC00DB4C50 /* Codec.m */,
				116CAF7E24DE56CC00DB4C50 /* Codec.h */,
			);
			path = util;
			sourceTree = "<group>";
		};
		116CAF8324DE56CC00DB4C50 /* strategies */ = {
			isa = PBXGroup;
			children = (
				116CAF8424DE56CC00DB4C50 /* MediaLibraryPermissionStrategy.m */,
				116CAF8524DE56CC00DB4C50 /* LocationPermissionStrategy.m */,
				116CAF8624DE56CC00DB4C50 /* EventPermissionStrategy.m */,
				116CAF8724DE56CC00DB4C50 /* PhotoPermissionStrategy.h */,
				116CAF8824DE56CC00DB4C50 /* StoragePermissionStrategy.m */,
				116CAF8924DE56CC00DB4C50 /* ContactPermissionStrategy.h */,
				116CAF8A24DE56CC00DB4C50 /* SensorPermissionStrategy.m */,
				116CAF8B24DE56CC00DB4C50 /* NotificationPermissionStrategy.h */,
				116CAF8C24DE56CC00DB4C50 /* SpeechPermissionStrategy.m */,
				116CAF8D24DE56CC00DB4C50 /* UnknownPermissionStrategy.h */,
				116CAF8E24DE56CC00DB4C50 /* AudioVideoPermissionStrategy.h */,
				116CAF8F24DE56CC00DB4C50 /* PhonePermissionStrategy.h */,
				116CAF9024DE56CC00DB4C50 /* PermissionStrategy.h */,
				116CAF9124DE56CC00DB4C50 /* MediaLibraryPermissionStrategy.h */,
				116CAF9224DE56CC00DB4C50 /* LocationPermissionStrategy.h */,
				116CAF9324DE56CC00DB4C50 /* StoragePermissionStrategy.h */,
				116CAF9424DE56CC00DB4C50 /* PhotoPermissionStrategy.m */,
				116CAF9524DE56CC00DB4C50 /* EventPermissionStrategy.h */,
				116CAF9624DE56CC00DB4C50 /* PhonePermissionStrategy.m */,
				116CAF9724DE56CC00DB4C50 /* AudioVideoPermissionStrategy.m */,
				116CAF9824DE56CC00DB4C50 /* UnknownPermissionStrategy.m */,
				116CAF9924DE56CC00DB4C50 /* NotificationPermissionStrategy.m */,
				116CAF9A24DE56CC00DB4C50 /* SpeechPermissionStrategy.h */,
				116CAF9B24DE56CC00DB4C50 /* SensorPermissionStrategy.h */,
				116CAF9C24DE56CC00DB4C50 /* ContactPermissionStrategy.m */,
			);
			path = strategies;
			sourceTree = "<group>";
		};
		117A558324DECCC200E424CA /* wx-api */ = {
			isa = PBXGroup;
			children = (
				667A64B924B725700090382A /* WxApiHostModule.h */,
				667A64BA24B725700090382A /* WxApiHostModule.m */,
				B19A5CF021CF85E1005E6B11 /* FluwxResponseHandler.h */,
				B19A5CFC21CF85EF005E6B11 /* FluwxResponseHandler.m */,
			);
			path = "wx-api";
			sourceTree = "<group>";
		};
		117A558424DED09700E424CA /* mix-method */ = {
			isa = PBXGroup;
			children = (
				66137F8724DA382D00E44F63 /* url-lanucher */,
				664BC56224D3C57400825CF5 /* HippyMixInvokeMethodModule.h */,
				664BC56124D3C57400825CF5 /* HippyMixInvokeMethodModule.m */,
			);
			path = "mix-method";
			sourceTree = "<group>";
		};
		118E722B28F2C27B0079006F /* svgview */ = {
			isa = PBXGroup;
			children = (
				66BF71DA28F54B0A00EE457A /* SVGManager.h */,
				66BF71DB28F54B0A00EE457A /* SVGManager.m */,
				118E722C28F2C27B0079006F /* SVGView.m */,
				118E722D28F2C27B0079006F /* SVGViewManager.h */,
				118E722E28F2C27B0079006F /* SVGViewManager.m */,
				118E722F28F2C27B0079006F /* SVGView.h */,
			);
			path = svgview;
			sourceTree = "<group>";
		};
		118F0C7E24E811D900EAAD89 /* share */ = {
			isa = PBXGroup;
			children = (
				118F0C7F24E8121100EAAD89 /* ShareModule.h */,
				118F0C8024E8121100EAAD89 /* ShareModule.m */,
			);
			path = share;
			sourceTree = "<group>";
		};
		1192AE68241CD4E0007D8797 /* SubProject */ = {
			isa = PBXGroup;
			children = (
				66137F8224D9836000E44F63 /* appinfo */,
				66137F7E24D9807500E44F63 /* plugins */,
				11EA45DF24D858F4009BD26E /* prefs */,
				66C17D1E2435BC880009D385 /* common */,
				667A64BF24B746200090382A /* wx-api */,
				6632F0D424CEC14A00B1BC5C /* IQKeyboardManager */,
				1192AE69241CD4E0007D8797 /* hippy */,
			);
			path = SubProject;
			sourceTree = "<group>";
		};
		1192AE69241CD4E0007D8797 /* hippy */ = {
			isa = PBXGroup;
			children = (
				1192AE77241CD729007D8797 /* engine */,
				66C17D2A2435DD960009D385 /* modules */,
				66C17CAB2435B2B60009D385 /* views */,
				1192AE74241CD5E8007D8797 /* HippyViewController.h */,
				1192AE75241CD5E8007D8797 /* HippyViewController.m */,
				66C17D272435D43B0009D385 /* DebugView.h */,
				66C17D282435D43B0009D385 /* DebugView.m */,
				11D552C8243625280049D7ED /* HostHippyMessageBridge.h */,
				11D552C9243625280049D7ED /* HostHippyMessageBridge.m */,
				66137FA124DBA5D600E44F63 /* HippyMethodInvoideCallback.h */,
			);
			path = hippy;
			sourceTree = "<group>";
		};
		1192AE77241CD729007D8797 /* engine */ = {
			isa = PBXGroup;
			children = (
				B997711C283616AA00D34173 /* sdk */,
				B99770442836169900D34173 /* core */,
			);
			path = engine;
			sourceTree = "<group>";
		};
		11D552B9243618DE0049D7ED /* transition */ = {
			isa = PBXGroup;
			children = (
				11D552BC243618DE0049D7ED /* ABCBaseAnimation.h */,
				11D552BA243618DE0049D7ED /* ABCBaseAnimation.m */,
				11D552BB243618DE0049D7ED /* ABCBaseInteraction.h */,
				11D552BD243618DE0049D7ED /* ABCBaseInteraction.m */,
			);
			path = transition;
			sourceTree = "<group>";
		};
		11D552C0243619DA0049D7ED /* utils */ = {
			isa = PBXGroup;
			children = (
				11D552C1243619F30049D7ED /* ABCCGRectUtil.h */,
			);
			path = utils;
			sourceTree = "<group>";
		};
		11DA63F92445FBCB00DBA6C6 /* wheel */ = {
			isa = PBXGroup;
			children = (
				11DA64072445FCE500DBA6C6 /* WheelView.h */,
				11DA640B2445FCE500DBA6C6 /* WheelView.m */,
				11DA640D2445FCE500DBA6C6 /* WheelViewManager.h */,
				11DA64062445FCE500DBA6C6 /* WheelViewManager.m */,
			);
			path = wheel;
			sourceTree = "<group>";
		};
		11EA45DF24D858F4009BD26E /* prefs */ = {
			isa = PBXGroup;
			children = (
				11EA45E024D85918009BD26E /* Preferences.h */,
			);
			path = prefs;
			sourceTree = "<group>";
		};
		660081342481F1DC00761AE3 /* will-pop-listener */ = {
			isa = PBXGroup;
			children = (
				B997733C2836268300D34173 /* WillPopListener.m */,
				6600813E2481F22700761AE3 /* WillPopListener.h */,
				660081452481F3C000761AE3 /* WillPopListenerManager.h */,
				660081442481F3C000761AE3 /* WillPopListenerManager.m */,
			);
			path = "will-pop-listener";
			sourceTree = "<group>";
		};
		6608379324E4D90200530B70 /* stat-manager */ = {
			isa = PBXGroup;
			children = (
				6608379524E4DA0400530B70 /* StatManagerModule.h */,
				6608379424E4DA0400530B70 /* StatManagerModule.m */,
			);
			path = "stat-manager";
			sourceTree = "<group>";
		};
		6608379724E4DE0600530B70 /* bugly */ = {
			isa = PBXGroup;
			children = (
				6608379824E4DEC100530B70 /* BuglyModule.h */,
				6608379924E4DEC100530B70 /* BuglyModule.m */,
			);
			path = bugly;
			sourceTree = "<group>";
		};
		660929AC256F3F6800436093 /* url-launcher */ = {
			isa = PBXGroup;
			children = (
				660929AD256F3FDC00436093 /* UrlLauncherModule.h */,
				660929AE256F3FDC00436093 /* UrlLauncherModule.m */,
			);
			path = "url-launcher";
			sourceTree = "<group>";
		};
		66137EFD24D8E9C900E44F63 /* abc-network */ = {
			isa = PBXGroup;
			children = (
				7DFEBCD12E12A6580024EE75 /* AbcSseConnection.h */,
				7DFEBCD22E12A6580024EE75 /* AbcSseConnection.m */,
				7DFEBCD32E12A6580024EE75 /* AbcSseEvent.h */,
				7DFEBCD42E12A6580024EE75 /* AbcSseEvent.m */,
				66137F0124D8E9C900E44F63 /* AbcNetwork.h */,
				66137EFF24D8E9C900E44F63 /* AbcNetwork.m */,
				66137F0024D8E9C900E44F63 /* AbcFetchInfo.h */,
				66137EFE24D8E9C900E44F63 /* AbcFetchInfo.m */,
			);
			path = "abc-network";
			sourceTree = "<group>";
		};
		66137F0424D94B3F00E44F63 /* file */ = {
			isa = PBXGroup;
			children = (
				66137F0524D94B6300E44F63 /* FileUtils.h */,
				66137F0624D94B6300E44F63 /* FileUtils.m */,
			);
			path = file;
			sourceTree = "<group>";
		};
		66137F7E24D9807500E44F63 /* plugins */ = {
			isa = PBXGroup;
			children = (
				66137F7F24D9808F00E44F63 /* PluginUtils.h */,
				66137F8024D9808F00E44F63 /* PluginUtils.m */,
			);
			path = plugins;
			sourceTree = "<group>";
		};
		66137F8224D9836000E44F63 /* appinfo */ = {
			isa = PBXGroup;
			children = (
				66137F8324D9836E00E44F63 /* AppInfo.h */,
				66137F8424D9836E00E44F63 /* AppInfo.m */,
			);
			path = appinfo;
			sourceTree = "<group>";
		};
		66137F8724DA382D00E44F63 /* url-lanucher */ = {
			isa = PBXGroup;
			children = (
				66137F8824DA382D00E44F63 /* UrlLanucherUtils.h */,
				66137F8924DA382D00E44F63 /* UrlLanucherUtils.m */,
			);
			path = "url-lanucher";
			sourceTree = "<group>";
		};
		66137F8B24DA811000E44F63 /* socket-io */ = {
			isa = PBXGroup;
			children = (
				66137F8C24DA819C00E44F63 /* SocketIOModule.h */,
				66137F8D24DA819C00E44F63 /* SocketIOModule.m */,
			);
			path = "socket-io";
			sourceTree = "<group>";
		};
		66137F9D24DBA03300E44F63 /* local-notification */ = {
			isa = PBXGroup;
			children = (
				66137FA624DBA9C900E44F63 /* LocalNotificationModule.h */,
				66137FAA24DBA9C900E44F63 /* LocalNotificationModule.m */,
				66137FA724DBA9C900E44F63 /* NotificationAttachment.h */,
				66137FA424DBA9C900E44F63 /* NotificationAttachment.m */,
				66137FAB24DBA9C900E44F63 /* NotificationDetails.h */,
				66137FA824DBA9C900E44F63 /* NotificationDetails.m */,
				66137FA924DBA9C900E44F63 /* NotificationTime.h */,
				66137FA524DBA9C900E44F63 /* NotificationTime.m */,
			);
			path = "local-notification";
			sourceTree = "<group>";
		};
		66137FB024DBB07300E44F63 /* push */ = {
			isa = PBXGroup;
			children = (
				66137FC124DBF16700E44F63 /* XGPushModule.h */,
				66137FC024DBF16700E44F63 /* XGPushModule.m */,
			);
			path = push;
			sourceTree = "<group>";
		};
		662AB36724DCE28400596BB6 /* oss */ = {
			isa = PBXGroup;
			children = (
				662AB36C24DCE57F00596BB6 /* OssFileUploader.h */,
				662AB36B24DCE57F00596BB6 /* OssFileUploader.mm */,
				662AB36E24DCE57F00596BB6 /* OssManager.h */,
				662AB36D24DCE57F00596BB6 /* OssManager.mm */,
				662AB36924DCE30F00596BB6 /* OSSModule.h */,
				662AB36824DCE30F00596BB6 /* OSSModule.m */,
			);
			path = oss;
			sourceTree = "<group>";
		};
		6632F0D424CEC14A00B1BC5C /* IQKeyboardManager */ = {
			isa = PBXGroup;
			children = (
				6632F0D524CEC14A00B1BC5C /* LICENSE.md */,
				6632F0D624CEC14A00B1BC5C /* README.md */,
				6632F0D724CEC14A00B1BC5C /* IQKeyboardManager */,
			);
			path = IQKeyboardManager;
			sourceTree = "<group>";
		};
		6632F0D724CEC14A00B1BC5C /* IQKeyboardManager */ = {
			isa = PBXGroup;
			children = (
				B9ABD8DE283B1C1B00504F28 /* Categories */,
				B9ABD8DA283B1C1A00504F28 /* Constants */,
				B9ABD8EA283B1C1B00504F28 /* IQKeyboardManager.h */,
				B9ABD8DD283B1C1A00504F28 /* IQKeyboardManager.m */,
				B9ABD8E9283B1C1B00504F28 /* IQKeyboardReturnKeyHandler.h */,
				B9ABD8D9283B1C1A00504F28 /* IQKeyboardReturnKeyHandler.m */,
				B9ABD8EB283B1C1B00504F28 /* IQTextView */,
				B9ABD8CE283B1C1A00504F28 /* IQToolbar */,
			);
			path = IQKeyboardManager;
			sourceTree = "<group>";
		};
		6643497524D297A500137935 /* charts */ = {
			isa = PBXGroup;
			children = (
				6643498424D2C23D00137935 /* DateValueFormatter.h */,
				6643498324D2C23D00137935 /* DateValueFormatter.m */,
				6643497C24D29C6400137935 /* AbcLineChartView.h */,
				6643497F24D29C6400137935 /* AbcLineChartView.m */,
				6643497E24D29C6400137935 /* AbcLineChartViewManager.h */,
				6643497D24D29C6400137935 /* AbcLineChartViewManager.m */,
				35B64AD02689F97300CF0190 /* AbcPieChartView.h */,
				35B64AD32689F9EA00CF0190 /* AbcPieChartView.m */,
				35B64AD52689FA0800CF0190 /* AbcPieChartViewManager.h */,
				35B64AD12689F9C000CF0190 /* AbcPieChartViewManager.m */,
			);
			path = charts;
			sourceTree = "<group>";
		};
		664AE9372435FEE00063FCC2 /* ui */ = {
			isa = PBXGroup;
			children = (
				664AE984243600190063FCC2 /* framework */,
				664AE9392435FEE00063FCC2 /* animation */,
				664AE93F2435FEE00063FCC2 /* UIUtils.h */,
				664AE9412435FEE00063FCC2 /* UIUtils.m */,
				660081472481F4C400761AE3 /* UIView+ABCExt.h */,
				660081482481F4C400761AE3 /* UIView+ABCExt.m */,
			);
			path = ui;
			sourceTree = "<group>";
		};
		664AE9392435FEE00063FCC2 /* animation */ = {
			isa = PBXGroup;
			children = (
				11D552B9243618DE0049D7ED /* transition */,
				11B49486243997F80009856A /* ABCViewAnimationController.h */,
				11B49485243997F80009856A /* ABCViewAnimationController.m */,
				11B49488243998160009856A /* ABCViewControllerAnimationController.h */,
				11B49489243998160009856A /* ABCViewControllerAnimationController.m */,
				11D552B6243618740049D7ED /* ABCAnimationCenter.h */,
				11D552B72436187B0049D7ED /* ABCAnimationCenter.m */,
				664AE9882436010A0063FCC2 /* easing.c */,
				664AE9892436010A0063FCC2 /* easing.h */,
				664AE93A2435FEE00063FCC2 /* ABCAnimationDefine.h */,
				664AE93B2435FEE00063FCC2 /* CAKeyframeAnimation+AHEasing.h */,
				664AE93D2435FEE00063FCC2 /* CAKeyframeAnimation+AHEasing.m */,
				664AE93C2435FEE00063FCC2 /* UIView+ABCAnimation.h */,
				664AE93E2435FEE00063FCC2 /* UIView+ABCAnimation.m */,
			);
			path = animation;
			sourceTree = "<group>";
		};
		664AE984243600190063FCC2 /* framework */ = {
			isa = PBXGroup;
			children = (
				11D552C3243622D10049D7ED /* ABCBaseNavigationController.h */,
				11D552C5243623060049D7ED /* ABCBaseNavigationController.m */,
				664AE9862436003E0063FCC2 /* ABCBaseViewController.h */,
				664AE9852436003E0063FCC2 /* ABCBaseViewController.m */,
				66137FBB24DBDF3200E44F63 /* AbcBaseAppDelegate.h */,
				66137FBE24DBE04000E44F63 /* AbcBaseAppDelegate.m */,
			);
			path = framework;
			sourceTree = "<group>";
		};
		664BC56424D3C92100825CF5 /* barcode_scan */ = {
			isa = PBXGroup;
			children = (
				117A558524DED67700E424CA /* BarcodeScanModule.h */,
				117A558624DED67700E424CA /* BarcodeScanModule.m */,
				6610A83124D3DE41006D24A0 /* BarcodeScanManager.h */,
				6610A83224D3DE41006D24A0 /* BarcodeScanManager.m */,
				664BC56724D3C92100825CF5 /* UIButton+TouchArea.h */,
				664BC56924D3C92100825CF5 /* TestFontViewController.h */,
				664BC56624D3C92100825CF5 /* ScannerOverlay.h */,
				664BC56C24D3C92100825CF5 /* ScannerOverlay.m */,
				664BC56B24D3C92100825CF5 /* BarcodeScannerViewControllerDelegate.h */,
				664BC56524D3C92100825CF5 /* BarcodeScannerViewController.h */,
				664BC56D24D3C92100825CF5 /* BarcodeScannerViewController.m */,
				664BC57024D3C92100825CF5 /* UIButton+TouchArea.m */,
				664BC57124D3C92100825CF5 /* TestFontViewController.m */,
			);
			path = barcode_scan;
			sourceTree = "<group>";
		};
		665A72DC2468F60E008C6C61 /* abc-textinput */ = {
			isa = PBXGroup;
			children = (
				6601D12324AC41D300BC67C0 /* AbcCustomKeyboardView.h */,
				6601D12224AC41D300BC67C0 /* AbcCustomKeyboardView.m */,
				6601D12024AC417500BC67C0 /* AbcCustomKeyboardViewManager.h */,
				6601D11F24AC417500BC67C0 /* AbcCustomKeyboardViewManager.mm */,
				665A7304246901AA008C6C61 /* AbcInputAccessoryView.h */,
				665A7301246901AA008C6C61 /* AbcInputAccessoryView.m */,
				665A7302246901AA008C6C61 /* AbcInputAccessoryViewManager.h */,
				665A7303246901AA008C6C61 /* AbcInputAccessoryViewManager.mm */,
				665A72F62468F7B3008C6C61 /* AbcHippyBaseTextInput.h */,
				665A72F22468F7B3008C6C61 /* AbcHippyBaseTextInput.m */,
				665A72F02468F7B3008C6C61 /* AbcHippyShadowTextView.h */,
				665A72F72468F7B3008C6C61 /* AbcHippyShadowTextView.mm */,
				665A72F52468F7B3008C6C61 /* AbcHippyTextField.h */,
				665A72F92468F7B3008C6C61 /* AbcHippyTextField.m */,
				665A72F42468F7B3008C6C61 /* AbcHippyTextSelection.h */,
				665A72F82468F7B3008C6C61 /* AbcHippyTextSelection.m */,
				665A72F32468F7B3008C6C61 /* AbcHippyTextView.h */,
				665A72EF2468F7B3008C6C61 /* AbcHippyTextView.m */,
				665A72F12468F7B3008C6C61 /* AbcHippyTextViewManager.h */,
				665A72FA2468F7B3008C6C61 /* AbcHippyTextViewManager.mm */,
				6625AA06246D20EB00E55AE9 /* AbcHippyTextViewImpl.h */,
				6625AA08246D20F200E55AE9 /* AbcHippyTextViewImpl.mm */,
				6625AA0D246D222200E55AE9 /* AbcHippyTextViewImplManager.h */,
				6625AA0E246D222200E55AE9 /* AbcHippyTextViewImplManager.mm */,
			);
			path = "abc-textinput";
			sourceTree = "<group>";
		};
		665D782224F5E661000DF0F7 /* alipay */ = {
			isa = PBXGroup;
			children = (
				665D782424F5E6B2000DF0F7 /* AliPayModule.h */,
				665D782324F5E6B2000DF0F7 /* AliPayModule.m */,
			);
			path = alipay;
			sourceTree = "<group>";
		};
		667A64BF24B746200090382A /* wx-api */ = {
			isa = PBXGroup;
			children = (
				664BC55B24D3A81D00825CF5 /* WXApiHandler.h */,
				664BC55C24D3A81D00825CF5 /* WXApiHandler.m */,
				B19A5CF321CF85E2005E6B11 /* WXApiRequestHandler.h */,
				B19A5CF921CF85ED005E6B11 /* WXApiRequestHandler.m */,
			);
			path = "wx-api";
			sourceTree = "<group>";
		};
		667CE5D425A2C26500FF6C5A /* pinchimageview */ = {
			isa = PBXGroup;
			children = (
				667CE5D525A2C26500FF6C5A /* PinchImageView.h */,
				667CE5D725A2C26500FF6C5A /* PinchImageView.m */,
				667CE5D825A2C26500FF6C5A /* PinchImageViewManager.h */,
				667CE5D625A2C26500FF6C5A /* PinchImageViewManager.m */,
			);
			path = pinchimageview;
			sourceTree = "<group>";
		};
		668E758824370ABD002922A2 /* abc-navigator */ = {
			isa = PBXGroup;
			children = (
				6661F26C24581BD300ECEA46 /* TransitionType.h */,
				668E758A24370ABD002922A2 /* ABCNavigator.h */,
				668E758F24370ABD002922A2 /* ABCNavigator.m */,
				668E758E24370ABD002922A2 /* ABCNavigatorItem.h */,
				668E758B24370ABD002922A2 /* ABCNavigatorItem.m */,
				668E758924370ABD002922A2 /* ABCNavigatorManager.h */,
				668E758D24370ABD002922A2 /* ABCNavigatorManager.m */,
				668E758C24370ABD002922A2 /* ABCNavigatorItemManager.h */,
				668E759024370ABD002922A2 /* ABCNavigatorItemManager.m */,
				66D36F0A250F63D200E8E232 /* IBackListener.h */,
			);
			path = "abc-navigator";
			sourceTree = "<group>";
		};
		66942EC024E53CB400DC1B3A /* captureview */ = {
			isa = PBXGroup;
			children = (
				66942EC324E53E9E00DC1B3A /* CaptureView.h */,
				66942EC124E53E9E00DC1B3A /* CaptureView.m */,
				66942EC224E53E9E00DC1B3A /* CaptureViewManager.h */,
				66942EC424E53E9F00DC1B3A /* CaptureViewManager.m */,
			);
			path = captureview;
			sourceTree = "<group>";
		};
		66942EC724E548D500DC1B3A /* gallery-saver */ = {
			isa = PBXGroup;
			children = (
				66942EC824E548FA00DC1B3A /* GallerySaverModule.h */,
				66942EC924E548FA00DC1B3A /* GallerySaverModule.m */,
			);
			path = "gallery-saver";
			sourceTree = "<group>";
		};
		66942ECB24E621FD00DC1B3A /* webviewutils */ = {
			isa = PBXGroup;
			children = (
				66942ECD24E622B700DC1B3A /* WebViewUtilsModule.h */,
				66942ECC24E622B700DC1B3A /* WebViewUtilsModule.m */,
			);
			path = webviewutils;
			sourceTree = "<group>";
		};
		66942ECF24E6719700DC1B3A /* abcwebview */ = {
			isa = PBXGroup;
			children = (
				66942ED324E671C000DC1B3A /* AbcWebView.h */,
				66942ED224E671C000DC1B3A /* AbcWebView.m */,
				66942ED124E671C000DC1B3A /* AbcWebViewManager.h */,
				66942ED024E671C000DC1B3A /* AbcWebViewManager.m */,
			);
			path = abcwebview;
			sourceTree = "<group>";
		};
		66AFC5E124DD88F300BB0F83 /* audio-recorder */ = {
			isa = PBXGroup;
			children = (
				66AFC5E324DD88F300BB0F83 /* AudioRecorderModule.h */,
				66AFC5E224DD88F300BB0F83 /* AudioRecorderModule.m */,
			);
			path = "audio-recorder";
			sourceTree = "<group>";
		};
		66AFC5E524E2E77900BB0F83 /* third-call */ = {
			isa = PBXGroup;
			children = (
				66AFC5E624E2E7AA00BB0F83 /* ThirdCallModule.h */,
				66AFC5E724E2E7AA00BB0F83 /* ThirdCallModule.m */,
			);
			path = "third-call";
			sourceTree = "<group>";
		};
		66B3130124E50E3600F75F77 /* qrview */ = {
			isa = PBXGroup;
			children = (
				66B3130324E50E8700F75F77 /* QRView.h */,
				66B3130424E50E8700F75F77 /* QRView.m */,
				66B3130524E50E8800F75F77 /* QRViewManager.h */,
				66B3130224E50E8700F75F77 /* QRViewManager.m */,
			);
			path = qrview;
			sourceTree = "<group>";
		};
		66BF71DD28F54D1B00EE457A /* lrucache */ = {
			isa = PBXGroup;
			children = (
				66BF71DE28F54D1B00EE457A /* LruCache.h */,
				66BF71DF28F54D1B00EE457A /* LruCache.m */,
			);
			path = lrucache;
			sourceTree = "<group>";
		};
		66C17CAB2435B2B60009D385 /* views */ = {
			isa = PBXGroup;
			children = (
				66DC5C062990C8E9000F7965 /* abcscrollview */,
				118E722B28F2C27B0079006F /* svgview */,
				667CE5D425A2C26500FF6C5A /* pinchimageview */,
				66E2334E25418FD700135BE5 /* blurview */,
				66942ECF24E6719700DC1B3A /* abcwebview */,
				66942EC024E53CB400DC1B3A /* captureview */,
				66B3130124E50E3600F75F77 /* qrview */,
				6643497524D297A500137935 /* charts */,
				660081342481F1DC00761AE3 /* will-pop-listener */,
				665A72DC2468F60E008C6C61 /* abc-textinput */,
				112E28C7245C6D0100A5DF37 /* audio */,
				11DA63F92445FBCB00DBA6C6 /* wheel */,
				668E758824370ABD002922A2 /* abc-navigator */,
				66C17D202435C1980009D385 /* touch-listener-view */,
				66C17CBD2435B3480009D385 /* reverse-listview */,
			);
			path = views;
			sourceTree = "<group>";
		};
		66C17CBD2435B3480009D385 /* reverse-listview */ = {
			isa = PBXGroup;
			children = (
				66C17CD02435B3CB0009D385 /* ReverseListItemView.h */,
				66C17CD12435B3CB0009D385 /* ReverseListItemView.m */,
				66C17CD42435B3CB0009D385 /* ReverseListItemViewManager.h */,
				66C17CD52435B3CB0009D385 /* ReverseListItemViewManager.m */,
				66C17CD32435B3CB0009D385 /* ReverseListView.h */,
				66C17CD62435B3CB0009D385 /* ReverseListView.m */,
				66C17CD22435B3CB0009D385 /* ReverseListViewDataSource.h */,
				66C17CD72435B3CB0009D385 /* ReverseListViewDataSource.m */,
				66C17CD82435B3CB0009D385 /* ReverseListViewManager.h */,
				66C17CCE2435B3CB0009D385 /* ReverseListViewManager.m */,
			);
			path = "reverse-listview";
			sourceTree = "<group>";
		};
		66C17D1E2435BC880009D385 /* common */ = {
			isa = PBXGroup;
			children = (
				66BF71DD28F54D1B00EE457A /* lrucache */,
				66137F0424D94B3F00E44F63 /* file */,
				112E2909245CFF0800A5DF37 /* auido */,
				11D552C0243619DA0049D7ED /* utils */,
				664AE9372435FEE00063FCC2 /* ui */,
				66C17D2F2435DF180009D385 /* Log.h */,
				66C17D2E2435DF0D0009D385 /* Singleton.h */,
				664AE9812435FF3D0063FCC2 /* ABCMacros.h */,
			);
			path = common;
			sourceTree = "<group>";
		};
		66C17D202435C1980009D385 /* touch-listener-view */ = {
			isa = PBXGroup;
			children = (
				66413FF92448618A0029D998 /* TouchListenerViewTouchHandler.h */,
				66413FF82448618A0029D998 /* TouchListenerViewTouchHandler.m */,
				66C17D222435C1F40009D385 /* TouchListenerView.h */,
				66C17D232435C1F40009D385 /* TouchListenerView.m */,
				66C17D212435C1F40009D385 /* TouchListenerViewManager.h */,
				66C17D242435C1F40009D385 /* TouchListenerViewManager.m */,
			);
			path = "touch-listener-view";
			sourceTree = "<group>";
		};
		66C17D2A2435DD960009D385 /* modules */ = {
			isa = PBXGroup;
			children = (
				7D88C5092E24E511003AFF5A /* asr */,
				66137EFD24D8E9C900E44F63 /* abc-network */,
				66D59D0B2481363700EC1077 /* AbcImageLoaderModule.h */,
				66D59D0A2481363700EC1077 /* AbcImageLoaderModule.m */,
				665D782224F5E661000DF0F7 /* alipay */,
				66AFC5E124DD88F300BB0F83 /* audio-recorder */,
				664BC56424D3C92100825CF5 /* barcode_scan */,
				6608379724E4DE0600530B70 /* bugly */,
				66220EB72447E96B00DA8A34 /* FileManagerModule.h */,
				66220EB62447E96B00DA8A34 /* FileManagerModule.m */,
				66942EC724E548D500DC1B3A /* gallery-saver */,
				66CBBE7824DD0378003A9F78 /* image-picker */,
				66137F9D24DBA03300E44F63 /* local-notification */,
				117A558424DED09700E424CA /* mix-method */,
				662AB36724DCE28400596BB6 /* oss */,
				116CAF7B24DE569600DB4C50 /* permissions */,
				66137FB024DBB07300E44F63 /* push */,
				118F0C7E24E811D900EAAD89 /* share */,
				66137F8B24DA811000E44F63 /* socket-io */,
				6608379324E4D90200530B70 /* stat-manager */,
				66AFC5E524E2E77900BB0F83 /* third-call */,
				660929AC256F3F6800436093 /* url-launcher */,
				66942ECB24E621FD00DC1B3A /* webviewutils */,
				117A558324DECCC200E424CA /* wx-api */,
			);
			path = modules;
			sourceTree = "<group>";
		};
		66CBBE7824DD0378003A9F78 /* image-picker */ = {
			isa = PBXGroup;
			children = (
				66CBBE8C24DD0422003A9F78 /* ImagePickerModule.h */,
				66CBBE8B24DD0422003A9F78 /* ImagePickerModule.m */,
				66CBBE8024DD03D3003A9F78 /* ImagePickerImageUtil.h */,
				66CBBE8424DD03D3003A9F78 /* ImagePickerImageUtil.m */,
				66CBBE8324DD03D3003A9F78 /* ImagePickerMetaDataUtil.h */,
				66CBBE7C24DD03D3003A9F78 /* ImagePickerMetaDataUtil.m */,
				66CBBE7E24DD03D3003A9F78 /* ImagePickerPhotoAssetUtil.h */,
				66CBBE7D24DD03D3003A9F78 /* ImagePickerPhotoAssetUtil.m */,
			);
			path = "image-picker";
			sourceTree = "<group>";
		};
		66DC5C062990C8E9000F7965 /* abcscrollview */ = {
			isa = PBXGroup;
			children = (
				66DC5C0F2990C944000F7965 /* AbcScrollableProtocol.h */,
				66DC5C122990C945000F7965 /* AbcScrollView.h */,
				66DC5C112990C944000F7965 /* AbcScrollView.m */,
				66DC5C102990C944000F7965 /* AbcScrollViewManager.h */,
				66DC5C0E2990C944000F7965 /* AbcScrollViewManager.mm */,
			);
			path = abcscrollview;
			sourceTree = "<group>";
		};
		66E2334E25418FD700135BE5 /* blurview */ = {
			isa = PBXGroup;
			children = (
				66E2335225418FD700135BE5 /* BlurEffectWithAmount.h */,
				66E2335025418FD700135BE5 /* BlurEffectWithAmount.m */,
				66E2335425418FD700135BE5 /* BlurView.h */,
				66E2335125418FD700135BE5 /* BlurView.m */,
				66E2334F25418FD700135BE5 /* BlurViewManager.h */,
				66E2335325418FD700135BE5 /* BlurViewManager.m */,
			);
			path = blurview;
			sourceTree = "<group>";
		};
		7D88C4FB2E24E511003AFF5A /* listener */ = {
			isa = PBXGroup;
			children = (
			);
			path = listener;
			sourceTree = "<group>";
		};
		7D88C4FE2E24E511003AFF5A /* model */ = {
			isa = PBXGroup;
			children = (
				7D88C4FC2E24E511003AFF5A /* AbcAsrConfig.h */,
				7D88C4FD2E24E511003AFF5A /* AbcAsrConfig.m */,
			);
			path = model;
			sourceTree = "<group>";
		};
		7D88C5092E24E511003AFF5A /* asr */ = {
			isa = PBXGroup;
			children = (
				7D88C4FB2E24E511003AFF5A /* listener */,
				7D88C4FE2E24E511003AFF5A /* model */,
				7D88C4FF2E24E511003AFF5A /* AbcAsrBackgroundHandler.h */,
				7D88C5002E24E511003AFF5A /* AbcAsrBackgroundHandler.m */,
				7D88C5012E24E511003AFF5A /* AbcAsrManager.h */,
				7D88C5022E24E511003AFF5A /* AbcAsrManager.m */,
				7D88C5032E24E511003AFF5A /* AbcAsrModule.h */,
				7D88C5042E24E511003AFF5A /* AbcAsrModule.m */,
				7D88C5052E24E511003AFF5A /* AbcAsrWebSocketClient.h */,
				7D88C5062E24E511003AFF5A /* AbcAsrWebSocketClient.m */,
				7D88C5072E24E511003AFF5A /* AbcAudioRecorder.h */,
				7D88C5082E24E511003AFF5A /* AbcAudioRecorder.m */,
			);
			path = asr;
			sourceTree = "<group>";
		};
		8139B1BC4547FE6D9A769B12 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6664094F2521DFE40024188E /* libc++.tbd */,
				6664094D2521DFDB0024188E /* CFNetwork.framework */,
				6664094B2521DFD50024188E /* CoreData.framework */,
				666409492521DFD00024188E /* libz.tbd */,
				666409472521DFC20024188E /* UserNotifications.framework */,
				666409452521DFB70024188E /* SystemConfiguration.framework */,
				666409432521DFA10024188E /* CoreTelephony.framework */,
				F0D9DBE547833A806402BC6D /* Pods_Runner.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				6664093D2521DF720024188E /* libXG-SDK-Cloud.a */,
				6664093F2521DF7E0024188E /* XGMTACloud.framework */,
				1192B0DF241CF7AA007D8797 /* res */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				0E0E6303618A65B9B8263CFB /* Pods */,
				8139B1BC4547FE6D9A769B12 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				66BA7F5D24F3E4B600899127 /* Runner.entitlements */,
				1192AE68241CD4E0007D8797 /* SubProject */,
				11D6D9C4239E7D9600E3D688 /* RunnerRelease.entitlements */,
				11D6D9C3239E3B4B00E3D688 /* RunnerDebug.entitlements */,
				B19A5CCE21CF85C2005E6B11 /* FluwxKeys.h */,
				B19A5CD421CF85C4005E6B11 /* FluwxKeys.m */,
				B19A5CD221CF85C4005E6B11 /* FluwxMethods.h */,
				B19A5CD521CF85C5005E6B11 /* FluwxMethods.m */,
				B19A5CD121CF85C3005E6B11 /* ImageSchema.m */,
				B19A5CD621CF85C5005E6B11 /* StringToWeChatScene.h */,
				B19A5CCD21CF85C2005E6B11 /* StringToWeChatScene.m */,
				B19A5CCF21CF85C3005E6B11 /* StringUtil.h */,
				B19A5CD321CF85C4005E6B11 /* StringUtil.m */,
				B19A5CE021CF85D0005E6B11 /* FluwxAuthHandler.m */,
				B19A5CDD21CF85CE005E6B11 /* FluwxLaunchMiniProgramHandler.m */,
				B19A5CE321CF85D0005E6B11 /* FluwxPaymentHandler.m */,
				B19A5CDE21CF85CF005E6B11 /* FluwxShareHandler.m */,
				B19A5CE121CF85D0005E6B11 /* FluwxSubscribeMsgHandler.m */,
				B19A5CE221CF85D0005E6B11 /* NSStringWrapper.h */,
				B19A5CE421CF85D1005E6B11 /* NSStringWrapper.m */,
				B19A5CCA21CF859D005E6B11 /* CallResults.m */,
				B19A5CF521CF85E3005E6B11 /* FluwxAuthHandler.h */,
				B19A5CEC21CF85E0005E6B11 /* FluwxLaunchMiniProgramHandler.h */,
				B19A5CEE21CF85E1005E6B11 /* FluwxPaymentHandler.h */,
				B19A5CED21CF85E0005E6B11 /* FluwxPlugin.h */,
				B19A5CF121CF85E2005E6B11 /* FluwxShareHandler.h */,
				B19A5CF221CF85E2005E6B11 /* FluwxSubscribeMsgHandler.h */,
				B19A5CFB21CF85EE005E6B11 /* SendMessageToWXReq+requestWithTextOrMediaMessage.h */,
				B19A5CF821CF85ED005E6B11 /* WXMediaMessage+messageConstruct.h */,
				B19A5CFA21CF85ED005E6B11 /* WXMediaMessage+messageConstruct.m */,
				B19A5CC921CF859A005E6B11 /* CallResults.h */,
				B19A5C9E21CF7AF1005E6B11 /* FluwxPlugin.m */,
				7AFFD8ED1D35381100E5BB4D /* AppDelegate.h */,
				7AFFD8EE1D35381100E5BB4D /* AppDelegate.m */,
				667A64C024B746B30090382A /* AbcConfig.h */,
				667A64C124B746B30090382A /* AbcConfig.m */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				97C146F11CF9000F007C117D /* Supporting Files */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		97C146F11CF9000F007C117D /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				97C146F21CF9000F007C117D /* main.m */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		B99770442836169900D34173 /* core */ = {
			isa = PBXGroup;
			children = (
				B997706E2836169900D34173 /* include */,
				B99770982836169900D34173 /* third_party */,
				B99770B52836169900D34173 /* src */,
			);
			path = core;
			sourceTree = "<group>";
		};
		B997706E2836169900D34173 /* include */ = {
			isa = PBXGroup;
			children = (
				B997706F2836169900D34173 /* core */,
			);
			path = include;
			sourceTree = "<group>";
		};
		B997706F2836169900D34173 /* core */ = {
			isa = PBXGroup;
			children = (
				B99770702836169900D34173 /* engine.h */,
				B99770712836169900D34173 /* core.h */,
				B99770722836169900D34173 /* task */,
				B99770772836169900D34173 /* modules */,
				B997707D2836169900D34173 /* scope.h */,
				B997707E2836169900D34173 /* base */,
				B997708B2836169900D34173 /* napi */,
			);
			path = core;
			sourceTree = "<group>";
		};
		B99770722836169900D34173 /* task */ = {
			isa = PBXGroup;
			children = (
				B99770732836169900D34173 /* javascript_task.h */,
				B99770742836169900D34173 /* worker_task_runner.h */,
				B99770752836169900D34173 /* common_task.h */,
				B99770762836169900D34173 /* javascript_task_runner.h */,
			);
			path = task;
			sourceTree = "<group>";
		};
		B99770772836169900D34173 /* modules */ = {
			isa = PBXGroup;
			children = (
				B99770782836169900D34173 /* module_register.h */,
				B99770792836169900D34173 /* console_module.h */,
				B997707A2836169900D34173 /* timer_module.h */,
				B997707B2836169900D34173 /* module_base.h */,
				B997707C2836169900D34173 /* contextify_module.h */,
			);
			path = modules;
			sourceTree = "<group>";
		};
		B997707E2836169900D34173 /* base */ = {
			isa = PBXGroup;
			children = (
				B997707F2836169900D34173 /* js_value_wrapper.h */,
				B99770802836169900D34173 /* uri_loader.h */,
				B99770812836169900D34173 /* task.h */,
				B99770822836169900D34173 /* string_view_utils.h */,
				B99770832836169900D34173 /* file.h */,
				B99770842836169900D34173 /* task_runner.h */,
				B99770852836169900D34173 /* thread.h */,
				B99770862836169900D34173 /* common.h */,
				B99770872836169900D34173 /* macros.h */,
				B99770882836169900D34173 /* thread_id.h */,
				B99770892836169900D34173 /* base_time.h */,
				B997708A2836169900D34173 /* hash.h */,
			);
			path = base;
			sourceTree = "<group>";
		};
		B997708B2836169900D34173 /* napi */ = {
			isa = PBXGroup;
			children = (
				B997708F2836169900D34173 /* callback_info.h */,
				B99770902836169900D34173 /* js_native_api_types.h */,
				B99770912836169900D34173 /* js_native_turbo.h */,
				B99770922836169900D34173 /* js_native_api.h */,
				B99770932836169900D34173 /* native_source_code.h */,
				B99770942836169900D34173 /* jsc */,
			);
			path = napi;
			sourceTree = "<group>";
		};
		B99770942836169900D34173 /* jsc */ = {
			isa = PBXGroup;
			children = (
				B99770952836169900D34173 /* js_native_turbo_jsc.h */,
				B99770962836169900D34173 /* js_native_jsc_helper.h */,
				B99770972836169900D34173 /* js_native_api_jsc.h */,
			);
			path = jsc;
			sourceTree = "<group>";
		};
		B99770982836169900D34173 /* third_party */ = {
			isa = PBXGroup;
			children = (
				B99770992836169900D34173 /* base */,
			);
			path = third_party;
			sourceTree = "<group>";
		};
		B99770992836169900D34173 /* base */ = {
			isa = PBXGroup;
			children = (
				B997709B2836169900D34173 /* include */,
				B99770A22836169900D34173 /* .gitignore */,
				B99770A32836169900D34173 /* .clang-format */,
				B99770A42836169900D34173 /* src */,
			);
			path = base;
			sourceTree = "<group>";
		};
		B997709B2836169900D34173 /* include */ = {
			isa = PBXGroup;
			children = (
				B997709C2836169900D34173 /* base */,
			);
			path = include;
			sourceTree = "<group>";
		};
		B997709C2836169900D34173 /* base */ = {
			isa = PBXGroup;
			children = (
				B997709D2836169900D34173 /* logging.h */,
				B997709E2836169900D34173 /* log_level.h */,
				B997709F2836169900D34173 /* macros.h */,
				B99770A02836169900D34173 /* log_settings.h */,
				B99770A12836169900D34173 /* unicode_string_view.h */,
			);
			path = base;
			sourceTree = "<group>";
		};
		B99770A42836169900D34173 /* src */ = {
			isa = PBXGroup;
			children = (
				B99770A62836169900D34173 /* platform */,
				B99770AD2836169900D34173 /* base */,
			);
			path = src;
			sourceTree = "<group>";
		};
		B99770A62836169900D34173 /* platform */ = {
			isa = PBXGroup;
			children = (
				B99770AA2836169900D34173 /* ios */,
			);
			path = platform;
			sourceTree = "<group>";
		};
		B99770AA2836169900D34173 /* ios */ = {
			isa = PBXGroup;
			children = (
				B99770AC2836169900D34173 /* logging.cc */,
			);
			path = ios;
			sourceTree = "<group>";
		};
		B99770AD2836169900D34173 /* base */ = {
			isa = PBXGroup;
			children = (
				B99770AE2836169900D34173 /* log_settings_state.cc */,
				B99770B02836169900D34173 /* log_settings.cc */,
				B99770B12836169900D34173 /* unicode_string_view.cc */,
			);
			path = base;
			sourceTree = "<group>";
		};
		B99770B52836169900D34173 /* src */ = {
			isa = PBXGroup;
			children = (
				B99770B62836169900D34173 /* scope.cc */,
				B99770B72836169900D34173 /* engine.cc */,
				B99770B82836169900D34173 /* task */,
				B99770BD2836169900D34173 /* modules */,
				B99770C22836169900D34173 /* base */,
				B99770C92836169900D34173 /* napi */,
			);
			path = src;
			sourceTree = "<group>";
		};
		B99770B82836169900D34173 /* task */ = {
			isa = PBXGroup;
			children = (
				B99770B92836169900D34173 /* javascript_task_runner.cc */,
				B99770BA2836169900D34173 /* javascript_task.cc */,
				B99770BB2836169900D34173 /* worker_task_runner.cc */,
				B99770BC2836169900D34173 /* common_task.cc */,
			);
			path = task;
			sourceTree = "<group>";
		};
		B99770BD2836169900D34173 /* modules */ = {
			isa = PBXGroup;
			children = (
				B99770BE2836169900D34173 /* module_register.cc */,
				B99770BF2836169900D34173 /* console_module.cc */,
				B99770C02836169900D34173 /* timer_module.cc */,
				B99770C12836169900D34173 /* contextify_module.cc */,
			);
			path = modules;
			sourceTree = "<group>";
		};
		B99770C22836169900D34173 /* base */ = {
			isa = PBXGroup;
			children = (
				B99770C32836169900D34173 /* js_value_wrapper.cc */,
				B99770C42836169900D34173 /* file.cc */,
				B99770C52836169900D34173 /* thread_id.cc */,
				B99770C62836169900D34173 /* task_runner.cc */,
				B99770C72836169900D34173 /* task.cc */,
				B99770C82836169900D34173 /* thread.cc */,
			);
			path = base;
			sourceTree = "<group>";
		};
		B99770C92836169900D34173 /* napi */ = {
			isa = PBXGroup;
			children = (
				B99770CE2836169900D34173 /* callback_info.cc */,
				B99770CF2836169900D34173 /* js_native_turbo.cc */,
				B99770D02836169900D34173 /* jsc */,
			);
			path = napi;
			sourceTree = "<group>";
		};
		B99770D02836169900D34173 /* jsc */ = {
			isa = PBXGroup;
			children = (
				B99770D12836169900D34173 /* js_native_jsc_helper.cc */,
				B99770D22836169900D34173 /* js_native_turbo_jsc.cc */,
				B99770D32836169900D34173 /* native_source_code_ios.cc */,
				B99770D42836169900D34173 /* js_native_api_value_jsc.cc */,
				B99770D52836169900D34173 /* js_native_api_jsc.cc */,
			);
			path = jsc;
			sourceTree = "<group>";
		};
		B997711C283616AA00D34173 /* sdk */ = {
			isa = PBXGroup;
			children = (
				B997711D283616AA00D34173 /* handler */,
				B997711F283616AA00D34173 /* layout */,
				B997712F283616AA00D34173 /* module */,
				B9977173283616AA00D34173 /* component */,
				B9977214283616AA00D34173 /* utils */,
				B9977234283616AA00D34173 /* base */,
				B9977278283616AA00D34173 /* debug */,
			);
			path = sdk;
			sourceTree = "<group>";
		};
		B997711D283616AA00D34173 /* handler */ = {
			isa = PBXGroup;
			children = (
				B997711E283616AA00D34173 /* HippyCustomTouchHandlerProtocol.h */,
			);
			path = handler;
			sourceTree = "<group>";
		};
		B997711F283616AA00D34173 /* layout */ = {
			isa = PBXGroup;
			children = (
				B9977120283616AA00D34173 /* MTTLayoutCache.h */,
				B9977121283616AA00D34173 /* x5LayoutUtil.m */,
				B9977122283616AA00D34173 /* MTTFlexLine.h */,
				B9977123283616AA00D34173 /* MTTFlex.h */,
				B9977124283616AA00D34173 /* MTTNode.h */,
				B9977125283616AA00D34173 /* MTTLayout.cpp */,
				B9977126283616AA00D34173 /* MTTLayout.h */,
				B9977127283616AA00D34173 /* MTTTStyle.h */,
				B9977128283616AA00D34173 /* MTTLayoutCache.cpp */,
				B9977129283616AA00D34173 /* MTTTStyle.cpp */,
				B997712A283616AA00D34173 /* MTTUtil.h */,
				B997712B283616AA00D34173 /* x5LayoutUtil.h */,
				B997712C283616AA00D34173 /* MTTNode.cpp */,
				B997712D283616AA00D34173 /* MTTFlexLine.cpp */,
				B997712E283616AA00D34173 /* MTTUtil.cpp */,
			);
			path = layout;
			sourceTree = "<group>";
		};
		B997712F283616AA00D34173 /* module */ = {
			isa = PBXGroup;
			children = (
				B9977130283616AA00D34173 /* turbo */,
				B9977139283616AA00D34173 /* network */,
				B997713E283616AA00D34173 /* animation */,
				B997714C283616AA00D34173 /* netinfo */,
				B9977151283616AA00D34173 /* timing */,
				B9977154283616AA00D34173 /* loader */,
				B9977157283616AA00D34173 /* event observer */,
				B997715A283616AA00D34173 /* localstorage */,
				B997715D283616AA00D34173 /* dev */,
				B9977164283616AA00D34173 /* exception */,
				B9977167283616AA00D34173 /* image loader */,
				B997716C283616AA00D34173 /* uimanager */,
				B9977170283616AA00D34173 /* clipboard */,
			);
			path = module;
			sourceTree = "<group>";
		};
		B9977130283616AA00D34173 /* turbo */ = {
			isa = PBXGroup;
			children = (
				B9977131283616AA00D34173 /* HippyOCTurboModule.h */,
				B9977132283616AA00D34173 /* HippyTurboModule.h */,
				B9977133283616AA00D34173 /* HippyTurboModuleManager.h */,
				B9977134283616AA00D34173 /* NSObject+HippyTurbo.h */,
				B9977135283616AA00D34173 /* HippyOCTurboModule+Inner.h */,
				B9977136283616AA00D34173 /* HippyOCTurboModule.mm */,
				B9977137283616AA00D34173 /* HippyTurboModuleManager.mm */,
				B9977138283616AA00D34173 /* NSObject+HippyTurbo.mm */,
			);
			path = turbo;
			sourceTree = "<group>";
		};
		B9977139283616AA00D34173 /* network */ = {
			isa = PBXGroup;
			children = (
				B997713A283616AA00D34173 /* HippyNetWork.m */,
				B997713B283616AA00D34173 /* HippyFetchInfo.m */,
				B997713C283616AA00D34173 /* HippyNetWork.h */,
				B997713D283616AA00D34173 /* HippyFetchInfo.h */,
			);
			path = network;
			sourceTree = "<group>";
		};
		B997713E283616AA00D34173 /* animation */ = {
			isa = PBXGroup;
			children = (
				B997713F283616AA00D34173 /* HippyExtAnimation+Value.m */,
				B9977140283616AA00D34173 /* HippyExtAnimationGroup.h */,
				B9977141283616AA00D34173 /* CALayer+HippyAnimation.h */,
				B9977142283616AA00D34173 /* HippyExtAnimation.m */,
				B9977143283616AA00D34173 /* HippyExtAnimation+Group.h */,
				B9977144283616AA00D34173 /* HippyExtAnimationModule.m */,
				B9977145283616AA00D34173 /* HippyExtAnimationViewParams.h */,
				B9977146283616AA00D34173 /* HippyExtAnimation+Value.h */,
				B9977147283616AA00D34173 /* HippyExtAnimation.h */,
				B9977148283616AA00D34173 /* CALayer+HippyAnimation.m */,
				B9977149283616AA00D34173 /* HippyExtAnimationGroup.m */,
				B997714A283616AA00D34173 /* HippyExtAnimationViewParams.m */,
				B997714B283616AA00D34173 /* HippyExtAnimationModule.h */,
			);
			path = animation;
			sourceTree = "<group>";
		};
		B997714C283616AA00D34173 /* netinfo */ = {
			isa = PBXGroup;
			children = (
				B997714D283616AA00D34173 /* HippyNetInfo.h */,
				B997714E283616AA00D34173 /* HippyNetInfoIntenal.m */,
				B997714F283616AA00D34173 /* HippyNetInfoIntenal.h */,
				B9977150283616AA00D34173 /* HippyNetInfo.m */,
			);
			path = netinfo;
			sourceTree = "<group>";
		};
		B9977151283616AA00D34173 /* timing */ = {
			isa = PBXGroup;
			children = (
				B9977152283616AA00D34173 /* HippyTiming.mm */,
				B9977153283616AA00D34173 /* HippyTiming.h */,
			);
			path = timing;
			sourceTree = "<group>";
		};
		B9977154283616AA00D34173 /* loader */ = {
			isa = PBXGroup;
			children = (
				B9977155283616AA00D34173 /* ios_loader.h */,
				B9977156283616AA00D34173 /* ios_loader.cc */,
			);
			path = loader;
			sourceTree = "<group>";
		};
		B9977157283616AA00D34173 /* event observer */ = {
			isa = PBXGroup;
			children = (
				B9977158283616AA00D34173 /* HippyEventObserverModule.m */,
				B9977159283616AA00D34173 /* HippyEventObserverModule.h */,
			);
			path = "event observer";
			sourceTree = "<group>";
		};
		B997715A283616AA00D34173 /* localstorage */ = {
			isa = PBXGroup;
			children = (
				B997715B283616AA00D34173 /* HippyAsyncLocalStorage.h */,
				B997715C283616AA00D34173 /* HippyAsyncLocalStorage.m */,
			);
			path = localstorage;
			sourceTree = "<group>";
		};
		B997715D283616AA00D34173 /* dev */ = {
			isa = PBXGroup;
			children = (
				B997715E283616AA00D34173 /* HippyDevMenu.mm */,
				B997715F283616AA00D34173 /* HippyDevMenu.h */,
				B9977160283616AA00D34173 /* HippyRedBox.m */,
				B9977161283616AA00D34173 /* HippyDevLoadingView.h */,
				B9977162283616AA00D34173 /* HippyDevLoadingView.m */,
				B9977163283616AA00D34173 /* HippyRedBox.h */,
			);
			path = dev;
			sourceTree = "<group>";
		};
		B9977164283616AA00D34173 /* exception */ = {
			isa = PBXGroup;
			children = (
				B9977165283616AA00D34173 /* HippyExceptionModule.h */,
				B9977166283616AA00D34173 /* HippyExceptionModule.m */,
			);
			path = exception;
			sourceTree = "<group>";
		};
		B9977167283616AA00D34173 /* image loader */ = {
			isa = PBXGroup;
			children = (
				B9977168283616AA00D34173 /* HippyImageLoaderModule.m */,
				B9977169283616AA00D34173 /* HippyImageCacheManager.h */,
				B997716A283616AA00D34173 /* HippyImageLoaderModule.h */,
				B997716B283616AA00D34173 /* HippyImageCacheManager.m */,
			);
			path = "image loader";
			sourceTree = "<group>";
		};
		B997716C283616AA00D34173 /* uimanager */ = {
			isa = PBXGroup;
			children = (
				B997716D283616AA00D34173 /* HippyUIManager.h */,
				B997716E283616AA00D34173 /* HippyUIManager+Private.h */,
				B997716F283616AA00D34173 /* HippyUIManager.mm */,
			);
			path = uimanager;
			sourceTree = "<group>";
		};
		B9977170283616AA00D34173 /* clipboard */ = {
			isa = PBXGroup;
			children = (
				B9977171283616AA00D34173 /* HippyClipboardModule.h */,
				B9977172283616AA00D34173 /* HippyClipboardModule.m */,
			);
			path = clipboard;
			sourceTree = "<group>";
		};
		B9977173283616AA00D34173 /* component */ = {
			isa = PBXGroup;
			children = (
				B9977174283616AA00D34173 /* scrollview */,
				B997717A283616AA00D34173 /* refreshview */,
				B9977183283616AA00D34173 /* refresh */,
				B9977186283616AA00D34173 /* viewPager */,
				B997718F283616AA00D34173 /* textinput */,
				B997719C283616AA00D34173 /* webview */,
				B99771A1283616AA00D34173 /* headerrefresh */,
				B99771A6283616AA00D34173 /* waterfalllist */,
				B99771B3283616AA00D34173 /* image */,
				B99771C5283616AA00D34173 /* listview */,
				B99771D5283616AA00D34173 /* view */,
				B99771EC283616AA00D34173 /* navigator */,
				B99771F7283616AA00D34173 /* footerrefresh */,
				B99771FC283616AA00D34173 /* text */,
				B9977206283616AA00D34173 /* modal */,
			);
			path = component;
			sourceTree = "<group>";
		};
		B9977174283616AA00D34173 /* scrollview */ = {
			isa = PBXGroup;
			children = (
				B9977175283616AA00D34173 /* HippyScrollView.m */,
				B9977176283616AA00D34173 /* HippyScrollableProtocol.h */,
				B9977177283616AA00D34173 /* HippyScrollViewManager.h */,
				B9977178283616AA00D34173 /* HippyScrollView.h */,
				B9977179283616AA00D34173 /* HippyScrollViewManager.mm */,
			);
			path = scrollview;
			sourceTree = "<group>";
		};
		B997717A283616AA00D34173 /* refreshview */ = {
			isa = PBXGroup;
			children = (
				B997717B283616AA00D34173 /* HippyRefreshWrapperItemViewManager.h */,
				B997717C283616AA00D34173 /* HippyRefreshWrapperItemView.m */,
				B997717D283616AA00D34173 /* HippyRefreshWrapperViewManager.m */,
				B997717E283616AA00D34173 /* HippyRefreshWrapper.m */,
				B997717F283616AA00D34173 /* HippyRefreshWrapperItemView.h */,
				B9977180283616AA00D34173 /* HippyRefreshWrapperItemViewManager.m */,
				B9977181283616AA00D34173 /* HippyRefreshWrapper.h */,
				B9977182283616AA00D34173 /* HippyRefreshWrapperViewManager.h */,
			);
			path = refreshview;
			sourceTree = "<group>";
		};
		B9977183283616AA00D34173 /* refresh */ = {
			isa = PBXGroup;
			children = (
				B9977184283616AA00D34173 /* HippyRefresh.h */,
				B9977185283616AA00D34173 /* HippyRefresh.m */,
			);
			path = refresh;
			sourceTree = "<group>";
		};
		B9977186283616AA00D34173 /* viewPager */ = {
			isa = PBXGroup;
			children = (
				B9977187283616AA00D34173 /* HippyViewPagerItemManager.h */,
				B9977188283616AA00D34173 /* HippyViewPager.m */,
				B9977189283616AA00D34173 /* HippyViewPagerItem.h */,
				B997718A283616AA00D34173 /* HippyViewPagerManager.m */,
				B997718B283616AA00D34173 /* HippyViewPagerItem.m */,
				B997718C283616AA00D34173 /* HippyViewPager.h */,
				B997718D283616AA00D34173 /* HippyViewPagerItemManager.m */,
				B997718E283616AA00D34173 /* HippyViewPagerManager.h */,
			);
			path = viewPager;
			sourceTree = "<group>";
		};
		B997718F283616AA00D34173 /* textinput */ = {
			isa = PBXGroup;
			children = (
				B9977190283616AA00D34173 /* HippyBaseTextInput.h */,
				B9977191283616AA00D34173 /* HippyTextView.mm */,
				B9977192283616AA00D34173 /* HippyTextViewManager.mm */,
				B9977193283616AA00D34173 /* HippyTextField.h */,
				B9977194283616AA00D34173 /* HippyShadowTextView.mm */,
				B9977195283616AA00D34173 /* HippyTextSelection.h */,
				B9977196283616AA00D34173 /* HippyTextViewManager.h */,
				B9977197283616AA00D34173 /* HippyBaseTextInput.m */,
				B9977198283616AA00D34173 /* HippyShadowTextView.h */,
				B9977199283616AA00D34173 /* HippyTextView.h */,
				B997719A283616AA00D34173 /* HippyTextSelection.m */,
				B997719B283616AA00D34173 /* HippyTextField.m */,
			);
			path = textinput;
			sourceTree = "<group>";
		};
		B997719C283616AA00D34173 /* webview */ = {
			isa = PBXGroup;
			children = (
				B997719D283616AA00D34173 /* HippySimpleWebView.h */,
				B997719E283616AA00D34173 /* HippySimpleWebViewManager.h */,
				B997719F283616AA00D34173 /* HippySimpleWebView.m */,
				B99771A0283616AA00D34173 /* HippySimpleWebViewManager.m */,
			);
			path = webview;
			sourceTree = "<group>";
		};
		B99771A1283616AA00D34173 /* headerrefresh */ = {
			isa = PBXGroup;
			children = (
				B99771A2283616AA00D34173 /* HippyHeaderRefreshManager.h */,
				B99771A3283616AA00D34173 /* HippyHeaderRefresh.m */,
				B99771A4283616AA00D34173 /* HippyHeaderRefreshManager.m */,
				B99771A5283616AA00D34173 /* HippyHeaderRefresh.h */,
			);
			path = headerrefresh;
			sourceTree = "<group>";
		};
		B99771A6283616AA00D34173 /* waterfalllist */ = {
			isa = PBXGroup;
			children = (
				B99771A7283616AA00D34173 /* HippyReusableNodeCache.m */,
				B99771A8283616AA00D34173 /* HippyWaterfallItemViewManager.h */,
				B99771A9283616AA00D34173 /* HippyCollectionViewWaterfallLayout.h */,
				B99771AA283616AA00D34173 /* HippyWaterfallItemView.h */,
				B99771AB283616AA00D34173 /* HippyWaterfallViewManager.h */,
				B99771AC283616AA00D34173 /* HippyWaterfallView.m */,
				B99771AD283616AA00D34173 /* HippyWaterfallItemViewManager.m */,
				B99771AE283616AA00D34173 /* HippyReusableNodeCache.h */,
				B99771AF283616AA00D34173 /* HippyWaterfallItemView.m */,
				B99771B0283616AA00D34173 /* HippyCollectionViewWaterfallLayout.m */,
				B99771B1283616AA00D34173 /* HippyWaterfallViewManager.m */,
				B99771B2283616AA00D34173 /* HippyWaterfallView.h */,
			);
			path = waterfalllist;
			sourceTree = "<group>";
		};
		B99771B3283616AA00D34173 /* image */ = {
			isa = PBXGroup;
			children = (
				B99771B4283616AA00D34173 /* HippyDefaultImageProvider.h */,
				B99771B5283616AA00D34173 /* HippyImageViewManager.h */,
				B99771B6283616AA00D34173 /* HippyImageView.m */,
				B99771B7283616AA00D34173 /* HippyImageProviderProtocol.m */,
				B99771B8283616AA00D34173 /* HippyAnimatedImageView.h */,
				B99771B9283616AA00D34173 /* HippyImageCache.h */,
				B99771BA283616AA00D34173 /* UIImageView+Hippy.m */,
				B99771BB283616AA00D34173 /* HippyAnimatedImage.m */,
				B99771BC283616AA00D34173 /* HippyDefaultImageProvider.m */,
				B99771BD283616AA00D34173 /* HippyImageProviderProtocol.h */,
				B99771BE283616AA00D34173 /* HippyAnimatedImageView.m */,
				B99771BF283616AA00D34173 /* HippyImageViewCustomLoader.h */,
				B99771C0283616AA00D34173 /* HippyImageView.h */,
				B99771C1283616AA00D34173 /* HippyImageViewManager.m */,
				B99771C2283616AA00D34173 /* HippyAnimatedImage.h */,
				B99771C3283616AA00D34173 /* UIImageView+Hippy.h */,
				B99771C4283616AA00D34173 /* HippyImageCache.m */,
			);
			path = image;
			sourceTree = "<group>";
		};
		B99771C5283616AA00D34173 /* listview */ = {
			isa = PBXGroup;
			children = (
				B99771C6283616AA00D34173 /* HippyBaseListItemView.h */,
				B99771C7283616AA00D34173 /* HippyBaseListViewProtocol.h */,
				B99771C8283616AA00D34173 /* HippyBaseListItemViewManager.m */,
				B99771C9283616AA00D34173 /* HippyBaseListViewDataSource.m */,
				B99771CA283616AA00D34173 /* HippyListTableView.h */,
				B99771CB283616AA00D34173 /* HippyBaseListViewManager.h */,
				B99771CC283616AA00D34173 /* HippyBaseListView.h */,
				B99771CD283616AA00D34173 /* HippyBaseListViewCell.h */,
				B99771CE283616AA00D34173 /* HippyBaseListItemViewManager.h */,
				B99771CF283616AA00D34173 /* HippyBaseListItemView.m */,
				B99771D0283616AA00D34173 /* HippyBaseListViewManager.m */,
				B99771D1283616AA00D34173 /* HippyListTableView.m */,
				B99771D2283616AA00D34173 /* HippyBaseListViewDataSource.h */,
				B99771D3283616AA00D34173 /* HippyBaseListView.m */,
				B99771D4283616AA00D34173 /* HippyBaseListViewCell.m */,
			);
			path = listview;
			sourceTree = "<group>";
		};
		B99771D5283616AA00D34173 /* view */ = {
			isa = PBXGroup;
			children = (
				B99771D6283616AA00D34173 /* UIView+AppearEvent.m */,
				B99771D7283616AA00D34173 /* UIView+HippyAnimationProtocol.m */,
				B99771D8283616AA00D34173 /* HippyView+HippyViewAnimation.m */,
				B99771D9283616AA00D34173 /* HippyBackgroundImageCacheManager.h */,
				B99771DA283616AA00D34173 /* UIView+Hippy.mm */,
				B99771DB283616AA00D34173 /* HippyShadowView.h */,
				B99771DC283616AA00D34173 /* HippyPointerEvents.h */,
				B99771DD283616AA00D34173 /* HippyViewManager.mm */,
				B99771DE283616AA00D34173 /* HippyViewEventProtocol.h */,
				B99771DF283616AA00D34173 /* HippyBorderStyle.h */,
				B99771E0283616AA00D34173 /* HippyShadowView.mm */,
				B99771E1283616AA00D34173 /* HippyView.m */,
				B99771E2283616AA00D34173 /* HippyBorderDrawing.m */,
				B99771E3283616AA00D34173 /* HippyView+HippyViewAnimation.h */,
				B99771E4283616AA00D34173 /* UIView+HippyAnimationProtocol.h */,
				B99771E5283616AA00D34173 /* UIView+AppearEvent.h */,
				B99771E6283616AA00D34173 /* HippyBackgroundImageCacheManager.m */,
				B99771E7283616AA00D34173 /* HippyViewManager.h */,
				B99771E8283616AA00D34173 /* UIView+Hippy.h */,
				B99771E9283616AA00D34173 /* UIView+Private.h */,
				B99771EA283616AA00D34173 /* HippyBorderDrawing.h */,
				B99771EB283616AA00D34173 /* HippyView.h */,
			);
			path = view;
			sourceTree = "<group>";
		};
		B99771EC283616AA00D34173 /* navigator */ = {
			isa = PBXGroup;
			children = (
				B99771ED283616AA00D34173 /* HippyNavigatorRootViewController.m */,
				B99771EE283616AA00D34173 /* HippyNavigatorItemViewController.m */,
				B99771EF283616AA00D34173 /* HippyNavigatorHostView.mm */,
				B99771F0283616AA00D34173 /* HippyNavigationControllerAnimator.m */,
				B99771F1283616AA00D34173 /* HippyNavigatorViewManager.h */,
				B99771F2283616AA00D34173 /* HippyNavigatorItemViewController.h */,
				B99771F3283616AA00D34173 /* HippyNavigatorRootViewController.h */,
				B99771F4283616AA00D34173 /* HippyNavigatorViewManager.m */,
				B99771F5283616AA00D34173 /* HippyNavigationControllerAnimator.h */,
				B99771F6283616AA00D34173 /* HippyNavigatorHostView.h */,
			);
			path = navigator;
			sourceTree = "<group>";
		};
		B99771F7283616AA00D34173 /* footerrefresh */ = {
			isa = PBXGroup;
			children = (
				B99771F8283616AA00D34173 /* HippyFooterRefreshManager.m */,
				B99771F9283616AA00D34173 /* HippyFooterRefresh.m */,
				B99771FA283616AA00D34173 /* HippyFooterRefreshManager.h */,
				B99771FB283616AA00D34173 /* HippyFooterRefresh.h */,
			);
			path = footerrefresh;
			sourceTree = "<group>";
		};
		B99771FC283616AA00D34173 /* text */ = {
			isa = PBXGroup;
			children = (
				B99771FD283616AA00D34173 /* HippyVirtualTextNode.h */,
				B99771FE283616AA00D34173 /* HippyShadowText.mm */,
				B99771FF283616AA00D34173 /* HippyShadowText.h */,
				B9977200283616AA00D34173 /* HippyText.mm */,
				B9977201283616AA00D34173 /* HippyVirtualTextNode.m */,
				B9977202283616AA00D34173 /* HippyText.h */,
				B9977203283616AA00D34173 /* HippyTextDecorationLineType.h */,
				B9977204283616AA00D34173 /* HippyTextManager.mm */,
				B9977205283616AA00D34173 /* HippyTextManager.h */,
			);
			path = text;
			sourceTree = "<group>";
		};
		B9977206283616AA00D34173 /* modal */ = {
			isa = PBXGroup;
			children = (
				B9977207283616AA00D34173 /* HippyModalCustomAnimationTransition.h */,
				B9977208283616AA00D34173 /* HippyModalCustomPresentationController.m */,
				B9977209283616AA00D34173 /* HippyModalHostViewManager.mm */,
				B997720A283616AA00D34173 /* HippyModalTransitioningDelegate.mm */,
				B997720B283616AA00D34173 /* HippyModalHostViewController.m */,
				B997720C283616AA00D34173 /* HippyModalHostView.m */,
				B997720D283616AA00D34173 /* HippyModalHostViewInteractor.h */,
				B997720E283616AA00D34173 /* HippyModalCustomPresentationController.h */,
				B997720F283616AA00D34173 /* HippyModalCustomAnimationTransition.m */,
				B9977210283616AA00D34173 /* HippyModalHostViewManager.h */,
				B9977211283616AA00D34173 /* HippyModalHostViewController.h */,
				B9977212283616AA00D34173 /* HippyModalTransitioningDelegate.h */,
				B9977213283616AA00D34173 /* HippyModalHostView.h */,
			);
			path = modal;
			sourceTree = "<group>";
		};
		B9977214283616AA00D34173 /* utils */ = {
			isa = PBXGroup;
			children = (
				B9977215283616AA00D34173 /* HippyParserUtils.m */,
				B9977216283616AA00D34173 /* NSNumber+HippyNumberDeepCopy.h */,
				B9977217283616AA00D34173 /* HippyI18nUtils.h */,
				B9977218283616AA00D34173 /* HippyGradientObject.m */,
				B9977219283616AA00D34173 /* HippyLog.mm */,
				B997721A283616AA00D34173 /* HippyErrorCustomizer.h */,
				B997721B283616AA00D34173 /* HippyConvert+Transform.m */,
				B997721C283616AA00D34173 /* HippyErrorInfo.h */,
				B997721D283616AA00D34173 /* NSArray+HippyArrayDeepCopy.m */,
				B997721E283616AA00D34173 /* NSDictionary+HippyDictionaryDeepCopy.h */,
				B997721F283616AA00D34173 /* HippyDefines.h */,
				B9977220283616AA00D34173 /* NSData+DataType.m */,
				B9977221283616AA00D34173 /* HippyUtils.h */,
				B9977222283616AA00D34173 /* HippyAssert.m */,
				B9977223283616AA00D34173 /* HippyGradientObject.h */,
				B9977224283616AA00D34173 /* HippyI18nUtils.m */,
				B9977225283616AA00D34173 /* NSNumber+HippyNumberDeepCopy.m */,
				B9977226283616AA00D34173 /* HippyParserUtils.h */,
				B9977227283616AA00D34173 /* HippyConvert+Transform.h */,
				B9977228283616AA00D34173 /* HippyNullability.h */,
				B9977229283616AA00D34173 /* HippyConvert.mm */,
				B997722A283616AA00D34173 /* HippyConvert.h */,
				B997722B283616AA00D34173 /* NSArray+HippyArrayDeepCopy.h */,
				B997722C283616AA00D34173 /* HippyErrorInfo.m */,
				B997722D283616AA00D34173 /* HippyDeepCopyProtocol.h */,
				B997722E283616AA00D34173 /* HippyAssert.h */,
				B997722F283616AA00D34173 /* HippyUtils.m */,
				B9977230283616AA00D34173 /* HippyLog.h */,
				B9977231283616AA00D34173 /* NSData+DataType.h */,
				B9977232283616AA00D34173 /* NSDictionary+HippyDictionaryDeepCopy.m */,
			);
			path = utils;
			sourceTree = "<group>";
		};
		B9977234283616AA00D34173 /* base */ = {
			isa = PBXGroup;
			children = (
				B9977235283616AA00D34173 /* HippyJSEnginesMapper.h */,
				B9977236283616AA00D34173 /* HippyPerformanceLogger.m */,
				B9977237283616AA00D34173 /* virtualNode */,
				B997723C283616AA00D34173 /* HippyComponentData.mm */,
				B997723D283616AA00D34173 /* HippyTouchHandler.m */,
				B997723E283616AA00D34173 /* HippyComponent.h */,
				B997723F283616AA00D34173 /* HippyAutoInsetsProtocol.h */,
				B9977240283616AA00D34173 /* HippyAnimationType.h */,
				B9977241283616AA00D34173 /* HippyBridge.h */,
				B9977242283616AA00D34173 /* HippyJSStackFrame.m */,
				B9977243283616AA00D34173 /* HippyMethodInterceptorProtocol.h */,
				B9977244283616AA00D34173 /* HippyEventDispatcher.m */,
				B9977245283616AA00D34173 /* HippyModuleMethod.mm */,
				B9977246283616AA00D34173 /* HippyBridgeModule.h */,
				B9977247283616AA00D34173 /* HippyBridge+Mtt.h */,
				B9977248283616AA00D34173 /* HippyKeyCommands.h */,
				B9977249283616AA00D34173 /* HippyRootView.mm */,
				B997724A283616AA00D34173 /* HippyRootView.h */,
				B997724B283616AA00D34173 /* HippyBridge+Private.h */,
				B997724C283616AA00D34173 /* HippyFont.h */,
				B997724D283616AA00D34173 /* HippyModuleMethod.h */,
				B997724E283616AA00D34173 /* HippyModuleData.h */,
				B997724F283616AA00D34173 /* HippyBridge+LocalFileSource.h */,
				B9977250283616AA00D34173 /* HippyScrollProtocol.h */,
				B9977251283616AA00D34173 /* HippyDisplayLink.h */,
				B9977252283616AA00D34173 /* HippyFrameUpdate.h */,
				B9977253283616AA00D34173 /* HippyDeviceBaseInfo.h */,
				B9977254283616AA00D34173 /* HippyBundleURLProvider.m */,
				B9977255283616AA00D34173 /* HippyTouchHandler.h */,
				B9977256283616AA00D34173 /* HippyJSEnginesMapper.mm */,
				B9977257283616AA00D34173 /* HippyRootShadowView.h */,
				B9977258283616AA00D34173 /* HippyJavaScriptLoader.mm */,
				B9977259283616AA00D34173 /* HippyPerformanceLogger.h */,
				B997725A283616AA00D34173 /* HippyBatchedBridge.mm */,
				B997725B283616AA00D34173 /* HippyBridgeDelegate.h */,
				B997725C283616AA00D34173 /* HippyInvalidating.h */,
				B997725D283616AA00D34173 /* executors */,
				B9977264283616AA00D34173 /* HippyKeyCommands.m */,
				B9977265283616AA00D34173 /* HippyFont.mm */,
				B9977266283616AA00D34173 /* HippyEventDispatcher.h */,
				B9977267283616AA00D34173 /* HippyBridgeMethod.h */,
				B9977268283616AA00D34173 /* HippyJSStackFrame.h */,
				B9977269283616AA00D34173 /* HippyBridge+LocalFileSource.m */,
				B997726A283616AA00D34173 /* HippyJavaScriptExecutor.h */,
				B997726B283616AA00D34173 /* HippyBridge.mm */,
				B997726C283616AA00D34173 /* HippyJavaScriptLoader.h */,
				B997726D283616AA00D34173 /* HippyMemoryOpt.h */,
				B997726E283616AA00D34173 /* HippyDeviceBaseInfo.m */,
				B997726F283616AA00D34173 /* HippyBundleURLProvider.h */,
				B9977270283616AA00D34173 /* HippyComponentData.h */,
				B9977271283616AA00D34173 /* HippyRootViewDelegate.h */,
				B9977272283616AA00D34173 /* HippyModuleData.mm */,
				B9977273283616AA00D34173 /* HippyBridge+Mtt.mm */,
				B9977274283616AA00D34173 /* HippyFrameUpdate.m */,
				B9977275283616AA00D34173 /* HippyDisplayLink.m */,
				B9977276283616AA00D34173 /* HippyRootViewInternal.h */,
				B9977277283616AA00D34173 /* HippyRootShadowView.mm */,
			);
			path = base;
			sourceTree = "<group>";
		};
		B9977237283616AA00D34173 /* virtualNode */ = {
			isa = PBXGroup;
			children = (
				B9977238283616AA00D34173 /* HippyVirtualList.h */,
				B9977239283616AA00D34173 /* HippyVirtualNode.m */,
				B997723A283616AA00D34173 /* HippyVirtualList.m */,
				B997723B283616AA00D34173 /* HippyVirtualNode.h */,
			);
			path = virtualNode;
			sourceTree = "<group>";
		};
		B997725D283616AA00D34173 /* executors */ = {
			isa = PBXGroup;
			children = (
				B997725E283616AA00D34173 /* HippyJSCExecutor.mm */,
				B997725F283616AA00D34173 /* HippyJSCErrorHandling.m */,
				B9977260283616AA00D34173 /* HippyJSCWrapper.h */,
				B9977261283616AA00D34173 /* HippyJSCExecutor.h */,
				B9977262283616AA00D34173 /* HippyJSCErrorHandling.h */,
				B9977263283616AA00D34173 /* HippyJSCWrapper.mm */,
			);
			path = executors;
			sourceTree = "<group>";
		};
		B9977278283616AA00D34173 /* debug */ = {
			isa = PBXGroup;
			children = (
				B9977279283616AA00D34173 /* websocket */,
				B9977282283616AA00D34173 /* devtools */,
			);
			path = debug;
			sourceTree = "<group>";
		};
		B9977279283616AA00D34173 /* websocket */ = {
			isa = PBXGroup;
			children = (
				B997727A283616AA00D34173 /* HippyWebSocketProxyDelegate.h */,
				B997727B283616AA00D34173 /* HippySRSIMDHelpers.h */,
				B997727C283616AA00D34173 /* HippyWebSocketManager.h */,
				B997727D283616AA00D34173 /* HippyWebSocketProxy.h */,
				B997727E283616AA00D34173 /* HippySRWebSocket.m */,
				B997727F283616AA00D34173 /* HippySRSIMDHelpers.m */,
				B9977280283616AA00D34173 /* HippyWebSocketManager.m */,
				B9977281283616AA00D34173 /* HippySRWebSocket.h */,
			);
			path = websocket;
			sourceTree = "<group>";
		};
		B9977282283616AA00D34173 /* devtools */ = {
			isa = PBXGroup;
			children = (
				B9977283283616AA00D34173 /* HippyDevWebSocketClient.h */,
				B9977284283616AA00D34173 /* HippyDevInfo.h */,
				B9977285283616AA00D34173 /* inspector */,
				B997729C283616AA00D34173 /* HippyDevManager.m */,
				B997729D283616AA00D34173 /* HippyDevInfo.m */,
				B997729E283616AA00D34173 /* HippyDevWebSocketClient.m */,
				B997729F283616AA00D34173 /* HippyDevManager.h */,
			);
			path = devtools;
			sourceTree = "<group>";
		};
		B9977285283616AA00D34173 /* inspector */ = {
			isa = PBXGroup;
			children = (
				B9977286283616AA00D34173 /* HippyInspector.h */,
				B9977287283616AA00D34173 /* HippyDevCommand.h */,
				B9977288283616AA00D34173 /* model */,
				B9977291283616AA00D34173 /* domain */,
				B997729A283616AA00D34173 /* HippyDevCommand.m */,
				B997729B283616AA00D34173 /* HippyInspector.m */,
			);
			path = inspector;
			sourceTree = "<group>";
		};
		B9977288283616AA00D34173 /* model */ = {
			isa = PBXGroup;
			children = (
				B9977289283616AA00D34173 /* HippyCSSModel.h */,
				B997728A283616AA00D34173 /* HippyPageModel.h */,
				B997728B283616AA00D34173 /* HippyDomModel.h */,
				B997728C283616AA00D34173 /* HippyCSSPropsDefine.m */,
				B997728D283616AA00D34173 /* HippyPageModel.m */,
				B997728E283616AA00D34173 /* HippyCSSModel.m */,
				B997728F283616AA00D34173 /* HippyCSSPropsDefine.h */,
				B9977290283616AA00D34173 /* HippyDomModel.m */,
			);
			path = model;
			sourceTree = "<group>";
		};
		B9977291283616AA00D34173 /* domain */ = {
			isa = PBXGroup;
			children = (
				B9977292283616AA00D34173 /* HippyPageDomain.m */,
				B9977293283616AA00D34173 /* HippyInspectorDomain.h */,
				B9977294283616AA00D34173 /* HippyCSSDomain.h */,
				B9977295283616AA00D34173 /* HippyDomDomain.m */,
				B9977296283616AA00D34173 /* HippyPageDomain.h */,
				B9977297283616AA00D34173 /* HippyDomDomain.h */,
				B9977298283616AA00D34173 /* HippyCSSDomain.m */,
				B9977299283616AA00D34173 /* HippyInspectorDomain.m */,
			);
			path = domain;
			sourceTree = "<group>";
		};
		B9ABD8CE283B1C1A00504F28 /* IQToolbar */ = {
			isa = PBXGroup;
			children = (
				B9ABD8CF283B1C1A00504F28 /* IQUIView+IQKeyboardToolbar.h */,
				B9ABD8D0283B1C1A00504F28 /* IQToolbar.m */,
				B9ABD8D1283B1C1A00504F28 /* IQPreviousNextView.h */,
				B9ABD8D2283B1C1A00504F28 /* IQTitleBarButtonItem.m */,
				B9ABD8D3283B1C1A00504F28 /* IQBarButtonItem.m */,
				B9ABD8D4283B1C1A00504F28 /* IQUIView+IQKeyboardToolbar.m */,
				B9ABD8D5283B1C1A00504F28 /* IQPreviousNextView.m */,
				B9ABD8D6283B1C1A00504F28 /* IQToolbar.h */,
				B9ABD8D7283B1C1A00504F28 /* IQBarButtonItem.h */,
				B9ABD8D8283B1C1A00504F28 /* IQTitleBarButtonItem.h */,
			);
			path = IQToolbar;
			sourceTree = "<group>";
		};
		B9ABD8DA283B1C1A00504F28 /* Constants */ = {
			isa = PBXGroup;
			children = (
				B9ABD8DB283B1C1A00504F28 /* IQKeyboardManagerConstantsInternal.h */,
				B9ABD8DC283B1C1A00504F28 /* IQKeyboardManagerConstants.h */,
			);
			path = Constants;
			sourceTree = "<group>";
		};
		B9ABD8DE283B1C1B00504F28 /* Categories */ = {
			isa = PBXGroup;
			children = (
				B9ABD8DF283B1C1B00504F28 /* IQNSArray+Sort.m */,
				B9ABD8E0283B1C1B00504F28 /* IQUIViewController+Additions.h */,
				B9ABD8E1283B1C1B00504F28 /* IQUITextFieldView+Additions.m */,
				B9ABD8E2283B1C1B00504F28 /* IQUIScrollView+Additions.m */,
				B9ABD8E3283B1C1B00504F28 /* IQUIView+Hierarchy.m */,
				B9ABD8E4283B1C1B00504F28 /* IQNSArray+Sort.h */,
				B9ABD8E5283B1C1B00504F28 /* IQUIScrollView+Additions.h */,
				B9ABD8E6283B1C1B00504F28 /* IQUITextFieldView+Additions.h */,
				B9ABD8E7283B1C1B00504F28 /* IQUIViewController+Additions.m */,
				B9ABD8E8283B1C1B00504F28 /* IQUIView+Hierarchy.h */,
			);
			path = Categories;
			sourceTree = "<group>";
		};
		B9ABD8EB283B1C1B00504F28 /* IQTextView */ = {
			isa = PBXGroup;
			children = (
				B9ABD8EC283B1C1B00504F28 /* IQTextView.m */,
				B9ABD8ED283B1C1B00504F28 /* IQTextView.h */,
			);
			path = IQTextView;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				7538E7C88B0973456FDAEBB0 /* [CP] Check Pods Manifest.lock */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				9705A1C41CF9048500538489 /* Embed Frameworks */,
				57CFC42BEDACF2FEEF1BEC4C /* [CP] Embed Pods Frameworks */,
				26F3D547FE0D716166159A38 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0910;
				ORGANIZATIONNAME = "The Chromium Authors";
				TargetAttributes = {
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						DevelopmentTeam = 2AXBKZQ9AH;
						ProvisioningStyle = Manual;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				6632F0F824CEC14A00B1BC5C /* LICENSE.md in Resources */,
				6632F0F924CEC14A00B1BC5C /* README.md in Resources */,
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				1192B0E0241CF7AA007D8797 /* res in Resources */,
				B99770F42836169900D34173 /* .gitignore in Resources */,
				B99770F52836169900D34173 /* .clang-format in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		26F3D547FE0D716166159A38 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		57CFC42BEDACF2FEEF1BEC4C /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		7538E7C88B0973456FDAEBB0 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				664AE9872436003E0063FCC2 /* ABCBaseViewController.m in Sources */,
				B99772FC283616AB00D34173 /* HippyVirtualTextNode.m in Sources */,
				B99772F0283616AB00D34173 /* HippyView.m in Sources */,
				B9977319283616AB00D34173 /* HippyModuleMethod.mm in Sources */,
				B99772B1283616AA00D34173 /* HippyExtAnimationViewParams.m in Sources */,
				665A7306246901AA008C6C61 /* AbcInputAccessoryViewManager.mm in Sources */,
				66AFC5E424DD88F400BB0F83 /* AudioRecorderModule.m in Sources */,
				B99772F2283616AB00D34173 /* HippyBackgroundImageCacheManager.m in Sources */,
				B19A5CDB21CF85C5005E6B11 /* FluwxKeys.m in Sources */,
				B99771162836169A00D34173 /* js_native_turbo.cc in Sources */,
				B19A5CE921CF85D1005E6B11 /* FluwxSubscribeMsgHandler.m in Sources */,
				662AB36A24DCE30F00596BB6 /* OSSModule.m in Sources */,
				35B64AD42689F9EA00CF0190 /* AbcPieChartView.m in Sources */,
				B19A5CEB21CF85D1005E6B11 /* NSStringWrapper.m in Sources */,
				B19A5CEA21CF85D1005E6B11 /* FluwxPaymentHandler.m in Sources */,
				66C17CDD2435B3CC0009D385 /* ReverseListViewDataSource.m in Sources */,
				B99772CD283616AB00D34173 /* HippyShadowTextView.mm in Sources */,
				664AE9442435FEE10063FCC2 /* UIView+ABCAnimation.m in Sources */,
				B99772A8283616AA00D34173 /* HippyTurboModuleManager.mm in Sources */,
				B9977310283616AB00D34173 /* NSDictionary+HippyDictionaryDeepCopy.m in Sources */,
				66137FAC24DBA9C900E44F63 /* NotificationAttachment.m in Sources */,
				B9977322283616AB00D34173 /* HippyKeyCommands.m in Sources */,
				B99772A1283616AA00D34173 /* MTTLayout.cpp in Sources */,
				66137F0724D94B6300E44F63 /* FileUtils.m in Sources */,
				B99772BD283616AB00D34173 /* HippyImageCacheManager.m in Sources */,
				11DA640E2445FCE500DBA6C6 /* WheelViewManager.m in Sources */,
				7D88C50A2E24E511003AFF5A /* AbcAudioRecorder.m in Sources */,
				7D88C50B2E24E511003AFF5A /* AbcAsrManager.m in Sources */,
				7D88C50C2E24E511003AFF5A /* AbcAsrWebSocketClient.m in Sources */,
				7D88C50D2E24E511003AFF5A /* AbcAsrConfig.m in Sources */,
				7D88C50E2E24E511003AFF5A /* AbcAsrBackgroundHandler.m in Sources */,
				7D88C50F2E24E511003AFF5A /* AbcAsrModule.m in Sources */,
				11D552B82436187B0049D7ED /* ABCAnimationCenter.m in Sources */,
				B9ABD8F5283B1C1B00504F28 /* IQNSArray+Sort.m in Sources */,
				66C17CDB2435B3CC0009D385 /* ReverseListItemViewManager.m in Sources */,
				66CBBE8D24DD0422003A9F78 /* ImagePickerModule.m in Sources */,
				B9977335283616AB00D34173 /* HippyCSSDomain.m in Sources */,
				6643498124D29C6400137935 /* AbcLineChartView.m in Sources */,
				B99772C6283616AB00D34173 /* HippyRefresh.m in Sources */,
				112E28CD245C6D0100A5DF37 /* AudioViewManager.m in Sources */,
				660929AF256F3FDC00436093 /* UrlLauncherModule.m in Sources */,
				664AE98A2436010A0063FCC2 /* easing.c in Sources */,
				978B8F6F1D3862AE00F588F7 /* AppDelegate.m in Sources */,
				66137F8A24DA382D00E44F63 /* UrlLanucherUtils.m in Sources */,
				B99772A0283616AA00D34173 /* x5LayoutUtil.m in Sources */,
				B9977314283616AB00D34173 /* HippyVirtualList.m in Sources */,
				B9ABD8F9283B1C1B00504F28 /* IQUIViewController+Additions.m in Sources */,
				B997732D283616AB00D34173 /* HippySRSIMDHelpers.m in Sources */,
				B99771082836169900D34173 /* module_register.cc in Sources */,
				B99771022836169900D34173 /* scope.cc in Sources */,
				116CAFAB24DE56CD00DB4C50 /* NotificationPermissionStrategy.m in Sources */,
				B99772C1283616AB00D34173 /* HippyScrollViewManager.mm in Sources */,
				B19A5CE621CF85D1005E6B11 /* FluwxShareHandler.m in Sources */,
				6610A83324D3DE41006D24A0 /* BarcodeScanManager.m in Sources */,
				66137FAF24DBA9C900E44F63 /* LocalNotificationModule.m in Sources */,
				B997733B283616AB00D34173 /* HippyDevWebSocketClient.m in Sources */,
				B99772AE283616AA00D34173 /* HippyExtAnimationModule.m in Sources */,
				B99772CB283616AB00D34173 /* HippyTextView.mm in Sources */,
				667CE5DA25A2C26600FF6C5A /* PinchImageView.m in Sources */,
				B9977327283616AB00D34173 /* HippyModuleData.mm in Sources */,
				B99772E7283616AB00D34173 /* HippyListTableView.m in Sources */,
				B99772B2283616AB00D34173 /* HippyNetInfoIntenal.m in Sources */,
				B19A5CA421CF7AF1005E6B11 /* FluwxPlugin.m in Sources */,
				B9977309283616AB00D34173 /* NSData+DataType.m in Sources */,
				B997732E283616AB00D34173 /* HippyWebSocketManager.m in Sources */,
				B99772F9283616AB00D34173 /* HippyFooterRefresh.m in Sources */,
				66C17D262435C1F40009D385 /* TouchListenerViewManager.m in Sources */,
				664AE9452435FEE10063FCC2 /* UIUtils.m in Sources */,
				664BC55D24D3A81D00825CF5 /* WXApiHandler.m in Sources */,
				B99772D6283616AB00D34173 /* HippyWaterfallView.m in Sources */,
				116CAFA524DE56CD00DB4C50 /* SensorPermissionStrategy.m in Sources */,
				66CBBE8924DD03D3003A9F78 /* ImagePickerImageUtil.m in Sources */,
				660081462481F3C000761AE3 /* WillPopListenerManager.m in Sources */,
				664BC57824D3C92100825CF5 /* UIButton+TouchArea.m in Sources */,
				66CBBE8724DD03D3003A9F78 /* ImagePickerPhotoAssetUtil.m in Sources */,
				116CAF9E24DE56CD00DB4C50 /* Codec.m in Sources */,
				118F0C8124E8121100EAAD89 /* ShareModule.m in Sources */,
				116CAFA824DE56CD00DB4C50 /* PhonePermissionStrategy.m in Sources */,
				B99772FE283616AB00D34173 /* HippyModalCustomPresentationController.m in Sources */,
				66942ED424E671C000DC1B3A /* AbcWebViewManager.m in Sources */,
				B99771182836169A00D34173 /* js_native_turbo_jsc.cc in Sources */,
				665A72FD2468F7B3008C6C61 /* AbcHippyShadowTextView.mm in Sources */,
				B99772E0283616AB00D34173 /* HippyAnimatedImageView.m in Sources */,
				66C17D252435C1F40009D385 /* TouchListenerView.m in Sources */,
				66DC5C142990C945000F7965 /* AbcScrollView.m in Sources */,
				664BC57624D3C92100825CF5 /* BarcodeScannerViewController.m in Sources */,
				B99772EF283616AB00D34173 /* HippyShadowView.mm in Sources */,
				B19A5CDA21CF85C5005E6B11 /* StringUtil.m in Sources */,
				B99772A7283616AA00D34173 /* HippyOCTurboModule.mm in Sources */,
				B9977321283616AB00D34173 /* HippyJSCWrapper.mm in Sources */,
				1192AE76241CD5E9007D8797 /* HippyViewController.m in Sources */,
				116CAFA724DE56CD00DB4C50 /* PhotoPermissionStrategy.m in Sources */,
				B99772D2283616AB00D34173 /* HippySimpleWebViewManager.m in Sources */,
				B19A5CFD21CF85EF005E6B11 /* WXApiRequestHandler.m in Sources */,
				B9977330283616AB00D34173 /* HippyPageModel.m in Sources */,
				B9977333283616AB00D34173 /* HippyPageDomain.m in Sources */,
				B9977325283616AB00D34173 /* HippyBridge.mm in Sources */,
				B99772D5283616AB00D34173 /* HippyReusableNodeCache.m in Sources */,
				B19A5CCB21CF859D005E6B11 /* CallResults.m in Sources */,
				66B3130624E50E8800F75F77 /* QRViewManager.m in Sources */,
				B99772BF283616AB00D34173 /* HippyClipboardModule.m in Sources */,
				B99772BC283616AB00D34173 /* HippyImageLoaderModule.m in Sources */,
				668E759224370ABD002922A2 /* ABCNavigatorManager.m in Sources */,
				B997731F283616AB00D34173 /* HippyJSCExecutor.mm in Sources */,
				B99770FB2836169900D34173 /* log_settings_state.cc in Sources */,
				B9977336283616AB00D34173 /* HippyInspectorDomain.m in Sources */,
				11B49487243997F80009856A /* ABCViewAnimationController.m in Sources */,
				B99771072836169900D34173 /* common_task.cc in Sources */,
				B99771052836169900D34173 /* javascript_task.cc in Sources */,
				66942EC524E53E9F00DC1B3A /* CaptureView.m in Sources */,
				B99771092836169900D34173 /* console_module.cc in Sources */,
				B997710C2836169900D34173 /* js_value_wrapper.cc in Sources */,
				7DFEBCD52E12A6580024EE75 /* AbcSseEvent.m in Sources */,
				7DFEBCD62E12A6580024EE75 /* AbcSseConnection.m in Sources */,
				B99772CE283616AB00D34173 /* HippyBaseTextInput.m in Sources */,
				B997733D2836268300D34173 /* WillPopListener.m in Sources */,
				B99772FA283616AB00D34173 /* HippyShadowText.mm in Sources */,
				B9ABD8F4283B1C1B00504F28 /* IQKeyboardManager.m in Sources */,
				B99772D0283616AB00D34173 /* HippyTextField.m in Sources */,
				B997730C283616AB00D34173 /* NSNumber+HippyNumberDeepCopy.m in Sources */,
				B99772E1283616AB00D34173 /* HippyImageViewManager.m in Sources */,
				B9977326283616AB00D34173 /* HippyDeviceBaseInfo.m in Sources */,
				11D552BF243618DF0049D7ED /* ABCBaseInteraction.m in Sources */,
				35B64AD22689F9C000CF0190 /* AbcPieChartViewManager.m in Sources */,
				B99772FF283616AB00D34173 /* HippyModalHostViewManager.mm in Sources */,
				116CAFA224DE56CD00DB4C50 /* LocationPermissionStrategy.m in Sources */,
				664BC57524D3C92100825CF5 /* ScannerOverlay.m in Sources */,
				B9977313283616AB00D34173 /* HippyVirtualNode.m in Sources */,
				B997733A283616AB00D34173 /* HippyDevInfo.m in Sources */,
				B99772BA283616AB00D34173 /* HippyDevLoadingView.m in Sources */,
				66D59D0C2481363700EC1077 /* AbcImageLoaderModule.m in Sources */,
				B997710F2836169900D34173 /* task_runner.cc in Sources */,
				B99772A4283616AA00D34173 /* MTTNode.cpp in Sources */,
				B19A5CE521CF85D1005E6B11 /* FluwxLaunchMiniProgramHandler.m in Sources */,
				B9ABD8F8283B1C1B00504F28 /* IQUIView+Hierarchy.m in Sources */,
				B997732C283616AB00D34173 /* HippySRWebSocket.m in Sources */,
				66137F0324D8E9C900E44F63 /* AbcNetwork.m in Sources */,
				B99772A2283616AA00D34173 /* MTTLayoutCache.cpp in Sources */,
				B19A5CD921CF85C5005E6B11 /* ImageSchema.m in Sources */,
				B99772C3283616AB00D34173 /* HippyRefreshWrapperViewManager.m in Sources */,
				B99772DA283616AB00D34173 /* HippyWaterfallViewManager.m in Sources */,
				B99770FA2836169900D34173 /* logging.cc in Sources */,
				B99772B6283616AB00D34173 /* HippyEventObserverModule.m in Sources */,
				B99772EA283616AB00D34173 /* UIView+AppearEvent.m in Sources */,
				B99772B5283616AB00D34173 /* ios_loader.cc in Sources */,
				B99772B3283616AB00D34173 /* HippyNetInfo.m in Sources */,
				B99772D9283616AB00D34173 /* HippyCollectionViewWaterfallLayout.m in Sources */,
				665A72FE2468F7B3008C6C61 /* AbcHippyTextSelection.m in Sources */,
				B99772C5283616AB00D34173 /* HippyRefreshWrapperItemViewManager.m in Sources */,
				66942EC624E53E9F00DC1B3A /* CaptureViewManager.m in Sources */,
				66137FBF24DBE04000E44F63 /* AbcBaseAppDelegate.m in Sources */,
				6643498024D29C6400137935 /* AbcLineChartViewManager.m in Sources */,
				B99772E5283616AB00D34173 /* HippyBaseListItemView.m in Sources */,
				B99772F7283616AB00D34173 /* HippyNavigatorViewManager.m in Sources */,
				66C17CDC2435B3CC0009D385 /* ReverseListView.m in Sources */,
				B9977307283616AB00D34173 /* HippyConvert+Transform.m in Sources */,
				B99772FD283616AB00D34173 /* HippyTextManager.mm in Sources */,
				B99771042836169900D34173 /* javascript_task_runner.cc in Sources */,
				B99772C9283616AB00D34173 /* HippyViewPagerItem.m in Sources */,
				665A73002468F7B3008C6C61 /* AbcHippyTextViewManager.mm in Sources */,
				B99772AF283616AA00D34173 /* CALayer+HippyAnimation.m in Sources */,
				112E28CC245C6D0100A5DF37 /* AudioView.m in Sources */,
				B9977331283616AB00D34173 /* HippyCSSModel.m in Sources */,
				B9ABD8F1283B1C1B00504F28 /* IQUIView+IQKeyboardToolbar.m in Sources */,
				66DC5C132990C945000F7965 /* AbcScrollViewManager.mm in Sources */,
				B99772F8283616AB00D34173 /* HippyFooterRefreshManager.m in Sources */,
				668E759424370ABD002922A2 /* ABCNavigatorItemManager.m in Sources */,
				66137FAE24DBA9C900E44F63 /* NotificationDetails.m in Sources */,
				667A64C224B746B30090382A /* AbcConfig.m in Sources */,
				118E723128F2C27B0079006F /* SVGViewManager.m in Sources */,
				B9977308283616AB00D34173 /* NSArray+HippyArrayDeepCopy.m in Sources */,
				66C17CD92435B3CC0009D385 /* ReverseListViewManager.m in Sources */,
				660081492481F4C400761AE3 /* UIView+ABCExt.m in Sources */,
				B99772F3283616AB00D34173 /* HippyNavigatorRootViewController.m in Sources */,
				11DA64112445FCE500DBA6C6 /* WheelView.m in Sources */,
				11D552CA243625280049D7ED /* HostHippyMessageBridge.m in Sources */,
				97C146F31CF9000F007C117D /* main.m in Sources */,
				B9977306283616AB00D34173 /* HippyLog.mm in Sources */,
				665A72FB2468F7B3008C6C61 /* AbcHippyTextView.m in Sources */,
				B99772F1283616AB00D34173 /* HippyBorderDrawing.m in Sources */,
				66B3130724E50E8800F75F77 /* QRView.m in Sources */,
				B9977318283616AB00D34173 /* HippyEventDispatcher.m in Sources */,
				B9977332283616AB00D34173 /* HippyDomModel.m in Sources */,
				B997711A2836169A00D34173 /* js_native_api_value_jsc.cc in Sources */,
				B9977302283616AB00D34173 /* HippyModalHostView.m in Sources */,
				66942ECE24E622B700DC1B3A /* WebViewUtilsModule.m in Sources */,
				B9977304283616AB00D34173 /* HippyParserUtils.m in Sources */,
				B9977339283616AB00D34173 /* HippyDevManager.m in Sources */,
				B99772FB283616AB00D34173 /* HippyText.mm in Sources */,
				B99771112836169900D34173 /* thread.cc in Sources */,
				66220EB82447E96B00DA8A34 /* FileManagerModule.m in Sources */,
				B997710A2836169900D34173 /* timer_module.cc in Sources */,
				B99771102836169900D34173 /* task.cc in Sources */,
				11D552C7243623060049D7ED /* ABCBaseNavigationController.m in Sources */,
				116CAF9F24DE56CD00DB4C50 /* PermissionManager.m in Sources */,
				B99772E6283616AB00D34173 /* HippyBaseListViewManager.m in Sources */,
				B9977300283616AB00D34173 /* HippyModalTransitioningDelegate.mm in Sources */,
				B99772B8283616AB00D34173 /* HippyDevMenu.mm in Sources */,
				B9977305283616AB00D34173 /* HippyGradientObject.m in Sources */,
				B99772A5283616AA00D34173 /* MTTFlexLine.cpp in Sources */,
				B9977328283616AB00D34173 /* HippyBridge+Mtt.mm in Sources */,
				B19A5CFF21CF85EF005E6B11 /* FluwxResponseHandler.m in Sources */,
				11D552BE243618DF0049D7ED /* ABCBaseAnimation.m in Sources */,
				B9977303283616AB00D34173 /* HippyModalCustomAnimationTransition.m in Sources */,
				B9ABD8F7283B1C1B00504F28 /* IQUIScrollView+Additions.m in Sources */,
				116CAFAC24DE56CD00DB4C50 /* ContactPermissionStrategy.m in Sources */,
				112E2920245D0C4E00A5DF37 /* AudioPlayer.m in Sources */,
				B99772E2283616AB00D34173 /* HippyImageCache.m in Sources */,
				667A64BB24B725700090382A /* WxApiHostModule.m in Sources */,
				667CE5D925A2C26600FF6C5A /* PinchImageViewManager.m in Sources */,
				B19A5CDC21CF85C5005E6B11 /* FluwxMethods.m in Sources */,
				6608379624E4DA0400530B70 /* StatManagerModule.m in Sources */,
				B99772AC283616AA00D34173 /* HippyExtAnimation+Value.m in Sources */,
				66413FFA2448618A0029D998 /* TouchListenerViewTouchHandler.m in Sources */,
				66137FC224DBF16800E44F63 /* XGPushModule.m in Sources */,
				B99772AD283616AA00D34173 /* HippyExtAnimation.m in Sources */,
				B99772DC283616AB00D34173 /* HippyImageProviderProtocol.m in Sources */,
				B19A5CFE21CF85EF005E6B11 /* WXMediaMessage+messageConstruct.m in Sources */,
				116CAFA424DE56CD00DB4C50 /* StoragePermissionStrategy.m in Sources */,
				B997731E283616AB00D34173 /* HippyBatchedBridge.mm in Sources */,
				B9977301283616AB00D34173 /* HippyModalHostViewController.m in Sources */,
				B997730E283616AB00D34173 /* HippyErrorInfo.m in Sources */,
				B19A5CD721CF85C5005E6B11 /* StringToWeChatScene.m in Sources */,
				B99772D4283616AB00D34173 /* HippyHeaderRefreshManager.m in Sources */,
				118E723028F2C27B0079006F /* SVGView.m in Sources */,
				B99771152836169900D34173 /* callback_info.cc in Sources */,
				B99772C0283616AB00D34173 /* HippyScrollView.m in Sources */,
				66942ECA24E548FA00DC1B3A /* GallerySaverModule.m in Sources */,
				B99772ED283616AB00D34173 /* UIView+Hippy.mm in Sources */,
				665D782524F5E6B2000DF0F7 /* AliPayModule.m in Sources */,
				11B4948A243998160009856A /* ABCViewControllerAnimationController.m in Sources */,
				B99772CF283616AB00D34173 /* HippyTextSelection.m in Sources */,
				B997731D283616AB00D34173 /* HippyJavaScriptLoader.mm in Sources */,
				B99772DE283616AB00D34173 /* HippyAnimatedImage.m in Sources */,
				B99772A9283616AA00D34173 /* NSObject+HippyTurbo.mm in Sources */,
				B99772D3283616AB00D34173 /* HippyHeaderRefresh.m in Sources */,
				B99770FE2836169900D34173 /* unicode_string_view.cc in Sources */,
				B99772C7283616AB00D34173 /* HippyViewPager.m in Sources */,
				B99772BB283616AB00D34173 /* HippyExceptionModule.m in Sources */,
				B19A5CE821CF85D1005E6B11 /* FluwxAuthHandler.m in Sources */,
				B99772F6283616AB00D34173 /* HippyNavigationControllerAnimator.m in Sources */,
				B9ABD8F2283B1C1B00504F28 /* IQPreviousNextView.m in Sources */,
				665A72FF2468F7B3008C6C61 /* AbcHippyTextField.m in Sources */,
				66942ED524E671C000DC1B3A /* AbcWebView.m in Sources */,
				B9977317283616AB00D34173 /* HippyJSStackFrame.m in Sources */,
				B99772C8283616AB00D34173 /* HippyViewPagerManager.m in Sources */,
				B997732A283616AB00D34173 /* HippyDisplayLink.m in Sources */,
				B99772EB283616AB00D34173 /* UIView+HippyAnimationProtocol.m in Sources */,
				B99772CC283616AB00D34173 /* HippyTextViewManager.mm in Sources */,
				66137F0224D8E9C900E44F63 /* AbcFetchInfo.m in Sources */,
				B99772D7283616AB00D34173 /* HippyWaterfallItemViewManager.m in Sources */,
				B9977316283616AB00D34173 /* HippyTouchHandler.m in Sources */,
				664BC56324D3C57400825CF5 /* HippyMixInvokeMethodModule.m in Sources */,
				B99772BE283616AB00D34173 /* HippyUIManager.mm in Sources */,
				66137F8E24DA819D00E44F63 /* SocketIOModule.m in Sources */,
				66CBBE8624DD03D3003A9F78 /* ImagePickerMetaDataUtil.m in Sources */,
				6690F68E24F632F200F91E26 /* AudioPlayerMananger.m in Sources */,
				B99772EE283616AB00D34173 /* HippyViewManager.mm in Sources */,
				66E2335525418FD700135BE5 /* BlurEffectWithAmount.m in Sources */,
				6643498524D2C23D00137935 /* DateValueFormatter.m in Sources */,
				B99772F4283616AB00D34173 /* HippyNavigatorItemViewController.m in Sources */,
				B997730B283616AB00D34173 /* HippyI18nUtils.m in Sources */,
				66BF71E028F54D1B00EE457A /* LruCache.m in Sources */,
				66BF71DC28F54B0A00EE457A /* SVGManager.m in Sources */,
				B99772E9283616AB00D34173 /* HippyBaseListViewCell.m in Sources */,
				B997731A283616AB00D34173 /* HippyRootView.mm in Sources */,
				B9977337283616AB00D34173 /* HippyDevCommand.m in Sources */,
				B99772D1283616AB00D34173 /* HippySimpleWebView.m in Sources */,
				665A7305246901AA008C6C61 /* AbcInputAccessoryView.m in Sources */,
				B9977324283616AB00D34173 /* HippyBridge+LocalFileSource.m in Sources */,
				B997710B2836169900D34173 /* contextify_module.cc in Sources */,
				668E759324370ABD002922A2 /* ABCNavigator.m in Sources */,
				66E2335625418FD700135BE5 /* BlurView.m in Sources */,
				668E759124370ABD002922A2 /* ABCNavigatorItem.m in Sources */,
				B997710D2836169900D34173 /* file.cc in Sources */,
				116CAFA024DE56CD00DB4C50 /* PermissionHandlerModule.m in Sources */,
				66137FAD24DBA9C900E44F63 /* NotificationTime.m in Sources */,
				66C17CDA2435B3CC0009D385 /* ReverseListItemView.m in Sources */,
				6625AA0F246D222200E55AE9 /* AbcHippyTextViewImplManager.mm in Sources */,
				66C17D292435D43B0009D385 /* DebugView.m in Sources */,
				66E2335725418FD700135BE5 /* BlurViewManager.m in Sources */,
				66137F8524D9836E00E44F63 /* AppInfo.m in Sources */,
				B99772B4283616AB00D34173 /* HippyTiming.mm in Sources */,
				B997730D283616AB00D34173 /* HippyConvert.mm in Sources */,
				662AB37024DCE57F00596BB6 /* OssManager.mm in Sources */,
				B99772E3283616AB00D34173 /* HippyBaseListItemViewManager.m in Sources */,
				6608379A24E4DEC100530B70 /* BuglyModule.m in Sources */,
				B9ABD8F6283B1C1B00504F28 /* IQUITextFieldView+Additions.m in Sources */,
				B997732B283616AB00D34173 /* HippyRootShadowView.mm in Sources */,
				665A72FC2468F7B3008C6C61 /* AbcHippyBaseTextInput.m in Sources */,
				B99772E8283616AB00D34173 /* HippyBaseListView.m in Sources */,
				B9977312283616AB00D34173 /* HippyPerformanceLogger.m in Sources */,
				B9977334283616AB00D34173 /* HippyDomDomain.m in Sources */,
				B99771062836169900D34173 /* worker_task_runner.cc in Sources */,
				664AE9432435FEE10063FCC2 /* CAKeyframeAnimation+AHEasing.m in Sources */,
				B99772C2283616AB00D34173 /* HippyRefreshWrapperItemView.m in Sources */,
				B9977323283616AB00D34173 /* HippyFont.mm in Sources */,
				B9ABD8EE283B1C1B00504F28 /* IQToolbar.m in Sources */,
				B99772C4283616AB00D34173 /* HippyRefreshWrapper.m in Sources */,
				116CAFA624DE56CD00DB4C50 /* SpeechPermissionStrategy.m in Sources */,
				B9ABD8F3283B1C1B00504F28 /* IQKeyboardReturnKeyHandler.m in Sources */,
				B9977338283616AB00D34173 /* HippyInspector.m in Sources */,
				B997730F283616AB00D34173 /* HippyUtils.m in Sources */,
				B99772B0283616AA00D34173 /* HippyExtAnimationGroup.m in Sources */,
				B99772B9283616AB00D34173 /* HippyRedBox.m in Sources */,
				B99772D8283616AB00D34173 /* HippyWaterfallItemView.m in Sources */,
				662AB36F24DCE57F00596BB6 /* OssFileUploader.mm in Sources */,
				6601D12424AC41D300BC67C0 /* AbcCustomKeyboardView.m in Sources */,
				B99770FD2836169900D34173 /* log_settings.cc in Sources */,
				B997730A283616AB00D34173 /* HippyAssert.m in Sources */,
				6601D12124AC417500BC67C0 /* AbcCustomKeyboardViewManager.mm in Sources */,
				B9ABD8FA283B1C1B00504F28 /* IQTextView.m in Sources */,
				B99771032836169900D34173 /* engine.cc in Sources */,
				6625AA09246D20F200E55AE9 /* AbcHippyTextViewImpl.mm in Sources */,
				B99772B7283616AB00D34173 /* HippyAsyncLocalStorage.m in Sources */,
				116CAFA324DE56CD00DB4C50 /* EventPermissionStrategy.m in Sources */,
				B9ABD8EF283B1C1B00504F28 /* IQTitleBarButtonItem.m in Sources */,
				B9977329283616AB00D34173 /* HippyFrameUpdate.m in Sources */,
				116CAFA124DE56CD00DB4C50 /* MediaLibraryPermissionStrategy.m in Sources */,
				B997732F283616AB00D34173 /* HippyCSSPropsDefine.m in Sources */,
				117A558724DED67700E424CA /* BarcodeScanModule.m in Sources */,
				66AFC5E824E2E7AA00BB0F83 /* ThirdCallModule.m in Sources */,
				664BC57924D3C92100825CF5 /* TestFontViewController.m in Sources */,
				B99772CA283616AB00D34173 /* HippyViewPagerItemManager.m in Sources */,
				B99772EC283616AB00D34173 /* HippyView+HippyViewAnimation.m in Sources */,
				B99772E4283616AB00D34173 /* HippyBaseListViewDataSource.m in Sources */,
				B9ABD8F0283B1C1B00504F28 /* IQBarButtonItem.m in Sources */,
				B99772DD283616AB00D34173 /* UIImageView+Hippy.m in Sources */,
				B99771172836169A00D34173 /* js_native_jsc_helper.cc in Sources */,
				B997711B2836169A00D34173 /* js_native_api_jsc.cc in Sources */,
				B997710E2836169900D34173 /* thread_id.cc in Sources */,
				B99771192836169A00D34173 /* native_source_code_ios.cc in Sources */,
				B997731C283616AB00D34173 /* HippyJSEnginesMapper.mm in Sources */,
				B99772A6283616AA00D34173 /* MTTUtil.cpp in Sources */,
				B997731B283616AB00D34173 /* HippyBundleURLProvider.m in Sources */,
				B9977320283616AB00D34173 /* HippyJSCErrorHandling.m in Sources */,
				66137F8124D9808F00E44F63 /* PluginUtils.m in Sources */,
				B99772A3283616AA00D34173 /* MTTTStyle.cpp in Sources */,
				B99772AA283616AA00D34173 /* HippyNetWork.m in Sources */,
				B99772DB283616AB00D34173 /* HippyImageView.m in Sources */,
				B99772AB283616AA00D34173 /* HippyFetchInfo.m in Sources */,
				116CAFA924DE56CD00DB4C50 /* AudioVideoPermissionStrategy.m in Sources */,
				B99772DF283616AB00D34173 /* HippyDefaultImageProvider.m in Sources */,
				116CAFAA24DE56CD00DB4C50 /* UnknownPermissionStrategy.m in Sources */,
				B9977315283616AB00D34173 /* HippyComponentData.mm in Sources */,
				B99772F5283616AB00D34173 /* HippyNavigatorHostView.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A18C85A7F05F610A7BC5E5ED /* Pods-Runner.profile.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 0100;
				DEVELOPMENT_TEAM = 2AXBKZQ9AH;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 2AXBKZQ9AH;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/third-party/tpns",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AliyunOSSiOS-framework/AliyunOSSiOS.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Charts-framework/Charts.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/KVOController-framework/KVOController.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MTBBarcodeScanner-framework/MTBBarcodeScanner.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage-framework/SDWebImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive-framework/SSZipArchive.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Socket.IO-Client-Swift-framework/SocketIO.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Starscream-framework/Starscream.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/AliyunOSSiOS\"",
					"\"${PODS_ROOT}/Headers/Public/KVOController\"",
					"\"${PODS_ROOT}/Headers/Public/MTBBarcodeScanner\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SSZipArchive\"",
					"\"${PODS_ROOT}/Headers/Public/WechatOpenSDK\"",
					"$(PROJECT_DIR)/third-party/tpns/",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 9.3;
				LD_RUNPATH_SEARCH_PATHS = "/usr/lib/swift $(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
					"$(PROJECT_DIR)/third-party/tpns",
				);
				MARKETING_VERSION = 2.8.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"sqlite3\"",
					"-l\"sqlite3.0\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"AlipaySDK\"",
					"-framework",
					"\"AliyunOSSiOS\"",
					"-framework",
					"\"Bugly\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"Charts\"",
					"-framework",
					"\"CocoaLumberjack\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"KVOController\"",
					"-framework",
					"\"MTBBarcodeScanner\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SSZipArchive\"",
					"-framework",
					"\"SVGKit\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SocketIO\"",
					"-framework",
					"\"Starscream\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"UMAnalytics\"",
					"-framework",
					"\"UMCommon\"",
					"-framework",
					"\"UMCommonLog\"",
					"-framework",
					"\"UMErrorCatch\"",
					"-framework",
					"\"WebKit\"",
					"-ld64",
				);
				PRODUCT_BUNDLE_IDENTIFIER = cn.abcyun.clinic.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = ABCClinicDev;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = ABCClinicDev;
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = "\"$(SRCROOT)/Runner/SubProject/hippy/engine\" \"$(SRCROOT)/Runner/SubProject/hippy/engine/core/include\" \"$(SRCROOT)/Runner/SubProject/hippy/engine/core/third_party/base/include\"";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 41AC21A3B6B5805CB6B80A8C /* Pods-Runner.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerDebug.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 0100;
				DEVELOPMENT_TEAM = 2AXBKZQ9AH;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 2AXBKZQ9AH;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/third-party/tpns",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AliyunOSSiOS-framework/AliyunOSSiOS.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Charts-framework/Charts.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/KVOController-framework/KVOController.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MTBBarcodeScanner-framework/MTBBarcodeScanner.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage-framework/SDWebImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive-framework/SSZipArchive.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Socket.IO-Client-Swift-framework/SocketIO.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Starscream-framework/Starscream.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/AliyunOSSiOS\"",
					"\"${PODS_ROOT}/Headers/Public/KVOController\"",
					"\"${PODS_ROOT}/Headers/Public/MTBBarcodeScanner\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SSZipArchive\"",
					"\"${PODS_ROOT}/Headers/Public/WechatOpenSDK\"",
					"$(PROJECT_DIR)/third-party/tpns/",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 9.3;
				LD_RUNPATH_SEARCH_PATHS = "/usr/lib/swift $(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/third-party/tpns",
				);
				MARKETING_VERSION = 2.8.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"sqlite3\"",
					"-l\"sqlite3.0\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"AlipaySDK\"",
					"-framework",
					"\"AliyunOSSiOS\"",
					"-framework",
					"\"Bugly\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"Charts\"",
					"-framework",
					"\"CocoaLumberjack\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"KVOController\"",
					"-framework",
					"\"MTBBarcodeScanner\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SSZipArchive\"",
					"-framework",
					"\"SVGKit\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SocketIO\"",
					"-framework",
					"\"Starscream\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"UMAnalytics\"",
					"-framework",
					"\"UMCommon\"",
					"-framework",
					"\"UMCommonLog\"",
					"-framework",
					"\"UMErrorCatch\"",
					"-framework",
					"\"WebKit\"",
					"-ld64",
				);
				PRODUCT_BUNDLE_IDENTIFIER = cn.abcyun.clinic.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = ABCClinicDev;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = ABCClinicDev;
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = "\"$(SRCROOT)/Runner/SubProject/hippy/engine\" \"$(SRCROOT)/Runner/SubProject/hippy/engine/core/include\" \"$(SRCROOT)/Runner/SubProject/hippy/engine/core/third_party/base/include\"";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2E56DFACB575C483B1265A4E /* Pods-Runner.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerRelease.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 0100;
				DEVELOPMENT_TEAM = 2AXBKZQ9AH;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 2AXBKZQ9AH;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/third-party/tpns",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AliyunOSSiOS-framework/AliyunOSSiOS.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Charts-framework/Charts.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/KVOController-framework/KVOController.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MTBBarcodeScanner-framework/MTBBarcodeScanner.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage-framework/SDWebImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive-framework/SSZipArchive.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Socket.IO-Client-Swift-framework/SocketIO.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Starscream-framework/Starscream.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/AliyunOSSiOS\"",
					"\"${PODS_ROOT}/Headers/Public/KVOController\"",
					"\"${PODS_ROOT}/Headers/Public/MTBBarcodeScanner\"",
					"\"${PODS_ROOT}/Headers/Public/SDWebImage\"",
					"\"${PODS_ROOT}/Headers/Public/SSZipArchive\"",
					"\"${PODS_ROOT}/Headers/Public/WechatOpenSDK\"",
					"$(PROJECT_DIR)/third-party/tpns/",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 9.3;
				LD_RUNPATH_SEARCH_PATHS = "/usr/lib/swift $(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/third-party/tpns",
				);
				MARKETING_VERSION = 2.8.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"sqlite3\"",
					"-l\"sqlite3.0\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"AlipaySDK\"",
					"-framework",
					"\"AliyunOSSiOS\"",
					"-framework",
					"\"Bugly\"",
					"-framework",
					"\"CFNetwork\"",
					"-framework",
					"\"Charts\"",
					"-framework",
					"\"CocoaLumberjack\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"KVOController\"",
					"-framework",
					"\"MTBBarcodeScanner\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SSZipArchive\"",
					"-framework",
					"\"SVGKit\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SocketIO\"",
					"-framework",
					"\"Starscream\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"UMAnalytics\"",
					"-framework",
					"\"UMCommon\"",
					"-framework",
					"\"UMCommonLog\"",
					"-framework",
					"\"UMErrorCatch\"",
					"-framework",
					"\"WebKit\"",
					"-ld64",
				);
				PRODUCT_BUNDLE_IDENTIFIER = cn.abcyun.clinic.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = ABCClinicDev;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = AdHocTest;
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = "\"$(SRCROOT)/Runner/SubProject/hippy/engine\" \"$(SRCROOT)/Runner/SubProject/hippy/engine/core/include\" \"$(SRCROOT)/Runner/SubProject/hippy/engine/core/third_party/base/include\"";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
