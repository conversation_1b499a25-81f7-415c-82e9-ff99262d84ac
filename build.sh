#!/bin/bash

git stash && git pull --rebase && git stash

preBuildNo=0000

if [ -f 'buildNum.txt' ]; then
    preBuildNo=`cat buildNum.txt`;
fi

buildNum=`expr ${preBuildNo} + 0`
buildNum=`printf "%04d" $buildNum`


echo ${buildNum} > buildNum.txt



versionName=2.8.5.${buildNum}

echo "versionName=${versionName}"

#test|release
buildPlan=release

tagPrefix=t
tagFolder=test
if [ "$buildPlan" == "test" ]; then
    tagPrefix=t
    tagFolder=test
elif [ "$buildPlan" == "release" ]; then
    tagPrefix=v
    tagFolder=release
fi



tagName="${tagFolder}/$tagPrefix$versionName"
versionCode=${versionName//./}


#rm -rf build
rm -rf android/build
rm -rf dest

mkdir dest

timestamp=$(date "+%Y%m%d%H%M%S")

export versionName=$versionName

cd android

gradle assembleRelease

if [ $? == 0 ]; then
    cp build/app/outputs/mapping/release/mapping.txt ../dest/mapping_${fullVersionName}_$timestamp.txt
    apkName="abcyun_clinic_${versionName}_$timestamp.apk"
    cp build/app/outputs/apk/release/app-release.apk ../dest/${apkName}

    echo "打包成功，apk: ${apkName}"
#    git tag -d "${tagName}"
#    git tag -a "${tagName}" -m "${buildComment}"

    echo "tag ${tagName} 创建成功"

    #curl -F "file=@dest/$apkName" -F '_api_key=1c4cb6237f1420166a3040ff14c53fa3' https://www.pgyer.com/apiv2/app/upload
fi



cd ..

osascript -e 'display dialog "Android打包完成" buttons "Yes" with icon note'

open dest
