# Miscellaneous
*.class
*.lock
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
.flutter-plugins-dependencies

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# Visual Studio Code related
.vscode/

# Flutter/Dart/Pub related
**/doc/api/
.dart_tool/
.flutter-plugins
.packages
.pub-cache/
.pub/
build/
dest/
dest_ios/
buildNum.txt


# Android related
**/android/**/gradle-wrapper.jar
**/android/.gradle
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.java

# iOS/XCode related
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*
ios/Flutter/flutter_export_environment.sh

# Harmony related
.nyc_output
.vscode
.vs
.idea
.gradle
.externalNativeBuild
.cxx

./hos/build
./hos/coverage
./hos/dist
./hos/node_modules
./hos/xcuserdata
./hos/v8forios

./hos/*.log
./hos/*.iml
./hos/local.properties
./hos/maven-auth.properties

./hos/.hvigor/
./hos/oh_modules/*
./hos/oh-package-lock.json5
./hos/.clang-format
./hos/.clang-tidy
./hos/.clangd
./hos/BuildProfile.ets

# Exceptions to above rules.
!**/ios/**/default.mode1v3
!**/ios/**/default.mode2v3
!**/ios/**/default.pbxuser
!**/ios/**/default.perspectivev3
!/packages/flutter_tools/test/data/dart_dependencies_test/**/.packages

.gradle/

tools/channel_apks/output/

hippy-react/node_modules
hippy-react/dist

hippy-react/third-party/@hippy/react/node_modules

.gradletasknamecache