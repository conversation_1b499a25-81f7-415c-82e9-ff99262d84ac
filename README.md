[toc]



# 构建正式版本
## IOS
由于xcode 14.3之后打包报错，临时个性Pods-Runner-frameworks.sh里的readlink 添加参数

```
  if [ -L "${source}" ]; then
    echo "Symlinked..."
    source="$(readlink -f "${source}")"
  fi
```

```bash
$ ./build_ios.sh
```





## protocol buffer
** 安装protoc **
```bash
 $ brew install protobuf
 ```

## 生成应用图标
```bash
# https://github.com/dwmkerr/app-icon
$ app-icon generate
```




 ## ios 符号还源
 atos -o build/dSYMs.noindex/App.framework.dSYM/Contents/Resources/DWARF/App -arch arm64 -l 0x100a98000 0x0000000100aa3c7c

