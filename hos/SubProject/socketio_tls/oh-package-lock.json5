{"meta": {"stableOrder": true, "enableUnifiedLockfile": false}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@types/libclient_socket_c.so@src/main/cpp/types/libentry": "@types/libclient_socket_c.so@src/main/cpp/types/libentry"}, "packages": {"@types/libclient_socket_c.so@src/main/cpp/types/libentry": {"name": "@types/libclient_socket_c.so", "version": "2.1.0", "resolved": "src/main/cpp/types/libentry", "registryType": "local"}}}