<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="6254" systemVersion="14C109" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" initialViewController="FVA-Ip-uIE">
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="6247"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="4B5-FI-8YR">
            <objects>
                <viewController id="FVA-Ip-uIE" customClass="CRViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="j56-Sc-Uve">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="480"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="44" sectionHeaderHeight="22" sectionFooterHeight="22" id="Zd9-dA-z1N">
                                <rect key="frame" x="0.0" y="20" width="320" height="396"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                <prototypes>
                                    <tableViewCell contentMode="scaleToFill" selectionStyle="blue" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" reuseIdentifier="Msg" textLabel="c7q-ly-Hf0" style="IBUITableViewCellStyleDefault" id="13Y-SW-wci">
                                        <rect key="frame" x="0.0" y="22" width="320" height="44"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="13Y-SW-wci" id="CkG-vX-yh5">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="left" text="Title" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" id="c7q-ly-Hf0">
                                                    <rect key="frame" x="15" y="0.0" width="290" height="43"/>
                                                    <autoresizingMask key="autoresizingMask"/>
                                                    <fontDescription key="fontDescription" name="HelveticaNeue-Light" family="Helvetica Neue" pointSize="18"/>
                                                    <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="FVA-Ip-uIE" id="gbf-ws-uXu"/>
                                    <outlet property="delegate" destination="FVA-Ip-uIE" id="Y5j-aM-c5q"/>
                                </connections>
                            </tableView>
                            <view contentMode="scaleToFill" id="yh5-ME-DKM">
                                <rect key="frame" x="0.0" y="416" width="320" height="64"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                <subviews>
                                    <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="Type here..." minimumFontSize="17" id="Eq9-78-qhY">
                                        <rect key="frame" x="8" y="26" width="250" height="30"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                                        <fontDescription key="fontDescription" name="HelveticaNeue-Light" family="Helvetica Neue" pointSize="14"/>
                                        <textInputTraits key="textInputTraits" returnKeyType="send"/>
                                        <connections>
                                            <outlet property="delegate" destination="FVA-Ip-uIE" id="tUP-2R-seQ"/>
                                        </connections>
                                    </textField>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="wwz-tj-MLo">
                                        <rect key="frame" x="8" y="3" width="250" height="21"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                                        <fontDescription key="fontDescription" name="HelveticaNeue-Light" family="Helvetica Neue" pointSize="14"/>
                                        <color key="textColor" white="0.33333333333333331" alpha="1" colorSpace="calibratedWhite"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" id="WZS-UQ-rXy">
                                        <rect key="frame" x="266" y="30" width="46" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                        <fontDescription key="fontDescription" name="HelveticaNeue-Light" family="Helvetica Neue" pointSize="18"/>
                                        <state key="normal" title="Send">
                                            <color key="titleColor" white="0.0" alpha="1" colorSpace="calibratedWhite"/>
                                            <color key="titleShadowColor" white="0.5" alpha="1" colorSpace="calibratedWhite"/>
                                        </state>
                                        <connections>
                                            <action selector="onSend:" destination="FVA-Ip-uIE" eventType="touchUpInside" id="0VT-Nd-fdz"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                        <color key="tintColor" white="0.0" alpha="1" colorSpace="calibratedWhite"/>
                    </view>
                    <extendedEdge key="edgesForExtendedLayout" bottom="YES"/>
                    <connections>
                        <outlet property="loginPage" destination="FDy-KL-ozA" id="zBO-Gk-9OU"/>
                        <outlet property="messageArea" destination="yh5-ME-DKM" id="nFQ-Zd-Gmx"/>
                        <outlet property="messageField" destination="Eq9-78-qhY" id="HDE-p4-jGM"/>
                        <outlet property="nickName" destination="Xat-ec-Oga" id="xyE-A7-aZ5"/>
                        <outlet property="sendBtn" destination="WZS-UQ-rXy" id="nbZ-e0-RoK"/>
                        <outlet property="tableView" destination="Zd9-dA-z1N" id="Ekd-Is-nd5"/>
                        <outlet property="typingLabel" destination="wwz-tj-MLo" id="GxR-Bb-TGT"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="X4s-OP-QGg" userLabel="First Responder" sceneMemberID="firstResponder"/>
                <view contentMode="scaleToFill" id="FDy-KL-ozA">
                    <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Your Nickname:" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="Vbn-qK-OOg">
                            <rect key="frame" x="40" y="200" width="240" height="50"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                            <fontDescription key="fontDescription" name="HelveticaNeue-Light" family="Helvetica Neue" pointSize="20"/>
                            <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" minimumFontSize="17" id="Xat-ec-Oga">
                            <rect key="frame" x="40" y="269" width="240" height="30"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                            <fontDescription key="fontDescription" name="HelveticaNeue-Light" family="Helvetica Neue" pointSize="16"/>
                            <textInputTraits key="textInputTraits" returnKeyType="done"/>
                            <connections>
                                <outlet property="delegate" destination="FVA-Ip-uIE" id="fDd-Hi-o32"/>
                            </connections>
                        </textField>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="1" colorSpace="calibratedWhite"/>
                </view>
            </objects>
            <point key="canvasLocation" x="99" y="-451"/>
        </scene>
    </scenes>
    <simulatedMetricsContainer key="defaultSimulatedMetrics">
        <simulatedStatusBarMetrics key="statusBar"/>
        <simulatedOrientationMetrics key="orientation"/>
        <simulatedScreenMetrics key="destination"/>
    </simulatedMetricsContainer>
</document>
