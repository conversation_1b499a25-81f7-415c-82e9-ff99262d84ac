.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "DTLSV1_LISTEN 3"
.TH DTLSV1_LISTEN 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
SSL_stateless, DTLSv1_listen \&\- Statelessly listen for incoming connections
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_stateless(SSL *s);
\& int DTLSv1_listen(SSL *ssl, BIO_ADDR *peer);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fBSSL_stateless()\fR statelessly listens for new incoming TLSv1.3 connections.
\&\fBDTLSv1_listen()\fR statelessly listens for new incoming \s-1DTLS\s0 connections. If a
ClientHello is received that does not contain a cookie, then they respond with a
request for a new ClientHello that does contain a cookie. If a ClientHello is
received with a cookie that is verified then the function returns in order to
enable the handshake to be completed (for example by using \fBSSL_accept()\fR).
.SH "NOTES"
.IX Header "NOTES"
Some transport protocols (such as \s-1UDP\s0) can be susceptible to amplification
attacks. Unlike \s-1TCP\s0 there is no initial connection setup in \s-1UDP\s0 that
validates that the client can actually receive messages on its advertised source
address. An attacker could forge its source \s-1IP\s0 address and then send handshake
initiation messages to the server. The server would then send its response to
the forged source \s-1IP.\s0 If the response messages are larger than the original
message then the amplification attack has succeeded.
.PP
If \s-1DTLS\s0 is used over \s-1UDP\s0 (or any datagram based protocol that does not validate
the source \s-1IP\s0) then it is susceptible to this type of attack. TLSv1.3 is
designed to operate over a stream-based transport protocol (such as \s-1TCP\s0).
If \s-1TCP\s0 is being used then there is no need to use \fBSSL_stateless()\fR. However, some
stream-based transport protocols (e.g. \s-1QUIC\s0) may not validate the source
address. In this case a TLSv1.3 application would be susceptible to this attack.
.PP
As a countermeasure to this issue TLSv1.3 and \s-1DTLS\s0 include a stateless cookie
mechanism. The idea is that when a client attempts to connect to a server it
sends a ClientHello message. The server responds with a HelloRetryRequest (in
TLSv1.3) or a HelloVerifyRequest (in \s-1DTLS\s0) which contains a unique cookie. The
client then resends the ClientHello, but this time includes the cookie in the
message thus proving that the client is capable of receiving messages sent to
that address. All of this can be done by the server without allocating any
state, and thus without consuming expensive resources.
.PP
OpenSSL implements this capability via the \fBSSL_stateless()\fR and \fBDTLSv1_listen()\fR
functions. The \fBssl\fR parameter should be a newly allocated \s-1SSL\s0 object with its
read and write BIOs set, in the same way as might be done for a call to
\&\fBSSL_accept()\fR. Typically, for \s-1DTLS,\s0 the read \s-1BIO\s0 will be in an \*(L"unconnected\*(R"
state and thus capable of receiving messages from any peer.
.PP
When a ClientHello is received that contains a cookie that has been verified,
then these functions will return with the \fBssl\fR parameter updated into a state
where the handshake can be continued by a call to (for example) \fBSSL_accept()\fR.
Additionally, for \fBDTLSv1_listen()\fR, the \fB\s-1BIO_ADDR\s0\fR pointed to by \fBpeer\fR will be
filled in with details of the peer that sent the ClientHello. If the underlying
\&\s-1BIO\s0 is unable to obtain the \fB\s-1BIO_ADDR\s0\fR of the peer (for example because the \s-1BIO\s0
does not support this), then \fB*peer\fR will be cleared and the family set to
\&\s-1AF_UNSPEC.\s0 Typically user code is expected to \*(L"connect\*(R" the underlying socket to
the peer and continue the handshake in a connected state.
.PP
Prior to calling \fBDTLSv1_listen()\fR user code must ensure that cookie generation
and verification callbacks have been set up using
\&\fBSSL_CTX_set_cookie_generate_cb\fR\|(3) and \fBSSL_CTX_set_cookie_verify_cb\fR\|(3)
respectively. For \fBSSL_stateless()\fR, \fBSSL_CTX_set_stateless_cookie_generate_cb\fR\|(3)
and \fBSSL_CTX_set_stateless_cookie_verify_cb\fR\|(3) must be used instead.
.PP
Since \fBDTLSv1_listen()\fR operates entirely statelessly whilst processing incoming
ClientHellos it is unable to process fragmented messages (since this would
require the allocation of state). An implication of this is that \fBDTLSv1_listen()\fR
\&\fBonly\fR supports ClientHellos that fit inside a single datagram.
.PP
For \fBSSL_stateless()\fR if an entire ClientHello message cannot be read without the
\&\*(L"read\*(R" \s-1BIO\s0 becoming empty then the \fBSSL_stateless()\fR call will fail. It is the
application's responsibility to ensure that data read from the \*(L"read\*(R" \s-1BIO\s0 during
a single \fBSSL_stateless()\fR call is all from the same peer.
.PP
\&\fBSSL_stateless()\fR will fail (with a 0 return value) if some \s-1TLS\s0 version less than
TLSv1.3 is used.
.PP
Both \fBSSL_stateless()\fR and \fBDTLSv1_listen()\fR will clear the error queue when they
start.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
For \fBSSL_stateless()\fR a return value of 1 indicates success and the \fBssl\fR object
will be set up ready to continue the handshake. A return value of 0 or \-1
indicates failure. If the value is 0 then a HelloRetryRequest was sent. A value
of \-1 indicates any other error. User code may retry the \fBSSL_stateless()\fR call.
.PP
For \fBDTLSv1_listen()\fR a return value of >= 1 indicates success. The \fBssl\fR object
will be set up ready to continue the handshake.  the \fBpeer\fR value will also be
filled in.
.PP
A return value of 0 indicates a non-fatal error. This could (for
example) be because of nonblocking \s-1IO,\s0 or some invalid message having been
received from a peer. Errors may be placed on the OpenSSL error queue with
further information if appropriate. Typically user code is expected to retry the
call to \fBDTLSv1_listen()\fR in the event of a non-fatal error.
.PP
A return value of <0 indicates a fatal error. This could (for example) be
because of a failure to allocate sufficient memory for the operation.
.PP
For \fBDTLSv1_listen()\fR, prior to OpenSSL 1.1.0, fatal and non-fatal errors both
produce return codes <= 0 (in typical implementations user code treats all
errors as non-fatal), whilst return codes >0 indicate success.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_set_cookie_generate_cb\fR\|(3), \fBSSL_CTX_set_cookie_verify_cb\fR\|(3),
\&\fBSSL_CTX_set_stateless_cookie_generate_cb\fR\|(3),
\&\fBSSL_CTX_set_stateless_cookie_verify_cb\fR\|(3), \fBSSL_get_error\fR\|(3),
\&\fBSSL_accept\fR\|(3), \fBssl\fR\|(7), \fBbio\fR\|(7)
.SH "HISTORY"
.IX Header "HISTORY"
The \fBSSL_stateless()\fR function was added in OpenSSL 1.1.1.
.PP
The \fBDTLSv1_listen()\fR return codes were clarified in OpenSSL 1.1.0.
The type of \*(L"peer\*(R" also changed in OpenSSL 1.1.0.
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
