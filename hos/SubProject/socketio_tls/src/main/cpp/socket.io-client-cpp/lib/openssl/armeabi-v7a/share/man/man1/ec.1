.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "EC 1"
.TH EC 1 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
openssl\-ec, ec \- EC key processing
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBec\fR
[\fB\-help\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-outform PEM|DER\fR]
[\fB\-in filename\fR]
[\fB\-passin arg\fR]
[\fB\-out filename\fR]
[\fB\-passout arg\fR]
[\fB\-des\fR]
[\fB\-des3\fR]
[\fB\-idea\fR]
[\fB\-text\fR]
[\fB\-noout\fR]
[\fB\-param_out\fR]
[\fB\-pubin\fR]
[\fB\-pubout\fR]
[\fB\-conv_form arg\fR]
[\fB\-param_enc arg\fR]
[\fB\-no_public\fR]
[\fB\-check\fR]
[\fB\-engine id\fR]
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
The \fBec\fR command processes \s-1EC\s0 keys. They can be converted between various
forms and their components printed out. \fBNote\fR OpenSSL uses the
private key format specified in '\s-1SEC 1:\s0 Elliptic Curve Cryptography'
(http://www.secg.org/). To convert an OpenSSL \s-1EC\s0 private key into the
PKCS#8 private key format use the \fBpkcs8\fR command.
.SH "OPTIONS"
.IX Header "OPTIONS"
.IP "\fB\-help\fR" 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format. The \fB\s-1DER\s0\fR option with a private key uses
an \s-1ASN.1 DER\s0 encoded \s-1SEC1\s0 private key. When used with a public key it
uses the SubjectPublicKeyInfo structure as specified in \s-1RFC 3280.\s0
The \fB\s-1PEM\s0\fR form is the default format: it consists of the \fB\s-1DER\s0\fR format base64
encoded with additional header and footer lines. In the case of a private key
PKCS#8 format is also accepted.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read a key from or standard input if this
option is not specified. If the key is encrypted a pass phrase will be
prompted for.
.IP "\fB\-passin arg\fR" 4
.IX Item "-passin arg"
The input file password source. For more information about the format of \fBarg\fR
see \*(L"Pass Phrase Options\*(R" in \fBopenssl\fR\|(1).
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write a key to or standard output by
is not specified. If any encryption options are set then a pass phrase will be
prompted for. The output filename should \fBnot\fR be the same as the input
filename.
.IP "\fB\-passout arg\fR" 4
.IX Item "-passout arg"
The output file password source. For more information about the format of \fBarg\fR
see \*(L"Pass Phrase Options\*(R" in \fBopenssl\fR\|(1).
.IP "\fB\-des|\-des3|\-idea\fR" 4
.IX Item "-des|-des3|-idea"
These options encrypt the private key with the \s-1DES,\s0 triple \s-1DES, IDEA\s0 or
any other cipher supported by OpenSSL before outputting it. A pass phrase is
prompted for.
If none of these options is specified the key is written in plain text. This
means that using the \fBec\fR utility to read in an encrypted key with no
encryption option can be used to remove the pass phrase from a key, or by
setting the encryption options it can be use to add or change the pass phrase.
These options can only be used with \s-1PEM\s0 format output files.
.IP "\fB\-text\fR" 4
.IX Item "-text"
Prints out the public, private key components and parameters.
.IP "\fB\-noout\fR" 4
.IX Item "-noout"
This option prevents output of the encoded version of the key.
.IP "\fB\-pubin\fR" 4
.IX Item "-pubin"
By default, a private key is read from the input file. With this option a
public key is read instead.
.IP "\fB\-pubout\fR" 4
.IX Item "-pubout"
By default a private key is output. With this option a public
key will be output instead. This option is automatically set if the input is
a public key.
.IP "\fB\-conv_form\fR" 4
.IX Item "-conv_form"
This specifies how the points on the elliptic curve are converted
into octet strings. Possible values are: \fBcompressed\fR (the default
value), \fBuncompressed\fR and \fBhybrid\fR. For more information regarding
the point conversion forms please read the X9.62 standard.
\&\fBNote\fR Due to patent issues the \fBcompressed\fR option is disabled
by default for binary curves and can be enabled by defining
the preprocessor macro \fB\s-1OPENSSL_EC_BIN_PT_COMP\s0\fR at compile time.
.IP "\fB\-param_enc arg\fR" 4
.IX Item "-param_enc arg"
This specifies how the elliptic curve parameters are encoded.
Possible value are: \fBnamed_curve\fR, i.e. the ec parameters are
specified by an \s-1OID,\s0 or \fBexplicit\fR where the ec parameters are
explicitly given (see \s-1RFC 3279\s0 for the definition of the
\&\s-1EC\s0 parameters structures). The default value is \fBnamed_curve\fR.
\&\fBNote\fR the \fBimplicitlyCA\fR alternative, as specified in \s-1RFC 3279,\s0
is currently not implemented in OpenSSL.
.IP "\fB\-no_public\fR" 4
.IX Item "-no_public"
This option omits the public key components from the private key output.
.IP "\fB\-check\fR" 4
.IX Item "-check"
This option checks the consistency of an \s-1EC\s0 private or public key.
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBec\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.SH "NOTES"
.IX Header "NOTES"
The \s-1PEM\s0 private key format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN EC PRIVATE KEY\-\-\-\-\-
\& \-\-\-\-\-END EC PRIVATE KEY\-\-\-\-\-
.Ve
.PP
The \s-1PEM\s0 public key format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN PUBLIC KEY\-\-\-\-\-
\& \-\-\-\-\-END PUBLIC KEY\-\-\-\-\-
.Ve
.SH "EXAMPLES"
.IX Header "EXAMPLES"
To encrypt a private key using triple \s-1DES:\s0
.PP
.Vb 1
\& openssl ec \-in key.pem \-des3 \-out keyout.pem
.Ve
.PP
To convert a private key from \s-1PEM\s0 to \s-1DER\s0 format:
.PP
.Vb 1
\& openssl ec \-in key.pem \-outform DER \-out keyout.der
.Ve
.PP
To print out the components of a private key to standard output:
.PP
.Vb 1
\& openssl ec \-in key.pem \-text \-noout
.Ve
.PP
To just output the public part of a private key:
.PP
.Vb 1
\& openssl ec \-in key.pem \-pubout \-out pubkey.pem
.Ve
.PP
To change the parameters encoding to \fBexplicit\fR:
.PP
.Vb 1
\& openssl ec \-in key.pem \-param_enc explicit \-out keyout.pem
.Ve
.PP
To change the point conversion form to \fBcompressed\fR:
.PP
.Vb 1
\& openssl ec \-in key.pem \-conv_form compressed \-out keyout.pem
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBecparam\fR\|(1), \fBdsa\fR\|(1), \fBrsa\fR\|(1)
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2003\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
