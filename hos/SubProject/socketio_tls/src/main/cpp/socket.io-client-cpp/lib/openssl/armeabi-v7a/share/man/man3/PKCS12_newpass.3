.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "PKCS12_NEWPASS 3"
.TH PKCS12_NEWPASS 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
PKCS12_newpass \- change the password of a PKCS12 structure
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& int PKCS12_newpass(PKCS12 *p12, const char *oldpass, const char *newpass);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fBPKCS12_newpass()\fR changes the password of a \s-1PKCS12\s0 structure.
.PP
\&\fBp12\fR is a pointer to a \s-1PKCS12\s0 structure. \fBoldpass\fR is the existing password
and \fBnewpass\fR is the new password.
.SH "NOTES"
.IX Header "NOTES"
Each of \fBoldpass\fR and \fBnewpass\fR is independently interpreted as a string in
the \s-1UTF\-8\s0 encoding. If it is not valid \s-1UTF\-8,\s0 it is assumed to be \s-1ISO8859\-1\s0
instead.
.PP
In particular, this means that passwords in the locale character set
(or code page on Windows) must potentially be converted to \s-1UTF\-8\s0 before
use. This may include passwords from local text files, or input from
the terminal or command line. Refer to the documentation of
\&\fBUI_OpenSSL\fR\|(3), for example.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS12_newpass()\fR returns 1 on success or 0 on failure. Applications can
retrieve the most recent error from \fBPKCS12_newpass()\fR with \fBERR_get_error()\fR.
.SH "EXAMPLES"
.IX Header "EXAMPLES"
This example loads a PKCS#12 file, changes its password and writes out
the result to a new file.
.PP
.Vb 5
\& #include <stdio.h>
\& #include <stdlib.h>
\& #include <openssl/pem.h>
\& #include <openssl/err.h>
\& #include <openssl/pkcs12.h>
\&
\& int main(int argc, char **argv)
\& {
\&     FILE *fp;
\&     PKCS12 *p12;
\&
\&     if (argc != 5) {
\&         fprintf(stderr, "Usage: pkread p12file password newpass opfile\en");
\&         return 1;
\&     }
\&     if ((fp = fopen(argv[1], "rb")) == NULL) {
\&         fprintf(stderr, "Error opening file %s\en", argv[1]);
\&         return 1;
\&     }
\&     p12 = d2i_PKCS12_fp(fp, NULL);
\&     fclose(fp);
\&     if (p12 == NULL) {
\&         fprintf(stderr, "Error reading PKCS#12 file\en");
\&         ERR_print_errors_fp(stderr);
\&         return 1;
\&     }
\&     if (PKCS12_newpass(p12, argv[2], argv[3]) == 0) {
\&         fprintf(stderr, "Error changing password\en");
\&         ERR_print_errors_fp(stderr);
\&         PKCS12_free(p12);
\&         return 1;
\&     }
\&     if ((fp = fopen(argv[4], "wb")) == NULL) {
\&         fprintf(stderr, "Error opening file %s\en", argv[4]);
\&         PKCS12_free(p12);
\&         return 1;
\&     }
\&     i2d_PKCS12_fp(fp, p12);
\&     PKCS12_free(p12);
\&     fclose(fp);
\&     return 0;
\& }
.Ve
.SH "NOTES"
.IX Header "NOTES"
If the PKCS#12 structure does not have a password, then you must use the empty
string "" for \fBoldpass\fR. Using \s-1NULL\s0 for \fBoldpass\fR will result in a
\&\fBPKCS12_newpass()\fR failure.
.PP
If the wrong password is used for \fBoldpass\fR then the function will fail,
with a \s-1MAC\s0 verification error. In rare cases the \s-1PKCS12\s0 structure does not
contain a \s-1MAC:\s0 in this case it will usually fail with a decryption padding
error.
.SH "BUGS"
.IX Header "BUGS"
The password format is a \s-1NULL\s0 terminated \s-1ASCII\s0 string which is converted to
Unicode form internally. As a result some passwords cannot be supplied to
this function.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS12_create\fR\|(3), \fBERR_get_error\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2016\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
