.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "OSSL_STORE_SEARCH 3"
.TH OSSL_STORE_SEARCH 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
OSSL_STORE_SEARCH, OSSL_STORE_SEARCH_by_name, OSSL_STORE_SEARCH_by_issuer_serial, OSSL_STORE_SEARCH_by_key_fingerprint, OSSL_STORE_SEARCH_by_alias, OSSL_STORE_SEARCH_free, OSSL_STORE_SEARCH_get_type, OSSL_STORE_SEARCH_get0_name, OSSL_STORE_SEARCH_get0_serial, OSSL_STORE_SEARCH_get0_bytes, OSSL_STORE_SEARCH_get0_string, OSSL_STORE_SEARCH_get0_digest \&\- Type and functions to create OSSL_STORE search criteria
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/store.h>
\&
\& typedef struct ossl_store_search_st OSSL_STORE_SEARCH;
\&
\& OSSL_STORE_SEARCH *OSSL_STORE_SEARCH_by_name(X509_NAME *name);
\& OSSL_STORE_SEARCH *OSSL_STORE_SEARCH_by_issuer_serial(X509_NAME *name,
\&                                                       const ASN1_INTEGER
\&                                                       *serial);
\& OSSL_STORE_SEARCH *OSSL_STORE_SEARCH_by_key_fingerprint(const EVP_MD *digest,
\&                                                         const unsigned char
\&                                                         *bytes, int len);
\& OSSL_STORE_SEARCH *OSSL_STORE_SEARCH_by_alias(const char *alias);
\&
\& void OSSL_STORE_SEARCH_free(OSSL_STORE_SEARCH *search);
\&
\& int OSSL_STORE_SEARCH_get_type(const OSSL_STORE_SEARCH *criterion);
\& X509_NAME *OSSL_STORE_SEARCH_get0_name(OSSL_STORE_SEARCH *criterion);
\& const ASN1_INTEGER *OSSL_STORE_SEARCH_get0_serial(const OSSL_STORE_SEARCH
\&                                                   *criterion);
\& const unsigned char *OSSL_STORE_SEARCH_get0_bytes(const OSSL_STORE_SEARCH
\&                                                   *criterion, size_t *length);
\& const char *OSSL_STORE_SEARCH_get0_string(const OSSL_STORE_SEARCH *criterion);
\& const EVP_MD *OSSL_STORE_SEARCH_get0_digest(const OSSL_STORE_SEARCH
\&                                             *criterion);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
These functions are used to specify search criteria to help search for specific
objects through other names than just the \s-1URI\s0 that's given to \fBOSSL_STORE_open()\fR.
For example, this can be useful for an application that has received a \s-1URI\s0
and then wants to add on search criteria in a uniform and supported manner.
.SS "Types"
.IX Subsection "Types"
\&\fB\s-1OSSL_STORE_SEARCH\s0\fR is an opaque type that holds the constructed search
criterion, and that can be given to an \s-1OSSL_STORE\s0 context with
\&\fBOSSL_STORE_find()\fR.
.PP
The calling application owns the allocation of an \fB\s-1OSSL_STORE_SEARCH\s0\fR at all
times, and should therefore be careful not to deallocate it before
\&\fBOSSL_STORE_close()\fR has been called for the \s-1OSSL_STORE\s0 context it was given
to.
.SS "Application Functions"
.IX Subsection "Application Functions"
\&\fBOSSL_STORE_SEARCH_by_name()\fR,
\&\fBOSSL_STORE_SEARCH_by_issuer_serial()\fR,
\&\fBOSSL_STORE_SEARCH_by_key_fingerprint()\fR,
and \fBOSSL_STORE_SEARCH_by_alias()\fR
are used to create an \fB\s-1OSSL_STORE_SEARCH\s0\fR from a subject name, an issuer name
and serial number pair, a key fingerprint, and an alias (for example a friendly
name).
The parameters that are provided are not copied, only referred to in a
criterion, so they must have at least the same life time as the created
\&\fB\s-1OSSL_STORE_SEARCH\s0\fR.
.PP
\&\fBOSSL_STORE_SEARCH_free()\fR is used to free the \fB\s-1OSSL_STORE_SEARCH\s0\fR.
.SS "Loader Functions"
.IX Subsection "Loader Functions"
\&\fBOSSL_STORE_SEARCH_get_type()\fR returns the criterion type for the given
\&\fB\s-1OSSL_STORE_SEARCH\s0\fR.
.PP
\&\fBOSSL_STORE_SEARCH_get0_name()\fR, \fBOSSL_STORE_SEARCH_get0_serial()\fR,
\&\fBOSSL_STORE_SEARCH_get0_bytes()\fR, \fBOSSL_STORE_SEARCH_get0_string()\fR,
and \fBOSSL_STORE_SEARCH_get0_digest()\fR
are used to retrieve different data from a \fB\s-1OSSL_STORE_SEARCH\s0\fR, as
available for each type.
For more information, see \*(L"\s-1SUPPORTED CRITERION TYPES\*(R"\s0 below.
.SH "SUPPORTED CRITERION TYPES"
.IX Header "SUPPORTED CRITERION TYPES"
Currently supported criterion types are:
.IP "\s-1OSSL_STORE_SEARCH_BY_NAME\s0" 4
.IX Item "OSSL_STORE_SEARCH_BY_NAME"
This criterion supports a search by exact match of subject name.
The subject name itself is a \fBX509_NAME\fR pointer.
A criterion of this type is created with \fBOSSL_STORE_SEARCH_by_name()\fR,
and the actual subject name is retrieved with \fBOSSL_STORE_SEARCH_get0_name()\fR.
.IP "\s-1OSSL_STORE_SEARCH_BY_ISSUER_SERIAL\s0" 4
.IX Item "OSSL_STORE_SEARCH_BY_ISSUER_SERIAL"
This criterion supports a search by exact match of both issuer name and serial
number.
The issuer name itself is a \fBX509_NAME\fR pointer, and the serial number is
a \fB\s-1ASN1_INTEGER\s0\fR pointer.
A criterion of this type is created with \fBOSSL_STORE_SEARCH_by_issuer_serial()\fR
and the actual issuer name and serial number are retrieved with
\&\fBOSSL_STORE_SEARCH_get0_name()\fR and \fBOSSL_STORE_SEARCH_get0_serial()\fR.
.IP "\s-1OSSL_STORE_SEARCH_BY_KEY_FINGERPRINT\s0" 4
.IX Item "OSSL_STORE_SEARCH_BY_KEY_FINGERPRINT"
This criterion supports a search by exact match of key fingerprint.
The key fingerprint in itself is a string of bytes and its length, as
well as the algorithm that was used to compute the fingerprint.
The digest may be left unspecified (\s-1NULL\s0), and in that case, the
loader has to decide on a default digest and compare fingerprints
accordingly.
A criterion of this type is created with \fBOSSL_STORE_SEARCH_by_key_fingerprint()\fR
and the actual fingerprint and its length can be retrieved with
\&\fBOSSL_STORE_SEARCH_get0_bytes()\fR.
The digest can be retrieved with \fBOSSL_STORE_SEARCH_get0_digest()\fR.
.IP "\s-1OSSL_STORE_SEARCH_BY_ALIAS\s0" 4
.IX Item "OSSL_STORE_SEARCH_BY_ALIAS"
This criterion supports a search by match of an alias of some kind.
The alias in itself is a simple C string.
A criterion of this type is created with \fBOSSL_STORE_SEARCH_by_alias()\fR
and the actual alias is retrieved with \fBOSSL_STORE_SEARCH_get0_string()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_STORE_SEARCH_by_name()\fR,
\&\fBOSSL_STORE_SEARCH_by_issuer_serial()\fR,
\&\fBOSSL_STORE_SEARCH_by_key_fingerprint()\fR,
and \fBOSSL_STORE_SEARCH_by_alias()\fR
return a \fB\s-1OSSL_STORE_SEARCH\s0\fR pointer on success, or \fB\s-1NULL\s0\fR on failure.
.PP
\&\fBOSSL_STORE_SEARCH_get_type()\fR returns the criterion type of the given
\&\fB\s-1OSSL_STORE_SEARCH\s0\fR.
There is no error value.
.PP
\&\fBOSSL_STORE_SEARCH_get0_name()\fR returns a \fBX509_NAME\fR pointer on success,
or \fB\s-1NULL\s0\fR when the given \fB\s-1OSSL_STORE_SEARCH\s0\fR was of a different type.
.PP
\&\fBOSSL_STORE_SEARCH_get0_serial()\fR returns a \fB\s-1ASN1_INTEGER\s0\fR pointer on success,
or \fB\s-1NULL\s0\fR when the given \fB\s-1OSSL_STORE_SEARCH\s0\fR was of a different type.
.PP
\&\fBOSSL_STORE_SEARCH_get0_bytes()\fR returns a \fBconst unsigned char\fR pointer and
sets \fB*length\fR to the strings length on success, or \fB\s-1NULL\s0\fR when the given
\&\fB\s-1OSSL_STORE_SEARCH\s0\fR was of a different type.
.PP
\&\fBOSSL_STORE_SEARCH_get0_string()\fR returns a \fBconst char\fR pointer on success,
or \fB\s-1NULL\s0\fR when the given \fB\s-1OSSL_STORE_SEARCH\s0\fR was of a different type.
.PP
\&\fBOSSL_STORE_SEARCH_get0_digest()\fR returns a \fBconst \s-1EVP_MD\s0\fR pointer.
\&\fB\s-1NULL\s0\fR is a valid value and means that the store loader default will
be used when applicable.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBossl_store\fR\|(7), \fBOSSL_STORE_supports_search\fR\|(3), \fBOSSL_STORE_find\fR\|(3)
.SH "HISTORY"
.IX Header "HISTORY"
\&\fB\s-1OSSL_STORE_SEARCH\s0\fR,
\&\fBOSSL_STORE_SEARCH_by_name()\fR,
\&\fBOSSL_STORE_SEARCH_by_issuer_serial()\fR,
\&\fBOSSL_STORE_SEARCH_by_key_fingerprint()\fR,
\&\fBOSSL_STORE_SEARCH_by_alias()\fR,
\&\fBOSSL_STORE_SEARCH_free()\fR,
\&\fBOSSL_STORE_SEARCH_get_type()\fR,
\&\fBOSSL_STORE_SEARCH_get0_name()\fR,
\&\fBOSSL_STORE_SEARCH_get0_serial()\fR,
\&\fBOSSL_STORE_SEARCH_get0_bytes()\fR,
and \fBOSSL_STORE_SEARCH_get0_string()\fR
were added in OpenSSL 1.1.1.
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
