.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "CTLOG_NEW 3"
.TH CTLOG_NEW 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
CTLOG_new, CTLOG_new_from_base64, CTLOG_free, CTLOG_get0_name, CTLOG_get0_log_id, CTLOG_get0_public_key \- encapsulates information about a Certificate Transparency log
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ct.h>
\&
\& CTLOG *CTLOG_new(EVP_PKEY *public_key, const char *name);
\& int CTLOG_new_from_base64(CTLOG ** ct_log,
\&                           const char *pkey_base64, const char *name);
\& void CTLOG_free(CTLOG *log);
\& const char *CTLOG_get0_name(const CTLOG *log);
\& void CTLOG_get0_log_id(const CTLOG *log, const uint8_t **log_id,
\&                        size_t *log_id_len);
\& EVP_PKEY *CTLOG_get0_public_key(const CTLOG *log);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fBCTLOG_new()\fR returns a new \s-1CTLOG\s0 that represents the Certificate Transparency
(\s-1CT\s0) log with the given public key. A name must also be provided that can be
used to help users identify this log. Ownership of the public key is
transferred.
.PP
\&\fBCTLOG_new_from_base64()\fR also creates a new \s-1CTLOG,\s0 but takes the public key in
base64\-encoded \s-1DER\s0 form and sets the ct_log pointer to point to the new \s-1CTLOG.\s0
The base64 will be decoded and the public key parsed.
.PP
Regardless of whether \fBCTLOG_new()\fR or \fBCTLOG_new_from_base64()\fR is used, it is the
caller's responsibility to pass the \s-1CTLOG\s0 to \fBCTLOG_free()\fR once it is no longer
needed. This will delete it and, if created by \fBCTLOG_new()\fR, the \s-1EVP_PKEY\s0 that
was passed to it.
.PP
\&\fBCTLOG_get0_name()\fR returns the name of the log, as provided when the \s-1CTLOG\s0 was
created. Ownership of the string remains with the \s-1CTLOG.\s0
.PP
\&\fBCTLOG_get0_log_id()\fR sets *log_id to point to a string containing that log's
LogID (see \s-1RFC 6962\s0). It sets *log_id_len to the length of that LogID. For a
v1 \s-1CT\s0 log, the LogID will be a \s-1SHA\-256\s0 hash (i.e. 32 bytes long). Ownership of
the string remains with the \s-1CTLOG.\s0
.PP
\&\fBCTLOG_get0_public_key()\fR returns the public key of the \s-1CT\s0 log. Ownership of the
\&\s-1EVP_PKEY\s0 remains with the \s-1CTLOG.\s0
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCTLOG_new()\fR will return \s-1NULL\s0 if an error occurs.
.PP
\&\fBCTLOG_new_from_base64()\fR will return 1 on success, 0 otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBct\fR\|(7)
.SH "HISTORY"
.IX Header "HISTORY"
These functions were added in OpenSSL 1.1.0.
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
