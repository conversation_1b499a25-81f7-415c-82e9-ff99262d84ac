<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ASN1_TYPE_get</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ASN1_TYPE_get, ASN1_TYPE_set, ASN1_TYPE_set1, ASN1_TYPE_cmp, ASN1_TYPE_unpack_sequence, ASN1_TYPE_pack_sequence - ASN1_TYPE utility functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code> #include &lt;openssl/asn1.h&gt;

 int ASN1_TYPE_get(const ASN1_TYPE *a);
 void ASN1_TYPE_set(ASN1_TYPE *a, int type, void *value);
 int ASN1_TYPE_set1(ASN1_TYPE *a, int type, const void *value);
 int ASN1_TYPE_cmp(const ASN1_TYPE *a, const ASN1_TYPE *b);

 void *ASN1_TYPE_unpack_sequence(const ASN1_ITEM *it, const ASN1_TYPE *t);
 ASN1_TYPE *ASN1_TYPE_pack_sequence(const ASN1_ITEM *it, void *s,
                                    ASN1_TYPE **t);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions allow an ASN1_TYPE structure to be manipulated. The ASN1_TYPE structure can contain any ASN.1 type or constructed type such as a SEQUENCE: it is effectively equivalent to the ASN.1 ANY type.</p>

<p>ASN1_TYPE_get() returns the type of <b>a</b>.</p>

<p>ASN1_TYPE_set() sets the value of <b>a</b> to <b>type</b> and <b>value</b>. This function uses the pointer <b>value</b> internally so it must <b>not</b> be freed up after the call.</p>

<p>ASN1_TYPE_set1() sets the value of <b>a</b> to <b>type</b> a copy of <b>value</b>.</p>

<p>ASN1_TYPE_cmp() compares ASN.1 types <b>a</b> and <b>b</b> and returns 0 if they are identical and nonzero otherwise.</p>

<p>ASN1_TYPE_unpack_sequence() attempts to parse the SEQUENCE present in <b>t</b> using the ASN.1 structure <b>it</b>. If successful it returns a pointer to the ASN.1 structure corresponding to <b>it</b> which must be freed by the caller. If it fails it return NULL.</p>

<p>ASN1_TYPE_pack_sequence() attempts to encode the ASN.1 structure <b>s</b> corresponding to <b>it</b> into an ASN1_TYPE. If successful the encoded ASN1_TYPE is returned. If <b>t</b> and <b>*t</b> are not NULL the encoded type is written to <b>t</b> overwriting any existing data. If <b>t</b> is not NULL but <b>*t</b> is NULL the returned ASN1_TYPE is written to <b>*t</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The type and meaning of the <b>value</b> parameter for ASN1_TYPE_set() and ASN1_TYPE_set1() is determined by the <b>type</b> parameter. If <b>type</b> is V_ASN1_NULL <b>value</b> is ignored. If <b>type</b> is V_ASN1_BOOLEAN then the boolean is set to TRUE if <b>value</b> is not NULL. If <b>type</b> is V_ASN1_OBJECT then value is an ASN1_OBJECT structure. Otherwise <b>type</b> is and ASN1_STRING structure. If <b>type</b> corresponds to a primitive type (or a string type) then the contents of the ASN1_STRING contain the content octets of the type. If <b>type</b> corresponds to a constructed type or a tagged type (V_ASN1_SEQUENCE, V_ASN1_SET or V_ASN1_OTHER) then the ASN1_STRING contains the entire ASN.1 encoding verbatim (including tag and length octets).</p>

<p>ASN1_TYPE_cmp() may not return zero if two types are equivalent but have different encodings. For example the single content octet of the boolean TRUE value under BER can have any nonzero encoding but ASN1_TYPE_cmp() will only return zero if the values are the same.</p>

<p>If either or both of the parameters passed to ASN1_TYPE_cmp() is NULL the return value is nonzero. Technically if both parameters are NULL the two types could be absent OPTIONAL fields and so should match, however, passing NULL values could also indicate a programming error (for example an unparsable type which returns NULL) for types which do <b>not</b> match. So applications should handle the case of two absent values separately.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>ASN1_TYPE_get() returns the type of the ASN1_TYPE argument.</p>

<p>ASN1_TYPE_set() does not return a value.</p>

<p>ASN1_TYPE_set1() returns 1 for success and 0 for failure.</p>

<p>ASN1_TYPE_cmp() returns 0 if the types are identical and nonzero otherwise.</p>

<p>ASN1_TYPE_unpack_sequence() returns a pointer to an ASN.1 structure or NULL on failure.</p>

<p>ASN1_TYPE_pack_sequence() return an ASN1_TYPE structure if it succeeds or NULL on failure.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


