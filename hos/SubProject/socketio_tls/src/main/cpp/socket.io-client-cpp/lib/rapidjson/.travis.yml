language: cpp

compiler:
 - clang
 - gcc

env:
  matrix:
    - CONF=debug   ARCH=x86_64
    - CONF=release ARCH=x86_64
    - CONF=debug   ARCH=x86
    - CONF=release ARCH=x86
  global:
    - ARCH_FLAGS_x86='-m32'        # #266: don't use SSE on 32-bit
    - ARCH_FLAGS_x86_64='-msse4.2' #       use SSE4.2 on 64-bit
    - GITHUB_REPO='miloyip/rapidjson'
    - secure: "HrsaCb+N66EG1HR+LWH1u51SjaJyRwJEDzqJGYMB7LJ/bfqb9mWKF1fLvZGk46W5t7TVaXRDD5KHFx9DPWvKn4gRUVkwTHEy262ah5ORh8M6n/6VVVajeV/AYt2C0sswdkDBDO4Xq+xy5gdw3G8s1A4Inbm73pUh+6vx+7ltBbk="

before_install:
  - sudo apt-get update -qq
  - sudo apt-get install -qq cmake doxygen valgrind
  - if [ "$ARCH" = "x86" ]; then sudo apt-get install -qq g++-multilib libc6-dbg:i386; fi

install: true

before_script:
#   hack to avoid Valgrind bug (https://bugs.kde.org/show_bug.cgi?id=326469),
#   exposed by merging PR#163 (using -march=native)
    - sed -i "s/-march=native//" CMakeLists.txt
    - mkdir build 
    - >
        eval "ARCH_FLAGS=\${ARCH_FLAGS_${ARCH}}" ;
        (cd build && cmake 
        -DRAPIDJSON_HAS_STDSTRING=ON
        -DCMAKE_VERBOSE_MAKEFILE=ON 
        -DCMAKE_BUILD_TYPE=$CONF 
        -DCMAKE_CXX_FLAGS="$ARCH_FLAGS" ..)

script:
  - cd build
  - make tests
  - make examples
  - ctest -V `[ "$CONF" = "release" ] || echo "-E perftest"`
  - make travis_doc
