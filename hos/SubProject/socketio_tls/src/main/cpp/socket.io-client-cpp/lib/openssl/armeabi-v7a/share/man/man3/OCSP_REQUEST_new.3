.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "OCSP_REQUEST_NEW 3"
.TH OCSP_REQUEST_NEW 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
OCSP_REQUEST_new, OCSP_REQUEST_free, OCSP_request_add0_id, OCSP_request_sign, OCSP_request_add1_cert, OCSP_request_onereq_count, OCSP_request_onereq_get0 \- OCSP request functions
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ocsp.h>
\&
\& OCSP_REQUEST *OCSP_REQUEST_new(void);
\& void OCSP_REQUEST_free(OCSP_REQUEST *req);
\&
\& OCSP_ONEREQ *OCSP_request_add0_id(OCSP_REQUEST *req, OCSP_CERTID *cid);
\&
\& int OCSP_request_sign(OCSP_REQUEST *req,
\&                       X509 *signer, EVP_PKEY *key, const EVP_MD *dgst,
\&                       STACK_OF(X509) *certs, unsigned long flags);
\&
\& int OCSP_request_add1_cert(OCSP_REQUEST *req, X509 *cert);
\&
\& int OCSP_request_onereq_count(OCSP_REQUEST *req);
\& OCSP_ONEREQ *OCSP_request_onereq_get0(OCSP_REQUEST *req, int i);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fBOCSP_REQUEST_new()\fR allocates and returns an empty \fB\s-1OCSP_REQUEST\s0\fR structure.
.PP
\&\fBOCSP_REQUEST_free()\fR frees up the request structure \fBreq\fR.
.PP
\&\fBOCSP_request_add0_id()\fR adds certificate \s-1ID\s0 \fBcid\fR to \fBreq\fR. It returns
the \fB\s-1OCSP_ONEREQ\s0\fR structure added so an application can add additional
extensions to the request. The \fBid\fR parameter \fB\s-1MUST NOT\s0\fR be freed up after
the operation.
.PP
\&\fBOCSP_request_sign()\fR signs \s-1OCSP\s0 request \fBreq\fR using certificate
\&\fBsigner\fR, private key \fBkey\fR, digest \fBdgst\fR and additional certificates
\&\fBcerts\fR. If the \fBflags\fR option \fB\s-1OCSP_NOCERTS\s0\fR is set then no certificates
will be included in the request.
.PP
\&\fBOCSP_request_add1_cert()\fR adds certificate \fBcert\fR to request \fBreq\fR. The
application is responsible for freeing up \fBcert\fR after use.
.PP
\&\fBOCSP_request_onereq_count()\fR returns the total number of \fB\s-1OCSP_ONEREQ\s0\fR
structures in \fBreq\fR.
.PP
\&\fBOCSP_request_onereq_get0()\fR returns an internal pointer to the \fB\s-1OCSP_ONEREQ\s0\fR
contained in \fBreq\fR of index \fBi\fR. The index value \fBi\fR runs from 0 to
OCSP_request_onereq_count(req) \- 1.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOCSP_REQUEST_new()\fR returns an empty \fB\s-1OCSP_REQUEST\s0\fR structure or \fB\s-1NULL\s0\fR if
an error occurred.
.PP
\&\fBOCSP_request_add0_id()\fR returns the \fB\s-1OCSP_ONEREQ\s0\fR structure containing \fBcid\fR
or \fB\s-1NULL\s0\fR if an error occurred.
.PP
\&\fBOCSP_request_sign()\fR and \fBOCSP_request_add1_cert()\fR return 1 for success and 0
for failure.
.PP
\&\fBOCSP_request_onereq_count()\fR returns the total number of \fB\s-1OCSP_ONEREQ\s0\fR
structures in \fBreq\fR.
.PP
\&\fBOCSP_request_onereq_get0()\fR returns a pointer to an \fB\s-1OCSP_ONEREQ\s0\fR structure
or \fB\s-1NULL\s0\fR if the index value is out or range.
.SH "NOTES"
.IX Header "NOTES"
An \s-1OCSP\s0 request structure contains one or more \fB\s-1OCSP_ONEREQ\s0\fR structures
corresponding to each certificate.
.PP
\&\fBOCSP_request_onereq_count()\fR and \fBOCSP_request_onereq_get0()\fR are mainly used by
\&\s-1OCSP\s0 responders.
.SH "EXAMPLES"
.IX Header "EXAMPLES"
Create an \fB\s-1OCSP_REQUEST\s0\fR structure for certificate \fBcert\fR with issuer
\&\fBissuer\fR:
.PP
.Vb 2
\& OCSP_REQUEST *req;
\& OCSP_ID *cid;
\&
\& req = OCSP_REQUEST_new();
\& if (req == NULL)
\&    /* error */
\& cid = OCSP_cert_to_id(EVP_sha1(), cert, issuer);
\& if (cid == NULL)
\&    /* error */
\&
\& if (OCSP_REQUEST_add0_id(req, cid) == NULL)
\&    /* error */
\&
\& /* Do something with req, e.g. query responder */
\&
\& OCSP_REQUEST_free(req);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7),
\&\fBOCSP_cert_to_id\fR\|(3),
\&\fBOCSP_request_add1_nonce\fR\|(3),
\&\fBOCSP_resp_find_status\fR\|(3),
\&\fBOCSP_response_status\fR\|(3),
\&\fBOCSP_sendreq_new\fR\|(3)
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2015\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
