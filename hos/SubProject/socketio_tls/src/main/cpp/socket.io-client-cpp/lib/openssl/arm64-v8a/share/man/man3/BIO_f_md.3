.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "BIO_F_MD 3"
.TH BIO_F_MD 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
BIO_f_md, BIO_set_md, BIO_get_md, BIO_get_md_ctx \- message digest BIO filter
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/bio.h>
\& #include <openssl/evp.h>
\&
\& const BIO_METHOD *BIO_f_md(void);
\& int BIO_set_md(BIO *b, EVP_MD *md);
\& int BIO_get_md(BIO *b, EVP_MD **mdp);
\& int BIO_get_md_ctx(BIO *b, EVP_MD_CTX **mdcp);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fBBIO_f_md()\fR returns the message digest \s-1BIO\s0 method. This is a filter
\&\s-1BIO\s0 that digests any data passed through it, it is a \s-1BIO\s0 wrapper
for the digest routines \fBEVP_DigestInit()\fR, \fBEVP_DigestUpdate()\fR
and \fBEVP_DigestFinal()\fR.
.PP
Any data written or read through a digest \s-1BIO\s0 using \fBBIO_read_ex()\fR and
\&\fBBIO_write_ex()\fR is digested.
.PP
\&\fBBIO_gets()\fR, if its \fBsize\fR parameter is large enough finishes the
digest calculation and returns the digest value. \fBBIO_puts()\fR is
not supported.
.PP
\&\fBBIO_reset()\fR reinitialises a digest \s-1BIO.\s0
.PP
\&\fBBIO_set_md()\fR sets the message digest of \s-1BIO\s0 \fBb\fR to \fBmd\fR: this
must be called to initialize a digest \s-1BIO\s0 before any data is
passed through it. It is a \fBBIO_ctrl()\fR macro.
.PP
\&\fBBIO_get_md()\fR places the a pointer to the digest BIOs digest method
in \fBmdp\fR, it is a \fBBIO_ctrl()\fR macro.
.PP
\&\fBBIO_get_md_ctx()\fR returns the digest BIOs context into \fBmdcp\fR.
.SH "NOTES"
.IX Header "NOTES"
The context returned by \fBBIO_get_md_ctx()\fR can be used in calls
to \fBEVP_DigestFinal()\fR and also the signature routines \fBEVP_SignFinal()\fR
and \fBEVP_VerifyFinal()\fR.
.PP
The context returned by \fBBIO_get_md_ctx()\fR is an internal context
structure. Changes made to this context will affect the digest
\&\s-1BIO\s0 itself and the context pointer will become invalid when the digest
\&\s-1BIO\s0 is freed.
.PP
After the digest has been retrieved from a digest \s-1BIO\s0 it must be
reinitialized by calling \fBBIO_reset()\fR, or \fBBIO_set_md()\fR before any more
data is passed through it.
.PP
If an application needs to call \fBBIO_gets()\fR or \fBBIO_puts()\fR through
a chain containing digest BIOs then this can be done by prepending
a buffering \s-1BIO.\s0
.PP
Calling \fBBIO_get_md_ctx()\fR will return the context and initialize the \s-1BIO\s0
state. This allows applications to initialize the context externally
if the standard calls such as \fBBIO_set_md()\fR are not sufficiently flexible.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_f_md()\fR returns the digest \s-1BIO\s0 method.
.PP
\&\fBBIO_set_md()\fR, \fBBIO_get_md()\fR and \fBBIO_md_ctx()\fR return 1 for success and
0 for failure.
.SH "EXAMPLES"
.IX Header "EXAMPLES"
The following example creates a \s-1BIO\s0 chain containing an \s-1SHA1\s0 and \s-1MD5\s0
digest \s-1BIO\s0 and passes the string \*(L"Hello World\*(R" through it. Error
checking has been omitted for clarity.
.PP
.Vb 2
\& BIO *bio, *mdtmp;
\& char message[] = "Hello World";
\&
\& bio = BIO_new(BIO_s_null());
\& mdtmp = BIO_new(BIO_f_md());
\& BIO_set_md(mdtmp, EVP_sha1());
\& /*
\&  * For BIO_push() we want to append the sink BIO and keep a note of
\&  * the start of the chain.
\&  */
\& bio = BIO_push(mdtmp, bio);
\& mdtmp = BIO_new(BIO_f_md());
\& BIO_set_md(mdtmp, EVP_md5());
\& bio = BIO_push(mdtmp, bio);
\& /* Note: mdtmp can now be discarded */
\& BIO_write(bio, message, strlen(message));
.Ve
.PP
The next example digests data by reading through a chain instead:
.PP
.Vb 3
\& BIO *bio, *mdtmp;
\& char buf[1024];
\& int rdlen;
\&
\& bio = BIO_new_file(file, "rb");
\& mdtmp = BIO_new(BIO_f_md());
\& BIO_set_md(mdtmp, EVP_sha1());
\& bio = BIO_push(mdtmp, bio);
\& mdtmp = BIO_new(BIO_f_md());
\& BIO_set_md(mdtmp, EVP_md5());
\& bio = BIO_push(mdtmp, bio);
\& do {
\&     rdlen = BIO_read(bio, buf, sizeof(buf));
\&     /* Might want to do something with the data here */
\& } while (rdlen > 0);
.Ve
.PP
This next example retrieves the message digests from a \s-1BIO\s0 chain and
outputs them. This could be used with the examples above.
.PP
.Vb 4
\& BIO *mdtmp;
\& unsigned char mdbuf[EVP_MAX_MD_SIZE];
\& int mdlen;
\& int i;
\&
\& mdtmp = bio;   /* Assume bio has previously been set up */
\& do {
\&     EVP_MD *md;
\&
\&     mdtmp = BIO_find_type(mdtmp, BIO_TYPE_MD);
\&     if (!mdtmp)
\&         break;
\&     BIO_get_md(mdtmp, &md);
\&     printf("%s digest", OBJ_nid2sn(EVP_MD_type(md)));
\&     mdlen = BIO_gets(mdtmp, mdbuf, EVP_MAX_MD_SIZE);
\&     for (i = 0; i < mdlen; i++) printf(":%02X", mdbuf[i]);
\&     printf("\en");
\&     mdtmp = BIO_next(mdtmp);
\& } while (mdtmp);
\&
\& BIO_free_all(bio);
.Ve
.SH "BUGS"
.IX Header "BUGS"
The lack of support for \fBBIO_puts()\fR and the non standard behaviour of
\&\fBBIO_gets()\fR could be regarded as anomalous. It could be argued that \fBBIO_gets()\fR
and \fBBIO_puts()\fR should be passed to the next \s-1BIO\s0 in the chain and digest
the data passed through and that digests should be retrieved using a
separate \fBBIO_ctrl()\fR call.
.SH "HISTORY"
.IX Header "HISTORY"
Before OpenSSL 1.0.0., the call to \fBBIO_get_md_ctx()\fR would only work if the
\&\s-1BIO\s0 was initialized first.
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
