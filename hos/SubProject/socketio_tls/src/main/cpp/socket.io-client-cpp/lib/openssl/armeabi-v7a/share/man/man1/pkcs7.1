.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "PKCS7 1"
.TH PKCS7 1 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
openssl\-pkcs7, pkcs7 \- PKCS#7 utility
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBpkcs7\fR
[\fB\-help\fR]
[\fB\-inform PEM|DER\fR]
[\fB\-outform PEM|DER\fR]
[\fB\-in filename\fR]
[\fB\-out filename\fR]
[\fB\-print_certs\fR]
[\fB\-text\fR]
[\fB\-noout\fR]
[\fB\-engine id\fR]
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
The \fBpkcs7\fR command processes PKCS#7 files in \s-1DER\s0 or \s-1PEM\s0 format.
.SH "OPTIONS"
.IX Header "OPTIONS"
.IP "\fB\-help\fR" 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform DER|PEM\fR" 4
.IX Item "-inform DER|PEM"
This specifies the input format. \fB\s-1DER\s0\fR format is \s-1DER\s0 encoded PKCS#7
v1.5 structure.\fB\s-1PEM\s0\fR (the default) is a base64 encoded version of
the \s-1DER\s0 form with header and footer lines.
.IP "\fB\-outform DER|PEM\fR" 4
.IX Item "-outform DER|PEM"
This specifies the output format, the options have the same meaning and default
as the \fB\-inform\fR option.
.IP "\fB\-in filename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read from or standard input if this
option is not specified.
.IP "\fB\-out filename\fR" 4
.IX Item "-out filename"
Specifies the output filename to write to or standard output by
default.
.IP "\fB\-print_certs\fR" 4
.IX Item "-print_certs"
Prints out any certificates or CRLs contained in the file. They are
preceded by their subject and issuer names in one line format.
.IP "\fB\-text\fR" 4
.IX Item "-text"
Prints out certificates details in full rather than just subject and
issuer names.
.IP "\fB\-noout\fR" 4
.IX Item "-noout"
Don't output the encoded version of the PKCS#7 structure (or certificates
is \fB\-print_certs\fR is set).
.IP "\fB\-engine id\fR" 4
.IX Item "-engine id"
Specifying an engine (by its unique \fBid\fR string) will cause \fBpkcs7\fR
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.
.SH "EXAMPLES"
.IX Header "EXAMPLES"
Convert a PKCS#7 file from \s-1PEM\s0 to \s-1DER:\s0
.PP
.Vb 1
\& openssl pkcs7 \-in file.pem \-outform DER \-out file.der
.Ve
.PP
Output all certificates in a file:
.PP
.Vb 1
\& openssl pkcs7 \-in file.pem \-print_certs \-out certs.pem
.Ve
.SH "NOTES"
.IX Header "NOTES"
The \s-1PEM\s0 PKCS#7 format uses the header and footer lines:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN PKCS7\-\-\-\-\-
\& \-\-\-\-\-END PKCS7\-\-\-\-\-
.Ve
.PP
For compatibility with some CAs it will also accept:
.PP
.Vb 2
\& \-\-\-\-\-BEGIN CERTIFICATE\-\-\-\-\-
\& \-\-\-\-\-END CERTIFICATE\-\-\-\-\-
.Ve
.SH "RESTRICTIONS"
.IX Header "RESTRICTIONS"
There is no option to print out all the fields of a PKCS#7 file.
.PP
This PKCS#7 routines only understand PKCS#7 v 1.5 as specified in \s-1RFC2315\s0 they
cannot currently parse, for example, the new \s-1CMS\s0 as described in \s-1RFC2630.\s0
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrl2pkcs7\fR\|(1)
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
