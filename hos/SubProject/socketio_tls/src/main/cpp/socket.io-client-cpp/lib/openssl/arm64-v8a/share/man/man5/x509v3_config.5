.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "X509V3_CONFIG 5"
.TH X509V3_CONFIG 5 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
x509v3_config \- X509 V3 certificate extension configuration format
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
Several of the OpenSSL utilities can add extensions to a certificate or
certificate request based on the contents of a configuration file.
.PP
Typically the application will contain an option to point to an extension
section. Each line of the extension section takes the form:
.PP
.Vb 1
\& extension_name=[critical,] extension_options
.Ve
.PP
If \fBcritical\fR is present then the extension will be critical.
.PP
The format of \fBextension_options\fR depends on the value of \fBextension_name\fR.
.PP
There are four main types of extension: \fIstring\fR extensions, \fImulti-valued\fR
extensions, \fIraw\fR and \fIarbitrary\fR extensions.
.PP
String extensions simply have a string which contains either the value itself
or how it is obtained.
.PP
For example:
.PP
.Vb 1
\& nsComment="This is a Comment"
.Ve
.PP
Multi-valued extensions have a short form and a long form. The short form
is a list of names and values:
.PP
.Vb 1
\& basicConstraints=critical,CA:true,pathlen:1
.Ve
.PP
The long form allows the values to be placed in a separate section:
.PP
.Vb 1
\& basicConstraints=critical,@bs_section
\&
\& [bs_section]
\&
\& CA=true
\& pathlen=1
.Ve
.PP
Both forms are equivalent.
.PP
The syntax of raw extensions is governed by the extension code: it can
for example contain data in multiple sections. The correct syntax to
use is defined by the extension code itself: check out the certificate
policies extension for an example.
.PP
If an extension type is unsupported then the \fIarbitrary\fR extension syntax
must be used, see the \s-1ARBITRARY EXTENSIONS\s0 section for more details.
.SH "STANDARD EXTENSIONS"
.IX Header "STANDARD EXTENSIONS"
The following sections describe each supported extension in detail.
.SS "Basic Constraints."
.IX Subsection "Basic Constraints."
This is a multi valued extension which indicates whether a certificate is
a \s-1CA\s0 certificate. The first (mandatory) name is \fB\s-1CA\s0\fR followed by \fB\s-1TRUE\s0\fR or
\&\fB\s-1FALSE\s0\fR. If \fB\s-1CA\s0\fR is \fB\s-1TRUE\s0\fR then an optional \fBpathlen\fR name followed by a
nonnegative value can be included.
.PP
For example:
.PP
.Vb 1
\& basicConstraints=CA:TRUE
\&
\& basicConstraints=CA:FALSE
\&
\& basicConstraints=critical,CA:TRUE, pathlen:0
.Ve
.PP
A \s-1CA\s0 certificate \fBmust\fR include the basicConstraints value with the \s-1CA\s0 field
set to \s-1TRUE.\s0 An end user certificate must either set \s-1CA\s0 to \s-1FALSE\s0 or exclude the
extension entirely. Some software may require the inclusion of basicConstraints
with \s-1CA\s0 set to \s-1FALSE\s0 for end entity certificates.
.PP
The pathlen parameter indicates the maximum number of CAs that can appear
below this one in a chain. So if you have a \s-1CA\s0 with a pathlen of zero it can
only be used to sign end user certificates and not further CAs.
.SS "Key Usage."
.IX Subsection "Key Usage."
Key usage is a multi valued extension consisting of a list of names of the
permitted key usages.
.PP
The supported names are: digitalSignature, nonRepudiation, keyEncipherment,
dataEncipherment, keyAgreement, keyCertSign, cRLSign, encipherOnly
and decipherOnly.
.PP
Examples:
.PP
.Vb 1
\& keyUsage=digitalSignature, nonRepudiation
\&
\& keyUsage=critical, keyCertSign
.Ve
.SS "Extended Key Usage."
.IX Subsection "Extended Key Usage."
This extensions consists of a list of usages indicating purposes for which
the certificate public key can be used for,
.PP
These can either be object short names or the dotted numerical form of OIDs.
While any \s-1OID\s0 can be used only certain values make sense. In particular the
following \s-1PKIX, NS\s0 and \s-1MS\s0 values are meaningful:
.PP
.Vb 10
\& Value                  Meaning
\& \-\-\-\-\-                  \-\-\-\-\-\-\-
\& serverAuth             SSL/TLS Web Server Authentication.
\& clientAuth             SSL/TLS Web Client Authentication.
\& codeSigning            Code signing.
\& emailProtection        E\-mail Protection (S/MIME).
\& timeStamping           Trusted Timestamping
\& OCSPSigning            OCSP Signing
\& ipsecIKE               ipsec Internet Key Exchange
\& msCodeInd              Microsoft Individual Code Signing (authenticode)
\& msCodeCom              Microsoft Commercial Code Signing (authenticode)
\& msCTLSign              Microsoft Trust List Signing
\& msEFS                  Microsoft Encrypted File System
.Ve
.PP
Examples:
.PP
.Vb 2
\& extendedKeyUsage=critical,codeSigning,*******
\& extendedKeyUsage=serverAuth,clientAuth
.Ve
.SS "Subject Key Identifier."
.IX Subsection "Subject Key Identifier."
This is really a string extension and can take two possible values. Either
the word \fBhash\fR which will automatically follow the guidelines in \s-1RFC3280\s0
or a hex string giving the extension value to include. The use of the hex
string is strongly discouraged.
.PP
Example:
.PP
.Vb 1
\& subjectKeyIdentifier=hash
.Ve
.SS "Authority Key Identifier."
.IX Subsection "Authority Key Identifier."
The authority key identifier extension permits two options. keyid and issuer:
both can take the optional value \*(L"always\*(R".
.PP
If the keyid option is present an attempt is made to copy the subject key
identifier from the parent certificate. If the value \*(L"always\*(R" is present
then an error is returned if the option fails.
.PP
The issuer option copies the issuer and serial number from the issuer
certificate. This will only be done if the keyid option fails or
is not included unless the \*(L"always\*(R" flag will always include the value.
.PP
Example:
.PP
.Vb 1
\& authorityKeyIdentifier=keyid,issuer
.Ve
.SS "Subject Alternative Name."
.IX Subsection "Subject Alternative Name."
The subject alternative name extension allows various literal values to be
included in the configuration file. These include \fBemail\fR (an email address)
\&\fB\s-1URI\s0\fR a uniform resource indicator, \fB\s-1DNS\s0\fR (a \s-1DNS\s0 domain name), \fB\s-1RID\s0\fR (a
registered \s-1ID: OBJECT IDENTIFIER\s0), \fB\s-1IP\s0\fR (an \s-1IP\s0 address), \fBdirName\fR
(a distinguished name) and otherName.
.PP
The email option include a special 'copy' value. This will automatically
include any email addresses contained in the certificate subject name in
the extension.
.PP
The \s-1IP\s0 address used in the \fB\s-1IP\s0\fR options can be in either IPv4 or IPv6 format.
.PP
The value of \fBdirName\fR should point to a section containing the distinguished
name to use as a set of name value pairs. Multi values AVAs can be formed by
prefacing the name with a \fB+\fR character.
.PP
otherName can include arbitrary data associated with an \s-1OID:\s0 the value
should be the \s-1OID\s0 followed by a semicolon and the content in standard
\&\fBASN1_generate_nconf\fR\|(3) format.
.PP
Examples:
.PP
.Vb 5
\& subjectAltName=email:copy,email:<EMAIL>,URI:http://my.url.here/
\& subjectAltName=IP:***********
\& subjectAltName=IP:13::17
\& subjectAltName=email:<EMAIL>,RID:*******
\& subjectAltName=otherName:*******;UTF8:some other identifier
\&
\& subjectAltName=dirName:dir_sect
\&
\& [dir_sect]
\& C=UK
\& O=My Organization
\& OU=My Unit
\& CN=My Name
.Ve
.SS "Issuer Alternative Name."
.IX Subsection "Issuer Alternative Name."
The issuer alternative name option supports all the literal options of
subject alternative name. It does \fBnot\fR support the email:copy option because
that would not make sense. It does support an additional issuer:copy option
that will copy all the subject alternative name values from the issuer
certificate (if possible).
.PP
Example:
.PP
.Vb 1
\& issuerAltName = issuer:copy
.Ve
.SS "Authority Info Access."
.IX Subsection "Authority Info Access."
The authority information access extension gives details about how to access
certain information relating to the \s-1CA.\s0 Its syntax is accessOID;location
where \fIlocation\fR has the same syntax as subject alternative name (except
that email:copy is not supported). accessOID can be any valid \s-1OID\s0 but only
certain values are meaningful, for example \s-1OCSP\s0 and caIssuers.
.PP
Example:
.PP
.Vb 2
\& authorityInfoAccess = OCSP;URI:http://ocsp.my.host/
\& authorityInfoAccess = caIssuers;URI:http://my.ca/ca.html
.Ve
.SS "\s-1CRL\s0 distribution points"
.IX Subsection "CRL distribution points"
This is a multi-valued extension whose options can be either in name:value pair
using the same form as subject alternative name or a single value representing
a section name containing all the distribution point fields.
.PP
For a name:value pair a new DistributionPoint with the fullName field set to
the given value both the cRLissuer and reasons fields are omitted in this case.
.PP
In the single option case the section indicated contains values for each
field. In this section:
.PP
If the name is \*(L"fullname\*(R" the value field should contain the full name
of the distribution point in the same format as subject alternative name.
.PP
If the name is \*(L"relativename\*(R" then the value field should contain a section
name whose contents represent a \s-1DN\s0 fragment to be placed in this field.
.PP
The name \*(L"CRLIssuer\*(R" if present should contain a value for this field in
subject alternative name format.
.PP
If the name is \*(L"reasons\*(R" the value field should consist of a comma
separated field containing the reasons. Valid reasons are: \*(L"keyCompromise\*(R",
\&\*(L"CACompromise\*(R", \*(L"affiliationChanged\*(R", \*(L"superseded\*(R", \*(L"cessationOfOperation\*(R",
\&\*(L"certificateHold\*(R", \*(L"privilegeWithdrawn\*(R" and \*(L"AACompromise\*(R".
.PP
Simple examples:
.PP
.Vb 2
\& crlDistributionPoints=URI:http://myhost.com/myca.crl
\& crlDistributionPoints=URI:http://my.com/my.crl,URI:http://oth.com/my.crl
.Ve
.PP
Full distribution point example:
.PP
.Vb 1
\& crlDistributionPoints=crldp1_section
\&
\& [crldp1_section]
\&
\& fullname=URI:http://myhost.com/myca.crl
\& CRLissuer=dirName:issuer_sect
\& reasons=keyCompromise, CACompromise
\&
\& [issuer_sect]
\& C=UK
\& O=Organisation
\& CN=Some Name
.Ve
.SS "Issuing Distribution Point"
.IX Subsection "Issuing Distribution Point"
This extension should only appear in CRLs. It is a multi valued extension
whose syntax is similar to the \*(L"section\*(R" pointed to by the \s-1CRL\s0 distribution
points extension with a few differences.
.PP
The names \*(L"reasons\*(R" and \*(L"CRLissuer\*(R" are not recognized.
.PP
The name \*(L"onlysomereasons\*(R" is accepted which sets this field. The value is
in the same format as the \s-1CRL\s0 distribution point \*(L"reasons\*(R" field.
.PP
The names \*(L"onlyuser\*(R", \*(L"onlyCA\*(R", \*(L"onlyAA\*(R" and \*(L"indirectCRL\*(R" are also accepted
the values should be a boolean value (\s-1TRUE\s0 or \s-1FALSE\s0) to indicate the value of
the corresponding field.
.PP
Example:
.PP
.Vb 1
\& issuingDistributionPoint=critical, @idp_section
\&
\& [idp_section]
\&
\& fullname=URI:http://myhost.com/myca.crl
\& indirectCRL=TRUE
\& onlysomereasons=keyCompromise, CACompromise
\&
\& [issuer_sect]
\& C=UK
\& O=Organisation
\& CN=Some Name
.Ve
.SS "Certificate Policies."
.IX Subsection "Certificate Policies."
This is a \fIraw\fR extension. All the fields of this extension can be set by
using the appropriate syntax.
.PP
If you follow the \s-1PKIX\s0 recommendations and just using one \s-1OID\s0 then you just
include the value of that \s-1OID.\s0 Multiple OIDs can be set separated by commas,
for example:
.PP
.Vb 1
\& certificatePolicies= *******, *******
.Ve
.PP
If you wish to include qualifiers then the policy \s-1OID\s0 and qualifiers need to
be specified in a separate section: this is done by using the \f(CW@section\fR syntax
instead of a literal \s-1OID\s0 value.
.PP
The section referred to must include the policy \s-1OID\s0 using the name
policyIdentifier, cPSuri qualifiers can be included using the syntax:
.PP
.Vb 1
\& CPS.nnn=value
.Ve
.PP
userNotice qualifiers can be set using the syntax:
.PP
.Vb 1
\& userNotice.nnn=@notice
.Ve
.PP
The value of the userNotice qualifier is specified in the relevant section.
This section can include explicitText, organization and noticeNumbers
options. explicitText and organization are text strings, noticeNumbers is a
comma separated list of numbers. The organization and noticeNumbers options
(if included) must \s-1BOTH\s0 be present. If you use the userNotice option with \s-1IE5\s0
then you need the 'ia5org' option at the top level to modify the encoding:
otherwise it will not be interpreted properly.
.PP
Example:
.PP
.Vb 1
\& certificatePolicies=ia5org,*******,*******.8,@polsect
\&
\& [polsect]
\&
\& policyIdentifier = *******
\& CPS.1="http://my.host.name/"
\& CPS.2="http://my.your.name/"
\& userNotice.1=@notice
\&
\& [notice]
\&
\& explicitText="Explicit Text Here"
\& organization="Organisation Name"
\& noticeNumbers=1,2,3,4
.Ve
.PP
The \fBia5org\fR option changes the type of the \fIorganization\fR field. In \s-1RFC2459\s0
it can only be of type DisplayText. In \s-1RFC3280\s0 IA5String is also permissible.
Some software (for example some versions of \s-1MSIE\s0) may require ia5org.
.PP
\&\s-1ASN1\s0 type of explicitText can be specified by prepending \fB\s-1UTF8\s0\fR,
\&\fB\s-1BMP\s0\fR or \fB\s-1VISIBLE\s0\fR prefix followed by colon. For example:
.PP
.Vb 2
\& [notice]
\& explicitText="UTF8:Explicit Text Here"
.Ve
.SS "Policy Constraints"
.IX Subsection "Policy Constraints"
This is a multi-valued extension which consisting of the names
\&\fBrequireExplicitPolicy\fR or \fBinhibitPolicyMapping\fR and a non negative integer
value. At least one component must be present.
.PP
Example:
.PP
.Vb 1
\& policyConstraints = requireExplicitPolicy:3
.Ve
.SS "Inhibit Any Policy"
.IX Subsection "Inhibit Any Policy"
This is a string extension whose value must be a non negative integer.
.PP
Example:
.PP
.Vb 1
\& inhibitAnyPolicy = 2
.Ve
.SS "Name Constraints"
.IX Subsection "Name Constraints"
The name constraints extension is a multi-valued extension. The name should
begin with the word \fBpermitted\fR or \fBexcluded\fR followed by a \fB;\fR. The rest of
the name and the value follows the syntax of subjectAltName except email:copy
is not supported and the \fB\s-1IP\s0\fR form should consist of an \s-1IP\s0 addresses and
subnet mask separated by a \fB/\fR.
.PP
Examples:
.PP
.Vb 1
\& nameConstraints=permitted;IP:***********/***********
\&
\& nameConstraints=permitted;email:.somedomain.com
\&
\& nameConstraints=excluded;email:.com
.Ve
.SS "\s-1OCSP\s0 No Check"
.IX Subsection "OCSP No Check"
The \s-1OCSP\s0 No Check extension is a string extension but its value is ignored.
.PP
Example:
.PP
.Vb 1
\& noCheck = ignored
.Ve
.SS "\s-1TLS\s0 Feature (aka Must Staple)"
.IX Subsection "TLS Feature (aka Must Staple)"
This is a multi-valued extension consisting of a list of \s-1TLS\s0 extension
identifiers. Each identifier may be a number (0..65535) or a supported name.
When a \s-1TLS\s0 client sends a listed extension, the \s-1TLS\s0 server is expected to
include that extension in its reply.
.PP
The supported names are: \fBstatus_request\fR and \fBstatus_request_v2\fR.
.PP
Example:
.PP
.Vb 1
\& tlsfeature = status_request
.Ve
.SH "DEPRECATED EXTENSIONS"
.IX Header "DEPRECATED EXTENSIONS"
The following extensions are non standard, Netscape specific and largely
obsolete. Their use in new applications is discouraged.
.SS "Netscape String extensions."
.IX Subsection "Netscape String extensions."
Netscape Comment (\fBnsComment\fR) is a string extension containing a comment
which will be displayed when the certificate is viewed in some browsers.
.PP
Example:
.PP
.Vb 1
\& nsComment = "Some Random Comment"
.Ve
.PP
Other supported extensions in this category are: \fBnsBaseUrl\fR,
\&\fBnsRevocationUrl\fR, \fBnsCaRevocationUrl\fR, \fBnsRenewalUrl\fR, \fBnsCaPolicyUrl\fR
and \fBnsSslServerName\fR.
.SS "Netscape Certificate Type"
.IX Subsection "Netscape Certificate Type"
This is a multi-valued extensions which consists of a list of flags to be
included. It was used to indicate the purposes for which a certificate could
be used. The basicConstraints, keyUsage and extended key usage extensions are
now used instead.
.PP
Acceptable values for nsCertType are: \fBclient\fR, \fBserver\fR, \fBemail\fR,
\&\fBobjsign\fR, \fBreserved\fR, \fBsslCA\fR, \fBemailCA\fR, \fBobjCA\fR.
.SH "ARBITRARY EXTENSIONS"
.IX Header "ARBITRARY EXTENSIONS"
If an extension is not supported by the OpenSSL code then it must be encoded
using the arbitrary extension format. It is also possible to use the arbitrary
format for supported extensions. Extreme care should be taken to ensure that
the data is formatted correctly for the given extension type.
.PP
There are two ways to encode arbitrary extensions.
.PP
The first way is to use the word \s-1ASN1\s0 followed by the extension content
using the same syntax as \fBASN1_generate_nconf\fR\|(3).
For example:
.PP
.Vb 1
\& *******=critical,ASN1:UTF8String:Some random data
\&
\& *******=ASN1:SEQUENCE:seq_sect
\&
\& [seq_sect]
\&
\& field1 = UTF8:field1
\& field2 = UTF8:field2
.Ve
.PP
It is also possible to use the word \s-1DER\s0 to include the raw encoded data in any
extension.
.PP
.Vb 2
\& *******=critical,DER:01:02:03:04
\& *******=DER:01020304
.Ve
.PP
The value following \s-1DER\s0 is a hex dump of the \s-1DER\s0 encoding of the extension
Any extension can be placed in this form to override the default behaviour.
For example:
.PP
.Vb 1
\& basicConstraints=critical,DER:00:01:02:03
.Ve
.SH "WARNINGS"
.IX Header "WARNINGS"
There is no guarantee that a specific implementation will process a given
extension. It may therefore be sometimes possible to use certificates for
purposes prohibited by their extensions because a specific application does
not recognize or honour the values of the relevant extensions.
.PP
The \s-1DER\s0 and \s-1ASN1\s0 options should be used with caution. It is possible to create
totally invalid extensions if they are not used carefully.
.SH "NOTES"
.IX Header "NOTES"
If an extension is multi-value and a field value must contain a comma the long
form must be used otherwise the comma would be misinterpreted as a field
separator. For example:
.PP
.Vb 1
\& subjectAltName=URI:ldap://somehost.com/CN=foo,OU=bar
.Ve
.PP
will produce an error but the equivalent form:
.PP
.Vb 1
\& subjectAltName=@subject_alt_section
\&
\& [subject_alt_section]
\& subjectAltName=URI:ldap://somehost.com/CN=foo,OU=bar
.Ve
.PP
is valid.
.PP
Due to the behaviour of the OpenSSL \fBconf\fR library the same field name
can only occur once in a section. This means that:
.PP
.Vb 1
\& subjectAltName=@alt_section
\&
\& [alt_section]
\&
\& email=steve@here
\& email=steve@there
.Ve
.PP
will only recognize the last value. This can be worked around by using the form:
.PP
.Vb 1
\& [alt_section]
\&
\& email.1=steve@here
\& email.2=steve@there
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBreq\fR\|(1), \fBca\fR\|(1), \fBx509\fR\|(1),
\&\fBASN1_generate_nconf\fR\|(3)
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2004\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
