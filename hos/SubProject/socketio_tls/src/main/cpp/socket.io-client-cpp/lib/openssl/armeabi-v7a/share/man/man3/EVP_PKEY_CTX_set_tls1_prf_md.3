.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_CTX_SET_TLS1_PRF_MD 3"
.TH EVP_PKEY_CTX_SET_TLS1_PRF_MD 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
EVP_PKEY_CTX_set_tls1_prf_md, EVP_PKEY_CTX_set1_tls1_prf_secret, EVP_PKEY_CTX_add1_tls1_prf_seed \- TLS PRF key derivation algorithm
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/kdf.h>
\&
\& int EVP_PKEY_CTX_set_tls1_prf_md(EVP_PKEY_CTX *pctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_set1_tls1_prf_secret(EVP_PKEY_CTX *pctx,
\&                                       unsigned char *sec, int seclen);
\& int EVP_PKEY_CTX_add1_tls1_prf_seed(EVP_PKEY_CTX *pctx,
\&                                     unsigned char *seed, int seedlen);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
The \fB\s-1EVP_PKEY_TLS1_PRF\s0\fR algorithm implements the \s-1PRF\s0 key derivation function for
\&\s-1TLS.\s0 It has no associated private key and only implements key derivation
using \fBEVP_PKEY_derive\fR\|(3).
.PP
\&\fBEVP_PKEY_set_tls1_prf_md()\fR sets the message digest associated with the
\&\s-1TLS PRF.\s0 \fBEVP_md5_sha1()\fR is treated as a special case which uses the \s-1PRF\s0
algorithm using both \fB\s-1MD5\s0\fR and \fB\s-1SHA1\s0\fR as used in \s-1TLS 1.0\s0 and 1.1.
.PP
\&\fBEVP_PKEY_CTX_set_tls1_prf_secret()\fR sets the secret value of the \s-1TLS PRF\s0
to \fBseclen\fR bytes of the buffer \fBsec\fR. Any existing secret value is replaced
and any seed is reset.
.PP
\&\fBEVP_PKEY_CTX_add1_tls1_prf_seed()\fR sets the seed to \fBseedlen\fR bytes of \fBseed\fR.
If a seed is already set it is appended to the existing value.
.SH "STRING CTRLS"
.IX Header "STRING CTRLS"
The \s-1TLS PRF\s0 also supports string based control operations using
\&\fBEVP_PKEY_CTX_ctrl_str\fR\|(3).
The \fBtype\fR parameter \*(L"md\*(R" uses the supplied \fBvalue\fR as the name of the digest
algorithm to use.
The \fBtype\fR parameters \*(L"secret\*(R" and \*(L"seed\*(R" use the supplied \fBvalue\fR parameter
as a secret or seed value.
The names \*(L"hexsecret\*(R" and \*(L"hexseed\*(R" are similar except they take a hex string
which is converted to binary.
.SH "NOTES"
.IX Header "NOTES"
All these functions are implemented as macros.
.PP
A context for the \s-1TLS PRF\s0 can be obtained by calling:
.PP
.Vb 1
\& EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_TLS1_PRF, NULL);
.Ve
.PP
The digest, secret value and seed must be set before a key is derived or an
error occurs.
.PP
The total length of all seeds cannot exceed 1024 bytes in length: this should
be more than enough for any normal use of the \s-1TLS PRF.\s0
.PP
The output length of the \s-1PRF\s0 is specified by the length parameter in the
\&\fBEVP_PKEY_derive()\fR function. Since the output length is variable, setting
the buffer to \fB\s-1NULL\s0\fR is not meaningful for the \s-1TLS PRF.\s0
.PP
Optimised versions of the \s-1TLS PRF\s0 can be implemented in an \s-1ENGINE.\s0
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All these functions return 1 for success and 0 or a negative value for failure.
In particular a return value of \-2 indicates the operation is not supported by
the public key algorithm.
.SH "EXAMPLES"
.IX Header "EXAMPLES"
This example derives 10 bytes using \s-1SHA\-256\s0 with the secret key \*(L"secret\*(R"
and seed value \*(L"seed\*(R":
.PP
.Vb 3
\& EVP_PKEY_CTX *pctx;
\& unsigned char out[10];
\& size_t outlen = sizeof(out);
\&
\& pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_TLS1_PRF, NULL);
\& if (EVP_PKEY_derive_init(pctx) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set_tls1_prf_md(pctx, EVP_sha256()) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set1_tls1_prf_secret(pctx, "secret", 6) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_add1_tls1_prf_seed(pctx, "seed", 4) <= 0)
\&     /* Error */
\& if (EVP_PKEY_derive(pctx, out, &outlen) <= 0)
\&     /* Error */
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_CTX_ctrl_str\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2016\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
