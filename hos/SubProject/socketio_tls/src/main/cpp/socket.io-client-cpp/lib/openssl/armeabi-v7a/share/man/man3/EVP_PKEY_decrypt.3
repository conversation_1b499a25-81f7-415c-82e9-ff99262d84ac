.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_DECRYPT 3"
.TH EVP_PKEY_DECRYPT 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
EVP_PKEY_decrypt_init, EVP_PKEY_decrypt \- decrypt using a public key algorithm
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_decrypt_init(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_decrypt(EVP_PKEY_CTX *ctx,
\&                      unsigned char *out, size_t *outlen,
\&                      const unsigned char *in, size_t inlen);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_decrypt_init()\fR function initializes a public key algorithm
context using key \fBpkey\fR for a decryption operation.
.PP
The \fBEVP_PKEY_decrypt()\fR function performs a public key decryption operation
using \fBctx\fR. The data to be decrypted is specified using the \fBin\fR and
\&\fBinlen\fR parameters. If \fBout\fR is \fB\s-1NULL\s0\fR then the maximum size of the output
buffer is written to the \fBoutlen\fR parameter. If \fBout\fR is not \fB\s-1NULL\s0\fR then
before the call the \fBoutlen\fR parameter should contain the length of the
\&\fBout\fR buffer, if the call is successful the decrypted data is written to
\&\fBout\fR and the amount of data written to \fBoutlen\fR.
.SH "NOTES"
.IX Header "NOTES"
After the call to \fBEVP_PKEY_decrypt_init()\fR algorithm specific control
operations can be performed to set any appropriate parameters for the
operation.
.PP
The function \fBEVP_PKEY_decrypt()\fR can be called more than once on the same
context if several operations are performed using the same parameters.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_decrypt_init()\fR and \fBEVP_PKEY_decrypt()\fR return 1 for success and 0
or a negative value for failure. In particular a return value of \-2
indicates the operation is not supported by the public key algorithm.
.SH "EXAMPLES"
.IX Header "EXAMPLES"
Decrypt data using \s-1OAEP\s0 (for \s-1RSA\s0 keys):
.PP
.Vb 2
\& #include <openssl/evp.h>
\& #include <openssl/rsa.h>
\&
\& EVP_PKEY_CTX *ctx;
\& ENGINE *eng;
\& unsigned char *out, *in;
\& size_t outlen, inlen;
\& EVP_PKEY *key;
\&
\& /*
\&  * NB: assumes key, eng, in, inlen are already set up
\&  * and that key is an RSA private key
\&  */
\& ctx = EVP_PKEY_CTX_new(key, eng);
\& if (!ctx)
\&     /* Error occurred */
\& if (EVP_PKEY_decrypt_init(ctx) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set_rsa_padding(ctx, RSA_OAEP_PADDING) <= 0)
\&     /* Error */
\&
\& /* Determine buffer length */
\& if (EVP_PKEY_decrypt(ctx, NULL, &outlen, in, inlen) <= 0)
\&     /* Error */
\&
\& out = OPENSSL_malloc(outlen);
\&
\& if (!out)
\&     /* malloc failure */
\&
\& if (EVP_PKEY_decrypt(ctx, out, &outlen, in, inlen) <= 0)
\&     /* Error */
\&
\& /* Decrypted data is outlen bytes written to buffer out */
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH "HISTORY"
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.0.
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2006\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
