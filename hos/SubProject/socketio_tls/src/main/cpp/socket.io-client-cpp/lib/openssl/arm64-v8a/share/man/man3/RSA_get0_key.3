.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "RSA_GET0_KEY 3"
.TH RSA_GET0_KEY 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
RSA_set0_key, RSA_set0_factors, RSA_set0_crt_params, RSA_get0_key, RSA_get0_factors, RSA_get0_crt_params, RSA_get0_n, RSA_get0_e, RSA_get0_d, RSA_get0_p, RSA_get0_q, RSA_get0_dmp1, RSA_get0_dmq1, RSA_get0_iqmp, RSA_get0_pss_params, RSA_clear_flags, RSA_test_flags, RSA_set_flags, RSA_get0_engine, RSA_get_multi_prime_extra_count, RSA_get0_multi_prime_factors, RSA_get0_multi_prime_crt_params, RSA_set0_multi_prime_params, RSA_get_version \&\- Routines for getting and setting data in an RSA object
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
\&
\& int RSA_set0_key(RSA *r, BIGNUM *n, BIGNUM *e, BIGNUM *d);
\& int RSA_set0_factors(RSA *r, BIGNUM *p, BIGNUM *q);
\& int RSA_set0_crt_params(RSA *r, BIGNUM *dmp1, BIGNUM *dmq1, BIGNUM *iqmp);
\& void RSA_get0_key(const RSA *r,
\&                   const BIGNUM **n, const BIGNUM **e, const BIGNUM **d);
\& void RSA_get0_factors(const RSA *r, const BIGNUM **p, const BIGNUM **q);
\& void RSA_get0_crt_params(const RSA *r,
\&                          const BIGNUM **dmp1, const BIGNUM **dmq1,
\&                          const BIGNUM **iqmp);
\& const BIGNUM *RSA_get0_n(const RSA *d);
\& const BIGNUM *RSA_get0_e(const RSA *d);
\& const BIGNUM *RSA_get0_d(const RSA *d);
\& const BIGNUM *RSA_get0_p(const RSA *d);
\& const BIGNUM *RSA_get0_q(const RSA *d);
\& const BIGNUM *RSA_get0_dmp1(const RSA *r);
\& const BIGNUM *RSA_get0_dmq1(const RSA *r);
\& const BIGNUM *RSA_get0_iqmp(const RSA *r);
\& const RSA_PSS_PARAMS *RSA_get0_pss_params(const RSA *r);
\& void RSA_clear_flags(RSA *r, int flags);
\& int RSA_test_flags(const RSA *r, int flags);
\& void RSA_set_flags(RSA *r, int flags);
\& ENGINE *RSA_get0_engine(RSA *r);
\& int RSA_get_multi_prime_extra_count(const RSA *r);
\& int RSA_get0_multi_prime_factors(const RSA *r, const BIGNUM *primes[]);
\& int RSA_get0_multi_prime_crt_params(const RSA *r, const BIGNUM *exps[],
\&                                     const BIGNUM *coeffs[]);
\& int RSA_set0_multi_prime_params(RSA *r, BIGNUM *primes[], BIGNUM *exps[],
\&                                BIGNUM *coeffs[], int pnum);
\& int RSA_get_version(RSA *r);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
An \s-1RSA\s0 object contains the components for the public and private key,
\&\fBn\fR, \fBe\fR, \fBd\fR, \fBp\fR, \fBq\fR, \fBdmp1\fR, \fBdmq1\fR and \fBiqmp\fR.  \fBn\fR is
the modulus common to both public and private key, \fBe\fR is the public
exponent and \fBd\fR is the private exponent.  \fBp\fR, \fBq\fR, \fBdmp1\fR,
\&\fBdmq1\fR and \fBiqmp\fR are the factors for the second representation of a
private key (see PKCS#1 section 3 Key Types), where \fBp\fR and \fBq\fR are
the first and second factor of \fBn\fR and \fBdmp1\fR, \fBdmq1\fR and \fBiqmp\fR
are the exponents and coefficient for \s-1CRT\s0 calculations.
.PP
For multi-prime \s-1RSA\s0 (defined in \s-1RFC 8017\s0), there are also one or more
\&'triplet' in an \s-1RSA\s0 object. A triplet contains three members, \fBr\fR, \fBd\fR
and \fBt\fR. \fBr\fR is the additional prime besides \fBp\fR and \fBq\fR. \fBd\fR and
\&\fBt\fR are the exponent and coefficient for \s-1CRT\s0 calculations.
.PP
The \fBn\fR, \fBe\fR and \fBd\fR parameters can be obtained by calling
\&\fBRSA_get0_key()\fR.  If they have not been set yet, then \fB*n\fR, \fB*e\fR and
\&\fB*d\fR will be set to \s-1NULL.\s0  Otherwise, they are set to pointers to
their respective values. These point directly to the internal
representations of the values and therefore should not be freed
by the caller.
.PP
The \fBn\fR, \fBe\fR and \fBd\fR parameter values can be set by calling
\&\fBRSA_set0_key()\fR and passing the new values for \fBn\fR, \fBe\fR and \fBd\fR as
parameters to the function.  The values \fBn\fR and \fBe\fR must be non-NULL
the first time this function is called on a given \s-1RSA\s0 object. The
value \fBd\fR may be \s-1NULL.\s0 On subsequent calls any of these values may be
\&\s-1NULL\s0 which means the corresponding \s-1RSA\s0 field is left untouched.
Calling this function transfers the memory management of the values to
the \s-1RSA\s0 object, and therefore the values that have been passed in
should not be freed by the caller after this function has been called.
.PP
In a similar fashion, the \fBp\fR and \fBq\fR parameters can be obtained and
set with \fBRSA_get0_factors()\fR and \fBRSA_set0_factors()\fR, and the \fBdmp1\fR,
\&\fBdmq1\fR and \fBiqmp\fR parameters can be obtained and set with
\&\fBRSA_get0_crt_params()\fR and \fBRSA_set0_crt_params()\fR.
.PP
For \fBRSA_get0_key()\fR, \fBRSA_get0_factors()\fR, and \fBRSA_get0_crt_params()\fR,
\&\s-1NULL\s0 value \s-1BIGNUM\s0 ** output parameters are permitted. The functions
ignore \s-1NULL\s0 parameters but return values for other, non-NULL, parameters.
.PP
For multi-prime \s-1RSA,\s0 \fBRSA_get0_multi_prime_factors()\fR and \fBRSA_get0_multi_prime_params()\fR
can be used to obtain other primes and related \s-1CRT\s0 parameters. The
return values are stored in an array of \fB\s-1BIGNUM\s0 *\fR. \fBRSA_set0_multi_prime_params()\fR
sets a collect of multi-prime 'triplet' members (prime, exponent and coefficient)
into an \s-1RSA\s0 object.
.PP
Any of the values \fBn\fR, \fBe\fR, \fBd\fR, \fBp\fR, \fBq\fR, \fBdmp1\fR, \fBdmq1\fR, and \fBiqmp\fR can also be
retrieved separately by the corresponding function
\&\fBRSA_get0_n()\fR, \fBRSA_get0_e()\fR, \fBRSA_get0_d()\fR, \fBRSA_get0_p()\fR, \fBRSA_get0_q()\fR,
\&\fBRSA_get0_dmp1()\fR, \fBRSA_get0_dmq1()\fR, and \fBRSA_get0_iqmp()\fR, respectively.
.PP
\&\fBRSA_get0_pss_params()\fR is used to retrieve the RSA-PSS parameters.
.PP
\&\fBRSA_set_flags()\fR sets the flags in the \fBflags\fR parameter on the \s-1RSA\s0
object. Multiple flags can be passed in one go (bitwise ORed together).
Any flags that are already set are left set. \fBRSA_test_flags()\fR tests to
see whether the flags passed in the \fBflags\fR parameter are currently
set in the \s-1RSA\s0 object. Multiple flags can be tested in one go. All
flags that are currently set are returned, or zero if none of the
flags are set. \fBRSA_clear_flags()\fR clears the specified flags within the
\&\s-1RSA\s0 object.
.PP
\&\fBRSA_get0_engine()\fR returns a handle to the \s-1ENGINE\s0 that has been set for
this \s-1RSA\s0 object, or \s-1NULL\s0 if no such \s-1ENGINE\s0 has been set.
.PP
\&\fBRSA_get_version()\fR returns the version of an \s-1RSA\s0 object \fBr\fR.
.SH "NOTES"
.IX Header "NOTES"
Values retrieved with \fBRSA_get0_key()\fR are owned by the \s-1RSA\s0 object used
in the call and may therefore \fInot\fR be passed to \fBRSA_set0_key()\fR.  If
needed, duplicate the received value using \fBBN_dup()\fR and pass the
duplicate.  The same applies to \fBRSA_get0_factors()\fR and \fBRSA_set0_factors()\fR
as well as \fBRSA_get0_crt_params()\fR and \fBRSA_set0_crt_params()\fR.
.PP
The caller should obtain the size by calling \fBRSA_get_multi_prime_extra_count()\fR
in advance and allocate sufficient buffer to store the return values before
calling \fBRSA_get0_multi_prime_factors()\fR and \fBRSA_get0_multi_prime_params()\fR.
.PP
\&\fBRSA_set0_multi_prime_params()\fR always clears the original multi-prime
triplets in \s-1RSA\s0 object \fBr\fR and assign the new set of triplets into it.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRSA_set0_key()\fR, \fBRSA_set0_factors()\fR, \fBRSA_set0_crt_params()\fR and
\&\fBRSA_set0_multi_prime_params()\fR return 1 on success or 0 on failure.
.PP
\&\fBRSA_get0_n()\fR, \fBRSA_get0_e()\fR, \fBRSA_get0_d()\fR, \fBRSA_get0_p()\fR, \fBRSA_get0_q()\fR,
\&\fBRSA_get0_dmp1()\fR, \fBRSA_get0_dmq1()\fR, and \fBRSA_get0_iqmp()\fR
return the respective value.
.PP
\&\fBRSA_get0_multi_prime_factors()\fR and \fBRSA_get0_multi_prime_crt_params()\fR return
1 on success or 0 on failure.
.PP
\&\fBRSA_get_multi_prime_extra_count()\fR returns two less than the number of primes
in use, which is 0 for traditional \s-1RSA\s0 and the number of extra primes for
multi-prime \s-1RSA.\s0
.PP
\&\fBRSA_get_version()\fR returns \fB\s-1RSA_ASN1_VERSION_MULTI\s0\fR for multi-prime \s-1RSA\s0 and
\&\fB\s-1RSA_ASN1_VERSION_DEFAULT\s0\fR for normal two-prime \s-1RSA,\s0 as defined in \s-1RFC 8017.\s0
.PP
\&\fBRSA_test_flags()\fR returns the current state of the flags in the \s-1RSA\s0 object.
.PP
\&\fBRSA_get0_engine()\fR returns the \s-1ENGINE\s0 set for the \s-1RSA\s0 object or \s-1NULL\s0 if no
\&\s-1ENGINE\s0 has been set.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRSA_new\fR\|(3), \fBRSA_size\fR\|(3)
.SH "HISTORY"
.IX Header "HISTORY"
The \fBRSA_get0_pss_params()\fR function was added in OpenSSL 1.1.1e.
.PP
The
\&\fBRSA_get_multi_prime_extra_count()\fR, \fBRSA_get0_multi_prime_factors()\fR,
\&\fBRSA_get0_multi_prime_crt_params()\fR, \fBRSA_set0_multi_prime_params()\fR,
and \fBRSA_get_version()\fR functions were added in OpenSSL 1.1.1.
.PP
Other functions described here were added in OpenSSL 1.1.0.
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2016\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
