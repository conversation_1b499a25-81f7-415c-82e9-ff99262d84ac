[/
 / Copyright (c) 2003-2017 <PERSON> (chris at kohlhoff dot com)
 /
 / Distributed under the Boost Software License, Version 1.0. (See accompanying
 / file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
 /]

[section:examples Examples]


* [link asio.examples.cpp03_examples C++03 Examples]: Illustrates the use of
Asio using only C++03 language and library features. Where necessary, the
examples make use of selected Boost C++ libraries.

* [link asio.examples.cpp11_examples C++11 Examples]: Contains a limited set of
the C++03 Asio examples, updated to use only C++11 library and language
facilities. These examples do not make direct use of Boost C++ libraries.
[/boostify: non-boost docs start here]
To show the changes between C++03 and C++11, these examples include a diff
between the C++03 and C++11 versions.
[/boostify: non-boost docs end here]


[section:cpp03_examples C++03 Examples]


[heading Allocation]

This example shows how to customise the allocation of memory associated with
asynchronous operations.

* [@../src/examples/cpp03/allocation/server.cpp]


[heading Buffers]

This example demonstrates how to create reference counted buffers that can be
used with socket read and write operations.

* [@../src/examples/cpp03/buffers/reference_counted.cpp]


[heading Chat]

This example implements a chat server and client. The programs use a custom
protocol with a fixed length message header and variable length message body.

* [@../src/examples/cpp03/chat/chat_message.hpp]
* [@../src/examples/cpp03/chat/chat_client.cpp]
* [@../src/examples/cpp03/chat/chat_server.cpp]

The following POSIX-specific chat client demonstrates how to use the
[link asio.reference.posix__stream_descriptor posix::stream_descriptor] class to
perform console input and output.

* [@../src/examples/cpp03/chat/posix_chat_client.cpp]


[heading Echo]

A collection of simple clients and servers, showing the use of both synchronous
and asynchronous operations.

* [@../src/examples/cpp03/echo/async_tcp_echo_server.cpp]
* [@../src/examples/cpp03/echo/async_udp_echo_server.cpp]
* [@../src/examples/cpp03/echo/blocking_tcp_echo_client.cpp]
* [@../src/examples/cpp03/echo/blocking_tcp_echo_server.cpp]
* [@../src/examples/cpp03/echo/blocking_udp_echo_client.cpp]
* [@../src/examples/cpp03/echo/blocking_udp_echo_server.cpp]


[heading Fork]

These POSIX-specific examples show how to use Asio in conjunction with the
`fork()` system call. The first example illustrates the steps required to start
a daemon process:

* [@../src/examples/cpp03/fork/daemon.cpp]

The second example demonstrates how it is possible to fork a process from
within a completion handler.

* [@../src/examples/cpp03/fork/process_per_connection.cpp]


[heading HTTP Client]

Example programs implementing simple HTTP 1.0 clients. These examples show how
to use the [link asio.reference.read_until read_until] and [link
asio.reference.async_read_until async_read_until] functions.

* [@../src/examples/cpp03/http/client/sync_client.cpp]
* [@../src/examples/cpp03/http/client/async_client.cpp]


[heading HTTP Server]

This example illustrates the use of asio in a simple single-threaded server
implementation of HTTP 1.0. It demonstrates how to perform a clean shutdown by
cancelling all outstanding asynchronous operations.

* [@../src/examples/cpp03/http/server/connection.cpp]
* [@../src/examples/cpp03/http/server/connection.hpp]
* [@../src/examples/cpp03/http/server/connection_manager.cpp]
* [@../src/examples/cpp03/http/server/connection_manager.hpp]
* [@../src/examples/cpp03/http/server/header.hpp]
* [@../src/examples/cpp03/http/server/main.cpp]
* [@../src/examples/cpp03/http/server/mime_types.cpp]
* [@../src/examples/cpp03/http/server/mime_types.hpp]
* [@../src/examples/cpp03/http/server/reply.cpp]
* [@../src/examples/cpp03/http/server/reply.hpp]
* [@../src/examples/cpp03/http/server/request.hpp]
* [@../src/examples/cpp03/http/server/request_handler.cpp]
* [@../src/examples/cpp03/http/server/request_handler.hpp]
* [@../src/examples/cpp03/http/server/request_parser.cpp]
* [@../src/examples/cpp03/http/server/request_parser.hpp]
* [@../src/examples/cpp03/http/server/server.cpp]
* [@../src/examples/cpp03/http/server/server.hpp]


[heading HTTP Server 2]

An HTTP server using an io_context-per-CPU design.

* [@../src/examples/cpp03/http/server2/connection.cpp]
* [@../src/examples/cpp03/http/server2/connection.hpp]
* [@../src/examples/cpp03/http/server2/header.hpp]
* [@../src/examples/cpp03/http/server2/io_context_pool.cpp]
* [@../src/examples/cpp03/http/server2/io_context_pool.hpp]
* [@../src/examples/cpp03/http/server2/main.cpp]
* [@../src/examples/cpp03/http/server2/mime_types.cpp]
* [@../src/examples/cpp03/http/server2/mime_types.hpp]
* [@../src/examples/cpp03/http/server2/reply.cpp]
* [@../src/examples/cpp03/http/server2/reply.hpp]
* [@../src/examples/cpp03/http/server2/request.hpp]
* [@../src/examples/cpp03/http/server2/request_handler.cpp]
* [@../src/examples/cpp03/http/server2/request_handler.hpp]
* [@../src/examples/cpp03/http/server2/request_parser.cpp]
* [@../src/examples/cpp03/http/server2/request_parser.hpp]
* [@../src/examples/cpp03/http/server2/server.cpp]
* [@../src/examples/cpp03/http/server2/server.hpp]


[heading HTTP Server 3]

An HTTP server using a single io_context and a thread pool calling `io_context::run()`.

* [@../src/examples/cpp03/http/server3/connection.cpp]
* [@../src/examples/cpp03/http/server3/connection.hpp]
* [@../src/examples/cpp03/http/server3/header.hpp]
* [@../src/examples/cpp03/http/server3/main.cpp]
* [@../src/examples/cpp03/http/server3/mime_types.cpp]
* [@../src/examples/cpp03/http/server3/mime_types.hpp]
* [@../src/examples/cpp03/http/server3/reply.cpp]
* [@../src/examples/cpp03/http/server3/reply.hpp]
* [@../src/examples/cpp03/http/server3/request.hpp]
* [@../src/examples/cpp03/http/server3/request_handler.cpp]
* [@../src/examples/cpp03/http/server3/request_handler.hpp]
* [@../src/examples/cpp03/http/server3/request_parser.cpp]
* [@../src/examples/cpp03/http/server3/request_parser.hpp]
* [@../src/examples/cpp03/http/server3/server.cpp]
* [@../src/examples/cpp03/http/server3/server.hpp]


[heading HTTP Server 4]

A single-threaded HTTP server implemented using stackless coroutines.

* [@../src/examples/cpp03/http/server4/file_handler.cpp]
* [@../src/examples/cpp03/http/server4/file_handler.hpp]
* [@../src/examples/cpp03/http/server4/header.hpp]
* [@../src/examples/cpp03/http/server4/main.cpp]
* [@../src/examples/cpp03/http/server4/mime_types.cpp]
* [@../src/examples/cpp03/http/server4/mime_types.hpp]
* [@../src/examples/cpp03/http/server4/reply.cpp]
* [@../src/examples/cpp03/http/server4/reply.hpp]
* [@../src/examples/cpp03/http/server4/request.hpp]
* [@../src/examples/cpp03/http/server4/request_parser.cpp]
* [@../src/examples/cpp03/http/server4/request_parser.hpp]
* [@../src/examples/cpp03/http/server4/server.cpp]
* [@../src/examples/cpp03/http/server4/server.hpp]


[heading ICMP]

This example shows how to use raw sockets with ICMP to ping a remote host.

* [@../src/examples/cpp03/icmp/ping.cpp]
* [@../src/examples/cpp03/icmp/ipv4_header.hpp]
* [@../src/examples/cpp03/icmp/icmp_header.hpp]


[heading Invocation]

This example shows how to customise handler invocation. Completion handlers are
added to a priority queue rather than executed immediately.

* [@../src/examples/cpp03/invocation/prioritised_handlers.cpp]


[heading Iostreams]

Two examples showing how to use [link asio.reference.ip__tcp.iostream
ip::tcp::iostream].

* [@../src/examples/cpp03/iostreams/daytime_client.cpp]
* [@../src/examples/cpp03/iostreams/daytime_server.cpp]
* [@../src/examples/cpp03/iostreams/http_client.cpp]


[heading Multicast]

An example showing the use of multicast to transmit packets to a group of
subscribers.

* [@../src/examples/cpp03/multicast/receiver.cpp]
* [@../src/examples/cpp03/multicast/sender.cpp]


[heading Serialization]

This example shows how Boost.Serialization can be used with asio to encode and
decode structures for transmission over a socket.

* [@../src/examples/cpp03/serialization/client.cpp]
* [@../src/examples/cpp03/serialization/connection.hpp]
* [@../src/examples/cpp03/serialization/server.cpp]
* [@../src/examples/cpp03/serialization/stock.hpp]


[heading Services]

This example demonstrates how to integrate custom functionality (in this case,
for logging) into asio's [link asio.reference.io_context io_context], and
how to use a custom service with [link
asio.reference.basic_stream_socket basic_stream_socket<>].

* [@../src/examples/cpp03/services/basic_logger.hpp]
* [@../src/examples/cpp03/services/daytime_client.cpp]
* [@../src/examples/cpp03/services/logger.hpp]
* [@../src/examples/cpp03/services/logger_service.cpp]
* [@../src/examples/cpp03/services/logger_service.hpp]
* [@../src/examples/cpp03/services/stream_socket_service.hpp]


[heading SOCKS 4]

Example client program implementing the SOCKS 4 protocol for communication via
a proxy.

* [@../src/examples/cpp03/socks4/sync_client.cpp]
* [@../src/examples/cpp03/socks4/socks4.hpp]


[heading SSL]

Example client and server programs showing the use of the [link
asio.reference.ssl__stream ssl::stream<>] template with asynchronous operations.

* [@../src/examples/cpp03/ssl/client.cpp]
* [@../src/examples/cpp03/ssl/server.cpp]


[heading Timeouts]

A collection of examples showing how to cancel long running asynchronous
operations after a period of time.

* [@../src/examples/cpp03/timeouts/async_tcp_client.cpp]
* [@../src/examples/cpp03/timeouts/blocking_tcp_client.cpp]
* [@../src/examples/cpp03/timeouts/blocking_udp_client.cpp]
* [@../src/examples/cpp03/timeouts/server.cpp]


[heading Timers]

Examples showing how to customise deadline_timer using different time types.

* [@../src/examples/cpp03/timers/tick_count_timer.cpp]
* [@../src/examples/cpp03/timers/time_t_timer.cpp]


[heading Porthopper]

Example illustrating mixed synchronous and asynchronous operations, and how to
use Boost.Lambda with Asio.

* [@../src/examples/cpp03/porthopper/protocol.hpp]
* [@../src/examples/cpp03/porthopper/client.cpp]
* [@../src/examples/cpp03/porthopper/server.cpp]


[heading Nonblocking]

Example demonstrating reactor-style operations for integrating a third-party
library that wants to perform the I/O operations itself.

* [@../src/examples/cpp03/nonblocking/third_party_lib.cpp]


[heading Spawn]

Example of using the asio::spawn() function, a wrapper around the
[@http://www.boost.org/doc/libs/release/libs/coroutine/index.html Boost.Coroutine]
library, to implement a chain of asynchronous operations using stackful
coroutines.

* [@../src/examples/cpp03/spawn/echo_server.cpp]


[heading UNIX Domain Sockets]

Examples showing how to use UNIX domain (local) sockets.

* [@../src/examples/cpp03/local/connect_pair.cpp]
* [@../src/examples/cpp03/local/stream_server.cpp]
* [@../src/examples/cpp03/local/stream_client.cpp]


[heading Windows]

An example showing how to use the Windows-specific function `TransmitFile`
with Asio.

* [@../src/examples/cpp03/windows/transmit_file.cpp]


[endsect]


[section:cpp11_examples C++11 Examples]


[heading Allocation]

This example shows how to customise the allocation of memory associated with
asynchronous operations.

* [@../src/examples/cpp11/allocation/server.cpp] ([@examples/diffs/allocation/server.cpp.html diff to C++03])


[heading Buffers]

This example demonstrates how to create reference counted buffers that can be
used with socket read and write operations.

* [@../src/examples/cpp11/buffers/reference_counted.cpp] ([@examples/diffs/buffers/reference_counted.cpp.html diff to C++03])


[heading Chat]

This example implements a chat server and client. The programs use a custom
protocol with a fixed length message header and variable length message body.

* [@../src/examples/cpp11/chat/chat_message.hpp] ([@examples/diffs/chat/chat_message.hpp.html diff to C++03])
* [@../src/examples/cpp11/chat/chat_client.cpp] ([@examples/diffs/chat/chat_client.cpp.html diff to C++03])
* [@../src/examples/cpp11/chat/chat_server.cpp] ([@examples/diffs/chat/chat_server.cpp.html diff to C++03])


[heading Echo]

A collection of simple clients and servers, showing the use of both synchronous
and asynchronous operations.

* [@../src/examples/cpp11/echo/async_tcp_echo_server.cpp] ([@examples/diffs/echo/async_tcp_echo_server.cpp.html diff to C++03])
* [@../src/examples/cpp11/echo/async_udp_echo_server.cpp] ([@examples/diffs/echo/async_udp_echo_server.cpp.html diff to C++03])
* [@../src/examples/cpp11/echo/blocking_tcp_echo_client.cpp] ([@examples/diffs/echo/blocking_tcp_echo_client.cpp.html diff to C++03])
* [@../src/examples/cpp11/echo/blocking_tcp_echo_server.cpp] ([@examples/diffs/echo/blocking_tcp_echo_server.cpp.html diff to C++03])
* [@../src/examples/cpp11/echo/blocking_udp_echo_client.cpp] ([@examples/diffs/echo/blocking_udp_echo_client.cpp.html diff to C++03])
* [@../src/examples/cpp11/echo/blocking_udp_echo_server.cpp] ([@examples/diffs/echo/blocking_udp_echo_server.cpp.html diff to C++03])


[heading Futures]

This example demonstrates how to use std::future in conjunction with
Asio's asynchronous operations.

* [@../src/examples/cpp11/futures/daytime_client.cpp]


[heading HTTP Server]

This example illustrates the use of asio in a simple single-threaded server
implementation of HTTP 1.0. It demonstrates how to perform a clean shutdown by
cancelling all outstanding asynchronous operations.

* [@../src/examples/cpp11/http/server/connection.cpp] ([@examples/diffs/http/server/connection.cpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/connection.hpp] ([@examples/diffs/http/server/connection.hpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/connection_manager.cpp] ([@examples/diffs/http/server/connection_manager.cpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/connection_manager.hpp] ([@examples/diffs/http/server/connection_manager.hpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/header.hpp] ([@examples/diffs/http/server/header.hpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/main.cpp] ([@examples/diffs/http/server/main.cpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/mime_types.cpp] ([@examples/diffs/http/server/mime_types.cpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/mime_types.hpp] ([@examples/diffs/http/server/mime_types.hpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/reply.cpp] ([@examples/diffs/http/server/reply.cpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/reply.hpp] ([@examples/diffs/http/server/reply.hpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/request.hpp] ([@examples/diffs/http/server/request.hpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/request_handler.cpp] ([@examples/diffs/http/server/request_handler.cpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/request_handler.hpp] ([@examples/diffs/http/server/request_handler.hpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/request_parser.cpp] ([@examples/diffs/http/server/request_parser.cpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/request_parser.hpp] ([@examples/diffs/http/server/request_parser.hpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/server.cpp] ([@examples/diffs/http/server/server.cpp.html diff to C++03])
* [@../src/examples/cpp11/http/server/server.hpp] ([@examples/diffs/http/server/server.hpp.html diff to C++03])


[heading Spawn]

Example of using the asio::spawn() function, a wrapper around the
[@http://www.boost.org/doc/libs/release/libs/coroutine/index.html Boost.Coroutine]
library, to implement a chain of asynchronous operations using stackful
coroutines.

* [@../src/examples/cpp11/spawn/echo_server.cpp] ([@examples/diffs/spawn/echo_server.cpp.html diff to C++03])


[endsect]


[endsect]
