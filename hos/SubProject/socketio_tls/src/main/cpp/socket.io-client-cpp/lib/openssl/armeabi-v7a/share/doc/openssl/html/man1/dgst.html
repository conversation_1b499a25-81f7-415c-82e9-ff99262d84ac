<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>dgst</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-dgst, dgst - perform digest operations</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl dgst</b> [<b>-<i>digest</i></b>] [<b>-help</b>] [<b>-c</b>] [<b>-d</b>] [<b>-list</b>] [<b>-hex</b>] [<b>-binary</b>] [<b>-r</b>] [<b>-out filename</b>] [<b>-sign filename</b>] [<b>-keyform arg</b>] [<b>-passin arg</b>] [<b>-verify filename</b>] [<b>-prverify filename</b>] [<b>-signature filename</b>] [<b>-sigopt nm:v</b>] [<b>-hmac key</b>] [<b>-fips-fingerprint</b>] [<b>-rand file...</b>] [<b>-engine id</b>] [<b>-engine_impl</b>] [<b>file...</b>]</p>

<p><b>openssl</b> <i>digest</i> [<b>...</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The digest functions output the message digest of a supplied file or files in hexadecimal. The digest functions also generate and verify digital signatures using message digests.</p>

<p>The generic name, <b>dgst</b>, may be used with an option specifying the algorithm to be used. The default digest is <i>sha256</i>. A supported <i>digest</i> name may also be used as the command name. To see the list of supported algorithms, use the <i>list --digest-commands</i> command.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="digest"><b>-<i>digest</i></b></dt>
<dd>

<p>Specifies name of a supported digest to be used. To see the list of supported digests, use the command <i>list --digest-commands</i>.</p>

</dd>
<dt id="c"><b>-c</b></dt>
<dd>

<p>Print out the digest in two digit groups separated by colons, only relevant if <b>hex</b> format output is used.</p>

</dd>
<dt id="d"><b>-d</b></dt>
<dd>

<p>Print out BIO debugging information.</p>

</dd>
<dt id="list"><b>-list</b></dt>
<dd>

<p>Prints out a list of supported message digests.</p>

</dd>
<dt id="hex"><b>-hex</b></dt>
<dd>

<p>Digest is to be output as a hex dump. This is the default case for a &quot;normal&quot; digest as opposed to a digital signature. See NOTES below for digital signatures using <b>-hex</b>.</p>

</dd>
<dt id="binary"><b>-binary</b></dt>
<dd>

<p>Output the digest or signature in binary form.</p>

</dd>
<dt id="r"><b>-r</b></dt>
<dd>

<p>Output the digest in the &quot;coreutils&quot; format, including newlines. Used by programs like <b>sha1sum</b>.</p>

</dd>
<dt id="out-filename"><b>-out filename</b></dt>
<dd>

<p>Filename to output to, or standard output by default.</p>

</dd>
<dt id="sign-filename"><b>-sign filename</b></dt>
<dd>

<p>Digitally sign the digest using the private key in &quot;filename&quot;. Note this option does not support Ed25519 or Ed448 private keys.</p>

</dd>
<dt id="keyform-arg"><b>-keyform arg</b></dt>
<dd>

<p>Specifies the key format to sign digest with. The DER, PEM, P12, and ENGINE formats are supported.</p>

</dd>
<dt id="sigopt-nm:v"><b>-sigopt nm:v</b></dt>
<dd>

<p>Pass options to the signature algorithm during sign or verify operations. Names and values of these options are algorithm-specific.</p>

</dd>
<dt id="passin-arg"><b>-passin arg</b></dt>
<dd>

<p>The private key password source. For more information about the format of <b>arg</b> see <a href="../man1/openssl.html">&quot;Pass Phrase Options&quot; in openssl(1)</a>.</p>

</dd>
<dt id="verify-filename"><b>-verify filename</b></dt>
<dd>

<p>Verify the signature using the public key in &quot;filename&quot;. The output is either &quot;Verification OK&quot; or &quot;Verification Failure&quot;.</p>

</dd>
<dt id="prverify-filename"><b>-prverify filename</b></dt>
<dd>

<p>Verify the signature using the private key in &quot;filename&quot;.</p>

</dd>
<dt id="signature-filename"><b>-signature filename</b></dt>
<dd>

<p>The actual signature to verify.</p>

</dd>
<dt id="hmac-key"><b>-hmac key</b></dt>
<dd>

<p>Create a hashed MAC using &quot;key&quot;.</p>

</dd>
<dt id="mac-alg"><b>-mac alg</b></dt>
<dd>

<p>Create MAC (keyed Message Authentication Code). The most popular MAC algorithm is HMAC (hash-based MAC), but there are other MAC algorithms which are not based on hash, for instance <b>gost-mac</b> algorithm, supported by <b>ccgost</b> engine. MAC keys and other options should be set via <b>-macopt</b> parameter.</p>

</dd>
<dt id="macopt-nm:v"><b>-macopt nm:v</b></dt>
<dd>

<p>Passes options to MAC algorithm, specified by <b>-mac</b> key. Following options are supported by both by <b>HMAC</b> and <b>gost-mac</b>:</p>

<dl>

<dt id="key:string"><b>key:string</b></dt>
<dd>

<p>Specifies MAC key as alphanumeric string (use if key contain printable characters only). String length must conform to any restrictions of the MAC algorithm for example exactly 32 chars for gost-mac.</p>

</dd>
<dt id="hexkey:string"><b>hexkey:string</b></dt>
<dd>

<p>Specifies MAC key in hexadecimal form (two hex digits per byte). Key length must conform to any restrictions of the MAC algorithm for example exactly 32 chars for gost-mac.</p>

</dd>
</dl>

</dd>
<dt id="rand-file"><b>-rand file...</b></dt>
<dd>

<p>A file or files containing random data used to seed the random number generator. Multiple files can be specified separated by an OS-dependent character. The separator is <b>;</b> for MS-Windows, <b>,</b> for OpenVMS, and <b>:</b> for all others.</p>

</dd>
<dt id="writerand-file">[<b>-writerand file</b>]</dt>
<dd>

<p>Writes random data to the specified <i>file</i> upon exit. This can be used with a subsequent <b>-rand</b> flag.</p>

</dd>
<dt id="fips-fingerprint"><b>-fips-fingerprint</b></dt>
<dd>

<p>Compute HMAC using a specific key for certain OpenSSL-FIPS operations.</p>

</dd>
<dt id="engine-id"><b>-engine id</b></dt>
<dd>

<p>Use engine <b>id</b> for operations (including private key storage). This engine is not used as source for digest algorithms, unless it is also specified in the configuration file or <b>-engine_impl</b> is also specified.</p>

</dd>
<dt id="engine_impl"><b>-engine_impl</b></dt>
<dd>

<p>When used with the <b>-engine</b> option, it specifies to also use engine <b>id</b> for digest operations.</p>

</dd>
<dt id="file"><b>file...</b></dt>
<dd>

<p>File or files to digest. If no files are specified then standard input is used.</p>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>To create a hex-encoded message digest of a file: openssl dgst -md5 -hex file.txt</p>

<p>To sign a file using SHA-256 with binary file output: openssl dgst -sha256 -sign privatekey.pem -out signature.sign file.txt</p>

<p>To verify a signature: openssl dgst -sha256 -verify publickey.pem \ -signature signature.sign \ file.txt</p>

<h1 id="NOTES">NOTES</h1>

<p>The digest mechanisms that are available will depend on the options used when building OpenSSL. The <b>list digest-commands</b> command can be used to list them.</p>

<p>New or agile applications should use probably use SHA-256. Other digests, particularly SHA-1 and MD5, are still widely used for interoperating with existing formats and protocols.</p>

<p>When signing a file, <b>dgst</b> will automatically determine the algorithm (RSA, ECC, etc) to use for signing based on the private key&#39;s ASN.1 info. When verifying signatures, it only handles the RSA, DSA, or ECDSA signature itself, not the related data to identify the signer and algorithm used in formats such as x.509, CMS, and S/MIME.</p>

<p>A source of random numbers is required for certain signing algorithms, in particular ECDSA and DSA.</p>

<p>The signing and verify options should only be used if a single file is being signed or verified.</p>

<p>Hex signatures cannot be verified using <b>openssl</b>. Instead, use &quot;xxd -r&quot; or similar program to transform the hex signature into a binary signature prior to verification.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The default digest was changed from MD5 to SHA256 in OpenSSL 1.1.0. The FIPS-related options were removed in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the OpenSSL license (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


