.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "CMS_ENCRYPT 3"
.TH CMS_ENCRYPT 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
CMS_encrypt \- create a CMS envelopedData structure
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& CMS_ContentInfo *CMS_encrypt(STACK_OF(X509) *certs, BIO *in,
\&                              const EVP_CIPHER *cipher, unsigned int flags);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fBCMS_encrypt()\fR creates and returns a \s-1CMS\s0 EnvelopedData structure. \fBcerts\fR
is a list of recipient certificates. \fBin\fR is the content to be encrypted.
\&\fBcipher\fR is the symmetric cipher to use. \fBflags\fR is an optional set of flags.
.SH "NOTES"
.IX Header "NOTES"
Only certificates carrying \s-1RSA,\s0 Diffie-Hellman or \s-1EC\s0 keys are supported by this
function.
.PP
\&\fBEVP_des_ede3_cbc()\fR (triple \s-1DES\s0) is the algorithm of choice for S/MIME use
because most clients will support it.
.PP
The algorithm passed in the \fBcipher\fR parameter must support \s-1ASN1\s0 encoding of
its parameters.
.PP
Many browsers implement a \*(L"sign and encrypt\*(R" option which is simply an S/MIME
envelopedData containing an S/MIME signed message. This can be readily produced
by storing the S/MIME signed message in a memory \s-1BIO\s0 and passing it to
\&\fBCMS_encrypt()\fR.
.PP
The following flags can be passed in the \fBflags\fR parameter.
.PP
If the \fB\s-1CMS_TEXT\s0\fR flag is set \s-1MIME\s0 headers for type \fBtext/plain\fR are
prepended to the data.
.PP
Normally the supplied content is translated into \s-1MIME\s0 canonical format (as
required by the S/MIME specifications) if \fB\s-1CMS_BINARY\s0\fR is set no translation
occurs. This option should be used if the supplied data is in binary format
otherwise the translation will corrupt it. If \fB\s-1CMS_BINARY\s0\fR is set then
\&\fB\s-1CMS_TEXT\s0\fR is ignored.
.PP
OpenSSL will by default identify recipient certificates using issuer name
and serial number. If \fB\s-1CMS_USE_KEYID\s0\fR is set it will use the subject key
identifier value instead. An error occurs if all recipient certificates do not
have a subject key identifier extension.
.PP
If the \fB\s-1CMS_STREAM\s0\fR flag is set a partial \fBCMS_ContentInfo\fR structure is
returned suitable for streaming I/O: no data is read from the \s-1BIO\s0 \fBin\fR.
.PP
If the \fB\s-1CMS_PARTIAL\s0\fR flag is set a partial \fBCMS_ContentInfo\fR structure is
returned to which additional recipients and attributes can be added before
finalization.
.PP
The data being encrypted is included in the CMS_ContentInfo structure, unless
\&\fB\s-1CMS_DETACHED\s0\fR is set in which case it is omitted. This is rarely used in
practice and is not supported by \fBSMIME_write_CMS()\fR.
.SH "NOTES"
.IX Header "NOTES"
If the flag \fB\s-1CMS_STREAM\s0\fR is set the returned \fBCMS_ContentInfo\fR structure is
\&\fBnot\fR complete and outputting its contents via a function that does not
properly finalize the \fBCMS_ContentInfo\fR structure will give unpredictable
results.
.PP
Several functions including \fBSMIME_write_CMS()\fR, \fBi2d_CMS_bio_stream()\fR,
\&\fBPEM_write_bio_CMS_stream()\fR finalize the structure. Alternatively finalization
can be performed by obtaining the streaming \s-1ASN1\s0 \fB\s-1BIO\s0\fR directly using
\&\fBBIO_new_CMS()\fR.
.PP
The recipients specified in \fBcerts\fR use a \s-1CMS\s0 KeyTransRecipientInfo info
structure. KEKRecipientInfo is also supported using the flag \fB\s-1CMS_PARTIAL\s0\fR
and \fBCMS_add0_recipient_key()\fR.
.PP
The parameter \fBcerts\fR may be \s-1NULL\s0 if \fB\s-1CMS_PARTIAL\s0\fR is set and recipients
added later using \fBCMS_add1_recipient_cert()\fR or \fBCMS_add0_recipient_key()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_encrypt()\fR returns either a CMS_ContentInfo structure or \s-1NULL\s0 if an error
occurred. The error can be obtained from \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_decrypt\fR\|(3)
.SH "HISTORY"
.IX Header "HISTORY"
The \fB\s-1CMS_STREAM\s0\fR flag was first supported in OpenSSL 1.0.0.
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2008\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
