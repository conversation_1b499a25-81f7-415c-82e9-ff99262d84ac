.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_NEW 3"
.TH EVP_PKEY_NEW 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
EVP_PKEY_new, EVP_PKEY_up_ref, EVP_PKEY_free, EVP_PKEY_new_raw_private_key, EVP_PKEY_new_raw_public_key, EVP_PKEY_new_CMAC_key, EVP_PKEY_new_mac_key, EVP_PKEY_get_raw_private_key, EVP_PKEY_get_raw_public_key \&\- public/private key allocation and raw key handling functions
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_PKEY *EVP_PKEY_new(void);
\& int EVP_PKEY_up_ref(EVP_PKEY *key);
\& void EVP_PKEY_free(EVP_PKEY *key);
\&
\& EVP_PKEY *EVP_PKEY_new_raw_private_key(int type, ENGINE *e,
\&                                        const unsigned char *key, size_t keylen);
\& EVP_PKEY *EVP_PKEY_new_raw_public_key(int type, ENGINE *e,
\&                                       const unsigned char *key, size_t keylen);
\& EVP_PKEY *EVP_PKEY_new_CMAC_key(ENGINE *e, const unsigned char *priv,
\&                                 size_t len, const EVP_CIPHER *cipher);
\& EVP_PKEY *EVP_PKEY_new_mac_key(int type, ENGINE *e, const unsigned char *key,
\&                                int keylen);
\&
\& int EVP_PKEY_get_raw_private_key(const EVP_PKEY *pkey, unsigned char *priv,
\&                                  size_t *len);
\& int EVP_PKEY_get_raw_public_key(const EVP_PKEY *pkey, unsigned char *pub,
\&                                 size_t *len);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_new()\fR function allocates an empty \fB\s-1EVP_PKEY\s0\fR structure which is
used by OpenSSL to store public and private keys. The reference count is set to
\&\fB1\fR.
.PP
\&\fBEVP_PKEY_up_ref()\fR increments the reference count of \fBkey\fR.
.PP
\&\fBEVP_PKEY_free()\fR decrements the reference count of \fBkey\fR and, if the reference
count is zero, frees it up. If \fBkey\fR is \s-1NULL,\s0 nothing is done.
.PP
\&\fBEVP_PKEY_new_raw_private_key()\fR allocates a new \fB\s-1EVP_PKEY\s0\fR. If \fBe\fR is non-NULL
then the new \fB\s-1EVP_PKEY\s0\fR structure is associated with the engine \fBe\fR. The
\&\fBtype\fR argument indicates what kind of key this is. The value should be a \s-1NID\s0
for a public key algorithm that supports raw private keys, i.e. one of
\&\fB\s-1EVP_PKEY_HMAC\s0\fR, \fB\s-1EVP_PKEY_POLY1305\s0\fR, \fB\s-1EVP_PKEY_SIPHASH\s0\fR, \fB\s-1EVP_PKEY_X25519\s0\fR,
\&\fB\s-1EVP_PKEY_ED25519\s0\fR, \fB\s-1EVP_PKEY_X448\s0\fR or \fB\s-1EVP_PKEY_ED448\s0\fR. \fBkey\fR points to the
raw private key data for this \fB\s-1EVP_PKEY\s0\fR which should be of length \fBkeylen\fR.
The length should be appropriate for the type of the key. The public key data
will be automatically derived from the given private key data (if appropriate
for the algorithm type).
.PP
\&\fBEVP_PKEY_new_raw_public_key()\fR works in the same way as
\&\fBEVP_PKEY_new_raw_private_key()\fR except that \fBkey\fR points to the raw public key
data. The \fB\s-1EVP_PKEY\s0\fR structure will be initialised without any private key
information. Algorithm types that support raw public keys are
\&\fB\s-1EVP_PKEY_X25519\s0\fR, \fB\s-1EVP_PKEY_ED25519\s0\fR, \fB\s-1EVP_PKEY_X448\s0\fR or \fB\s-1EVP_PKEY_ED448\s0\fR.
.PP
\&\fBEVP_PKEY_new_CMAC_key()\fR works in the same way as \fBEVP_PKEY_new_raw_private_key()\fR
except it is only for the \fB\s-1EVP_PKEY_CMAC\s0\fR algorithm type. In addition to the
raw private key data, it also takes a cipher algorithm to be used during
creation of a \s-1CMAC\s0 in the \fBcipher\fR argument. The cipher should be a standard
encryption only cipher. For example \s-1AEAD\s0 and \s-1XTS\s0 ciphers should not be used.
.PP
\&\fBEVP_PKEY_new_mac_key()\fR works in the same way as \fBEVP_PKEY_new_raw_private_key()\fR.
New applications should use \fBEVP_PKEY_new_raw_private_key()\fR instead.
.PP
\&\fBEVP_PKEY_get_raw_private_key()\fR fills the buffer provided by \fBpriv\fR with raw
private key data. The size of the \fBpriv\fR buffer should be in \fB*len\fR on entry
to the function, and on exit \fB*len\fR is updated with the number of bytes
actually written. If the buffer \fBpriv\fR is \s-1NULL\s0 then \fB*len\fR is populated with
the number of bytes required to hold the key. The calling application is
responsible for ensuring that the buffer is large enough to receive the private
key data. This function only works for algorithms that support raw private keys.
Currently this is: \fB\s-1EVP_PKEY_HMAC\s0\fR, \fB\s-1EVP_PKEY_POLY1305\s0\fR, \fB\s-1EVP_PKEY_SIPHASH\s0\fR,
\&\fB\s-1EVP_PKEY_X25519\s0\fR, \fB\s-1EVP_PKEY_ED25519\s0\fR, \fB\s-1EVP_PKEY_X448\s0\fR or \fB\s-1EVP_PKEY_ED448\s0\fR.
.PP
\&\fBEVP_PKEY_get_raw_public_key()\fR fills the buffer provided by \fBpub\fR with raw
public key data. The size of the \fBpub\fR buffer should be in \fB*len\fR on entry
to the function, and on exit \fB*len\fR is updated with the number of bytes
actually written. If the buffer \fBpub\fR is \s-1NULL\s0 then \fB*len\fR is populated with
the number of bytes required to hold the key. The calling application is
responsible for ensuring that the buffer is large enough to receive the public
key data. This function only works for algorithms that support raw public  keys.
Currently this is: \fB\s-1EVP_PKEY_X25519\s0\fR, \fB\s-1EVP_PKEY_ED25519\s0\fR, \fB\s-1EVP_PKEY_X448\s0\fR or
\&\fB\s-1EVP_PKEY_ED448\s0\fR.
.SH "NOTES"
.IX Header "NOTES"
The \fB\s-1EVP_PKEY\s0\fR structure is used by various OpenSSL functions which require a
general private key without reference to any particular algorithm.
.PP
The structure returned by \fBEVP_PKEY_new()\fR is empty. To add a private or public
key to this empty structure use the appropriate functions described in
\&\fBEVP_PKEY_set1_RSA\fR\|(3), EVP_PKEY_set1_DSA, EVP_PKEY_set1_DH or
EVP_PKEY_set1_EC_KEY.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_new()\fR, \fBEVP_PKEY_new_raw_private_key()\fR, \fBEVP_PKEY_new_raw_public_key()\fR,
\&\fBEVP_PKEY_new_CMAC_key()\fR and \fBEVP_PKEY_new_mac_key()\fR return either the newly
allocated \fB\s-1EVP_PKEY\s0\fR structure or \fB\s-1NULL\s0\fR if an error occurred.
.PP
\&\fBEVP_PKEY_up_ref()\fR, \fBEVP_PKEY_get_raw_private_key()\fR and
\&\fBEVP_PKEY_get_raw_public_key()\fR return 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_set1_RSA\fR\|(3), EVP_PKEY_set1_DSA, EVP_PKEY_set1_DH or
EVP_PKEY_set1_EC_KEY
.SH "HISTORY"
.IX Header "HISTORY"
The
\&\fBEVP_PKEY_new()\fR and \fBEVP_PKEY_free()\fR functions exist in all versions of OpenSSL.
.PP
The \fBEVP_PKEY_up_ref()\fR function was added in OpenSSL 1.1.0.
.PP
The
\&\fBEVP_PKEY_new_raw_private_key()\fR, \fBEVP_PKEY_new_raw_public_key()\fR,
\&\fBEVP_PKEY_new_CMAC_key()\fR, \fBEVP_PKEY_new_raw_private_key()\fR and
\&\fBEVP_PKEY_get_raw_public_key()\fR functions were added in OpenSSL 1.1.1.
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2002\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
