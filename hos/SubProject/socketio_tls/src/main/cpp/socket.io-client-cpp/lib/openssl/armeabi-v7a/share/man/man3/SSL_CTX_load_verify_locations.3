.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "SSL_CTX_LOAD_VERIFY_LOCATIONS 3"
.TH SSL_CTX_LOAD_VERIFY_LOCATIONS 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
SSL_CTX_load_verify_locations, SSL_CTX_set_default_verify_paths, SSL_CTX_set_default_verify_dir, SSL_CTX_set_default_verify_file \- set default locations for trusted CA certificates
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_load_verify_locations(SSL_CTX *ctx, const char *CAfile,
\&                                   const char *CApath);
\&
\& int SSL_CTX_set_default_verify_paths(SSL_CTX *ctx);
\&
\& int SSL_CTX_set_default_verify_dir(SSL_CTX *ctx);
\&
\& int SSL_CTX_set_default_verify_file(SSL_CTX *ctx);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_load_verify_locations()\fR specifies the locations for \fBctx\fR, at
which \s-1CA\s0 certificates for verification purposes are located. The certificates
available via \fBCAfile\fR and \fBCApath\fR are trusted.
.PP
\&\fBSSL_CTX_set_default_verify_paths()\fR specifies that the default locations from
which \s-1CA\s0 certificates are loaded should be used. There is one default directory
and one default file. The default \s-1CA\s0 certificates directory is called \*(L"certs\*(R" in
the default OpenSSL directory. Alternatively the \s-1SSL_CERT_DIR\s0 environment
variable can be defined to override this location. The default \s-1CA\s0 certificates
file is called \*(L"cert.pem\*(R" in the default OpenSSL directory. Alternatively the
\&\s-1SSL_CERT_FILE\s0 environment variable can be defined to override this location.
.PP
\&\fBSSL_CTX_set_default_verify_dir()\fR is similar to
\&\fBSSL_CTX_set_default_verify_paths()\fR except that just the default directory is
used.
.PP
\&\fBSSL_CTX_set_default_verify_file()\fR is similar to
\&\fBSSL_CTX_set_default_verify_paths()\fR except that just the default file is
used.
.SH "NOTES"
.IX Header "NOTES"
If \fBCAfile\fR is not \s-1NULL,\s0 it points to a file of \s-1CA\s0 certificates in \s-1PEM\s0
format. The file can contain several \s-1CA\s0 certificates identified by
.PP
.Vb 3
\& \-\-\-\-\-BEGIN CERTIFICATE\-\-\-\-\-
\& ... (CA certificate in base64 encoding) ...
\& \-\-\-\-\-END CERTIFICATE\-\-\-\-\-
.Ve
.PP
sequences. Before, between, and after the certificates text is allowed
which can be used e.g. for descriptions of the certificates.
.PP
The \fBCAfile\fR is processed on execution of the \fBSSL_CTX_load_verify_locations()\fR
function.
.PP
If \fBCApath\fR is not \s-1NULL,\s0 it points to a directory containing \s-1CA\s0 certificates
in \s-1PEM\s0 format. The files each contain one \s-1CA\s0 certificate. The files are
looked up by the \s-1CA\s0 subject name hash value, which must hence be available.
If more than one \s-1CA\s0 certificate with the same name hash value exist, the
extension must be different (e.g. 9d66eef0.0, 9d66eef0.1 etc). The search
is performed in the ordering of the extension number, regardless of other
properties of the certificates.
Use the \fBc_rehash\fR utility to create the necessary links.
.PP
The certificates in \fBCApath\fR are only looked up when required, e.g. when
building the certificate chain or when actually performing the verification
of a peer certificate.
.PP
When looking up \s-1CA\s0 certificates, the OpenSSL library will first search the
certificates in \fBCAfile\fR, then those in \fBCApath\fR. Certificate matching
is done based on the subject name, the key identifier (if present), and the
serial number as taken from the certificate to be verified. If these data
do not match, the next certificate will be tried. If a first certificate
matching the parameters is found, the verification process will be performed;
no other certificates for the same parameters will be searched in case of
failure.
.PP
In server mode, when requesting a client certificate, the server must send
the list of CAs of which it will accept client certificates. This list
is not influenced by the contents of \fBCAfile\fR or \fBCApath\fR and must
explicitly be set using the
\&\fBSSL_CTX_set_client_CA_list\fR\|(3)
family of functions.
.PP
When building its own certificate chain, an OpenSSL client/server will
try to fill in missing certificates from \fBCAfile\fR/\fBCApath\fR, if the
certificate chain was not explicitly specified (see
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3),
\&\fBSSL_CTX_use_certificate\fR\|(3).
.SH "WARNINGS"
.IX Header "WARNINGS"
If several \s-1CA\s0 certificates matching the name, key identifier, and serial
number condition are available, only the first one will be examined. This
may lead to unexpected results if the same \s-1CA\s0 certificate is available
with different expiration dates. If a \*(L"certificate expired\*(R" verification
error occurs, no other certificate will be searched. Make sure to not
have expired certificates mixed with valid ones.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
For SSL_CTX_load_verify_locations the following return values can occur:
.IP "0" 4
The operation failed because \fBCAfile\fR and \fBCApath\fR are \s-1NULL\s0 or the
processing at one of the locations specified failed. Check the error
stack to find out the reason.
.IP "1" 4
.IX Item "1"
The operation succeeded.
.PP
\&\fBSSL_CTX_set_default_verify_paths()\fR, \fBSSL_CTX_set_default_verify_dir()\fR and
\&\fBSSL_CTX_set_default_verify_file()\fR all return 1 on success or 0 on failure. A
missing default location is still treated as a success.
.SH "EXAMPLES"
.IX Header "EXAMPLES"
Generate a \s-1CA\s0 certificate file with descriptive text from the \s-1CA\s0 certificates
ca1.pem ca2.pem ca3.pem:
.PP
.Vb 5
\& #!/bin/sh
\& rm CAfile.pem
\& for i in ca1.pem ca2.pem ca3.pem ; do
\&     openssl x509 \-in $i \-text >> CAfile.pem
\& done
.Ve
.PP
Prepare the directory /some/where/certs containing several \s-1CA\s0 certificates
for use as \fBCApath\fR:
.PP
.Vb 2
\& cd /some/where/certs
\& c_rehash .
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_set_client_CA_list\fR\|(3),
\&\fBSSL_get_client_CA_list\fR\|(3),
\&\fBSSL_CTX_use_certificate\fR\|(3),
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3),
\&\fBSSL_CTX_set_cert_store\fR\|(3),
\&\fBSSL_CTX_set_client_CA_list\fR\|(3)
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2000\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
