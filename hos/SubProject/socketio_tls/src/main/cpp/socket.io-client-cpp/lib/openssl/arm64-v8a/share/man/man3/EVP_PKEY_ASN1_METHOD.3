.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_ASN1_METHOD 3"
.TH EVP_PKEY_ASN1_METHOD 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
EVP_PKEY_ASN1_METHOD, EVP_PKEY_asn1_new, EVP_PKEY_asn1_copy, EVP_PKEY_asn1_free, EVP_PKEY_asn1_add0, EVP_PKEY_asn1_add_alias, EVP_PKEY_asn1_set_public, EVP_PKEY_asn1_set_private, EVP_PKEY_asn1_set_param, EVP_PKEY_asn1_set_free, EVP_PKEY_asn1_set_ctrl, EVP_PKEY_asn1_set_item, EVP_PKEY_asn1_set_siginf, EVP_PKEY_asn1_set_check, EVP_PKEY_asn1_set_public_check, EVP_PKEY_asn1_set_param_check, EVP_PKEY_asn1_set_security_bits, EVP_PKEY_asn1_set_set_priv_key, EVP_PKEY_asn1_set_set_pub_key, EVP_PKEY_asn1_set_get_priv_key, EVP_PKEY_asn1_set_get_pub_key, EVP_PKEY_get0_asn1 \&\- manipulating and registering EVP_PKEY_ASN1_METHOD structure
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& typedef struct evp_pkey_asn1_method_st EVP_PKEY_ASN1_METHOD;
\&
\& EVP_PKEY_ASN1_METHOD *EVP_PKEY_asn1_new(int id, int flags,
\&                                         const char *pem_str,
\&                                         const char *info);
\& void EVP_PKEY_asn1_copy(EVP_PKEY_ASN1_METHOD *dst,
\&                         const EVP_PKEY_ASN1_METHOD *src);
\& void EVP_PKEY_asn1_free(EVP_PKEY_ASN1_METHOD *ameth);
\& int EVP_PKEY_asn1_add0(const EVP_PKEY_ASN1_METHOD *ameth);
\& int EVP_PKEY_asn1_add_alias(int to, int from);
\&
\& void EVP_PKEY_asn1_set_public(EVP_PKEY_ASN1_METHOD *ameth,
\&                               int (*pub_decode) (EVP_PKEY *pk,
\&                                                  X509_PUBKEY *pub),
\&                               int (*pub_encode) (X509_PUBKEY *pub,
\&                                                  const EVP_PKEY *pk),
\&                               int (*pub_cmp) (const EVP_PKEY *a,
\&                                               const EVP_PKEY *b),
\&                               int (*pub_print) (BIO *out,
\&                                                 const EVP_PKEY *pkey,
\&                                                 int indent, ASN1_PCTX *pctx),
\&                               int (*pkey_size) (const EVP_PKEY *pk),
\&                               int (*pkey_bits) (const EVP_PKEY *pk));
\& void EVP_PKEY_asn1_set_private(EVP_PKEY_ASN1_METHOD *ameth,
\&                                int (*priv_decode) (EVP_PKEY *pk,
\&                                                    const PKCS8_PRIV_KEY_INFO
\&                                                    *p8inf),
\&                                int (*priv_encode) (PKCS8_PRIV_KEY_INFO *p8,
\&                                                    const EVP_PKEY *pk),
\&                                int (*priv_print) (BIO *out,
\&                                                   const EVP_PKEY *pkey,
\&                                                   int indent,
\&                                                   ASN1_PCTX *pctx));
\& void EVP_PKEY_asn1_set_param(EVP_PKEY_ASN1_METHOD *ameth,
\&                              int (*param_decode) (EVP_PKEY *pkey,
\&                                                   const unsigned char **pder,
\&                                                   int derlen),
\&                              int (*param_encode) (const EVP_PKEY *pkey,
\&                                                   unsigned char **pder),
\&                              int (*param_missing) (const EVP_PKEY *pk),
\&                              int (*param_copy) (EVP_PKEY *to,
\&                                                 const EVP_PKEY *from),
\&                              int (*param_cmp) (const EVP_PKEY *a,
\&                                                const EVP_PKEY *b),
\&                              int (*param_print) (BIO *out,
\&                                                  const EVP_PKEY *pkey,
\&                                                  int indent,
\&                                                  ASN1_PCTX *pctx));
\&
\& void EVP_PKEY_asn1_set_free(EVP_PKEY_ASN1_METHOD *ameth,
\&                             void (*pkey_free) (EVP_PKEY *pkey));
\& void EVP_PKEY_asn1_set_ctrl(EVP_PKEY_ASN1_METHOD *ameth,
\&                             int (*pkey_ctrl) (EVP_PKEY *pkey, int op,
\&                                               long arg1, void *arg2));
\& void EVP_PKEY_asn1_set_item(EVP_PKEY_ASN1_METHOD *ameth,
\&                             int (*item_verify) (EVP_MD_CTX *ctx,
\&                                                 const ASN1_ITEM *it,
\&                                                 void *asn,
\&                                                 X509_ALGOR *a,
\&                                                 ASN1_BIT_STRING *sig,
\&                                                 EVP_PKEY *pkey),
\&                             int (*item_sign) (EVP_MD_CTX *ctx,
\&                                               const ASN1_ITEM *it,
\&                                               void *asn,
\&                                               X509_ALGOR *alg1,
\&                                               X509_ALGOR *alg2,
\&                                               ASN1_BIT_STRING *sig));
\&
\& void EVP_PKEY_asn1_set_siginf(EVP_PKEY_ASN1_METHOD *ameth,
\&                               int (*siginf_set) (X509_SIG_INFO *siginf,
\&                                                  const X509_ALGOR *alg,
\&                                                  const ASN1_STRING *sig));
\&
\& void EVP_PKEY_asn1_set_check(EVP_PKEY_ASN1_METHOD *ameth,
\&                              int (*pkey_check) (const EVP_PKEY *pk));
\&
\& void EVP_PKEY_asn1_set_public_check(EVP_PKEY_ASN1_METHOD *ameth,
\&                                     int (*pkey_pub_check) (const EVP_PKEY *pk));
\&
\& void EVP_PKEY_asn1_set_param_check(EVP_PKEY_ASN1_METHOD *ameth,
\&                                    int (*pkey_param_check) (const EVP_PKEY *pk));
\&
\& void EVP_PKEY_asn1_set_security_bits(EVP_PKEY_ASN1_METHOD *ameth,
\&                                      int (*pkey_security_bits) (const EVP_PKEY
\&                                                                 *pk));
\&
\& void EVP_PKEY_asn1_set_set_priv_key(EVP_PKEY_ASN1_METHOD *ameth,
\&                                     int (*set_priv_key) (EVP_PKEY *pk,
\&                                                          const unsigned char
\&                                                             *priv,
\&                                                          size_t len));
\&
\& void EVP_PKEY_asn1_set_set_pub_key(EVP_PKEY_ASN1_METHOD *ameth,
\&                                    int (*set_pub_key) (EVP_PKEY *pk,
\&                                                        const unsigned char *pub,
\&                                                        size_t len));
\&
\& void EVP_PKEY_asn1_set_get_priv_key(EVP_PKEY_ASN1_METHOD *ameth,
\&                                     int (*get_priv_key) (const EVP_PKEY *pk,
\&                                                          unsigned char *priv,
\&                                                          size_t *len));
\&
\& void EVP_PKEY_asn1_set_get_pub_key(EVP_PKEY_ASN1_METHOD *ameth,
\&                                    int (*get_pub_key) (const EVP_PKEY *pk,
\&                                                        unsigned char *pub,
\&                                                        size_t *len));
\&
\& const EVP_PKEY_ASN1_METHOD *EVP_PKEY_get0_asn1(const EVP_PKEY *pkey);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR is a structure which holds a set of \s-1ASN.1\s0
conversion, printing and information methods for a specific public key
algorithm.
.PP
There are two places where the \fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR objects are
stored: one is a built-in array representing the standard methods for
different algorithms, and the other one is a stack of user-defined
application-specific methods, which can be manipulated by using
\&\fBEVP_PKEY_asn1_add0\fR\|(3).
.SS "Methods"
.IX Subsection "Methods"
The methods are the underlying implementations of a particular public
key algorithm present by the \fB\s-1EVP_PKEY\s0\fR object.
.PP
.Vb 5
\& int (*pub_decode) (EVP_PKEY *pk, X509_PUBKEY *pub);
\& int (*pub_encode) (X509_PUBKEY *pub, const EVP_PKEY *pk);
\& int (*pub_cmp) (const EVP_PKEY *a, const EVP_PKEY *b);
\& int (*pub_print) (BIO *out, const EVP_PKEY *pkey, int indent,
\&                   ASN1_PCTX *pctx);
.Ve
.PP
The \fBpub_decode()\fR and \fBpub_encode()\fR methods are called to decode /
encode \fBX509_PUBKEY\fR \s-1ASN.1\s0 parameters to / from \fBpk\fR.
They \s-1MUST\s0 return 0 on error, 1 on success.
They're called by \fBX509_PUBKEY_get0\fR\|(3) and \fBX509_PUBKEY_set\fR\|(3).
.PP
The \fBpub_cmp()\fR method is called when two public keys are to be
compared.
It \s-1MUST\s0 return 1 when the keys are equal, 0 otherwise.
It's called by \fBEVP_PKEY_cmp\fR\|(3).
.PP
The \fBpub_print()\fR method is called to print a public key in humanly
readable text to \fBout\fR, indented \fBindent\fR spaces.
It \s-1MUST\s0 return 0 on error, 1 on success.
It's called by \fBEVP_PKEY_print_public\fR\|(3).
.PP
.Vb 4
\& int (*priv_decode) (EVP_PKEY *pk, const PKCS8_PRIV_KEY_INFO *p8inf);
\& int (*priv_encode) (PKCS8_PRIV_KEY_INFO *p8, const EVP_PKEY *pk);
\& int (*priv_print) (BIO *out, const EVP_PKEY *pkey, int indent,
\&                    ASN1_PCTX *pctx);
.Ve
.PP
The \fBpriv_decode()\fR and \fBpriv_encode()\fR methods are called to decode /
encode \fB\s-1PKCS8_PRIV_KEY_INFO\s0\fR form private key to / from \fBpk\fR.
They \s-1MUST\s0 return 0 on error, 1 on success.
They're called by \s-1\fBEVP_PKCS82PKEY\s0\fR\|(3) and \s-1\fBEVP_PKEY2PKCS8\s0\fR\|(3).
.PP
The \fBpriv_print()\fR method is called to print a private key in humanly
readable text to \fBout\fR, indented \fBindent\fR spaces.
It \s-1MUST\s0 return 0 on error, 1 on success.
It's called by \fBEVP_PKEY_print_private\fR\|(3).
.PP
.Vb 3
\& int (*pkey_size) (const EVP_PKEY *pk);
\& int (*pkey_bits) (const EVP_PKEY *pk);
\& int (*pkey_security_bits) (const EVP_PKEY *pk);
.Ve
.PP
The \fBpkey_size()\fR method returns the key size in bytes.
It's called by \fBEVP_PKEY_size\fR\|(3).
.PP
The \fBpkey_bits()\fR method returns the key size in bits.
It's called by \fBEVP_PKEY_bits\fR\|(3).
.PP
.Vb 8
\& int (*param_decode) (EVP_PKEY *pkey,
\&                      const unsigned char **pder, int derlen);
\& int (*param_encode) (const EVP_PKEY *pkey, unsigned char **pder);
\& int (*param_missing) (const EVP_PKEY *pk);
\& int (*param_copy) (EVP_PKEY *to, const EVP_PKEY *from);
\& int (*param_cmp) (const EVP_PKEY *a, const EVP_PKEY *b);
\& int (*param_print) (BIO *out, const EVP_PKEY *pkey, int indent,
\&                     ASN1_PCTX *pctx);
.Ve
.PP
The \fBparam_decode()\fR and \fBparam_encode()\fR methods are called to decode /
encode \s-1DER\s0 formatted parameters to / from \fBpk\fR.
They \s-1MUST\s0 return 0 on error, 1 on success.
They're called by \fBPEM_read_bio_Parameters\fR\|(3) and the \fBfile:\fR
\&\s-1\fBOSSL_STORE_LOADER\s0\fR\|(3).
.PP
The \fBparam_missing()\fR method returns 0 if a key parameter is missing,
otherwise 1.
It's called by \fBEVP_PKEY_missing_parameters\fR\|(3).
.PP
The \fBparam_copy()\fR method copies key parameters from \fBfrom\fR to \fBto\fR.
It \s-1MUST\s0 return 0 on error, 1 on success.
It's called by \fBEVP_PKEY_copy_parameters\fR\|(3).
.PP
The \fBparam_cmp()\fR method compares the parameters of keys \fBa\fR and \fBb\fR.
It \s-1MUST\s0 return 1 when the keys are equal, 0 when not equal, or a
negative number on error.
It's called by \fBEVP_PKEY_cmp_parameters\fR\|(3).
.PP
The \fBparam_print()\fR method prints the private key parameters in humanly
readable text to \fBout\fR, indented \fBindent\fR spaces.
It \s-1MUST\s0 return 0 on error, 1 on success.
It's called by \fBEVP_PKEY_print_params\fR\|(3).
.PP
.Vb 3
\& int (*sig_print) (BIO *out,
\&                   const X509_ALGOR *sigalg, const ASN1_STRING *sig,
\&                   int indent, ASN1_PCTX *pctx);
.Ve
.PP
The \fBsig_print()\fR method prints a signature in humanly readable text to
\&\fBout\fR, indented \fBindent\fR spaces.
\&\fBsigalg\fR contains the exact signature algorithm.
If the signature in \fBsig\fR doesn't correspond to what this method
expects, \fBX509_signature_dump()\fR must be used as a last resort.
It \s-1MUST\s0 return 0 on error, 1 on success.
It's called by \fBX509_signature_print\fR\|(3).
.PP
.Vb 1
\& void (*pkey_free) (EVP_PKEY *pkey);
.Ve
.PP
The \fBpkey_free()\fR method helps freeing the internals of \fBpkey\fR.
It's called by \fBEVP_PKEY_free\fR\|(3), \fBEVP_PKEY_set_type\fR\|(3),
\&\fBEVP_PKEY_set_type_str\fR\|(3), and \fBEVP_PKEY_assign\fR\|(3).
.PP
.Vb 1
\& int (*pkey_ctrl) (EVP_PKEY *pkey, int op, long arg1, void *arg2);
.Ve
.PP
The \fBpkey_ctrl()\fR method adds extra algorithm specific control.
It's called by \fBEVP_PKEY_get_default_digest_nid\fR\|(3),
\&\fBEVP_PKEY_set1_tls_encodedpoint\fR\|(3),
\&\fBEVP_PKEY_get1_tls_encodedpoint\fR\|(3), \fBPKCS7_SIGNER_INFO_set\fR\|(3),
\&\fBPKCS7_RECIP_INFO_set\fR\|(3), ...
.PP
.Vb 3
\& int (*old_priv_decode) (EVP_PKEY *pkey,
\&                         const unsigned char **pder, int derlen);
\& int (*old_priv_encode) (const EVP_PKEY *pkey, unsigned char **pder);
.Ve
.PP
The \fBold_priv_decode()\fR and \fBold_priv_encode()\fR methods decode / encode
they private key \fBpkey\fR from / to a \s-1DER\s0 formatted array.
These are exclusively used to help decoding / encoding older (pre
PKCS#8) \s-1PEM\s0 formatted encrypted private keys.
\&\fBold_priv_decode()\fR \s-1MUST\s0 return 0 on error, 1 on success.
\&\fBold_priv_encode()\fR \s-1MUST\s0 the return same kind of values as
\&\fBi2d_PrivateKey()\fR.
They're called by \fBd2i_PrivateKey\fR\|(3) and \fBi2d_PrivateKey\fR\|(3).
.PP
.Vb 5
\& int (*item_verify) (EVP_MD_CTX *ctx, const ASN1_ITEM *it, void *asn,
\&                     X509_ALGOR *a, ASN1_BIT_STRING *sig, EVP_PKEY *pkey);
\& int (*item_sign) (EVP_MD_CTX *ctx, const ASN1_ITEM *it, void *asn,
\&                   X509_ALGOR *alg1, X509_ALGOR *alg2,
\&                   ASN1_BIT_STRING *sig);
.Ve
.PP
The \fBitem_sign()\fR and  \fBitem_verify()\fR methods make it possible to have
algorithm specific signatures and verification of them.
.PP
\&\fBitem_sign()\fR \s-1MUST\s0 return one of:
.IP "<=0" 4
.IX Item "<=0"
error
.IP "1" 4
.IX Item "1"
\&\fBitem_sign()\fR did everything, OpenSSL internals just needs to pass the
signature length back.
.IP "2" 4
.IX Item "2"
\&\fBitem_sign()\fR did nothing, OpenSSL internal standard routines are
expected to continue with the default signature production.
.IP "3" 4
.IX Item "3"
\&\fBitem_sign()\fR set the algorithm identifier \fBalgor1\fR and \fBalgor2\fR,
OpenSSL internals should just sign using those algorithms.
.PP
\&\fBitem_verify()\fR \s-1MUST\s0 return one of:
.IP "<=0" 4
.IX Item "<=0"
error
.IP "1" 4
.IX Item "1"
\&\fBitem_sign()\fR did everything, OpenSSL internals just needs to pass the
signature length back.
.IP "2" 4
.IX Item "2"
\&\fBitem_sign()\fR did nothing, OpenSSL internal standard routines are
expected to continue with the default signature production.
.PP
\&\fBitem_verify()\fR and \fBitem_sign()\fR are called by \fBASN1_item_verify\fR\|(3) and
\&\fBASN1_item_sign\fR\|(3), and by extension, \fBX509_verify\fR\|(3),
\&\fBX509_REQ_verify\fR\|(3), \fBX509_sign\fR\|(3), \fBX509_REQ_sign\fR\|(3), ...
.PP
.Vb 2
\& int (*siginf_set) (X509_SIG_INFO *siginf, const X509_ALGOR *alg,
\&                    const ASN1_STRING *sig);
.Ve
.PP
The \fBsiginf_set()\fR method is used to set custom \fBX509_SIG_INFO\fR
parameters.
It \s-1MUST\s0 return 0 on error, or 1 on success.
It's called as part of \fBX509_check_purpose\fR\|(3), \fBX509_check_ca\fR\|(3)
and \fBX509_check_issued\fR\|(3).
.PP
.Vb 3
\& int (*pkey_check) (const EVP_PKEY *pk);
\& int (*pkey_public_check) (const EVP_PKEY *pk);
\& int (*pkey_param_check) (const EVP_PKEY *pk);
.Ve
.PP
The \fBpkey_check()\fR, \fBpkey_public_check()\fR and \fBpkey_param_check()\fR methods are used
to check the validity of \fBpk\fR for key-pair, public component and parameters,
respectively.
They \s-1MUST\s0 return 0 for an invalid key, or 1 for a valid key.
They are called by \fBEVP_PKEY_check\fR\|(3), \fBEVP_PKEY_public_check\fR\|(3) and
\&\fBEVP_PKEY_param_check\fR\|(3) respectively.
.PP
.Vb 2
\& int (*set_priv_key) (EVP_PKEY *pk, const unsigned char *priv, size_t len);
\& int (*set_pub_key) (EVP_PKEY *pk, const unsigned char *pub, size_t len);
.Ve
.PP
The \fBset_priv_key()\fR and \fBset_pub_key()\fR methods are used to set the raw private and
public key data for an \s-1EVP_PKEY.\s0 They \s-1MUST\s0 return 0 on error, or 1 on success.
They are called by \fBEVP_PKEY_new_raw_private_key\fR\|(3), and
\&\fBEVP_PKEY_new_raw_public_key\fR\|(3) respectively.
.SS "Functions"
.IX Subsection "Functions"
\&\fBEVP_PKEY_asn1_new()\fR creates and returns a new \fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR
object, and associates the given \fBid\fR, \fBflags\fR, \fBpem_str\fR and
\&\fBinfo\fR.
\&\fBid\fR is a \s-1NID,\s0 \fBpem_str\fR is the \s-1PEM\s0 type string, \fBinfo\fR is a
descriptive string.
The following \fBflags\fR are supported:
.PP
.Vb 1
\& ASN1_PKEY_SIGPARAM_NULL
.Ve
.PP
If \fB\s-1ASN1_PKEY_SIGPARAM_NULL\s0\fR is set, then the signature algorithm
parameters are given the type \fBV_ASN1_NULL\fR by default, otherwise
they will be given the type \fBV_ASN1_UNDEF\fR (i.e. the parameter is
omitted).
See \fBX509_ALGOR_set0\fR\|(3) for more information.
.PP
\&\fBEVP_PKEY_asn1_copy()\fR copies an \fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR object from
\&\fBsrc\fR to \fBdst\fR.
This function is not thread safe, it's recommended to only use this
when initializing the application.
.PP
\&\fBEVP_PKEY_asn1_free()\fR frees an existing \fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR pointed
by \fBameth\fR.
.PP
\&\fBEVP_PKEY_asn1_add0()\fR adds \fBameth\fR to the user defined stack of
methods unless another \fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR with the same \s-1NID\s0 is
already there.
This function is not thread safe, it's recommended to only use this
when initializing the application.
.PP
\&\fBEVP_PKEY_asn1_add_alias()\fR creates an alias with the \s-1NID\s0 \fBto\fR for the
\&\fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR with \s-1NID\s0 \fBfrom\fR unless another
\&\fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR with the same \s-1NID\s0 is already added.
This function is not thread safe, it's recommended to only use this
when initializing the application.
.PP
\&\fBEVP_PKEY_asn1_set_public()\fR, \fBEVP_PKEY_asn1_set_private()\fR,
\&\fBEVP_PKEY_asn1_set_param()\fR, \fBEVP_PKEY_asn1_set_free()\fR,
\&\fBEVP_PKEY_asn1_set_ctrl()\fR, \fBEVP_PKEY_asn1_set_item()\fR,
\&\fBEVP_PKEY_asn1_set_siginf()\fR, \fBEVP_PKEY_asn1_set_check()\fR,
\&\fBEVP_PKEY_asn1_set_public_check()\fR, \fBEVP_PKEY_asn1_set_param_check()\fR,
\&\fBEVP_PKEY_asn1_set_security_bits()\fR, \fBEVP_PKEY_asn1_set_set_priv_key()\fR,
\&\fBEVP_PKEY_asn1_set_set_pub_key()\fR, \fBEVP_PKEY_asn1_set_get_priv_key()\fR and
\&\fBEVP_PKEY_asn1_set_get_pub_key()\fR set the diverse methods of the given
\&\fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR object.
.PP
\&\fBEVP_PKEY_get0_asn1()\fR finds the \fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR associated
with the key \fBpkey\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_asn1_new()\fR returns \s-1NULL\s0 on error, or a pointer to an
\&\fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR object otherwise.
.PP
\&\fBEVP_PKEY_asn1_add0()\fR and \fBEVP_PKEY_asn1_add_alias()\fR return 0 on error,
or 1 on success.
.PP
\&\fBEVP_PKEY_get0_asn1()\fR returns \s-1NULL\s0 on error, or a pointer to a constant
\&\fB\s-1EVP_PKEY_ASN1_METHOD\s0\fR object otherwise.
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
