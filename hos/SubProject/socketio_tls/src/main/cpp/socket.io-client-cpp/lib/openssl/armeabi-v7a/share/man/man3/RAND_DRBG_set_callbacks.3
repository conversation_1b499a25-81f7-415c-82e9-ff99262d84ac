.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "RAND_DRBG_SET_CALLBACKS 3"
.TH RAND_DRBG_SET_CALLBACKS 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
RAND_DRBG_set_callbacks, RAND_DRBG_get_entropy_fn, RAND_DRBG_cleanup_entropy_fn, RAND_DRBG_get_nonce_fn, RAND_DRBG_cleanup_nonce_fn \&\- set callbacks for reseeding
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand_drbg.h>
\&
\&
\& int RAND_DRBG_set_callbacks(RAND_DRBG *drbg,
\&                             RAND_DRBG_get_entropy_fn get_entropy,
\&                             RAND_DRBG_cleanup_entropy_fn cleanup_entropy,
\&                             RAND_DRBG_get_nonce_fn get_nonce,
\&                             RAND_DRBG_cleanup_nonce_fn cleanup_nonce);
.Ve
.SS "Callback Functions"
.IX Subsection "Callback Functions"
.Vb 6
\& typedef size_t (*RAND_DRBG_get_entropy_fn)(
\&                       RAND_DRBG *drbg,
\&                       unsigned char **pout,
\&                       int entropy,
\&                       size_t min_len, size_t max_len,
\&                       int prediction_resistance);
\&
\& typedef void (*RAND_DRBG_cleanup_entropy_fn)(
\&                     RAND_DRBG *drbg,
\&                     unsigned char *out, size_t outlen);
\&
\& typedef size_t (*RAND_DRBG_get_nonce_fn)(
\&                       RAND_DRBG *drbg,
\&                       unsigned char **pout,
\&                       int entropy,
\&                       size_t min_len, size_t max_len);
\&
\& typedef void (*RAND_DRBG_cleanup_nonce_fn)(
\&                     RAND_DRBG *drbg,
\&                     unsigned char *out, size_t outlen);
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fBRAND_DRBG_set_callbacks()\fR sets the callbacks for obtaining fresh entropy and
the nonce when reseeding the given \fBdrbg\fR.
The callback functions are implemented and provided by the caller.
Their parameter lists need to match the function prototypes above.
.PP
Setting the callbacks is allowed only if the \s-1DRBG\s0 has not been initialized yet.
Otherwise, the operation will fail.
To change the settings for one of the three shared DRBGs it is necessary to call
\&\fBRAND_DRBG_uninstantiate()\fR first.
.PP
The \fBget_entropy\fR() callback is called by the \fBdrbg\fR when it requests fresh
random input.
It is expected that the callback allocates and fills a random buffer of size
\&\fBmin_len\fR <= size <= \fBmax_len\fR (in bytes) which contains at least \fBentropy\fR
bits of randomness.
The \fBprediction_resistance\fR flag indicates whether the reseeding was
triggered by a prediction resistance request.
.PP
The buffer's address is to be returned in *\fBpout\fR and the number of collected
randomness bytes as return value.
.PP
If the callback fails to acquire at least \fBentropy\fR bits of randomness,
it must indicate an error by returning a buffer length of 0.
.PP
If \fBprediction_resistance\fR was requested and the random source of the \s-1DRBG\s0
does not satisfy the conditions requested by [\s-1NIST SP 800\-90C\s0], then
it must also indicate an error by returning a buffer length of 0.
See \s-1NOTES\s0 section for more details.
.PP
The \fBcleanup_entropy\fR() callback is called from the \fBdrbg\fR to clear and
free the buffer allocated previously by \fBget_entropy()\fR.
The values \fBout\fR and \fBoutlen\fR are the random buffer's address and length,
as returned by the \fBget_entropy()\fR callback.
.PP
The \fBget_nonce\fR() and \fBcleanup_nonce\fR() callbacks are used to obtain a nonce
and free it again. A nonce is only required for instantiation (not for reseeding)
and only in the case where the \s-1DRBG\s0 uses a derivation function.
The callbacks are analogous to \fBget_entropy()\fR and \fBcleanup_entropy()\fR,
except for the missing prediction_resistance flag.
.PP
If the derivation function is disabled, then no nonce is used for instantiation,
and the \fBget_nonce\fR() and \fBcleanup_nonce\fR() callbacks can be omitted by
setting them to \s-1NULL.\s0
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_DRBG_set_callbacks()\fR return 1 on success, and 0 on failure
.SH "NOTES"
.IX Header "NOTES"
It is important that \fBcleanup_entropy\fR() and \fBcleanup_nonce\fR() clear the buffer
contents safely before freeing it, in order not to leave sensitive information
about the \s-1DRBG\s0's state in memory.
.PP
A request for prediction resistance can only be satisfied by pulling fresh
entropy from one of the approved entropy sources listed in section 5.5.2 of
[\s-1NIST SP 800\-90C\s0].
Since the default implementation of the get_entropy callback does not have access
to such an approved entropy source, a request for prediction resistance will
always fail.
In other words, prediction resistance is currently not supported yet by the \s-1DRBG.\s0
.PP
The derivation function is disabled during initialization by calling the
\&\fBRAND_DRBG_set()\fR function with the \s-1RAND_DRBG_FLAG_CTR_NO_DF\s0 flag.
For more information on the derivation function and when it can be omitted,
see [\s-1NIST SP 800\-90A\s0 Rev. 1]. Roughly speaking it can be omitted if the random
source has \*(L"full entropy\*(R", i.e., contains 8 bits of entropy per byte.
.PP
Even if a nonce is required, the \fBget_nonce\fR() and \fBcleanup_nonce\fR()
callbacks can be omitted by setting them to \s-1NULL.\s0
In this case the \s-1DRBG\s0 will automatically request an extra amount of entropy
(using the \fBget_entropy\fR() and \fBcleanup_entropy\fR() callbacks) which it will
utilize for the nonce, following the recommendations of [\s-1NIST SP 800\-90A\s0 Rev. 1],
section 8.6.7.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_DRBG_new\fR\|(3),
\&\fBRAND_DRBG_reseed\fR\|(3),
\&\s-1\fBRAND_DRBG\s0\fR\|(7)
.SH "HISTORY"
.IX Header "HISTORY"
The \s-1RAND_DRBG\s0 functions were added in OpenSSL 1.1.1.
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
