.\" Automatically generated by Pod::Man 4.11 (Pod::Simple 3.35)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" Set up some character translations and predefined strings.  \*(-- will
.\" give an unbreakable dash, \*(PI will give pi, \*(L" will give a left
.\" double quote, and \*(R" will give a right double quote.  \*(C+ will
.\" give a nicer C++.  Capital omega is used to do unbreakable dashes and
.\" therefore won't be available.  \*(C` and \*(C' expand to `' in nroff,
.\" nothing in troff, for use with C<>.
.tr \(*W-
.ds C+ C\v'-.1v'\h'-1p'\s-2+\h'-1p'+\s0\v'.1v'\h'-1p'
.ie n \{\
.    ds -- \(*W-
.    ds PI pi
.    if (\n(.H=4u)&(1m=24u) .ds -- \(*W\h'-12u'\(*W\h'-12u'-\" diablo 10 pitch
.    if (\n(.H=4u)&(1m=20u) .ds -- \(*W\h'-12u'\(*W\h'-8u'-\"  diablo 12 pitch
.    ds L" ""
.    ds R" ""
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds -- \|\(em\|
.    ds PI \(*p
.    ds L" ``
.    ds R" ''
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\"
.\" Accent mark definitions (@(#)ms.acc 1.5 88/02/08 SMI; from UCB 4.2).
.\" Fear.  Run.  Save yourself.  No user-serviceable parts.
.    \" fudge factors for nroff and troff
.if n \{\
.    ds #H 0
.    ds #V .8m
.    ds #F .3m
.    ds #[ \f1
.    ds #] \fP
.\}
.if t \{\
.    ds #H ((1u-(\\\\n(.fu%2u))*.13m)
.    ds #V .6m
.    ds #F 0
.    ds #[ \&
.    ds #] \&
.\}
.    \" simple accents for nroff and troff
.if n \{\
.    ds ' \&
.    ds ` \&
.    ds ^ \&
.    ds , \&
.    ds ~ ~
.    ds /
.\}
.if t \{\
.    ds ' \\k:\h'-(\\n(.wu*8/10-\*(#H)'\'\h"|\\n:u"
.    ds ` \\k:\h'-(\\n(.wu*8/10-\*(#H)'\`\h'|\\n:u'
.    ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'^\h'|\\n:u'
.    ds , \\k:\h'-(\\n(.wu*8/10)',\h'|\\n:u'
.    ds ~ \\k:\h'-(\\n(.wu-\*(#H-.1m)'~\h'|\\n:u'
.    ds / \\k:\h'-(\\n(.wu*8/10-\*(#H)'\z\(sl\h'|\\n:u'
.\}
.    \" troff and (daisy-wheel) nroff accents
.ds : \\k:\h'-(\\n(.wu*8/10-\*(#H+.1m+\*(#F)'\v'-\*(#V'\z.\h'.2m+\*(#F'.\h'|\\n:u'\v'\*(#V'
.ds 8 \h'\*(#H'\(*b\h'-\*(#H'
.ds o \\k:\h'-(\\n(.wu+\w'\(de'u-\*(#H)/2u'\v'-.3n'\*(#[\z\(de\v'.3n'\h'|\\n:u'\*(#]
.ds d- \h'\*(#H'\(pd\h'-\w'~'u'\v'-.25m'\f2\(hy\fP\v'.25m'\h'-\*(#H'
.ds D- D\\k:\h'-\w'D'u'\v'-.11m'\z\(hy\v'.11m'\h'|\\n:u'
.ds th \*(#[\v'.3m'\s+1I\s-1\v'-.3m'\h'-(\w'I'u*2/3)'\s-1o\s+1\*(#]
.ds Th \*(#[\s+2I\s-2\h'-\w'I'u*3/5'\v'-.3m'o\v'.3m'\*(#]
.ds ae a\h'-(\w'a'u*4/10)'e
.ds Ae A\h'-(\w'A'u*4/10)'E
.    \" corrections for vroff
.if v .ds ~ \\k:\h'-(\\n(.wu*9/10-\*(#H)'\s-2\u~\d\s+2\h'|\\n:u'
.if v .ds ^ \\k:\h'-(\\n(.wu*10/11-\*(#H)'\v'-.4m'^\v'.4m'\h'|\\n:u'
.    \" for low resolution devices (crt and lpr)
.if \n(.H>23 .if \n(.V>19 \
\{\
.    ds : e
.    ds 8 ss
.    ds o a
.    ds d- d\h'-1'\(ga
.    ds D- D\h'-1'\(hy
.    ds th \o'bp'
.    ds Th \o'LP'
.    ds ae ae
.    ds Ae AE
.\}
.rm #[ #] #H #V #F C
.\" ========================================================================
.\"
.IX Title "DSA_GENERATE_PARAMETERS 3"
.TH DSA_GENERATE_PARAMETERS 3 "2023-05-30" "1.1.1u" "OpenSSL"
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH "NAME"
DSA_generate_parameters_ex, DSA_generate_parameters \- generate DSA parameters
.SH "SYNOPSIS"
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dsa.h>
\&
\& int DSA_generate_parameters_ex(DSA *dsa, int bits,
\&                                const unsigned char *seed, int seed_len,
\&                                int *counter_ret, unsigned long *h_ret,
\&                                BN_GENCB *cb);
.Ve
.PP
Deprecated:
.PP
.Vb 5
\& #if OPENSSL_API_COMPAT < 0x00908000L
\& DSA *DSA_generate_parameters(int bits, unsigned char *seed, int seed_len,
\&                              int *counter_ret, unsigned long *h_ret,
\&                              void (*callback)(int, int, void *), void *cb_arg);
\& #endif
.Ve
.SH "DESCRIPTION"
.IX Header "DESCRIPTION"
\&\fBDSA_generate_parameters_ex()\fR generates primes p and q and a generator g
for use in the \s-1DSA\s0 and stores the result in \fBdsa\fR.
.PP
\&\fBbits\fR is the length of the prime p to be generated.
For lengths under 2048 bits, the length of q is 160 bits; for lengths
greater than or equal to 2048 bits, the length of q is set to 256 bits.
.PP
If \fBseed\fR is \s-1NULL,\s0 the primes will be generated at random.
If \fBseed_len\fR is less than the length of q, an error is returned.
.PP
\&\fBDSA_generate_parameters_ex()\fR places the iteration count in
*\fBcounter_ret\fR and a counter used for finding a generator in
*\fBh_ret\fR, unless these are \fB\s-1NULL\s0\fR.
.PP
A callback function may be used to provide feedback about the progress
of the key generation. If \fBcb\fR is not \fB\s-1NULL\s0\fR, it will be
called as shown below. For information on the \s-1BN_GENCB\s0 structure and the
BN_GENCB_call function discussed below, refer to
\&\fBBN_generate_prime\fR\|(3).
.PP
\&\fBDSA_generate_prime()\fR is similar to \fBDSA_generate_prime_ex()\fR but
expects an old-style callback function; see
\&\fBBN_generate_prime\fR\|(3) for information on the old-style callback.
.IP "\(bu" 2
When a candidate for q is generated, \fBBN_GENCB_call(cb, 0, m++)\fR is called
(m is 0 for the first candidate).
.IP "\(bu" 2
When a candidate for q has passed a test by trial division,
\&\fBBN_GENCB_call(cb, 1, \-1)\fR is called.
While a candidate for q is tested by Miller-Rabin primality tests,
\&\fBBN_GENCB_call(cb, 1, i)\fR is called in the outer loop
(once for each witness that confirms that the candidate may be prime);
i is the loop counter (starting at 0).
.IP "\(bu" 2
When a prime q has been found, \fBBN_GENCB_call(cb, 2, 0)\fR and
\&\fBBN_GENCB_call(cb, 3, 0)\fR are called.
.IP "\(bu" 2
Before a candidate for p (other than the first) is generated and tested,
\&\fBBN_GENCB_call(cb, 0, counter)\fR is called.
.IP "\(bu" 2
When a candidate for p has passed the test by trial division,
\&\fBBN_GENCB_call(cb, 1, \-1)\fR is called.
While it is tested by the Miller-Rabin primality test,
\&\fBBN_GENCB_call(cb, 1, i)\fR is called in the outer loop
(once for each witness that confirms that the candidate may be prime).
i is the loop counter (starting at 0).
.IP "\(bu" 2
When p has been found, \fBBN_GENCB_call(cb, 2, 1)\fR is called.
.IP "\(bu" 2
When the generator has been found, \fBBN_GENCB_call(cb, 3, 1)\fR is called.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDSA_generate_parameters_ex()\fR returns a 1 on success, or 0 otherwise.
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.PP
\&\fBDSA_generate_parameters()\fR returns a pointer to the \s-1DSA\s0 structure or
\&\fB\s-1NULL\s0\fR if the parameter generation fails.
.SH "BUGS"
.IX Header "BUGS"
Seed lengths greater than 20 are not supported.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDSA_new\fR\|(3), \fBERR_get_error\fR\|(3), \fBRAND_bytes\fR\|(3),
\&\fBDSA_free\fR\|(3), \fBBN_generate_prime\fR\|(3)
.SH "HISTORY"
.IX Header "HISTORY"
\&\fBDSA_generate_parameters()\fR was deprecated in OpenSSL 0.9.8; use
\&\fBDSA_generate_parameters_ex()\fR instead.
.SH "COPYRIGHT"
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the OpenSSL license (the \*(L"License\*(R").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file \s-1LICENSE\s0 in the source distribution or at
<https://www.openssl.org/source/license.html>.
