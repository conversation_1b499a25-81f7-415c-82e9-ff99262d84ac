import fs from '@ohos.file.fs';
import Version from './Version';
import AbcPreferences from './AbcPreferences';
import { util } from '@kit.ArkTS';
import { APP_VERSION } from '../../pages/Index';

interface ContentInterface {
  version: string,
  hostMinVersion: string,
  hostMaxVersion: string
}

export class PluginUtils {
  private static readonly ASSERT_SCHEME: string = "assets://";

  /**
   * 准备插件，会在内置，已下载，已下载tmp目录中选择版本最大的一个
   * 返回插件的根目录
   *
   * @param name 插件名
   * @param mvIfTmp 由于js bundle在运行时更新，不能直接删除到当前运行目录，需要先放到临时目录，这里是否需要将tmp目录移到正常目录
   * @returns 插件根目录路径
   */
  public static preparePlugin(name: string, mvIfTmp: boolean): string {
    // 下载插件目录
    const tmpSuffix = "_tmp";
    const downloadPluginRootDir = "/data/storage/el2/base/haps/entry/files/plugins";

    // 获取灰度标记
    // const grayFlag = AbcPreferences.getStringSync(AbcPreferences.PREFS_HIPPY_GRAY_FLAG, "");
    // const isGray = grayFlag.startsWith("gray");
    // const isPre = grayFlag.startsWith("pre");
    // const graySuffix = isGray ? "_gray" : (isPre ? "_pre" : "");

    const normalDir = `${downloadPluginRootDir}/${name}`;
    const tmpDir = `${normalDir}${tmpSuffix}`;

    // 检查所有可能的插件目录，选择版本最高的
    const pluginDirs = [normalDir, tmpDir];
    let maxVersion = Version.kMinVersion;
    let maxVersionPath: string = '';

    for (const dir of pluginDirs) {
      try {
        const version = PluginUtils.getPluginVersion(dir);
        if (version && version.compareTo(maxVersion) >= 0) {
          maxVersion = version;
          maxVersionPath = dir;
        }
      } catch (err) {
        console.log(`Failed to get plugin version for ${dir}: ${err.message}`);
      }
    }

    if (!maxVersionPath) {
      return "";
    }

    // 处理内置资源路径
    if (!maxVersionPath.startsWith("/")) {
      return PluginUtils.ASSERT_SCHEME + maxVersionPath;
    }

    // 如果是临时目录且需要移动到正常目录
    if ((maxVersionPath.endsWith(tmpSuffix)) && mvIfTmp) {
      try {
        // 删除正常目录（如果存在）
        try {
          if (fs.accessSync(normalDir)) {
            fs.rmdirSync(normalDir);
          }
        } catch (err) {
          // 目录不存在，忽略错误
        }

        // 确保目标目录的父目录存在
        const parentDir = downloadPluginRootDir;
        try {
          if (!fs.accessSync(parentDir)) {
            fs.mkdirSync(parentDir, true);
          }
        } catch (err) {
          fs.mkdirSync(parentDir, true);
        }

        // 移动到正常目录
        PluginUtils.copyDirectory(tmpDir, normalDir)

        // 查看normalDir文件列表
        const listDir = fs.listFileSync(normalDir);
        console.log("normalDir: ", listDir);

        // 删除临时目录
        fs.rmdir(tmpDir);

        // 更新路径，去掉临时后缀
        maxVersionPath = maxVersionPath.substring(0, maxVersionPath.length - tmpSuffix.length);
      } catch (err) {
        console.error(`Failed to move temp dir to normal dir: ${err.message}`);
      }
    }

    // 查看normalDir文件列表
    const listDir = fs.listFileSync(maxVersionPath);
    console.log("normalDir: ", listDir);
    return maxVersionPath;
  }

  /**
   * 获取插件版本信息
   * @param rootDir 插件根目录
   * @returns 插件版本，如果无法获取则返回null
   */
  public static getPluginVersion(rootDir: string): Version | null {
    let confDir = `${rootDir}/conf.json`;
    let content: string = '';

    try {
      if (confDir.startsWith("/")) {
        // 文件系统路径
        try {
          const listDir = fs.accessSync(confDir);
          if (listDir) {
            const file = fs.openSync(confDir, fs.OpenMode.READ_ONLY);
            const fileSize = fs.statSync(confDir).size;
            const buf = new ArrayBuffer(fileSize);
            fs.readSync(file.fd, buf);
            fs.closeSync(file);

            // 转换为字符串
            let decoder = util.TextDecoder.create('utf-8');
            content = decoder.decodeToString(new Uint8Array(buf));
          }
        } catch (err) {
          console.log(`Failed to read conf.json from ${confDir}: ${err.message}`);
        }
      } else if (rootDir.startsWith(PluginUtils.ASSERT_SCHEME)) {
        // 资源路径，需要特殊处理
        const assetPath = confDir.substring(PluginUtils.ASSERT_SCHEME.length);
        // 这里需要根据HarmonyOS的资源加载方式实现
        // 暂时留空，实际项目中需要实现
      } else {
        // 普通资源路径
        // 这里需要根据HarmonyOS的资源加载方式实现
        // 暂时留空，实际项目中需要实现
      }

      if (!content) {
        return null;
      }

      // 解析JSON
      const confJson = JSON.parse(content) as ContentInterface;

      const hostMinVersion = Version.fromString(confJson.hostMinVersion);
      const hostMaxVersion = Version.fromString(confJson.hostMaxVersion);
      const version = Version.fromString(confJson.version);

      // 获取应用版本
      const appVersion = Version.fromString(PluginUtils.getAppVersion());

      // 检查版本兼容性
      if (appVersion.compareTo(hostMinVersion) >= 0 && appVersion.compareTo(hostMaxVersion) <= 0) {
        return version;
      }
    } catch (err) {
      console.error(`Error parsing plugin version: ${err.message}`);
    }

    return null;
  }

  /**
   * 复制目录
   * @param srcDir 源目录
   * @param destDir 目标目录
   */
  private static copyDirectory(srcDir: string, destDir: string): void {
    try {
      // 创建目标目录
      fs.mkdirSync(destDir, true);

      // 获取源目录中的所有文件和子目录
      const files = fs.listFileSync(srcDir);

      // 遍历并复制每个文件/目录
      for (let i = 0; i < files.length; i++) {
        const srcPath = `${srcDir}/${files[i]}`;
        const destPath = `${destDir}/${files[i]}`;

        const stat = fs.statSync(srcPath);
        if (stat.isDirectory()) {
          // 递归复制子目录
          PluginUtils.copyDirectory(srcPath, destPath);
        } else {
          // 复制文件
          fs.copyFileSync(srcPath, destPath);
        }
      }
    } catch (err) {
      console.error(`Failed to copy directory: ${err.message}`);
    }
  }

  /**
   * 获取应用版本
   * @returns 应用版本字符串
   */
  private static getAppVersion(): string {
    return APP_VERSION;
  }
}

export default PluginUtils;
