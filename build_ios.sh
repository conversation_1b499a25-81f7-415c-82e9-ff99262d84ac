#!/bin/bash -x
version=2.8.4
preBuildNo=0000

if [ -f 'buildNum.txt' ]; then
    preBuildNo=`cat buildNum.txt`;
fi

buildNum=`expr ${preBuildNo} + 0`
buildNum=`printf "%04d" $buildNum`


echo ${buildNum} > buildNum.txt





versionName=$version.$buildNum
#test|release
buildPlan=test
buildComment=集成测试

tagPrefix=t
tagFolder=test
if [ "$buildPlan" == "test" ]; then
    tagPrefix=t
    tagFolder=test
elif [ "$buildPlan" == "release" ]; then
    tagPrefix=v
    tagFolder=release
fi



tagName="${tagFolder}/$tagPrefix$versionName"
versionCode=${versionName//./}





dest=`pwd`/dest_ios
echo "dest= $dest"

rm -rf $dest
mkdir -p $dest


timestamp=$(date "+%Y%m%d%H%M%S")




cd ios
echo "000003" > ./res/channel.txt
/usr/libexec/PlistBuddy -c "Set CFBundleShortVersionString ${version}" Runner/Info.plist
/usr/libexec/PlistBuddy -c "Set CFBundleVersion ${buildNum}" Runner/Info.plist

#pod install --verbose

xcodebuild -workspace Runner.xcworkspace -scheme Runner archive -archivePath $dest/Runner.xcarchive -destination 'generic/platform=iOS'
xcodebuild -exportArchive -archivePath $dest/Runner.xcarchive -exportOptionsPlist ./certs/AdHocTestExportOptions.plist -exportPath $dest -allowProvisioningUpdates
#mv $dest/abcyun-clinic-app.ipa $dest/abcyun-clinic-app-${versionName}-${timestamp}.ipa
mv $dest/ABCYun.ipa $dest/abcyun-clinic-app-${versionName}-${timestamp}.ipa
#rm -fr $dest/Runner.xcarchive



if [ $? == 0 ]; then
 echo "生成ipa包成功..."
 echo "upload ipa $dest/$apkName 到葡公英平台"
# curl -F "file=@$apkName" -F '_api_key=1c4cb6237f1420166a3040ff14c53fa3' https://www.pgyer.com/apiv2/app/upload
fi



osascript -e 'display dialog "IOS打包完成" buttons "Yes" with icon note'

open $dest
exit
#echo "开始编译生成archive..."
#xcodebuild -workspace Runner.xcworkspace -scheme Runner -configuration Release -sdk iphoneos -archivePath "$dest/abcyun" clean archive
#echo "开始生成ipa包..."
#xcodebuild -exportArchive -archivePath $dest/abcyun.xcarchive \
#-exportPath $dest/ipa/ \
#-exportOptionsPlist "build/ExportOptionsAdHoc.plist"

