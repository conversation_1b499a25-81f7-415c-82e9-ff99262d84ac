plugins { id 'com.android.application' }
plugins { id 'kotlin-android' }
plugins { id 'com.tencent.android.tpns' }

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def commonProperties = new Properties()
def commonPropertiesFile = rootProject.file('common.properties')
if (commonPropertiesFile.exists()) {
    commonPropertiesFile.withReader('UTF-8') { reader ->
        commonProperties.load(reader)
    }
}

def configVersionCode = commonProperties.getProperty('versionCode').toInteger()

def configVersionName = System.getenv("versionName")
if (configVersionName == null) {
    configVersionName = commonProperties.getProperty('versionName')
}

print 'configVersionName=' + configVersionName;
print 'configVersionCode=' + configVersionCode;

android {
    compileSdk 31

    lintOptions {
        disable 'InvalidPackage'
    }

    defaultConfig {
        applicationId "cn.abcyun.clinic.app"
        minSdk 19
        targetSdk 30
        versionCode configVersionCode
        versionName configVersionName

        multiDexEnabled true

        manifestPlaceholders = [
                XG_ACCESS_ID    : '2100346095',
                XG_ACCESS_KEY   : 'A15IG23JK2DK',
                XG_ACCESS_SCHEME: 'abcyunpush',// 非官方参数，可自定义(例如：fake_push)，统一打开方式为 intent
                HW_APPID        : '101406513',
                XIAOMI_APPID    : '2882303761518287527',// 小米通道
                XIAOMI_APPKEY   : '5851828740527',
                PACKAGE_NAME    : "${applicationId}",
        ]

        buildConfigField "String", "oppoAppID", "${oppo_appID}"
        buildConfigField "String", "oppoAppKey", "${oppo_appKey}"
        buildConfigField "String", "oppoAppSecret", "${oppo_appSecret}"
    }

    signingConfigs {
        debug {
            storeFile file("abcyun_clinic.keystore")
            storePassword "kd3tkz6U9F2RVz"
            keyAlias "abcyun"
            keyPassword "sk3Y3bSegxQP86"
        }

        release {
            storeFile file("abcyun_clinic.keystore")
            storePassword "kd3tkz6U9F2RVz"
            keyAlias "abcyun"
            keyPassword "sk3Y3bSegxQP86"
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.release
//
//            ndk {
//                //设置支持的SO库架构
//                abiFilters 'armeabi'//, 'arm64-v8a', 'x86', 'x86_64'
//            }
        }

        release {
            signingConfig signingConfigs.release
            minifyEnabled true
//            useProguard true

            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            ndk {
                //设置支持的SO库架构
                abiFilters 'armeabi-v7a', 'arm64-v8a'//, 'x86', 'x86_64'
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
//    implementation 'androidx.appcompat:appcompat:1.3.0'
//    implementation 'com.google.android.material:material:1.4.0'
    implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'])
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android-with-mta:+'
    api 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation project(':CommonBaseModule')
    implementation project(':hippy')
    def multidex_version = "2.0.1"
    implementation "androidx.multidex:multidex:$multidex_version"
    implementation 'com.squareup.okhttp3:okhttp:4.2.2'
}