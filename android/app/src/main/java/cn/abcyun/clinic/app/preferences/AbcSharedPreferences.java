package cn.abcyun.clinic.app.preferences;

import android.content.Context;
import android.content.SharedPreferences;

import com.abc.common.utils.ContextHolder;

public class AbcSharedPreferences {
    public static final String PREFS_HIPPY_DEBUG_SWITCH = "prefs_hippy_debug_switch";
    public static final String PREFS_HIPPY_LOG_LEVEL = "prefs_hippy_log_level";
    public static final String PREFS_HIPPY_GRAY_FLAG = "prefs_grayflag";


    private static final String SHARED_PREFERENCES_NAME = "ABCAppSharedPreferences";

    private static AbcSharedPreferences sInstance = new AbcSharedPreferences();

    public static AbcSharedPreferences getInstance() {
        return sInstance;
    }

    private SharedPreferences mPreferences;

    private AbcSharedPreferences() {
        this.mPreferences = ContextHolder.getAppContext().getSharedPreferences(SHARED_PREFERENCES_NAME, Context.MODE_PRIVATE);
    }


    public void setString(String key, String value) {
        this.mPreferences.edit().putString(key, value).apply();
    }


    public String getString(String key, String defaultValue) {
        return this.mPreferences.getString(key, defaultValue);
    }

    public void setBool(String key, boolean value) {
        this.mPreferences.edit().putBoolean(key, value).apply();
    }


    public boolean getBool(String key, boolean defaultValue) {
        return this.mPreferences.getBoolean(key, defaultValue);
    }

    public int getInt(String key, int defaultValue) {
        return this.mPreferences.getInt(key, defaultValue);
    }

    public void setInt(String key, int value) {
        this.mPreferences.edit().putInt(key, value).apply();
    }
}
