package cn.abcyun.clinic.app.xgpush;

import android.content.Context;

import com.tencent.android.tpush.XGPushBaseReceiver;
import com.tencent.android.tpush.XGPushClickedResult;
import com.tencent.android.tpush.XGPushRegisterResult;
import com.tencent.android.tpush.XGPushShowedResult;
import com.tencent.android.tpush.XGPushTextMessage;


public class XGMessageReceiver extends XGPushBaseReceiver{
    @Override
    public void onRegisterResult(Context context, int errorCode, XGPushRegisterResult message) {
        // 注册的回调
    }

    @Override
    public void onUnregisterResult(Context context, int errorCode) {
        // 反注册的回调
    }


    @Override
    public void onSetTagResult(Context context, int errorCode, String tagName) {
        // 设置tag的回调
    }

    @Override
    public void onDeleteTagResult(Context context, int errorCode, String tagName) {
        // 删除tag的回调
    }

    @Override
    public void onSetAccountResult(Context context, int i, String s) {

    }

    @Override
    public void onDeleteAccountResult(Context context, int i, String s) {

    }

    @Override
    public void onSetAttributeResult(Context context, int i, String s) {

    }

    @Override
    public void onQueryTagsResult(Context context, int i, String s, String s1) {

    }

    @Override
    public void onDeleteAttributeResult(Context context, int i, String s) {

    }

    @Override
    public void onTextMessage(Context context, XGPushTextMessage message) {
        // 消息透传的回调
        PushManagerForHippy.getInstance().onReceiveMessage(context, message);
    }

    @Override
    public void onNotificationClickedResult(Context context, XGPushClickedResult xgPushClickedResult) {
        // 通知点击回调 actionType=1为该消息被清除，actionType=0为该消息被点击。
        // 此处不能做点击消息跳转，详细方法请参照官网的Android常见问题文档
    }

    @Override
    public void onNotificationShowedResult(Context context, XGPushShowedResult xgPushShowedResult) {
        // 通知展示
        // notificationActionType==1为Activity，2为url，3为intent
        // Activity,url,intent都可以通过getActivity()获得
        PushManagerForHippy.getInstance().onReceiveNotification(context, xgPushShowedResult);
    }
}
