package cn.abcyun.clinic.app.wxapi;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import com.abc.hippy.modules.wxapi.WXAPIHandler;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;

public class WXEntryActivity extends Activity implements IWXAPIEventHandler {
    private static final String TAG = "WXEntryActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.d(TAG, "onCreate() called with: savedInstanceState = [" + savedInstanceState + "]");
        super.onCreate(savedInstanceState);
        try {
            WXAPIHandler.getInstance().handleIntent(getIntent(), this);
        } catch (Exception e) {
            e.printStackTrace();
            finish();
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        Log.d(TAG, "onNewIntent() called with: intent = [" + intent + "]");
        super.onNewIntent(intent);
        setIntent(intent);
        try {
            WXAPIHandler.getInstance().handleIntent(intent, this);
        } catch (Exception e) {
            e.printStackTrace();
            finish();
        }

    }

    @Override
    public void onReq(BaseReq baseReq) {
        Log.d(TAG, "onReq() called with: baseReq = [" + baseReq + "]");
        WXAPIHandler.getInstance().handleWXReq(baseReq);
    }

    @Override
    public void onResp(BaseResp baseResp) {
        WXAPIHandler.getInstance().handleWXRsp(baseResp);
        finish();
    }
}
