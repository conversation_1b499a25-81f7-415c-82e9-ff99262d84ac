package cn.abcyun.clinic.app.plugins;

import android.text.TextUtils;

import com.abc.common.utils.AppInfo;
import com.abc.common.utils.FileUtils;
import com.abc.common.utils.Version;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;

import cn.abcyun.clinic.app.preferences.AbcSharedPreferences;

public class PluginUtils {

    final static String ASSERT_SCHEME = "assets://";

    /**
     * 做一些插件准备工作，会在内置，已下载，已下载tmp目录中选择版本最大的一个
     * 返回插件的根目录
     *
     * @param name    插件名
     * @param mvIfTmp 由于js bundle在运行时更新，不能直接删除到当前运行目录，需要先放到临时目录，这里是否需要将tmp目录移到正常目录
     *                程序刚启动时，可以指定这个值
     * @return 插件根目录
     */
    static public String preparePlugin(String name, boolean mvIfTmp) {
        String assetPluginRootDir = "plugins/" + name;

        final String tmpSuffix = "_tmp";
        String downloadPluginRootDir = FileUtils.getDataDir() + "/plugins";

        String grayFlag = AbcSharedPreferences.getInstance().getString(AbcSharedPreferences.PREFS_HIPPY_GRAY_FLAG, "");
        boolean isGray = grayFlag.startsWith("gray");
        boolean isPre = grayFlag.startsWith("pre");
        String graySuffix = isGray ? "_gray" : (isPre? "_pre" : "");
        String normalDir = downloadPluginRootDir + "/" + name + graySuffix;
        String tmpDir = normalDir + tmpSuffix;
        String[] pluginDirs = new String[]{assetPluginRootDir, normalDir, tmpDir};
        Version maxVersion = Version.kMinVersion;
        String maxVersionPath = null;
        for (String dir : pluginDirs) {
            Version version = getPluginVersion(dir);
            if (version != null && version.compareTo(maxVersion) >= 0) {
                maxVersion = version;
                maxVersionPath = dir;
            }
        }

        if (maxVersionPath == null) return "";

        if (!maxVersionPath.startsWith("/"))
            return ASSERT_SCHEME + maxVersionPath;


        if (maxVersionPath.endsWith(tmpSuffix) && mvIfTmp) {
            FileUtils.deleteQuietly(new File(normalDir));
            FileUtils.copyMoveFile(tmpDir, normalDir);
            FileUtils.deleteQuietly(new File(tmpDir));

            maxVersionPath = maxVersionPath.substring(0, maxVersionPath.length() - tmpSuffix.length());
        }

        return maxVersionPath;
    }


    static public Version getPluginVersion(String rootDir) {
        String confDir = rootDir + "/conf.json";
        String content = null;
        if (confDir.startsWith("/")) {
            if (new File(confDir).exists()) {
                try {
                    content = FileUtils.readFileAsString(new File(confDir), "utf-8");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else if (rootDir.startsWith(ASSERT_SCHEME)) {
            content = FileUtils.readAssetFileAsString(confDir.substring(ASSERT_SCHEME.length()));
        } else {
            content = FileUtils.readAssetFileAsString(confDir);
        }


        if (TextUtils.isEmpty(content)) return null;

        try {
            JSONObject confJson = new JSONObject(content);

            Version hostMinVersion = Version.fromString(confJson.getString("hostMinVersion"));
            Version hostMaxVersion = Version.fromString(confJson.getString("hostMaxVersion"));
            Version version = Version.fromString(confJson.getString("version"));

            Version appVersion = Version.fromString(AppInfo.getAppVersion());
            if (appVersion.compareTo(hostMinVersion) >= 0 && appVersion.compareTo(hostMaxVersion) <= 0) {
                return version;
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }

        return null;
    }

}
