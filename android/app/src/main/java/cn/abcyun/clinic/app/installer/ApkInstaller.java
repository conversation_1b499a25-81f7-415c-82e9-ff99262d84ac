//package cn.abcyun.clinic.app.installer;
//
//import android.content.Intent;
//import android.net.Uri;
//import android.os.Build;
//import androidx.core.content.FileProvider;
//import android.text.TextUtils;
//
//import java.io.File;
//
//import cn.abcyun.clinic.app.BuildConfig;
//import cn.abcyun.clinic.app.ClinicApplication;
//import io.flutter.plugin.common.MethodCall;
//import io.flutter.plugin.common.MethodChannel;
//
//public class ApkInstaller implements MethodChannel.MethodCallHandler {
//    public static final String CHANNEL_NAME = "cn.abcyun.clinic/installer";
//
//    @Override
//    public void onMethodCall(MethodCall methodCall, MethodChannel.Result result) {
//        if (TextUtils.equals("install", methodCall.method)) {
//            String filepath = "";
//            if (methodparams.containsKey("filepath")) {
//                filepath = methodparams.get("filepath");
//            }
//
//            if (!TextUtils.isEmpty(filepath)) {
//                doInstallApk(filepath);
//                result.success(true);
//            } else {
//                result.success(false);
//            }
//        }
//    }
//
//    private void doInstallApk(String filepath) {
//
//        File apkFile = new File(filepath);
//        Intent intent;
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
////            intent = new Intent(Intent.ACTION_INSTALL_PACKAGE);
//            intent = new Intent(Intent.ACTION_VIEW);
//            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
//            Uri contentUri = FileProvider.getUriForFile(ClinicApplication.getInstance(), BuildConfig.APPLICATION_ID + ".FileProvider", apkFile);
//            intent.setDataAndType(contentUri, "application/vnd.android.package-archive");
//        } else {
//            intent = new Intent(Intent.ACTION_VIEW);
//            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//            intent.setDataAndType(Uri.fromFile(apkFile), "application/vnd.android.package-archive");
//        }
//
//        ClinicApplication.getInstance().startActivity(intent);
//    }
//}
