/**
 * 成都字节星期科技公司
 *
 * <AUTHOR>
 * @date 2020-09-28
 * @description 用于对接tpns消息推送，并通知到hippy侧
 */
package cn.abcyun.clinic.app.xgpush;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.abc.common.utils.ContextHolder;
import com.abc.hippy.modules.push.IPushManager;
import com.abc.hippy.modules.push.IPushManagerListener;
import com.abc.hippy.modules.push.PushManager;
import com.tencent.android.tpush.XGIOperateCallback;
import com.tencent.android.tpush.XGPushConfig;
import com.tencent.android.tpush.XGPushManager;
import com.tencent.android.tpush.XGPushShowedResult;
import com.tencent.android.tpush.XGPushTextMessage;

import java.util.HashMap;
import java.util.Map;

public class PushManagerForHippy implements IPushManager {

    private static PushManagerForHippy mInstance;

    private IPushManagerListener mListener;

    public static synchronized PushManagerForHippy getInstance() {
        if (mInstance == null)
            mInstance = new PushManagerForHippy();


        return mInstance;
    }

    public void enablePullUpOtherApp(Context context, boolean pullUp) {
        XGPushConfig.enablePullUpOtherApp(context, pullUp);
    }

    @Override
    public boolean handleNotificationClickedFromIntent(Intent intent) {
        String customContent = XinGeMSGClickActivity.extraClick(intent);
        if (customContent != null && mListener != null) {
            mListener.onResumeNotification(customContent);
            return true;
        }

        return false;
    }

    @Override
    public void onActivityDestroyed() {

    }

    @Override
    public void setListener(IPushManagerListener listener) {
        mListener = listener;
    }

    @Override
    public void start(Context appContext, boolean enableDebug, Intent intent) {
        //打开第三方推送
        XGPushConfig.enableOtherPush(appContext, true);
        XGPushConfig.enableDebug(appContext, enableDebug);
        XGPushManager.registerPush(appContext, new XGIOperateCallback() {
            @Override
            public void onSuccess(Object data, int flag) {
                //token在设备卸载重装的时候有可能会变
                Log.d("TPush", "注册成功，设备token为：" + data);
                if (mListener != null) {
                    mListener.onRegisterSuccess((String) data);
                }
            }

            @Override
            public void onFail(Object data, int errCode, String msg) {
                Log.d("TPush", "注册失败，错误码：" + errCode + ",错误信息：" + msg);
                if (mListener != null) {
                    mListener.onRegisterFailed(errCode + ",错误信息：" + msg);
                }
            }
        });
    }

    @Override
    public String getDeviceToken() {
        return XGPushConfig.getToken(ContextHolder.getAppContext());
    }

    @Override
    public Object getPushChannelId() {
        return 4;
    }

    public void onReceiveNotification(Context context, XGPushShowedResult message) {
        Map<String, Object> map = new HashMap<>();
        map.put(PushManager.ARGUMENT_KEY_RESULT_TITLE, message.getTitle());
        map.put(PushManager.ARGUMENT_KEY_RESULT_CONTENT, message.getContent());
        map.put(PushManager.ARGUMENT_KEY_RESULT_CUSTOMCONTENT, message.getActivity());
        int actionType = message.getNotificationActionType();
        if (actionType == XGPushShowedResult.NOTIFICATION_ACTION_INTENT || actionType == XGPushShowedResult.NOTIFICATION_ACTION_URL)
            map.put(PushManager.ARGUMENT_KEY_RESULT_URL, message.getActivity());

        if (mListener != null) {
            mListener.onReceiveMessage(map);
        }
    }

    public void onReceiveMessage(Context context, XGPushTextMessage message) {
        Map<String, Object> map = new HashMap<>();
        map.put(PushManager.ARGUMENT_KEY_RESULT_TITLE, message.getTitle());
        String dest = message.getContent();
        map.put(PushManager.ARGUMENT_KEY_RESULT_CONTENT, dest);
        map.put(PushManager.ARGUMENT_KEY_RESULT_CUSTOMCONTENT, message.getCustomContent());

        if (mListener != null) {
            mListener.onReceiveMessage(map);
        }
    }
}
