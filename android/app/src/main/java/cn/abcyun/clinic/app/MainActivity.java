package cn.abcyun.clinic.app;

import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.multidex.BuildConfig;

import com.abc.common.utils.BaseActivity;
import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.LogUtils;
import com.abc.common.utils.Version;
import com.abc.common.utils.bugly.BuglyManager;
import com.abc.hippy.HippyView;
import com.abc.hippy.modules.HostHippyMessageBridge;
import com.abc.hippy.modules.push.PushManager;
import com.abc.hippy.ui.statusbar.AndroidBug5497Workaround;
import com.abc.hippy.ui.statusbar.ImmersionBar;
import com.abc.hippy.ui.statusbar.StatusBarUtil;

import java.util.HashMap;
import java.util.Map;

import cn.abcyun.clinic.app.plugins.PluginUtils;
import cn.abcyun.clinic.app.preferences.AbcSharedPreferences;
import cn.abcyun.clinic.app.xgpush.PushManagerForHippy;

public class MainActivity extends BaseActivity implements HostHippyMessageBridge.IReloadRequestListener {
    private static final String TAG = "MainActivity";
    private FrameLayout mContentView;
    private HippyView mHippyView;
    private ImageView mSplashCoverView;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        LogUtils.d(TAG, "onCreate() called with: savedInstanceState = [" + savedInstanceState + "]");
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.WHITE));
        ContextHolder.setMainActivity(this);
        mContentView = new FrameLayout(this);
        mContentView.setLayoutParams(new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
        this.setContentView(mContentView);

        ImmersionBar.with(this).init();
        StatusBarUtil.StatusBarLightMode(this);
        AndroidBug5497Workaround.assistActivity(this);


        PushManagerForHippy.getInstance().enablePullUpOtherApp(this, false);
        PushManager.getInstance().setPushManager(PushManagerForHippy.getInstance());
        HostHippyMessageBridge.getInstance().addHippyReloadRequestListener(this);
        this.loadHippyBundle(true);

        mSplashCoverView = new ImageView(this);
        mSplashCoverView.setBackgroundResource(R.drawable.launch_background);
        mContentView.addView(mSplashCoverView, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                MainActivity.this.removeSplashView();
            }
        }, 3000);
    }


    @Override
    public void onBackPressed() {
        LogUtils.d(TAG, "onBackPressed");
        if (!mHippyView.onBackPressed()) {
            super.onBackPressed();
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        LogUtils.d(TAG, "onNewIntent() called with: intent = [" + intent + "]");
    }

    @Override
    protected void onPause() {
        LogUtils.d(TAG, "onPause() called");
        super.onPause();
        mHippyView.onPause();
    }

    @Override
    protected void onResume() {
        LogUtils.d(TAG, "onResume() called");
        super.onResume();
        mHippyView.onResume();
    }

    @Override
    protected void onDestroy() {
        LogUtils.d(TAG, "onDestroy() called");
        super.onDestroy();
        ContextHolder.setMainActivity(null);
        if (mHippyView != null) {
            mHippyView.destroy();
            mHippyView = null;
        }
    }

    @Override
    public void onSaveInstanceState(Bundle savedInstanceState) {
        super.onSaveInstanceState(savedInstanceState);
        LogUtils.d(TAG, "onSaveInstanceState() called with: savedInstanceState = [" + savedInstanceState + "]");
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        LogUtils.d(TAG, "onConfigurationChanged() called with: newConfig = [" + newConfig + "]");
    }


    private void removeSplashView() {
        if (mSplashCoverView != null) {
            final View splashView = mSplashCoverView;
            mSplashCoverView = null;
            AlphaAnimation animation = new AlphaAnimation(1.0f, 0.0f);
            animation.setDuration(300);
            animation.setRepeatCount(0);
            animation.setFillAfter(true);
            animation.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {
                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    mContentView.removeView(splashView);
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });

            splashView.startAnimation(animation);

        }
    }

    private void loadHippyBundle(boolean firstLoad) {
        if (mHippyView != null) {
            mContentView.removeView(mHippyView);
            mHippyView.destroy();
            mHippyView = null;
        }

        mHippyView = new HippyView(this);
        mHippyView.setBackgroundColor(Color.WHITE);
        mContentView.addView(mHippyView, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));

        String grayFlag = AbcSharedPreferences.getInstance().getString(AbcSharedPreferences.PREFS_HIPPY_GRAY_FLAG, "");
        boolean isGray = grayFlag.startsWith("gray");
        boolean isPre = grayFlag.startsWith("pre");
        int launchGrayFlag = isGray ? 1 : (isPre ? 2 : 0);

        String rootDir = PluginUtils.preparePlugin("abcyun-hippy", firstLoad);
        HashMap<String, Object> params = new HashMap<>();
        params.put("action", "abcyun://home");
        params.put("pwd", rootDir);
        params.put("launchGrayFlag", launchGrayFlag);
        params.put("androidFullScreen", true);

        Version version = PluginUtils.getPluginVersion(rootDir);
        if (version != null) {
            String versionStr = (rootDir.contains("gray") ? "g" : "v") + version.major + "." + version.minor + "." + version.revision + "." + version.build;
            BuglyManager.getInstance().setExceptionSuffix(versionStr);
        }


        int logLevel = AbcSharedPreferences.getInstance().getInt(AbcSharedPreferences.PREFS_HIPPY_LOG_LEVEL, -1);
        boolean debugMode = AbcSharedPreferences.getInstance().getBool(AbcSharedPreferences.PREFS_HIPPY_DEBUG_SWITCH, false);
        if (logLevel != -1 && (debugMode || BuildConfig.DEBUG)) {
            logLevel = 0;
        }

        if (logLevel != -1)
            params.put("logLevel", logLevel);

        mHippyView.load(rootDir, debugMode, params);
    }

    @Override
    public void onHippyRequestInvokeHostMethod(String methodName, Map<String, Object> params) {
        switch (methodName) {
            case "reload":
                ContextHolder.uiHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        MainActivity.this.loadHippyBundle(false);
                    }
                });
                break;
            case "setHippyDebug": {
                boolean enable = false;
                if (params != null) {
                    Boolean enableObj = (Boolean) params.get("enable");
                    if (enableObj != null)
                        enable = enableObj;
                }

                AbcSharedPreferences.getInstance().setBool(AbcSharedPreferences.PREFS_HIPPY_DEBUG_SWITCH, enable);
                break;
            }

            case "setLogDebugLevel":
                if (params != null) {
                    Object levelObj = params.get("level");
                    if (levelObj instanceof Integer)
                        AbcSharedPreferences.getInstance().setInt(AbcSharedPreferences.PREFS_HIPPY_LOG_LEVEL, (Integer) levelObj);
                }
                break;

            case "setGrayFlag":
                if (params != null) {
                    Object grayFlag = params.get("grayFlag");
                    if (grayFlag instanceof String)
                        AbcSharedPreferences.getInstance().setString(AbcSharedPreferences.PREFS_HIPPY_GRAY_FLAG, (String) grayFlag);
                }
                break;

            case "hippyFirstFrameReady":
                ContextHolder.uiHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        MainActivity.this.removeSplashView();
                    }
                });
                break;
        }
    }
}
