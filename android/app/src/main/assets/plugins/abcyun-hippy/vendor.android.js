var hippyReactBase=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}({"./node_modules/object-assign/index.js":function(e,t,n){"use strict";
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var r=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable;function a(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("**********"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(e){r[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(e){return!1}}()?Object.assign:function(e,t){for(var n,l,s=a(e),u=1;u<arguments.length;u++){for(var c in n=Object(arguments[u]))i.call(n,c)&&(s[c]=n[c]);if(r){l=r(n);for(var d=0;d<l.length;d++)o.call(n,l[d])&&(s[l[d]]=n[l[d]])}}return s}},"./node_modules/react/cjs/react.production.min.js":function(e,t,n){"use strict";
/** @license React v17.0.2
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n("./node_modules/object-assign/index.js"),i=60103,o=60106;t.Fragment=60107,t.StrictMode=60108,t.Profiler=60114;var a=60109,l=60110,s=60112;t.Suspense=60113;var u=60115,c=60116;if("function"==typeof Symbol&&Symbol.for){var d=Symbol.for;i=d("react.element"),o=d("react.portal"),t.Fragment=d("react.fragment"),t.StrictMode=d("react.strict_mode"),t.Profiler=d("react.profiler"),a=d("react.provider"),l=d("react.context"),s=d("react.forward_ref"),t.Suspense=d("react.suspense"),u=d("react.memo"),c=d("react.lazy")}var f="function"==typeof Symbol&&Symbol.iterator;function p(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m={};function y(e,t,n){this.props=e,this.context=t,this.refs=m,this.updater=n||h}function g(){}function b(e,t,n){this.props=e,this.context=t,this.refs=m,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error(p(85));this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},g.prototype=y.prototype;var v=b.prototype=new g;v.constructor=b,r(v,y.prototype),v.isPureReactComponent=!0;var w={current:null},k=Object.prototype.hasOwnProperty,S={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,n){var r,o={},a=null,l=null;if(null!=t)for(r in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(a=""+t.key),t)k.call(t,r)&&!S.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(1===s)o.children=n;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===o[r]&&(o[r]=s[r]);return{$$typeof:i,type:e,key:a,ref:l,props:o,_owner:w.current}}function x(e){return"object"==typeof e&&null!==e&&e.$$typeof===i}var C=/\/+/g;function N(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function I(e,t,n,r,a){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case i:case o:s=!0}}if(s)return a=a(s=e),e=""===r?"."+N(s,0):r,Array.isArray(a)?(n="",null!=e&&(n=e.replace(C,"$&/")+"/"),I(a,t,n,"",(function(e){return e}))):null!=a&&(x(a)&&(a=function(e,t){return{$$typeof:i,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,n+(!a.key||s&&s.key===a.key?"":(""+a.key).replace(C,"$&/")+"/")+e)),t.push(a)),1;if(s=0,r=""===r?".":r+":",Array.isArray(e))for(var u=0;u<e.length;u++){var c=r+N(l=e[u],u);s+=I(l,t,n,c,a)}else if("function"==typeof(c=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=f&&e[f]||e["@@iterator"])?e:null}(e)))for(e=c.call(e),u=0;!(l=e.next()).done;)s+=I(l=l.value,t,n,c=r+N(l,u++),a);else if("object"===l)throw t=""+e,Error(p(31,"[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t));return s}function P(e,t,n){if(null==e)return e;var r=[],i=0;return I(e,r,"","",(function(e){return t.call(n,e,i++)})),r}function _(e){if(-1===e._status){var t=e._result;t=t(),e._status=0,e._result=t,t.then((function(t){0===e._status&&(t=t.default,e._status=1,e._result=t)}),(function(t){0===e._status&&(e._status=2,e._result=t)}))}if(1===e._status)return e._result;throw e._result}var L={current:null};function A(){var e=L.current;if(null===e)throw Error(p(321));return e}var R={ReactCurrentDispatcher:L,ReactCurrentBatchConfig:{transition:0},ReactCurrentOwner:w,IsSomeRendererActing:{current:!1},assign:r};t.Children={map:P,forEach:function(e,t,n){P(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return P(e,(function(){t++})),t},toArray:function(e){return P(e,(function(e){return e}))||[]},only:function(e){if(!x(e))throw Error(p(143));return e}},t.Component=y,t.PureComponent=b,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=R,t.cloneElement=function(e,t,n){if(null==e)throw Error(p(267,e));var o=r({},e.props),a=e.key,l=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,s=w.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(c in t)k.call(t,c)&&!S.hasOwnProperty(c)&&(o[c]=void 0===t[c]&&void 0!==u?u[c]:t[c])}var c=arguments.length-2;if(1===c)o.children=n;else if(1<c){u=Array(c);for(var d=0;d<c;d++)u[d]=arguments[d+2];o.children=u}return{$$typeof:i,type:e.type,key:a,ref:l,props:o,_owner:s}},t.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:l,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:a,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:s,render:e}},t.isValidElement=x,t.lazy=function(e){return{$$typeof:c,_payload:{_status:-1,_result:e},_init:_}},t.memo=function(e,t){return{$$typeof:u,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return A().useCallback(e,t)},t.useContext=function(e,t){return A().useContext(e,t)},t.useDebugValue=function(){},t.useEffect=function(e,t){return A().useEffect(e,t)},t.useImperativeHandle=function(e,t,n){return A().useImperativeHandle(e,t,n)},t.useLayoutEffect=function(e,t){return A().useLayoutEffect(e,t)},t.useMemo=function(e,t){return A().useMemo(e,t)},t.useReducer=function(e,t,n){return A().useReducer(e,t,n)},t.useRef=function(e){return A().useRef(e)},t.useState=function(e){return A().useState(e)},t.version="17.0.2"},"./node_modules/react/index.js":function(e,t,n){"use strict";e.exports=n("./node_modules/react/cjs/react.production.min.js")},"./node_modules/scheduler/cjs/scheduler.production.min.js":function(e,t,n){"use strict";
/** @license React v0.20.2
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r,i,o,a;if("object"==typeof performance&&"function"==typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var s=Date,u=s.now();t.unstable_now=function(){return s.now()-u}}if("undefined"==typeof window||"function"!=typeof MessageChannel){var c=null,d=null,f=function(){if(null!==c)try{var e=t.unstable_now();c(!0,e),c=null}catch(e){throw setTimeout(f,0),e}};r=function(e){null!==c?setTimeout(r,0,e):(c=e,setTimeout(f,0))},i=function(e,t){d=setTimeout(e,t)},o=function(){clearTimeout(d)},t.unstable_shouldYield=function(){return!1},a=t.unstable_forceFrameRate=function(){}}else{var p=window.setTimeout,h=window.clearTimeout;if("undefined"!=typeof console){var m=window.cancelAnimationFrame;"function"!=typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills"),"function"!=typeof m&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://reactjs.org/link/react-polyfills")}var y=!1,g=null,b=-1,v=5,w=0;t.unstable_shouldYield=function(){return t.unstable_now()>=w},a=function(){},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):v=0<e?Math.floor(1e3/e):5};var k=new MessageChannel,S=k.port2;k.port1.onmessage=function(){if(null!==g){var e=t.unstable_now();w=e+v;try{g(!0,e)?S.postMessage(null):(y=!1,g=null)}catch(e){throw S.postMessage(null),e}}else y=!1},r=function(e){g=e,y||(y=!0,S.postMessage(null))},i=function(e,n){b=p((function(){e(t.unstable_now())}),n)},o=function(){h(b),b=-1}}function E(e,t){var n=e.length;e.push(t);e:for(;;){var r=n-1>>>1,i=e[r];if(!(void 0!==i&&0<N(i,t)))break e;e[r]=t,e[n]=i,n=r}}function x(e){return void 0===(e=e[0])?null:e}function C(e){var t=e[0];if(void 0!==t){var n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length;r<i;){var o=2*(r+1)-1,a=e[o],l=o+1,s=e[l];if(void 0!==a&&0>N(a,n))void 0!==s&&0>N(s,a)?(e[r]=s,e[l]=n,r=l):(e[r]=a,e[o]=n,r=o);else{if(!(void 0!==s&&0>N(s,n)))break e;e[r]=s,e[l]=n,r=l}}}return t}return null}function N(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}var I=[],P=[],_=1,L=null,A=3,R=!1,z=!1,O=!1;function T(e){for(var t=x(P);null!==t;){if(null===t.callback)C(P);else{if(!(t.startTime<=e))break;C(P),t.sortIndex=t.expirationTime,E(I,t)}t=x(P)}}function j(e){if(O=!1,T(e),!z)if(null!==x(I))z=!0,r(F);else{var t=x(P);null!==t&&i(j,t.startTime-e)}}function F(e,n){z=!1,O&&(O=!1,o()),R=!0;var r=A;try{for(T(n),L=x(I);null!==L&&(!(L.expirationTime>n)||e&&!t.unstable_shouldYield());){var a=L.callback;if("function"==typeof a){L.callback=null,A=L.priorityLevel;var l=a(L.expirationTime<=n);n=t.unstable_now(),"function"==typeof l?L.callback=l:L===x(I)&&C(I),T(n)}else C(I);L=x(I)}if(null!==L)var s=!0;else{var u=x(P);null!==u&&i(j,u.startTime-n),s=!1}return s}finally{L=null,A=r,R=!1}}var H=a;t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){z||R||(z=!0,r(F))},t.unstable_getCurrentPriorityLevel=function(){return A},t.unstable_getFirstCallbackNode=function(){return x(I)},t.unstable_next=function(e){switch(A){case 1:case 2:case 3:var t=3;break;default:t=A}var n=A;A=t;try{return e()}finally{A=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=H,t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=A;A=e;try{return t()}finally{A=n}},t.unstable_scheduleCallback=function(e,n,a){var l=t.unstable_now();switch("object"==typeof a&&null!==a?a="number"==typeof(a=a.delay)&&0<a?l+a:l:a=l,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:_++,callback:n,priorityLevel:e,startTime:a,expirationTime:s=a+s,sortIndex:-1},a>l?(e.sortIndex=a,E(P,e),null===x(I)&&e===x(P)&&(O?o():O=!0,i(j,a-l))):(e.sortIndex=s,E(I,e),z||R||(z=!0,r(F))),e},t.unstable_wrapCallback=function(e){var t=A;return function(){var n=A;A=t;try{return e.apply(this,arguments)}finally{A=n}}}},"./node_modules/scheduler/index.js":function(e,t,n){"use strict";e.exports=n("./node_modules/scheduler/cjs/scheduler.production.min.js")},"./node_modules/webpack/buildin/global.js":function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},"./node_modules/webpack/buildin/module.js":function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},"./scripts/vendor.js":function(e,t,n){n("./third-party/@hippy/react/dist/index.js")},"./third-party/@hippy/react/dist/index.js":function(e,t,n){"use strict";n.r(t),function(e){n.d(t,"Animated",(function(){return tn})),n.d(t,"Animation",(function(){return Ce})),n.d(t,"AnimationSet",(function(){return Ie})),n.d(t,"AppRegistry",(function(){return Gt})),n.d(t,"AsyncStorage",(function(){return Rn})),n.d(t,"BackAndroid",(function(){return zn})),n.d(t,"Clipboard",(function(){return Tn})),n.d(t,"ConsoleModule",(function(){return Kn})),n.d(t,"Dimensions",(function(){return nr})),n.d(t,"Easing",(function(){return nn})),n.d(t,"Focusable",(function(){return Nn})),n.d(t,"Hippy",(function(){return Yn})),n.d(t,"HippyEventEmitter",(function(){return de})),n.d(t,"HippyEventListener",(function(){return ue})),n.d(t,"HippyRegister",(function(){return Hn})),n.d(t,"Image",(function(){return en})),n.d(t,"ImageBackground",(function(){return Jn})),n.d(t,"ImageLoaderModule",(function(){return Mn})),n.d(t,"ListView",(function(){return un})),n.d(t,"ListViewItem",(function(){return rn})),n.d(t,"Modal",(function(){return Cn})),n.d(t,"Navigator",(function(){return fn})),n.d(t,"NetInfo",(function(){return Dn})),n.d(t,"NetworkModule",(function(){return jn})),n.d(t,"PixelRatio",(function(){return rr})),n.d(t,"Platform",(function(){return Gn})),n.d(t,"PullFooter",(function(){return an})),n.d(t,"PullHeader",(function(){return on})),n.d(t,"RNfqb",(function(){return Xn})),n.d(t,"RNfqbEventEmitter",(function(){return er})),n.d(t,"RNfqbEventListener",(function(){return tr})),n.d(t,"RNfqbRegister",(function(){return Zn})),n.d(t,"RefreshWrapper",(function(){return cn})),n.d(t,"ScrollView",(function(){return En})),n.d(t,"StyleSheet",(function(){return kn})),n.d(t,"Text",(function(){return Jt})),n.d(t,"TextInput",(function(){return gn})),n.d(t,"TimerModule",(function(){return Qn})),n.d(t,"UIManagerModule",(function(){return Un})),n.d(t,"View",(function(){return Yt})),n.d(t,"ViewPager",(function(){return hn})),n.d(t,"WaterfallView",(function(){return An})),n.d(t,"WebSocket",(function(){return _n})),n.d(t,"WebView",(function(){return In})),n.d(t,"callNative",(function(){return Bn})),n.d(t,"callNativeWithCallbackId",(function(){return $n})),n.d(t,"callNativeWithPromise",(function(){return Vn})),n.d(t,"colorParse",(function(){return Ut})),n.d(t,"default",(function(){return Kt})),n.d(t,"flushSync",(function(){return Wn})),n.d(t,"removeNativeCallback",(function(){return qn}));var r=n("./third-party/react-reconciler/index.js"),i=n.n(r),o=n("./node_modules/react/index.js"),a=n.n(o);const l=["children"],s=["collapsable","style"],u=["style"],c=["children","style","imageStyle","imageRef","source","sources","src","srcs","tintColor","tintColors"],d=["children"],f=["children"],p=["children","style","renderRow","renderPullHeader","renderPullFooter","getRowType","getRowStyle","getRowKey","dataSource","initialListSize","rowShouldSticky","onRowLayout","onHeaderPulling","onHeaderReleased","onFooterPulling","onFooterReleased","onAppear","onDisappear","onWillAppear","onWillDisappear"],h=["children"],m=["initialRoute"],y=["component"],g=["children","onPageScrollStateChanged"],b=["style","renderBanner","numberOfColumns","columnSpacing","interItemSpacing","numberOfItems","preloadItemNumber","renderItem","renderPullHeader","renderPullFooter","getItemType","getItemKey","getItemStyle","contentInset","onItemLayout","onHeaderPulling","onHeaderReleased","onFooterPulling","onFooterReleased","containPullHeader","containPullFooter","containBannerView"];function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){k(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function S(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}
/*!
 * @hippy/react v2.13.7
 * Build at: Sat Jul 09 2022 19:42:31 GMT+0800 (China Standard Time)
 *
 * Tencent is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2022 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}e.__GLOBAL__||(e.__GLOBAL__={}),e.__GLOBAL__.nodeId=0,e.__GLOBAL__.animationId=0;const{asyncStorage:E,bridge:x,device:C,document:N,register:I,on:P,off:_,emit:L}=e.Hippy;var A=Object.freeze({__proto__:null,addEventListener:P,removeEventListener:_,dispatchEvent:L,AsyncStorage:E,Bridge:x,Device:C,HippyRegister:I,UIManager:N});let R,z;const O=new Map;function T(e,t){z=e,R=t}function j(){if(!z)throw new Error("getRootViewId must execute after setRootContainer");return z}function F(e){if(!R)return null;const{current:t}=R,n=[t];for(;n.length;){const t=n.shift();if(!t)break;if(e(t))return t;t.child&&n.push(t.child),t.sibling&&n.push(t.sibling)}return null}function H(e,t){O.set(t,e)}function M(e){O.delete(e)}function D(e){return(null==e?void 0:e.stateNode)||null}function U(e){return O.get(e)||null}function W(t){!function(t,n){if(!e.requestIdleCallback)return setTimeout(()=>{t({didTimeout:!1,timeRemaining:()=>1/0})},1);e.requestIdleCallback(t,n)}(e=>{(e.timeRemaining()>0||e.didTimeout)&&function e(t){"number"==typeof t?M(t):t&&(M(t.nodeId),Array.isArray(t.childNodes)&&t.childNodes.forEach(t=>e(t)))}(t)},{timeout:50})}const B={onTouchStart:["onTouchStart","onTouchDown"],onPress:["onPress","onClick"]},V=new RegExp(/^\d+$/);let $=!1,q=!1;function Q(e){return e.replace(/\\u[\dA-F]{4}|\\x[\dA-F]{2}/gi,e=>String.fromCharCode(parseInt(e.replace(/\\u|\\x/g,""),16)))}const K=new RegExp("^on.+Capture$");const G=new RegExp("^(?=.+)[+-]?\\d*\\.?\\d*([Ee][+-]?\\d+)?$");function Y(e){return"[object Function]"===Object.prototype.toString.call(e)}function X(e){$=e}function J(){return q}function Z(e){if(e&&!/^(http|https):\/\//.test(e)&&e.indexOf("assets")>-1){0;return`${"hpfile://"}./${e}`}return e}function ee(e){return 5===e}class te{constructor(e){this.handlerContainer={},this.nextIdForHandler=0,this.eventName=e}getEventListeners(){return Object.keys(this.handlerContainer).filter(e=>this.handlerContainer[e]).map(e=>this.handlerContainer[e])}getHandlerSize(){return Object.keys(this.handlerContainer).length}addEventHandler(e,t){if(!e)throw new TypeError("Invalid arguments for addEventHandler");const n=this.nextIdForHandler;this.nextIdForHandler+=1;const r={id:n,eventHandler:e,context:t},i="eventHandler_"+n;return this.handlerContainer[i]=r,n}notifyEvent(e){Object.keys(this.handlerContainer).forEach(t=>{const n=this.handlerContainer[t];n&&n.eventHandler&&(n.context?n.eventHandler.call(n.context,e):n.eventHandler(e))})}removeEventHandler(e){if("number"!=typeof e)throw new TypeError("Invalid arguments for removeEventHandler");const t="eventHandler_"+e;this.handlerContainer[t]&&delete this.handlerContainer[t]}}class ne{constructor(e,t,n){this.type=e,this.bubbles=!0,this.timeStamp=Date.now(),this.currentTarget=t,this.target=n}stopPropagation(){this.bubbles=!1}preventDefault(){}}const re=new Map;function ie(e,t){let n=e;if(t.memoizedProps&&!t.memoizedProps[e]){const r=Object.keys(B);for(let i=0;i<r.length;i+=1){const o=r[i],a=B[o];if(t.memoizedProps[o]&&e===a[1]){n=o;break}}}return n}function oe(e,t){return!(!t.memoizedProps||"function"!=typeof t.memoizedProps[e])}function ae(e){if("string"!=typeof e)throw new TypeError("Invalid eventName for getHippyEventHub: "+e);return re.get(e)||null}const le={registerNativeEventHub:function(e){if("string"!=typeof e)throw new TypeError("Invalid eventName for registerNativeEventHub: "+e);let t=re.get(e);return t||(t=new te(e),re.set(e,t)),t},getHippyEventHub:ae,unregisterNativeEventHub:function(e){if("string"!=typeof e)throw new TypeError("Invalid eventName for unregisterNativeEventHub: "+e);re.has(e)&&re.delete(e)},receiveNativeEvent:function(e){if(!e||!Array.isArray(e)||e.length<2)throw new TypeError("Invalid params for receiveNativeEvent: "+JSON.stringify(e));const[t,n]=e;if("string"!=typeof t)throw new TypeError("Invalid arguments for nativeEvent eventName");const r=ae(t);r&&r.notifyEvent(n)},receiveNativeGesture:function(e){if(!e)return;const{id:t}=e,n=U(t);if(!n)return;let r=!0,{name:i}=e;i=ie(i,n);const o=i+"Capture",a=n;n.memoizedProps&&"function"!=typeof n.memoizedProps[o]&&(r=!1),r?function(e,t,n){const r=[];let i=n,o=e;for(;i;){o=ie(o,i);const e=o+"Capture";if(oe(e,i)&&r.unshift({eventName:e,listener:i.memoizedProps[e],isCapture:!0,currentTarget:D(i)}),oe(o,i)&&r.push({eventName:o,listener:i.memoizedProps[o],isCapture:!1,currentTarget:D(i)}),0===r.length)i=null;else for(i=i.return;i&&!ee(i.tag);)i=i.return}if(r.length>0){let e,i=!1;const o=D(n);for(;!i&&void 0!==(e=r.shift());)try{const{eventName:n,currentTarget:r,listener:a,isCapture:l}=e,s=new ne(n,r,o);Object.assign(s,t),l?(a(s),s.bubbles||(i=!0)):(i=a(s),"boolean"!=typeof i&&(i=!J()),s.bubbles||(i=!0))}catch(e){console.reportUncaughtException(e)}}}(i,e,a):function(e,t,n){let r=!1,i=n,o=e;const a=D(n);do{if(o=ie(o,i),oe(o,i))try{const e=D(i),n=new ne(o,e,a);Object.assign(n,t),r=i.memoizedProps[o](n),"boolean"!=typeof r&&(r=!J()),n.bubbles||(r=!0)}catch(e){console.reportUncaughtException(e)}if(!1===r)for(i=i.return;i&&!ee(i.tag);)i=i.return}while(!r&&i)}(i,e,a)},receiveUIComponentEvent:function(e){if(!e||!Array.isArray(e)||e.length<2)return;const[t,n,r]=e;if("number"!=typeof t||"string"!=typeof n)return;const i=U(t);i&&oe(n,i)&&i.memoizedProps[n](r)}};e.__GLOBAL__&&(e.__GLOBAL__.jsModuleList.EventDispatcher=le);class se{constructor(e,t){this.callback=e,this.bindListener=t}remove(){"number"==typeof this.callback&&this.bindListener&&(this.bindListener.removeCallback(this.callback),this.bindListener=void 0)}}class ue{constructor(e){this.eventName=e,this.listenerIdList=[]}unregister(){const e=le.getHippyEventHub(this.eventName);if(!e)throw new ReferenceError("No listeners for "+this.eventName);const t=this.listenerIdList.length;for(let n=0;n<t;n+=1)e.removeEventHandler(this.listenerIdList[n]);this.listenerIdList=[],0===e.getHandlerSize()&&le.unregisterNativeEventHub(this.eventName)}getSize(){return this.listenerIdList.length}addCallback(e,t){if("function"!=typeof e)throw new TypeError("Invalid addCallback function arguments");const n=le.registerNativeEventHub(this.eventName);if(!n)throw new ReferenceError("No listeners for "+this.eventName);const r=n.addEventHandler(e,t);if("number"!=typeof r)throw new Error("Fail to addEventHandler in addCallback function");return this.listenerIdList.push(r),r}removeCallback(e){if("number"!=typeof e)throw new TypeError("Invalid arguments for removeCallback");const t=le.getHippyEventHub(this.eventName);if(!t)throw new ReferenceError("No listeners for "+this.eventName);t.removeEventHandler(e);const n=this.listenerIdList.length;for(let t=0;t<n;t+=1)if(e===this.listenerIdList[t]){this.listenerIdList.splice(t,1);break}}}function ce(e){if("string"!=typeof e)throw new TypeError("Invalid arguments for getNameForEvent");return"eventEmitter_"+e}class de{constructor(e){this.hippyEventListeners=e&&"object"==typeof e?e:{}}sharedListeners(){return this.hippyEventListeners}addListener(e,t,n){if("string"!=typeof e||"function"!=typeof t)throw new TypeError("Invalid arguments for addListener");let r=this.hippyEventListeners[ce(e)];r||(r=new ue(e),this.hippyEventListeners[ce(e)]=r);const i=r.addCallback(t,n);if("number"!=typeof i)throw new Error("Fail to addCallback in addListener");return new se(i,r)}removeAllListeners(e){if("string"!=typeof e)throw new TypeError("Invalid arguments for removeAllListeners");const t=this.hippyEventListeners[ce(e)];t&&(t.unregister(),delete this.hippyEventListeners[ce(e)])}emit(e,t){if("string"!=typeof e)return!1;const n=le.getHippyEventHub(e);return!!n&&(n.notifyEvent(t),!0)}listenerSize(e){if("string"!=typeof e)throw new TypeError("Invalid arguments for listenerSize");const t=this.hippyEventListeners[ce(e)];return t?t.getSize():0}}de.emit=de.prototype.emit;var fe=Object.freeze({__proto__:null,getString:function(){return x.callNativeWithPromise("ClipboardModule","getString")},setString:function(e){x.callNative("ClipboardModule","setString",e)}});var pe=Object.freeze({__proto__:null,getCookies:function(e){return x.callNativeWithPromise("network","getCookie",e)},setCookie:function(e,t,n){let r="";"string"==typeof n&&(r=n),n instanceof Date&&(r=n.toUTCString()),x.callNative("network","setCookie",e,t,r)}});function he(e){return x.callNativeWithPromise("ImageLoaderModule","getSize",e)}function me(e){x.callNative("ImageLoaderModule","prefetch",e)}var ye=Object.freeze({__proto__:null,getSize:he,prefetch:me});const ge=new Map;let be;class ve{constructor(e,t){this.eventName=e,this.listener=t}remove(){this.eventName&&this.listener&&(we(this.eventName,this.listener),this.listener=void 0)}}function we(e,t){if(t instanceof ve)return void t.remove();let n=e;"change"===e&&(n="networkStatusDidChange");be.listenerSize(n)<=1&&x.callNative("NetInfo","removeListener",n);const r=ge.get(t);r&&(r.remove(),ge.delete(t))}var ke=Object.freeze({__proto__:null,addEventListener:function(e,t){be=new de;let n=e;n&&"change"===n&&(n="networkStatusDidChange"),be.listenerSize(n)<1&&x.callNative("NetInfo","addListener",n);const r=be.addListener(n,e=>{t(e)});return ge.set(t,r),new ve(n,t)},removeEventListener:we,fetch:function(){return x.callNativeWithPromise("NetInfo","getCurrentConnectivity").then(e=>e.network_info)}});function Se(e){return"loop"===e?-1:e}const Ee=new de;function xe(e,t){return"color"===e&&["number","string"].indexOf(typeof t)>=0?Ut(t):t}class Ce{constructor(e){var t;let n=0;if((null===(t=e.startValue)||void 0===t?void 0:t.constructor)&&"Animation"===e.startValue.constructor.name)n={animationId:e.startValue.animationId};else{const{startValue:t}=e;n=xe(e.valueType,t)}const r=xe(e.valueType,e.toValue);this.mode=e.mode||"timing",this.delay=e.delay||0,this.startValue=n||0,this.toValue=r||0,this.valueType=e.valueType||void 0,this.duration=e.duration||0,this.direction=e.direction||"center",this.timingFunction=e.timingFunction||"linear",this.repeatCount=Se(e.repeatCount||0),this.inputRange=e.inputRange||[],this.outputRange=e.outputRange||[],this.animationId=It.callNativeWithCallbackId("AnimationModule","createAnimation",!0,this.mode,Object.assign({delay:this.delay,startValue:this.startValue,toValue:this.toValue,duration:this.duration,direction:this.direction,timingFunction:this.timingFunction,repeatCount:this.repeatCount,inputRange:this.inputRange,outputRange:this.outputRange},this.valueType?{valueType:this.valueType}:{})),this.destroy=this.destroy.bind(this),this.onRNfqbAnimationStart=this.onAnimationStart.bind(this),this.onRNfqbAnimationEnd=this.onAnimationEnd.bind(this),this.onRNfqbAnimationCancel=this.onAnimationCancel.bind(this),this.onRNfqbAnimationRepeat=this.onAnimationRepeat.bind(this),this.onHippyAnimationStart=this.onAnimationStart.bind(this),this.onHippyAnimationEnd=this.onAnimationEnd.bind(this),this.onHippyAnimationCancel=this.onAnimationCancel.bind(this),this.onHippyAnimationRepeat=this.onAnimationRepeat.bind(this)}removeEventListener(){this.animationStartListener&&this.animationStartListener.remove(),this.animationEndListener&&this.animationEndListener.remove(),this.animationCancelListener&&this.animationCancelListener.remove(),this.animationRepeatListener&&this.animationRepeatListener.remove()}start(){this.removeEventListener();let e="onAnimation";e="onHippyAnimation","function"==typeof this.onAnimationStartCallback&&(this.animationStartListener=Ee.addListener(e+"Start",e=>{e===this.animationId&&(this.animationStartListener.remove(),"function"==typeof this.onAnimationStartCallback&&this.onAnimationStartCallback())})),"function"==typeof this.onAnimationEndCallback&&(this.animationEndListener=Ee.addListener(e+"End",e=>{e===this.animationId&&(this.animationEndListener.remove(),"function"==typeof this.onAnimationEndCallback&&this.onAnimationEndCallback())})),"function"==typeof this.onAnimationCancelCallback&&(this.animationCancelListener=Ee.addListener(e+"Cancel",e=>{e===this.animationId&&(this.animationCancelListener.remove(),"function"==typeof this.onAnimationCancelCallback&&this.onAnimationCancelCallback())})),"function"==typeof this.onAnimationRepeatCallback&&(this.animationRepeatListener=Ee.addListener(e+"Repeat",e=>{e===this.animationId&&"function"==typeof this.onAnimationRepeatCallback&&this.onAnimationRepeatCallback()})),It.callNative("AnimationModule","startAnimation",this.animationId)}destory(){this.destroy()}destroy(){this.removeEventListener(),It.callNative("AnimationModule","destroyAnimation",this.animationId)}pause(){It.callNative("AnimationModule","pauseAnimation",this.animationId)}resume(){It.callNative("AnimationModule","resumeAnimation",this.animationId)}updateAnimation(e){if("object"!=typeof e)throw new TypeError("Invalid arguments");if("string"==typeof e.mode&&e.mode!==this.mode)throw new TypeError("Update animation mode not supported");Object.keys(e).forEach(t=>{const n=e[t];if("startValue"===t){let t=0;if(e.startValue instanceof Ce)t={animationId:e.startValue.animationId};else{const{startValue:n}=e;t=xe(this.valueType,n)}this.startValue=t||0}else"repeatCount"===t?this.repeatCount=Se(e.repeatCount||0):Object.defineProperty(this,t,{value:n})}),It.callNative("AnimationModule","updateAnimation",this.animationId,Object.assign({delay:this.delay,startValue:this.startValue,toValue:xe(this.valueType,this.toValue),duration:this.duration,direction:this.direction,timingFunction:this.timingFunction,repeatCount:this.repeatCount,inputRange:this.inputRange,outputRange:this.outputRange},this.valueType?{valueType:this.valueType}:{}))}onAnimationStart(e){this.onAnimationStartCallback=e}onAnimationEnd(e){this.onAnimationEndCallback=e}onAnimationCancel(e){this.onAnimationCancelCallback=e}onAnimationRepeat(e){this.onAnimationRepeatCallback=e}}const Ne=new de;class Ie{constructor(e){this.animationList=[],e.children.forEach(e=>{this.animationList.push({animationId:e.animation.animationId,follow:e.follow||!1})}),this.animationId=It.callNativeWithCallbackId("AnimationModule","createAnimationSet",!0,{repeatCount:Se(e.repeatCount||0),children:this.animationList,virtual:e.virtual}),this.onRNfqbAnimationStart=this.onAnimationStart.bind(this),this.onRNfqbAnimationEnd=this.onAnimationEnd.bind(this),this.onRNfqbAnimationCancel=this.onAnimationCancel.bind(this),this.onRNfqbAnimationRepeat=this.onAnimationRepeat.bind(this),this.onHippyAnimationStart=this.onAnimationStart.bind(this),this.onHippyAnimationEnd=this.onAnimationEnd.bind(this),this.onHippyAnimationCancel=this.onAnimationCancel.bind(this),this.onHippyAnimationRepeat=this.onAnimationRepeat.bind(this)}removeEventListener(){this.animationStartListener&&this.animationStartListener.remove(),this.animationEndListener&&this.animationEndListener.remove(),this.animationCancelListener&&this.animationCancelListener.remove(),this.animationRepeatListener&&this.animationRepeatListener.remove()}start(){this.removeEventListener();let e="onAnimation";e="onHippyAnimation","function"==typeof this.onAnimationStartCallback&&(this.animationStartListener=Ne.addListener(e+"Start",e=>{e===this.animationId&&(this.animationStartListener.remove(),"function"==typeof this.onAnimationStartCallback&&this.onAnimationStartCallback())})),"function"==typeof this.onAnimationEndCallback&&(this.animationEndListener=Ne.addListener(e+"End",e=>{e===this.animationId&&(this.animationEndListener.remove(),"function"==typeof this.onAnimationEndCallback&&this.onAnimationEndCallback())})),"function"==typeof this.onAnimationCancelCallback&&(this.animationCancelListener=Ne.addListener(e+"Cancel",e=>{e===this.animationId&&(this.animationCancelListener.remove(),"function"==typeof this.onAnimationCancelCallback&&this.onAnimationCancelCallback())})),"function"==typeof this.onAnimationRepeatCallback&&(this.animationRepeatListener=Ne.addListener(e+"Repeat",e=>{e===this.animationId&&"function"==typeof this.onAnimationRepeatCallback&&this.onAnimationRepeatCallback()})),It.callNative("AnimationModule","startAnimation",this.animationId)}destory(){this.destroy()}destroy(){this.removeEventListener(),this.animationList.forEach(e=>Number.isInteger(e.animationId)&&It.callNative("AnimationModule","destroyAnimation",e.animationId)),It.callNative("AnimationModule","destroyAnimation",this.animationId)}pause(){It.callNative("AnimationModule","pauseAnimation",this.animationId)}resume(){It.callNative("AnimationModule","resumeAnimation",this.animationId)}onAnimationStart(e){this.onAnimationStartCallback=e}onAnimationEnd(e){this.onAnimationEndCallback=e}onAnimationCancel(e){this.onAnimationCancelCallback=e}onAnimationRepeat(e){this.onAnimationRepeatCallback=e}}const Pe={createNode:Symbol("createNode"),updateNode:Symbol("updateNode"),deleteNode:Symbol("deleteNode")};let _e=!0,Le=[];function Ae(e){(function(e){const t=[];for(let n=0;n<e.length;n+=1){const r=e[n],{type:i,nodes:o}=r,a=t[t.length-1];a&&a.type===i?a.nodes=a.nodes.concat(o):t.push({type:i,nodes:o})}return t})(Le).forEach(t=>{switch(t.type){case Pe.createNode:t.nodes,Xe(e,t.nodes);break;case Pe.updateNode:t.nodes,"ios"===C.platform.OS?t.nodes.forEach(t=>Je(e,[t])):Je(e,t.nodes);break;case Pe.deleteNode:t.nodes,"ios"===C.platform.OS?t.nodes.forEach(t=>Ze(e,[t])):Ze(e,t.nodes)}})}function Re(e=!1){if(!_e)return;if(_e=!1,0===Le.length)return void(_e=!0);const t=j();tt(),e?(Ae(t),nt(),Le=[],_e=!0):Promise.resolve().then(()=>{Ae(t),nt(),Le=[],_e=!0})}function ze(e){const t=e.attributes,{children:n}=t;return S(t,l)}function Oe(e,t){var n;if(!t.nativeName)return null;if(t.meta.skipAddToDom)return null;if(!t.meta.component)throw new Error("Specific tag is not supported yet: "+t.tagName);return{id:t.nodeId,pId:(null===(n=t.parentNode)||void 0===n?void 0:n.nodeId)||e,index:t.index,name:t.nativeName,props:w(w({},ze(t)),{},{style:t.style})}}function Te(e,t,n,r){const i=[];let o=n;return void 0===o&&t&&t.parentNode&&(o=t.parentNode.childNodes.indexOf(t)),t.traverseChildren(t=>{const n=Oe(e,t);n&&i.push(n),"function"==typeof r&&r(t)},o),i}function je(e){return!!R&&e instanceof R.containerInfo.constructor}function Fe(e,t,n=-1){if(!e||!t)return;if(t.meta.skipAddToDom)return;const r=j();if(je(e)&&!e.isMounted){const e=Te(r,t,n,e=>{e.isMounted||(e.isMounted=!0)});Le.push({type:Pe.createNode,nodes:e})}else if(e.isMounted&&!t.isMounted){const e=Te(r,t,n,e=>{e.isMounted||(e.isMounted=!0)});Le.push({type:Pe.createNode,nodes:e})}}function He(e,t,n){if(!t||t.meta.skipAddToDom)return;t.isMounted=!1,t.index=n;const r=j(),i=[{id:t.nodeId,pId:t.parentNode?t.parentNode.nodeId:r,index:t.index}];Le.push({type:Pe.deleteNode,nodes:i})}function Me(e){if(!e.isMounted)return;const t=Oe(j(),e);t&&Le.push({type:Pe.updateNode,nodes:[t]})}let De=0;class Ue{constructor(){this.meta={component:{}},this.index=0,this.childNodes=[],this.parentNode=null,this.mounted=!1,this.nodeId=(De+=1,De%10==0&&(De+=1),De)}toString(){return this.constructor.name}get isMounted(){return this.mounted}set isMounted(e){this.mounted=e}insertBefore(e,t){if(!e)throw new Error("Can't insert child.");if(e.meta.skipAddToDom)return;if(!t)return this.appendChild(e);if(t.parentNode!==this)throw new Error("Can't insert child, because the reference node has a different parent.");if(e.parentNode&&e.parentNode!==this)throw new Error("Can't insert child, because it already has a different parent.");const n=this.childNodes.indexOf(t);return e.parentNode=this,this.childNodes.splice(n,0,e),Fe(this,e,n)}moveChild(e,t){if(!e)throw new Error("Can't move child.");if(e.meta.skipAddToDom)return;if(!t)return this.appendChild(e);if(t.parentNode!==this)throw new Error("Can't move child, because the reference node has a different parent.");if(e.parentNode&&e.parentNode!==this)throw new Error("Can't move child, because it already has a different parent.");const n=this.childNodes.indexOf(e);if(this.childNodes.indexOf(t)===n)return e;this.childNodes.splice(n,1),He(0,e,n);const r=this.childNodes.indexOf(t);return this.childNodes.splice(r,0,e),Fe(this,e,r)}appendChild(e){if(!e)throw new Error("Can't append child.");if(!e.meta.skipAddToDom){if(e.parentNode&&e.parentNode!==this)throw new Error("Can't append child, because it already has a different parent.");e.parentNode=this,this.childNodes.push(e),Fe(this,e,this.childNodes.length-1)}}removeChild(e){if(!e)throw new Error("Can't remove child.");if(e.meta.skipAddToDom)return;if(!e.parentNode)throw new Error("Can't remove child, because it has no parent.");if(e.parentNode!==this)throw new Error("Can't remove child, because it has a different parent.");const t=this.childNodes.indexOf(e);this.childNodes.splice(t,1),He(0,e,t)}findChild(e){if(e(this))return this;if(this.childNodes.length)for(let t=0;t<this.childNodes.length;t+=1){const n=this.childNodes[t],r=this.findChild.call(n,e);if(r)return r}return null}traverseChildren(e,t=0){this.index=this.parentNode?t:0,e(this),this.childNodes.length&&this.childNodes.forEach((t,n)=>{this.traverseChildren.call(t,e,n)})}}const We={textDecoration:"textDecorationLine",boxShadowOffset:"shadowOffset",boxShadowOffsetX:"shadowOffsetX",boxShadowOffsetY:"shadowOffsetY",boxShadowOpacity:"shadowOpacity",boxShadowRadius:"shadowRadius",boxShadowSpread:"shadowSpread",boxShadowColor:"shadowColor"},Be={totop:"0",totopright:"totopright",toright:"90",tobottomright:"tobottomright",tobottom:"180",tobottomleft:"tobottomleft",toleft:"270",totopleft:"totopleft"},Ve="turn",$e="rad",qe="deg";function Qe(e){const t=(e||"").replace(/\s*/g,"").toLowerCase(),n=/^([+-]?\d+\.?\d*)+(deg|turn|rad)|(to\w+)$/g.exec(t);if(!Array.isArray(n))return;let r="180";const[i,o,a]=n;return o&&a?r=function(e,t=qe){const n=parseFloat(e);let r=e||"";const[,i]=e.split(".");switch(i&&i.length>2&&(r=n.toFixed(2)),t){case Ve:r=""+(360*n).toFixed(2);break;case $e:r=""+(180/Math.PI*n).toFixed(2)}return r}(o,a):i&&void 0!==Be[i]&&(r=Be[i]),r}function Ke(e){const t=(e||"").replace(/\s+/g," ").trim(),[n,r]=t.split(/\s+(?![^(]*?\))/),i=/^([+-]?\d+\.?\d*)%$/g;return!n||i.exec(n)||r?n&&i.exec(r)?{ratio:parseFloat(r.split("%")[0])/100,color:Ut(n)}:void 0:{color:Ut(n)}}function Ge(e){var t;return t=e,K.test(t)&&(e=e.replace("Capture","")),B[e]?B[e][1]:e}class Ye extends Ue{constructor(e){super(),this.id="",this.style={},this.attributes={},this.tagName=e}get nativeName(){return this.meta.component.name}toString(){return`${this.tagName}:(${this.nativeName})`}hasAttribute(e){return!!this.attributes[e]}getAttribute(e){return this.attributes[e]}setStyleAttribute(e){this.style={};let t=e;if(!Array.isArray(t)&&Object.hasOwnProperty.call(t,0)){const e=[],n={};Object.keys(t).forEach(r=>{var i;i=r,V.test(i)?e.push(t[r]):n[r]=t[r]}),t=[...e,n]}Array.isArray(t)||(t=[t]);let n={};t.forEach(e=>{Array.isArray(e)?e.forEach(e=>{n=w(w({},n),e)}):"object"==typeof e&&e&&(n=w(w({},n),e))}),Object.keys(n).forEach(e=>{const t=n[e];if(Object.prototype.hasOwnProperty.call(We,e)&&(e=We[e]),"transform"===e){const e={};if(!Array.isArray(t))throw new TypeError("transform only support array args");t.forEach(t=>{Object.keys(t).forEach(n=>{const r=t[n];r instanceof Ce||r instanceof Ie?e[n]={animationId:r.animationId}:null===r?e[n]&&delete e[n]:void 0!==r&&(e[n]=r)})});const n=Object.keys(e);n.length&&(Array.isArray(this.style.transform)||(this.style.transform=[]),n.forEach(t=>this.style.transform.push({[t]:e[t]})))}else if(null===t&&void 0!==this.style[e])this.style[e]=void 0;else if(t instanceof Ce||t instanceof Ie)this.style[e]={animationId:t.animationId};else if(e.toLowerCase().indexOf("colors")>-1)this.style[e]=Wt(t);else if(e.toLowerCase().indexOf("color")>-1)this.style[e]=Ut(t);else if("backgroundImage"===e&&t)this.style=function(e,t,n){if(0===t.indexOf("linear-gradient")){const e=t.substring(t.indexOf("(")+1,t.lastIndexOf(")")).split(/,(?![^(]*?\))/),r=[];n.linearGradient=n.linearGradient||{},e.forEach((e,t)=>{if(0===t){const t=Qe(e);if(t)n.linearGradient.angle=t;else{n.linearGradient.angle="180";const t=Ke(e);t&&r.push(t)}}else{const t=Ke(e);t&&r.push(t)}}),n.linearGradient.colorStopList=r}else n[e]=Z(t);return n}(e,t,this.style);else if("textShadowOffset"===e){const{x:n=0,width:r=0,y:i=0,height:o=0}=t||{};this.style[e]={width:n||r,height:i||o}}else["textShadowOffsetX","textShadowOffsetY"].indexOf(e)>=0?this.style=function(e,t,n){return n.textShadowOffset=n.textShadowOffset||{},Object.assign(n.textShadowOffset,{[{textShadowOffsetX:"width",textShadowOffsetY:"height"}[e]]:t||0}),n}(e,t,this.style):this.style[e]=t})}setAttribute(e,t){try{if("boolean"==typeof this.attributes[e]&&""===t&&(t=!0),void 0===e)return void Me(this);let n=!1;if([{match:()=>["id"].indexOf(e)>=0,action:()=>(t===this.id||(this.id=t,function(e){if(!e.isMounted)return;const t=Te(j(),e);Le.push({type:Pe.updateNode,nodes:t})}(this)),!0)},{match:()=>["value","defaultValue","placeholder"].indexOf(e)>=0,action:()=>(this.attributes[e]=Q(t),!1)},{match:()=>["text"].indexOf(e)>=0,action:()=>(this.attributes[e]=t,!1)},{match:()=>["numberOfRows"].indexOf(e)>=0,action:()=>(this.attributes[e]=t,"ios"!==Pt.platform.OS)},{match:()=>["style"].indexOf(e)>=0,action:()=>"object"!=typeof t||null==t||(this.setStyleAttribute(t),!1)},{match:()=>!0,action:()=>{if("function"==typeof t){const t=Ge(e);this.attributes[t]=!0,this.attributes["__bind__"+t]=!0}else{this.attributes[e]=t;const n=Ge(e);!0===this.attributes["__bind__"+n]&&"function"!=typeof t&&(this.attributes[n]=!1,this.attributes["__bind__"+n]=!1)}return!1}}].some(e=>!!e.match()&&(n=e.action(),!0)),n)return;let r=!1;Object.keys(this.style).some(e=>{const t=this.style[e];if(t&&Array.isArray(t)&&"transform"===e)for(let e=0;e<t.length;e+=1){const n=t[e];for(const e in n){const t=n[e];if("object"==typeof t&&null!==t&&Number.isInteger(t.animationId))return r=!0,t}}return!("object"!=typeof t||null===t||!Number.isInteger(t.animationId))&&(r=!0,t)}),r?this.attributes.useAnimation=!0:"boolean"==typeof this.attributes.useAnimation&&(this.attributes.useAnimation=void 0),Me(this)}catch(e){}}removeAttribute(e){delete this.attributes[e]}setStyle(e,t,n=!1){if(null===t)return void delete this.style[e];let r=t,i=e;Object.prototype.hasOwnProperty.call(We,e)&&(i=We[e]),"string"==typeof r&&(r=t.trim(),r=i.toLowerCase().indexOf("colors")>-1?Wt(r):i.toLowerCase().indexOf("color")>-1?Ut(r):function(e){if("number"==typeof e)return e;if("string"==typeof e&&G.test(e))try{return parseFloat(e)}catch(t){return e}return e}(r)),null!=r&&this.style[i]!==r&&(this.style[i]=r,n||Me(this))}setNativeProps(e){if(e){const{style:t}=e;if(t){const e=t;Object.keys(e).forEach(t=>{this.setStyle(t,e[t],!0)}),Me(this),Re(!0)}}}setText(e){if("string"!=typeof e)try{e=e.toString()}catch(e){throw new Error("Only string type is acceptable for setText")}return(e=e.trim())||this.getAttribute("text")?(e=(e=Q(e)).replace(/&nbsp;/g," ").replace(/\xc2/g," "),"textarea"===this.tagName?this.setAttribute("value",e):this.setAttribute("text",e)):null}}const{createNode:Xe,updateNode:Je,deleteNode:Ze,flushBatch:et,startBatch:tt,endBatch:nt,sendRenderError:rt}=N,it=function(e){return F(t=>t.stateNode&&t.stateNode.nodeId===e)};function ot(e){if(e instanceof Ye)return e;const t=e._reactInternalFiber||e._reactInternals;if(null==t?void 0:t.child){let e=t.child;for(;e&&!(e.stateNode instanceof Ye);)e=e.child;return e&&e.stateNode?e.stateNode:null}return null}function at(e){let t=e;if("string"==typeof e){const n=F(t=>!!(t.return&&t.return.ref&&t.return.ref._stringRef)&&t.return.ref._stringRef===e);if(!n||!n.stateNode)return 0;t=n.stateNode}if(!t.nodeId){const e=ot(t);return e?e.nodeId:0}return t.nodeId}function lt(e,t,...n){let{nativeName:r,nodeId:i}=e;if(!i||!r){const t=ot(e);t&&({nodeId:i,nativeName:r}=t)}if(!r)throw new Error("callUIFunction is calling a unnamed component");if(!i)throw new Error("callUIFunction is calling a component have no nodeId");let[o=[],a]=n;Y(o)&&(a=o,o=[]);null!==j()&&("ios"===C.platform.OS?(Y(a)&&Array.isArray(o)&&o.push(a),x.callNative("UIManagerModule","callUIFunction",[r,i,t,o])):"android"===C.platform.OS&&(Y(a)?x.callNative("UIManagerModule","callUIFunction",[i,t,o],a):x.callNative("UIManagerModule","callUIFunction",[i,t,o])))}function st(e,t,n){const r=at(t);return new Promise((t,i)=>r?x.callNative("UIManagerModule",e,r,e=>(n&&Y(n)&&n(e),"this view is null"===e?i(new Error("Android cannot get the node")):t(e))):(n&&Y(n)&&n("this view is null"),i(new Error(e+" cannot get nodeId"))))}var ut=Object.freeze({__proto__:null,createNode:Xe,updateNode:Je,deleteNode:Ze,flushBatch:et,startBatch:tt,endBatch:nt,sendRenderError:rt,getNodeById:it,getNodeIdByRef:at,getElementFromFiberRef:ot,callUIFunction:lt,measureInWindow:function(e,t){return st("measureInWindow",e,t)},measureInAppWindow:function(e,t){return"android"===C.platform.OS?st("measureInWindow",e,t):st("measureInAppWindow",e,t)}});const ct=new de,dt=new Set,ft={exitApp(){x.callNative("DeviceEventModule","invokeDefaultBackPressHandler")},addListener:e=>(x.callNative("DeviceEventModule","setListenBackPress",!0),dt.add(e),{remove(){ft.removeListener(e)}}),removeListener(e){dt.delete(e),0===dt.size&&x.callNative("DeviceEventModule","setListenBackPress",!1)},initEventListener(){ct.addListener("hardwareBackPress",()=>{let e=!0;[...dt].reverse().every(t=>"function"!=typeof t||!t()||(e=!1,!1)),e&&ft.exitApp()})}},pt=(ft.initEventListener(),ft);var ht=Array.isArray,mt=Object.keys,yt=Object.prototype.hasOwnProperty,gt=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){var r,i,o,a=ht(t),l=ht(n);if(a&&l){if((i=t.length)!=n.length)return!1;for(r=i;0!=r--;)if(!e(t[r],n[r]))return!1;return!0}if(a!=l)return!1;var s=t instanceof Date,u=n instanceof Date;if(s!=u)return!1;if(s&&u)return t.getTime()==n.getTime();var c=t instanceof RegExp,d=n instanceof RegExp;if(c!=d)return!1;if(c&&d)return t.toString()==n.toString();var f=mt(t);if((i=f.length)!==mt(n).length)return!1;for(r=i;0!=r--;)if(!yt.call(n,f[r]))return!1;for(r=i;0!=r--;)if(!e(t[o=f[r]],n[o]))return!1;return!0}return t!=t&&n!=n};const bt=setTimeout,vt=clearTimeout;var wt=Object.freeze({__proto__:null,commitMutationEffectsBegin:function(){},commitMutationEffectsComplete:function(){Re(!0)},getCurrentEventPriority:function(){return 16},scheduleTimeout:bt,cancelTimeout:vt,noTimeout:-1,afterActiveInstanceBlur:function(){},appendChild:function(e,t){e.childNodes.indexOf(t)>=0&&e.removeChild(t),e.appendChild(t)},appendChildToContainer:function(e,t){e.appendChild(t)},appendInitialChild:function(e,t){e.appendChild(t)},beforeActiveInstanceBlur:function(){},commitMount:function(){},commitTextUpdate:function(){},commitUpdate:function(e,t,n,r,i,o){H(o,e.nodeId);const a=Object.keys(t);0!==a.length&&a.forEach(n=>e.setAttribute(n,t[n]))},clearContainer:function(){},createContainerChildSet:function(){},createInstance:function(e,t,n,r,i){const o=n.createElement(e);return Object.keys(t).forEach(e=>{switch(e){case"children":break;case"nativeName":o.meta.component.name=t.nativeName;break;default:o.setAttribute(e,t[e])}}),[5,7].indexOf(i.tag)<0&&(o.meta.skipAddToDom=!0),H(i,o.nodeId),o},createTextInstance:function(e,t,n,r){const i=t.createElement("p");return i.setAttribute("text",Q(e)),i.meta={component:{name:"Text"}},H(r,i.nodeId),i},detachDeletedInstance:function(){},finalizeContainerChildren:function(){},finalizeInitialChildren:function(){return!0},getChildHostContext:function(){return{}},getPublicInstance:function(e){return e},getInstanceFromNode:function(){throw new Error("Not yet implemented.")},getFundamentalComponentInstance:function(){throw new Error("Not yet implemented.")},getRootHostContext:function(){return{}},hideInstance:function(e){const t={style:{display:"none"}};Object.keys(t).forEach(n=>e.setAttribute(n,t[n]))},hideTextInstance:function(){throw new Error("Not yet implemented.")},insertBefore:function(e,t,n){e.childNodes.indexOf(t)>=0?e.moveChild(t,n):e.insertBefore(t,n)},isOpaqueHydratingObject:function(){throw new Error("Not yet implemented")},makeClientId:function(){throw new Error("Not yet implemented")},makeClientIdInDEV:function(){throw new Error("Not yet implemented")},makeOpaqueHydratingObject:function(){throw new Error("Not yet implemented.")},mountFundamentalComponent:function(){throw new Error("Not yet implemented.")},prepareForCommit:function(){return null},preparePortalMount:function(){},prepareUpdate:function(e,t,n,r){const i={};let o=!1;Object.keys(r).forEach(e=>{const t=n[e],a=r[e];switch(e){case"children":t===a||"number"!=typeof a&&"string"!=typeof a||(i[e]=a);break;default:"function"!=typeof t||"function"!=typeof a||gt(t,a)?gt(t,a)||(i[e]=a):o=!0}});const a=0===Object.keys(i).length;return a&&o?{}:a?null:i},replaceContainerChildren:function(){},removeChild:function(e,t){e.removeChild(t),W(t)},removeChildFromContainer:function(e,t){e.removeChild(t),W(t)},resetAfterCommit:function(){},resetTextContent:function(){},unmountFundamentalComponent:function(){throw new Error("Not yet implemented.")},updateFundamentalComponent:function(){throw new Error("Not yet implemented.")},unhideTextInstance:function(){throw new Error("Not yet implemented.")},unhideInstance:function(e,t){const n=w(w({},t),{},{style:w(w({},t.style),{},{display:"flex"})});Object.keys(n).forEach(t=>e.setAttribute(t,n[t]))},shouldDeprioritizeSubtree:function(){return!0},shouldUpdateFundamentalComponent:function(){throw new Error("Not yet implemented.")},shouldSetTextContent:function(e,t){if(t&&"Text"===t.nativeName||-1!==["p","span"].indexOf(e)){const{children:e}=t;return"string"==typeof e||"number"==typeof e}return!1}});const kt=i()(w(w({},wt),{},{clearTimeout:clearTimeout,setTimeout:setTimeout,isPrimaryRenderer:!0,noTimeout:-1,supportsMutation:!0,supportsHydration:!1,supportsPersistence:!1,now:Date.now,scheduleDeferredCallback:()=>{},cancelDeferredCallback:()=>{}})),{flushSync:St}=kt,{addEventListener:Et,removeEventListener:xt,dispatchEvent:Ct,AsyncStorage:Nt,Bridge:It,Device:Pt,HippyRegister:_t}=A;var Lt=Object.freeze({__proto__:null,addEventListener:Et,removeEventListener:xt,dispatchEvent:Ct,AsyncStorage:Nt,BackAndroid:pt,Bridge:It,Clipboard:fe,Cookie:pe,Device:Pt,HippyRegister:_t,ImageLoader:ye,NetworkInfo:ke,UIManager:ut,flushSync:St});function At(...e){return`\\(\\s*(${e.join(")\\s*,\\s*(")})\\s*\\)`}const Rt={transparent:0,aliceblue:4042850303,antiquewhite:4209760255,aqua:16777215,aquamarine:2147472639,azure:4043309055,beige:4126530815,bisque:4293182719,black:255,blanchedalmond:4293643775,blue:65535,blueviolet:2318131967,brown:2771004159,burlywood:3736635391,burntsienna:3934150143,cadetblue:1604231423,chartreuse:2147418367,chocolate:3530104575,coral:4286533887,cornflowerblue:1687547391,cornsilk:4294499583,crimson:3692313855,cyan:16777215,darkblue:35839,darkcyan:9145343,darkgoldenrod:3095792639,darkgray:2846468607,darkgreen:6553855,darkgrey:2846468607,darkkhaki:3182914559,darkmagenta:2332068863,darkolivegreen:1433087999,darkorange:4287365375,darkorchid:2570243327,darkred:2332033279,darksalmon:3918953215,darkseagreen:2411499519,darkslateblue:1211993087,darkslategray:793726975,darkslategrey:793726975,darkturquoise:13554175,darkviolet:2483082239,deeppink:4279538687,deepskyblue:12582911,dimgray:1768516095,dimgrey:1768516095,dodgerblue:512819199,firebrick:2988581631,floralwhite:4294635775,forestgreen:579543807,fuchsia:4278255615,gainsboro:3705462015,ghostwhite:4177068031,gold:4292280575,goldenrod:3668254975,gray:2155905279,green:8388863,greenyellow:2919182335,grey:2155905279,honeydew:4043305215,hotpink:4285117695,indianred:3445382399,indigo:1258324735,ivory:4294963455,khaki:4041641215,lavender:3873897215,lavenderblush:4293981695,lawngreen:2096890111,lemonchiffon:4294626815,lightblue:2916673279,lightcoral:4034953471,lightcyan:3774873599,lightgoldenrodyellow:4210742015,lightgray:3553874943,lightgreen:2431553791,lightgrey:3553874943,lightpink:4290167295,lightsalmon:4288707327,lightseagreen:548580095,lightskyblue:2278488831,lightslategray:2005441023,lightslategrey:2005441023,lightsteelblue:2965692159,lightyellow:4294959359,lime:16711935,limegreen:852308735,linen:4210091775,magenta:4278255615,maroon:2147483903,mediumaquamarine:1724754687,mediumblue:52735,mediumorchid:3126187007,mediumpurple:2473647103,mediumseagreen:1018393087,mediumslateblue:2070474495,mediumspringgreen:16423679,mediumturquoise:1221709055,mediumvioletred:3340076543,midnightblue:421097727,mintcream:4127193855,mistyrose:4293190143,moccasin:4293178879,navajowhite:4292783615,navy:33023,oldlace:4260751103,olive:2155872511,olivedrab:1804477439,orange:4289003775,orangered:4282712319,orchid:3664828159,palegoldenrod:4008225535,palegreen:2566625535,paleturquoise:2951671551,palevioletred:3681588223,papayawhip:4293907967,peachpuff:4292524543,peru:3448061951,pink:4290825215,plum:3718307327,powderblue:2967529215,purple:2147516671,rebeccapurple:1714657791,red:4278190335,rosybrown:3163525119,royalblue:1097458175,saddlebrown:2336560127,salmon:4202722047,sandybrown:4104413439,seagreen:780883967,seashell:4294307583,sienna:2689740287,silver:3233857791,skyblue:2278484991,slateblue:1784335871,slategray:1887473919,slategrey:1887473919,snow:4294638335,springgreen:16744447,steelblue:1182971135,tan:3535047935,teal:8421631,thistle:3636451583,tomato:4284696575,turquoise:1088475391,violet:4001558271,wheat:4125012991,white:4294967295,whitesmoke:4126537215,yellow:4294902015,yellowgreen:2597139199},zt="[-+]?\\d*\\.?\\d+",Ot={rgb:new RegExp("rgb"+At(zt,zt,zt)),rgba:new RegExp("rgba"+At(zt,zt,zt,zt)),hsl:new RegExp("hsl"+At(zt,"[-+]?\\d*\\.?\\d+%","[-+]?\\d*\\.?\\d+%")),hsla:new RegExp("hsla"+At(zt,"[-+]?\\d*\\.?\\d+%","[-+]?\\d*\\.?\\d+%",zt)),hex3:/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex4:/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#([0-9a-fA-F]{6})$/,hex8:/^#([0-9a-fA-F]{8})$/};function Tt(e){const t=parseInt(e,10);return t<0?0:t>255?255:t}function jt(e){const t=parseFloat(e);return t<0?0:t>1?255:Math.round(255*t)}function Ft(e,t,n){let r=n;return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+6*(t-e)*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function Ht(e,t,n){const r=n<.5?n*(1+t):n+t-n*t,i=2*n-r,o=Ft(i,r,e+1/3),a=Ft(i,r,e),l=Ft(i,r,e-1/3);return Math.round(255*o)<<24|Math.round(255*a)<<16|Math.round(255*l)<<8}function Mt(e){return(parseFloat(e)%360+360)%360/360}function Dt(e){const t=parseFloat(e);return t<0?0:t>100?1:t/100}function Ut(e,t={}){if(Number.isInteger(e))return e;let n=function(e){let t;return"number"==typeof e?e>>>0===e&&e>=0&&e<=4294967295?e:null:(t=Ot.hex6.exec(e),Array.isArray(t)?parseInt(t[1]+"ff",16)>>>0:Object.hasOwnProperty.call(Rt,e)?Rt[e]:(t=Ot.rgb.exec(e),Array.isArray(t)?(Tt(t[1])<<24|Tt(t[2])<<16|Tt(t[3])<<8|255)>>>0:(t=Ot.rgba.exec(e),t?(Tt(t[1])<<24|Tt(t[2])<<16|Tt(t[3])<<8|jt(t[4]))>>>0:(t=Ot.hex3.exec(e),t?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+"ff",16)>>>0:(t=Ot.hex8.exec(e),t?parseInt(t[1],16)>>>0:(t=Ot.hex4.exec(e),t?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+t[4]+t[4],16)>>>0:(t=Ot.hsl.exec(e),t?(255|Ht(Mt(t[1]),Dt(t[2]),Dt(t[3])))>>>0:(t=Ot.hsla.exec(e),t?(Ht(Mt(t[1]),Dt(t[2]),Dt(t[3]))|jt(t[4]))>>>0:null))))))))}(e);return t.platform||(t.platform="android"),null===n?0:(n=(n<<24|n>>>8)>>>0,"android"===t.platform&&(n|=0),n)}function Wt(e,t){return Array.isArray(e)?e.map(e=>Ut(e,t)):[0]}class Bt extends Ue{constructor(){super(),this.documentElement=new Ye("document")}createElement(e){return new Ye(e)}createElementNS(e,t){return new Ye(`${e}:${t}`)}}Bt.createElement=Bt.prototype.createElement,Bt.createElementNS=Bt.prototype.createElementNS;const{createContainer:Vt,updateContainer:$t,getPublicRootInstance:qt,injectIntoDevTools:Qt}=kt;class Kt{constructor(e){if(!e.appName||!e.entryPage)throw new TypeError("Invalid arguments");this.config=e,this.regist=this.start,this.render=this.render.bind(this);const t=new Bt;this.rootContainer=Vt(t,0,!1,null)}static get Native(){return Lt}start(){_t.regist(this.config.appName,this.render)}render(e){const{appName:t,entryPage:n,silent:r=!1,bubbles:i=!1,callback:o=(()=>{})}=this.config,{__instanceId__:l}=e;this.rootContainer.containerInfo.nodeId=l,r&&X(r),i&&function(e=!1){q=e}(i),T(l,this.rootContainer);const s=a.a.createElement(n,e);return $t(s,this.rootContainer,null,o),qt(this.rootContainer)}}Kt.version="2.13.7";const Gt={registerComponent(e,t){new Kt({appName:e,entryPage:t}).start()}};class Yt extends a.a.Component{constructor(){super(...arguments),this.instance=null}setPressed(e){lt(this.instance,"setPressed",[e])}setHotspot(e,t){lt(this.instance,"setHotspot",[e,t])}render(){const e=this.props,{collapsable:t,style:n={}}=e,r=S(e,s),i=n,{nativeBackgroundAndroid:o}=r;return"boolean"==typeof t&&(i.collapsable=t),void 0!==(null==o?void 0:o.color)&&(o.color=Ut(o.color)),a.a.createElement("div",w({ref:e=>{this.instance=e},nativeName:"View",style:i},r))}}function Xt(e,t){let{style:n}=e,r=S(e,u);const i=n;if(n&&(Array.isArray(n)?-1===n.filter(e=>"object"==typeof e&&e).findIndex(e=>e.color||e.colors)&&(i[0].color="#000"):"object"==typeof n&&void 0===n.color&&void 0===n.colors&&(i.color="#000")),r.text="","string"==typeof r.children)r.text=Q(r.children);else if("number"==typeof r.children)r.text=Q(r.children.toString());else if(Array.isArray(r.children)){const e=r.children.filter(e=>"string"==typeof e||"number"==typeof e).join("");e&&(r.text=Q(e),r.children=r.text)}return a.a.createElement("p",w({ref:t,nativeName:"Text",style:i},r))}Xt.displayName="Text";const Jt=a.a.forwardRef(Xt);Jt.displayName="Text";var Zt=Object.freeze({__proto__:null,default:Jt});class en extends a.a.Component{static get resizeMode(){return{contain:"contain",cover:"cover",stretch:"stretch",center:"center",repeat:"repeat"}}static getSize(e,t,n){if("string"!=typeof e)throw new TypeError("Image.getSize first argument must be a string url");const r=he(e);return"function"==typeof t&&r.then(e=>t(e.width,e.height)),"function"==typeof n?r.catch(n):r.catch(e=>{}),r}render(){const e=this.props,{children:t,style:n,imageStyle:r,imageRef:i,source:o,sources:l,src:s,srcs:u,tintColor:d,tintColors:f}=e,p=S(e,c),h=this.getImageUrls({src:s,srcs:u,source:o,sources:l});"ios"===Pt.platform.OS?h.length&&(p.source=h.map(e=>({uri:e}))):"android"===Pt.platform.OS&&(1===h.length?[p.src]=h:h.length>1&&(p.srcs=h)),"string"==typeof p.defaultSource&&(p.defaultSource.indexOf("data:image/"),p.defaultSource=Z(p.defaultSource));const m=w({},n);return this.handleTintColor(m,d,f),p.style=m,t?a.a.createElement(Yt,{style:n},a.a.createElement("img",w(w({},p),{},{nativeName:"Image",alt:"",ref:i,style:[{position:"absolute",left:0,right:0,top:0,bottom:0,width:n.width,height:n.height},r]})),t):a.a.createElement("img",w(w({},p),{},{nativeName:"Image",alt:"",ref:i}))}getImageUrls({src:e,srcs:t,source:n,sources:r}){let i=[];if("string"==typeof e&&i.push(e),Array.isArray(t)&&(i=[...i,...t]),n)if("string"==typeof n)i.push(n);else if("object"==typeof n&&null!==n){const{uri:e}=n;e&&i.push(e)}return r&&Array.isArray(r)&&r.forEach(e=>{"string"==typeof e?i.push(e):"object"==typeof e&&null!==e&&e.uri&&i.push(e.uri)}),i.length&&(i=i.map(e=>Z(e))),i}handleTintColor(e,t,n){t&&Object.assign(e,{tintColor:t}),Array.isArray(n)&&Object.assign(e,{tintColors:n})}}en.prefetch=me;class tn{constructor(){this.Value=tn.Value}static Value(e){return e}static timing(e,t){return new Ce({mode:"timing",delay:0,startValue:e,toValue:t.toValue,duration:t.duration,timingFunction:t.easing||"linear"})}}tn.View=Yt,tn.Text=Zt,tn.Image=en;const nn={step0:e=>e>0?1:0,step1:e=>e>=1?1:0,linear:()=>"linear",ease:()=>"ease",quad:e=>e**2,cubic:e=>e**3,poly:e=>t=>t**e,sin:e=>1-Math.cos(e*Math.PI/2),circle:e=>1-Math.sqrt(1-e*e),exp:e=>2**(10*(e-1)),elastic:()=>"elastic",back:(e=1.70158)=>t=>t*t*((e+1)*t-e),bounce(e){let t=e;return t<1/2.75?7.5625*t*t:t<2/2.75?(t-=1.5/2.75,7.5625*t*t+.75):t<2.5/2.75?(t-=2.25/2.75,7.5625*t*t+.9375):(t-=2.625/2.75,7.5625*t*t+.984375)},bezier:()=>"bezier",in:()=>"ease-in",out:()=>"ease-out",inOut:()=>"ease-in-out"};function rn(e){return a.a.createElement("li",w({nativeName:"ListViewItem"},e))}class on extends a.a.Component{constructor(){super(...arguments),this.instance=null}expandPullHeader(){lt(this.instance,"expandPullHeader",[])}collapsePullHeader(e){"android"===Pt.platform.OS?lt(this.instance,"collapsePullHeader",[e]):void 0!==e?lt(this.instance,"collapsePullHeaderWithOptions",[e]):lt(this.instance,"collapsePullHeader",[])}render(){const e=this.props,{children:t}=e,n=S(e,d);return a.a.createElement("div",w({nativeName:"PullHeaderView",ref:e=>{this.instance=e}},n),t)}}class an extends a.a.Component{constructor(){super(...arguments),this.instance=null}expandPullFooter(){lt(this.instance,"expandPullFooter",[])}collapsePullFooter(){lt(this.instance,"collapsePullFooter",[])}render(){const e=this.props,{children:t}=e,n=S(e,f);return a.a.createElement("div",w({nativeName:"PullFooterView",ref:e=>{this.instance=e}},n),t)}}an.defaultProps={sticky:!0};const ln={onDisappear:"onDisAppear"},sn={onDisappear:"onDisappear"};class un extends a.a.Component{constructor(e){super(e),this.instance=null,this.pullHeader=null,this.pullFooter=null,this.handleInitialListReady=this.handleInitialListReady.bind(this),this.state={initialListReady:!1}}static convertName(e){return"android"===Pt.platform.OS&&ln[e]?ln[e]:"ios"===Pt.platform.OS&&sn[e]?sn[e]:e}componentDidMount(){const{getRowKey:e}=this.props}scrollToIndex(e,t,n){"number"==typeof e&&"number"==typeof t&&"boolean"==typeof n&&lt(this.instance,"scrollToIndex",[e,t,n])}scrollToContentOffset(e,t,n){"number"==typeof e&&"number"==typeof t&&"boolean"==typeof n&&lt(this.instance,"scrollToContentOffset",[e,t,n])}expandPullHeader(){this.pullHeader&&this.pullHeader.expandPullHeader()}collapsePullHeader(e){this.pullHeader&&this.pullHeader.collapsePullHeader(e)}expandPullFooter(){this.pullFooter&&this.pullFooter.expandPullFooter()}collapsePullFooter(){this.pullFooter&&this.pullFooter.collapsePullFooter()}render(){const e=this.props,{children:t,style:n,renderRow:r,renderPullHeader:i,renderPullFooter:o,getRowType:l,getRowStyle:s,getRowKey:u,dataSource:c,initialListSize:d,rowShouldSticky:f,onRowLayout:h,onHeaderPulling:m,onHeaderReleased:y,onFooterPulling:g,onFooterReleased:b,onAppear:v,onDisappear:k,onWillAppear:E,onWillDisappear:x}=e,C=S(e,p),N=[];if("function"==typeof r){const{initialListReady:e}=this.state;let{numberOfRows:t}=this.props;const p=this.getPullHeader(i,m,y),S=this.getPullFooter(o,g,b);!t&&c&&(t=c.length),e||(t=Math.min(t,d||10));for(let e=0;e<t;e+=1){const t={};let n;n=c?r(c[e],null,e):r(e),this.handleRowProps(t,e,{getRowKey:u,getRowStyle:s,getRowType:l,onRowLayout:h,rowShouldSticky:f}),[{func:v,name:"onAppear"},{func:k,name:"onDisappear"},{func:E,name:"onWillAppear"},{func:x,name:"onWillDisappear"}].forEach(({func:n,name:r})=>{"function"==typeof n&&(t[un.convertName(r)]=()=>{n(e)})}),n&&N.push(a.a.createElement(rn,w({},t),n))}p&&N.unshift(p),S&&N.push(S),"function"==typeof f&&Object.assign(C,{rowShouldSticky:!0});const I=[v,k,E,x];C.exposureEventEnabled=I.some(e=>"function"==typeof e),C.numberOfRows=N.length,C.initialListSize=d,C.style=w({overflow:"scroll"},n)}return!C.onLoadMore&&C.onEndReached&&(C.onLoadMore=C.onEndReached),a.a.createElement("ul",w({ref:e=>{this.instance=e},nativeName:"ListView",initialListReady:this.handleInitialListReady},C),N.length?N:t)}handleInitialListReady(){this.setState({initialListReady:!0})}getPullHeader(e,t,n){let r=null;return"function"==typeof e&&(r=a.a.createElement(on,{key:"pull-header",ref:e=>{this.pullHeader=e},onHeaderPulling:t,onHeaderReleased:n},e())),r}getPullFooter(e,t,n){let r=null;return"function"==typeof e&&(r=a.a.createElement(an,{key:"pull-footer",ref:e=>{this.pullFooter=e},onFooterPulling:t,onFooterReleased:n},e())),r}handleRowProps(e,t,{getRowKey:n,getRowStyle:r,onRowLayout:i,getRowType:o,rowShouldSticky:a}){if("function"==typeof n&&(e.key=n(t)),"function"==typeof r&&(e.style=r(t)),"function"==typeof i&&(e.onLayout=e=>{i(e,t)}),"function"==typeof o){const n=o(t);Number.isInteger(n),e.type=n}"function"==typeof a&&(e.sticky=a(t))}}un.defaultProps={numberOfRows:0};class cn extends a.a.Component{constructor(e){super(e),this.instance=null,this.refreshComplected=this.refreshCompleted.bind(this)}startRefresh(){lt(this.instance,"startRefresh",null)}refreshCompleted(){lt(this.instance,"refreshComplected",null)}render(){const e=this.props,{children:t}=e,n=S(e,h);return a.a.createElement("div",w({nativeName:"RefreshWrapper",ref:e=>{this.instance=e}},n),a.a.createElement("div",{nativeName:"RefreshWrapperItemView",style:{left:0,right:0,position:"absolute"}},this.getRefresh()),t)}getRefresh(){const{getRefresh:e}=this.props;return"function"==typeof e&&e()||null}}class dn{constructor(){this.top=null,this.size=0}push(e){this.top={data:e,next:this.top},this.size+=1}peek(){return null===this.top?null:this.top.data}pop(){if(null===this.top)return null;const e=this.top;return this.top=this.top.next,this.size>0&&(this.size-=1),e.data}clear(){this.top=null,this.size=0}displayAll(){const e=[];if(null===this.top)return e;let t=this.top;for(let n=0,r=this.size;n<r;n+=1)e[n]=t.data,t=t.next;return e}}class fn extends a.a.Component{constructor(e){super(e),this.stack=new dn,this.instance=null,this.routeList={};const{initialRoute:t}=e;if(null==t?void 0:t.component){new Kt({appName:t.routeName,entryPage:t.component}).regist(),this.routeList[t.routeName]=!0}this.handleAndroidBack=this.handleAndroidBack.bind(this)}componentWillMount(){"android"===Pt.platform.OS&&(this.backListener=pt.addListener(this.handleAndroidBack))}componentDidMount(){const{initialRoute:e}=this.props;this.stack.push({routeName:e.routeName||"",component:e.component||"",initProps:e.initProps||""})}componentWillUnmount(){this.backListener&&this.backListener.remove()}getCurrentPage(){return this.stack.peek()}handleAndroidBack(){this.stack.size>1&&this.pop({animated:!0})}push(e){if(null==e?void 0:e.component){if(!this.routeList[e.routeName]){new Kt({appName:e.routeName,entryPage:e.component}).regist(),this.routeList[e.routeName]=!0}delete e.component}const t=[e];this.stack.push(e),lt(this.instance,"push",t)}pop(e){if(this.stack.size>1){const t=[e];this.stack.pop(),lt(this.instance,"pop",t)}}clear(){this.stack.clear()}render(){const e=this.props,{initialRoute:{component:t}}=e,n=S(e.initialRoute,y),r=S(e,m);return r.initialRoute=n,a.a.createElement("div",w({nativeName:"Navigator",ref:e=>{this.instance=e}},r))}}function pn(e){return a.a.createElement("div",w(w({nativeName:"ViewPagerItem"},e),{},{style:{position:"absolute",left:0,top:0,right:0,bottom:0,collapsable:!1}}))}class hn extends a.a.Component{constructor(e){super(e),this.instance=null,this.setPage=this.setPage.bind(this),this.setPageWithoutAnimation=this.setPageWithoutAnimation.bind(this),this.onPageScrollStateChanged=this.onPageScrollStateChanged.bind(this)}onPageScrollStateChanged(e){const{onPageScrollStateChanged:t}=this.props;t&&t(e.pageScrollState)}setPage(e){"number"==typeof e&&lt(this.instance,"setPage",[e])}setPageWithoutAnimation(e){"number"==typeof e&&lt(this.instance,"setPageWithoutAnimation",[e])}render(){const e=this.props,{children:t,onPageScrollStateChanged:n}=e,r=S(e,g);let i=[];return Array.isArray(t)?i=t.map(e=>{const t={};return"string"==typeof e.key&&(t.key="viewPager_"+e.key),a.a.createElement(pn,w({},t),e)}):i.push(a.a.createElement(pn,null,t)),"function"==typeof n&&(r.onPageScrollStateChanged=this.onPageScrollStateChanged),a.a.createElement("div",w({nativeName:"ViewPager",ref:e=>{this.instance=e}},r),i)}}function mn(){const e=C.platform.Localization;return!!e&&1===e.direction}const yn={caretColor:"caret-color"};class gn extends a.a.Component{constructor(e){super(e),this.instance=null,this._lastNativeText="",this.onChangeText=this.onChangeText.bind(this),this.onKeyboardWillShow=this.onKeyboardWillShow.bind(this)}componentDidMount(){const{value:e,autoFocus:t}=this.props;this._lastNativeText=e,t&&this.focus()}componentWillUnmount(){this.blur()}getValue(){return new Promise(e=>{lt(this.instance,"getValue",t=>e(t.text))})}setValue(e){return lt(this.instance,"setValue",[e]),e}focus(){lt(this.instance,"focusTextInput",[])}blur(){lt(this.instance,"blurTextInput",[])}showInputMethod(){}hideInputMethod(){}clear(){lt(this.instance,"clear",[])}render(){const e=w({},this.props);return["underlineColorAndroid","placeholderTextColor","placeholderTextColors","caretColor","caret-color"].forEach(t=>{let n=t;const r=this.props[t];"string"==typeof this.props[t]&&(yn[t]&&(n=yn[t]),Array.isArray(e.style)?e.style.push({[n]:r}):e.style&&"object"==typeof e.style?e.style[n]=r:e.style={[n]:r},delete e[t])}),mn()&&(e.style?"object"!=typeof e.style||Array.isArray(e.style)||e.style.textAlign||(e.style.textAlign="right"):e.style={textAlign:"right"}),a.a.createElement("div",w(w({nativeName:"TextInput"},e),{},{ref:e=>{this.instance=e},onChangeText:this.onChangeText,onKeyboardWillShow:this.onKeyboardWillShow}))}onChangeText(e){const{onChangeText:t}=this.props;"function"==typeof t&&t(e.text),this.instance&&(this._lastNativeText=e.text)}onKeyboardWillShow(e){const{onKeyboardWillShow:t}=this.props,n=e;"android"===Pt.platform.OS&&(n.keyboardHeight/=Pt.screen.scale),"function"==typeof t&&t(n)}}const bn=Pt.window.scale;let vn=Math.round(.4*bn)/bn;function wn(e){return e}0===vn&&(vn=1/bn);var kn=Object.freeze({__proto__:null,get hairlineWidth(){return vn},create:wn});const Sn={baseVertical:{flexGrow:1,flexShrink:1,flexDirection:"column",overflow:"scroll"},baseHorizontal:{flexGrow:1,flexShrink:1,flexDirection:"row",overflow:"scroll"},contentContainerVertical:{collapsable:!1,flexDirection:"column"},contentContainerHorizontal:{collapsable:!1,flexDirection:"row"}};class En extends a.a.Component{constructor(){super(...arguments),this.instance=null}scrollTo(e,t,n=!0){let r=e,i=t,o=n;"number"==typeof e||"object"==typeof e&&e&&({x:r,y:i,animated:o}=e),r=r||0,i=i||0,o=!!o,lt(this.instance,"scrollTo",[r,i,o])}scrollToWithDuration(e=0,t=0,n=1e3){lt(this.instance,"scrollToWithOptions",[{x:e,y:t,duration:n}])}render(){const{horizontal:e,contentContainerStyle:t,children:n,style:r,onContentSizeChanged:i}=this.props,o=[e?Sn.contentContainerHorizontal:Sn.contentContainerVertical,t],l=e?Object.assign({},Sn.baseHorizontal,r):Object.assign({},Sn.baseVertical,r);return e&&(l.flexDirection=mn()?"row-reverse":"row"),a.a.createElement("div",w(w({nativeName:"ScrollView",ref:e=>{this.instance=e}},this.props),{},{style:l}),a.a.createElement(Yt,{style:o,onLayout:e=>{i&&i({width:e.width,height:e.height})}},n))}}const xn={modal:{position:"absolute",collapsable:!1},container:{position:"absolute",left:0,top:0}};class Cn extends a.a.Component{constructor(e){super(e),this.eventSubscription=null}componentDidMount(){"ios"===Pt.platform.OS&&(this.eventSubscription=new ue("modalDismissed"),this.eventSubscription.addCallback(e=>{const{primaryKey:t,onDismiss:n}=this.props;e.primaryKey===t&&"function"==typeof n&&n()}))}componentWillUnmount(){"ios"===Pt.platform.OS&&this.eventSubscription&&this.eventSubscription.unregister()}render(){const{children:e,visible:t,transparent:n,animated:r}=this.props;let{animationType:i}=this.props;if(!1===t)return null;const o={backgroundColor:n?"transparent":"white"};return i||(i="none",r&&(i="slide")),a.a.createElement("div",w({nativeName:"Modal",animationType:i,transparent:n,style:xn.modal},this.props),a.a.createElement(Yt,{style:[xn.container,o]},e))}}Cn.defaultProps={visible:!0};class Nn extends a.a.Component{constructor(e){super(e);const{requestFocus:t}=this.props;this.state={isFocus:!!t},this.handleFocus=this.handleFocus.bind(this)}render(){var e,t,n;const{requestFocus:r,children:i,nextFocusDownId:o,nextFocusUpId:l,nextFocusLeftId:s,nextFocusRightId:u,style:c,noFocusStyle:d,focusStyle:f,onClick:p}=this.props,{isFocus:h}=this.state,m=a.a.Children.only(i);let y;(null===(t=null===(e=null==m?void 0:m.child)||void 0===e?void 0:e.memoizedProps)||void 0===t?void 0:t.nativeName)?y=m.child.memoizedProps.nativeName:(null===(n=null==m?void 0:m.type)||void 0===n?void 0:n.displayName)&&(y=m.type.displayName);const g=o&&at(o),b=l&&at(l),v=s&&at(s),k=u&&at(u);let S=c;if("Text"!==y){const e=m.memoizedProps.style;S=w(w({},S),e)}if(Object.assign(S,h?f:d),"Text"===y)return a.a.createElement(Yt,{focusable:!0,nextFocusDownId:g,nextFocusUpId:b,nextFocusLeftId:v,nextFocusRightId:k,requestFocus:r,style:S,onClick:p,onFocus:this.handleFocus},m);const{children:E}=m.memoizedProps;return a.a.cloneElement(m,{nextFocusDownId:o,nextFocusUpId:l,nextFocusLeftId:s,nextFocusRightId:u,requestFocus:r,onClick:p,focusable:!0,children:E,style:S,onFocus:this.handleFocus})}handleFocus(e){const{onFocus:t}=this.props;"function"==typeof t&&t(e);const{isFocus:n}=this.state;n!==e.focus&&this.setState({isFocus:e.focus})}}function In(e){return a.a.createElement("iframe",w({title:"hippy",nativeName:"WebView"},e))}let Pn;class _n{constructor(e,t,n){if(this.protocol="",this.onWebSocketEvent=this.onWebSocketEvent.bind(this),Pn||(Pn=new ue("hippyWebsocketEvents")),this.readyState=0,this.webSocketCallbacks={},!e||"string"!=typeof e)throw new TypeError("Invalid WebSocket url");const r=w({},n);if(void 0!==t)if(Array.isArray(t)&&t.length>0)r["Sec-WebSocket-Protocol"]=t.join(",");else{if("string"!=typeof t)throw new TypeError("Invalid WebSocket protocols");r["Sec-WebSocket-Protocol"]=t}const i={headers:r,url:e};this.url=e,this.webSocketCallbackId=Pn.addCallback(this.onWebSocketEvent),x.callNativeWithPromise("websocket","connect",i).then(e=>{e&&0===e.code&&"number"==typeof e.id&&(this.webSocketId=e.id)})}close(e,t){1===this.readyState&&(this.readyState=2,x.callNative("websocket","close",{id:this.webSocketId,code:e,reason:t}))}send(e){if(1===this.readyState){if("string"!=typeof e)throw new TypeError("Unsupported websocket data type: "+typeof e);x.callNative("websocket","send",{id:this.webSocketId,data:e})}}set onopen(e){this.webSocketCallbacks.onOpen=e}set onclose(e){this.webSocketCallbacks.onClose=e}set onerror(e){this.webSocketCallbacks.onError=e}set onmessage(e){this.webSocketCallbacks.onMessage=e}onWebSocketEvent(e){if("object"!=typeof e||e.id!==this.webSocketId)return;const{type:t}=e;"onOpen"===t?this.readyState=1:"onClose"===t&&(this.readyState=3,Pn.removeCallback(this.webSocketCallbackId));const n=this.webSocketCallbacks[t];"function"==typeof n&&n(e.data)}}function Ln(e){return a.a.createElement("li",w({nativeName:"WaterfallItem"},e))}class An extends a.a.Component{constructor(e){super(e),this.instance=null,this.pullHeader=null,this.pullFooter=null,this.handleInitialListReady=this.handleInitialListReady.bind(this)}scrollToIndex({index:e=0,animated:t=!0}){lt(this.instance,"scrollToIndex",[e,e,t])}scrollToContentOffset({xOffset:e=0,yOffset:t=0,animated:n=!0}){lt(this.instance,"scrollToContentOffset",[e,t,n])}expandPullHeader(){this.pullHeader&&this.pullHeader.expandPullHeader()}collapsePullHeader(e){this.pullHeader&&this.pullHeader.collapsePullHeader(e)}expandPullFooter(){this.pullFooter&&this.pullFooter.expandPullFooter()}collapsePullFooter(){this.pullFooter&&this.pullFooter.collapsePullFooter()}render(){const e=this.props,{style:t={},renderBanner:n,numberOfColumns:r=2,columnSpacing:i=0,interItemSpacing:o=0,numberOfItems:l=0,preloadItemNumber:s=0,renderItem:u,renderPullHeader:c,renderPullFooter:d,getItemType:f,getItemKey:p,getItemStyle:h,contentInset:m={top:0,left:0,bottom:0,right:0},onItemLayout:y,onHeaderPulling:g,onHeaderReleased:v,onFooterPulling:k,onFooterReleased:E,containPullHeader:x=!1,containPullFooter:C=!1,containBannerView:N=!1}=e,I=w(w({},S(e,b)),{},{style:t,numberOfColumns:r,columnSpacing:i,interItemSpacing:o,preloadItemNumber:s,contentInset:m,containPullHeader:x,containPullFooter:C,containBannerView:N}),P=[];if("function"==typeof n){const e=n();e&&(P.push(a.a.createElement(Yt,{key:"bannerView"},a.a.cloneElement(e))),I.containBannerView=!0)}if("function"==typeof u){const e=this.getPullHeader(c,g,v),n=this.getPullFooter(d,k,E);for(let e=0;e<l;e+=1){const t={},n=u(e)||null;this.handleRowProps(t,e,{getItemKey:p,getItemStyle:h,getItemType:f,onItemLayout:y}),n&&P.push(a.a.createElement(Ln,w({},t),n))}e&&(P.unshift(e),I.containPullHeader=!0),n&&(P.push(n),I.containPullFooter=!0),I.style=w({},t)}return a.a.createElement("ul",w({nativeName:"WaterfallView",ref:e=>this.instance=e,initialListReady:this.handleInitialListReady.bind(this)},I),P)}componentDidMount(){const{getItemKey:e}=this.props}handleRowProps(e,t,{getItemKey:n,getItemStyle:r,onItemLayout:i,getItemType:o}){if("function"==typeof n&&(e.key=n(t)),"function"==typeof r&&(e.style=r(t)),"function"==typeof i&&(e.onLayout=e=>{i.call(this,e,t)}),"function"==typeof o){const n=o(t);Number.isInteger(n),e.type=n}}getPullHeader(e,t,n){let r=null;return"function"==typeof e&&(r=a.a.createElement(on,{key:"PullHeader",ref:e=>{this.pullHeader=e},onHeaderPulling:t,onHeaderReleased:n},e())),r}getPullFooter(e,t,n){let r=null;return"function"==typeof e&&(r=a.a.createElement(an,{key:"PullFooter",ref:e=>{this.pullFooter=e},onFooterPulling:t,onFooterReleased:n},e())),r}handleInitialListReady(){const{onInitialListReady:e}=this.props;"function"==typeof e&&e()}}e.WebSocket=_n;const{AsyncStorage:Rn,BackAndroid:zn,Bridge:On,Clipboard:Tn,Cookie:jn,Device:Fn,HippyRegister:Hn,ImageLoader:Mn,NetworkInfo:Dn,UIManager:Un,flushSync:Wn}=Lt,{callNative:Bn,callNativeWithPromise:Vn,callNativeWithCallbackId:$n,removeNativeCallback:qn}=On,Qn=null,Kn=e.ConsoleModule||e.console,Gn=Fn.platform,Yn=Kt,Xn=Kt,Jn=en,Zn=Hn,er=de,tr=ue,nr={get:e=>Fn[e]},rr={get:()=>Fn.screen.scale}}.call(this,n("./node_modules/webpack/buildin/global.js"))},"./third-party/react-reconciler/cjs/react-reconciler.production.min.js":function(e,t,n){(function(e){
/** @license React v0.26.2
 * react-reconciler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
e.exports=function(t){var r={},i=n("./node_modules/object-assign/index.js"),o=n("./node_modules/react/index.js"),a=n("./node_modules/scheduler/index.js");function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var s=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,u=60103,c=60106,d=60107,f=60108,p=60114,h=60109,m=60110,y=60112,g=60113,b=60120,v=60115,w=60116,k=60121,S=60129,E=60130,x=60131;if("function"==typeof Symbol&&Symbol.for){var C=Symbol.for;u=C("react.element"),c=C("react.portal"),d=C("react.fragment"),f=C("react.strict_mode"),p=C("react.profiler"),h=C("react.provider"),m=C("react.context"),y=C("react.forward_ref"),g=C("react.suspense"),b=C("react.suspense_list"),v=C("react.memo"),w=C("react.lazy"),k=C("react.block"),C("react.scope"),S=C("react.debug_trace_mode"),E=C("react.offscreen"),x=C("react.legacy_hidden")}var N="function"==typeof Symbol&&Symbol.iterator;function I(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=N&&e[N]||e["@@iterator"])?e:null}function P(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case d:return"Fragment";case c:return"Portal";case p:return"Profiler";case f:return"StrictMode";case g:return"Suspense";case b:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case m:return(e.displayName||"Context")+".Consumer";case h:return(e._context.displayName||"Context")+".Provider";case y:var t=e.render;return t=t.displayName||t.name||"",e.displayName||(""!==t?"ForwardRef("+t+")":"ForwardRef");case v:return P(e.type);case k:return P(e._render);case w:t=e._payload,e=e._init;try{return P(e(t))}catch(e){}}return null}function _(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!=(1026&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function L(e){if(_(e)!==e)throw Error(l(188))}function A(e){var t=e.alternate;if(!t){if(null===(t=_(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var o=i.alternate;if(null===o){if(null!==(r=i.return)){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return L(i),e;if(o===r)return L(i),t;o=o.sibling}throw Error(l(188))}if(n.return!==r.return)n=i,r=o;else{for(var a=!1,s=i.child;s;){if(s===n){a=!0,n=i,r=o;break}if(s===r){a=!0,r=i,n=o;break}s=s.sibling}if(!a){for(s=o.child;s;){if(s===n){a=!0,n=o,r=i;break}if(s===r){a=!0,r=o,n=i;break}s=s.sibling}if(!a)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}function R(e){if(!(e=A(e)))return null;for(var t=e;;){if(5===t.tag||6===t.tag)return t;if(t.child)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}function z(e,t){for(var n=e.alternate;null!==t;){if(t===e||t===n)return!0;t=t.return}return!1}var O,T=t.getPublicInstance,j=t.getRootHostContext,F=t.getChildHostContext,H=t.prepareForCommit,M=t.resetAfterCommit,D=t.createInstance,U=t.appendInitialChild,W=t.finalizeInitialChildren,B=t.prepareUpdate,V=t.shouldSetTextContent,$=t.createTextInstance,q=t.scheduleTimeout,Q=t.cancelTimeout,K=t.noTimeout,G=t.isPrimaryRenderer,Y=t.supportsMutation,X=t.supportsPersistence,J=t.supportsHydration,Z=t.getInstanceFromNode,ee=t.makeOpaqueHydratingObject,te=t.makeClientId,ne=t.beforeActiveInstanceBlur,re=t.afterActiveInstanceBlur,ie=t.preparePortalMount,oe=t.supportsTestSelectors,ae=t.findFiberRoot,le=t.getBoundingRect,se=t.getTextContent,ue=t.isHiddenSubtree,ce=t.matchAccessibilityRole,de=t.setFocusIfFocusable,fe=t.setupIntersectionObserver,pe=t.appendChild,he=t.appendChildToContainer,me=t.commitTextUpdate,ye=t.commitMount,ge=t.commitUpdate,be=t.insertBefore,ve=t.insertInContainerBefore,we=t.removeChild,ke=t.removeChildFromContainer,Se=t.resetTextContent,Ee=t.hideInstance,xe=t.hideTextInstance,Ce=t.unhideInstance,Ne=t.unhideTextInstance,Ie=t.clearContainer,Pe=t.commitMutationEffectsBegin,_e=t.commitMutationEffectsComplete,Le=t.cloneInstance,Ae=t.createContainerChildSet,Re=t.appendChildToContainerChildSet,ze=t.finalizeContainerChildren,Oe=t.replaceContainerChildren,Te=t.cloneHiddenInstance,je=t.cloneHiddenTextInstance,Fe=t.canHydrateInstance,He=t.canHydrateTextInstance,Me=t.canHydrateSuspenseInstance,De=t.isSuspenseInstancePending,Ue=t.isSuspenseInstanceFallback,We=t.registerSuspenseInstanceRetry,Be=t.getNextHydratableSibling,Ve=t.getFirstHydratableChild,$e=t.hydrateInstance,qe=t.hydrateTextInstance,Qe=t.hydrateSuspenseInstance,Ke=t.getNextHydratableInstanceAfterSuspenseInstance,Ge=t.commitHydratedContainer,Ye=t.commitHydratedSuspenseInstance,Xe=t.clearSuspenseBoundary,Je=t.clearSuspenseBoundaryFromContainer;function Ze(e){if(void 0===O)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);O=t&&t[1]||""}return"\n"+O+e}var et=!1;function tt(e,t){if(!e||et)return"";et=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var r=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){r=e}e.call(t.prototype)}else{try{throw Error()}catch(e){r=e}e()}}catch(e){if(e&&r&&"string"==typeof e.stack){for(var i=e.stack.split("\n"),o=r.stack.split("\n"),a=i.length-1,l=o.length-1;1<=a&&0<=l&&i[a]!==o[l];)l--;for(;1<=a&&0<=l;a--,l--)if(i[a]!==o[l]){if(1!==a||1!==l)do{if(a--,0>--l||i[a]!==o[l])return"\n"+i[a].replace(" at new "," at ")}while(1<=a&&0<=l);break}}}finally{et=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Ze(e):""}var nt=[],rt=-1;function it(e){return{current:e}}function ot(e){0>rt||(e.current=nt[rt],nt[rt]=null,rt--)}function at(e,t){rt++,nt[rt]=e.current,e.current=t}var lt={},st=it(lt),ut=it(!1),ct=lt;function dt(e,t){var n=e.type.contextTypes;if(!n)return lt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,o={};for(i in n)o[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function ft(e){return null!=(e=e.childContextTypes)}function pt(){ot(ut),ot(st)}function ht(e,t,n){if(st.current!==lt)throw Error(l(168));at(st,t),at(ut,n)}function mt(e,t,n){var r=e.stateNode;if(e=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in e))throw Error(l(108,P(t)||"Unknown",o));return i({},n,r)}function yt(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||lt,ct=st.current,at(st,e),at(ut,ut.current),!0}function gt(e,t,n){var r=e.stateNode;if(!r)throw Error(l(169));n?(e=mt(e,t,ct),r.__reactInternalMemoizedMergedChildContext=e,ot(ut),ot(st),at(st,e)):ot(ut),at(ut,n)}var bt=null,vt=null;(0,a.unstable_now)();var wt=0,kt=8;function St(e){if(0!=(1&e))return kt=15,1;if(0!=(2&e))return kt=14,2;if(0!=(4&e))return kt=13,4;var t=24&e;return 0!==t?(kt=12,t):0!=(32&e)?(kt=11,32):0!==(t=192&e)?(kt=10,t):0!=(256&e)?(kt=9,256):0!==(t=3584&e)?(kt=8,t):0!=(4096&e)?(kt=7,4096):0!==(t=4186112&e)?(kt=6,t):0!==(t=62914560&e)?(kt=5,t):67108864&e?(kt=4,67108864):0!=(134217728&e)?(kt=3,134217728):0!==(t=805306368&e)?(kt=2,t):0!=(1073741824&e)?(kt=1,1073741824):(kt=8,e)}function Et(e,t){var n=e.pendingLanes;if(0===n)return kt=0;var r=0,i=0,o=e.expiredLanes,a=e.suspendedLanes,l=e.pingedLanes;if(0!==o)r=o,i=kt=15;else if(0!==(o=134217727&n)){var s=o&~a;0!==s?(r=St(s),i=kt):0!==(l&=o)&&(r=St(l),i=kt)}else 0!==(o=n&~a)?(r=St(o),i=kt):0!==l&&(r=St(l),i=kt);if(0===r)return 0;if(r=n&((0>(r=31-_t(r))?0:1<<r)<<1)-1,0!==t&&t!==r&&0==(t&a)){if(St(t),i<=kt)return t;kt=i}if(0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)i=1<<(n=31-_t(t)),r|=e[n],t&=~i;return r}function xt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function Ct(e,t){switch(e){case 15:return 1;case 14:return 2;case 12:return 0===(e=Nt(24&~t))?Ct(10,t):e;case 10:return 0===(e=Nt(192&~t))?Ct(8,t):e;case 8:return 0===(e=Nt(3584&~t))&&(0===(e=Nt(4186112&~t))&&(e=512)),e;case 2:return 0===(t=Nt(805306368&~t))&&(t=268435456),t}throw Error(l(358,e))}function Nt(e){return e&-e}function It(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Pt(e,t,n){e.pendingLanes|=t;var r=t-1;e.suspendedLanes&=r,e.pingedLanes&=r,(e=e.eventTimes)[t=31-_t(t)]=n}var _t=Math.clz32?Math.clz32:function(e){return 0===e?32:31-(Lt(e)/At|0)|0},Lt=Math.log,At=Math.LN2;var Rt=a.unstable_runWithPriority,zt=a.unstable_scheduleCallback,Ot=a.unstable_cancelCallback,Tt=a.unstable_shouldYield,jt=a.unstable_requestPaint,Ft=a.unstable_now,Ht=a.unstable_getCurrentPriorityLevel,Mt=a.unstable_ImmediatePriority,Dt=a.unstable_UserBlockingPriority,Ut=a.unstable_NormalPriority,Wt=a.unstable_LowPriority,Bt=a.unstable_IdlePriority,Vt={},$t=void 0!==jt?jt:function(){},qt=null,Qt=null,Kt=!1,Gt=Ft(),Yt=1e4>Gt?Ft:function(){return Ft()-Gt};function Xt(){switch(Ht()){case Mt:return 99;case Dt:return 98;case Ut:return 97;case Wt:return 96;case Bt:return 95;default:throw Error(l(332))}}function Jt(e){switch(e){case 99:return Mt;case 98:return Dt;case 97:return Ut;case 96:return Wt;case 95:return Bt;default:throw Error(l(332))}}function Zt(e,t){return e=Jt(e),Rt(e,t)}function en(e,t,n){return e=Jt(e),zt(e,t,n)}function tn(){if(null!==Qt){var e=Qt;Qt=null,Ot(e)}nn()}function nn(){if(!Kt&&null!==qt){Kt=!0;var e=0;try{var t=qt;Zt(99,(function(){for(;e<t.length;e++){var n=t[e];do{n=n(!0)}while(null!==n)}})),qt=null}catch(t){throw null!==qt&&(qt=qt.slice(e+1)),zt(Mt,tn),t}finally{Kt=!1}}}var rn=s.ReactCurrentBatchConfig;var on="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},an=Object.prototype.hasOwnProperty;function ln(e,t){if(on(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++)if(!an.call(t,n[r])||!on(e[n[r]],t[n[r]]))return!1;return!0}function sn(e){switch(e.tag){case 5:return Ze(e.type);case 16:return Ze("Lazy");case 13:return Ze("Suspense");case 19:return Ze("SuspenseList");case 0:case 2:case 15:return e=tt(e.type,!1);case 11:return e=tt(e.type.render,!1);case 22:return e=tt(e.type._render,!1);case 1:return e=tt(e.type,!0);default:return""}}function un(e,t){if(e&&e.defaultProps){for(var n in t=i({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}var cn=it(null),dn=null,fn=null,pn=null;function hn(){pn=fn=dn=null}function mn(e,t){e=e.type._context,G?(at(cn,e._currentValue),e._currentValue=t):(at(cn,e._currentValue2),e._currentValue2=t)}function yn(e){var t=cn.current;ot(cn),e=e.type._context,G?e._currentValue=t:e._currentValue2=t}function gn(e,t){for(;null!==e;){var n=e.alternate;if((e.childLanes&t)===t){if(null===n||(n.childLanes&t)===t)break;n.childLanes|=t}else e.childLanes|=t,null!==n&&(n.childLanes|=t);e=e.return}}function bn(e,t){dn=e,pn=fn=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!=(e.lanes&t)&&(Xr=!0),e.firstContext=null)}function vn(e,t){if(pn!==e&&!1!==t&&0!==t)if("number"==typeof t&&1073741823!==t||(pn=e,t=1073741823),t={context:e,observedBits:t,next:null},null===fn){if(null===dn)throw Error(l(308));fn=t,dn.dependencies={lanes:0,firstContext:t,responders:null}}else fn=fn.next=t;return G?e._currentValue:e._currentValue2}var wn=!1;function kn(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null},effects:null}}function Sn(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function En(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function xn(e,t){if(null!==(e=e.updateQueue)){var n=(e=e.shared).pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}}function Cn(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var i=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?i=o=a:o=o.next=a,n=n.next}while(null!==n);null===o?i=o=t:o=o.next=t}else i=o=t;return n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Nn(e,t,n,r){var o=e.updateQueue;wn=!1;var a=o.firstBaseUpdate,l=o.lastBaseUpdate,s=o.shared.pending;if(null!==s){o.shared.pending=null;var u=s,c=u.next;u.next=null,null===l?a=c:l.next=c,l=u;var d=e.alternate;if(null!==d){var f=(d=d.updateQueue).lastBaseUpdate;f!==l&&(null===f?d.firstBaseUpdate=c:f.next=c,d.lastBaseUpdate=u)}}if(null!==a){for(f=o.baseState,l=0,d=c=u=null;;){s=a.lane;var p=a.eventTime;if((r&s)===s){null!==d&&(d=d.next={eventTime:p,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var h=e,m=a;switch(s=t,p=n,m.tag){case 1:if("function"==typeof(h=m.payload)){f=h.call(p,f,s);break e}f=h;break e;case 3:h.flags=-4097&h.flags|64;case 0:if(null==(s="function"==typeof(h=m.payload)?h.call(p,f,s):h))break e;f=i({},f,s);break e;case 2:wn=!0}}null!==a.callback&&(e.flags|=32,null===(s=o.effects)?o.effects=[a]:s.push(a))}else p={eventTime:p,lane:s,tag:a.tag,payload:a.payload,callback:a.callback,next:null},null===d?(c=d=p,u=f):d=d.next=p,l|=s;if(null===(a=a.next)){if(null===(s=o.shared.pending))break;a=s.next,s.next=null,o.lastBaseUpdate=s,o.shared.pending=null}}null===d&&(u=f),o.baseState=u,o.firstBaseUpdate=c,o.lastBaseUpdate=d,So|=l,e.lanes=l,e.memoizedState=f}}function In(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(null!==i){if(r.callback=null,r=n,"function"!=typeof i)throw Error(l(191,i));i.call(r)}}}var Pn=(new o.Component).refs;function _n(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:i({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var Ln={isMounted:function(e){return!!(e=e._reactInternals)&&_(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Qo(),i=Ko(e),o=En(r,i);o.payload=t,null!=n&&(o.callback=n),xn(e,o),Go(e,i,r)},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Qo(),i=Ko(e),o=En(r,i);o.tag=1,o.payload=t,null!=n&&(o.callback=n),xn(e,o),Go(e,i,r)},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Qo(),r=Ko(e),i=En(n,r);i.tag=2,null!=t&&(i.callback=t),xn(e,i),Go(e,r,n)}};function An(e,t,n,r,i,o,a){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,a):!t.prototype||!t.prototype.isPureReactComponent||(!ln(n,r)||!ln(i,o))}function Rn(e,t,n){var r=!1,i=lt,o=t.contextType;return"object"==typeof o&&null!==o?o=vn(o):(i=ft(t)?ct:st.current,o=(r=null!=(r=t.contextTypes))?dt(e,i):lt),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=Ln,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function zn(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ln.enqueueReplaceState(t,t.state,null)}function On(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs=Pn,kn(e);var o=t.contextType;"object"==typeof o&&null!==o?i.context=vn(o):(o=ft(t)?ct:st.current,i.context=dt(e,o)),Nn(e,n,i,r),i.state=e.memoizedState,"function"==typeof(o=t.getDerivedStateFromProps)&&(_n(e,t,o,n),i.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof i.getSnapshotBeforeUpdate||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||(t=i.state,"function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&Ln.enqueueReplaceState(i,i.state,null),Nn(e,n,i,r),i.state=e.memoizedState),"function"==typeof i.componentDidMount&&(e.flags|=4)}var Tn=Array.isArray;function jn(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(l(309));var r=n.stateNode}if(!r)throw Error(l(147,e));var i=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===i?t.ref:((t=function(e){var t=r.refs;t===Pn&&(t=r.refs={}),null===e?delete t[i]:t[i]=e})._stringRef=i,t)}if("string"!=typeof e)throw Error(l(284));if(!n._owner)throw Error(l(290,e))}return e}function Fn(e,t){if("textarea"!==e.type)throw Error(l(31,"[object Object]"===Object.prototype.toString.call(t)?"object with keys {"+Object.keys(t).join(", ")+"}":t))}function Hn(e){try{return(0,e._init)(e._payload)}catch(t){return e}}function Mn(e){function t(t,n){if(e){var r=t.lastEffect;null!==r?(r.nextEffect=n,t.lastEffect=n):t.firstEffect=t.lastEffect=n,n.nextEffect=null,n.flags=8}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Fa(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags=2,n):r:(t.flags=2,n):n}function a(t){return e&&null===t.alternate&&(t.flags=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Ua(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function f(e,t,n,r){if(null!==t){if(t.elementType===n.type){var o=i(t,n.props);return o.ref=jn(e,t,n),o.return=e,o}if(22===t.tag&&((o=n.type).$$typeof===w&&(o=Hn(o)),o.$$typeof===k&&o._render===t.type._render))return(t=i(t,n.props)).return=e,t.type=o,t}return(o=Ha(n.type,n.key,n.props,null,e.mode,r)).ref=jn(e,t,n),o.return=e,o}function p(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Wa(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function h(e,t,n,r,o){return null===t||7!==t.tag?((t=Ma(n,e.mode,r,o)).return=e,t):((t=i(t,n)).return=e,t)}function m(e,t,n){if("string"==typeof t||"number"==typeof t)return(t=Ua(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case u:return(n=Ha(t.type,t.key,t.props,null,e.mode,n)).ref=jn(e,null,t),n.return=e,n;case c:return(t=Wa(t,e.mode,n)).return=e,t;case w:return m(e,(0,t._init)(t._payload),n)}if(Tn(t)||I(t))return(t=Ma(t,e.mode,n,null)).return=e,t;Fn(e,t)}return null}function y(e,t,n,r){var i=null!==t?t.key:null;if("string"==typeof n||"number"==typeof n)return null!==i?null:s(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case u:return n.key===i?n.type===d?h(e,t,n.props.children,r,i):f(e,t,n,r):null;case c:return n.key===i?p(e,t,n,r):null;case w:return y(e,t,(i=n._init)(n._payload),r)}if(Tn(n)||I(n))return null!==i?null:h(e,t,n,r,null);Fn(e,n)}return null}function g(e,t,n,r,i){if("string"==typeof r||"number"==typeof r)return s(t,e=e.get(n)||null,""+r,i);if("object"==typeof r&&null!==r){switch(r.$$typeof){case u:return e=e.get(null===r.key?n:r.key)||null,r.type===d?h(t,e,r.props.children,i,r.key):f(t,e,r,i);case c:return p(t,e=e.get(null===r.key?n:r.key)||null,r,i);case w:return g(e,t,n,(0,r._init)(r._payload),i)}if(Tn(r)||I(r))return h(t,e=e.get(n)||null,r,i,null);Fn(t,r)}return null}function b(i,a,l,s){for(var u=null,c=null,d=a,f=a=0,p=null;null!==d&&f<l.length;f++){d.index>f?(p=d,d=null):p=d.sibling;var h=y(i,d,l[f],s);if(null===h){null===d&&(d=p);break}e&&d&&null===h.alternate&&t(i,d),a=o(h,a,f),null===c?u=h:c.sibling=h,c=h,d=p}if(f===l.length)return n(i,d),u;if(null===d){for(;f<l.length;f++)null!==(d=m(i,l[f],s))&&(a=o(d,a,f),null===c?u=d:c.sibling=d,c=d);return u}for(d=r(i,d);f<l.length;f++)null!==(p=g(d,i,f,l[f],s))&&(e&&null!==p.alternate&&d.delete(null===p.key?f:p.key),a=o(p,a,f),null===c?u=p:c.sibling=p,c=p);return e&&d.forEach((function(e){return t(i,e)})),u}function v(i,a,s,u){var c=I(s);if("function"!=typeof c)throw Error(l(150));if(null==(s=c.call(s)))throw Error(l(151));for(var d=c=null,f=a,p=a=0,h=null,b=s.next();null!==f&&!b.done;p++,b=s.next()){f.index>p?(h=f,f=null):h=f.sibling;var v=y(i,f,b.value,u);if(null===v){null===f&&(f=h);break}e&&f&&null===v.alternate&&t(i,f),a=o(v,a,p),null===d?c=v:d.sibling=v,d=v,f=h}if(b.done)return n(i,f),c;if(null===f){for(;!b.done;p++,b=s.next())null!==(b=m(i,b.value,u))&&(a=o(b,a,p),null===d?c=b:d.sibling=b,d=b);return c}for(f=r(i,f);!b.done;p++,b=s.next())null!==(b=g(f,i,p,b.value,u))&&(e&&null!==b.alternate&&f.delete(null===b.key?p:b.key),a=o(b,a,p),null===d?c=b:d.sibling=b,d=b);return e&&f.forEach((function(e){return t(i,e)})),c}return function e(r,o,s,f){var p="object"==typeof s&&null!==s&&s.type===d&&null===s.key;p&&(s=s.props.children);var h="object"==typeof s&&null!==s;if(h)switch(s.$$typeof){case u:e:{for(h=s.key,p=o;null!==p;){if(p.key===h){switch(p.tag){case 7:if(s.type===d){n(r,p.sibling),(o=i(p,s.props.children)).return=r,r=o;break e}break;case 22:if((h=s.type).$$typeof===w&&(h=Hn(h)),h.$$typeof===k&&h._render===p.type._render){n(r,p.sibling),(o=i(p,s.props)).type=h,o.return=r,r=o;break e}default:if(p.elementType===s.type){n(r,p.sibling),(o=i(p,s.props)).ref=jn(r,p,s),o.return=r,r=o;break e}}n(r,p);break}t(r,p),p=p.sibling}s.type===d?((o=Ma(s.props.children,r.mode,f,s.key)).return=r,r=o):((f=Ha(s.type,s.key,s.props,null,r.mode,f)).ref=jn(r,o,s),f.return=r,r=f)}return a(r);case c:e:{for(p=s.key;null!==o;){if(o.key===p){if(4===o.tag&&o.stateNode.containerInfo===s.containerInfo&&o.stateNode.implementation===s.implementation){n(r,o.sibling),(o=i(o,s.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=Wa(s,r.mode,f)).return=r,r=o}return a(r);case w:return e(r,o,(p=s._init)(s._payload),f)}if("string"==typeof s||"number"==typeof s)return s=""+s,null!==o&&6===o.tag?(n(r,o.sibling),(o=i(o,s)).return=r,r=o):(n(r,o),(o=Ua(s,r.mode,f)).return=r,r=o),a(r);if(Tn(s))return b(r,o,s,f);if(I(s))return v(r,o,s,f);if(h&&Fn(r,s),void 0===s&&!p)switch(r.tag){case 1:case 22:case 0:case 11:case 15:throw Error(l(152,P(r.type)||"Component"))}return n(r,o)}}var Dn=Mn(!0),Un=Mn(!1),Wn={},Bn=it(Wn),Vn=it(Wn),$n=it(Wn);function qn(e){if(e===Wn)throw Error(l(174));return e}function Qn(e,t){at($n,t),at(Vn,e),at(Bn,Wn),e=j(t),ot(Bn),at(Bn,e)}function Kn(){ot(Bn),ot(Vn),ot($n)}function Gn(e){var t=qn($n.current),n=qn(Bn.current);n!==(t=F(n,e.type,t))&&(at(Vn,e),at(Bn,t))}function Yn(e){Vn.current===e&&(ot(Bn),ot(Vn))}var Xn=it(0);function Jn(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||De(n)||Ue(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!=(64&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Zn=null,er=null,tr=!1;function nr(e,t){var n=Ta(5,null,null,0);n.elementType="DELETED",n.type="DELETED",n.stateNode=t,n.return=e,n.flags=8,null!==e.lastEffect?(e.lastEffect.nextEffect=n,e.lastEffect=n):e.firstEffect=e.lastEffect=n}function rr(e,t){switch(e.tag){case 5:return null!==(t=Fe(t,e.type,e.pendingProps))&&(e.stateNode=t,!0);case 6:return null!==(t=He(t,e.pendingProps))&&(e.stateNode=t,!0);case 13:if(null!==(t=Me(t))){e.memoizedState={dehydrated:t,retryLane:1073741824};var n=Ta(18,null,null,0);return n.stateNode=t,n.return=e,e.child=n,!0}return!1;default:return!1}}function ir(e){if(tr){var t=er;if(t){var n=t;if(!rr(e,t)){if(!(t=Be(n))||!rr(e,t))return e.flags=-1025&e.flags|2,tr=!1,void(Zn=e);nr(Zn,n)}Zn=e,er=Ve(t)}else e.flags=-1025&e.flags|2,tr=!1,Zn=e}}function or(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;Zn=e}function ar(e){if(!J||e!==Zn)return!1;if(!tr)return or(e),tr=!0,!1;var t=e.type;if(5!==e.tag||"head"!==t&&"body"!==t&&!V(t,e.memoizedProps))for(t=er;t;)nr(e,t),t=Be(t);if(or(e),13===e.tag){if(!J)throw Error(l(316));if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));er=Ke(e)}else er=Zn?Be(e.stateNode):null;return!0}function lr(){J&&(er=Zn=null,tr=!1)}var sr=[];function ur(){for(var e=0;e<sr.length;e++){var t=sr[e];G?t._workInProgressVersionPrimary=null:t._workInProgressVersionSecondary=null}sr.length=0}var cr=s.ReactCurrentDispatcher,dr=s.ReactCurrentBatchConfig,fr=0,pr=null,hr=null,mr=null,yr=!1,gr=!1;function br(){throw Error(l(321))}function vr(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!on(e[n],t[n]))return!1;return!0}function wr(e,t,n,r,i,o){if(fr=o,pr=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,cr.current=null===e||null===e.memoizedState?Qr:Kr,e=n(r,i),gr){o=0;do{if(gr=!1,!(25>o))throw Error(l(301));o+=1,mr=hr=null,t.updateQueue=null,cr.current=Gr,e=n(r,i)}while(gr)}if(cr.current=qr,t=null!==hr&&null!==hr.next,fr=0,mr=hr=pr=null,yr=!1,t)throw Error(l(300));return e}function kr(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-517,e.lanes&=~n}function Sr(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===mr?pr.memoizedState=mr=e:mr=mr.next=e,mr}function Er(){if(null===hr){var e=pr.alternate;e=null!==e?e.memoizedState:null}else e=hr.next;var t=null===mr?pr.memoizedState:mr.next;if(null!==t)mr=t,hr=e;else{if(null===e)throw Error(l(310));e={memoizedState:(hr=e).memoizedState,baseState:hr.baseState,baseQueue:hr.baseQueue,queue:hr.queue,next:null},null===mr?pr.memoizedState=mr=e:mr=mr.next=e}return mr}function xr(e,t){return"function"==typeof t?t(e):t}function Cr(e){var t=Er(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=hr,i=r.baseQueue,o=n.pending;if(null!==o){if(null!==i){var a=i.next;i.next=o.next,o.next=a}r.baseQueue=i=o,n.pending=null}if(null!==i){i=i.next,r=r.baseState;var s=a=o=null,u=i;do{var c=u.lane;if((fr&c)===c)null!==s&&(s=s.next={lane:0,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null}),r=u.eagerReducer===e?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null};null===s?(a=s=d,o=r):s=s.next=d,pr.lanes|=c,So|=c}u=u.next}while(null!==u&&u!==i);null===s?o=r:s.next=a,on(r,t.memoizedState)||(Xr=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=s,n.lastRenderedState=r}return[t.memoizedState,n.dispatch]}function Nr(e){var t=Er(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(null!==i){n.pending=null;var a=i=i.next;do{o=e(o,a.action),a=a.next}while(a!==i);on(o,t.memoizedState)||(Xr=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ir(e,t,n){var r=t._getVersion;r=r(t._source);var i=G?t._workInProgressVersionPrimary:t._workInProgressVersionSecondary;if(null!==i?e=i===r:(e=e.mutableReadLanes,(e=(fr&e)===e)&&(G?t._workInProgressVersionPrimary=r:t._workInProgressVersionSecondary=r,sr.push(t))),e)return n(t._source);throw sr.push(t),Error(l(350))}function Pr(e,t,n,r){var i=ho;if(null===i)throw Error(l(349));var o=t._getVersion,a=o(t._source),s=cr.current,u=s.useState((function(){return Ir(i,t,n)})),c=u[1],d=u[0];u=mr;var f=e.memoizedState,p=f.refs,h=p.getSnapshot,m=f.source;f=f.subscribe;var y=pr;return e.memoizedState={refs:p,source:t,subscribe:r},s.useEffect((function(){p.getSnapshot=n,p.setSnapshot=c;var e=o(t._source);if(!on(a,e)){e=n(t._source),on(d,e)||(c(e),e=Ko(y),i.mutableReadLanes|=e&i.pendingLanes),e=i.mutableReadLanes,i.entangledLanes|=e;for(var r=i.entanglements,l=e;0<l;){var s=31-_t(l),u=1<<s;r[s]|=e,l&=~u}}}),[n,t,r]),s.useEffect((function(){return r(t._source,(function(){var e=p.getSnapshot,n=p.setSnapshot;try{n(e(t._source));var r=Ko(y);i.mutableReadLanes|=r&i.pendingLanes}catch(e){n((function(){throw e}))}}))}),[t,r]),on(h,n)&&on(m,t)&&on(f,r)||((e={pending:null,dispatch:null,lastRenderedReducer:xr,lastRenderedState:d}).dispatch=c=$r.bind(null,pr,e),u.queue=e,u.baseQueue=null,d=Ir(i,t,n),u.memoizedState=u.baseState=d),d}function _r(e,t,n){return Pr(Er(),e,t,n)}function Lr(e){var t=Sr();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e=(e=t.queue={pending:null,dispatch:null,lastRenderedReducer:xr,lastRenderedState:e}).dispatch=$r.bind(null,pr,e),[t.memoizedState,e]}function Ar(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=pr.updateQueue)?(t={lastEffect:null},pr.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Rr(e){return e={current:e},Sr().memoizedState=e}function zr(){return Er().memoizedState}function Or(e,t,n,r){var i=Sr();pr.flags|=e,i.memoizedState=Ar(1|t,n,void 0,void 0===r?null:r)}function Tr(e,t,n,r){var i=Er();r=void 0===r?null:r;var o=void 0;if(null!==hr){var a=hr.memoizedState;if(o=a.destroy,null!==r&&vr(r,a.deps))return void Ar(t,n,o,r)}pr.flags|=e,i.memoizedState=Ar(1|t,n,o,r)}function jr(e,t){return Or(516,4,e,t)}function Fr(e,t){return Tr(516,4,e,t)}function Hr(e,t){return Tr(4,2,e,t)}function Mr(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Dr(e,t,n){return n=null!=n?n.concat([e]):null,Tr(4,2,Mr.bind(null,t,e),n)}function Ur(){}function Wr(e,t){var n=Er();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&vr(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Br(e,t){var n=Er();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&vr(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Vr(e,t){var n=Xt();Zt(98>n?98:n,(function(){e(!0)})),Zt(97<n?97:n,(function(){var n=dr.transition;dr.transition=1;try{e(!1),t()}finally{dr.transition=n}}))}function $r(e,t,n){var r=Qo(),i=Ko(e),o={lane:i,action:n,eagerReducer:null,eagerState:null,next:null},a=t.pending;if(null===a?o.next=o:(o.next=a.next,a.next=o),t.pending=o,a=e.alternate,e===pr||null!==a&&a===pr)gr=yr=!0;else{if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var l=t.lastRenderedState,s=a(l,n);if(o.eagerReducer=a,o.eagerState=s,on(s,l))return}catch(e){}Go(e,i,r)}}var qr={readContext:vn,useCallback:br,useContext:br,useEffect:br,useImperativeHandle:br,useLayoutEffect:br,useMemo:br,useReducer:br,useRef:br,useState:br,useDebugValue:br,useDeferredValue:br,useTransition:br,useMutableSource:br,useOpaqueIdentifier:br,unstable_isNewReconciler:!1},Qr={readContext:vn,useCallback:function(e,t){return Sr().memoizedState=[e,void 0===t?null:t],e},useContext:vn,useEffect:jr,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,Or(4,2,Mr.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Or(4,2,e,t)},useMemo:function(e,t){var n=Sr();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Sr();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e=(e=r.queue={pending:null,dispatch:null,lastRenderedReducer:e,lastRenderedState:t}).dispatch=$r.bind(null,pr,e),[r.memoizedState,e]},useRef:Rr,useState:Lr,useDebugValue:Ur,useDeferredValue:function(e){var t=Lr(e),n=t[0],r=t[1];return jr((function(){var t=dr.transition;dr.transition=1;try{r(e)}finally{dr.transition=t}}),[e]),n},useTransition:function(){var e=Lr(!1),t=e[0];return Rr(e=Vr.bind(null,e[1])),[e,t]},useMutableSource:function(e,t,n){var r=Sr();return r.memoizedState={refs:{getSnapshot:t,setSnapshot:null},source:e,subscribe:n},Pr(r,e,t,n)},useOpaqueIdentifier:function(){if(tr){var e=!1,t=ee((function(){throw e||(e=!0,n(te())),Error(l(355))})),n=Lr(t)[1];return 0==(2&pr.mode)&&(pr.flags|=516,Ar(5,(function(){n(te())}),void 0,null)),t}return Lr(t=te()),t},unstable_isNewReconciler:!1},Kr={readContext:vn,useCallback:Wr,useContext:vn,useEffect:Fr,useImperativeHandle:Dr,useLayoutEffect:Hr,useMemo:Br,useReducer:Cr,useRef:zr,useState:function(){return Cr(xr)},useDebugValue:Ur,useDeferredValue:function(e){var t=Cr(xr),n=t[0],r=t[1];return Fr((function(){var t=dr.transition;dr.transition=1;try{r(e)}finally{dr.transition=t}}),[e]),n},useTransition:function(){var e=Cr(xr)[0];return[zr().current,e]},useMutableSource:_r,useOpaqueIdentifier:function(){return Cr(xr)[0]},unstable_isNewReconciler:!1},Gr={readContext:vn,useCallback:Wr,useContext:vn,useEffect:Fr,useImperativeHandle:Dr,useLayoutEffect:Hr,useMemo:Br,useReducer:Nr,useRef:zr,useState:function(){return Nr(xr)},useDebugValue:Ur,useDeferredValue:function(e){var t=Nr(xr),n=t[0],r=t[1];return Fr((function(){var t=dr.transition;dr.transition=1;try{r(e)}finally{dr.transition=t}}),[e]),n},useTransition:function(){var e=Nr(xr)[0];return[zr().current,e]},useMutableSource:_r,useOpaqueIdentifier:function(){return Nr(xr)[0]},unstable_isNewReconciler:!1},Yr=s.ReactCurrentOwner,Xr=!1;function Jr(e,t,n,r){t.child=null===e?Un(t,null,n,r):Dn(t,e.child,n,r)}function Zr(e,t,n,r,i){n=n.render;var o=t.ref;return bn(t,i),r=wr(e,t,n,r,o,i),null===e||Xr?(t.flags|=1,Jr(e,t,r,i),t.child):(kr(e,t,i),Ei(e,t,i))}function ei(e,t,n,r,i,o){if(null===e){var a=n.type;return"function"!=typeof a||ja(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Ha(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,ti(e,t,a,r,i,o))}return a=e.child,0==(i&o)&&(i=a.memoizedProps,(n=null!==(n=n.compare)?n:ln)(i,r)&&e.ref===t.ref)?Ei(e,t,o):(t.flags|=1,(e=Fa(a,r)).ref=t.ref,e.return=t,t.child=e)}function ti(e,t,n,r,i,o){if(null!==e&&ln(e.memoizedProps,r)&&e.ref===t.ref){if(Xr=!1,0==(o&i))return t.lanes=e.lanes,Ei(e,t,o);0!=(16384&e.flags)&&(Xr=!0)}return ii(e,t,n,r,o)}function ni(e,t,n){var r=t.pendingProps,i=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode||"unstable-defer-without-hiding"===r.mode)if(0==(4&t.mode))t.memoizedState={baseLanes:0},ra(t,n);else{if(0==(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e},ra(t,e),null;t.memoizedState={baseLanes:0},ra(t,null!==o?o.baseLanes:n)}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,ra(t,r);return Jr(e,t,i,n),t.child}function ri(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=128)}function ii(e,t,n,r,i){var o=ft(n)?ct:st.current;return o=dt(t,o),bn(t,i),n=wr(e,t,n,r,o,i),null===e||Xr?(t.flags|=1,Jr(e,t,n,i),t.child):(kr(e,t,i),Ei(e,t,i))}function oi(e,t,n,r,i){var o=n._render;return n=n._data,bn(t,i),r=wr(e,t,o,r,n,i),null===e||Xr?(t.flags|=1,Jr(e,t,r,i),t.child):(kr(e,t,i),Ei(e,t,i))}function ai(e,t,n,r,i){if(ft(n)){var o=!0;yt(t)}else o=!1;if(bn(t,i),null===t.stateNode)null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),Rn(t,n,r),On(t,n,r,i),r=!0;else if(null===e){var a=t.stateNode,l=t.memoizedProps;a.props=l;var s=a.context,u=n.contextType;"object"==typeof u&&null!==u?u=vn(u):u=dt(t,u=ft(n)?ct:st.current);var c=n.getDerivedStateFromProps,d="function"==typeof c||"function"==typeof a.getSnapshotBeforeUpdate;d||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(l!==r||s!==u)&&zn(t,a,r,u),wn=!1;var f=t.memoizedState;a.state=f,Nn(t,r,a,i),s=t.memoizedState,l!==r||f!==s||ut.current||wn?("function"==typeof c&&(_n(t,n,c,r),s=t.memoizedState),(l=wn||An(t,n,l,r,f,s,u))?(d||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||("function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"==typeof a.componentDidMount&&(t.flags|=4)):("function"==typeof a.componentDidMount&&(t.flags|=4),t.memoizedProps=r,t.memoizedState=s),a.props=r,a.state=s,a.context=u,r=l):("function"==typeof a.componentDidMount&&(t.flags|=4),r=!1)}else{a=t.stateNode,Sn(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:un(t.type,l),a.props=u,d=t.pendingProps,f=a.context,"object"==typeof(s=n.contextType)&&null!==s?s=vn(s):s=dt(t,s=ft(n)?ct:st.current);var p=n.getDerivedStateFromProps;(c="function"==typeof p||"function"==typeof a.getSnapshotBeforeUpdate)||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(l!==d||f!==s)&&zn(t,a,r,s),wn=!1,f=t.memoizedState,a.state=f,Nn(t,r,a,i);var h=t.memoizedState;l!==d||f!==h||ut.current||wn?("function"==typeof p&&(_n(t,n,p,r),h=t.memoizedState),(u=wn||An(t,n,u,r,f,h,s))?(c||"function"!=typeof a.UNSAFE_componentWillUpdate&&"function"!=typeof a.componentWillUpdate||("function"==typeof a.componentWillUpdate&&a.componentWillUpdate(r,h,s),"function"==typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,h,s)),"function"==typeof a.componentDidUpdate&&(t.flags|=4),"function"==typeof a.getSnapshotBeforeUpdate&&(t.flags|=256)):("function"!=typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=256),t.memoizedProps=r,t.memoizedState=h),a.props=r,a.state=h,a.context=s,r=u):("function"!=typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=256),r=!1)}return li(e,t,n,r,o,i)}function li(e,t,n,r,i,o){ri(e,t);var a=0!=(64&t.flags);if(!r&&!a)return i&&gt(t,n,!1),Ei(e,t,o);r=t.stateNode,Yr.current=t;var l=a&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&a?(t.child=Dn(t,e.child,null,o),t.child=Dn(t,null,l,o)):Jr(e,t,l,o),t.memoizedState=r.state,i&&gt(t,n,!0),t.child}function si(e){var t=e.stateNode;t.pendingContext?ht(0,t.pendingContext,t.pendingContext!==t.context):t.context&&ht(0,t.context,!1),Qn(e,t.containerInfo)}var ui,ci,di,fi,pi={dehydrated:null,retryLane:0};function hi(e,t,n){var r,i=t.pendingProps,o=Xn.current,a=!1,s=0!=(64&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!=(2&o)),r?(a=!0,t.flags&=-65):null!==e&&null===e.memoizedState||void 0===i.fallback||!0===i.unstable_avoidThisFallback||(o|=1),at(Xn,1&o),null===e){if(void 0!==i.fallback&&(ir(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)))return 0==(2&t.mode)?t.lanes=1:Ue(e)?t.lanes=256:t.lanes=1073741824,null;e=i.children;var u=i.fallback;return a?(e=yi(t,e,u,n),t.child.memoizedState={baseLanes:n},t.memoizedState=pi,e):"number"==typeof i.unstable_expectedLoadTime?(e=yi(t,e,u,n),t.child.memoizedState={baseLanes:n},t.memoizedState=pi,t.lanes=33554432,e):mi(t,e,n)}if(null!==(o=e.memoizedState)){if(null!==(r=o.dehydrated)){if(s)return null!==t.memoizedState?(t.child=e.child,t.flags|=64,null):(a=i.fallback,u=t.mode,i=Da(i.children,u,0,null),(a=Ma(a,u,n,null)).flags|=2,i.return=t,a.return=t,i.sibling=a,t.child=i,0!=(2&t.mode)&&Dn(t,e.child,null,n),t.child.memoizedState={baseLanes:n},t.memoizedState=pi,a);if(0!=(64&po)||0==(2&t.mode)||Ue(r))t=vi(e,t,n);else if(i=0!=(n&e.childLanes),Xr||i){if(null!==(i=ho)){switch(St(n),kt){case 15:case 14:u=0;break;case 13:case 12:u=4;break;case 11:case 10:u=32;break;case 9:case 8:u=256;break;case 7:case 6:case 5:u=4096;break;case 4:u=67108864;break;case 3:case 2:u=134217728;break;case 1:case 0:u=0;break;default:throw Error(l(360,u))}0!==(i=0!=(u&(i.suspendedLanes|n))?0:u)&&i!==o.retryLane&&(o.retryLane=i,Go(e,i,-1))}sa(),t=vi(e,t,n)}else De(r)?(t.flags|=64,t.child=e.child,t=Ca.bind(null,e),We(r,t),t=null):(J&&(er=Be(r),or(t),tr=!0),(t=mi(t,t.pendingProps.children,n)).flags|=1024);return t}return a?(i=bi(e,t,i.children,i.fallback,n),a=t.child,u=e.child.memoizedState,a.memoizedState=null===u?{baseLanes:n}:{baseLanes:u.baseLanes|n},a.childLanes=e.childLanes&~n,t.memoizedState=pi,i):(n=gi(e,t,i.children,n),t.memoizedState=null,n)}return a?(i=bi(e,t,i.children,i.fallback,n),a=t.child,u=e.child.memoizedState,a.memoizedState=null===u?{baseLanes:n}:{baseLanes:u.baseLanes|n},a.childLanes=e.childLanes&~n,t.memoizedState=pi,i):(n=gi(e,t,i.children,n),t.memoizedState=null,n)}function mi(e,t,n){return(t=Da({mode:"visible",children:t},e.mode,n,null)).return=e,e.child=t}function yi(e,t,n,r){var i=e.mode,o=e.child;return t={mode:"hidden",children:t},0==(2&i)&&null!==o?(o.childLanes=0,o.pendingProps=t):o=Da(t,i,0,null),n=Ma(n,i,r,null),o.return=e,n.return=e,o.sibling=n,e.child=o,n}function gi(e,t,n,r){var i=e.child;return e=i.sibling,n=Fa(i,{mode:"visible",children:n}),0==(2&t.mode)&&(n.lanes=r),n.return=t,n.sibling=null,null!==e&&(e.nextEffect=null,e.flags=8,t.firstEffect=t.lastEffect=e),t.child=n}function bi(e,t,n,r,i){var o=t.mode,a=e.child;e=a.sibling;var l={mode:"hidden",children:n};return 0==(2&o)&&t.child!==a?((n=t.child).childLanes=0,n.pendingProps=l,null!==(a=n.lastEffect)?(t.firstEffect=n.firstEffect,t.lastEffect=a,a.nextEffect=null):t.firstEffect=t.lastEffect=null):n=Fa(a,l),null!==e?r=Fa(e,r):(r=Ma(r,o,i,null)).flags|=2,r.return=t,n.return=t,n.sibling=r,t.child=n,r}function vi(e,t,n){return Dn(t,e.child,null,n),(e=mi(t,t.pendingProps.children,n)).flags|=2,t.memoizedState=null,e}function wi(e,t){e.lanes|=t;var n=e.alternate;null!==n&&(n.lanes|=t),gn(e.return,t)}function ki(e,t,n,r,i,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i,lastEffect:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=i,a.lastEffect=o)}function Si(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(Jr(e,t,r.children,n),0!=(2&(r=Xn.current)))r=1&r|2,t.flags|=64;else{if(null!==e&&0!=(64&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&wi(e,n);else if(19===e.tag)wi(e,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(at(Xn,r),0==(2&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===Jn(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),ki(t,!1,i,n,o,t.lastEffect);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===Jn(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}ki(t,!0,n,null,o,t.lastEffect);break;case"together":ki(t,!1,null,null,void 0,t.lastEffect);break;default:t.memoizedState=null}return t.child}function Ei(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),So|=t.lanes,0!=(n&t.childLanes)){if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Fa(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Fa(e,e.pendingProps)).return=t;n.sibling=null}return t.child}return null}function xi(e){e.flags|=4}if(Y)ui=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)U(e,n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},ci=function(){},di=function(e,t,n,r,i){if((e=e.memoizedProps)!==r){var o=t.stateNode,a=qn(Bn.current);n=B(o,n,e,r,i,a),(t.updateQueue=n)&&xi(t)}},fi=function(e,t,n,r){n!==r&&xi(t)};else if(X){ui=function(e,t,n,r){for(var i=t.child;null!==i;){if(5===i.tag){var o=i.stateNode;n&&r&&(o=Te(o,i.type,i.memoizedProps,i)),U(e,o)}else if(6===i.tag)o=i.stateNode,n&&r&&(o=je(o,i.memoizedProps,i)),U(e,o);else if(4!==i.tag){if(13===i.tag&&0!=(4&i.flags)&&(o=null!==i.memoizedState)){var a=i.child;if(null!==a&&(null!==a.child&&(a.child.return=a,ui(e,a,!0,o)),null!==(o=a.sibling))){o.return=i,i=o;continue}}if(null!==i.child){i.child.return=i,i=i.child;continue}}if(i===t)break;for(;null===i.sibling;){if(null===i.return||i.return===t)return;i=i.return}i.sibling.return=i.return,i=i.sibling}};var Ci=function(e,t,n,r){for(var i=t.child;null!==i;){if(5===i.tag){var o=i.stateNode;n&&r&&(o=Te(o,i.type,i.memoizedProps,i)),Re(e,o)}else if(6===i.tag)o=i.stateNode,n&&r&&(o=je(o,i.memoizedProps,i)),Re(e,o);else if(4!==i.tag){if(13===i.tag&&0!=(4&i.flags)&&(o=null!==i.memoizedState)){var a=i.child;if(null!==a&&(null!==a.child&&(a.child.return=a,Ci(e,a,!0,o)),null!==(o=a.sibling))){o.return=i,i=o;continue}}if(null!==i.child){i.child.return=i,i=i.child;continue}}if(i===t)break;for(;null===i.sibling;){if(null===i.return||i.return===t)return;i=i.return}i.sibling.return=i.return,i=i.sibling}};ci=function(e){var t=e.stateNode;if(null!==e.firstEffect){var n=t.containerInfo,r=Ae(n);Ci(r,e,!1,!1),t.pendingChildren=r,xi(e),ze(n,r)}},di=function(e,t,n,r,i){var o=e.stateNode,a=e.memoizedProps;if((e=null===t.firstEffect)&&a===r)t.stateNode=o;else{var l=t.stateNode,s=qn(Bn.current),u=null;a!==r&&(u=B(l,n,a,r,i,s)),e&&null===u?t.stateNode=o:(o=Le(o,u,n,a,r,t,e,l),W(o,n,r,i,s)&&xi(t),t.stateNode=o,e?xi(t):ui(o,t,!1,!1))}},fi=function(e,t,n,r){n!==r?(e=qn($n.current),n=qn(Bn.current),t.stateNode=$(r,e,n,t),xi(t)):t.stateNode=e.stateNode}}else ci=function(){},di=function(){},fi=function(){};function Ni(e,t){if(!tr)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ii(e,t,n){var r=t.pendingProps;switch(t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:return ft(t.type)&&pt(),null;case 3:return Kn(),ot(ut),ot(st),ur(),(r=t.stateNode).pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(ar(t)?xi(t):r.hydrate||(t.flags|=256)),ci(t),null;case 5:Yn(t);var i=qn($n.current);if(n=t.type,null!==e&&null!=t.stateNode)di(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=128);else{if(!r){if(null===t.stateNode)throw Error(l(166));return null}if(e=qn(Bn.current),ar(t)){if(!J)throw Error(l(175));e=$e(t.stateNode,t.type,t.memoizedProps,i,e,t),t.updateQueue=e,null!==e&&xi(t)}else{var o=D(n,r,i,e,t);ui(o,t,!1,!1),t.stateNode=o,W(o,n,r,i,e)&&xi(t)}null!==t.ref&&(t.flags|=128)}return null;case 6:if(e&&null!=t.stateNode)fi(e,t,e.memoizedProps,r);else{if("string"!=typeof r&&null===t.stateNode)throw Error(l(166));if(e=qn($n.current),i=qn(Bn.current),ar(t)){if(!J)throw Error(l(176));qe(t.stateNode,t.memoizedProps,t)&&xi(t)}else t.stateNode=$(r,e,i,t)}return null;case 13:if(ot(Xn),null!==(r=t.memoizedState)&&null!==r.dehydrated){if(null===e){if(!ar(t))throw Error(l(318));if(!J)throw Error(l(344));if(!(e=null!==(e=t.memoizedState)?e.dehydrated:null))throw Error(l(317));Qe(e,t)}else lr(),0==(64&t.flags)&&(t.memoizedState=null),t.flags|=4;return null}return 0!=(64&t.flags)?(t.lanes=n,t):(r=null!==r,i=!1,null===e?void 0!==t.memoizedProps.fallback&&ar(t):i=null!==e.memoizedState,r&&!i&&0!=(2&t.mode)&&(null===e&&!0!==t.memoizedProps.unstable_avoidThisFallback||0!=(1&Xn.current)?0===vo&&(vo=3):sa()),X&&r&&(t.flags|=4),Y&&(r||i)&&(t.flags|=4),null);case 4:return Kn(),ci(t),null===e&&ie(t.stateNode.containerInfo),null;case 10:return yn(t),null;case 17:return ft(t.type)&&pt(),null;case 19:if(ot(Xn),null===(r=t.memoizedState))return null;if(i=0!=(64&t.flags),null===(o=r.rendering))if(i)Ni(r,!1);else{if(0!==vo||null!==e&&0!=(64&e.flags))for(e=t.child;null!==e;){if(null!==(o=Jn(e))){for(t.flags|=64,Ni(r,!1),null!==(e=o.updateQueue)&&(t.updateQueue=e,t.flags|=4),null===r.lastEffect&&(t.firstEffect=null),t.lastEffect=r.lastEffect,e=n,r=t.child;null!==r;)n=e,(i=r).flags&=2,i.nextEffect=null,i.firstEffect=null,i.lastEffect=null,null===(o=i.alternate)?(i.childLanes=0,i.lanes=n,i.child=null,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,n=o.dependencies,i.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext}),r=r.sibling;return at(Xn,1&Xn.current|2),t.child}e=e.sibling}null!==r.tail&&Yt()>Io&&(t.flags|=64,i=!0,Ni(r,!1),t.lanes=33554432)}else{if(!i)if(null!==(e=Jn(o))){if(t.flags|=64,i=!0,null!==(e=e.updateQueue)&&(t.updateQueue=e,t.flags|=4),Ni(r,!0),null===r.tail&&"hidden"===r.tailMode&&!o.alternate&&!tr)return null!==(t=t.lastEffect=r.lastEffect)&&(t.nextEffect=null),null}else 2*Yt()-r.renderingStartTime>Io&&1073741824!==n&&(t.flags|=64,i=!0,Ni(r,!1),t.lanes=33554432);r.isBackwards?(o.sibling=t.child,t.child=o):(null!==(e=r.last)?e.sibling=o:t.child=o,r.last=o)}return null!==r.tail?(e=r.tail,r.rendering=e,r.tail=e.sibling,r.lastEffect=t.lastEffect,r.renderingStartTime=Yt(),e.sibling=null,t=Xn.current,at(Xn,i?1&t|2:1&t),e):null;case 22:return null;case 23:case 24:return ia(),null!==e&&null!==e.memoizedState!=(null!==t.memoizedState)&&"unstable-defer-without-hiding"!==r.mode&&(t.flags|=4),null}throw Error(l(156,t.tag))}function Pi(e){switch(e.tag){case 1:ft(e.type)&&pt();var t=e.flags;return 4096&t?(e.flags=-4097&t|64,e):null;case 3:if(Kn(),ot(ut),ot(st),ur(),0!=(64&(t=e.flags)))throw Error(l(285));return e.flags=-4097&t|64,e;case 5:return Yn(e),null;case 13:if(ot(Xn),null!==(t=e.memoizedState)&&null!==t.dehydrated){if(null===e.alternate)throw Error(l(340));lr()}return 4096&(t=e.flags)?(e.flags=-4097&t|64,e):null;case 19:return ot(Xn),null;case 4:return Kn(),null;case 10:return yn(e),null;case 23:case 24:return ia(),null;default:return null}}function _i(e,t){try{var n="",r=t;do{n+=sn(r),r=r.return}while(r);var i=n}catch(e){i="\nError generating stack: "+e.message+"\n"+e.stack}return{value:e,source:t,stack:i}}function Li(e,t){try{console.error(t.value)}catch(e){setTimeout((function(){throw e}))}}var Ai="function"==typeof WeakMap?WeakMap:Map;function Ri(e,t,n){(n=En(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ao||(Ao=!0,Ro=r),Li(0,t)},n}function zi(e,t,n){(n=En(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var i=t.value;n.payload=function(){return Li(0,t),r(i)}}var o=e.stateNode;return null!==o&&"function"==typeof o.componentDidCatch&&(n.callback=function(){"function"!=typeof r&&(null===zo?zo=new Set([this]):zo.add(this),Li(0,t));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}var Oi="function"==typeof WeakSet?WeakSet:Set;function Ti(e){var t=e.ref;if(null!==t)if("function"==typeof t)try{t(null)}catch(t){Sa(e,t)}else t.current=null}function ji(e,t){switch(t.tag){case 0:case 11:case 15:case 22:return;case 1:if(256&t.flags&&null!==e){var n=e.memoizedProps,r=e.memoizedState;t=(e=t.stateNode).getSnapshotBeforeUpdate(t.elementType===t.type?n:un(t.type,n),r),e.__reactInternalSnapshotBeforeUpdate=t}return;case 3:return void(Y&&256&t.flags&&Ie(t.stateNode.containerInfo));case 5:case 6:case 4:case 17:return}throw Error(l(163))}function Fi(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.destroy;n.destroy=void 0,void 0!==r&&r()}n=n.next}while(n!==t)}}function Hi(e,t,n){switch(n.tag){case 0:case 11:case 15:case 22:if(null!==(t=null!==(t=n.updateQueue)?t.lastEffect:null)){e=t=t.next;do{if(3==(3&e.tag)){var r=e.create;e.destroy=r()}e=e.next}while(e!==t)}if(null!==(t=null!==(t=n.updateQueue)?t.lastEffect:null)){e=t=t.next;do{var i=e;r=i.next,0!=(4&(i=i.tag))&&0!=(1&i)&&(va(n,e),ba(n,e)),e=r}while(e!==t)}return;case 1:return e=n.stateNode,4&n.flags&&(null===t?e.componentDidMount():(r=n.elementType===n.type?t.memoizedProps:un(n.type,t.memoizedProps),e.componentDidUpdate(r,t.memoizedState,e.__reactInternalSnapshotBeforeUpdate))),void(null!==(t=n.updateQueue)&&In(n,t,e));case 3:if(null!==(t=n.updateQueue)){if(e=null,null!==n.child)switch(n.child.tag){case 5:e=T(n.child.stateNode);break;case 1:e=n.child.stateNode}In(n,t,e)}return;case 5:return e=n.stateNode,void(null===t&&4&n.flags&&ye(e,n.type,n.memoizedProps,n));case 6:case 4:case 12:return;case 13:return void(J&&null===n.memoizedState&&(n=n.alternate,null!==n&&(n=n.memoizedState,null!==n&&(n=n.dehydrated,null!==n&&Ye(n)))));case 19:case 17:case 20:case 21:case 23:case 24:return}throw Error(l(163))}function Mi(e,t){if(Y)for(var n=e;;){if(5===n.tag){var r=n.stateNode;t?Ee(r):Ce(n.stateNode,n.memoizedProps)}else if(6===n.tag)r=n.stateNode,t?xe(r):Ne(r,n.memoizedProps);else if((23!==n.tag&&24!==n.tag||null===n.memoizedState||n===e)&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}}function Di(e,t){if(vt&&"function"==typeof vt.onCommitFiberUnmount)try{vt.onCommitFiberUnmount(bt,t)}catch(e){}switch(t.tag){case 0:case 11:case 14:case 15:case 22:if(null!==(e=t.updateQueue)&&null!==(e=e.lastEffect)){var n=e=e.next;do{var r=n,i=r.destroy;if(r=r.tag,void 0!==i)if(0!=(4&r))va(t,n);else{r=t;try{i()}catch(e){Sa(r,e)}}n=n.next}while(n!==e)}break;case 1:if(Ti(t),"function"==typeof(e=t.stateNode).componentWillUnmount)try{e.props=t.memoizedProps,e.state=t.memoizedState,e.componentWillUnmount()}catch(e){Sa(t,e)}break;case 5:Ti(t);break;case 4:Y?$i(e,t):X&&X&&(t=t.stateNode.containerInfo,e=Ae(t),Oe(t,e))}}function Ui(e,t){for(var n=t;;)if(Di(e,n),null===n.child||Y&&4===n.tag){if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}else n.child.return=n,n=n.child}function Wi(e){e.alternate=null,e.child=null,e.dependencies=null,e.firstEffect=null,e.lastEffect=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.return=null,e.updateQueue=null}function Bi(e){return 5===e.tag||3===e.tag||4===e.tag}function Vi(e){if(Y){e:{for(var t=e.return;null!==t;){if(Bi(t))break e;t=t.return}throw Error(l(160))}var n=t;switch(t=n.stateNode,n.tag){case 5:var r=!1;break;case 3:case 4:t=t.containerInfo,r=!0;break;default:throw Error(l(161))}16&n.flags&&(Se(t),n.flags&=-17);e:t:for(n=e;;){for(;null===n.sibling;){if(null===n.return||Bi(n.return)){n=null;break e}n=n.return}for(n.sibling.return=n.return,n=n.sibling;5!==n.tag&&6!==n.tag&&18!==n.tag;){if(2&n.flags)continue t;if(null===n.child||4===n.tag)continue t;n.child.return=n,n=n.child}if(!(2&n.flags)){n=n.stateNode;break e}}r?function e(t,n,r){var i=t.tag,o=5===i||6===i;if(o)t=o?t.stateNode:t.stateNode.instance,n?ve(r,t,n):he(r,t);else if(4!==i&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t):function e(t,n,r){var i=t.tag,o=5===i||6===i;if(o)t=o?t.stateNode:t.stateNode.instance,n?be(r,t,n):pe(r,t);else if(4!==i&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t)}}function $i(e,t){for(var n,r,i=t,o=!1;;){if(!o){o=i.return;e:for(;;){if(null===o)throw Error(l(160));switch(n=o.stateNode,o.tag){case 5:r=!1;break e;case 3:case 4:n=n.containerInfo,r=!0;break e}o=o.return}o=!0}if(5===i.tag||6===i.tag)Ui(e,i),r?ke(n,i.stateNode):we(n,i.stateNode);else if(18===i.tag)r?Je(n,i.stateNode):Xe(n,i.stateNode);else if(4===i.tag){if(null!==i.child){n=i.stateNode.containerInfo,r=!0,i.child.return=i,i=i.child;continue}}else if(Di(e,i),null!==i.child){i.child.return=i,i=i.child;continue}if(i===t)break;for(;null===i.sibling;){if(null===i.return||i.return===t)return;4===(i=i.return).tag&&(o=!1)}i.sibling.return=i.return,i=i.sibling}}function qi(e,t){if(Y){switch(t.tag){case 0:case 11:case 14:case 15:case 22:return void Fi(3,t);case 1:return;case 5:var n=t.stateNode;if(null!=n){var r=t.memoizedProps;e=null!==e?e.memoizedProps:r;var i=t.type,o=t.updateQueue;t.updateQueue=null,null!==o&&ge(n,o,i,e,r,t)}return;case 6:if(null===t.stateNode)throw Error(l(162));return n=t.memoizedProps,void me(t.stateNode,null!==e?e.memoizedProps:n,n);case 3:return void(J&&(t=t.stateNode,t.hydrate&&(t.hydrate=!1,Ge(t.containerInfo))));case 12:return;case 13:return Qi(t),void Ki(t);case 19:return void Ki(t);case 17:return;case 23:case 24:return void Mi(t,null!==t.memoizedState)}throw Error(l(163))}switch(t.tag){case 0:case 11:case 14:case 15:case 22:return void Fi(3,t);case 12:return;case 13:return Qi(t),void Ki(t);case 19:return void Ki(t);case 3:J&&((n=t.stateNode).hydrate&&(n.hydrate=!1,Ge(n.containerInfo)));break;case 23:case 24:return}e:if(X){switch(t.tag){case 1:case 5:case 6:case 20:break e;case 3:case 4:t=t.stateNode,Oe(t.containerInfo,t.pendingChildren);break e}throw Error(l(163))}}function Qi(e){null!==e.memoizedState&&(No=Yt(),Y&&Mi(e.child,!0))}function Ki(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Oi),t.forEach((function(t){var r=Na.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function Gi(e,t){return null!==e&&(null===(e=e.memoizedState)||null!==e.dehydrated)&&(null!==(t=t.memoizedState)&&null===t.dehydrated)}var Yi=0,Xi=1,Ji=2,Zi=3,eo=4;if("function"==typeof Symbol&&Symbol.for){var to=Symbol.for;Yi=to("selector.component"),Xi=to("selector.has_pseudo_class"),Ji=to("selector.role"),Zi=to("selector.test_id"),eo=to("selector.text")}function no(e){var t=Z(e);if(null!=t){if("string"!=typeof t.memoizedProps["data-testname"])throw Error(l(364));return t}if(null===(e=ae(e)))throw Error(l(362));return e.stateNode.current}function ro(e,t){switch(t.$$typeof){case Yi:if(e.type===t.value)return!0;break;case Xi:e:{t=t.value,e=[e,0];for(var n=0;n<e.length;){var r=e[n++],i=e[n++],o=t[i];if(5!==r.tag||!ue(r)){for(;null!=o&&ro(r,o);)o=t[++i];if(i===t.length){t=!0;break e}for(r=r.child;null!==r;)e.push(r,i),r=r.sibling}}t=!1}return t;case Ji:if(5===e.tag&&ce(e.stateNode,t.value))return!0;break;case eo:if((5===e.tag||6===e.tag)&&(null!==(e=se(e))&&0<=e.indexOf(t.value)))return!0;break;case Zi:if(5===e.tag&&("string"==typeof(e=e.memoizedProps["data-testname"])&&e.toLowerCase()===t.value.toLowerCase()))return!0;break;default:throw Error(l(365,t))}return!1}function io(e){switch(e.$$typeof){case Yi:return"<"+(P(e.value)||"Unknown")+">";case Xi:return":has("+(io(e)||"")+")";case Ji:return'[role="'+e.value+'"]';case eo:return'"'+e.value+'"';case Zi:return'[data-testname="'+e.value+'"]';default:throw Error(l(365,e))}}function oo(e,t){var n=[];e=[e,0];for(var r=0;r<e.length;){var i=e[r++],o=e[r++],a=t[o];if(5!==i.tag||!ue(i)){for(;null!=a&&ro(i,a);)a=t[++o];if(o===t.length)n.push(i);else for(i=i.child;null!==i;)e.push(i,o),i=i.sibling}}return n}function ao(e,t){if(!oe)throw Error(l(363));e=oo(e=no(e),t),t=[],e=Array.from(e);for(var n=0;n<e.length;){var r=e[n++];if(5===r.tag)ue(r)||t.push(r.stateNode);else for(r=r.child;null!==r;)e.push(r),r=r.sibling}return t}var lo=null;var so=Math.ceil,uo=s.ReactCurrentDispatcher,co=s.ReactCurrentOwner,fo=s.IsSomeRendererActing,po=0,ho=null,mo=null,yo=0,go=0,bo=it(0),vo=0,wo=null,ko=0,So=0,Eo=0,xo=0,Co=null,No=0,Io=1/0;function Po(){Io=Yt()+500}var _o,Lo=null,Ao=!1,Ro=null,zo=null,Oo=!1,To=null,jo=90,Fo=[],Ho=[],Mo=null,Do=0,Uo=null,Wo=-1,Bo=0,Vo=0,$o=null,qo=!1;function Qo(){return 0!=(48&po)?Yt():-1!==Wo?Wo:Wo=Yt()}function Ko(e){if(0==(2&(e=e.mode)))return 1;if(0==(4&e))return 99===Xt()?1:2;if(0===Bo&&(Bo=ko),0!==rn.transition){0!==Vo&&(Vo=null!==Co?Co.pendingLanes:0),e=Bo;var t=4186112&~Vo;return 0===(t&=-t)&&(0===(t=(e=4186112&~e)&-e)&&(t=8192)),t}return e=Xt(),0!=(4&po)&&98===e?e=Ct(12,Bo):e=Ct(e=function(e){switch(e){case 99:return 15;case 98:return 10;case 97:case 96:return 8;case 95:return 2;default:return 0}}(e),Bo),e}function Go(e,t,n){if(50<Do)throw Do=0,Uo=null,Error(l(185));if(null===(e=Yo(e,t)))return null;Pt(e,t,n),e===ho&&(Eo|=t,4===vo&&Zo(e,yo));var r=Xt();1===t?0!=(8&po)&&0==(48&po)?ea(e):(Xo(e,n),0===po&&(Po(),tn())):(0==(4&po)||98!==r&&99!==r||(null===Mo?Mo=new Set([e]):Mo.add(e)),Xo(e,n)),Co=e}function Yo(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}function Xo(e,t){for(var n=e.callbackNode,r=e.suspendedLanes,i=e.pingedLanes,o=e.expirationTimes,a=e.pendingLanes;0<a;){var s=31-_t(a),u=1<<s,c=o[s];if(-1===c){if(0==(u&r)||0!=(u&i)){c=t,St(u);var d=kt;o[s]=10<=d?c+250:6<=d?c+5e3:-1}}else c<=t&&(e.expiredLanes|=u);a&=~u}if(r=Et(e,e===ho?yo:0),t=kt,0===r)null!==n&&(n!==Vt&&Ot(n),e.callbackNode=null,e.callbackPriority=0);else{if(null!==n){if(e.callbackPriority===t)return;n!==Vt&&Ot(n)}15===t?(n=ea.bind(null,e),null===qt?(qt=[n],Qt=zt(Mt,nn)):qt.push(n),n=Vt):14===t?n=en(99,ea.bind(null,e)):n=en(n=function(e){switch(e){case 15:case 14:return 99;case 13:case 12:case 11:case 10:return 98;case 9:case 8:case 7:case 6:case 4:case 5:return 97;case 3:case 2:case 1:return 95;case 0:return 90;default:throw Error(l(358,e))}}(t),Jo.bind(null,e)),e.callbackPriority=t,e.callbackNode=n}}function Jo(e){if(Wo=-1,Vo=Bo=0,0!=(48&po))throw Error(l(327));var t=e.callbackNode;if(ga()&&e.callbackNode!==t)return null;var n=Et(e,e===ho?yo:0);if(0===n)return null;var r=n,i=po;po|=16;var o=la();for(ho===e&&yo===r||(Po(),oa(e,r));;)try{da();break}catch(t){aa(e,t)}if(hn(),uo.current=o,po=i,null!==mo?r=0:(ho=null,yo=0,r=vo),0!=(ko&Eo))oa(e,0);else if(0!==r){if(2===r&&(po|=64,e.hydrate&&(e.hydrate=!1,Ie(e.containerInfo)),0!==(n=xt(e))&&(r=ua(e,n))),1===r)throw t=wo,oa(e,0),Zo(e,n),Xo(e,Yt()),t;switch(e.finishedWork=e.current.alternate,e.finishedLanes=n,r){case 0:case 1:throw Error(l(345));case 2:ha(e);break;case 3:if(Zo(e,n),(62914560&n)===n&&10<(r=No+500-Yt())){if(0!==Et(e,0))break;if(((i=e.suspendedLanes)&n)!==n){Qo(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=q(ha.bind(null,e),r);break}ha(e);break;case 4:if(Zo(e,n),(4186112&n)===n)break;for(r=e.eventTimes,i=-1;0<n;){var a=31-_t(n);o=1<<a,(a=r[a])>i&&(i=a),n&=~o}if(n=i,10<(n=(120>(n=Yt()-n)?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*so(n/1960))-n)){e.timeoutHandle=q(ha.bind(null,e),n);break}ha(e);break;case 5:ha(e);break;default:throw Error(l(329))}}return Xo(e,Yt()),e.callbackNode===t?Jo.bind(null,e):null}function Zo(e,t){for(t&=~xo,t&=~Eo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-_t(t),r=1<<n;e[n]=-1,t&=~r}}function ea(e){if(0!=(48&po))throw Error(l(327));if(ga(),e===ho&&0!=(e.expiredLanes&yo)){var t=yo,n=ua(e,t);0!=(ko&Eo)&&(n=ua(e,t=Et(e,t)))}else n=ua(e,t=Et(e,0));if(0!==e.tag&&2===n&&(po|=64,e.hydrate&&(e.hydrate=!1,Ie(e.containerInfo)),0!==(t=xt(e))&&(n=ua(e,t))),1===n)throw n=wo,oa(e,0),Zo(e,t),Xo(e,Yt()),n;return e.finishedWork=e.current.alternate,e.finishedLanes=t,ha(e),Xo(e,Yt()),null}function ta(e,t){var n=po;po|=1;try{return e(t)}finally{0===(po=n)&&(Po(),tn())}}function na(e,t){var n=po;if(0!=(48&n))return e(t);po|=1;try{if(e)return Zt(99,e.bind(null,t))}finally{po=n,tn()}}function ra(e,t){at(bo,go),go|=t,ko|=t}function ia(){go=bo.current,ot(bo)}function oa(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==K&&(e.timeoutHandle=K,Q(n)),null!==mo)for(n=mo.return;null!==n;){var r=n;switch(r.tag){case 1:null!=(r=r.type.childContextTypes)&&pt();break;case 3:Kn(),ot(ut),ot(st),ur();break;case 5:Yn(r);break;case 4:Kn();break;case 13:case 19:ot(Xn);break;case 10:yn(r);break;case 23:case 24:ia()}n=n.return}ho=e,mo=Fa(e.current,null),yo=go=ko=t,vo=0,wo=null,xo=Eo=So=0}function aa(e,t){for(;;){var n=mo;try{if(hn(),cr.current=qr,yr){for(var r=pr.memoizedState;null!==r;){var i=r.queue;null!==i&&(i.pending=null),r=r.next}yr=!1}if(fr=0,mr=hr=pr=null,gr=!1,co.current=null,null===n||null===n.return){vo=1,wo=t,mo=null;break}e:{var o=e,a=n.return,l=n,s=t;if(t=yo,l.flags|=2048,l.firstEffect=l.lastEffect=null,null!==s&&"object"==typeof s&&"function"==typeof s.then){var u=s;if(0==(2&l.mode)){var c=l.alternate;c?(l.updateQueue=c.updateQueue,l.memoizedState=c.memoizedState,l.lanes=c.lanes):(l.updateQueue=null,l.memoizedState=null)}var d=0!=(1&Xn.current),f=a;do{var p;if(p=13===f.tag){var h=f.memoizedState;if(null!==h)p=null!==h.dehydrated;else{var m=f.memoizedProps;p=void 0!==m.fallback&&(!0!==m.unstable_avoidThisFallback||!d)}}if(p){var y=f.updateQueue;if(null===y){var g=new Set;g.add(u),f.updateQueue=g}else y.add(u);if(0==(2&f.mode)){if(f.flags|=64,l.flags|=16384,l.flags&=-2981,1===l.tag)if(null===l.alternate)l.tag=17;else{var b=En(-1,1);b.tag=2,xn(l,b)}l.lanes|=1;break e}s=void 0,l=t;var v=o.pingCache;if(null===v?(v=o.pingCache=new Ai,s=new Set,v.set(u,s)):void 0===(s=v.get(u))&&(s=new Set,v.set(u,s)),!s.has(l)){s.add(l);var w=Ea.bind(null,o,u,l);u.then(w,w)}f.flags|=4096,f.lanes=t;break e}f=f.return}while(null!==f);s=Error((P(l.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display.")}5!==vo&&(vo=2),s=_i(s,l),f=a;do{switch(f.tag){case 3:o=s,f.flags|=4096,t&=-t,f.lanes|=t,Cn(f,Ri(0,o,t));break e;case 1:o=s;var k=f.type,S=f.stateNode;if(0==(64&f.flags)&&("function"==typeof k.getDerivedStateFromError||null!==S&&"function"==typeof S.componentDidCatch&&(null===zo||!zo.has(S)))){f.flags|=4096,t&=-t,f.lanes|=t,Cn(f,zi(f,o,t));break e}}f=f.return}while(null!==f)}pa(n)}catch(e){t=e,mo===n&&null!==n&&(mo=n=n.return);continue}break}}function la(){var e=uo.current;return uo.current=qr,null===e?qr:e}function sa(){0!==vo&&3!==vo||(vo=4),null===ho||0==(134217727&So)&&0==(134217727&Eo)||Zo(ho,yo)}function ua(e,t){var n=po;po|=16;var r=la();for(ho===e&&yo===t||oa(e,t);;)try{ca();break}catch(t){aa(e,t)}if(hn(),po=n,uo.current=r,null!==mo)throw Error(l(261));return ho=null,yo=0,vo}function ca(){for(;null!==mo;)fa(mo)}function da(){for(;null!==mo&&!Tt();)fa(mo)}function fa(e){var t=_o(e.alternate,e,go);e.memoizedProps=e.pendingProps,null===t?pa(e):mo=t,co.current=null}function pa(e){var t=e;do{var n=t.alternate;if(e=t.return,0==(2048&t.flags)){if(null!==(n=Ii(n,t,go)))return void(mo=n);if(24!==(n=t).tag&&23!==n.tag||null===n.memoizedState||0!=(1073741824&go)||0==(4&n.mode)){for(var r=0,i=n.child;null!==i;)r|=i.lanes|i.childLanes,i=i.sibling;n.childLanes=r}null!==e&&0==(2048&e.flags)&&(null===e.firstEffect&&(e.firstEffect=t.firstEffect),null!==t.lastEffect&&(null!==e.lastEffect&&(e.lastEffect.nextEffect=t.firstEffect),e.lastEffect=t.lastEffect),1<t.flags&&(null!==e.lastEffect?e.lastEffect.nextEffect=t:e.firstEffect=t,e.lastEffect=t))}else{if(null!==(n=Pi(t)))return n.flags&=2047,void(mo=n);null!==e&&(e.firstEffect=e.lastEffect=null,e.flags|=2048)}if(null!==(t=t.sibling))return void(mo=t);mo=t=e}while(null!==t);0===vo&&(vo=5)}function ha(e){var t=Xt();return Zt(99,ma.bind(null,e,t)),null}function ma(e,t){do{ga()}while(null!==To);if(0!=(48&po))throw Error(l(327));var n=e.finishedWork;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(l(177));e.callbackNode=null;var r=n.lanes|n.childLanes,i=r,o=e.pendingLanes&~i;e.pendingLanes=i,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=i,e.mutableReadLanes&=i,e.entangledLanes&=i,i=e.entanglements;for(var a=e.eventTimes,s=e.expirationTimes;0<o;){var u=31-_t(o),c=1<<u;i[u]=0,a[u]=-1,s[u]=-1,o&=~c}if(null!==Mo&&0==(24&r)&&Mo.has(e)&&Mo.delete(e),e===ho&&(mo=ho=null,yo=0),1<n.flags?null!==n.lastEffect?(n.lastEffect.nextEffect=n,r=n.firstEffect):r=n:r=n.firstEffect,null!==r){i=po,po|=32,co.current=null,$o=H(e.containerInfo),qo=!1,Lo=r;do{try{ya()}catch(e){if(null===Lo)throw Error(l(330));Sa(Lo,e),Lo=Lo.nextEffect}}while(null!==Lo);$o=null,Lo=r;do{try{for(a=e,"function"==typeof Pe&&Pe();null!==Lo;){var d=Lo.flags;if(16&d&&Y&&Se(Lo.stateNode),128&d){var f=Lo.alternate;if(null!==f){var p=f.ref;null!==p&&("function"==typeof p?p(null):p.current=null)}}switch(1038&d){case 2:Vi(Lo),Lo.flags&=-3;break;case 6:Vi(Lo),Lo.flags&=-3,qi(Lo.alternate,Lo);break;case 1024:Lo.flags&=-1025;break;case 1028:Lo.flags&=-1025,qi(Lo.alternate,Lo);break;case 4:qi(Lo.alternate,Lo);break;case 8:s=a,o=Lo,Y?$i(s,o):Ui(s,o);var h=o.alternate;Wi(o),null!==h&&Wi(h)}Lo=Lo.nextEffect}"function"==typeof _e&&_e()}catch(e){if(null===Lo)throw Error(l(330));Sa(Lo,e),Lo=Lo.nextEffect}}while(null!==Lo);qo&&re(),M(e.containerInfo),e.current=n,Lo=r;do{try{for(d=e;null!==Lo;){var m=Lo.flags;if(36&m&&Hi(d,Lo.alternate,Lo),128&m){f=void 0;var y=Lo.ref;if(null!==y){var g=Lo.stateNode;switch(Lo.tag){case 5:f=T(g);break;default:f=g}"function"==typeof y?y(f):y.current=f}}Lo=Lo.nextEffect}}catch(e){if(null===Lo)throw Error(l(330));Sa(Lo,e),Lo=Lo.nextEffect}}while(null!==Lo);Lo=null,$t(),po=i}else e.current=n;if(Oo)Oo=!1,To=e,jo=t;else for(Lo=r;null!==Lo;)t=Lo.nextEffect,Lo.nextEffect=null,8&Lo.flags&&((m=Lo).sibling=null,m.stateNode=null),Lo=t;if(0===(r=e.pendingLanes)&&(zo=null),1===r?e===Uo?Do++:(Do=0,Uo=e):Do=0,n=n.stateNode,vt&&"function"==typeof vt.onCommitFiberRoot)try{vt.onCommitFiberRoot(bt,n,void 0,64==(64&n.current.flags))}catch(e){}if(Xo(e,Yt()),Ao)throw Ao=!1,e=Ro,Ro=null,e;return 0!=(8&po)||tn(),null}function ya(){for(;null!==Lo;){var e=Lo.alternate;qo||null===$o||(0!=(8&Lo.flags)?z(Lo,$o)&&(qo=!0,ne()):13===Lo.tag&&Gi(e,Lo)&&z(Lo,$o)&&(qo=!0,ne()));var t=Lo.flags;0!=(256&t)&&ji(e,Lo),0==(512&t)||Oo||(Oo=!0,en(97,(function(){return ga(),null}))),Lo=Lo.nextEffect}}function ga(){if(90!==jo){var e=97<jo?97:jo;return jo=90,Zt(e,wa)}return!1}function ba(e,t){Fo.push(t,e),Oo||(Oo=!0,en(97,(function(){return ga(),null})))}function va(e,t){Ho.push(t,e),Oo||(Oo=!0,en(97,(function(){return ga(),null})))}function wa(){if(null===To)return!1;var e=To;if(To=null,0!=(48&po))throw Error(l(331));var t=po;po|=32;var n=Ho;Ho=[];for(var r=0;r<n.length;r+=2){var i=n[r],o=n[r+1],a=i.destroy;if(i.destroy=void 0,"function"==typeof a)try{a()}catch(e){if(null===o)throw Error(l(330));Sa(o,e)}}for(n=Fo,Fo=[],r=0;r<n.length;r+=2){i=n[r],o=n[r+1];try{var s=i.create;i.destroy=s()}catch(e){if(null===o)throw Error(l(330));Sa(o,e)}}for(s=e.current.firstEffect;null!==s;)e=s.nextEffect,s.nextEffect=null,8&s.flags&&(s.sibling=null,s.stateNode=null),s=e;return po=t,tn(),!0}function ka(e,t,n){xn(e,t=Ri(0,t=_i(n,t),1)),t=Qo(),null!==(e=Yo(e,1))&&(Pt(e,1,t),Xo(e,t))}function Sa(e,t){if(3===e.tag)ka(e,e,t);else for(var n=e.return;null!==n;){if(3===n.tag){ka(n,e,t);break}if(1===n.tag){var r=n.stateNode;if("function"==typeof n.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===zo||!zo.has(r))){var i=zi(n,e=_i(t,e),1);if(xn(n,i),i=Qo(),null!==(n=Yo(n,1)))Pt(n,1,i),Xo(n,i);else if("function"==typeof r.componentDidCatch&&(null===zo||!zo.has(r)))try{r.componentDidCatch(t,e)}catch(e){}break}}n=n.return}}function Ea(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=Qo(),e.pingedLanes|=e.suspendedLanes&n,ho===e&&(yo&n)===n&&(4===vo||3===vo&&(62914560&yo)===yo&&500>Yt()-No?oa(e,0):xo|=n),Xo(e,t)}function xa(e,t){0===t&&(0==(2&(t=e.mode))?t=1:0==(4&t)?t=99===Xt()?1:2:(0===Bo&&(Bo=ko),0===(t=Nt(62914560&~Bo))&&(t=4194304)));var n=Qo();null!==(e=Yo(e,t))&&(Pt(e,t,n),Xo(e,n))}function Ca(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),xa(e,n)}function Na(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;null!==i&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(l(314))}null!==r&&r.delete(t),xa(e,n)}_o=function(e,t,n){var r=t.lanes;if(null!==e)if(e.memoizedProps!==t.pendingProps||ut.current)Xr=!0;else{if(0==(n&r)){switch(Xr=!1,t.tag){case 3:si(t),lr();break;case 5:Gn(t);break;case 1:ft(t.type)&&yt(t);break;case 4:Qn(t,t.stateNode.containerInfo);break;case 10:mn(t,t.memoizedProps.value);break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(at(Xn,1&Xn.current),t.flags|=64,null):0!=(n&t.child.childLanes)?hi(e,t,n):(at(Xn,1&Xn.current),null!==(t=Ei(e,t,n))?t.sibling:null);at(Xn,1&Xn.current);break;case 19:if(r=0!=(n&t.childLanes),0!=(64&e.flags)){if(r)return Si(e,t,n);t.flags|=64}var i=t.memoizedState;if(null!==i&&(i.rendering=null,i.tail=null,i.lastEffect=null),at(Xn,Xn.current),r)break;return null;case 23:case 24:return t.lanes=0,ni(e,t,n)}return Ei(e,t,n)}Xr=0!=(16384&e.flags)}else Xr=!1;switch(t.lanes=0,t.tag){case 2:if(r=t.type,null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps,i=dt(t,st.current),bn(t,n),i=wr(null,t,r,e,i,n),t.flags|=1,"object"==typeof i&&null!==i&&"function"==typeof i.render&&void 0===i.$$typeof){if(t.tag=1,t.memoizedState=null,t.updateQueue=null,ft(r)){var o=!0;yt(t)}else o=!1;t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,kn(t);var a=r.getDerivedStateFromProps;"function"==typeof a&&_n(t,r,a,e),i.updater=Ln,t.stateNode=i,i._reactInternals=t,On(t,r,e,n),t=li(null,t,r,!0,o,n)}else t.tag=0,Jr(null,t,i,n),t=t.child;return t;case 16:i=t.elementType;e:{switch(null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps,i=(o=i._init)(i._payload),t.type=i,o=t.tag=function(e){if("function"==typeof e)return ja(e)?1:0;if(null!=e){if((e=e.$$typeof)===y)return 11;if(e===v)return 14;if(e===k)return 22}return 2}(i),a=un(i,e),o){case 0:t=ii(null,t,i,a,n);break e;case 1:t=ai(null,t,i,a,n);break e;case 11:t=Zr(null,t,i,a,n);break e;case 14:t=ei(null,t,i,un(i.type,a),r,n);break e;case 22:t=oi(null,t,i,e,n);break e}throw Error(l(306,i,""))}return t;case 0:return r=t.type,i=t.pendingProps,ii(e,t,r,i=t.elementType===r?i:un(r,i),n);case 1:return r=t.type,i=t.pendingProps,ai(e,t,r,i=t.elementType===r?i:un(r,i),n);case 3:if(si(t),r=t.updateQueue,null===e||null===r)throw Error(l(282));if(r=t.pendingProps,i=null!==(i=t.memoizedState)?i.element:null,Sn(e,t),Nn(t,r,null,n),(r=t.memoizedState.element)===i)lr(),t=Ei(e,t,n);else{if((o=(i=t.stateNode).hydrate)&&(J?(er=Ve(t.stateNode.containerInfo),Zn=t,o=tr=!0):o=!1),o){if(J&&null!=(e=i.mutableSourceEagerHydrationData))for(i=0;i<e.length;i+=2)o=e[i],a=e[i+1],G?o._workInProgressVersionPrimary=a:o._workInProgressVersionSecondary=a,sr.push(o);for(n=Un(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|1024,n=n.sibling}else Jr(e,t,r,n),lr();t=t.child}return t;case 5:return Gn(t),null===e&&ir(t),r=t.type,i=t.pendingProps,o=null!==e?e.memoizedProps:null,a=i.children,V(r,i)?a=null:null!==o&&V(r,o)&&(t.flags|=16),ri(e,t),Jr(e,t,a,n),t.child;case 6:return null===e&&ir(t),null;case 13:return hi(e,t,n);case 4:return Qn(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Dn(t,null,r,n):Jr(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,Zr(e,t,r,i=t.elementType===r?i:un(r,i),n);case 7:return Jr(e,t,t.pendingProps,n),t.child;case 8:case 12:return Jr(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,a=t.memoizedProps,mn(t,o=i.value),null!==a){var s=a.value;if(0===(o=on(s,o)?0:0|("function"==typeof r._calculateChangedBits?r._calculateChangedBits(s,o):1073741823))){if(a.children===i.children&&!ut.current){t=Ei(e,t,n);break e}}else for(null!==(a=t.child)&&(a.return=t);null!==a;){var u=a.dependencies;if(null!==u){s=a.child;for(var c=u.firstContext;null!==c;){if(c.context===r&&0!=(c.observedBits&o)){1===a.tag&&((c=En(-1,n&-n)).tag=2,xn(a,c)),a.lanes|=n,null!==(c=a.alternate)&&(c.lanes|=n),gn(a.return,n),u.lanes|=n;break}c=c.next}}else if(10===a.tag)s=a.type===t.type?null:a.child;else if(18===a.tag){if(null===(s=a.return))throw Error(l(341));s.lanes|=n,null!==(u=s.alternate)&&(u.lanes|=n),gn(s,n),s=a.sibling}else s=a.child;if(null!==s)s.return=a;else for(s=a;null!==s;){if(s===t){s=null;break}if(null!==(a=s.sibling)){a.return=s.return,s=a;break}s=s.return}a=s}}Jr(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=(o=t.pendingProps).children,bn(t,n),r=r(i=vn(i,o.unstable_observedBits)),t.flags|=1,Jr(e,t,r,n),t.child;case 14:return o=un(i=t.type,t.pendingProps),ei(e,t,i,o=un(i.type,o),r,n);case 15:return ti(e,t,t.type,t.pendingProps,r,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:un(r,i),null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),t.tag=1,ft(r)?(e=!0,yt(t)):e=!1,bn(t,n),Rn(t,r,i),On(t,r,i,n),li(null,t,r,!0,e,n);case 19:return Si(e,t,n);case 22:return oi(e,t,t.type,t.pendingProps,n);case 23:case 24:return ni(e,t,n)}throw Error(l(156,t.tag))};var Ia={current:!1},Pa=a.unstable_flushAllWithoutAsserting,_a="function"==typeof Pa;function La(){if(void 0!==Pa)return Pa();for(var e=!1;ga();)e=!0;return e}function Aa(t){try{La(),function(t){if(null===lo)try{var n=("require"+Math.random()).slice(0,7);lo=(e&&e[n]).call(e,"timers").setImmediate}catch(e){lo=function(e){var t=new MessageChannel;t.port1.onmessage=e,t.port2.postMessage(void 0)}}lo(t)}((function(){La()?Aa(t):t()}))}catch(e){t(e)}}var Ra=0,za=!1;function Oa(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.flags=0,this.lastEffect=this.firstEffect=this.nextEffect=null,this.childLanes=this.lanes=0,this.alternate=null}function Ta(e,t,n,r){return new Oa(e,t,n,r)}function ja(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Fa(e,t){var n=e.alternate;return null===n?((n=Ta(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null),n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ha(e,t,n,r,i,o){var a=2;if(r=e,"function"==typeof e)ja(e)&&(a=1);else if("string"==typeof e)a=5;else e:switch(e){case d:return Ma(n.children,i,o,t);case S:a=8,i|=16;break;case f:a=8,i|=1;break;case p:return(e=Ta(12,n,t,8|i)).elementType=p,e.type=p,e.lanes=o,e;case g:return(e=Ta(13,n,t,i)).type=g,e.elementType=g,e.lanes=o,e;case b:return(e=Ta(19,n,t,i)).elementType=b,e.lanes=o,e;case E:return Da(n,i,o,t);case x:return(e=Ta(24,n,t,i)).elementType=x,e.lanes=o,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case h:a=10;break e;case m:a=9;break e;case y:a=11;break e;case v:a=14;break e;case w:a=16,r=null;break e;case k:a=22;break e}throw Error(l(130,null==e?e:typeof e,""))}return(t=Ta(a,n,t,i)).elementType=e,t.type=r,t.lanes=o,t}function Ma(e,t,n,r){return(e=Ta(7,e,r,t)).lanes=n,e}function Da(e,t,n,r){return(e=Ta(23,e,r,t)).elementType=E,e.lanes=n,e}function Ua(e,t,n){return(e=Ta(6,e,null,t)).lanes=n,e}function Wa(e,t,n){return(t=Ta(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ba(e,t,n){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=K,this.pendingContext=this.context=null,this.hydrate=n,this.callbackNode=null,this.callbackPriority=0,this.eventTimes=It(0),this.expirationTimes=It(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=It(0),J&&(this.mutableSourceEagerHydrationData=null)}function Va(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(l(188));throw Error(l(268,Object.keys(e)))}return null===(e=R(t))?null:e.stateNode}function $a(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function qa(e,t){$a(e,t),(e=e.alternate)&&$a(e,t)}function Qa(e){return null===(e=R(e))?null:e.stateNode}function Ka(){return null}return r.IsThisRendererActing=Ia,r.act=function(e){function t(){Ra--,fo.current=n,Ia.current=r}!1===za&&(za=!0,console.error("act(...) is not supported in production builds of React, and might not behave as expected.")),Ra++;var n=fo.current,r=Ia.current;fo.current=!0,Ia.current=!0;try{var i=ta(e)}catch(e){throw t(),e}if(null!==i&&"object"==typeof i&&"function"==typeof i.then)return{then:function(e,r){i.then((function(){1<Ra||!0===_a&&!0===n?(t(),e()):Aa((function(n){t(),n?r(n):e()}))}),(function(e){t(),r(e)}))}};try{1!==Ra||!1!==_a&&!1!==n||La(),t()}catch(e){throw t(),e}return{then:function(e){e()}}},r.attemptContinuousHydration=function(e){13===e.tag&&(Go(e,67108864,Qo()),qa(e,67108864))},r.attemptHydrationAtCurrentPriority=function(e){if(13===e.tag){var t=Qo(),n=Ko(e);Go(e,n,t),qa(e,n)}},r.attemptSynchronousHydration=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.hydrate){var n=St(t.pendingLanes);t.expiredLanes|=n&t.pendingLanes,Xo(t,Yt()),0==(48&po)&&(Po(),tn())}break;case 13:var r=Qo();na((function(){return Go(e,1,r)})),qa(e,4)}},r.attemptUserBlockingHydration=function(e){13===e.tag&&(Go(e,4,Qo()),qa(e,4))},r.batchedEventUpdates=function(e,t){var n=po;po|=2;try{return e(t)}finally{0===(po=n)&&(Po(),tn())}},r.batchedUpdates=ta,r.createComponentSelector=function(e){return{$$typeof:Yi,value:e}},r.createContainer=function(e,t,n){return e=new Ba(e,t,n),t=Ta(3,null,null,2===t?7:1===t?3:0),e.current=t,t.stateNode=e,kn(t),e},r.createHasPsuedoClassSelector=function(e){return{$$typeof:Xi,value:e}},r.createPortal=function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:c,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}},r.createRoleSelector=function(e){return{$$typeof:Ji,value:e}},r.createTestNameSelector=function(e){return{$$typeof:Zi,value:e}},r.createTextSelector=function(e){return{$$typeof:eo,value:e}},r.deferredUpdates=function(e){return Zt(97,e)},r.discreteUpdates=function(e,t,n,r,i){var o=po;po|=4;try{return Zt(98,e.bind(null,t,n,r,i))}finally{0===(po=o)&&(Po(),tn())}},r.findAllNodes=ao,r.findBoundingRects=function(e,t){if(!oe)throw Error(l(363));t=ao(e,t),e=[];for(var n=0;n<t.length;n++)e.push(le(t[n]));for(t=e.length-1;0<t;t--)for(var r=(n=e[t]).x,i=r+n.width,o=n.y,a=o+n.height,s=t-1;0<=s;s--)if(t!==s){var u=e[s],c=u.x,d=c+u.width,f=u.y,p=f+u.height;if(r>=c&&o>=f&&i<=d&&a<=p){e.splice(t,1);break}if(!(r!==c||n.width!==u.width||p<o||f>a)){f>o&&(u.height+=f-o,u.y=o),p<a&&(u.height=a-f),e.splice(t,1);break}if(!(o!==f||n.height!==u.height||d<r||c>i)){c>r&&(u.width+=c-r,u.x=r),d<i&&(u.width=i-c),e.splice(t,1);break}}return e},r.findHostInstance=Va,r.findHostInstanceWithNoPortals=function(e){return null===(e=function(e){if(!(e=A(e)))return null;for(var t=e;;){if(5===t.tag||6===t.tag)return t;if(t.child&&4!==t.tag)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}(e))?null:20===e.tag?e.stateNode.instance:e.stateNode},r.findHostInstanceWithWarning=function(e){return Va(e)},r.flushControlled=function(e){var t=po;po|=1;try{Zt(99,e)}finally{0===(po=t)&&(Po(),tn())}},r.flushDiscreteUpdates=function(){0==(49&po)&&(function(){if(null!==Mo){var e=Mo;Mo=null,e.forEach((function(e){e.expiredLanes|=24&e.pendingLanes,Xo(e,Yt())}))}tn()}(),ga())},r.flushPassiveEffects=ga,r.flushSync=na,r.focusWithin=function(e,t){if(!oe)throw Error(l(363));for(t=oo(e=no(e),t),t=Array.from(t),e=0;e<t.length;){var n=t[e++];if(!ue(n)){if(5===n.tag&&de(n.stateNode))return!0;for(n=n.child;null!==n;)t.push(n),n=n.sibling}}return!1},r.getCurrentUpdateLanePriority=function(){return wt},r.getFindAllNodesFailureDescription=function(e,t){if(!oe)throw Error(l(363));var n=0,r=[];e=[no(e),0];for(var i=0;i<e.length;){var o=e[i++],a=e[i++],s=t[a];if((5!==o.tag||!ue(o))&&(ro(o,s)&&(r.push(io(s)),++a>n&&(n=a)),a<t.length))for(o=o.child;null!==o;)e.push(o,a),o=o.sibling}if(n<t.length){for(e=[];n<t.length;n++)e.push(io(t[n]));return"findAllNodes was able to match part of the selector:\n  "+r.join(" > ")+"\n\nNo matching component was found for:\n  "+e.join(" > ")}return null},r.getPublicRootInstance=function(e){if(!(e=e.current).child)return null;switch(e.child.tag){case 5:return T(e.child.stateNode);default:return e.child.stateNode}},r.injectIntoDevTools=function(e){if(e={bundleType:e.bundleType,version:e.version,rendererPackageName:e.rendererPackageName,rendererConfig:e.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:s.ReactCurrentDispatcher,findHostInstanceByFiber:Qa,findFiberByHostInstance:e.findFiberByHostInstance||Ka,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null},"undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)e=!1;else{var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!t.isDisabled&&t.supportsFiber)try{bt=t.inject(e),vt=t}catch(e){}e=!0}return e},r.observeVisibleRects=function(e,t,n,r){if(!oe)throw Error(l(363));e=ao(e,t);var i=fe(e,n,r).disconnect;return{disconnect:function(){i()}}},r.registerMutableSourceForHydration=function(e,t){var n=t._getVersion;n=n(t._source),null==e.mutableSourceEagerHydrationData?e.mutableSourceEagerHydrationData=[t,n]:e.mutableSourceEagerHydrationData.push(t,n)},r.runWithPriority=function(e,t){var n=wt;try{return wt=e,t()}finally{wt=n}},r.shouldSuspend=function(){return!1},r.unbatchedUpdates=function(e,t){var n=po;po&=-2,po|=8;try{return e(t)}finally{0===(po=n)&&(Po(),tn())}},r.updateContainer=function(e,t,n,r){var i=t.current,o=Qo(),a=Ko(i);e:if(n){t:{if(_(n=n._reactInternals)!==n||1!==n.tag)throw Error(l(170));var s=n;do{switch(s.tag){case 3:s=s.stateNode.context;break t;case 1:if(ft(s.type)){s=s.stateNode.__reactInternalMemoizedMergedChildContext;break t}}s=s.return}while(null!==s);throw Error(l(171))}if(1===n.tag){var u=n.type;if(ft(u)){n=mt(n,u,s);break e}}n=s}else n=lt;return null===t.context?t.context=n:t.pendingContext=n,(t=En(o,a)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),xn(i,t),Go(i,a,o),a},r}}).call(this,n("./node_modules/webpack/buildin/module.js")(e))},"./third-party/react-reconciler/index.js":function(e,t,n){"use strict";e.exports=n("./third-party/react-reconciler/cjs/react-reconciler.production.min.js")},0:function(e,t,n){e.exports=n}});