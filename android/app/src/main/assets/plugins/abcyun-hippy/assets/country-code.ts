const COUNTRY_CODE_LIST = Object.freeze([
    {
        countryName: "Afghanistan",
        countryAreaCode: "AF",
        countryNameZH: "阿富汗",
        countryCode: "93",
        phoneLength: "9",
        phoneSegment: "24、70-79",
        memo: "-",
    },
    {
        countryName: "Albania",
        countryAreaCode: "AL",
        countryNameZH: "阿尔巴尼亚",
        countryCode: "355",
        phoneLength: "9",
        phoneSegment: "67- 69",
        memo: "-",
    },
    {
        countryName: "Algeria",
        countryAreaCode: "DZ",
        countryNameZH: "阿尔及利亚",
        countryCode: "213",
        phoneLength: "9",
        phoneSegment: "5、6、7、9",
        memo: "-",
    },
    {
        countryName: "American Samoa",
        countryAreaCode: "AS",
        countryNameZH: "美属萨摩亚",
        countryCode: "1684",
        phoneLength: "7",
        phoneSegment: "252、254、256、733",
        memo: "-",
    },
    {
        countryName: "Andorra",
        countryAreaCode: "AD",
        countryNameZH: "安道尔",
        countryCode: "376",
        phoneLength: "9",
        phoneSegment: "3、4、6",
        memo: "-",
    },
    {
        countryName: "Angola",
        countryAreaCode: "AO",
        countryNameZH: "安哥拉",
        countryCode: "244",
        phoneLength: "9",
        phoneSegment: "91、92、93",
        memo: "2开头是固话。",
    },
    {
        countryName: "Anguilla",
        countryAreaCode: "AI",
        countryNameZH: "安圭拉",
        countryCode: "1264",
        phoneLength: "7",
        phoneSegment: "53、58、7",
        memo: "-",
    },
    {
        countryName: "Antigua and Barbuda",
        countryAreaCode: "AG",
        countryNameZH: "安提瓜和巴布达",
        countryCode: "1268",
        phoneLength: "7",
        phoneSegment: "464、7",
        memo: "-",
    },
    {
        countryName: "Argentina",
        countryAreaCode: "AR",
        countryNameZH: "阿根廷",
        countryCode: "54",
        phoneLength: "10",
        phoneSegment: "9/15",
        memo: "号码结构是区号+用户号。",
    },
    {
        countryName: "Armenia",
        countryAreaCode: "AM",
        countryNameZH: "亚美尼亚",
        countryCode: "374",
        phoneLength: "8",
        phoneSegment: "41、43、44、55、77、91、93-94、97-99",
        memo: "-",
    },
    {
        countryName: "Aruba",
        countryAreaCode: "AW",
        countryNameZH: "阿鲁巴",
        countryCode: "297",
        phoneLength: "7",
        phoneSegment: "56、592-594、597、598、660、661、622、630、640、641、690、73、74、995-998",
        memo: "28/501开头是网络语音电讯业务。",
    },
    {
        countryName: "Australia",
        countryAreaCode: "AU",
        countryNameZH: "澳大利亚",
        countryCode: "61",
        phoneLength: "9",
        phoneSegment: "4",
        memo: "2/3/7/8开头是固话。",
    },
    {
        countryName: "Austria",
        countryAreaCode: "AT",
        countryNameZH: "奥地利",
        countryCode: "43",
        phoneLength: "10",
        phoneSegment: "650、660、664、676、680",
        memo: "-",
    },
    // {
    //     countryName: "11",
    //     countryAreaCode: "667、681、688、699",
    //     countryNameZH: "-",
    // },
    {
        countryName: "Azerbaijan",
        countryAreaCode: "AZ",
        countryNameZH: "阿塞拜疆",
        countryCode: "994",
        phoneLength: "9",
        phoneSegment: "41、50、51、55、70、77、99",
        memo: "1/2/3开头是固话。",
    },
    {
        countryName: "Bahamas",
        countryAreaCode: "BS",
        countryNameZH: "巴哈马",
        countryCode: "1242",
        phoneLength: "7",
        phoneSegment: "35、45、55",
        memo: "-",
    },
    {
        countryName: "Bahrain",
        countryAreaCode: "BH",
        countryNameZH: "巴林",
        countryCode: "973",
        phoneLength: "8",
        phoneSegment: "31、33、36、39、322、340、341、343-345、353、355、377、383、384、388、663、666、669",
        memo: "6/7开头是通用号码。1开头是固话。",
    },
    {
        countryName: "Bangladesh",
        countryAreaCode: "BD",
        countryNameZH: "孟加拉国",
        countryCode: "880",
        phoneLength: "8",
        phoneSegment: "13、140、15、16、17、18、19",
        memo: "-",
    },
    {
        countryName: "Barbados",
        countryAreaCode: "BB",
        countryNameZH: "巴巴多斯",
        countryCode: "1246",
        phoneLength: "7",
        phoneSegment: "230-255、256-269、280-289、450-459、820-859、883",
        memo: "-",
    },
    {
        countryName: "Belarus",
        countryAreaCode: "BY",
        countryNameZH: "白俄罗斯",
        countryCode: "375",
        phoneLength: "9",
        phoneSegment: "25、33、44、291-299",
        memo: "-",
    },
    {
        countryName: "Belgium",
        countryAreaCode: "BE",
        countryNameZH: "比利时",
        countryCode: "32",
        phoneLength: "9",
        phoneSegment: "456、47、48、49",
        memo: "-",
    },
    {
        countryName: "Belize",
        countryAreaCode: "BZ",
        countryNameZH: "伯利兹",
        countryCode: "501",
        phoneLength: "7",
        phoneSegment: "6",
        memo: "-",
    },
    {
        countryName: "Benin",
        countryAreaCode: "BJ",
        countryNameZH: "贝宁",
        countryCode: "229",
        phoneLength: "8",
        phoneSegment: "90、93、95、97",
        memo: "-",
    },
    {
        countryName: "Bermuda",
        countryAreaCode: "BM",
        countryNameZH: "百慕大群岛",
        countryCode: "1441",
        phoneLength: "7",
        phoneSegment: "3、500-539、59、7",
        memo: "-",
    },
    {
        countryName: "Bhutan",
        countryAreaCode: "BT",
        countryNameZH: "不丹",
        countryCode: "975",
        phoneLength: "8",
        phoneSegment: "17",
        memo: "-",
    },
    {
        countryName: "Bolivia",
        countryAreaCode: "BO",
        countryNameZH: "玻利维亚",
        countryCode: "591",
        phoneLength: "8",
        phoneSegment: "70-72、77",
        memo: "固话是7位数。",
    },
    {
        countryName: "Bosnia and Herzegovina",
        countryAreaCode: "BA",
        countryNameZH: "波斯尼亚和黑塞哥维那",
        countryCode: "387",
        phoneLength: "8或9",
        phoneSegment: "60、62-66、69",
        memo: "-",
    },
    {
        countryName: "Botswana",
        countryAreaCode: "BW",
        countryNameZH: "博茨瓦纳",
        countryCode: "267",
        phoneLength: "8",
        phoneSegment: "7",
        memo: "79开头是虚拟号。",
    },
    {
        countryName: "Brazil",
        countryAreaCode: "BR",
        countryNameZH: "巴西",
        countryCode: "55",
        phoneLength: "9",
        phoneSegment: "9",
        memo: "-",
    },
    {
        countryName: "Brunei",
        countryAreaCode: "BN",
        countryNameZH: "文莱",
        countryCode: "673",
        phoneLength: "7",
        phoneSegment: "2、7、8",
        memo: "-",
    },
    {
        countryName: "Bulgaria",
        countryAreaCode: "BG",
        countryNameZH: "保加利亚",
        countryCode: "359",
        phoneLength: "9",
        phoneSegment: "87-89、988",
        memo: "-",
    },
    {
        countryName: "Burkina Faso",
        countryAreaCode: "BF",
        countryNameZH: "布基纳法索",
        countryCode: "226",
        phoneLength: "8",
        phoneSegment: "51、55、56、60-69、70-79",
        memo: "2开头是固话。",
    },
    {
        countryName: "Burundi",
        countryAreaCode: "BI",
        countryNameZH: "布隆迪",
        countryCode: "257",
        phoneLength: "8",
        phoneSegment: "3、6、9、29",
        memo: "22开头是固话。",
    },
    {
        countryName: "Cambodia",
        countryAreaCode: "KH",
        countryNameZH: "柬埔寨",
        countryCode: "855",
        phoneLength: "8或9",
        phoneSegment: "10-12、15、16、70、76、77、81、86、87、92、93、96-99",
        memo: "其他号段：31、38、60、61、66-71、76-78、80、81、83-89、90-93、95-99",
    },
    {
        countryName: "Cameroon",
        countryAreaCode: "CM",
        countryNameZH: "喀麦隆",
        countryCode: "237",
        phoneLength: "9",
        phoneSegment: "7、9",
        memo: "222/233开头是固话。",
    },
    {
        countryName: "Canada",
        countryAreaCode: "CA",
        countryNameZH: "加拿大",
        countryCode: "1",
        phoneLength: "10",
        phoneSegment:
            "204、226、236、249、250、289、306、343、365、403、416、418、431、437、438、450、506、514、519、579、581、587、604、613、639、647、705、709、778、780、819、867、873、902",
        memo: "-",
    },
    {
        countryName: "Cape Verde",
        countryAreaCode: "CV",
        countryNameZH: "佛得角",
        countryCode: "238",
        phoneLength: "7",
        phoneSegment: "9",
        memo: "2开头是固话。",
    },
    {
        countryName: "Cayman Islands",
        countryAreaCode: "KY",
        countryNameZH: "开曼群岛",
        countryCode: "1345",
        phoneLength: "7",
        phoneSegment: "3、5、9",
        memo: "-",
    },
    {
        countryName: "Central African Republic",
        countryAreaCode: "CF",
        countryNameZH: "中非共和国",
        countryCode: "236",
        phoneLength: "8",
        phoneSegment: "70、75、77",
        memo: "21/22开头是固话。",
    },
    {
        countryName: "Chad",
        countryAreaCode: "TD",
        countryNameZH: "乍得",
        countryCode: "235",
        phoneLength: "8",
        phoneSegment: "63、65、66、77、90、93、95、99",
        memo: "22/77开头是固话。",
    },
    {
        countryName: "Chile",
        countryAreaCode: "CL",
        countryNameZH: "智利",
        countryCode: "56",
        phoneLength: "9",
        phoneSegment: "9",
        memo: "-",
    },
    {
        countryName: "Chinese Mainland",
        countryAreaCode: "CN",
        countryNameZH: "中国大陆",
        countryCode: "86",
        phoneLength: "11",
        phoneSegment:
            "中国电信号段：133、149、153、173、177、180、181、189、190、191、193、199；中国联通号段：130、131、132、145、155、156、166、167、171、175、176、185、186、196；中国移动号段：134(0-8)、135、136、137、138、139、1440、147、148、150、151、152、157、158、159、172、178、182、183、184、187、188、195、197、198；中国广电号段：192；其他号段：14号段部分为上网卡专属号段，中国联通145，中国移动147，中国电信149",
        memo: "虚拟运营商：",
    },
    {
        countryName: "Colombia",
        countryAreaCode: "CO",
        countryNameZH: "哥伦比亚",
        countryCode: "57",
        phoneLength: "10",
        phoneSegment: "300、301、310-319、350、351",
        memo: "固话是7位数。",
    },
    {
        countryName: "Comoros",
        countryAreaCode: "KM",
        countryNameZH: "科摩罗",
        countryCode: "269",
        phoneLength: "7",
        phoneSegment: "3",
        memo: "74/75/76/77开头是固话。",
    },
    {
        countryName: "Cook Islands",
        countryAreaCode: "CK",
        countryNameZH: "库克群岛",
        countryCode: "682",
        phoneLength: "5",
        phoneSegment: "7、54、55",
        memo: "2/3/4开头是固话。",
    },
    {
        countryName: "Costa Rica",
        countryAreaCode: "CR",
        countryNameZH: "哥斯达黎加",
        countryCode: "506",
        phoneLength: "8",
        phoneSegment: "6-8",
        memo: "2开头是固话。",
    },
    {
        countryName: "Côte d'Ivoire",
        countryAreaCode: "CI",
        countryNameZH: "科特迪瓦",
        countryCode: "225",
        phoneLength: "8",
        phoneSegment: "01-09、44-50、54、60、66-67、69、77-78",
        memo: "2/3开头是固话。",
    },
    {
        countryName: "Croatia",
        countryAreaCode: "HR",
        countryNameZH: "克罗地亚",
        countryCode: "385",
        phoneLength: "9",
        phoneSegment: "91、92、95、97-99",
        memo: "1/2/3/4/5开头是固话。",
    },
    {
        countryName: "Curacao",
        countryAreaCode: "CW",
        countryNameZH: "库拉索",
        countryCode: "599",
        phoneLength: "7",
        phoneSegment: "9",
        memo: "4/50/7/8开头是固话。",
    },
    {
        countryName: "Cyprus",
        countryAreaCode: "CY",
        countryNameZH: "塞浦路斯",
        countryCode: "357",
        phoneLength: "8",
        phoneSegment: "94-97、99",
        memo: "2开头是固话。",
    },
    {
        countryName: "Czechia",
        countryAreaCode: "CZ",
        countryNameZH: "捷克",
        countryCode: "420",
        phoneLength: "9",
        phoneSegment: "601-608、702、72、73、77、790",
        memo: "2/3/4/5开头是固话。",
    },
    {
        countryName: "Democratic Republic of the Congo",
        countryAreaCode: "CD",
        countryNameZH: "刚果民主共和国",
        countryCode: "243",
        phoneLength: "9",
        phoneSegment: "22、78、81-86、88-90、94-99",
        memo: "-",
    },
    {
        countryName: "Denmark",
        countryAreaCode: "DK",
        countryNameZH: "丹麦",
        countryCode: "45",
        phoneLength: "8",
        phoneSegment: "20-31、40-42、50-53、60、61、71、81",
        memo: "-",
    },
    {
        countryName: "Djibouti",
        countryAreaCode: "DJ",
        countryNameZH: "吉布提",
        countryCode: "253",
        phoneLength: "8",
        phoneSegment: "8",
        memo: "21/27开头是固话。",
    },
    {
        countryName: "Dominica",
        countryAreaCode: "DM",
        countryNameZH: "多米尼克",
        countryCode: "1767",
        phoneLength: "7",
        phoneSegment: "225、235、245、265、275-277、285、295、315-317、612-617",
        memo: "-",
    },
    {
        countryName: "Dominican Republic",
        countryAreaCode: "DO",
        countryNameZH: "多米尼加共和国",
        countryCode: "1809/1829/1849",
        phoneLength: "7",
        phoneSegment:
            "201、204、213、22230-22259、223、22430-22459、249-258、267-272、283、292-293、299、301-310、312、314、321-327、330、340-345、348、350、355、360、366、370、37400-37469、376、383、395-396、399、415-424、428-432、436-449、451-459、47010-47089、48100-48189、49、515、519、54290-54298、54320-54399、545、60410-60499、624、628-635、637、639-653、656、658-671、696-697、707、710、723、727、729、749-769、771-777、785-787、796、801-805、815-818、829、834-835、837-858、860-869、873-890、912-918、928-929、932、938-946、952-953、972-982、990-999",
        memo: "-",
    },
    {
        countryName: "Ecuador",
        countryAreaCode: "EC",
        countryNameZH: "厄瓜多尔",
        countryCode: "593",
        phoneLength: "7",
        phoneSegment: "8、9",
        memo: "2/4开头是固话。",
    },
    {
        countryName: "Egypt",
        countryAreaCode: "EG",
        countryNameZH: "埃及",
        countryCode: "20",
        phoneLength: "10",
        phoneSegment: "10-12、15",
        memo: "-",
    },
    {
        countryName: "El Salvador",
        countryAreaCode: "SV",
        countryNameZH: "萨尔瓦多",
        countryCode: "503",
        phoneLength: "8",
        phoneSegment: "6、7",
        memo: "2开头是固话。",
    },
    {
        countryName: "Equatorial Guinea",
        countryAreaCode: "GQ",
        countryNameZH: "赤道几内亚",
        countryCode: "240",
        phoneLength: "9",
        phoneSegment: "2、5",
        memo: "3开头是固话。",
    },
    {
        countryName: "Eritrea",
        countryAreaCode: "ER",
        countryNameZH: "厄立特里亚",
        countryCode: "291",
        phoneLength: "7",
        phoneSegment: "71-73",
        memo: "1开头是固话。",
    },
    {
        countryName: "Estonia",
        countryAreaCode: "EE",
        countryNameZH: "爱沙尼亚",
        countryCode: "372",
        phoneLength: "7或8",
        phoneSegment: "50-59、510-518、550-554、557-558、5195、",
        memo: "-",
    },
    {
        countryName: "Eswatini",
        countryAreaCode: "SZ",
        countryNameZH: "斯威士兰",
        countryCode: "268",
        phoneLength: "8",
        phoneSegment: "7",
        memo: "2/3开头是固话。",
    },
    {
        countryName: "Ethiopia",
        countryAreaCode: "ET",
        countryNameZH: "埃塞俄比亚",
        countryCode: "251",
        phoneLength: "9",
        phoneSegment: "9",
        memo: "-",
    },
    {
        countryName: "Faroe Islands",
        countryAreaCode: "FO",
        countryNameZH: "法罗群岛",
        countryCode: "298",
        phoneLength: "6",
        phoneSegment: "21-29、5、71-79、91-99",
        memo: "70开头是分享电话号。90开头是收费资讯号。",
    },
    {
        countryName: "Fiji",
        countryAreaCode: "FJ",
        countryNameZH: "斐济",
        countryCode: "679",
        phoneLength: "7",
        phoneSegment: "3、7、9",
        memo: "3/6/8开头是固话。",
    },
    {
        countryName: "Finland",
        countryAreaCode: "FI",
        countryNameZH: "芬兰",
        countryCode: "358",
        phoneLength: "9",
        phoneSegment: "40-50",
        memo: "-",
    },
    {
        countryName: "France",
        countryAreaCode: "FR",
        countryNameZH: "法国",
        countryCode: "33",
        phoneLength: "9",
        phoneSegment: "6、73-78、700",
        memo: "-",
    },
    {
        countryName: "French Guiana",
        countryAreaCode: "GF",
        countryNameZH: "法属圭亚那",
        countryCode: "594",
        phoneLength: "9",
        phoneSegment: "694、700",
        memo: "594开头是固话。",
    },
    {
        countryName: "French Polynesia",
        countryAreaCode: "PF",
        countryNameZH: "法属波利尼西亚",
        countryCode: "689",
        phoneLength: "8",
        phoneSegment: "87、89",
        memo: "-",
    },
    {
        countryName: "Gabon",
        countryAreaCode: "GA",
        countryNameZH: "加蓬",
        countryCode: "241",
        phoneLength: "8",
        phoneSegment: "2-7",
        memo: "1开头是固话。",
    },
    {
        countryName: "Gambia",
        countryAreaCode: "GM",
        countryNameZH: "冈比亚",
        countryCode: "220",
        phoneLength: "7",
        phoneSegment: "7、9",
        memo: "-",
    },
    {
        countryName: "Georgia",
        countryAreaCode: "GE",
        countryNameZH: "格鲁吉亚",
        countryCode: "995",
        phoneLength: "9",
        phoneSegment: "544、514、551、555、557、558、568、570、571、574、577-579、591-593、595-599",
        memo: "3/4开头是固话。",
    },
    {
        countryName: "Germany",
        countryAreaCode: "DE",
        countryNameZH: "德国",
        countryCode: "49",
        phoneLength: "11",
        phoneSegment:
            "15020、15050、15080、1511-1512、1514-1517、1520-1523、1525-1526、1529、15555、15630、15678、1570、1573、1575、1577-1579、15888、1590、160、162-163、170-179",
        memo: "-",
    },
    {
        countryName: "Ghana",
        countryAreaCode: "GH",
        countryNameZH: "加纳",
        countryCode: "233",
        phoneLength: "9",
        phoneSegment: "20、23、24、26-28、50、54-57、59",
        memo: "3开头是固话。",
    },
    {
        countryName: "Gibraltar",
        countryAreaCode: "GI",
        countryNameZH: "直布罗陀",
        countryCode: "350",
        phoneLength: "8",
        phoneSegment: "5、6",
        memo: "2开头是固话。",
    },
    {
        countryName: "Greece",
        countryAreaCode: "GR",
        countryNameZH: "希腊",
        countryCode: "30",
        phoneLength: "10",
        phoneSegment: "1、2、690、693-695、697-699",
        memo: "2开头是固话。",
    },
    {
        countryName: "Greenland",
        countryAreaCode: "GL",
        countryNameZH: "格陵兰岛",
        countryCode: "299",
        phoneLength: "6",
        phoneSegment: "21-29、42-49、51-59",
        memo: "3/6/8/9开头是固话。",
    },
    {
        countryName: "Grenada",
        countryAreaCode: "GD",
        countryNameZH: "格林纳达",
        countryCode: "1473",
        phoneLength: "7",
        phoneSegment: "402-407、409-410、414-420、458、520-521、533-538、901",
        memo: "-",
    },
    {
        countryName: "Guadeloupe",
        countryAreaCode: "GP",
        countryNameZH: "瓜德罗普岛",
        countryCode: "590",
        phoneLength: "9",
        phoneSegment: "690、700",
        memo: "59开头是固话。",
    },
    {
        countryName: "Guam",
        countryAreaCode: "GU",
        countryNameZH: "关岛",
        countryCode: "1671",
        phoneLength: "7",
        phoneSegment:
            "482-483、488-489、678、685-689、707、727、747、777、787-788、797、838、848、858、868、878、888、898、929、967、972、977、987-988、997-998",
        memo: "-",
    },
    {
        countryName: "Guatemala",
        countryAreaCode: "GT",
        countryNameZH: "危地马拉",
        countryCode: "502",
        phoneLength: "8",
        phoneSegment:
            "30、310、311、231、2324、2326、2327-2329、2428、2429、3120-3128、4476-4479、448、449、45、46、470-476、4773-4779、478-481、4822-4829、483-489、49、4、5",
        memo: "2/6/7开头是固话。",
    },
    {
        countryName: "Guinea",
        countryAreaCode: "GN",
        countryNameZH: "几内亚",
        countryCode: "224",
        phoneLength: "9",
        phoneSegment: "6",
        memo: "30开头是固话。",
    },
    {
        countryName: "Guinea-Bissau",
        countryAreaCode: "GW",
        countryNameZH: "几内亚比绍共和国",
        countryCode: "245",
        phoneLength: "9",
        phoneSegment: "6、7",
        memo: "4开头是固话。",
    },
    {
        countryName: "Guyana",
        countryAreaCode: "GY",
        countryNameZH: "圭亚那",
        countryCode: "592",
        phoneLength: "7",
        phoneSegment: "6",
        memo: "2/3/4/7开头是固话。",
    },
    {
        countryName: "Haiti",
        countryAreaCode: "HT",
        countryNameZH: "海地",
        countryCode: "509",
        phoneLength: "8",
        phoneSegment: "34-39",
        memo: "2开头是固话。9开头是虚拟号。",
    },
    {
        countryName: "Honduras",
        countryAreaCode: "HN",
        countryNameZH: "洪都拉斯",
        countryCode: "504",
        phoneLength: "8",
        phoneSegment: "3、7-9",
        memo: "2开头是固话。",
    },
    {
        countryName: "Hong Kong, China",
        countryAreaCode: "HK",
        countryNameZH: "中国香港",
        countryCode: "852",
        phoneLength: "8",
        phoneSegment: "460-469、510-579、590-599、601-699、701-709、840-849、901-910、912-989",
        memo: "2/3开头是固话。",
    },
    {
        countryName: "Hungary",
        countryAreaCode: "HU",
        countryNameZH: "匈牙利",
        countryCode: "36",
        phoneLength: "9",
        phoneSegment: "23、30、31、38、50、60、70",
        memo: "-",
    },
    {
        countryName: "Iceland",
        countryAreaCode: "IS",
        countryNameZH: "冰岛",
        countryCode: "354",
        phoneLength: "7",
        phoneSegment: "6",
        memo: "4/5开头是固话。",
    },
    {
        countryName: "India",
        countryAreaCode: "IN",
        countryNameZH: "印度",
        countryCode: "91",
        phoneLength: "10",
        phoneSegment: "6、7、8、9",
        memo: "-",
    },
    {
        countryName: "Indonesia",
        countryAreaCode: "ID",
        countryNameZH: "印度尼西亚",
        countryCode: "62",
        phoneLength: "10或11",
        phoneSegment: "811-819、838、852、853、855、856、858、859、878、896-899",
        memo: "17开头是IP电话。2/3/4/7开头是固话。",
    },
    {
        countryName: "Iraq",
        countryAreaCode: "IQ",
        countryNameZH: "伊拉克",
        countryCode: "964",
        phoneLength: "10",
        phoneSegment: "73-79",
        memo: "-",
    },
    {
        countryName: "Ireland",
        countryAreaCode: "IE",
        countryNameZH: "爱尔兰",
        countryCode: "353",
        phoneLength: "9",
        phoneSegment: "83、85-87、89",
        memo: "-",
    },
    {
        countryName: "Israel",
        countryAreaCode: "IL",
        countryNameZH: "以色列",
        countryCode: "972",
        phoneLength: "9",
        phoneSegment: "50、52-54、58、556、558、559、5522或5523、5570或5571",
        memo: "55开头是虚拟网。7开头是非地理固话，固话是8位数。",
    },
    {
        countryName: "Italy",
        countryAreaCode: "IT",
        countryNameZH: "意大利",
        countryCode: "39",
        phoneLength: "10",
        phoneSegment:
            "310、31100、31101、31105、313、319、320、322-324、327-331、333-349、3505、3510、3512、360-363、366、368、370、3710、3711、373、377、380-383、385、388-393、397",
        memo: "-",
    },
    {
        countryName: "Jamaica",
        countryAreaCode: "JM",
        countryNameZH: "牙买加",
        countryCode: "1876",
        phoneLength: "7",
        phoneSegment:
            "210、301-304、320、322、330-414、416-494、570-580、589、700、707、770-779、781-784、787-793、796-799、806-809、812-899、909、919、990、995、997、999",
        memo: "-",
    },
    {
        countryName: "Japan",
        countryAreaCode: "JP",
        countryNameZH: "日本",
        countryCode: "81",
        phoneLength: "10",
        phoneSegment: "60、70、80、90",
        memo: "-",
    },
    {
        countryName: "Jordan",
        countryAreaCode: "JO",
        countryNameZH: "约旦",
        countryCode: "962",
        phoneLength: "9",
        phoneSegment: "77-79",
        memo: "2/3/5/6开头是固话，固话是7位数。",
    },
    {
        countryName: "Kazakhstan",
        countryAreaCode: "KZ",
        countryNameZH: "哈萨克斯坦",
        countryCode: "76或77",
        phoneLength: "10",
        phoneSegment: "6",
        memo: "7开头是固话。",
    },
    {
        countryName: "Kenya",
        countryAreaCode: "KE",
        countryNameZH: "肯尼亚",
        countryCode: "254",
        phoneLength: "9",
        phoneSegment: "10、11、70-75、77、78、763",
        memo: "-",
    },
    {
        countryName: "Kiribati",
        countryAreaCode: "KI",
        countryNameZH: "基里巴斯",
        countryCode: "686",
        phoneLength: "7",
        phoneSegment: "63、7",
        memo: "-",
    },
    {
        countryName: "Kuwait",
        countryAreaCode: "KW",
        countryNameZH: "科威特",
        countryCode: "965",
        phoneLength: "8",
        phoneSegment: "5、6、9",
        memo: "2开头是固话。",
    },
    {
        countryName: "Kyrgyzstan",
        countryAreaCode: "KG",
        countryNameZH: "吉尔吉斯斯坦",
        countryCode: "996",
        phoneLength: "9",
        phoneSegment: "5、9",
        memo: "3开头是固话。52开头是卫星电话。",
    },
    {
        countryName: "Laos",
        countryAreaCode: "LA",
        countryNameZH: "老挝",
        countryCode: "856",
        phoneLength: "10",
        phoneSegment: "20",
        memo: "固话是8位数。",
    },
    {
        countryName: "Latvia",
        countryAreaCode: "LV",
        countryNameZH: "拉脱维亚",
        countryCode: "371",
        phoneLength: "8",
        phoneSegment: "2",
        memo: "5/6/7开头是固话。",
    },
    {
        countryName: "Lebanon",
        countryAreaCode: "LB",
        countryNameZH: "黎巴嫩",
        countryCode: "961",
        phoneLength: "8",
        phoneSegment: "3、70、71、76",
        memo: "固话是7位数。",
    },
    {
        countryName: "Lesotho",
        countryAreaCode: "LS",
        countryNameZH: "莱索托",
        countryCode: "266",
        phoneLength: "8",
        phoneSegment: "58、6",
        memo: "2开头是固话。",
    },
    {
        countryName: "Liberia",
        countryAreaCode: "LR",
        countryNameZH: "利比里亚",
        countryCode: "231",
        phoneLength: "9",
        phoneSegment: "5、7、46、47、64、65",
        memo: "3开头是固话。",
    },
    {
        countryName: "Libya",
        countryAreaCode: "LY",
        countryNameZH: "利比亚",
        countryCode: "218",
        phoneLength: "8",
        phoneSegment: "91、94",
        memo: "-",
    },
    {
        countryName: "Liechtenstein",
        countryAreaCode: "LI",
        countryNameZH: "列支敦士登",
        countryCode: "423",
        phoneLength: "7",
        phoneSegment: "6499、650-653、660、6610、6620、6626-6629、6637-6639、69742、6977-6978、742、77-79",
        memo: "2/3开头是固话。",
    },
    {
        countryName: "Lithuania",
        countryAreaCode: "LT",
        countryNameZH: "立陶宛",
        countryCode: "370",
        phoneLength: "8",
        phoneSegment: "6",
        memo: "-",
    },
    {
        countryName: "Luxembourg",
        countryAreaCode: "LU",
        countryNameZH: "卢森堡",
        countryCode: "352",
        phoneLength: "9",
        phoneSegment: "621、628、661、668、691、698",
        memo: "-",
    },
    {
        countryName: "Macao, China",
        countryAreaCode: "MO",
        countryNameZH: "中国澳门",
        countryCode: "853",
        phoneLength: "8",
        phoneSegment: "6",
        memo: "8开头是固话。",
    },
    {
        countryName: "North Macedonia",
        countryAreaCode: "MK",
        countryNameZH: "北马其顿",
        countryCode: "389",
        phoneLength: "8",
        phoneSegment: "70-73、75-78",
        memo: "2/3/4开头是固话。",
    },
    {
        countryName: "Madagascar",
        countryAreaCode: "MG",
        countryNameZH: "马达加斯加",
        countryCode: "261",
        phoneLength: "9",
        phoneSegment: "3",
        memo: "2开头是固话。",
    },
    {
        countryName: "Malawi",
        countryAreaCode: "MW",
        countryNameZH: "马拉维",
        countryCode: "265",
        phoneLength: "9",
        phoneSegment: "8、9",
        memo: "1开头7位数是固话。",
    },
    {
        countryName: "Malaysia",
        countryAreaCode: "MY",
        countryNameZH: "马来西亚",
        countryCode: "60",
        phoneLength: "11/15是10位，其他9位",
        phoneSegment: "1",
        memo: "注：支持携带或不带号码前的0。",
    },
    {
        countryName: "Maldives",
        countryAreaCode: "MV",
        countryNameZH: "马尔代夫",
        countryCode: "960",
        phoneLength: "7",
        phoneSegment: "7、9",
        memo: "3/6开头是固话。",
    },
    {
        countryName: "Mali",
        countryAreaCode: "ML",
        countryNameZH: "马里",
        countryCode: "223",
        phoneLength: "8",
        phoneSegment: "3-7",
        memo: "-",
    },
    {
        countryName: "Malta",
        countryAreaCode: "MT",
        countryNameZH: "马耳他",
        countryCode: "356",
        phoneLength: "8",
        phoneSegment: "77、79、98、99",
        memo: "217开头是邮箱号码。7117开头是传呼机。2开头是固话。",
    },
    {
        countryName: "Martinique",
        countryAreaCode: "MQ",
        countryNameZH: "马提尼克",
        countryCode: "596",
        phoneLength: "9",
        phoneSegment: "696、700",
        memo: "596开头是固话。",
    },
    {
        countryName: "Mauritania",
        countryAreaCode: "MR",
        countryNameZH: "毛里塔尼亚",
        countryCode: "222",
        phoneLength: "8",
        phoneSegment: "6",
        memo: "-",
    },
    {
        countryName: "Mauritius",
        countryAreaCode: "MU",
        countryNameZH: "毛里求斯",
        countryCode: "230",
        phoneLength: "8",
        phoneSegment: "54、57-59",
        memo: "-",
    },
    {
        countryName: "Mayotte",
        countryAreaCode: "YT",
        countryNameZH: "马约特",
        countryCode: "383",
        phoneLength: "8",
        phoneSegment: "639",
        memo: "269开头是固话。",
    },
    {
        countryName: "Mexico",
        countryAreaCode: "MX",
        countryNameZH: "墨西哥",
        countryCode: "52",
        phoneLength: "10",
        phoneSegment: "固话和手机号规则一致无法区分。",
        memo: "-",
    },
    {
        countryName: "Moldova",
        countryAreaCode: "MD",
        countryNameZH: "摩尔多瓦",
        countryCode: "373",
        phoneLength: "8",
        phoneSegment: "60、65、67-69、78、79",
        memo: "2/5开头是固话。",
    },
    {
        countryName: "Monaco",
        countryAreaCode: "MC",
        countryNameZH: "摩纳哥",
        countryCode: "377",
        phoneLength: "8",
        phoneSegment: "4、6",
        memo: "8/9开头是固话。",
    },
    {
        countryName: "Mongolia",
        countryAreaCode: "MN",
        countryNameZH: "蒙古",
        countryCode: "976",
        phoneLength: "8",
        phoneSegment: "70、88、89、91、93-96、98、99",
        memo: "7开头是虚拟号或固话。",
    },
    {
        countryName: "Montenegro",
        countryAreaCode: "ME",
        countryNameZH: "黑山",
        countryCode: "382",
        phoneLength: "8",
        phoneSegment: "60、63、66-69",
        memo: "78开头是虚拟号。",
    },
    {
        countryName: "Montserrat",
        countryAreaCode: "MS",
        countryNameZH: "蒙特塞拉特岛",
        countryCode: "1664",
        phoneLength: "7",
        phoneSegment: "固话和手机号规则一致无法区分。",
        memo: "-",
    },
    {
        countryName: "Morocco",
        countryAreaCode: "MA",
        countryNameZH: "摩洛哥",
        countryCode: "212",
        phoneLength: "9",
        phoneSegment: "6",
        memo: "5开头是固话。",
    },
    {
        countryName: "Mozambique",
        countryAreaCode: "MZ",
        countryNameZH: "莫桑比克",
        countryCode: "258",
        phoneLength: "9",
        phoneSegment: "82-87",
        memo: "2开头8位数是固话。",
    },
    {
        countryName: "Myanmar",
        countryAreaCode: "MM",
        countryNameZH: "缅甸",
        countryCode: "95",
        phoneLength: "10",
        phoneSegment: "92-96、925、926、943、944、973、991、996、997、977-979",
        memo: "-",
    },
    {
        countryName: "Namibia",
        countryAreaCode: "NA",
        countryNameZH: "纳米比亚",
        countryCode: "264",
        phoneLength: "9",
        phoneSegment: "60、81、85",
        memo: "6开头8位数是固话。",
    },
    {
        countryName: "Nepal",
        countryAreaCode: "NP",
        countryNameZH: "尼泊尔",
        countryCode: "977",
        phoneLength: "10",
        phoneSegment: "98",
        memo: "-",
    },
    {
        countryName: "Netherlands",
        countryAreaCode: "NL",
        countryNameZH: "荷兰",
        countryCode: "31",
        phoneLength: "9",
        phoneSegment: "6",
        memo: "-",
    },
    {
        countryName: "New Caledonia",
        countryAreaCode: "NC",
        countryNameZH: "新喀里多尼亚",
        countryCode: "687",
        phoneLength: "6",
        phoneSegment: "7、8、9",
        memo: "36开头是虚拟号。2/3/4开头是固话。",
    },
    {
        countryName: "New Zealand",
        countryAreaCode: "NZ",
        countryNameZH: "新西兰",
        countryCode: "64",
        phoneLength: "8或9或10",
        phoneSegment: "20-22、24、27-29、280、283",
        memo: "-",
    },
    {
        countryName: "Nicaragua",
        countryAreaCode: "NI",
        countryNameZH: "尼加拉瓜",
        countryCode: "505",
        phoneLength: "8",
        phoneSegment: "8",
        memo: "2开头是固话。",
    },
    {
        countryName: "Niger",
        countryAreaCode: "NE",
        countryNameZH: "尼日尔",
        countryCode: "227",
        phoneLength: "8",
        phoneSegment: "9",
        memo: "20开头是固话。",
    },
    {
        countryName: "Nigeria",
        countryAreaCode: "NG",
        countryNameZH: "尼日利亚",
        countryCode: "234",
        phoneLength: "8",
        phoneSegment: "802-805、809",
        memo: "-",
    },
    {
        countryName: "Norway",
        countryAreaCode: "NO",
        countryNameZH: "挪威",
        countryCode: "47",
        phoneLength: "8",
        phoneSegment: "4、9、59",
        memo: "2/3/5/6/7开头是固话。",
    },
    {
        countryName: "Oman",
        countryAreaCode: "OM",
        countryNameZH: "阿曼",
        countryCode: "968",
        phoneLength: "8",
        phoneSegment: "91-99",
        memo: "2开头是固话。",
    },
    {
        countryName: "Pakistan",
        countryAreaCode: "PK",
        countryNameZH: "巴基斯坦",
        countryCode: "92",
        phoneLength: "10",
        phoneSegment: "3、30-34",
        memo: "-",
    },
    {
        countryName: "Palau",
        countryAreaCode: "PW",
        countryNameZH: "帕劳",
        countryCode: "680",
        phoneLength: "7",
        phoneSegment: "77、88",
        memo: "-",
    },
    {
        countryName: "Palestine",
        countryAreaCode: "PS",
        countryNameZH: "巴勒斯坦",
        countryCode: "970",
        phoneLength: "9",
        phoneSegment: "56、59",
        memo: "固话是8位数。",
    },
    {
        countryName: "Panama",
        countryAreaCode: "PA",
        countryNameZH: "巴拿马",
        countryCode: "507",
        phoneLength: "8",
        phoneSegment: "6",
        memo: "-",
    },
    {
        countryName: "Papua New Guinea",
        countryAreaCode: "PG",
        countryNameZH: "巴布亚新几内亚",
        countryCode: "675",
        phoneLength: "8",
        phoneSegment: "7、8",
        memo: "20开头是虚拟号。固话是7位数。",
    },
    {
        countryName: "Paraguay",
        countryAreaCode: "PY",
        countryNameZH: "巴拉圭",
        countryCode: "595",
        phoneLength: "9",
        phoneSegment: "961、963、971-973、975、981-985、991-993、995",
        memo: "-",
    },
    {
        countryName: "Peru",
        countryAreaCode: "PE",
        countryNameZH: "秘鲁",
        countryCode: "51",
        phoneLength: "9",
        phoneSegment: "9",
        memo: "-",
    },
    {
        countryName: "Philippines",
        countryAreaCode: "PH",
        countryNameZH: "菲律宾",
        countryCode: "63",
        phoneLength: "10",
        phoneSegment: "905-910、912、915-923、926-930、932、933、935-939、942、943、947-949、973、974、977、979、989、996、997、999",
        memo: "-",
    },
    {
        countryName: "Poland",
        countryAreaCode: "PL",
        countryNameZH: "波兰",
        countryCode: "48",
        phoneLength: "9",
        phoneSegment: "45、50、51、53、57、30、33、39、72、73、78、79、88",
        memo: "-",
    },
    {
        countryName: "Portugal",
        countryAreaCode: "PT",
        countryNameZH: "葡萄牙",
        countryCode: "351",
        phoneLength: "9",
        phoneSegment: "91、93、96、921、922、924-927、9290-9294",
        memo: "2开头是固话。",
    },
    {
        countryName: "Puerto Rico",
        countryAreaCode: "PR",
        countryNameZH: "波多黎各",
        countryCode: "1787",
        phoneLength: "7",
        phoneSegment: "787、939",
        memo: "-",
    },
    {
        countryName: "Qatar",
        countryAreaCode: "QA",
        countryNameZH: "卡塔尔",
        countryCode: "974",
        phoneLength: "8",
        phoneSegment: "33、55、66、77",
        memo: "4开头是固话，固话是7位数。",
    },
    {
        countryName: "Republic Of The Congo",
        countryAreaCode: "CG",
        countryNameZH: "刚果共和国",
        countryCode: "2420",
        phoneLength: "9",
        phoneSegment: "4-6",
        memo: "2开头是固话。",
    },
    {
        countryName: "Réunion Island",
        countryAreaCode: "RE",
        countryNameZH: "留尼汪",
        countryCode: "262",
        phoneLength: "9",
        phoneSegment: "692、693、700",
        memo: "262/263/269开头是固话。",
    },
    {
        countryName: "Romania",
        countryAreaCode: "RO",
        countryNameZH: "罗马尼亚",
        countryCode: "40",
        phoneLength: "9",
        phoneSegment: "70、72-78、711",
        memo: "2/3开头是固话。",
    },
    {
        countryName: "Rwanda",
        countryAreaCode: "RW",
        countryNameZH: "卢旺达",
        countryCode: "250",
        phoneLength: "9",
        phoneSegment: "7、8",
        memo: "25开头是固话。",
    },
    {
        countryName: "Saint Kitts and Nevis",
        countryAreaCode: "KN",
        countryNameZH: "圣基茨和尼维斯",
        countryCode: "1869",
        phoneLength: "7",
        phoneSegment: "556-558、565-567、660-665、667-669、760、762-766",
        memo: "-",
    },
    {
        countryName: "Saint Lucia",
        countryAreaCode: "LC",
        countryNameZH: "圣卢西亚",
        countryCode: "1758",
        phoneLength: "7",
        phoneSegment: "284-287、384、460-461、484-489、518-520、584、712-728",
        memo: "-",
    },
    {
        countryName: "Saint Pierre and Miquelon",
        countryAreaCode: "PM",
        countryNameZH: "圣彼埃尔和密克隆岛",
        countryCode: "508",
        phoneLength: "6",
        phoneSegment: "55",
        memo: "-",
    },
    {
        countryName: "Saint Vincent and The Grenadines",
        countryAreaCode: "VC",
        countryNameZH: "圣文森特和格林纳丁斯",
        countryCode: "1784",
        phoneLength: "7",
        phoneSegment: "430-434、454-455、489-495、526-534",
        memo: "-",
    },
    {
        countryName: "Samoa",
        countryAreaCode: "WS",
        countryNameZH: "萨摩亚",
        countryCode: "685",
        phoneLength: "7",
        phoneSegment: "77",
        memo: "-",
    },
    {
        countryName: "San Marino",
        countryAreaCode: "SM",
        countryNameZH: "圣马力诺",
        countryCode: "378",
        phoneLength: "8",
        phoneSegment: "66",
        memo: "51/55/58开头是虚拟号。549开头是固话。",
    },
    {
        countryName: "Sao Tome and Principe",
        countryAreaCode: "ST",
        countryNameZH: "圣多美和普林西比",
        countryCode: "239",
        phoneLength: "7",
        phoneSegment: "90",
        memo: "2开头是固话。",
    },
    {
        countryName: "Saudi Arabia",
        countryAreaCode: "SA",
        countryNameZH: "沙特阿拉伯",
        countryCode: "966",
        phoneLength: "9",
        phoneSegment: "50、51、53-59",
        memo: "1开头是固话。",
    },
    {
        countryName: "Senegal",
        countryAreaCode: "SN",
        countryNameZH: "塞内加尔",
        countryCode: "221",
        phoneLength: "7",
        phoneSegment: "76、77",
        memo: "3开头是固话。",
    },
    {
        countryName: "Serbia",
        countryAreaCode: "RS",
        countryNameZH: "塞尔维亚",
        countryCode: "381",
        phoneLength: "9",
        phoneSegment: "60-66、68、69、677",
        memo: "1/2/3开头是固话。",
    },
    {
        countryName: "Seychelles",
        countryAreaCode: "SC",
        countryNameZH: "塞舌尔",
        countryCode: "248",
        phoneLength: "7",
        phoneSegment: "5、7",
        memo: "4开头是固话。6开头是虚拟号。",
    },
    {
        countryName: "Sierra Leone",
        countryAreaCode: "SL",
        countryNameZH: "塞拉利昂",
        countryCode: "232",
        phoneLength: "8",
        phoneSegment: "23、30、33、7",
        memo: "22/32/52开头是固话。",
    },
    {
        countryName: "Singapore",
        countryAreaCode: "SG",
        countryNameZH: "新加坡",
        countryCode: "65",
        phoneLength: "8",
        phoneSegment: "8、9",
        memo: "6开头是固话。",
    },
    {
        countryName: "Sint Maarten (Dutch Part)",
        countryAreaCode: "SX",
        countryNameZH: "圣马丁岛（荷兰部分）",
        countryCode: "1721",
        phoneLength: "7",
        phoneSegment: "52、55、58",
        memo: "54开头是固话。",
    },
    {
        countryName: "Slovakia",
        countryAreaCode: "SK",
        countryNameZH: "斯洛伐克",
        countryCode: "421",
        phoneLength: "9",
        phoneSegment: "901-608、910-912、914-918、940、944、948-951",
        memo: "-",
    },
    {
        countryName: "Slovenia",
        countryAreaCode: "SI",
        countryNameZH: "斯洛文尼亚",
        countryCode: "386",
        phoneLength: "8",
        phoneSegment: "20、21、30、31、40、41、49-51、60、61、64、70、71",
        memo: "49/59/81/82/83开头是虚拟号。",
    },
    {
        countryName: "Solomon Islands",
        countryAreaCode: "SB",
        countryNameZH: "所罗门群岛",
        countryCode: "677",
        phoneLength: "7",
        phoneSegment: "72、74、75、86",
        memo: "固话是5位数。",
    },
    {
        countryName: "Somalia",
        countryAreaCode: "SO",
        countryNameZH: "索马里",
        countryCode: "252",
        phoneLength: "9",
        phoneSegment: "61-63、68",
        memo: "-",
    },
    {
        countryName: "South Africa",
        countryAreaCode: "ZA",
        countryNameZH: "南非",
        countryCode: "27",
        phoneLength: "9",
        phoneSegment: "60、72-74、76、78、79、82-84、710-719、741、811-814",
        memo: "-",
    },
    {
        countryName: "South Korea",
        countryAreaCode: "KR",
        countryNameZH: "韩国",
        countryCode: "82",
        phoneLength: "10",
        phoneSegment: "10、11、16-19",
        memo: "-",
    },
    {
        countryName: "Spain",
        countryAreaCode: "ES",
        countryNameZH: "西班牙",
        countryCode: "34",
        phoneLength: "9",
        phoneSegment: "6、7",
        memo: "9开头是固话。",
    },
    {
        countryName: "Sri Lanka",
        countryAreaCode: "LK",
        countryNameZH: "斯里兰卡",
        countryCode: "94",
        phoneLength: "9",
        phoneSegment: "70-72、74-78",
        memo: "-",
    },
    {
        countryName: "Suriname",
        countryAreaCode: "SR",
        countryNameZH: "苏里南",
        countryCode: "597",
        phoneLength: "7",
        phoneSegment: "8",
        memo: "2-6开头是固话，固话是6位数。",
    },
    {
        countryName: "Sweden",
        countryAreaCode: "SE",
        countryNameZH: "瑞典",
        countryCode: "46",
        phoneLength: "9",
        phoneSegment:
            "70、72、76、79、710、733、736、739、7300、7301-7316、73170-73179、7318-7329、7340-7344、73450-73459、7346-7359、7370-7389",
        memo: "-",
    },
    {
        countryName: "Switzerland",
        countryAreaCode: "CH",
        countryNameZH: "瑞士",
        countryCode: "41",
        phoneLength: "9",
        phoneSegment: "74-79",
        memo: "2/3/4/5/6开头是固话。",
    },
    {
        countryName: "Taiwan",
        countryAreaCode: "TW",
        countryNameZH: "中国台湾",
        countryCode: "886",
        phoneLength: "9",
        phoneSegment: "9",
        memo: "2-8开头是固话。",
    },
    {
        countryName: "Tajikistan",
        countryAreaCode: "TJ",
        countryNameZH: "塔吉克斯坦",
        countryCode: "992",
        phoneLength: "9",
        phoneSegment: "9、90、92、93、95-98、910-919",
        memo: "3开头是固话。",
    },
    {
        countryName: "Tanzania",
        countryAreaCode: "TZ",
        countryNameZH: "坦桑尼亚",
        countryCode: "255",
        phoneLength: "9",
        phoneSegment: "62、65-69、71、73-78",
        memo: "41开头是虚拟号。2开头是固话。",
    },
    {
        countryName: "Thailand",
        countryAreaCode: "TH",
        countryNameZH: "泰国",
        countryCode: "66",
        phoneLength: "9",
        phoneSegment: "6、8、9",
        memo: "60/68是虚拟号。",
    },
    {
        countryName: "Timor-Leste",
        countryAreaCode: "TL",
        countryNameZH: "东帝汶",
        countryCode: "670",
        phoneLength: "8",
        phoneSegment: "72-78",
        memo: "2/3/4开头是固话，固话是7位数。71开头是语音邮箱，79开头是寻呼。",
    },
    {
        countryName: "Togo",
        countryAreaCode: "TG",
        countryNameZH: "多哥",
        countryCode: "228",
        phoneLength: "8",
        phoneSegment: "90-92、97-99",
        memo: "2开头是固话。",
    },
    {
        countryName: "Tonga",
        countryAreaCode: "TO",
        countryNameZH: "汤加",
        countryCode: "676",
        phoneLength: "7",
        phoneSegment: "15-19、87-89",
        memo: "固话是5位数。",
    },
    {
        countryName: "Trinidad and Tobago",
        countryAreaCode: "TT",
        countryNameZH: "特立尼达和多巴哥",
        countryCode: "1868",
        phoneLength: "7",
        phoneSegment: "271-299、301-310、312-399、460-469、470-484、490-499、620、678、680-689、710、712-799",
        memo: "-",
    },
    {
        countryName: "Tunisia",
        countryAreaCode: "TN",
        countryNameZH: "突尼斯",
        countryCode: "216",
        phoneLength: "8",
        phoneSegment: "2-5、9",
        memo: "43/45开头是虚拟号。7开头是固话。",
    },
    {
        countryName: "Turkey",
        countryAreaCode: "TR",
        countryNameZH: "土耳其",
        countryCode: "90",
        phoneLength: "10",
        phoneSegment: "50、53-55",
        memo: "-",
    },
    {
        countryName: "Turkmenistan",
        countryAreaCode: "TM",
        countryNameZH: "土库曼斯坦",
        countryCode: "993",
        phoneLength: "8",
        phoneSegment: "65-67",
        memo: "-",
    },
    {
        countryName: "Turks and Caicos Islands",
        countryAreaCode: "TC",
        countryNameZH: "特克斯和凯科斯群岛",
        countryCode: "1649",
        phoneLength: "7",
        phoneSegment: "239、3、431-433、441-443",
        memo: "-",
    },
    {
        countryName: "Uganda",
        countryAreaCode: "UG",
        countryNameZH: "乌干达",
        countryCode: "256",
        phoneLength: "9",
        phoneSegment: "2-4、720、730、740-744、750-764、770-794",
        memo: "2/3/4开头是固话。",
    },
    {
        countryName: "Ukraine",
        countryAreaCode: "UA",
        countryNameZH: "乌克兰",
        countryCode: "380",
        phoneLength: "9",
        phoneSegment: "50、63、66、67、73、91-99",
        memo: "-",
    },
    {
        countryName: "United Arab Emirates",
        countryAreaCode: "AE",
        countryNameZH: "阿拉伯联合酋长国",
        countryCode: "971",
        phoneLength: "9",
        phoneSegment: "50、52、54-56、58",
        memo: "2/3/4/6/7/9开头是固话。",
    },
    {
        countryName: "United Kingdom",
        countryAreaCode: "GB",
        countryNameZH: "英国",
        countryCode: "44",
        phoneLength: "10",
        phoneSegment: "71-75、77-79、7624",
        memo: "-",
    },
    {
        countryName: "United States",
        countryAreaCode: "US",
        countryNameZH: "美国",
        countryCode: "1",
        phoneLength: "10",
        phoneSegment:
            "201-203、205-210、212-220、224、225、228、229、231、234、239、240、248、251-254、256、260、262、267、269、270、272、276、281、301-305、307-310、312-321、323、325、330-332、334、336、337、339、346、347、351、352、360、361、364、380、385、386、401、402、404-410",
        memo: "-",
    },
    {
        countryName: "Uruguay",
        countryAreaCode: "UY",
        countryNameZH: "乌拉圭",
        countryCode: "598",
        phoneLength: "8",
        phoneSegment: "91、93-99",
        memo: "2/4开头是固话。",
    },
    {
        countryName: "Uzbekistan",
        countryAreaCode: "UZ",
        countryNameZH: "乌兹别克斯坦",
        countryCode: "998",
        phoneLength: "9",
        phoneSegment: "33、90、91、93、94、97",
        memo: "-",
    },
    {
        countryName: "Vanuatu",
        countryAreaCode: "VU",
        countryNameZH: "瓦努阿图",
        countryCode: "678",
        phoneLength: "7",
        phoneSegment: "4、5",
        memo: "固话是5位数。",
    },
    {
        countryName: "Venezuela",
        countryAreaCode: "VE",
        countryNameZH: "委内瑞拉",
        countryCode: "58",
        phoneLength: "10",
        phoneSegment: "4、412、414、416、424、426",
        memo: "2开头是固话。",
    },
    {
        countryName: "Vietnam",
        countryAreaCode: "VN",
        countryNameZH: "越南",
        countryCode: "84",
        phoneLength: "9",
        phoneSegment: "3、5、7-9",
        memo: "2开头是固话。",
    },
    {
        countryName: "Virgin Islands, British",
        countryAreaCode: "VG",
        countryNameZH: "英属维尔京群岛",
        countryCode: "1284",
        phoneLength: "7",
        phoneSegment: "300-303、340-347、368、440-445、4966-4969、499、5",
        memo: "-",
    },
    {
        countryName: "Virgin Islands, US",
        countryAreaCode: "VI",
        countryNameZH: "美属维尔京群岛",
        countryCode: "1340",
        phoneLength: "7",
        phoneSegment: "201、212、220、226-228、244、332、344、422、474、513-514、626、642-643、677",
        memo: "-",
    },
    {
        countryName: "Yemen",
        countryAreaCode: "YE",
        countryNameZH: "也门",
        countryCode: "967",
        phoneLength: "9",
        phoneSegment: "7、70-73、77",
        memo: "固话是7位数。",
    },
    {
        countryName: "Zambia",
        countryAreaCode: "ZM",
        countryNameZH: "赞比亚",
        countryCode: "260",
        phoneLength: "9",
        phoneSegment: "9",
        memo: "2开头是固话。",
    },
    {
        countryName: "Zimbabwe",
        countryAreaCode: "ZW",
        countryNameZH: "津巴布韦",
        countryCode: "263",
        phoneLength: "9",
        phoneSegment: "71、773、77",
        memo: "8开头是虚拟号。",
    },
]);

const orderArray = ["86", "852", "853", "886", "60", "63", "95", "65", "856", "66", "84", "81", "82", "62", "971"];

const COUNTRY_CODE_LIST_V1 = COUNTRY_CODE_LIST.filter((item) => orderArray.includes(item.countryCode)).sort(
    (a, b) => orderArray.indexOf(a.countryCode) - orderArray.indexOf(b.countryCode)
);

export { COUNTRY_CODE_LIST };

export default COUNTRY_CODE_LIST_V1;
