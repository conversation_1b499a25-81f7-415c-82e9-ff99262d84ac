/**
 * create by dengjie
 * desc: 西药用法列表
 * create date 2020/4/23
 */

const WesternMedicine = {
    usage: [
        {
            name: "口服",
            type: 1,
            latin: "po",
        },
        {
            name: "含服",
            type: 1,
            latin: "含服",
        },
        {
            name: "嚼服",
            type: 1,
            latin: "嚼服",
        },
        {
            name: "晨服",
            type: 1,
            latin: "晨服",
        },
        {
            name: "餐前服",
            type: 1,
            latin: "餐前服",
        },
        {
            name: "餐中服",
            type: 1,
            latin: "餐中服",
        },
        {
            name: "餐后服",
            type: 1,
            latin: "餐后服",
        },
        {
            name: "睡前服",
            type: 1,
            latin: "睡前服",
        },

        {
            name: "静脉滴注",
            type: 2,
            latin: "ivgtt",
            childType: 0,
        },
        {
            name: "静脉注射",
            type: 2,
            latin: "iv",
            childType: 1,
        },
        {
            name: "肌内注射",
            type: 2,
            latin: "im",
            childType: 1,
        },
        {
            name: "腔内注射",
            type: 2,
            latin: "腔内注射",
            childType: 1,
        },

        {
            name: "雾化吸入",
            type: 2,
            latin: "雾化吸入",
            isNebulizer: true,
            childType: 2,
        },
        {
            name: "皮下注射",
            type: 2,
            childType: 1,
            latin: "ih",
        },
        {
            name: "皮内注射",
            type: 2,
            latin: "id",
            childType: 1,
        },
        {
            name: "穴位注射",
            type: 2,
            latin: "穴位注射",
            childType: 1,
        },

        {
            name: "直肠滴注",
            type: 2,
            latin: "直肠滴注",
            childType: 0,
        },
        {
            name: "局部注射",
            type: 2,
            latin: "局部注射",
            childType: 1,
        },
        {
            name: "局部麻醉",
            type: 2,
            latin: "局部麻醉",
            childType: 1,
        },
        {
            name: "超声透药",
            type: 2,
            latin: "超声透药",
            childType: 1,
        },
        {
            name: "入壶静滴",
            type: 2,
            latin: "入壶静滴",
            childType: 1,
        },
        {
            name: "输液冲管",
            type: 2,
            latin: "输液冲管",
            childType: 1,
        },
        {
            name: "静脉泵入",
            type: 2,
            latin: "静脉泵入",
            childType: 1,
        },
        {
            name: "鼻饲",
            type: 2,
            latin: "鼻饲",
            childType: 1,
        },
        {
            name: "膀胱给药",
            type: 2,
            latin: "膀胱给药",
            childType: 1,
        },

        {
            name: "溶媒用",
            type: 3,
            latin: "溶媒用",
        },
        {
            name: "外用",
            type: 3,
            latin: "us ext",
        },
        {
            name: "滴眼",
            type: 3,
            latin: "滴眼",
        },
        {
            name: "滴鼻",
            type: 3,
            latin: "滴鼻",
        },
        {
            name: "滴耳",
            type: 3,
            latin: "滴耳",
        },
        {
            name: "口腔喷入",
            type: 3,
            latin: "口腔喷入",
        },
        {
            name: "鼻腔喷入",
            type: 3,
            latin: "鼻腔喷入",
        },
        {
            name: "含漱",
            type: 3,
            latin: "含漱",
        },
        {
            name: "涂抹",
            type: 3,
            latin: "涂抹",
        },
        {
            name: "塞肛",
            type: 3,
            latin: "塞肛",
        },
        {
            name: "直肠给药",
            type: 3,
            latin: "直肠给药",
        },
        {
            name: "阴道给药",
            type: 3,
            latin: "阴道给药",
        },
    ],
    allDosageUnit: [
        {
            id: 1,
            name: "片",
            namePY: "pina",
            namePYFirst: "P",
            type: 2,
        },
        {
            id: 2,
            name: "支",
            namePY: "zhi",
            namePYFirst: "Z",
            type: 2,
        },
        {
            id: 3,
            name: "粒",
            namePY: "li",
            namePYFirst: "L",
            type: 2,
        },
        {
            id: 4,
            name: "滴",
            namePY: "di",
            namePYFirst: "D",
            type: 2,
        },
        {
            id: 5,
            name: "袋",
            namePY: "dai",
            namePYFirst: "D",
            type: 2,
        },
        {
            id: 6,
            name: "丸",
            namePY: "wan",
            namePYFirst: "W",
            type: 2,
        },
        {
            id: 7,
            name: "枚",
            namePY: "mei",
            namePYFirst: "M",
            type: 2,
        },
        {
            id: 8,
            name: "板",
            namePY: "ban",
            namePYFirst: "B",
            type: 2,
        },
        {
            id: 9,
            name: "包",
            namePY: "bao",
            namePYFirst: "B",
            type: 2,
        },
        {
            id: 10,
            name: "s",
            namePY: "s",
            namePYFirst: "s",
            type: 0,
        },
        {
            id: 11,
            name: "g",
            namePY: "g",
            namePYFirst: "g",
            type: 1,
        },
        {
            id: 12,
            name: "mg",
            namePY: "mg",
            namePYFirst: "mg",
            type: 1,
        },
        {
            id: 13,
            name: "ml",
            namePY: "ml",
            namePYFirst: "ml",
            type: 1,
        },
        {
            id: 14,
            name: "适量",
            namePY: "shiliang",
            namePYFirst: "SL",
            type: 0,
        },
        {
            id: 15,
            name: "盒",
            namePY: "he",
            namePYFirst: "H",
            type: 2,
        },
        {
            id: 16,
            name: "瓶",
            namePY: "ping",
            namePYFirst: "P",
            type: 2,
        },
        {
            id: 17,
            name: "ug",
            namePY: "ug",
            namePYFirst: "UG",
            type: 1,
        },
        {
            id: 18,
            name: "ul",
            namePY: "ul",
            namePYFirst: "UL",
            type: 1,
        },
        {
            id: 19,
            name: "IU",
            namePY: "iu",
            namePYFirst: "IU",
            type: 1,
        },
    ],
    dosageUnit: [
        {
            name: "g",
        },
        {
            name: "mg",
        },
        {
            name: "ug",
        },
        {
            name: "ng",
        },
        {
            name: "L",
        },
        {
            name: "ml",
        },
        {
            name: "ul",
        },
        {
            name: "nl",
        },
        {
            name: "s",
        },
        {
            name: "U",
        },
        {
            name: "万U",
        },
        {
            name: "IU",
        },
        {
            name: "万IU",
        },
        {
            name: "Bq",
        },
        {
            name: "kBq",
        },
        {
            name: "GBq",
        },
        {
            name: "MBq",
        },
        {
            name: "LSU",
        },
        {
            name: "片",
        },
        {
            name: "粒",
        },
        {
            name: "丸",
        },
        {
            name: "适量",
        },
        {
            name: "单位",
        },
    ],
    dosageFormUnit: [
        {
            id: 1,
            name: "片",
            namePY: "pina",
            namePYFirst: "P",
            type: 2,
        },
        {
            id: 2,
            name: "颗",
            namePY: "ke",
            namePYFirst: "K",
            type: 2,
        },
        {
            id: 3,
            name: "粒",
            namePY: "li",
            namePYFirst: "L",
            type: 2,
        },
        {
            id: 4,
            name: "个",
            namePY: "ge",
            namePYFirst: "G",
            type: 2,
        },
        {
            id: 5,
            name: "支",
            namePY: "zhi",
            namePYFirst: "Z",
            type: 2,
        },
        {
            id: 6,
            name: "只",
            namePY: "zhi",
            namePYFirst: "Z",
            type: 2,
        },
        {
            id: 7,
            name: "丸",
            namePY: "wan",
            namePYFirst: "W",
            type: 2,
        },
        {
            id: 8,
            name: "枚",
            namePY: "mei",
            namePYFirst: "M",
            type: 2,
        },
        {
            id: 9,
            name: "张",
            namePY: "zhang",
            namePYFirst: "Z",
            type: 2,
        },
        {
            id: 10,
            name: "贴",
            namePY: "tie",
            namePYFirst: "T",
            type: 2,
        },
        {
            id: 11,
            name: "条",
            namePY: "tiao",
            namePYFirst: "T",
            type: 2,
        },
        {
            id: 12,
            name: "根",
            namePY: "gen",
            namePYFirst: "G",
            type: 2,
        },
        {
            id: 13,
            name: "板",
            namePY: "ban",
            namePYFirst: "B",
            type: 2,
        },
        {
            id: 14,
            name: "块",
            namePY: "kuai",
            namePYFirst: "K",
            type: 2,
        },
        {
            id: 15,
            name: "滴",
            namePY: "di",
            namePYFirst: "D",
            type: 2,
        },
        {
            id: 16,
            name: "揿",
            namePY: "qin",
            namePYFirst: "Q",
            type: 2,
        },
        {
            id: 17,
            name: "盒",
            namePY: "he",
            namePYFirst: "H",
            type: 2,
        },
        {
            id: 18,
            name: "瓶",
            namePY: "ping",
            namePYFirst: "P",
            type: 2,
        },
        {
            id: 19,
            name: "杯",
            namePY: "bei",
            namePYFirst: "B",
            type: 2,
        },
        {
            id: 20,
            name: "袋",
            namePY: "dai",
            namePYFirst: "D",
            type: 2,
        },
        {
            id: 21,
            name: "包",
            namePY: "bao",
            namePYFirst: "B",
            type: 2,
        },
        {
            id: 22,
            name: "桶",
            namePY: "tong",
            namePYFirst: "T",
            type: 2,
        },
        {
            id: 23,
            name: "罐",
            namePY: "guan",
            namePYFirst: "G",
            type: 2,
        },
        {
            id: 24,
            name: "盆",
            namePY: "pen",
            namePYFirst: "P",
            type: 2,
        },
        {
            id: 25,
            name: "箱",
            namePY: "xiang",
            namePYFirst: "X",
            type: 2,
        },
        {
            id: 26,
            name: "件",
            namePY: "jian",
            namePYFirst: "J",
            type: 2,
        },
        {
            id: 27,
            name: "筒",
            namePY: "tong",
            namePYFirst: "T",
            type: 2,
        },
        {
            id: 28,
            name: "卷",
            namePY: "juan",
            namePYFirst: "J",
            type: 2,
        },
        {
            id: 29,
            name: "捆",
            namePY: "kun",
            namePYFirst: "K",
            type: 2,
        },
        {
            id: 30,
            name: "把",
            namePY: "ba",
            namePYFirst: "B",
            type: 2,
        },
        {
            id: 31,
            name: "套",
            namePY: "tao",
            namePYFirst: "T",
            type: 2,
        },
        {
            id: 32,
            name: "双",
            namePY: "shuang",
            namePYFirst: "S",
            type: 2,
        },
        {
            id: 33,
            name: "米",
            namePY: "米",
            namePYFirst: "M",
            type: 2,
        },
        {
            id: 34,
            name: "付",
            namePY: "fu",
            namePYFirst: "F",
            type: 2,
        },
        {
            id: 35,
            name: "副",
            namePY: "fu",
            namePYFirst: "F",
            type: 2,
        },
        {
            id: 36,
            name: "对",
            namePY: "dui",
            namePYFirst: "D",
            type: 2,
        },
        {
            id: 37,
            name: "台",
            namePY: "tai",
            namePYFirst: "T",
            type: 2,
        },
        {
            id: 38,
            name: "部",
            namePY: "bu",
            namePYFirst: "B",
            type: 2,
        },
        {
            id: 39,
            name: "本",
            namePY: "ben",
            namePYFirst: "B",
            type: 2,
        },
        {
            id: 40,
            name: "碗",
            namePY: "wan",
            namePYFirst: "W",
            type: 2,
        },
        {
            id: 41,
            name: "份",
            namePY: "fen",
            namePYFirst: "F",
            type: 2,
        },
        {
            id: 42,
            name: "g",
            namePY: "g",
            namePYFirst: "G",
            type: 2,
        },
        {
            id: 43,
            name: "mg",
            namePY: "mg",
            namePYFirst: "H",
            type: 2,
        },
        {
            id: 44,
            name: "ml",
            namePY: "ml",
            namePYFirst: "H",
            type: 2,
        },
        {
            id: 45,
            name: "IU",
            namePY: "iu",
            namePYFirst: "IU",
            type: 2,
        },
    ],
    unit: [
        {
            id: 1,
            name: "盒",
            namePY: "he",
            namePYFirst: "H",
        },
        {
            id: 2,
            name: "瓶",
            namePY: "ping",
            namePYFirst: "P",
        },
        {
            id: 3,
            name: "袋",
            namePY: "dai",
            namePYFirst: "D",
        },
        {
            id: 4,
            name: "片",
            namePY: "pian",
            namePYFirst: "P",
        },
        {
            id: 5,
            name: "粒",
            namePY: "li",
            namePYFirst: "L",
        },
        {
            id: 6,
            name: "支",
            namePY: "zhi",
            namePYFirst: "Z",
        },
        {
            id: 7,
            name: "丸",
            namePY: "wan",
            namePYFirst: "W",
        },
        {
            id: 8,
            name: "枚",
            namePY: "mei",
            namePYFirst: "M",
        },
        {
            id: 9,
            name: "板",
            namePY: "ban",
            namePYFirst: "B",
        },
        {
            id: 10,
            name: "罐",
            namePY: "guan",
            namePYFirst: "G",
        },
        {
            id: 11,
            name: "个",
            namePY: "ge",
            namePYFirst: "G",
        },
        {
            id: 12,
            name: "包",
            namePY: "bao",
            namePYFirst: "B",
        },
    ],
    chpUnit: [
        {
            id: 1,
            name: "g",
            namePY: "g",
            namePYFirst: "G",
        },
        {
            id: 2,
            name: "个",
            namePY: "ge",
            namePYFirst: "G",
        },
        {
            id: 3,
            name: "枚",
            namePY: "mei",
            namePYFirst: "M",
        },
        {
            id: 4,
            name: "片",
            namePY: "pian",
            namePYFirst: "P",
        },
        {
            id: 5,
            name: "袋",
            namePY: "dai",
            namePYFirst: "D",
        },
        {
            id: 6,
            name: "罐",
            namePY: "guan",
            namePYFirst: "G",
        },
        {
            id: 7,
            name: "条",
            namePY: "tiao",
            namePYFirst: "T",
        },
        {
            id: 8,
            name: "台",
            namePY: "tai",
            namePYFirst: "T",
        },
        {
            id: 9,
            name: "对",
            namePY: "dui",
            namePYFirst: "D",
        },
        {
            id: 10,
            name: "支",
            namePY: "zhi",
            namePYFirst: "Z",
        },
        {
            id: 11,
            name: "盒",
            namePY: "he",
            namePYFirst: "H",
        },
    ],
    freq: [
        {
            id: 1,
            type: 0,
            en: "qd",
            name: "每天1次",
            namePY: "meitian1ci",
            namePYFirst: "MT1C",
            time: 24,
        },
        {
            id: 2,
            type: 0,
            en: "bid",
            name: "每天2次",
            namePY: "meitian2ci",
            namePYFirst: "MT2C",
            time: 12,
        },
        {
            id: 3,
            type: 0,
            en: "tid",
            name: "每天3次",
            namePY: "meitian3ci",
            namePYFirst: "MT3C",
            time: 8,
        },
        {
            id: 4,
            type: 0,
            en: "qid",
            name: "每天4次",
            namePY: "meitian4ci",
            namePYFirst: "MT4C",
            time: 6,
        },
        {
            id: 5,
            type: 0,
            en: "qod",
            name: "隔日1次",
            namePY: "geri1ci",
            namePYFirst: "GR1C",
            time: 48,
        },
        {
            id: 6,
            type: 0,
            en: "qw",
            name: "每周1次",
            namePY: "meizhou1ci",
            namePYFirst: "MZ1C",
            time: 168,
        },
        {
            id: 7,
            type: 0,
            en: "biw",
            name: "每周2次",
            namePY: "meizhou2ci",
            namePYFirst: "MZ2C",
            time: 84,
        },
        {
            id: 8,
            type: 0,
            en: "hs",
            name: "临睡前",
            namePY: "linshuiqian",
            namePYFirst: "LSQ",
            time: 24,
        },
        {
            id: 9,
            type: 0,
            en: "qn",
            name: "每晚1次",
            namePY: "meiwan1ci",
            namePYFirst: "MW1C",
            time: 24,
        },
        {
            id: 10,
            type: 0,
            en: "q2h",
            name: "每2小时1次",
            namePY: "mei2xiaoshi1ci",
            namePYFirst: "M2XS1C",
            time: 2,
        },
        {
            id: 11,
            type: 0,
            en: "q4h",
            name: "每4小时1次",
            namePY: "mei4xiaoshi1ci",
            namePYFirst: "M4XS1C",
            time: 4,
        },
        {
            id: 12,
            type: 0,
            en: "q6h",
            name: "每6小时1次",
            namePY: "mei6xiaoshi1ci",
            namePYFirst: "M6XS1C",
            time: 6,
        },
        {
            id: 13,
            type: 0,
            en: "q8h",
            name: "每8小时1次",
            namePY: "mei8xiaoshi1ci",
            namePYFirst: "M8XS1C",
            time: 8,
        },
        {
            id: 14,
            type: 0,
            en: "q12h",
            name: "每12小时1次",
            namePY: "mei12xiaoshi1ci",
            namePYFirst: "M12XS1C",
            time: 12,
        },
        {
            id: 15,
            type: 0,
            en: "st",
            name: "立即",
            namePY: "liji",
            namePYFirst: "LJ",
            time: undefined,
        },
        {
            id: 16,
            type: 0,
            en: "sos",
            name: "需要时",
            namePY: "xuyaoshi",
            namePYFirst: "XYS",
            time: undefined,
        },
        {
            id: 17,
            type: 0,
            en: "prn",
            name: "需要时(长期)",
            namePY: "xuyaoshi(changqi)",
            namePYFirst: "XYS(CQ)",
            time: undefined,
        },
        {
            id: 18,
            en: "qnd",
            type: 1,
            name: "N天1次",
            namePY: "ntian1ci",
            namePYFirst: "NT1C",
            hMultiple: 24,
            time: undefined,
            isCustom: true,
        },
        {
            id: 19,
            en: "qnw",
            type: 1,
            name: "N周1次",
            namePY: "nzhou1ci",
            namePYFirst: "NZ1C",
            hMultiple: 24 * 7,
            time: undefined,
            isCustom: true,
        },
        {
            id: 20,
            en: "qnh",
            type: 1,
            name: "N小时1次",
            namePY: "nxiaoshi1ci",
            namePYFirst: "NXS1C",
            hMultiple: 1,
            time: undefined,
            isCustom: true,
        },
    ],
    ivgtt: [10, 15, 20, 30, 40, 50, 60, 70, 80, 90, 100],
    ivgttUnit: "滴/分钟",
    medicinePricingUnit: [
        {
            name: "g",
        },
        {
            name: "克",
        },
        {
            name: "袋",
        },
        {
            name: "包",
        },
        {
            name: "瓶",
        },
        {
            name: "条",
        },
        {
            name: "盒",
        },
        {
            name: "个",
        },
        {
            name: "罐",
        },
        {
            name: "对",
        },
        {
            name: "支",
        },
        {
            name: "片",
        },
        {
            name: "枚",
        },
        {
            name: "贴",
        },
        {
            name: "粒",
        },
        {
            name: "根",
        },
        {
            name: "锭",
        },
        {
            name: "朵",
        },
        {
            name: "ml",
        },
    ],
    medicineDosageFormUnit: [
        {
            name: "片",
        },
        {
            name: "颗",
        },
        {
            name: "粒",
        },
        {
            name: "个",
        },
        {
            name: "支",
        },
        {
            name: "只",
        },
        {
            name: "丸",
        },
        {
            name: "枚",
        },
        {
            name: "张",
        },
        {
            name: "贴",
        },
        {
            name: "包",
        },
        {
            name: "袋",
        },
        {
            name: "条",
        },
        {
            name: "根",
        },
        {
            name: "块",
        },
        {
            name: "板",
        },
        {
            name: "盒",
        },
        {
            name: "瓶",
        },
        {
            name: "桶",
        },
        {
            name: "罐",
        },
        {
            name: "揿",
        },
        {
            name: "g",
        },
        {
            name: "ml",
        },
        {
            name: "IU",
        },
    ],
    // 新建物资、商品单位（最小单位、包装单位）
    materialAndCommodityUnit: [
        {
            id: 1,
            name: "片",
        },
        {
            id: 2,
            name: "颗",
        },
        {
            id: 3,
            name: "粒",
        },
        {
            id: 4,
            name: "个",
        },
        {
            id: 5,
            name: "支",
        },
        {
            id: 6,
            name: "只",
        },
        {
            id: 7,
            name: "丸",
        },
        {
            id: 8,
            name: "枚",
        },
        {
            id: 9,
            name: "张",
        },
        {
            id: 10,
            name: "贴",
        },
        {
            id: 11,
            name: "条",
        },
        {
            id: 12,
            name: "根",
        },
        {
            id: 13,
            name: "板",
        },
        {
            id: 14,
            name: "块",
        },
        {
            id: 15,
            name: "滴",
        },
        {
            id: 16,
            name: "揿",
        },
        {
            id: 17,
            name: "盒",
        },
        {
            id: 18,
            name: "瓶",
        },
        {
            id: 19,
            name: "杯",
        },
        {
            id: 20,
            name: "袋",
        },
        {
            id: 21,
            name: "包",
        },
        {
            id: 22,
            name: "桶",
        },
        {
            id: 23,
            name: "罐",
        },
        {
            id: 24,
            name: "盆",
        },
        {
            id: 25,
            name: "箱",
        },
        {
            id: 26,
            name: "件",
        },
        {
            id: 27,
            name: "筒",
        },
        {
            id: 28,
            name: "卷",
        },
        {
            id: 29,
            name: "捆",
        },
        {
            id: 30,
            name: "把",
        },
        {
            id: 31,
            name: "套",
        },
        {
            id: 32,
            name: "双",
        },
        {
            id: 33,
            name: "米",
        },
        {
            id: 34,
            name: "付",
        },
        {
            id: 35,
            name: "副",
        },
        {
            id: 36,
            name: "对",
        },
        {
            id: 37,
            name: "台",
        },
        {
            id: 38,
            name: "部",
        },
        {
            id: 39,
            name: "本",
        },
        {
            id: 40,
            name: "碗",
        },
        {
            id: 41,
            name: "份",
        },
        {
            id: 42,
            name: "壮",
        },
        {
            id: 43,
            name: "升",
        },
        {
            id: 44,
            name: "g",
        },
        {
            id: 45,
            name: "mg",
        },
        {
            id: 46,
            name: "ml",
        },
        {
            id: 47,
            name: "IU",
        },
    ],
    materialUnit: [
        {
            id: 1,
            name: "片",
            namePY: "pina",
            namePYFirst: "P",
            type: 2,
        },
        {
            id: 2,
            name: "颗",
            namePY: "ke",
            namePYFirst: "K",
            type: 2,
        },
        {
            id: 3,
            name: "粒",
            namePY: "li",
            namePYFirst: "L",
            type: 2,
        },
        {
            id: 4,
            name: "个",
            namePY: "ge",
            namePYFirst: "G",
            type: 2,
        },
        {
            id: 5,
            name: "支",
            namePY: "zhi",
            namePYFirst: "Z",
            type: 2,
        },
        {
            id: 6,
            name: "只",
            namePY: "zhi",
            namePYFirst: "Z",
            type: 2,
        },
        {
            id: 7,
            name: "丸",
            namePY: "wan",
            namePYFirst: "W",
            type: 2,
        },
        {
            id: 8,
            name: "枚",
            namePY: "mei",
            namePYFirst: "M",
            type: 2,
        },
        {
            id: 9,
            name: "张",
            namePY: "zhang",
            namePYFirst: "Z",
            type: 2,
        },
        {
            id: 10,
            name: "贴",
            namePY: "tie",
            namePYFirst: "T",
            type: 2,
        },
        {
            id: 11,
            name: "条",
            namePY: "tiao",
            namePYFirst: "T",
            type: 2,
        },
        {
            id: 12,
            name: "根",
            namePY: "gen",
            namePYFirst: "G",
            type: 2,
        },
        {
            id: 13,
            name: "板",
            namePY: "ban",
            namePYFirst: "B",
            type: 2,
        },
        {
            id: 14,
            name: "块",
            namePY: "kuai",
            namePYFirst: "K",
            type: 2,
        },
        {
            id: 15,
            name: "滴",
            namePY: "di",
            namePYFirst: "D",
            type: 2,
        },
        {
            id: 16,
            name: "揿",
            namePY: "qin",
            namePYFirst: "Q",
            type: 2,
        },
        {
            id: 17,
            name: "盒",
            namePY: "he",
            namePYFirst: "H",
            type: 2,
        },
        {
            id: 18,
            name: "瓶",
            namePY: "ping",
            namePYFirst: "P",
            type: 2,
        },
        {
            id: 19,
            name: "杯",
            namePY: "bei",
            namePYFirst: "B",
            type: 2,
        },
        {
            id: 20,
            name: "袋",
            namePY: "dai",
            namePYFirst: "D",
            type: 2,
        },
        {
            id: 21,
            name: "包",
            namePY: "bao",
            namePYFirst: "B",
            type: 2,
        },
        {
            id: 22,
            name: "桶",
            namePY: "tong",
            namePYFirst: "T",
            type: 2,
        },
        {
            id: 23,
            name: "罐",
            namePY: "guan",
            namePYFirst: "G",
            type: 2,
        },
        {
            id: 24,
            name: "盆",
            namePY: "pen",
            namePYFirst: "P",
            type: 2,
        },
        {
            id: 25,
            name: "箱",
            namePY: "xiang",
            namePYFirst: "X",
            type: 2,
        },
        {
            id: 26,
            name: "件",
            namePY: "jian",
            namePYFirst: "J",
            type: 2,
        },
        {
            id: 27,
            name: "泡",
            namePY: "pao",
            namePYFirst: "P",
            type: 2,
        },
        {
            id: 28,
            name: "喷",
            namePY: "pen",
            namePYFirst: "P",
            type: 2,
        },
        {
            id: 29,
            name: "吸",
            namePY: "xi",
            namePYFirst: "x",
            type: 2,
        },
        {
            id: 30,
            name: "付",
            namePY: "fu",
            namePYFirst: "F",
            type: 2,
        },
        {
            id: 31,
            name: "副",
            namePY: "fu",
            namePYFirst: "F",
            type: 2,
        },
        {
            id: 32,
            name: "对",
            namePY: "dui",
            namePYFirst: "D",
            type: 2,
        },
        {
            id: 33,
            name: "份",
            namePY: "fen",
            namePYFirst: "F",
            type: 2,
        },
        {
            id: 34,
            name: "g",
            namePY: "g",
            namePYFirst: "",
            type: 2,
        },
        {
            id: 35,
            name: "mg",
            namePY: "mg",
            namePYFirst: "mg",
            type: 2,
        },
        {
            id: 36,
            name: "ml",
            namePY: "ml",
            namePYFirst: "ml",
            type: 2,
        },
        {
            id: 37,
            name: "IU",
            namePY: "IU",
            namePYFirst: "IU",
            type: 2,
        },
    ],
    medicineDosageForm: [
        {
            name: "片剂",
            value: "片剂",
        },
        {
            name: "颗粒剂",
            value: "颗粒剂",
        },
        {
            name: "注射剂",
            value: "注射剂",
        },
        {
            name: "胶囊剂",
            value: "胶囊剂",
        },
        {
            name: "丸剂",
            value: "丸剂",
        },
        {
            name: "膏剂",
            value: "膏剂",
        },
        {
            name: "溶液剂",
            value: "溶液剂",
        },
        {
            name: "散剂",
            value: "散剂",
        },
        {
            name: "滴剂",
            value: "滴剂",
        },
        {
            name: "喷雾剂",
            value: "喷雾剂",
        },
        {
            name: "气雾剂",
            value: "气雾剂",
        },
        {
            name: "粉雾剂",
            value: "粉雾剂",
        },
        {
            name: "雾化剂",
            value: "雾化剂",
        },
        {
            name: "栓剂",
            value: "栓剂",
        },
        {
            name: "混悬剂",
            value: "混悬剂",
        },
        {
            name: "凝胶剂",
            value: "凝胶剂",
        },
        {
            name: "胶剂",
            value: "胶剂",
        },
        {
            name: "贴剂",
            value: "贴剂",
        },
        {
            name: "吸入剂",
            value: "吸入剂",
        },
        {
            name: "乳剂",
            value: "乳剂",
        },
        {
            name: "锭剂",
            value: "锭剂",
        },
        {
            name: "原料药",
            value: "原料药",
        },
        {
            name: "糖浆剂",
            value: "糖浆剂",
        },
        {
            name: "茶剂",
            value: "茶剂",
        },
        {
            name: "医疗器械",
            value: "医疗器械",
        },
        {
            name: "医用器械",
            value: "医用器械",
        },
    ],
    prescriptionInfusionDefaultValues: {
        usage: "静脉滴注",
        ivgtt: 60,
        ivgttUnit: "滴/分钟",
        freq: "qd",
    },
    materialDosageFormUnit: [
        {
            id: 1,
            name: "片",
        },
        {
            id: 2,
            name: "颗",
        },
        {
            id: 3,
            name: "粒",
        },
        {
            id: 4,
            name: "个",
        },
        {
            id: 5,
            name: "支",
        },
        {
            id: 6,
            name: "只",
        },
        {
            id: 7,
            name: "丸",
        },
        {
            id: 8,
            name: "枚",
        },
        {
            id: 9,
            name: "张",
        },
        {
            id: 10,
            name: "贴",
        },
        {
            id: 11,
            name: "包",
        },
        {
            id: 12,
            name: "袋",
        },
        {
            id: 13,
            name: "条",
        },
        {
            id: 14,
            name: "根",
        },
        {
            id: 15,
            name: "块",
        },
        {
            id: 16,
            name: "板",
        },
        {
            id: 17,
            name: "盒",
        },
        {
            id: 18,
            name: "瓶",
        },
        {
            id: 19,
            name: "桶",
        },
        {
            id: 20,
            name: "罐",
        },
        {
            id: 21,
            name: "筒",
        },
        {
            id: 22,
            name: "盆",
        },
        {
            id: 23,
            name: "箱",
        },
        {
            id: 24,
            name: "件",
        },
        {
            id: 25,
            name: "卷",
        },
        {
            id: 26,
            name: "捆",
        },
        {
            id: 27,
            name: "把",
        },
        {
            id: 28,
            name: "套",
        },
        {
            id: 29,
            name: "双",
        },
        {
            id: 30,
            name: "米",
        },
        {
            id: 31,
            name: "付",
        },
        {
            id: 32,
            name: "台",
        },
        {
            id: 33,
            name: "部",
        },
        {
            id: 34,
            name: "本",
        },
        {
            id: 35,
            name: "g",
        },
        {
            id: 36,
            name: "ml",
        },
        {
            id: 37,
            name: "份",
        },
    ],
    remarkList: [
        {
            name: "续用",
        },
        {
            name: "皮试阴性",
        },
        {
            name: "首次加倍",
        },
        {
            name: "宜空腹服用",
        },
        {
            name: "服用期勿饮酒",
        },
        {
            name: "服用后勿驾车",
        },
        {
            name: "与抗生素间隔2小时",
        },
    ],
    astList: [
        {
            name: "皮试",
            value: 1,
        },
        {
            name: "续用",
            value: 2,
        },
        {
            name: "免试",
            value: 3,
        },
    ],
};
const InfusionMedicine = {
    usage: [
        {
            type: 1,
            name: "静脉滴注",
        },
        {
            type: 1,
            name: "静脉注射",
        },
        {
            type: 1,
            name: "肌内注射",
        },
        {
            type: 1,
            name: "腔内注射",
        },
        {
            type: 1,
            name: "雾化吸入",
        },
        {
            type: 1,
            name: "皮下注射",
        },
        {
            type: 1,
            name: "皮内注射",
        },
        {
            type: 1,
            name: "穴位注射",
        },
        {
            type: 1,
            name: "直肠滴注",
        },
        {
            type: 1,
            name: "局部注射",
        },
        {
            type: 1,
            name: "局部麻醉",
        },
        {
            type: 1,
            name: "超声透药",
        },
        {
            type: 1,
            name: "入壶静滴",
        },
        {
            type: 1,
            name: "输液冲管",
        },
        {
            type: 1,
            name: "静脉泵入",
        },
        {
            type: 1,
            name: "鼻饲",
        },
        {
            type: 1,
            name: "膀胱给药",
        },
    ],
};
export { WesternMedicine, InfusionMedicine };
