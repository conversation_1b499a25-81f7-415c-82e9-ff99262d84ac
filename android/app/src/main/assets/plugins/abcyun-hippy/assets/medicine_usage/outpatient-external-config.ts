/**
 * create by den<PERSON><PERSON><PERSON>
 * desc:
 * create date 2020/10/26
 */

const ExternalConfig = {
    requirement: {
        label: "帖敷",
        list: [
            { label: "30分钟", value: 30 },
            { label: "1小时", value: 60 },
            { label: "2小时", value: 120 },
            { label: "3小时", value: 180 },
            { label: "4小时", value: 240 },
            { label: "5小时", value: 300 },
            { label: "6小时", value: 360 },
            { label: "7小时", value: 420 },
            { label: "8小时", value: 480 },
            { label: "10小时", value: 600 },
            { label: "12小时", value: 720 },
            { label: "14小时", value: 840 },
            { label: "16小时", value: 960 },
            { label: "18小时", value: 1080 },
            { label: "20小时", value: 1200 },
            { label: "24小时", value: 1440 },
        ],
    },
};

const ExternalPRUsageTypeEnum = Object.freeze({
    tieFu: 0, // 贴敷
    zhenCi: 1, // 针刺
    aiJiu: 2, // 艾灸
    baGuan: 3, // 拔罐
    guaSha: 4, // 刮痧
    tuiNa: 5, // 推拿
});
const TieFuUsageSubTypeEnum = Object.freeze({
    chenPinTie: 0, // 成品贴
    xianPeiTie: 1, // 现配贴
});
const ZhenCiUsageSubTypeEnum = Object.freeze({
    haoZhen: 0, // 毫针
    dianZhen: 1, // 电针
    sanLingZhen: 2, // 三棱针
    piFuZhen: 3, // 皮肤针
    meiHuaZhen: 4, // 梅花针
    qinZhen: 5, // 揿针
});
const AiJiuUsageSubTypeEnum = Object.freeze({
    aiZhuJiu: 0, // 艾炷灸
    aiTiaoJiu: 1, // 艾条灸
    wenZhenJiu: 2, // 温针灸
});

const BaGuanUsageSubTypeEnum = Object.freeze({
    huoGuanFa: 0, // 火罐法
    shuiGuanFa: 1, // 水罐法
    chouQiGuanFa: 2, // 抽气罐法
});

const TuiNaUsageSubTypeEnum = Object.freeze({
    chengRenTuiNa: 0, // 成人推拿
    xiaoErTuiNa: 1, // 小儿推拿
    shouZhiDianXue: 2, // 手指点穴
});

const TieFuSubOptions = [
    {
        value: TieFuUsageSubTypeEnum.chenPinTie,
        label: "成品贴",
    },
    {
        value: TieFuUsageSubTypeEnum.xianPeiTie,
        label: "现配贴",
    },
];

const ZhenCiSubOptions = [
    {
        value: ZhenCiUsageSubTypeEnum.haoZhen,
        label: "毫针",
    },
    {
        value: ZhenCiUsageSubTypeEnum.dianZhen,
        label: "电针",
    },
    {
        value: ZhenCiUsageSubTypeEnum.qinZhen,
        label: "揿针",
    },
    {
        value: ZhenCiUsageSubTypeEnum.sanLingZhen,
        label: "三棱针",
    },
    {
        value: ZhenCiUsageSubTypeEnum.piFuZhen,
        label: "皮肤针",
    },
    {
        value: ZhenCiUsageSubTypeEnum.meiHuaZhen,
        label: "梅花针",
    },
];

const AiJiuSubOptions = [
    {
        value: AiJiuUsageSubTypeEnum.aiZhuJiu,
        label: "艾炷灸",
    },
    {
        value: AiJiuUsageSubTypeEnum.aiTiaoJiu,
        label: "艾条灸",
    },
    {
        value: AiJiuUsageSubTypeEnum.wenZhenJiu,
        label: "温针灸",
    },
];

const BaGuanSubOptions = [
    {
        value: BaGuanUsageSubTypeEnum.huoGuanFa,
        label: "火罐法",
    },
    {
        value: BaGuanUsageSubTypeEnum.shuiGuanFa,
        label: "水罐法",
    },
    {
        value: BaGuanUsageSubTypeEnum.chouQiGuanFa,
        label: "抽气罐法",
    },
];

const TuiNaSubOptions = [
    {
        value: TuiNaUsageSubTypeEnum.chengRenTuiNa,
        label: "成人推拿",
    },
    {
        value: TuiNaUsageSubTypeEnum.xiaoErTuiNa,
        label: "小儿推拿",
    },
    {
        value: TuiNaUsageSubTypeEnum.shouZhiDianXue,
        label: "手指点穴",
    },
];

const UsageTypeOptions = [
    {
        value: ExternalPRUsageTypeEnum.tieFu,
        label: "贴敷",
        children: TieFuSubOptions,
    },
    {
        value: ExternalPRUsageTypeEnum.zhenCi,
        label: "针刺",
        children: ZhenCiSubOptions,
    },
    {
        value: ExternalPRUsageTypeEnum.aiJiu,
        label: "艾灸",
        children: AiJiuSubOptions,
    },
    {
        value: ExternalPRUsageTypeEnum.baGuan,
        label: "拔罐",
        children: BaGuanSubOptions,
    },
    {
        value: ExternalPRUsageTypeEnum.guaSha,
        label: "刮痧",
    },
    {
        value: ExternalPRUsageTypeEnum.tuiNa,
        label: "推拿",
        children: TuiNaSubOptions,
    },
];

const ExternalFreqOptions = [
    {
        id: 1,
        name: "1日1次",
    },
    {
        id: 2,
        name: "1日2次",
    },
    {
        id: 3,
        name: "2日1次",
    },
    {
        id: 4,
        name: "3日1次",
    },
    {
        id: 4,
        name: "4日1次",
    },
    {
        id: 6,
        name: "5日1次",
    },
    {
        id: 7,
        name: "6日1次",
    },
    {
        id: 8,
        name: "1周1次",
    },
    {
        id: 9,
        name: "隔周1次",
    },
    {
        id: 10,
        name: "必要时",
    },
];
// 外治处方贴敷备注列表
const ExternalTieFuRequirement = [
    {
        id: 1,
        name: "贴敷30分钟",
    },
    {
        id: 2,
        name: "贴敷1小时",
    },
    {
        id: 3,
        name: "贴敷2小时",
    },
    {
        id: 4,
        name: "贴敷3小时",
    },
    {
        id: 5,
        name: "贴敷4小时",
    },
    {
        id: 6,
        name: "贴敷5小时",
    },
    {
        id: 7,
        name: "贴敷6小时",
    },
    {
        id: 8,
        name: "贴敷7小时",
    },
    {
        id: 9,
        name: "贴敷8小时",
    },
    {
        id: 10,
        name: "贴敷10小时",
    },
    {
        id: 12,
        name: "贴敷12小时",
    },
    {
        id: 13,
        name: "贴敷14小时",
    },
    {
        id: 14,
        name: "贴敷16小时",
    },
    {
        id: 15,
        name: "贴敷18小时",
    },
    {
        id: 16,
        name: "贴敷20小时",
    },
    {
        id: 17,
        name: "贴敷24小时",
    },
];
// 外治处方针灸备注列表
const ExternalZhenJiuRequirement = [
    {
        id: 1,
        name: "每次10分钟",
    },
    {
        id: 2,
        name: "每次20分钟",
    },
    {
        id: 3,
        name: "每次30分钟",
    },
    {
        id: 4,
        name: "每次40分钟",
    },
    {
        id: 5,
        name: "每次50分钟",
    },
    {
        id: 6,
        name: "每次60分钟",
    },
    {
        id: 7,
        name: "每次70分钟",
    },
    {
        id: 8,
        name: "每次80分钟",
    },
    {
        id: 9,
        name: "每次90分钟",
    },
];

export {
    ExternalConfig,
    ExternalPRUsageTypeEnum,
    TieFuUsageSubTypeEnum,
    ZhenCiUsageSubTypeEnum,
    AiJiuUsageSubTypeEnum,
    UsageTypeOptions,
    TieFuSubOptions,
    ZhenCiSubOptions,
    AiJiuSubOptions,
    ExternalFreqOptions,
    ExternalTieFuRequirement,
    ExternalZhenJiuRequirement,
    BaGuanUsageSubTypeEnum,
    BaGuanSubOptions,
    TuiNaUsageSubTypeEnum,
    TuiNaSubOptions,
};
