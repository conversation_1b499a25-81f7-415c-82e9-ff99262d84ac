{"name": "hippyReactBase", "content": {"./abcyun_clinic_app_hippy/node_modules/object-assign/index.js": {"id": "./node_modules/object-assign/index.js", "buildMeta": {"providedExports": true}}, "./abcyun_clinic_app_hippy/node_modules/react/cjs/react.production.min.js": {"id": "./node_modules/react/cjs/react.production.min.js", "buildMeta": {"providedExports": true}}, "./abcyun_clinic_app_hippy/node_modules/react/index.js": {"id": "./node_modules/react/index.js", "buildMeta": {"providedExports": true}}, "./abcyun_clinic_app_hippy/node_modules/scheduler/cjs/scheduler.production.min.js": {"id": "./node_modules/scheduler/cjs/scheduler.production.min.js", "buildMeta": {"providedExports": true}}, "./abcyun_clinic_app_hippy/node_modules/scheduler/index.js": {"id": "./node_modules/scheduler/index.js", "buildMeta": {"providedExports": true}}, "./abcyun_clinic_app_hippy/node_modules/webpack/buildin/global.js": {"id": "./node_modules/webpack/buildin/global.js", "buildMeta": {"providedExports": true}}, "./abcyun_clinic_app_hippy/node_modules/webpack/buildin/module.js": {"id": "./node_modules/webpack/buildin/module.js", "buildMeta": {"providedExports": true}}, "./abcyun_clinic_app_hippy/scripts/vendor.js": {"id": "./scripts/vendor.js", "buildMeta": {"providedExports": true}}, "./abcyun_clinic_app_hippy/third-party/@hippy/react/dist/index.js": {"id": "./third-party/@hippy/react/dist/index.js", "buildMeta": {"exportsType": "namespace", "providedExports": ["Animated", "Animation", "AnimationSet", "AppRegistry", "AsyncStorage", "BackAndroid", "Clipboard", "ConsoleModule", "Dimensions", "Easing", "Focusable", "Hippy", "HippyEventEmitter", "HippyEventListener", "HippyR<PERSON><PERSON>", "Image", "ImageBackground", "ImageLoaderModule", "ListView", "ListViewItem", "Modal", "Navigator", "NetInfo", "NetworkModule", "PixelRatio", "Platform", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RNfqb", "RNfqbEventEmitter", "RNfqbEventListener", "RNfqbRegister", "RefreshWrapper", "ScrollView", "StyleSheet", "Text", "TextInput", "TimerModule", "UIManagerModule", "View", "ViewPager", "WaterfallView", "WebSocket", "WebView", "callNative", "callNativeWithCallbackId", "callNativeWithPromise", "colorParse", "default", "flushSync", "removeNativeCallback"]}}, "./abcyun_clinic_app_hippy/third-party/react-reconciler/cjs/react-reconciler.production.min.js": {"id": "./third-party/react-reconciler/cjs/react-reconciler.production.min.js", "buildMeta": {"providedExports": true}}, "./abcyun_clinic_app_hippy/third-party/react-reconciler/index.js": {"id": "./third-party/react-reconciler/index.js", "buildMeta": {"providedExports": true}}}}