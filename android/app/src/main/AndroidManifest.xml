<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="cn.abcyun.clinic.app">

    <!-- The INTERNET permission is required for development. Specifically,
         flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-feature android:name="android.hardware.camera" />

    <!--Oppo 权限配置-->

    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />

    <!--HUAWEI 权限配置-->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <!-- 兼容荣耀手机 -->
    <uses-permission android:name="com.hihonor.android.launcher.permission.CHANGE_BADGE" />


    <!--兼容处理push库与主工程sdk版本不一致的问题-->
    <uses-sdk
        android:targetSdkVersion="28"
        android:minSdkVersion="19"
        tools:overrideLibrary="io.github.v7lin.fakepush" />
    <!--OpenSDK的接口可能无法正常拉起微信，从而无法使用微信的部分功能，需要在主工程的AndroidManifest.xml 中增加标签-->
    <queries>
        <package android:name="com.tencent.mm" />
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
    </queries>

    <!-- io.flutter.app.FlutterApplication is an android.app.Application that
         calls FlutterMain.startInitialization(this); in its onCreate method.
         In most cases you can leave this as-is, but you if you want to provide
             additional functionality it is fine to subclass or reimplement
         FlutterApplication and put your custom class here. -->
    <application
        android:requestLegacyExternalStorage="true"
        android:name="cn.abcyun.clinic.app.ClinicApplication"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".MainActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize"
            android:exported="true" >

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="abcyun" />
            </intent-filter>
        </activity>

        <activity
            android:name=".wxapi.WXEntryActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:taskAffinity="cn.abcyun.clinic.app"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <activity
            android:name=".wxapi.WXPayEntryActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:taskAffinity="cn.abcyun.clinic.app"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.FileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />

        </provider>

        <!-- 在自身应用的AndroidManifest.xml文件中添加如下结点 -->
        <!-- 关闭与 移动推送TPNS 应用的联合保活功能，请配置 -->
        <provider
            android:name="com.tencent.android.tpush.XGPushProvider"
            tools:replace="android:authorities"
            android:authorities="cn.abcyun.clinic.app.XGVIP_PUSH_AUTH"
            android:enabled="false"
            android:exported="false" />
        <!-- 关闭与 移动推送TPNS 应用的联合保活功能，请配置 -->
        <provider
            android:name="com.tencent.android.tpush.XGVipPushKAProvider"
            tools:replace="android:exported"
            android:authorities="${applicationId}.AUTH_XGPUSH_KEEPALIVE"
            android:exported="false" />

        <provider
            android:name="com.tencent.android.tpush.SettingsContentProvider"
            tools:replace="android:authorities"
            android:authorities="cn.abcyun.clinic.app.TPUSH_PROVIDER"
            android:exported="false" />


        <receiver
            android:name="cn.abcyun.clinic.app.xgpush.XGMessageReceiver" >
            <intent-filter>

                <action android:name="com.tencent.android.xg.vip.action.PUSH_MESSAGE" />
                <action android:name="com.tencent.android.xg.vip.action.FEEDBACK" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="XG_OLD_ACCESS_ID"
            android:value="**********" />

        <!-- 配置首次启动应用时不自动启动信鸽推送服务进程，直到调用XGPushManager.registerPush接口才开始启动-->
        <meta-data
            android:name="XG_SERVICE_PULL_UP_OFF"
            android:value="true" />

    </application>

</manifest>

