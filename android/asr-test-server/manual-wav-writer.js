const fs = require('fs');
const path = require('path');

/**
 * 手动 WAV 文件写入器
 * 不依赖第三方库，直接写入 WAV 文件头和数据
 */
class ManualWavWriter {
    
    constructor(filepath, config) {
        this.filepath = filepath;
        this.config = config;
        this.audioData = [];
        this.totalBytes = 0;
        
        console.log(`Manual WAV writer created: ${filepath}`);
        console.log(`Config:`, config);
    }
    
    /**
     * 写入音频数据
     * @param {Buffer} audioBuffer 音频数据
     */
    writeAudioData(audioBuffer) {
        this.audioData.push(audioBuffer);
        this.totalBytes += audioBuffer.length;
    }
    
    /**
     * 完成 WAV 文件写入
     */
    finalize() {
        try {
            // 创建 WAV 文件头
            const header = this.createWavHeader();
            
            // 合并所有音频数据
            const combinedAudioData = Buffer.concat(this.audioData);
            
            // 写入文件
            const wavFile = Buffer.concat([header, combinedAudioData]);
            fs.writeFileSync(this.filepath, wavFile);
            
            console.log(`Manual WAV file written: ${this.filepath}`);
            console.log(`Header size: ${header.length} bytes`);
            console.log(`Audio data size: ${combinedAudioData.length} bytes`);
            console.log(`Total file size: ${wavFile.length} bytes`);
            
            // 验证文件
            this.verifyWavFile();
            
            return {
                filepath: this.filepath,
                headerSize: header.length,
                audioDataSize: combinedAudioData.length,
                totalSize: wavFile.length,
                duration: this.calculateDuration(combinedAudioData.length)
            };
            
        } catch (error) {
            console.error('Failed to finalize WAV file:', error);
            return null;
        }
    }
    
    /**
     * 创建 WAV 文件头
     */
    createWavHeader() {
        const sampleRate = this.config.sampleRate;
        const channels = this.config.channels;
        const bitsPerSample = this.config.bitDepth;
        const bytesPerSample = bitsPerSample / 8;
        const byteRate = sampleRate * channels * bytesPerSample;
        const blockAlign = channels * bytesPerSample;
        const audioDataSize = this.totalBytes;
        const fileSize = 36 + audioDataSize;
        
        const header = Buffer.alloc(44);
        
        // RIFF header
        header.write('RIFF', 0, 4, 'ascii');
        header.writeUInt32LE(fileSize, 4);
        header.write('WAVE', 8, 4, 'ascii');
        
        // fmt chunk
        header.write('fmt ', 12, 4, 'ascii');
        header.writeUInt32LE(16, 16); // fmt chunk size
        header.writeUInt16LE(1, 20);  // audio format (PCM)
        header.writeUInt16LE(channels, 22);
        header.writeUInt32LE(sampleRate, 24);
        header.writeUInt32LE(byteRate, 28);
        header.writeUInt16LE(blockAlign, 32);
        header.writeUInt16LE(bitsPerSample, 34);
        
        // data chunk
        header.write('data', 36, 4, 'ascii');
        header.writeUInt32LE(audioDataSize, 40);
        
        console.log('WAV Header created:');
        console.log(`  File size: ${fileSize} bytes`);
        console.log(`  Sample rate: ${sampleRate} Hz`);
        console.log(`  Channels: ${channels}`);
        console.log(`  Bits per sample: ${bitsPerSample}`);
        console.log(`  Byte rate: ${byteRate} bytes/sec`);
        console.log(`  Block align: ${blockAlign}`);
        console.log(`  Audio data size: ${audioDataSize} bytes`);
        
        return header;
    }
    
    /**
     * 验证生成的 WAV 文件
     */
    verifyWavFile() {
        try {
            const buffer = fs.readFileSync(this.filepath);
            
            console.log('\nWAV File Verification:');
            console.log(`File size: ${buffer.length} bytes`);
            
            // 读取文件头
            const chunkId = buffer.toString('ascii', 0, 4);
            const chunkSize = buffer.readUInt32LE(4);
            const format = buffer.toString('ascii', 8, 12);
            const subchunk1Id = buffer.toString('ascii', 12, 16);
            const subchunk1Size = buffer.readUInt32LE(16);
            const audioFormat = buffer.readUInt16LE(20);
            const numChannels = buffer.readUInt16LE(22);
            const sampleRate = buffer.readUInt32LE(24);
            const byteRate = buffer.readUInt32LE(28);
            const blockAlign = buffer.readUInt16LE(32);
            const bitsPerSample = buffer.readUInt16LE(34);
            const subchunk2Id = buffer.toString('ascii', 36, 40);
            const subchunk2Size = buffer.readUInt32LE(40);
            
            console.log(`  RIFF ID: ${chunkId} ${chunkId === 'RIFF' ? '✅' : '❌'}`);
            console.log(`  Format: ${format} ${format === 'WAVE' ? '✅' : '❌'}`);
            console.log(`  Audio Format: ${audioFormat} ${audioFormat === 1 ? '✅' : '❌'}`);
            console.log(`  Sample Rate: ${sampleRate} Hz ${sampleRate === this.config.sampleRate ? '✅' : '❌'}`);
            console.log(`  Channels: ${numChannels} ${numChannels === this.config.channels ? '✅' : '❌'}`);
            console.log(`  Bits per Sample: ${bitsPerSample} ${bitsPerSample === this.config.bitDepth ? '✅' : '❌'}`);
            console.log(`  Byte Rate: ${byteRate} bytes/sec`);
            console.log(`  Data chunk size: ${subchunk2Size} bytes`);
            
            // 计算时长
            const duration = this.calculateDuration(subchunk2Size);
            console.log(`  Duration: ${duration.toFixed(2)} seconds`);
            
            // 分析音频数据
            if (subchunk2Size > 0) {
                const audioData = buffer.slice(44, 44 + Math.min(subchunk2Size, buffer.length - 44));
                this.analyzeAudioSamples(audioData);
            }
            
        } catch (error) {
            console.error('Failed to verify WAV file:', error);
        }
    }
    
    /**
     * 分析音频样本
     * @param {Buffer} audioData 音频数据
     */
    analyzeAudioSamples(audioData) {
        if (audioData.length < 2) return;
        
        console.log('\nAudio Sample Analysis:');
        
        let min = 32767, max = -32768, sum = 0, zeroCount = 0;
        const sampleCount = Math.floor(audioData.length / 2);
        
        // 分析前100个样本
        const samplesToAnalyze = Math.min(100, sampleCount);
        const firstSamples = [];
        
        for (let i = 0; i < samplesToAnalyze; i++) {
            const sample = audioData.readInt16LE(i * 2);
            firstSamples.push(sample);
            
            min = Math.min(min, sample);
            max = Math.max(max, sample);
            sum += Math.abs(sample);
            
            if (sample === 0) zeroCount++;
        }
        
        console.log(`  Total samples: ${sampleCount}`);
        console.log(`  Analyzed samples: ${samplesToAnalyze}`);
        console.log(`  Range: ${min} to ${max}`);
        console.log(`  Average amplitude: ${(sum / samplesToAnalyze).toFixed(1)}`);
        console.log(`  Zero samples: ${zeroCount} (${(zeroCount/samplesToAnalyze*100).toFixed(1)}%)`);
        console.log(`  First 10 samples: [${firstSamples.slice(0, 10).join(', ')}]`);
        
        // 检查音频质量
        if (zeroCount === samplesToAnalyze) {
            console.log('  ❌ All samples are zero - silent audio');
        } else if (zeroCount > samplesToAnalyze * 0.9) {
            console.log('  ⚠️  Too many zero samples - mostly silent');
        } else if (Math.max(Math.abs(min), Math.abs(max)) < 100) {
            console.log('  ⚠️  Very low amplitude - quiet audio');
        } else {
            console.log('  ✅ Audio data looks normal');
        }
    }
    
    /**
     * 计算音频时长
     * @param {number} audioDataSize 音频数据大小（字节）
     */
    calculateDuration(audioDataSize) {
        const bytesPerSample = this.config.bitDepth / 8;
        const totalSamples = audioDataSize / (bytesPerSample * this.config.channels);
        return totalSamples / this.config.sampleRate;
    }
}

/**
 * 创建手动 WAV 写入器的工厂函数
 */
function createManualWavWriter(sessionId, config) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `manual_${sessionId}_${timestamp}.wav`;
    const filepath = path.join('./audio-files', filename);
    
    return new ManualWavWriter(filepath, config);
}

module.exports = { ManualWavWriter, createManualWavWriter };
