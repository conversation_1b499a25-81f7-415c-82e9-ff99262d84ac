{"name": "asr-test-server", "version": "1.0.0", "description": "ASR WebSocket Test Server for ABC Hippy Module", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-client.js", "test-remote": "node test-client.js ws://your-server-ip:8080/asr", "validate": "node audio-validator.js", "diagnose": "node quick-test.js", "postinstall": "echo '\\n🎉 安装完成！\\n\\n启动服务器: npm start\\n管理界面: http://localhost:8080\\nWebSocket: ws://localhost:8080/asr\\n'"}, "keywords": ["asr", "websocket", "speech-recognition", "test-server"], "author": "ABC Team", "license": "MIT", "dependencies": {"ws": "^8.14.2", "express": "^4.18.2", "cors": "^2.8.5", "wav": "^1.0.2", "fs-extra": "^11.1.1"}, "devDependencies": {"nodemon": "^3.0.1"}}