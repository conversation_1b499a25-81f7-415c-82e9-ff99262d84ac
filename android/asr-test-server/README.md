# ASR Test Server

一个用于测试 ABC Hippy ASR 模块的 Node.js WebSocket 服务器。

## 功能特性

- ✅ WebSocket 服务器，支持 ASR 协议
- ✅ 模拟语音识别过程
- ✅ 支持实时和最终识别结果
- ✅ 会话管理
- ✅ 状态监控
- ✅ 错误处理
- ✅ **音频文件保存为 WAV 格式**
- ✅ **音频文件管理和下载**
- ✅ **音频文件统计和清理**
- ✅ **音频诊断和问题检测**
- ✅ **播放速度问题分析**

## 快速开始

### 1. 安装依赖

```bash
cd asr-test-server
npm install
```

### 2. 启动服务器

```bash
# 生产模式
npm start

# 开发模式（自动重启）
npm run dev
```

### 3. 测试连接

```bash
# 测试本地服务器
npm test

# 测试远程服务器
npm run test-remote
```

### 4. 服务器信息

- **WebSocket 地址**: `ws://localhost:8080/asr`
- **HTTP 地址**: `http://localhost:8080`
- **状态监控**: `http://localhost:8080/status`
- **音频文件管理**: `http://localhost:8080/audio-files`
- **诊断报告**: `http://localhost:8080/diagnostics`

## 在 Android 应用中使用

在您的 Android 应用中，将 ASR 服务器地址配置为：

```javascript
const config = {
  serverUrl: 'ws://YOUR_COMPUTER_IP:8080/asr',  // 替换为您的电脑IP
  language: 'zh-CN',
  enableVad: true,
  enableSilentDetection: false,  // 测试时建议关闭
  enableForegroundService: false  // 测试时可以关闭前台服务
};

AbcASR.startRecognize(config);
```

## 获取电脑IP地址

### macOS/Linux
```bash
ifconfig | grep "inet " | grep -v 127.0.0.1
```

### Windows
```cmd
ipconfig | findstr "IPv4"
```

## 模拟识别流程

服务器会模拟真实的语音识别过程：

1. **配置阶段**: 接收配置并返回 `ready` 状态
2. **开始录音**: 返回 `recording` 状态
3. **音频处理**: 接收音频数据，定期返回 `recognizing` 状态
4. **实时结果**: 每2秒返回一个部分识别结果
5. **最终结果**: 识别完成后返回最终结果
6. **停止录音**: 返回 `stopped` 状态

### 模拟识别结果序列

```
"你好"                    (实时结果 1)
"你好，我是"              (实时结果 2)
"你好，我是语音"          (实时结果 3)
"你好，我是语音识别"      (实时结果 4)
"你好，我是语音识别系统"  (最终结果)
```

## 测试输出示例

### 服务器端日志
```
ASR Test Server is running on port 8080
WebSocket endpoint: ws://localhost:8080/asr
HTTP endpoint: http://localhost:8080
Ready to accept connections...

New WebSocket connection from: ::1
[test-session-1640995200000] Session created
[test-session-1640995200000] Config set: { language: 'zh-CN', sampleRate: 16000, channels: 1, enableVad: true }
[test-session-1640995200000] Status: ready - 配置已设置，准备开始识别
[test-session-1640995200000] Recognition started
[test-session-1640995200000] Status: recording - 开始录音
[test-session-1640995200000] Received audio data #1, size: 1024 bytes
[test-session-1640995200000] Sent partial result: "你好"
[test-session-1640995200000] Sent partial result: "你好，我是"
...
```

### 客户端测试日志
```
🚀 开始 ASR WebSocket 测试
=====================================
连接到 ASR 服务器: ws://localhost:8080/asr
✅ WebSocket 连接成功
📤 发送配置: { type: 'config', sessionId: 'test-session-1640995200000', ... }
✅ 状态: ready - 配置已设置，准备开始识别
🎤 开始识别
🎤 状态: recording - 开始录音
📡 已发送 10 个音频数据包
⚡ 实时结果: "你好" (置信度: 0.85)
📡 已发送 20 个音频数据包
⚡ 实时结果: "你好，我是" (置信度: 0.87)
...
🎯 最终结果: "你好，我是语音识别系统" (置信度: 0.95)
✨ 识别完成，准备停止...
⏹️ 停止识别
⏹️ 状态: stopped - 识别已停止
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8080
   
   # 或者使用其他端口
   PORT=9000 npm start
   ```

2. **Android 应用连接失败**
   - 确保手机和电脑在同一网络
   - 检查防火墙设置
   - 使用电脑的实际IP地址，不要使用 localhost

3. **权限错误**
   ```bash
   # 确保有权限监听端口
   sudo npm start  # 如果需要
   ```

### 调试方法

1. **查看服务器状态**
   ```bash
   curl http://localhost:8080/status
   ```

2. **使用浏览器测试**
   - 打开 `http://localhost:8080`
   - 查看服务器信息

3. **网络连通性测试**
   ```bash
   # 从手机浏览器访问
   http://YOUR_COMPUTER_IP:8080
   ```

## 音频文件功能

### 自动保存音频文件

服务器会自动将接收到的音频数据保存为 WAV 文件：

- **文件位置**: `audio-files/` 目录
- **文件命名**: `{sessionId}_{timestamp}.wav`
- **音频格式**: 根据客户端配置（默认 16kHz, 单声道, 16位）

### 音频文件管理 API

#### 1. 获取音频文件列表

```bash
GET http://localhost:8080/audio-files
```

返回：
```json
{
  "files": [
    {
      "filename": "test-session-123_2023-12-01T10-30-00-000Z.wav",
      "filepath": "/path/to/audio-files/test-session-123_2023-12-01T10-30-00-000Z.wav",
      "size": 102400,
      "sizeKB": "100.00",
      "created": "2023-12-01T10:30:00.000Z",
      "modified": "2023-12-01T10:30:15.000Z"
    }
  ],
  "stats": {
    "totalFiles": 5,
    "totalSize": 512000,
    "totalSizeKB": "500.00",
    "totalSizeMB": "0.49",
    "directory": "/path/to/audio-files",
    "oldestFile": {...},
    "newestFile": {...}
  }
}
```

#### 2. 下载音频文件

```bash
GET http://localhost:8080/audio-files/{filename}
```

#### 3. 删除单个音频文件

```bash
DELETE http://localhost:8080/audio-files/{filename}
```

#### 4. 清理所有音频文件

```bash
DELETE http://localhost:8080/audio-files
```

#### 5. 清理旧音频文件

```bash
DELETE http://localhost:8080/audio-files/cleanup/{days}
```

例如：`DELETE http://localhost:8080/audio-files/cleanup/7` 删除7天前的文件

### 音频文件信息

每个录音会话都会生成一个 WAV 文件，包含以下信息：

- **会话ID**: 用于关联识别会话
- **时间戳**: 录音开始时间
- **文件大小**: 字节数和可读格式
- **音频时长**: 根据音频配置计算
- **音频参数**: 采样率、声道数、位深度

### 使用示例

```bash
# 查看所有音频文件
curl http://localhost:8080/audio-files

# 下载特定音频文件
curl -O http://localhost:8080/audio-files/test-session-123_2023-12-01T10-30-00-000Z.wav

# 删除特定音频文件
curl -X DELETE http://localhost:8080/audio-files/test-session-123_2023-12-01T10-30-00-000Z.wav

# 清理7天前的音频文件
curl -X DELETE http://localhost:8080/audio-files/cleanup/7

# 清理所有音频文件
curl -X DELETE http://localhost:8080/audio-files
```

## 音频诊断功能

### 🔍 自动问题检测

服务器会自动分析音频数据并检测常见问题：

- **配置不匹配**: 检查音频配置与WAV文件头是否一致
- **数据包大小异常**: 检测音频数据包大小是否符合预期
- **静音过多**: 检测是否有过多的静音数据
- **文件完整性**: 验证WAV文件是否完整

### 📊 诊断报告 API

#### 1. 获取诊断报告列表

```bash
GET http://localhost:8080/diagnostics
```

返回：
```json
{
  "reports": [
    {
      "filename": "diagnostic_test-session-123_1640995200000.json",
      "sessionId": "test-session-123",
      "timestamp": "2023-12-01T10:30:00.000Z",
      "created": "2023-12-01T10:30:15.000Z",
      "size": 2048,
      "issueCount": 2,
      "hasIssues": true
    }
  ],
  "totalReports": 5,
  "reportsWithIssues": 2
}
```

#### 2. 获取特定诊断报告

```bash
GET http://localhost:8080/diagnostics/{filename}
```

#### 3. 分析现有WAV文件

```bash
POST http://localhost:8080/diagnostics/analyze/{filename}
```

### 🚨 常见问题和解决方案

#### 播放速度过慢问题

如果生成的WAV文件播放速度明显慢于正常语速，可能的原因：

1. **采样率配置错误**
   - 检查Android应用的AudioRecord采样率设置
   - 确保发送给服务器的配置参数正确

2. **音频数据格式问题**
   - 验证音频数据是16-bit PCM格式
   - 检查字节序（Little Endian）

3. **数据包大小异常**
   - 检查每个音频数据包的大小是否符合预期
   - 验证缓冲区大小计算

#### 诊断步骤

1. **查看诊断报告**
   ```bash
   curl http://localhost:8080/diagnostics
   ```

2. **分析WAV文件**
   ```bash
   curl -X POST http://localhost:8080/diagnostics/analyze/your-file.wav
   ```

3. **检查配置匹配**
   查看诊断报告中的 `configComparison` 部分

4. **验证音频参数**
   确认以下参数匹配：
   - 采样率: 16000 Hz
   - 声道数: 1 (单声道)
   - 位深度: 16 bit

### 🛠️ Android 端检查清单

```java
// 确保音频配置正确
int sampleRate = 16000;  // 与服务器配置一致
int channelConfig = AudioFormat.CHANNEL_IN_MONO;
int audioFormat = AudioFormat.ENCODING_PCM_16BIT;

// 创建AudioRecord
AudioRecord audioRecord = new AudioRecord(
    MediaRecorder.AudioSource.MIC,
    sampleRate,
    channelConfig,
    audioFormat,
    bufferSize
);

// 发送配置
AsrConfig config = new AsrConfig();
config.setSampleRate(16000);  // 必须与AudioRecord一致
config.setChannels(1);
config.setAudioFormat(16);
```

### 📈 诊断报告内容

每个诊断报告包含：

- **会话信息**: sessionId, 时间戳, 配置参数
- **音频数据分析**: 数据包数量, 总大小, 平均包大小, 静音检测
- **WAV文件分析**: 文件头信息, 完整性检查, 参数验证
- **配置比较**: 预期配置与实际文件的对比
- **问题检测**: 自动识别的问题和严重程度
- **修复建议**: 针对检测到的问题提供解决方案

### 🔧 使用外部工具验证

#### FFmpeg 分析
```bash
# 安装 FFmpeg
brew install ffmpeg  # macOS

# 分析音频文件
ffmpeg -i session-123.wav -f null -

# 获取详细信息
ffprobe -v quiet -print_format json -show_format -show_streams session-123.wav
```

#### Audacity 可视化
1. 下载 Audacity
2. 打开生成的WAV文件
3. 检查波形和频谱
4. 验证播放速度

详细的问题排查指南请参考 `audio-fix-suggestions.md` 文件。

## 扩展功能

可以根据需要修改 `server.js` 来扩展功能：

- 修改 `mockRecognitionResults` 数组来自定义识别结果
- 调整识别间隔时间
- 添加更多的状态和错误模拟
- 集成真实的语音识别引擎
- 添加音频文件压缩功能
- 实现音频文件上传到云存储

## 部署到云服务器

如果需要部署到云服务器供多人测试：

1. **上传代码到服务器**
2. **安装依赖**: `npm install`
3. **配置防火墙**: 开放 8080 端口
4. **启动服务**: `npm start`
5. **使用公网IP**: `ws://YOUR_SERVER_IP:8080/asr`
