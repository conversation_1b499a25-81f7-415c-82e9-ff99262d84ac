# 音频播放速度问题测试指南

## 🚀 快速测试步骤

### 1. 重新编译 Android 应用
确保使用了修复后的代码：
- `AsrConfig.java` - bufferSize 改为 3200
- `AbcAudioRecorder.java` - 字节序修复和缓冲区改进

### 2. 启动测试服务器
```bash
cd android/asr-test-server
npm start
```

### 3. 录制测试音频
- 使用 Android 应用连接到服务器
- 录制一段 5-10 秒的测试语音
- 观察 Android 日志输出

### 4. 运行快速诊断
```bash
npm run diagnose
```

## 📊 预期的日志输出

### Android 端（修复后）
```
Audio params - sampleRate: 16000, channels: 1, format: 16, bufferSize: 3200
Buffer size adjusted: 1024 -> 3200 (min: 1280, recommended: 3200)
Audio recording started successfully
Audio data #10: 3200 bytes, 100.0ms, expected: 3200 bytes, volume: 45.2
Audio data #20: 3200 bytes, 100.0ms, expected: 3200 bytes, volume: 52.1
```

### 服务器端（修复后）
```
[Server] Audio data #10:
  - Size: 3200 bytes (expected: 3200)
  - Duration: 100.0ms (expected: 100ms)
  - Total: 32000 bytes
  - Expected total duration: 10.00s, elapsed: 10.05s
```

## 🔍 问题诊断检查清单

### ✅ 检查 1: 音频参数配置
- [ ] 采样率: 16000 Hz
- [ ] 声道数: 1 (单声道)
- [ ] 位深度: 16 bit
- [ ] 缓冲区大小: 3200 bytes

### ✅ 检查 2: 数据包大小
- [ ] Android 发送的每个数据包应该是 3200 字节
- [ ] 对应 100ms 的音频数据
- [ ] 发送频率应该是每 100ms 一次

### ✅ 检查 3: WAV 文件验证
运行验证命令：
```bash
npm run validate
```

检查输出：
- [ ] Sample Rate: 16000 Hz ✅
- [ ] Channels: 1 ✅
- [ ] Bit Depth: 16 bit ✅
- [ ] Expected Byte Rate 匹配 ✅
- [ ] 音频内容正常 ✅

### ✅ 检查 4: 播放速度测试
1. 播放生成的 WAV 文件
2. 对比原始语音速度
3. 如果速度正常 = 问题已解决 ✅
4. 如果仍然慢 = 需要进一步调试 ❌

## 🛠️ 故障排除

### 问题 1: 数据包大小仍然不是 3200 字节

**可能原因**:
- Android 应用没有重新编译
- AudioRecord.read() 返回的数据小于缓冲区大小

**解决方案**:
1. 确认重新编译了 Android 应用
2. 检查 Android 日志中的 "Buffer size adjusted" 消息
3. 如果仍有问题，检查 AudioRecord 的最小缓冲区大小

### 问题 2: WAV 文件参数不正确

**可能原因**:
- 服务器端配置解析错误
- 客户端发送的配置参数错误

**解决方案**:
1. 检查服务器日志中的配置信息
2. 验证客户端发送的配置 JSON
3. 确认 Node.js 服务器正确解析了参数

### 问题 3: 播放速度仍然慢

**可能原因**:
- 字节序问题未完全解决
- WAV 文件生成有问题
- 音频播放器的问题

**解决方案**:
1. 使用专业音频软件（如 Audacity）验证文件
2. 检查音频数据的字节序
3. 对比修复前后的 WAV 文件

## 🔧 高级调试

### 使用 FFmpeg 分析
```bash
# 安装 FFmpeg
brew install ffmpeg  # macOS
# 或 apt-get install ffmpeg  # Ubuntu

# 分析 WAV 文件
ffprobe -v quiet -print_format json -show_format -show_streams your-file.wav

# 检查音频流信息
ffmpeg -i your-file.wav -f null -
```

### 使用 Audacity 可视化
1. 下载并安装 Audacity
2. 打开生成的 WAV 文件
3. 检查：
   - 波形是否正常
   - 频谱是否合理
   - 播放速度是否正确

### 对比测试
1. 录制相同内容的音频
2. 使用修复前和修复后的代码
3. 对比生成的 WAV 文件
4. 分析差异

## 📈 性能监控

### 实时监控
启动服务器后访问：
- 管理界面: http://localhost:8080
- 诊断报告: http://localhost:8080/diagnostics
- 文件验证: http://localhost:8080/validate

### API 测试
```bash
# 获取最新诊断报告
curl http://localhost:8080/diagnostics | jq '.reports[0]'

# 验证特定文件
curl -X POST http://localhost:8080/validate/your-file.wav

# 分析音频数据
curl -X POST http://localhost:8080/diagnostics/analyze/your-file.wav
```

## 📋 测试报告模板

### 测试环境
- Android 设备: ___________
- Android 版本: ___________
- 应用版本: ___________
- 服务器版本: ___________

### 测试结果
- [ ] Android 日志显示正确的缓冲区大小 (3200 bytes)
- [ ] 服务器接收到正确大小的数据包
- [ ] WAV 文件验证通过
- [ ] 播放速度正常

### 问题记录
如果仍有问题，请记录：
1. 具体的错误现象
2. Android 日志输出
3. 服务器日志输出
4. WAV 文件验证结果
5. 诊断报告内容

### 解决方案
记录最终的解决方案和配置。

## 🎯 成功标准

测试成功的标准：
1. ✅ Android 发送 3200 字节的数据包
2. ✅ 服务器接收到正确大小的数据
3. ✅ WAV 文件参数正确 (16kHz, 1ch, 16bit)
4. ✅ 播放速度与原始语音一致
5. ✅ 诊断报告无错误

当所有标准都满足时，音频播放速度问题应该完全解决。
