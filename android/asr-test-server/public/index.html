<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ASR Test Server - 音频文件管理</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #007AFF;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007AFF;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056CC;
        }
        .btn.danger {
            background: #FF3B30;
        }
        .btn.danger:hover {
            background: #D70015;
        }
        .files-table {
            width: 100%;
            border-collapse: collapse;
        }
        .files-table th,
        .files-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .files-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .files-table tr:hover {
            background: #f8f9fa;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            background: #FFE6E6;
            color: #D70015;
            padding: 15px;
            margin: 20px;
            border-radius: 6px;
            border: 1px solid #FFB3B3;
        }
        .success {
            background: #E6F7E6;
            color: #006600;
            padding: 15px;
            margin: 20px;
            border-radius: 6px;
            border: 1px solid #B3E6B3;
        }
        .file-actions {
            display: flex;
            gap: 5px;
        }
        .file-actions button {
            padding: 5px 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 ASR Test Server</h1>
            <p>音频文件管理系统</p>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-value" id="totalFiles">-</div>
                <div class="stat-label">音频文件总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalSize">-</div>
                <div class="stat-label">总大小 (MB)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeSessions">-</div>
                <div class="stat-label">活跃会话</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="uptime">-</div>
                <div class="stat-label">运行时间 (分钟)</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="refreshData()">🔄 刷新</button>
            <button class="btn" onclick="cleanupOldFiles()">🗑️ 清理7天前文件</button>
            <button class="btn danger" onclick="cleanupAllFiles()">⚠️ 清理所有文件</button>
            <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #eee;">
                <strong>控制命令：</strong>
                <button class="btn" id="startBtn" onclick="sendControlCommand('start')">🟢 启动服务</button>
                <button class="btn" id="stopBtn" onclick="sendControlCommand('stop')">🔴 停止服务</button>
                <button class="btn" onclick="sendControlCommand('restart')">🔄 重启服务</button>
                <button class="btn" onclick="sendControlCommand('status')">📊 查询状态</button>
            </div>
        </div>

        <div id="message"></div>

        <div id="loading" class="loading">
            正在加载数据...
        </div>

        <div id="filesContainer" style="display: none;">
            <table class="files-table">
                <thead>
                    <tr>
                        <th>文件名</th>
                        <th>大小</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="filesTableBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        let audioFiles = [];
        let serverStats = {};

        // 页面加载时获取数据
        window.onload = function() {
            refreshData();
            // 每30秒自动刷新
            setInterval(refreshData, 30000);
        };

        // 刷新数据
        async function refreshData() {
            try {
                await Promise.all([
                    loadAudioFiles(),
                    loadServerStatus()
                ]);
                updateUI();
            } catch (error) {
                showMessage('加载数据失败: ' + error.message, 'error');
            }
        }

        // 加载音频文件列表
        async function loadAudioFiles() {
            const response = await fetch('/audio-files');
            if (!response.ok) {
                throw new Error('获取音频文件列表失败');
            }
            const data = await response.json();
            audioFiles = data.files;
            return data;
        }

        // 加载服务器状态
        async function loadServerStatus() {
            const response = await fetch('/status');
            if (!response.ok) {
                throw new Error('获取服务器状态失败');
            }
            serverStats = await response.json();
            return serverStats;
        }

        // 更新UI
        function updateUI() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('filesContainer').style.display = 'block';

            // 更新统计信息
            document.getElementById('totalFiles').textContent = audioFiles.length;
            document.getElementById('totalSize').textContent = 
                audioFiles.reduce((sum, file) => sum + file.size, 0) / 1024 / 1024;
            document.getElementById('activeSessions').textContent = serverStats.activeSessions || 0;
            document.getElementById('uptime').textContent = Math.floor((serverStats.uptime || 0) / 60);

            // 更新文件列表
            updateFilesTable();
        }

        // 更新文件表格
        function updateFilesTable() {
            const tbody = document.getElementById('filesTableBody');
            tbody.innerHTML = '';

            if (audioFiles.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: #666;">暂无音频文件</td></tr>';
                return;
            }

            audioFiles.forEach(file => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${file.filename}</td>
                    <td>${file.sizeKB} KB</td>
                    <td>${new Date(file.created).toLocaleString()}</td>
                    <td>
                        <div class="file-actions">
                            <button class="btn" onclick="downloadFile('${file.filename}')">下载</button>
                            <button class="btn danger" onclick="deleteFile('${file.filename}')">删除</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 下载文件
        function downloadFile(filename) {
            window.open(`/audio-files/${filename}`, '_blank');
        }

        // 删除文件
        async function deleteFile(filename) {
            if (!confirm(`确定要删除文件 "${filename}" 吗？`)) {
                return;
            }

            try {
                const response = await fetch(`/audio-files/${filename}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    showMessage('文件删除成功', 'success');
                    refreshData();
                } else {
                    throw new Error('删除失败');
                }
            } catch (error) {
                showMessage('删除文件失败: ' + error.message, 'error');
            }
        }

        // 清理旧文件
        async function cleanupOldFiles() {
            if (!confirm('确定要清理7天前的音频文件吗？')) {
                return;
            }

            try {
                const response = await fetch('/audio-files/cleanup/7', {
                    method: 'DELETE'
                });

                if (response.ok) {
                    const result = await response.json();
                    showMessage(`清理完成，删除了 ${result.deletedCount} 个文件`, 'success');
                    refreshData();
                } else {
                    throw new Error('清理失败');
                }
            } catch (error) {
                showMessage('清理旧文件失败: ' + error.message, 'error');
            }
        }

        // 清理所有文件
        async function cleanupAllFiles() {
            if (!confirm('⚠️ 确定要删除所有音频文件吗？此操作不可恢复！')) {
                return;
            }

            try {
                const response = await fetch('/audio-files', {
                    method: 'DELETE'
                });

                if (response.ok) {
                    const result = await response.json();
                    showMessage(`清理完成，删除了 ${result.deletedCount} 个文件`, 'success');
                    refreshData();
                } else {
                    throw new Error('清理失败');
                }
            } catch (error) {
                showMessage('清理所有文件失败: ' + error.message, 'error');
            }
        }

        // 发送控制命令 (通过HTTP)
        async function sendControlCommand(command) {
            try {
                showMessage(`正在发送 ${command} 命令...`, 'success');

                const response = await fetch('/control-command', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        command: command,
                        timestamp: Date.now()
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('控制命令响应:', result);

                if (result.success) {
                    showMessage(`命令 ${command} 执行成功: ${result.message}`, 'success');

                    // 如果是查询状态命令，显示详细信息
                    if (command === 'status' && result.data) {
                        showStatusDetails(result.data);
                    }
                } else {
                    showMessage(`命令 ${command} 执行失败: ${result.message}`, 'error');
                }

                // 刷新数据以获取最新状态
                setTimeout(refreshData, 1000);

            } catch (error) {
                console.error('发送控制命令失败:', error);
                showMessage('发送命令失败: ' + error.message, 'error');
            }
        }

        // 显示状态详情
        function showStatusDetails(statusData) {
            let details = '服务状态详情:\n';
            details += `- 活跃会话: ${statusData.activeSessions || 0}\n`;
            details += `- 运行时间: ${Math.floor((statusData.uptime || 0) / 60)} 分钟\n`;
            details += `- 内存使用: ${Math.round((statusData.memory?.used || 0) / 1024 / 1024)} MB\n`;

            if (statusData.sessions && statusData.sessions.length > 0) {
                details += `- 会话列表:\n`;
                statusData.sessions.forEach(session => {
                    details += `  * ${session.sessionId}: ${session.isRecording ? '录音中' : '空闲'}\n`;
                });
            }

            alert(details);
        }

        // 显示消息
        function showMessage(message, type = 'success') {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;

            // 3秒后自动隐藏
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
