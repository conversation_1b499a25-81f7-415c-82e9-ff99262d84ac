#!/usr/bin/env node

const AudioValidator = require('./audio-validator');
const fs = require('fs');
const path = require('path');

/**
 * 快速测试脚本
 * 用于快速诊断音频播放速度问题
 */

console.log('🔍 ASR Audio Speed Diagnosis Tool');
console.log('================================\n');

// 检查音频文件目录
const audioDir = './audio-files';
if (!fs.existsSync(audioDir)) {
    console.log('❌ Audio files directory not found. Please record some audio first.');
    process.exit(1);
}

// 获取最新的WAV文件
const files = fs.readdirSync(audioDir);
const wavFiles = files.filter(file => file.endsWith('.wav'))
    .map(file => ({
        name: file,
        path: path.join(audioDir, file),
        mtime: fs.statSync(path.join(audioDir, file)).mtime
    }))
    .sort((a, b) => b.mtime - a.mtime);

if (wavFiles.length === 0) {
    console.log('❌ No WAV files found. Please record some audio first.');
    process.exit(1);
}

console.log(`📁 Found ${wavFiles.length} WAV file(s). Analyzing the latest one...\n`);

// 分析最新的文件
const latestFile = wavFiles[0];
console.log(`🎵 Analyzing: ${latestFile.name}`);
console.log(`📅 Created: ${latestFile.mtime.toLocaleString()}\n`);

const validation = AudioValidator.validateWavFile(latestFile.path);

// 显示验证结果
if (validation.valid) {
    console.log('✅ WAV file is structurally valid');
} else {
    console.log('❌ WAV file has issues:');
    validation.issues.forEach(issue => {
        console.log(`   - ${issue}`);
    });
}

// 显示详细分析
if (validation.analysis) {
    const analysis = validation.analysis;
    
    console.log('\n📊 Audio Analysis:');
    console.log('==================');
    
    if (analysis.header) {
        const h = analysis.header;
        console.log(`Sample Rate: ${h.sampleRate} Hz ${h.sampleRate === 16000 ? '✅' : '❌ (expected 16000)'}`);
        console.log(`Channels: ${h.numChannels} ${h.numChannels === 1 ? '✅' : '❌ (expected 1)'}`);
        console.log(`Bit Depth: ${h.bitsPerSample} bit ${h.bitsPerSample === 16 ? '✅' : '❌ (expected 16)'}`);
        console.log(`Byte Rate: ${h.byteRate} bytes/sec`);
        
        // 检查字节率是否正确
        const expectedByteRate = h.sampleRate * h.numChannels * (h.bitsPerSample / 8);
        console.log(`Expected Byte Rate: ${expectedByteRate} ${h.byteRate === expectedByteRate ? '✅' : '❌'}`);
    }
    
    console.log(`\nFile Size: ${validation.fileSize} bytes`);
    console.log(`Audio Data Size: ${analysis.audioDataSize} bytes`);
    console.log(`Duration: ${analysis.durationSeconds.toFixed(2)} seconds`);
    
    if (analysis.audioStats) {
        const stats = analysis.audioStats;
        console.log(`\n🔊 Audio Content:`);
        console.log(`Sample Count: ${stats.sampleCount}`);
        console.log(`Peak Level: ${stats.peakLevel} (max: 32767)`);
        console.log(`Average Level: ${stats.average.toFixed(1)}`);
        console.log(`Silent Ratio: ${(stats.silentRatio * 100).toFixed(1)}%`);
        
        // 检查是否有音频内容
        if (stats.peakLevel < 100) {
            console.log('⚠️  Audio seems very quiet or silent');
        } else if (stats.silentRatio > 0.8) {
            console.log('⚠️  Too much silence detected');
        } else {
            console.log('✅ Audio content looks normal');
        }
    }
}

// 速度问题诊断
console.log('\n🚀 Speed Issue Diagnosis:');
console.log('=========================');

if (validation.analysis && validation.analysis.header) {
    const h = validation.analysis.header;
    const duration = validation.analysis.durationSeconds;
    
    // 计算预期的数据包数量和大小
    const expectedPacketSize = 3200; // 100ms at 16kHz, mono, 16-bit
    const expectedPacketsPerSecond = 10;
    const expectedTotalPackets = Math.ceil(duration * expectedPacketsPerSecond);
    const expectedTotalSize = expectedTotalPackets * expectedPacketSize;
    
    console.log(`Expected packet size: ${expectedPacketSize} bytes (100ms)`);
    console.log(`Expected packets: ${expectedTotalPackets} for ${duration.toFixed(2)}s`);
    console.log(`Expected total size: ${expectedTotalSize} bytes`);
    console.log(`Actual total size: ${validation.analysis.audioDataSize} bytes`);
    
    const sizeRatio = validation.analysis.audioDataSize / expectedTotalSize;
    console.log(`Size ratio: ${sizeRatio.toFixed(2)} ${Math.abs(sizeRatio - 1.0) < 0.1 ? '✅' : '❌'}`);
    
    if (sizeRatio < 0.9) {
        console.log('⚠️  File is smaller than expected - possible data loss');
    } else if (sizeRatio > 1.1) {
        console.log('⚠️  File is larger than expected - possible duplicate data');
    }
    
    // 检查采样率问题
    if (h.sampleRate !== 16000) {
        console.log(`❌ Sample rate issue: ${h.sampleRate} Hz (should be 16000 Hz)`);
        console.log('   This will cause playback speed issues!');
    }
    
    // 检查字节序问题
    if (validation.analysis.audioStats) {
        const stats = validation.analysis.audioStats;
        if (stats.min === stats.max && stats.min === 0) {
            console.log('❌ All samples are zero - possible byte order issue');
        } else if (Math.abs(stats.min) > 32767 || Math.abs(stats.max) > 32767) {
            console.log('❌ Sample values out of range - possible byte order issue');
        }
    }
}

// 建议
console.log('\n💡 Recommendations:');
console.log('===================');

if (!validation.valid) {
    console.log('1. Fix WAV file structure issues first');
}

if (validation.analysis && validation.analysis.header) {
    const h = validation.analysis.header;
    
    if (h.sampleRate !== 16000) {
        console.log('2. Check Android AudioRecord sample rate configuration');
        console.log('   Ensure it matches the server configuration (16000 Hz)');
    }
    
    if (h.numChannels !== 1) {
        console.log('3. Check channel configuration (should be mono/1 channel)');
    }
    
    if (h.bitsPerSample !== 16) {
        console.log('4. Check bit depth configuration (should be 16-bit)');
    }
}

console.log('5. Check Android logs for audio data packet sizes');
console.log('6. Verify that packets are consistently 3200 bytes (100ms)');
console.log('7. Test with the updated Android code that fixes byte order');

// 播放建议
console.log('\n🎵 To test playback speed:');
console.log('==========================');
console.log(`1. Play the file: ${latestFile.name}`);
console.log('2. Compare with original speech speed');
console.log('3. If still slow, check server-side WAV generation');
console.log('4. Use audio editing software to verify sample rate');

console.log('\n✅ Diagnosis complete!');
