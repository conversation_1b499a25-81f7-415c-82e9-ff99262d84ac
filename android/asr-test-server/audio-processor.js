const fs = require('fs-extra');
const path = require('path');
const wav = require('wav');

/**
 * 音频处理器
 * 负责将接收到的音频数据保存为 WAV 文件
 */
class AudioProcessor {
    constructor() {
        this.audioDir = path.join(__dirname, 'audio-files');
        this.ensureAudioDirectory();
    }
    
    /**
     * 确保音频目录存在
     */
    async ensureAudioDirectory() {
        try {
            await fs.ensureDir(this.audioDir);
            console.log(`Audio directory ensured: ${this.audioDir}`);
        } catch (error) {
            console.error('Failed to create audio directory:', error);
        }
    }
    
    /**
     * 创建 WAV 文件写入器
     * @param {string} sessionId 会话ID
     * @param {Object} config 音频配置
     * @returns {Object} WAV 写入器和文件路径
     */
    createWavWriter(sessionId, config) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${sessionId}_${timestamp}.wav`;
        const filepath = path.join(this.audioDir, filename);

        // WAV 文件配置 - 确保参数正确
        const wavConfig = {
            sampleRate: parseInt(config.sampleRate) || 16000,
            channels: parseInt(config.channels) || 1,
            bitDepth: parseInt(config.audioFormat) || 16
        };

        console.log(`Creating WAV writer for ${filename} with config:`, wavConfig);
        console.log(`Expected audio format: ${wavConfig.sampleRate}Hz, ${wavConfig.channels} channel(s), ${wavConfig.bitDepth}-bit`);

        // 创建 WAV 写入器
        const writer = new wav.FileWriter(filepath, wavConfig);

        return {
            writer: writer,
            filepath: filepath,
            filename: filename,
            config: wavConfig,
            bytesWritten: 0,
            audioDataCount: 0,
            startTime: Date.now()
        };
    }
    
    /**
     * 写入音频数据到 WAV 文件
     * @param {Object} wavWriter WAV 写入器对象
     * @param {string} base64Data Base64 编码的音频数据
     */
    writeAudioData(wavWriter, base64Data) {
        try {
            // 将 Base64 数据转换为 Buffer
            const audioBuffer = Buffer.from(base64Data, 'base64');

            // 验证音频数据大小
            const expectedBytesPerSample = wavWriter.config.bitDepth / 8;
            const expectedBytesPerFrame = expectedBytesPerSample * wavWriter.config.channels;

            if (audioBuffer.length % expectedBytesPerFrame !== 0) {
                console.warn(`Audio data size mismatch: ${audioBuffer.length} bytes, expected multiple of ${expectedBytesPerFrame}`);
            }

            // 详细的音频数据分析
            const expectedPacketSize = wavWriter.config.sampleRate * wavWriter.config.channels * expectedBytesPerSample / 10; // 100ms
            const actualDurationMs = (audioBuffer.length / expectedBytesPerFrame) / wavWriter.config.sampleRate * 1000;

            // 写入 WAV 文件
            wavWriter.writer.write(audioBuffer);
            wavWriter.bytesWritten += audioBuffer.length;
            wavWriter.audioDataCount++;

            // 每10个数据包打印一次调试信息（更频繁的调试）
            if (wavWriter.audioDataCount % 10 === 0) {
                const elapsedMs = Date.now() - wavWriter.startTime;
                const expectedDurationMs = this.calculateDuration(wavWriter.bytesWritten, wavWriter.config) * 1000;
                console.log(`[Server] Audio data #${wavWriter.audioDataCount}:`);
                console.log(`  - Size: ${audioBuffer.length} bytes (expected: ${expectedPacketSize})`);
                console.log(`  - Duration: ${actualDurationMs.toFixed(1)}ms (expected: 100ms)`);
                console.log(`  - Total: ${wavWriter.bytesWritten} bytes`);
                console.log(`  - Expected total duration: ${(expectedDurationMs/1000).toFixed(2)}s, elapsed: ${(elapsedMs/1000).toFixed(2)}s`);

                // 检查时序问题
                const timingRatio = expectedDurationMs / elapsedMs;
                if (timingRatio < 0.8 || timingRatio > 1.2) {
                    console.warn(`  - ⚠️  Timing issue detected! Ratio: ${timingRatio.toFixed(2)} (should be ~1.0)`);
                }
            }

            return true;
        } catch (error) {
            console.error('Failed to write audio data:', error);
            return false;
        }
    }
    
    /**
     * 完成 WAV 文件写入
     * @param {Object} wavWriter WAV 写入器对象
     */
    finishWavFile(wavWriter) {
        try {
            wavWriter.writer.end();
            
            const fileSizeKB = (wavWriter.bytesWritten / 1024).toFixed(2);
            const durationSeconds = this.calculateDuration(wavWriter.bytesWritten, wavWriter.config);
            
            console.log(`WAV file completed: ${wavWriter.filename}`);
            console.log(`  - Size: ${fileSizeKB} KB`);
            console.log(`  - Duration: ${durationSeconds.toFixed(2)} seconds`);
            console.log(`  - Path: ${wavWriter.filepath}`);
            
            return {
                filename: wavWriter.filename,
                filepath: wavWriter.filepath,
                sizeBytes: wavWriter.bytesWritten,
                sizeKB: parseFloat(fileSizeKB),
                durationSeconds: durationSeconds,
                config: wavWriter.config
            };
        } catch (error) {
            console.error('Failed to finish WAV file:', error);
            return null;
        }
    }
    
    /**
     * 计算音频时长
     * @param {number} bytesWritten 写入的字节数
     * @param {Object} config 音频配置
     * @returns {number} 时长（秒）
     */
    calculateDuration(bytesWritten, config) {
        const bytesPerSample = config.bitDepth / 8;
        const bytesPerSecond = config.sampleRate * config.channels * bytesPerSample;
        return bytesWritten / bytesPerSecond;
    }
    
    /**
     * 获取音频文件列表
     * @returns {Array} 音频文件信息列表
     */
    async getAudioFilesList() {
        try {
            const files = await fs.readdir(this.audioDir);
            const wavFiles = files.filter(file => file.endsWith('.wav'));
            
            const fileInfos = await Promise.all(
                wavFiles.map(async (filename) => {
                    const filepath = path.join(this.audioDir, filename);
                    const stats = await fs.stat(filepath);
                    
                    return {
                        filename: filename,
                        filepath: filepath,
                        size: stats.size,
                        sizeKB: (stats.size / 1024).toFixed(2),
                        created: stats.birthtime,
                        modified: stats.mtime
                    };
                })
            );
            
            // 按创建时间倒序排列
            fileInfos.sort((a, b) => b.created - a.created);
            
            return fileInfos;
        } catch (error) {
            console.error('Failed to get audio files list:', error);
            return [];
        }
    }
    
    /**
     * 删除音频文件
     * @param {string} filename 文件名
     * @returns {boolean} 是否删除成功
     */
    async deleteAudioFile(filename) {
        try {
            const filepath = path.join(this.audioDir, filename);
            await fs.remove(filepath);
            console.log(`Audio file deleted: ${filename}`);
            return true;
        } catch (error) {
            console.error(`Failed to delete audio file ${filename}:`, error);
            return false;
        }
    }
    
    /**
     * 清理所有音频文件
     * @returns {number} 删除的文件数量
     */
    async cleanupAudioFiles() {
        try {
            const files = await this.getAudioFilesList();
            let deletedCount = 0;
            
            for (const file of files) {
                if (await this.deleteAudioFile(file.filename)) {
                    deletedCount++;
                }
            }
            
            console.log(`Cleaned up ${deletedCount} audio files`);
            return deletedCount;
        } catch (error) {
            console.error('Failed to cleanup audio files:', error);
            return 0;
        }
    }
    
    /**
     * 清理旧的音频文件（超过指定天数）
     * @param {number} days 保留天数
     * @returns {number} 删除的文件数量
     */
    async cleanupOldAudioFiles(days = 7) {
        try {
            const files = await this.getAudioFilesList();
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);
            
            let deletedCount = 0;
            
            for (const file of files) {
                if (file.created < cutoffDate) {
                    if (await this.deleteAudioFile(file.filename)) {
                        deletedCount++;
                    }
                }
            }
            
            console.log(`Cleaned up ${deletedCount} old audio files (older than ${days} days)`);
            return deletedCount;
        } catch (error) {
            console.error('Failed to cleanup old audio files:', error);
            return 0;
        }
    }
    
    /**
     * 获取音频目录统计信息
     * @returns {Object} 统计信息
     */
    async getAudioDirectoryStats() {
        try {
            const files = await this.getAudioFilesList();
            const totalSize = files.reduce((sum, file) => sum + file.size, 0);
            
            return {
                totalFiles: files.length,
                totalSize: totalSize,
                totalSizeKB: (totalSize / 1024).toFixed(2),
                totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
                directory: this.audioDir,
                oldestFile: files.length > 0 ? files[files.length - 1] : null,
                newestFile: files.length > 0 ? files[0] : null
            };
        } catch (error) {
            console.error('Failed to get audio directory stats:', error);
            return {
                totalFiles: 0,
                totalSize: 0,
                totalSizeKB: '0',
                totalSizeMB: '0',
                directory: this.audioDir,
                oldestFile: null,
                newestFile: null
            };
        }
    }
}

module.exports = AudioProcessor;
