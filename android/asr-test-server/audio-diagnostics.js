const fs = require('fs');
const path = require('path');

/**
 * 音频诊断工具
 * 用于分析音频数据和WAV文件的问题
 */
class AudioDiagnostics {
    
    /**
     * 分析接收到的音频数据
     * @param {Object} audioMessage 音频消息
     * @param {Object} config 音频配置
     */
    static analyzeAudioData(audioMessage, config) {
        try {
            const audioBuffer = Buffer.from(audioMessage.data, 'base64');
            const analysis = {
                seq: audioMessage.seq,
                timestamp: audioMessage.timestamp,
                dataSize: audioBuffer.length,
                config: config,
                analysis: {}
            };
            
            // 计算预期的数据大小
            const bytesPerSample = config.audioFormat / 8;
            const bytesPerFrame = bytesPerSample * config.channels;
            
            analysis.analysis = {
                bytesPerSample: bytesPerSample,
                bytesPerFrame: bytesPerFrame,
                samplesInBuffer: audioBuffer.length / bytesPerFrame,
                durationMs: (audioBuffer.length / bytesPerFrame) / config.sampleRate * 1000,
                isValidSize: audioBuffer.length % bytesPerFrame === 0
            };
            
            // 分析音频数据的统计信息
            if (config.audioFormat === 16) {
                analysis.analysis.audioStats = this.analyzeInt16Audio(audioBuffer);
            }
            
            return analysis;
        } catch (error) {
            console.error('Failed to analyze audio data:', error);
            return null;
        }
    }
    
    /**
     * 分析16位音频数据
     * @param {Buffer} audioBuffer 音频数据缓冲区
     */
    static analyzeInt16Audio(audioBuffer) {
        const samples = [];
        let min = 32767, max = -32768, sum = 0;
        
        // 读取16位有符号整数样本
        for (let i = 0; i < audioBuffer.length; i += 2) {
            const sample = audioBuffer.readInt16LE(i);
            samples.push(sample);
            min = Math.min(min, sample);
            max = Math.max(max, sample);
            sum += Math.abs(sample);
        }
        
        const avg = sum / samples.length;
        const rms = Math.sqrt(samples.reduce((sum, s) => sum + s * s, 0) / samples.length);
        
        return {
            sampleCount: samples.length,
            min: min,
            max: max,
            average: avg,
            rms: rms,
            dynamicRange: max - min,
            isSilent: max < 100 && min > -100,
            peakLevel: Math.max(Math.abs(min), Math.abs(max))
        };
    }
    
    /**
     * 分析WAV文件头
     * @param {string} filepath WAV文件路径
     */
    static analyzeWavFile(filepath) {
        try {
            if (!fs.existsSync(filepath)) {
                return { error: 'File not found' };
            }
            
            const buffer = fs.readFileSync(filepath);
            
            if (buffer.length < 44) {
                return { error: 'File too small to be a valid WAV file' };
            }
            
            // 解析WAV文件头
            const header = {
                chunkId: buffer.toString('ascii', 0, 4),
                chunkSize: buffer.readUInt32LE(4),
                format: buffer.toString('ascii', 8, 12),
                subchunk1Id: buffer.toString('ascii', 12, 16),
                subchunk1Size: buffer.readUInt32LE(16),
                audioFormat: buffer.readUInt16LE(20),
                numChannels: buffer.readUInt16LE(22),
                sampleRate: buffer.readUInt32LE(24),
                byteRate: buffer.readUInt32LE(28),
                blockAlign: buffer.readUInt16LE(32),
                bitsPerSample: buffer.readUInt16LE(34),
                subchunk2Id: buffer.toString('ascii', 36, 40),
                subchunk2Size: buffer.readUInt32LE(40)
            };
            
            // 计算音频时长
            const audioDataSize = header.subchunk2Size;
            const bytesPerSample = header.bitsPerSample / 8;
            const totalSamples = audioDataSize / (bytesPerSample * header.numChannels);
            const durationSeconds = totalSamples / header.sampleRate;
            
            // 验证文件完整性
            const expectedFileSize = 44 + audioDataSize;
            const actualFileSize = buffer.length;
            
            return {
                header: header,
                audioDataSize: audioDataSize,
                totalSamples: totalSamples,
                durationSeconds: durationSeconds,
                expectedFileSize: expectedFileSize,
                actualFileSize: actualFileSize,
                isComplete: actualFileSize >= expectedFileSize,
                analysis: {
                    isValidWav: header.chunkId === 'RIFF' && header.format === 'WAVE',
                    isPCM: header.audioFormat === 1,
                    calculatedByteRate: header.sampleRate * header.numChannels * bytesPerSample,
                    byteRateMatch: header.byteRate === (header.sampleRate * header.numChannels * bytesPerSample),
                    calculatedBlockAlign: header.numChannels * bytesPerSample,
                    blockAlignMatch: header.blockAlign === (header.numChannels * bytesPerSample)
                }
            };
        } catch (error) {
            return { error: error.message };
        }
    }
    
    /**
     * 比较音频配置和实际数据
     * @param {Object} expectedConfig 预期配置
     * @param {Object} actualData 实际数据分析结果
     */
    static compareAudioConfig(expectedConfig, actualData) {
        const comparison = {
            sampleRate: {
                expected: expectedConfig.sampleRate,
                actual: actualData.header ? actualData.header.sampleRate : 'unknown',
                match: actualData.header ? actualData.header.sampleRate === expectedConfig.sampleRate : false
            },
            channels: {
                expected: expectedConfig.channels,
                actual: actualData.header ? actualData.header.numChannels : 'unknown',
                match: actualData.header ? actualData.header.numChannels === expectedConfig.channels : false
            },
            bitDepth: {
                expected: expectedConfig.audioFormat,
                actual: actualData.header ? actualData.header.bitsPerSample : 'unknown',
                match: actualData.header ? actualData.header.bitsPerSample === expectedConfig.audioFormat : false
            }
        };
        
        comparison.allMatch = comparison.sampleRate.match && 
                             comparison.channels.match && 
                             comparison.bitDepth.match;
        
        return comparison;
    }
    
    /**
     * 生成诊断报告
     * @param {string} sessionId 会话ID
     * @param {Object} config 音频配置
     * @param {Array} audioDataAnalysis 音频数据分析结果数组
     * @param {string} wavFilePath WAV文件路径
     */
    static generateDiagnosticReport(sessionId, config, audioDataAnalysis, wavFilePath) {
        const report = {
            sessionId: sessionId,
            timestamp: new Date().toISOString(),
            config: config,
            audioDataAnalysis: {
                totalPackets: audioDataAnalysis.length,
                totalDataSize: audioDataAnalysis.reduce((sum, data) => sum + data.dataSize, 0),
                averagePacketSize: audioDataAnalysis.length > 0 ? 
                    audioDataAnalysis.reduce((sum, data) => sum + data.dataSize, 0) / audioDataAnalysis.length : 0,
                totalDurationMs: audioDataAnalysis.reduce((sum, data) => sum + (data.analysis?.durationMs || 0), 0),
                silentPackets: audioDataAnalysis.filter(data => data.analysis?.audioStats?.isSilent).length
            }
        };
        
        // 分析WAV文件
        if (wavFilePath && fs.existsSync(wavFilePath)) {
            report.wavFileAnalysis = this.analyzeWavFile(wavFilePath);
            report.configComparison = this.compareAudioConfig(config, report.wavFileAnalysis);
        }
        
        // 检测可能的问题
        report.issues = this.detectIssues(report);
        
        return report;
    }
    
    /**
     * 检测可能的问题
     * @param {Object} report 诊断报告
     */
    static detectIssues(report) {
        const issues = [];
        
        // 检查配置不匹配
        if (report.configComparison && !report.configComparison.allMatch) {
            issues.push({
                type: 'CONFIG_MISMATCH',
                severity: 'HIGH',
                message: '音频配置与WAV文件头不匹配',
                details: report.configComparison
            });
        }
        
        // 检查音频数据包大小异常
        if (report.audioDataAnalysis.totalPackets > 0) {
            const avgPacketSize = report.audioDataAnalysis.averagePacketSize;
            const expectedPacketSize = (report.config.sampleRate / 10) * (report.config.audioFormat / 8) * report.config.channels; // 100ms的数据
            
            if (Math.abs(avgPacketSize - expectedPacketSize) > expectedPacketSize * 0.5) {
                issues.push({
                    type: 'PACKET_SIZE_ANOMALY',
                    severity: 'MEDIUM',
                    message: '音频数据包大小异常',
                    details: {
                        averageSize: avgPacketSize,
                        expectedSize: expectedPacketSize,
                        difference: avgPacketSize - expectedPacketSize
                    }
                });
            }
        }
        
        // 检查静音数据过多
        const silentRatio = report.audioDataAnalysis.totalPackets > 0 ? 
            report.audioDataAnalysis.silentPackets / report.audioDataAnalysis.totalPackets : 0;
        
        if (silentRatio > 0.8) {
            issues.push({
                type: 'TOO_MUCH_SILENCE',
                severity: 'MEDIUM',
                message: '音频数据中静音过多',
                details: {
                    silentPackets: report.audioDataAnalysis.silentPackets,
                    totalPackets: report.audioDataAnalysis.totalPackets,
                    silentRatio: silentRatio
                }
            });
        }
        
        // 检查WAV文件完整性
        if (report.wavFileAnalysis && !report.wavFileAnalysis.isComplete) {
            issues.push({
                type: 'INCOMPLETE_WAV_FILE',
                severity: 'HIGH',
                message: 'WAV文件不完整',
                details: {
                    expectedSize: report.wavFileAnalysis.expectedFileSize,
                    actualSize: report.wavFileAnalysis.actualFileSize
                }
            });
        }
        
        return issues;
    }
    
    /**
     * 保存诊断报告
     * @param {Object} report 诊断报告
     * @param {string} outputDir 输出目录
     */
    static saveDiagnosticReport(report, outputDir = './audio-files') {
        try {
            const filename = `diagnostic_${report.sessionId}_${Date.now()}.json`;
            const filepath = path.join(outputDir, filename);
            
            fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
            console.log(`Diagnostic report saved: ${filepath}`);
            
            return filepath;
        } catch (error) {
            console.error('Failed to save diagnostic report:', error);
            return null;
        }
    }
}

module.exports = AudioDiagnostics;
