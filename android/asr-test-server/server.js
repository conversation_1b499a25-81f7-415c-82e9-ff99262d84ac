const WebSocket = require('ws');
const express = require('express');
const cors = require('cors');
const http = require('http');
const AudioProcessor = require('./audio-processor');
const AudioDiagnostics = require('./audio-diagnostics');
const AudioValidator = require('./audio-validator');
const { createManualWavWriter } = require('./manual-wav-writer');

// 创建 Express 应用
const app = express();
app.use(cors());
app.use(express.json());

// 静态文件服务
app.use(express.static('public'));

// 创建 HTTP 服务器
const server = http.createServer(app);

// 创建 WebSocket 服务器
const wss = new WebSocket.Server({ 
    server,
    path: '/asr'
});

// 存储活跃的会话
const activeSessions = new Map();

// 创建音频处理器
const audioProcessor = new AudioProcessor();

// 模拟的语音识别结果
const mockRecognitionResults = [
    "你好",
    "你好，我是",
    "你好，我是语音",
    "你好，我是语音识别",
    "你好，我是语音识别系统"
];

// ASR 会话类
class AsrSession {
    constructor(sessionId, ws) {
        this.sessionId = sessionId;
        this.ws = ws;
        this.config = null;
        this.isRecording = false;
        this.audioSequence = 0;
        this.recognitionTimer = null;
        this.partialResultIndex = 0;
        this.audioDataCount = 0;
        this.wavWriter = null;  // WAV 文件写入器
        this.manualWavWriter = null;  // 手动 WAV 文件写入器
        this.audioFileInfo = null;  // 音频文件信息
        this.audioDataAnalysis = [];  // 音频数据分析结果
        this.enableDiagnostics = true;  // 启用诊断功能

        console.log(`[${this.sessionId}] Session created`);
    }
    
    setConfig(config) {
        this.config = config;
        console.log(`[${this.sessionId}] Config set:`, {
            language: config.language,
            sampleRate: config.sampleRate,
            channels: config.channels,
            enableVad: config.enableVad
        });

        // 发送配置确认
        this.sendStatus('ready', '配置已设置，准备开始识别');
    }
    
    startRecognition() {
        if (this.isRecording) {
            console.log(`[${this.sessionId}] Already recording`);
            return;
        }

        this.isRecording = true;
        this.audioSequence = 0;
        this.partialResultIndex = 0;
        this.audioDataCount = 0;

        // 创建 WAV 文件写入器（使用两种方式对比）
        if (this.config) {
            // 原始方式
            this.wavWriter = audioProcessor.createWavWriter(this.sessionId, this.config);
            console.log(`[${this.sessionId}] Created WAV file: ${this.wavWriter.filename}`);

            // 手动方式
            this.manualWavWriter = createManualWavWriter(this.sessionId, this.config);
            console.log(`[${this.sessionId}] Created manual WAV writer`);
        }

        console.log(`[${this.sessionId}] Recognition started`);
        this.sendStatus('recording', '开始录音');

        // 模拟识别过程
        this.startMockRecognition();
    }
    
    stopRecognition() {
        if (!this.isRecording) {
            console.log(`[${this.sessionId}] Not recording`);
            return;
        }

        this.isRecording = false;

        if (this.recognitionTimer) {
            clearInterval(this.recognitionTimer);
            this.recognitionTimer = null;
        }

        // 完成 WAV 文件写入
        if (this.wavWriter) {
            this.audioFileInfo = audioProcessor.finishWavFile(this.wavWriter);
            this.wavWriter = null;
        }

        // 完成手动 WAV 文件写入
        if (this.manualWavWriter) {
            const manualResult = this.manualWavWriter.finalize();
            console.log(`[${this.sessionId}] Manual WAV file completed:`, manualResult);
            this.manualWavWriter = null;
        }

        // 生成诊断报告
        if (this.enableDiagnostics && this.audioDataAnalysis.length > 0) {
            const report = AudioDiagnostics.generateDiagnosticReport(
                this.sessionId,
                this.config,
                this.audioDataAnalysis,
                this.audioFileInfo ? this.audioFileInfo.filepath : null
            );

            // 保存诊断报告
            AudioDiagnostics.saveDiagnosticReport(report);

            // 打印问题摘要
            if (report.issues && report.issues.length > 0) {
                console.log(`[${this.sessionId}] 🚨 Audio issues detected:`);
                report.issues.forEach(issue => {
                    console.log(`  - ${issue.severity}: ${issue.message}`);
                });
            } else {
                console.log(`[${this.sessionId}] ✅ No audio issues detected`);
            }

            // 打印配置比较结果
            if (report.configComparison) {
                console.log(`[${this.sessionId}] Config comparison:`, {
                    sampleRate: report.configComparison.sampleRate,
                    channels: report.configComparison.channels,
                    bitDepth: report.configComparison.bitDepth,
                    allMatch: report.configComparison.allMatch
                });
            }
        }

        console.log(`[${this.sessionId}] Recognition stopped`);
        this.sendStatus('stopped', '识别已停止');

        // 发送最终结果
        this.sendFinalResult();
    }
    
    processAudioData(audioData) {
        if (!this.isRecording) {
            console.log(`[${this.sessionId}] Received audio data but not recording`);
            return;
        }

        this.audioDataCount++;

        // 诊断音频数据
        if (this.enableDiagnostics && this.config) {
            const analysis = AudioDiagnostics.analyzeAudioData(audioData, this.config);
            if (analysis) {
                this.audioDataAnalysis.push(analysis);

                // 每50个数据包打印一次详细分析
                if (this.audioDataCount % 50 === 0) {
                    console.log(`[${this.sessionId}] Audio analysis #${this.audioDataCount}:`, {
                        dataSize: analysis.dataSize,
                        durationMs: analysis.analysis.durationMs,
                        samplesInBuffer: analysis.analysis.samplesInBuffer,
                        isValidSize: analysis.analysis.isValidSize,
                        audioStats: analysis.analysis.audioStats
                    });
                }
            }
        }

        // 将音频数据写入 WAV 文件（两种方式）
        if (audioData.data) {
            // 原始方式
            if (this.wavWriter) {
                const success = audioProcessor.writeAudioData(this.wavWriter, audioData.data);
                if (!success) {
                    console.error(`[${this.sessionId}] Failed to write audio data to WAV file`);
                }
            }

            // 手动方式
            if (this.manualWavWriter) {
                try {
                    const audioBuffer = Buffer.from(audioData.data, 'base64');
                    this.manualWavWriter.writeAudioData(audioBuffer);
                } catch (error) {
                    console.error(`[${this.sessionId}] Failed to write audio data to manual WAV file:`, error);
                }
            }
        }

        // 详细的音频数据日志
        const base64Size = audioData.data ? audioData.data.length : 0;
        const actualBytes = base64Size * 3 / 4; // Base64 解码后的实际字节数
        console.log(`[${this.sessionId}] Received audio data #${audioData.seq}: base64=${base64Size} chars, actual=${Math.round(actualBytes)} bytes`);

        // 模拟音频处理延迟
        if (this.audioDataCount % 10 === 0) {
            this.sendStatus('recognizing', '正在识别中...');
        }
    }
    
    startMockRecognition() {
        // 每2秒发送一个部分结果
        this.recognitionTimer = setInterval(() => {
            if (!this.isRecording) {
                clearInterval(this.recognitionTimer);
                return;
            }
            
            if (this.partialResultIndex < mockRecognitionResults.length) {
                this.sendPartialResult(mockRecognitionResults[this.partialResultIndex]);
                this.partialResultIndex++;
            } else {
                // 发送最终结果并停止
                this.stopRecognition();
            }
        }, 2000);
    }
    
    sendPartialResult(text) {
        const result = {
            type: 'result',
            sessionId: this.sessionId,
            isFinal: false,
            text: text,
            seq: this.partialResultIndex + 1,
            startTime: Date.now() - (this.partialResultIndex + 1) * 2000,
            endTime: Date.now(),
            confidence: 0.8 + Math.random() * 0.15
        };
        
        this.sendMessage(result);
        console.log(`[${this.sessionId}] Sent partial result: "${text}"`);
    }
    
    sendFinalResult() {
        const finalText = mockRecognitionResults[mockRecognitionResults.length - 1];
        const result = {
            type: 'result',
            sessionId: this.sessionId,
            isFinal: true,
            text: finalText,
            seq: mockRecognitionResults.length,
            startTime: Date.now() - mockRecognitionResults.length * 2000,
            endTime: Date.now(),
            confidence: 0.95
        };
        
        this.sendMessage(result);
        console.log(`[${this.sessionId}] Sent final result: "${finalText}"`);
    }
    
    sendStatus(status, message) {
        const statusMsg = {
            type: 'status',
            status: status,
            message: message,
            timestamp: Date.now()
        };
        
        this.sendMessage(statusMsg);
        console.log(`[${this.sessionId}] Status: ${status} - ${message}`);
    }
    
    sendError(code, message) {
        const error = {
            type: 'error',
            code: code,
            message: message,
            timestamp: Date.now()
        };
        
        this.sendMessage(error);
        console.log(`[${this.sessionId}] Error: ${code} - ${message}`);
    }
    
    sendMessage(message) {
        if (this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    
    cleanup() {
        if (this.recognitionTimer) {
            clearInterval(this.recognitionTimer);
            this.recognitionTimer = null;
        }

        // 如果还在录音，完成 WAV 文件写入
        if (this.wavWriter) {
            this.audioFileInfo = audioProcessor.finishWavFile(this.wavWriter);
            this.wavWriter = null;
        }

        if (this.manualWavWriter) {
            this.manualWavWriter.finalize();
            this.manualWavWriter = null;
        }

        console.log(`[${this.sessionId}] Session cleaned up`);
    }
}

// WebSocket 连接处理
wss.on('connection', (ws, req) => {
    console.log('New WebSocket connection from:', req.socket.remoteAddress);
    
    let session = null;
    
    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data.toString());
            console.log('Received message:', message.type, message);
            
            switch (message.type) {
                case 'config':
                    handleConfig(message);
                    break;
                case 'start':
                    handleStart(message);
                    break;
                case 'audio':
                    handleAudio(message);
                    break;
                case 'stop':
                    handleStop(message);
                    break;
                default:
                    console.log('Unknown message type:', message.type);
                    break;
            }
        } catch (error) {
            console.error('Failed to parse message:', error);
            ws.send(JSON.stringify({
                type: 'error',
                code: 'INVALID_MESSAGE',
                message: '消息格式错误'
            }));
        }
    });
    
    ws.on('close', () => {
        console.log('WebSocket connection closed');
        if (session) {
            session.cleanup();
            activeSessions.delete(session.sessionId);
        }
    });
    
    ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        if (session) {
            session.cleanup();
            activeSessions.delete(session.sessionId);
        }
    });
    
    function handleConfig(message) {
        const sessionId = message.sessionId;
        if (!sessionId) {
            ws.send(JSON.stringify({
                type: 'error',
                code: 'MISSING_SESSION_ID',
                message: '缺少会话ID'
            }));
            return;
        }
        
        // 创建新会话
        session = new AsrSession(sessionId, ws);
        activeSessions.set(sessionId, session);
        
        // 设置配置
        session.setConfig(message);
    }
    
    function handleStart(message) {
        if (!session) {
            ws.send(JSON.stringify({
                type: 'error',
                code: 'NO_SESSION',
                message: '会话未初始化'
            }));
            return;
        }
        
        session.startRecognition();
    }
    
    function handleAudio(message) {
        if (!session) {
            ws.send(JSON.stringify({
                type: 'error',
                code: 'NO_SESSION',
                message: '会话未初始化'
            }));
            return;
        }
        
        session.processAudioData(message);
    }
    
    function handleStop(message) {
        if (!session) {
            ws.send(JSON.stringify({
                type: 'error',
                code: 'NO_SESSION',
                message: '会话未初始化'
            }));
            return;
        }

        session.stopRecognition();
    }
});

// HTTP 路由
app.get('/', (req, res) => {
    res.json({
        name: 'ASR Test Server',
        version: '1.0.0',
        websocket: 'ws://localhost:8080/asr',
        activeSessions: activeSessions.size,
        uptime: process.uptime()
    });
});

app.get('/status', (req, res) => {
    const sessions = Array.from(activeSessions.values()).map(session => ({
        sessionId: session.sessionId,
        isRecording: session.isRecording,
        audioDataCount: session.audioDataCount,
        config: session.config,
        audioFile: session.audioFileInfo
    }));

    res.json({
        activeSessions: activeSessions.size,
        sessions: sessions,
        uptime: process.uptime(),
        memory: process.memoryUsage()
    });
});

// 获取音频文件列表
app.get('/audio-files', async (req, res) => {
    try {
        const files = await audioProcessor.getAudioFilesList();
        const stats = await audioProcessor.getAudioDirectoryStats();

        res.json({
            files: files,
            stats: stats
        });
    } catch (error) {
        res.status(500).json({
            error: 'Failed to get audio files',
            message: error.message
        });
    }
});

// 下载音频文件
app.get('/audio-files/:filename', async (req, res) => {
    try {
        const filename = req.params.filename;
        const files = await audioProcessor.getAudioFilesList();
        const file = files.find(f => f.filename === filename);

        if (!file) {
            return res.status(404).json({
                error: 'File not found'
            });
        }

        res.download(file.filepath, filename);
    } catch (error) {
        res.status(500).json({
            error: 'Failed to download file',
            message: error.message
        });
    }
});

// 删除音频文件
app.delete('/audio-files/:filename', async (req, res) => {
    try {
        const filename = req.params.filename;
        const success = await audioProcessor.deleteAudioFile(filename);

        if (success) {
            res.json({
                message: 'File deleted successfully',
                filename: filename
            });
        } else {
            res.status(500).json({
                error: 'Failed to delete file'
            });
        }
    } catch (error) {
        res.status(500).json({
            error: 'Failed to delete file',
            message: error.message
        });
    }
});

// 清理所有音频文件
app.delete('/audio-files', async (req, res) => {
    try {
        const deletedCount = await audioProcessor.cleanupAudioFiles();

        res.json({
            message: 'Audio files cleaned up successfully',
            deletedCount: deletedCount
        });
    } catch (error) {
        res.status(500).json({
            error: 'Failed to cleanup audio files',
            message: error.message
        });
    }
});

// 清理旧的音频文件
app.delete('/audio-files/cleanup/:days', async (req, res) => {
    try {
        const days = parseInt(req.params.days) || 7;
        const deletedCount = await audioProcessor.cleanupOldAudioFiles(days);

        res.json({
            message: `Old audio files cleaned up successfully`,
            days: days,
            deletedCount: deletedCount
        });
    } catch (error) {
        res.status(500).json({
            error: 'Failed to cleanup old audio files',
            message: error.message
        });
    }
});

// 获取诊断报告列表
app.get('/diagnostics', async (req, res) => {
    try {
        const fs = require('fs-extra');
        const audioDir = './audio-files';

        const files = await fs.readdir(audioDir);
        const diagnosticFiles = files.filter(file => file.startsWith('diagnostic_') && file.endsWith('.json'));

        const reports = await Promise.all(
            diagnosticFiles.map(async (filename) => {
                const filepath = require('path').join(audioDir, filename);
                const stats = await fs.stat(filepath);
                const content = await fs.readJson(filepath);

                return {
                    filename: filename,
                    sessionId: content.sessionId,
                    timestamp: content.timestamp,
                    created: stats.birthtime,
                    size: stats.size,
                    issueCount: content.issues ? content.issues.length : 0,
                    hasIssues: content.issues && content.issues.length > 0
                };
            })
        );

        // 按创建时间倒序排列
        reports.sort((a, b) => new Date(b.created) - new Date(a.created));

        res.json({
            reports: reports,
            totalReports: reports.length,
            reportsWithIssues: reports.filter(r => r.hasIssues).length
        });
    } catch (error) {
        res.status(500).json({
            error: 'Failed to get diagnostic reports',
            message: error.message
        });
    }
});

// 获取特定的诊断报告
app.get('/diagnostics/:filename', async (req, res) => {
    try {
        const fs = require('fs-extra');
        const filename = req.params.filename;
        const filepath = require('path').join('./audio-files', filename);

        if (!filename.startsWith('diagnostic_') || !filename.endsWith('.json')) {
            return res.status(400).json({
                error: 'Invalid diagnostic file name'
            });
        }

        if (!await fs.pathExists(filepath)) {
            return res.status(404).json({
                error: 'Diagnostic report not found'
            });
        }

        const report = await fs.readJson(filepath);
        res.json(report);
    } catch (error) {
        res.status(500).json({
            error: 'Failed to get diagnostic report',
            message: error.message
        });
    }
});

// 分析现有的WAV文件
app.post('/diagnostics/analyze/:filename', async (req, res) => {
    try {
        const filename = req.params.filename;
        const filepath = require('path').join('./audio-files', filename);

        if (!filename.endsWith('.wav')) {
            return res.status(400).json({
                error: 'File must be a WAV file'
            });
        }

        const analysis = AudioDiagnostics.analyzeWavFile(filepath);

        if (analysis.error) {
            return res.status(400).json({
                error: 'Failed to analyze WAV file',
                details: analysis.error
            });
        }

        res.json({
            filename: filename,
            analysis: analysis,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            error: 'Failed to analyze WAV file',
            message: error.message
        });
    }
});

// 验证WAV文件
app.post('/validate/:filename', async (req, res) => {
    try {
        const filename = req.params.filename;
        const filepath = require('path').join('./audio-files', filename);

        if (!filename.endsWith('.wav')) {
            return res.status(400).json({
                error: 'File must be a WAV file'
            });
        }

        const validation = AudioValidator.validateWavFile(filepath);

        res.json({
            filename: filename,
            validation: validation,
            report: AudioValidator.generateReport(filepath),
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            error: 'Failed to validate WAV file',
            message: error.message
        });
    }
});

// 验证所有WAV文件
app.get('/validate', async (req, res) => {
    try {
        const results = AudioValidator.validateDirectory('./audio-files');

        const summary = {
            totalFiles: results.length,
            validFiles: results.filter(r => r.valid).length,
            invalidFiles: results.filter(r => !r.valid).length,
            results: results
        };

        res.json(summary);
    } catch (error) {
        res.status(500).json({
            error: 'Failed to validate WAV files',
            message: error.message
        });
    }
});

// HTTP控制命令接口
app.post('/control-command', (req, res) => {
    try {
        const { command, timestamp } = req.body;

        if (!command) {
            return res.status(400).json({
                success: false,
                error: 'MISSING_COMMAND',
                message: '缺少命令参数'
            });
        }

        const cmd = command.toLowerCase();
        console.log(`[HTTP Control Command] Received: ${cmd}`);

        // 向所有连接的WebSocket客户端广播控制命令
        const commandMessage = {
            type: 'control-command',
            command: cmd,
            timestamp: timestamp || Date.now(),
            source: 'http-api'
        };

        let sentCount = 0;
        wss.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                try {
                    client.send(JSON.stringify(commandMessage));
                    sentCount++;
                } catch (error) {
                    console.error('Failed to send command to WebSocket client:', error);
                }
            }
        });

        // 根据命令类型执行相应操作并返回结果
        let result = {
            success: true,
            command: cmd,
            message: '',
            sentToClients: sentCount,
            timestamp: Date.now()
        };

        switch (cmd) {
            case 'start':
                result.message = `启动命令已发送给 ${sentCount} 个连接的客户端`;
                console.log('🟢 HTTP Control Command: START - 启动命令已广播');
                break;

            case 'stop':
                result.message = `停止命令已发送给 ${sentCount} 个连接的客户端`;
                console.log('🔴 HTTP Control Command: STOP - 停止命令已广播');
                break;

            case 'restart':
                result.message = `重启命令已发送给 ${sentCount} 个连接的客户端`;
                console.log('🔄 HTTP Control Command: RESTART - 重启命令已广播');
                break;

            case 'status':
                // 返回详细的服务器状态
                const sessions = Array.from(activeSessions.values()).map(session => ({
                    sessionId: session.sessionId,
                    isRecording: session.isRecording,
                    audioDataCount: session.audioDataCount,
                    config: session.config ? {
                        language: session.config.language,
                        sampleRate: session.config.sampleRate,
                        channels: session.config.channels
                    } : null
                }));

                result.message = '服务器状态查询成功';
                result.data = {
                    activeSessions: activeSessions.size,
                    connectedClients: wss.clients.size,
                    sessions: sessions,
                    uptime: process.uptime(),
                    memory: process.memoryUsage(),
                    timestamp: new Date().toISOString()
                };
                console.log('📊 HTTP Control Command: STATUS - 状态查询完成');
                break;

            default:
                result.success = false;
                result.message = `未知命令: ${cmd}，支持的命令: start, stop, restart, status`;
                console.log(`❌ HTTP Control Command: UNKNOWN - ${cmd}`);
                break;
        }

        res.json(result);

    } catch (error) {
        console.error('HTTP控制命令处理失败:', error);
        res.status(500).json({
            success: false,
            error: 'INTERNAL_ERROR',
            message: '服务器内部错误: ' + error.message
        });
    }
});

// 启动服务器
const PORT = process.env.PORT || 8080;
server.listen(PORT, () => {
    console.log(`ASR Test Server is running on port ${PORT}`);
    console.log(`WebSocket endpoint: ws://localhost:${PORT}/asr`);
    console.log(`HTTP endpoint: http://localhost:${PORT}`);
    console.log('Ready to accept connections...');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\nShutting down server...');
    
    // 清理所有会话
    activeSessions.forEach(session => {
        session.cleanup();
    });
    activeSessions.clear();
    
    // 关闭 WebSocket 服务器
    wss.close(() => {
        console.log('WebSocket server closed');
        
        // 关闭 HTTP 服务器
        server.close(() => {
            console.log('HTTP server closed');
            process.exit(0);
        });
    });
});
