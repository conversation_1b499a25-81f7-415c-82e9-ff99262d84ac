const fs = require('fs');
const path = require('path');

/**
 * 音频文件验证工具
 * 用于验证生成的 WAV 文件是否正确
 */
class AudioValidator {
    
    /**
     * 验证 WAV 文件的完整性和正确性
     * @param {string} filePath WAV 文件路径
     * @returns {Object} 验证结果
     */
    static validateWavFile(filePath) {
        try {
            if (!fs.existsSync(filePath)) {
                return { valid: false, error: 'File not found' };
            }
            
            const buffer = fs.readFileSync(filePath);
            const result = {
                valid: true,
                filePath: filePath,
                fileSize: buffer.length,
                issues: [],
                analysis: {}
            };
            
            // 检查文件大小
            if (buffer.length < 44) {
                result.valid = false;
                result.issues.push('File too small to be a valid WAV file');
                return result;
            }
            
            // 解析 WAV 文件头
            const header = this.parseWavHeader(buffer);
            result.analysis.header = header;
            
            // 验证 WAV 文件头
            if (header.chunkId !== 'RIFF') {
                result.issues.push('Invalid RIFF header');
            }
            
            if (header.format !== 'WAVE') {
                result.issues.push('Invalid WAVE format');
            }
            
            if (header.audioFormat !== 1) {
                result.issues.push('Not PCM format');
            }
            
            // 验证音频参数
            const expectedSampleRate = 16000;
            const expectedChannels = 1;
            const expectedBitDepth = 16;
            
            if (header.sampleRate !== expectedSampleRate) {
                result.issues.push(`Sample rate mismatch: ${header.sampleRate} (expected: ${expectedSampleRate})`);
            }
            
            if (header.numChannels !== expectedChannels) {
                result.issues.push(`Channel count mismatch: ${header.numChannels} (expected: ${expectedChannels})`);
            }
            
            if (header.bitsPerSample !== expectedBitDepth) {
                result.issues.push(`Bit depth mismatch: ${header.bitsPerSample} (expected: ${expectedBitDepth})`);
            }
            
            // 计算音频时长
            const audioDataSize = header.subchunk2Size;
            const bytesPerSample = header.bitsPerSample / 8;
            const totalSamples = audioDataSize / (bytesPerSample * header.numChannels);
            const durationSeconds = totalSamples / header.sampleRate;
            
            result.analysis.audioDataSize = audioDataSize;
            result.analysis.totalSamples = totalSamples;
            result.analysis.durationSeconds = durationSeconds;
            result.analysis.durationMs = durationSeconds * 1000;
            
            // 验证文件完整性
            const expectedFileSize = 44 + audioDataSize;
            if (buffer.length < expectedFileSize) {
                result.issues.push(`Incomplete file: ${buffer.length} bytes (expected: ${expectedFileSize})`);
            }
            
            // 验证字节率计算
            const calculatedByteRate = header.sampleRate * header.numChannels * bytesPerSample;
            if (header.byteRate !== calculatedByteRate) {
                result.issues.push(`Byte rate mismatch: ${header.byteRate} (calculated: ${calculatedByteRate})`);
            }
            
            // 验证块对齐
            const calculatedBlockAlign = header.numChannels * bytesPerSample;
            if (header.blockAlign !== calculatedBlockAlign) {
                result.issues.push(`Block align mismatch: ${header.blockAlign} (calculated: ${calculatedBlockAlign})`);
            }
            
            // 分析音频数据
            if (audioDataSize > 0) {
                const audioDataStart = 44;
                const audioData = buffer.slice(audioDataStart, audioDataStart + Math.min(audioDataSize, buffer.length - 44));
                result.analysis.audioStats = this.analyzeAudioData(audioData, header);
            }
            
            result.valid = result.issues.length === 0;
            return result;
            
        } catch (error) {
            return {
                valid: false,
                error: error.message,
                filePath: filePath
            };
        }
    }
    
    /**
     * 解析 WAV 文件头
     * @param {Buffer} buffer 文件缓冲区
     * @returns {Object} 文件头信息
     */
    static parseWavHeader(buffer) {
        return {
            chunkId: buffer.toString('ascii', 0, 4),
            chunkSize: buffer.readUInt32LE(4),
            format: buffer.toString('ascii', 8, 12),
            subchunk1Id: buffer.toString('ascii', 12, 16),
            subchunk1Size: buffer.readUInt32LE(16),
            audioFormat: buffer.readUInt16LE(20),
            numChannels: buffer.readUInt16LE(22),
            sampleRate: buffer.readUInt32LE(24),
            byteRate: buffer.readUInt32LE(28),
            blockAlign: buffer.readUInt16LE(32),
            bitsPerSample: buffer.readUInt16LE(34),
            subchunk2Id: buffer.toString('ascii', 36, 40),
            subchunk2Size: buffer.readUInt32LE(40)
        };
    }
    
    /**
     * 分析音频数据
     * @param {Buffer} audioData 音频数据
     * @param {Object} header 文件头信息
     * @returns {Object} 音频统计信息
     */
    static analyzeAudioData(audioData, header) {
        const stats = {
            dataSize: audioData.length,
            sampleCount: 0,
            min: 32767,
            max: -32768,
            average: 0,
            rms: 0,
            silentSamples: 0,
            peakLevel: 0
        };
        
        if (header.bitsPerSample === 16) {
            let sum = 0;
            let sumSquares = 0;
            
            for (let i = 0; i < audioData.length; i += 2) {
                if (i + 1 < audioData.length) {
                    // Little Endian 16-bit signed integer
                    const sample = audioData.readInt16LE(i);
                    
                    stats.min = Math.min(stats.min, sample);
                    stats.max = Math.max(stats.max, sample);
                    sum += Math.abs(sample);
                    sumSquares += sample * sample;
                    stats.sampleCount++;
                    
                    // 检查静音样本（阈值：100）
                    if (Math.abs(sample) < 100) {
                        stats.silentSamples++;
                    }
                }
            }
            
            if (stats.sampleCount > 0) {
                stats.average = sum / stats.sampleCount;
                stats.rms = Math.sqrt(sumSquares / stats.sampleCount);
                stats.peakLevel = Math.max(Math.abs(stats.min), Math.abs(stats.max));
                stats.silentRatio = stats.silentSamples / stats.sampleCount;
            }
        }
        
        return stats;
    }
    
    /**
     * 生成验证报告
     * @param {string} filePath WAV 文件路径
     * @returns {string} 格式化的报告
     */
    static generateReport(filePath) {
        const result = this.validateWavFile(filePath);
        
        let report = `\n=== WAV File Validation Report ===\n`;
        report += `File: ${path.basename(filePath)}\n`;
        report += `Size: ${result.fileSize} bytes\n`;
        report += `Valid: ${result.valid ? '✅ YES' : '❌ NO'}\n`;
        
        if (result.error) {
            report += `Error: ${result.error}\n`;
            return report;
        }
        
        if (result.issues && result.issues.length > 0) {
            report += `\nIssues Found:\n`;
            result.issues.forEach(issue => {
                report += `  ❌ ${issue}\n`;
            });
        }
        
        if (result.analysis && result.analysis.header) {
            const h = result.analysis.header;
            report += `\nAudio Format:\n`;
            report += `  Sample Rate: ${h.sampleRate} Hz\n`;
            report += `  Channels: ${h.numChannels}\n`;
            report += `  Bit Depth: ${h.bitsPerSample} bit\n`;
            report += `  Byte Rate: ${h.byteRate} bytes/sec\n`;
            report += `  Block Align: ${h.blockAlign}\n`;
        }
        
        if (result.analysis) {
            report += `\nAudio Data:\n`;
            report += `  Data Size: ${result.analysis.audioDataSize} bytes\n`;
            report += `  Duration: ${result.analysis.durationSeconds.toFixed(2)} seconds\n`;
            report += `  Total Samples: ${result.analysis.totalSamples}\n`;
            
            if (result.analysis.audioStats) {
                const stats = result.analysis.audioStats;
                report += `\nAudio Statistics:\n`;
                report += `  Sample Count: ${stats.sampleCount}\n`;
                report += `  Range: ${stats.min} to ${stats.max}\n`;
                report += `  Average Level: ${stats.average.toFixed(1)}\n`;
                report += `  RMS Level: ${stats.rms.toFixed(1)}\n`;
                report += `  Peak Level: ${stats.peakLevel}\n`;
                report += `  Silent Ratio: ${(stats.silentRatio * 100).toFixed(1)}%\n`;
            }
        }
        
        report += `\n================================\n`;
        return report;
    }
    
    /**
     * 验证目录中的所有 WAV 文件
     * @param {string} dirPath 目录路径
     * @returns {Array} 验证结果数组
     */
    static validateDirectory(dirPath = './audio-files') {
        try {
            const files = fs.readdirSync(dirPath);
            const wavFiles = files.filter(file => file.endsWith('.wav'));
            
            const results = wavFiles.map(filename => {
                const filePath = path.join(dirPath, filename);
                const result = this.validateWavFile(filePath);
                result.filename = filename;
                return result;
            });
            
            return results;
        } catch (error) {
            console.error('Failed to validate directory:', error);
            return [];
        }
    }
}

// 如果直接运行此文件，验证所有 WAV 文件
if (require.main === module) {
    console.log('🔍 Validating WAV files...\n');
    
    const results = AudioValidator.validateDirectory();
    
    if (results.length === 0) {
        console.log('No WAV files found in ./audio-files directory');
    } else {
        results.forEach(result => {
            console.log(AudioValidator.generateReport(result.filePath));
        });
        
        const validFiles = results.filter(r => r.valid).length;
        const totalFiles = results.length;
        
        console.log(`\n📊 Summary: ${validFiles}/${totalFiles} files are valid`);
        
        if (validFiles < totalFiles) {
            console.log('\n🚨 Issues found! Check the reports above for details.');
        } else {
            console.log('\n✅ All files are valid!');
        }
    }
}

module.exports = AudioValidator;
