# 重复录音问题修复

## 🚨 问题分析

从日志中发现录音器被启动了两次：

```
2025-06-18 17:49:07.719 22280-11515 AbcAsrManager  D  Initializing audio recorder
2025-06-18 17:49:07.744 22280-11515 AbcAsrManager  D  Audio recording started successfully
2025-06-18 17:49:07.744 22280-11515 AbcAsrManager  D  Initializing audio recorder  // 重复！
2025-06-18 17:49:07.763 22280-11515 AbcAsrManager  D  Audio recording started successfully  // 重复！
```

### 根本原因

在 `AbcAsrWebSocketClient.java` 中，有两个地方会触发 `CONNECTED` 状态：

1. **WebSocket 连接成功时**（第 95 行）
2. **服务器返回 "ready" 状态时**（第 332 行）

这导致 `AbcAsrManager` 中的状态监听器被触发两次，从而初始化了两个音频录制器。

## ✅ 修复方案

### 1. 添加新的状态 `READY`

在 `AsrState.java` 中添加了 `READY` 状态：

```java
public enum State {
    IDLE,           // 空闲
    CONNECTING,     // 连接中
    CONNECTED,      // 已连接
    READY,          // 准备就绪（配置已设置）  // 新增
    RECORDING,      // 录音中
    RECOGNIZING,    // 识别中
    STOPPING,       // 停止中
    STOPPED,        // 已停止
    ERROR,          // 错误
    DISCONNECTED    // 已断开连接
}
```

### 2. 修改状态映射

在 `AbcAsrWebSocketClient.java` 中，将服务器的 "ready" 状态映射为 `READY` 而不是 `CONNECTED`：

```java
// 修复前
case "ready":
    state = AsrState.State.CONNECTED;  // 错误：导致重复触发
    break;

// 修复后
case "ready":
    state = AsrState.State.READY;      // 正确：区分连接和就绪状态
    break;
```

### 3. 调整初始化时机

在 `AbcAsrManager.java` 中，只在 `READY` 状态时初始化音频录制器：

```java
// 修复前
if (state.getState() == AsrState.State.CONNECTED) {
    isConnected = true;
    // WebSocket 连接成功后初始化音频录制器
    workerHandler.post(() -> initAudioRecorder());  // 错误：过早初始化
}

// 修复后
if (state.getState() == AsrState.State.CONNECTED) {
    isConnected = true;
} else if (state.getState() == AsrState.State.READY) {
    // 服务器配置就绪后初始化音频录制器
    workerHandler.post(() -> initAudioRecorder());  // 正确：在合适时机初始化
}
```

### 4. 添加重复初始化保护

在 `initAudioRecorder()` 方法中添加检查：

```java
private void initAudioRecorder() {
    if (audioRecorder != null) {
        LogUtils.w(TAG, "Audio recorder already initialized, skipping");
        return;  // 防止重复初始化
    }
    
    LogUtils.d(TAG, "Initializing audio recorder");
    audioRecorder = new AbcAudioRecorder(config);
    // ...
}
```

## 📊 状态流程

### 修复前的问题流程
```
WebSocket连接 → CONNECTED状态 → 初始化录音器1
     ↓
服务器ready → CONNECTED状态 → 初始化录音器2  ❌ 重复！
```

### 修复后的正确流程
```
WebSocket连接 → CONNECTED状态 → 标记已连接
     ↓
服务器ready → READY状态 → 初始化录音器  ✅ 只初始化一次
```

## 🔍 验证方法

修复后，日志应该显示：

```
AbcAsrManager  D  WebSocket state changed: CONNECTING
AbcAsrManager  D  WebSocket state changed: CONNECTED
AbcAsrManager  D  WebSocket state changed: READY
AbcAsrManager  D  Initializing audio recorder          // 只出现一次
AbcAsrManager  D  Audio recording started successfully  // 只出现一次
AbcAsrManager  D  WebSocket state changed: RECORDING
```

## 🎯 影响分析

### 修复前的问题
- 创建了两个音频录制器实例
- 可能导致资源冲突
- 音频数据可能被重复发送
- 增加了系统负担

### 修复后的改进
- ✅ 只创建一个音频录制器
- ✅ 避免资源冲突
- ✅ 音频数据发送正常
- ✅ 减少系统负担
- ✅ 状态流程更清晰

## 📋 测试清单

- [ ] 重新编译 Android 应用
- [ ] 测试 ASR 连接流程
- [ ] 确认只有一个录音器被创建
- [ ] 验证音频数据正常发送
- [ ] 检查播放速度是否正常

## 🔧 相关文件

修改的文件：
1. `AsrState.java` - 添加 READY 状态
2. `AbcAsrWebSocketClient.java` - 修改状态映射
3. `AbcAsrManager.java` - 调整初始化时机和添加保护

这个修复解决了重复录音的问题，同时也可能有助于解决音频播放速度问题，因为现在不会有重复的音频数据流。
