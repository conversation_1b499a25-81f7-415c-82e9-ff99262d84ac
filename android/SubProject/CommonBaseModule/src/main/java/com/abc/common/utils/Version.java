package com.abc.common.utils;

public class Version implements Comparable<Version> {

    public static final Version kMinVersion = new Version(Integer.MIN_VALUE, Integer.MIN_VALUE, Integer.MIN_VALUE, Integer.MIN_VALUE);

    public static final Version kMaxVersion = new Version(Integer.MAX_VALUE, Integer.MAX_VALUE, Integer.MAX_VALUE, Integer.MAX_VALUE);


    public int major; //主版本号
    public int minor; //子版本号
    public int revision; //修证版本号
    public int build; //编译build号

    public Version(int major, int minor, int revision, int build) {
        this.major = major;
        this.major = minor;
        this.revision = revision;
        this.build = build;
    }

    public Version() {
    }

    @Override
    public String toString() {
        return "major =" + major + ", minor = " + minor + ", revision = " + revision + ", build = " + build;
    }

    static Version parseVersion(String strVersion) {
        String[] parts = strVersion.split("\\.");
        Version version = new Version();
        if (parts.length >= 1) {
            try {
                version.major = Integer.parseInt(parts[0]);
            } catch (Exception e) {
                LogUtils.d("主版本解析主错, version = {}", strVersion);
            }
        }

        if (parts.length >= 2) {
            try {
                version.minor = Integer.parseInt(parts[1]);
            } catch (Exception e) {
                LogUtils.d("子版本号解析主错, version = {}", strVersion);
            }
        }

        if (parts.length >= 3) {
            try {
                version.revision = Integer.parseInt(parts[2]);
            } catch (Exception e) {
                LogUtils.d("修证版本号解析主错, version = {}", strVersion);
            }
        }

        if (parts.length >= 4) {
            try {
                version.build = Integer.parseInt(parts[3]);
            } catch (Exception e) {
                LogUtils.d("编译build号解析主错, version = {}", strVersion);
            }
        }


//            sLogger.info("解析后的版本:{}", version.toString());

        return version;
    }

    @Override
    public int compareTo(Version o) {
        if (major != o.major)
            return major > o.major ? 1 : -1;

        if (minor != o.minor)
            return minor > o.minor ? 1 : -1;

        if (revision != o.revision)
            return revision > o.revision ? 1 : -1;

        if (build != o.build)
            return build > o.build ? 1 : -1;

        return 0;
    }


    static public Version fromString(String strVersion) {
        String[] parts = strVersion.split("\\.");
        Version version = new Version();
        if (parts.length >= 1) {
            try {
                version.major = Integer.parseInt(parts[0]);
            } catch (Exception e) {
                LogUtils.d("主版本解析主错, version = {}", strVersion);
            }
        }

        if (parts.length >= 2) {
            try {
                version.minor = Integer.parseInt(parts[1]);
            } catch (Exception e) {
                LogUtils.d("子版本号解析主错, version = {}", strVersion);
            }
        }

        if (parts.length >= 3) {
            try {
                version.revision = Integer.parseInt(parts[2]);
            } catch (Exception e) {
                LogUtils.d("修证版本号解析主错, version = {}", strVersion);
            }
        }

        if (parts.length >= 4) {
            try {
                version.build = Integer.parseInt(parts[3]);
            } catch (Exception e) {
                LogUtils.d("编译build号解析主错, version = {}", strVersion);
            }
        }


//            sLogger.info("解析后的版本:{}", version.toString());

        return version;
    }
}
