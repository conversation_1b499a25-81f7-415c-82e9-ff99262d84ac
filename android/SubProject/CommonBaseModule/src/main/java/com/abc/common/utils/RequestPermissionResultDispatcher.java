package com.abc.common.utils;

import androidx.annotation.NonNull;

import java.util.ArrayList;


public class RequestPermissionResultDispatcher {
    public interface IListener {
        boolean onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults);
    }

    public static RequestPermissionResultDispatcher instance = new RequestPermissionResultDispatcher();

    ArrayList<IListener> mListeners = new ArrayList<>();

    boolean onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        ArrayList<IListener> listeners = new ArrayList<>(mListeners);
        for (IListener listener : listeners) {
            if (listener.onRequestPermissionsResult(requestCode, permissions, grantResults))
                return true;
        }

        return false;
    }

    public void addListener(IListener listener) {
        if (!mListeners.contains(listener))
            mListeners.add(listener);
    }

    public void removeListener(IListener listener) {
        mListeners.remove(listener);
    }
}
