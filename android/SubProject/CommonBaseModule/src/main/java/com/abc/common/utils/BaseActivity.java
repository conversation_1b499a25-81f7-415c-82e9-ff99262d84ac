package com.abc.common.utils;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class BaseActivity extends Activity {
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    protected void onResume() {
        super.onResume();
        ContextHolder.setsCurrentActivity(this);
        ActivityEventDispatcher.instance.onResume(this);
    }

    protected void onPause() {
        super.onPause();

        ActivityEventDispatcher.instance.onPause(this);
    }

    protected void onDestroy() {
        clearReferences();
        super.onDestroy();
        ActivityEventDispatcher.instance.onDestroy(this);
    }


    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);

        ActivityEventDispatcher.instance.onNewIntent(this, intent);
    }

    @Override
    protected void onStop() {
        super.onStop();

        ActivityEventDispatcher.instance.onStop(this);
    }

    private void clearReferences() {
        Activity currActivity = ContextHolder.getCurrentActivity();
        if (this.equals(currActivity))
            ContextHolder.setsCurrentActivity(null);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (!ActivityEventDispatcher.instance.onActivityResult(this, requestCode, resultCode, data))
            super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (!RequestPermissionResultDispatcher.instance.onRequestPermissionsResult(requestCode, permissions, grantResults))
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }
}
