/**
 * 
 */
package com.abc.common.utils;


import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.security.NoSuchAlgorithmException;


/*
 * Copyright (C) 2005-2010 TENCENT Inc.All Rights Reserved.
 * FileName：Md5.java
 * MD5的算法在RFC1321 中定义
 * 在RFC 1321中，给出了Test suite用来检验你的实现是否正确：
 * MD5 ("") = d41d8cd98f00b204e9800998ecf8427e
 * MD5 ("a") = 0cc175b9c0f1b6a831c399e269772661
 * MD5 ("abc") = 900150983cd24fb0d6963f7d28e17f72
 * MD5 ("message digest") = f96b697d7cb7938d525a2f31aaf161d0
 * MD5 ("abcdefghijklmnopqrstuvwxyz") = c3fcd3d76192e4007dfb496cca67e13b
 * History：
 * 1.0 samuelmo May 21, 2010 Create
 */

public class Md5Utils
{
	public static String getMD5(String string)
	{
		String s = null;
		if (string == null)
		{
			return null;
		}
		try
		{
			byte[] source = string.getBytes();
			java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
			md.update(source);
			return ByteUtils.byteToHexString(md.digest());
			// // 用字节表示就是 16 个字节
			// char str[] = new char[16 * 2];
			//
			// int k = 0;
			// for (int i = 0; i < 16; i++)
			// {
			// // 从第一个字节开始，对 MD5 的每一个字节转换成 16 进制字符的转换
			// byte byte0 = tmp[i];
			//
			// // 取字节中高 4 位的数字转换
			// str[k++] = hexDigits[byte0 >>> 4 & 0xf];
			//
			// // >>> 为逻辑右移，将符号位一起右移
			// // 取字节中低 4 位的数字转换
			// str[k++] = hexDigits[byte0 & 0xf];
			// }
			// // 换后的结果转换为字符串
			// s = new String(str);
		}
		catch (Exception e)
		{
		}
		return s;
	}

	public static byte[] getMD5(byte[] src,int start,int length)
	{
		try
		{
			java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
			md.update(src,start,length);
			return md.digest();
		}
		catch (Exception e)
		{
		}
		return null;
	}
	public static byte[] getMD5(byte[] src)
	{
		try
		{
			java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
			md.update(src);
			return md.digest();
		}
		catch (Exception e)
		{
		}
		return null;
	}

	/**
	 * 
	 * 对一个文件求他的md5值
	 * 
	 * @param f 要求md5值的文件
	 * 
	 * @return md5串
	 */

	public static String getMD5(File f)
	{
		FileInputStream fis = null;
		try
		{
			java.security.MessageDigest md = null;

			try
			{
				md = java.security.MessageDigest.getInstance("MD5");
			}
			catch (NoSuchAlgorithmException e)
			{
				e.printStackTrace();
			}
			fis = new FileInputStream(f);
			byte[] buffer = new byte[8192];
			int length;

			while ((length = fis.read(buffer)) != -1)
			{
				md.update(buffer, 0, length);
			}

			return ByteUtils.byteToHexString(md.digest());
		}
		catch (FileNotFoundException e)
		{
			return null;
		}
		catch (IOException e)
		{
			return null;
		}
		finally
		{
			try
			{
				if (fis != null)
					fis.close();
			}
			catch (IOException e)
			{
				e.printStackTrace();
			}
		}
	}

	/**
	 * 通过一个输入流获取一个文件的MD5
	 * 
	 * @param is
	 * @return
	 */
	public static byte[] getMD5(InputStream is)
	{
		byte[] rslt = null;
		if (is != null)
		{
			try
			{
				java.security.MessageDigest md = null;
				md = java.security.MessageDigest.getInstance("MD5");
				if (md != null)
				{
					byte[] buffer = new byte[8192];
					int length;

					while ((length = is.read(buffer)) != -1)
					{
						md.update(buffer, 0, length);
					}
					rslt = md.digest();
				}
			}
			catch (Throwable e)
			{
				rslt = null;
			}
		}

		return rslt;
	}

}
