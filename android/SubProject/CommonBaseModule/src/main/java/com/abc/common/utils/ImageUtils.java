package com.abc.common.utils;

import android.graphics.BitmapFactory;

import android.media.ExifInterface;
import android.text.TextUtils;

import java.io.IOException;


public class ImageUtils {
    public static Size getBitmapSize(String path) {
        Size r = new Size();
        BitmapFactory.Options op = new BitmapFactory.Options();
        op.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(path, op);

        int rotate = getPicOrientation(path);
        if (rotate == 90 || rotate == 270) {
            r.width = op.outWidth;
            r.height = op.outHeight;
        } else {
            r.height = op.outHeight;
            r.width = op.outWidth;
        }
        return r;
    }

    /**
     * 旋转带有方向信息的图片到正确的角度, Write by yozhang
     *
     * @param path 图片路径
     * @return
     */
    public static int getPicOrientation(String path) {
        int degree = 0;
        if (TextUtils.isEmpty(path)) {
            return degree;
        }

        try {
            ExifInterface exifInterface = new ExifInterface(path);
            int orientation = exifInterface.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    degree = 90;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    degree = 180;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    degree = 270;
                    break;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return degree;
    }
}
