package com.abc.common.utils;

import android.content.Context;
import android.content.SharedPreferences;

public class PublicSettings {
    private static final String SHARED_PREFERENCES_NAME = "publicsettings";
    private SharedPreferences mPreference;


    static PublicSettings sInstance;

    public static PublicSettings getInstance() {
        if (sInstance == null) {
            synchronized (PublicSettings.class) {
                if (sInstance == null) {
                    sInstance = new PublicSettings();
                }
            }
        }

        return sInstance;
    }

    private PublicSettings() {
        Context context = ContextHolder.getAppContext();
        mPreference = context.getSharedPreferences(SHARED_PREFERENCES_NAME, Context.MODE_MULTI_PROCESS);
    }

    public int getInt(String key, int defaultValue) {
        return mPreference.getInt(key, defaultValue);
    }

    public String getString(String key, String defaultValue) {
        return mPreference.getString(key, defaultValue);
    }

    public void setString(String key, String value) {
        SharedPreferences.Editor editor = mPreference.edit();
        editor.putString(key, value);
        editor.apply();
    }
}
