package com.abc.common.utils;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import java.util.HashMap;
import java.util.Map;

public class UrlUtils {

    public static Map<String, String> getUrlParams(@NonNull String url) {
        Map<String, String> paramMap = new HashMap<String, String>();
        if (TextUtils.isEmpty(url)) {
            return paramMap;
        }


        int pos1 = url.indexOf('?');
        int pos2 = url.indexOf('#');

        if (pos1 >= 0 && pos2 >= 0) {
            url = url.substring(pos1 + 1, pos2);
        } else if (pos1 >= 0) {
            url = url.substring(pos1 + 1);
        }


        String[] tokens = url.split("&");
        for (String token : tokens) {
            String[] keyValue = token.split("=");
            String key;
            String value = "";
            key = keyValue[0].trim();
            if (keyValue.length == 2) {
                value = keyValue[1].trim();
            }

            paramMap.put(key, value);
        }


        String[] params = url.split(",");
        for (String param : params) {
            String[] key_value = param.split("=", 2);
            if (key_value.length == 2) {
                paramMap.put(key_value[0], key_value[1]);
            }
        }
        return paramMap;
    }


    public static String deletePrefix(String url) {
        if (TextUtils.isEmpty(url)) {
            return null;
        } else {
            int pos;
            if (url.startsWith("page://")) {
                pos = url.indexOf("http://");
                if (pos != -1) {
                    return url.substring(pos);
                }
            }

            pos = url.indexOf("://");
            if (pos >= 0) {
                url = url.substring(pos + 3);
            }

            return url;
        }
    }

    public static String addParamsToUrl(String url, String paramsString) {
        String retUrl = "";
        if (TextUtils.isEmpty(url) || TextUtils.isEmpty(paramsString)) {
            return url;
        }
        String beforeSharp = url;
        String afterSharp = "";
        int sharpPos = url.indexOf('#');
        if (sharpPos > 0) {
            beforeSharp = url.substring(0, sharpPos);
            afterSharp = url.substring(sharpPos);
        }

        String splitSing = "?";
        if (beforeSharp.contains("?")) {
            splitSing = "&";
        }

        retUrl = beforeSharp + splitSing + paramsString + afterSharp;

        return retUrl;
    }


}
