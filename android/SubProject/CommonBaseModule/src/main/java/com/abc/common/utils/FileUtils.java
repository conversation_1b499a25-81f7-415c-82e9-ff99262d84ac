package com.abc.common.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Environment;
import android.os.StatFs;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;

import static android.content.ContentValues.TAG;

public class FileUtils {


    public static int SUCCESS = 0;
    public static int ERR_BASE = -1000;
    public static int ERR_SDCARD_NOT_AVAILABLE = ERR_BASE - 1;
    public static int ERR_SAVE_IMAGE_FAILED = ERR_BASE - 2;

    private static final int DEFAULT_BUFFER_SIZE = 1024 * 4;

    private final ByteBuffer[] mByteBufferPool;
    public int mByteBufferPoolAvailableSize;
    private int mByteBufferBufferNumber = 0;
    private int mMaxPoolSize = 0;
    public static FileUtils sInstance = null;

    public static synchronized FileUtils getInstance() {
        if (sInstance == null)
            sInstance = new FileUtils(4);
        return sInstance;
    }

    /**
     * Creates a new instance.
     *
     * @param maxPoolSize The max pool size.
     * @throws IllegalArgumentException If the max pool size is less than
     *                                  zero.
     */
    private FileUtils(int maxPoolSize) {
        if (maxPoolSize <= 0) {
            throw new IllegalArgumentException("The max pool size must be > 0");
        }
        mMaxPoolSize = maxPoolSize;
        mByteBufferPool = new ByteBuffer[maxPoolSize];
    }


    public static String readAssetFileAsString(String fileName) {
        InputStream input = null;
        try {
            input = FileUtils.openAssetsInput(fileName);
            return toString(input, "utf-8");

        } catch (Exception e) {
            e.fillInStackTrace();
        } finally {
            try {
                if (input != null) {
                    input.close();
                    input = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }


    public static final String DIR_DATA = "data";

    public static File getDataDir() {
        return ContextHolder.getAppContext().getDir("data", Context.MODE_PRIVATE);
    }

    /**
     * 获得名为childName的缓存文件夹
     * Note 如果dirName没有，那么将创建该文件夹，文件夹的权限为默认600
     *
     * @return
     */
    public static File createDir(File parent, String dirName) {
        if (parent == null || dirName == null || dirName.length() == 0)
            return null;

        File childDir = new File(parent, dirName);
        if (!childDir.exists())
            childDir.mkdirs();

        return childDir;
    }


    /**
     * 打开assets目录下文件流
     *
     * @param assetsName
     * @return
     */
    public static InputStream openAssetsInput(String assetsName) throws IOException {
        return ContextHolder.getAppContext().getAssets().open(assetsName);
    }


    public static String toString(InputStream in, String encoding) throws IOException, OutOfMemoryError {
        ByteBuffer buffer = FileUtils.read(in, -1);
        String ret = new String(buffer.array(), 0, buffer.position(), encoding);
        FileUtils.getInstance().releaseByteBuffer(buffer);
        return ret;
    }


    /**
     * 保存文件
     *
     * @param file
     * @param data
     * @return
     */
    public static boolean save(File file, byte[] data) {
        OutputStream os = null;
        try {
            os = openOutputStream(file);
            os.write(data, 0, data.length);

            return true;
        } catch (Exception e) {
            LogUtils.d(TAG, "save " + file + "error! " + e.getMessage());
            return false;
        } finally {
            try {
                if (os != null)
                    os.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static boolean writeStringToFile(File file, String info, String encoding) throws UnsupportedEncodingException {
        if (file == null) {
            return false;
        }
        if ("base64".equals(encoding)) {
            // 当encoding为base64时，需要将base64字符串解码为字节数组后写入文件
            byte[] decodedData = ByteUtils.decodeBase64(info);
            if (decodedData == null) {
                return false;
            }
            return FileUtils.save(file, decodedData);
        }
        return FileUtils.save(file, info.getBytes(encoding));
    }

    public static String readFileAsString(File file, String encoding) throws Exception {
        FileInputStream inputStream = new FileInputStream(file);
        if ("base64".equals(encoding)) {
            // 当encoding为base64时，需要读取文件的字节数据并编码为base64字符串
            try {
                ByteBuffer buffer = FileUtils.read(inputStream, -1);
                byte[] data = new byte[buffer.position()];
                buffer.rewind();
                buffer.get(data);
                FileUtils.getInstance().releaseByteBuffer(buffer);
                return ByteUtils.encodeBase64(data);
            } finally {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return toString(inputStream, encoding);
    }

    public static FileOutputStream openOutputStream(File file) throws IOException {
        if (file.exists()) {
            if (file.isDirectory()) {
                throw new IOException("File '" + file + "' exists but is a directory");
            }
            if (file.canWrite() == false) {
                throw new IOException("File '" + file + "' cannot be written to");
            }
        } else {
            File parent = file.getParentFile();
            if (parent != null && parent.exists() == false) {
                if (parent.mkdirs() == false) {
                    throw new IOException("File '" + file + "' could not be created");
                }
            }
        }
        return new FileOutputStream(file);
    }


    public static void forceMkdir(File directory) throws IOException {
        if (directory.exists()) {
            if (!directory.isDirectory()) {
                String message = "File " + directory + " exists and is " + "not a directory. Unable to create directory.";
                throw new IOException(message);
            }
            return;
        }

        if (!directory.mkdirs()) {
            // Double-check that some other thread or process hasn't made
            // the directory in the background
            if (!directory.isDirectory()) {
                String message = "Unable to create directory " + directory;
                throw new IOException(message);
            }
        }
    }


    /**
     * 复制文件
     *
     * @param oldPath
     * @param newPath
     * @return
     */
    public static synchronized boolean copyFile(String oldPath, String newPath) {
        boolean result = false;
        InputStream inStream = null;
        FileOutputStream fs = null;
        try {
            int bytesum = 0;
            int byteread = 0;
            File oldfile = new File(oldPath);
            if (oldfile.exists()) {
                // 文件存在时
                inStream = new FileInputStream(oldPath); // 读入原文件
                fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1024 * 100];
                while ((byteread = inStream.read(buffer)) != -1) {
                    bytesum += byteread; // 字节数 文件大小
                    fs.write(buffer, 0, byteread);
                }
                fs.flush();
                result = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } catch (OutOfMemoryError oom) {
            oom.printStackTrace();
        } finally {
            try {
                if (inStream != null) {
                    inStream.close();
                }
                if (fs != null) {
                    fs.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }


    public static boolean copyMoveFile(String strFilePath, String dstFilePath) {
        boolean result = renameTo(new File(strFilePath), new File(dstFilePath));
        if (!result) {
            result = copyFile(strFilePath, dstFilePath);
            if (result) {
                try {
                    delete(new File(strFilePath));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

    /**
     * 移动文件或者文件夹
     *
     * @param fromFile
     * @param toFile
     */
    public static boolean renameTo(File fromFile, File toFile) {
        boolean result = false;
        if (fromFile != null && toFile != null) {
            long start = System.currentTimeMillis();
            try {
                // File.renameTo在跨文件系统拷贝时会出错
                // 此处通过简单的判断两个文件的父目录相同来判断是否时同一文件系统 robinsli
                if (fromFile.getParent().equalsIgnoreCase(toFile.getParent())) {
                    if (fromFile.renameTo(toFile)) {

                        LogUtils.d("move", "move file success : " + fromFile.getName() + "      cost : " + (System.currentTimeMillis() - start));
                        result = true;
                    }
                }
                // 跨文件系统，用文件打开读取数据的方式.
                if (!result) {
                    InputStream inStream = null;
                    FileOutputStream fs = null;
                    try {
                        int bytesum = 0;
                        int byteread = 0;

                        // 文件存在时
                        inStream = new FileInputStream(fromFile); // 读入原文件
                        fs = new FileOutputStream(toFile);
                        byte[] buffer = new byte[1024 * 100];
                        while ((byteread = inStream.read(buffer)) != -1) {
                            bytesum += byteread; // 字节数 文件大小
                            fs.write(buffer, 0, byteread);
                        }
                        fs.flush();
                        if (inStream != null) {
                            inStream.close();
                        }
                        if (fs != null) {
                            fs.close();
                        }
                        fromFile.delete();// 拷贝成功，把源文件删掉.
                        result = true;
                    } catch (Exception e) {
                        // System.out.println("复制单个文件操作出错");
                        e.printStackTrace();
                    } finally {
                        try {
                            if (inStream != null) {
                                inStream.close();
                            }
                            if (fs != null) {
                                fs.close();
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    LogUtils.d("renameTo", "move file success : " + fromFile.getName() + "      cost : " + (System.currentTimeMillis() - start));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return result;
    }


    /**
     * 读取文件数据
     * 返回值为Byteuffer对象，使用完的ByteBuffer对象可以通过
     * FileUtils.getInstance().releasByteBuffer（）放回去继续使用。
     */
    public static ByteBuffer read(InputStream a, int x) throws IOException {
        ByteBuffer buffer = FileUtils.getInstance().acquireByteBuffer();
        buffer = readInputStreamToByteBuffer(a, buffer, 0, x);
        //		if (buffer.position() < x)
        //		{
        //			 FileUtils.getInstance().releaseByteBuffer(buffer);
        //			return buffer;
        //		}
        return buffer;
    }


    public static ByteBuffer readInputStreamToByteBuffer(InputStream input, ByteBuffer bs, long offset, int totalReadLen) throws IOException,
            OutOfMemoryError {
        if (input == null) {
            return bs;
        }

        byte[] buffer = FileUtils.getInstance().accqureByteArray();
        long alreadyReadcount = 0;
        int n = 0;
        input.skip(offset);
        int readLeftLen = totalReadLen;//剩余多少没有读.
        int readLen = 0;
        if (totalReadLen == -1)
            readLen = DEFAULT_BUFFER_SIZE;
        else
            readLen = readLeftLen < DEFAULT_BUFFER_SIZE ? readLeftLen : DEFAULT_BUFFER_SIZE;
        while (-1 != (n = input.read(buffer, 0, readLen))) {
            //先保证buffer的空间足够
            bs = reserveBufferSpace(bs, n);
            if (bs.remaining() < n)
                break;
            bs.put(buffer, 0, n);
            alreadyReadcount += n;
            readLeftLen -= n;
            if (readLeftLen == 0)
                break;
            if (totalReadLen == -1)
                readLen = DEFAULT_BUFFER_SIZE;
            else
                readLen = readLeftLen < DEFAULT_BUFFER_SIZE ? readLeftLen : DEFAULT_BUFFER_SIZE;

        }
        FileUtils.getInstance().releaseByteArray(buffer);
        return bs;
    }

    public void releaseByteArray(byte[] releaseArray) {
        synchronized (mByteArray) {
            if (bByteInUse == true && mByteArray == releaseArray) {
                bByteInUse = false;
            }
        }
    }

    public static void delete(File file) throws IOException, IllegalArgumentException, FileNotFoundException {
        if (file == null)
            return;

        if (file.isDirectory())
            cleanDirectory(file);

        // 把这部分逻辑从foreDelete移动过来.
        boolean filePresent = file.exists();
        if (!file.delete()) {
            if (!filePresent) {
                throw new FileNotFoundException("File does not exist: " + file);
            }
            String message = "Unable to delete file: " + file;
            throw new IOException(message);
        }
    }

    /**
     * 删除文件和目录。
     * 如果file是目录将删除目录及目录下的文件。
     *
     * @param file 要删除的文件.
     * @return false failed
     * true delete the file success .
     */
    public static boolean deleteQuietly(File file) {
        if (file == null)
            return false;

        if (!file.exists())
            return true;

        // If is dir.
        try {
            if (file.isDirectory())
                cleanDirectory(file);
        } catch (Exception ignored) {
            ignored.printStackTrace();
        }

        // Delete self.
        try {
            return file.delete();
        } catch (Exception ignored) {
            return false;
        }
    }

    /**
     * 清除目录下的所有文件。
     */
    public static void cleanDirectory(File directory) throws IOException, IllegalArgumentException {
        if (directory == null)
            return;

        if (!directory.exists()) {
            String message = directory + " does not exist";
            throw new IllegalArgumentException(message);
        }

        if (!directory.isDirectory()) {
            String message = directory + " is not a directory";
            throw new IllegalArgumentException(message);
        }

        File[] files = null;
        try {
            files = directory.listFiles();
        } catch (Error e) {
            e.printStackTrace();
        }
        if (files == null) { // null if security restricted
            throw new IOException("Failed to list contents of " + directory);
        }

        IOException exception = null;
        for (File file : files) {
            try {
                delete(file);
            } catch (IOException ioe) {
                exception = ioe;
            }
        }

        if (null != exception) {
            throw exception;
        }
    }


    /**
     * 把图片保存为png格式
     *
     * @return SUCCESS
     * ERR_SDCARD_NOT_AVAILABLE sdcard有问题.
     * ERR_SAVE_IMAGE_FAILED
     * @note you'd better toaster according to the return value.
     */

    public static int saveImage(File imageFile, Bitmap bitmapImage) {
        boolean isExternalFile = false;
        File sdDir = getSDcardDir();
        if (sdDir != null && sdDir.exists()) {
            isExternalFile = imageFile != null && imageFile.getAbsolutePath().startsWith(sdDir.getAbsolutePath());
        }
//		return false;
        if (isExternalFile && !hasSDcard()) {
            // MttToaster.show(R.string.sd_not_available, Toast.LENGTH_SHORT);
            return ERR_SDCARD_NOT_AVAILABLE;
        }

        boolean saveSucc = saveImage(imageFile, bitmapImage, Bitmap.CompressFormat.PNG);
        if (!saveSucc) {
            // MttToaster.show(MttResources.getString(R.string.save_image_failed),
            // Toast.LENGTH_SHORT);
            return ERR_SAVE_IMAGE_FAILED;
        }

        return SUCCESS;
    }

    public static boolean saveImage(File imageFile, Bitmap bitmap, Bitmap.CompressFormat format) {
        if (imageFile != null && bitmap != null && !bitmap.isRecycled()) {
            if (imageFile.exists()) {
                imageFile.delete();
            }

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            if (bitmap.isRecycled())
                return false;
            else {
                bitmap.compress(format, 100, baos);
            }
            int dataSize = baos.size();
            LogUtils.d(TAG, "[saveImage] dataSize:" + dataSize);
            // sd卡空间不足，则不进行保存
            boolean isExteranlFile = false;
            File sdDir = getSDcardDir();
            if (sdDir != null && sdDir.exists()) {
                isExteranlFile = (imageFile != null && imageFile.getAbsolutePath().startsWith(sdDir.getAbsolutePath()));
            }
//			return false;
            if (isExteranlFile && hasSDcard()) {
                if (dataSize > getSdcardFreeSpace()) {
                    return false;
                }
            }

            OutputStream os = null;
            try {
                byte[] picBytes = baos.toByteArray();
                LogUtils.d(TAG, "[saveImage] picBytes length:" + picBytes.length);
                closeQuietly(baos);
                os = new FileOutputStream(imageFile);
                os.write(picBytes);
                os.flush();
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            } catch (OutOfMemoryError er) {
                er.printStackTrace();
                return false;
            } finally {
                closeQuietly(baos);
                closeQuietly(os);
            }
        }

        return true;
    }


    /**
     * Get free space of internal Sdcard.
     * * robinsli
     * return -1 means getFreeDataSpace error
     */
    public static long getSdcardFreeSpace() {
        File sdDir = getSDcardDir();
        if (sdDir != null && sdDir.exists()) {
            return getSdcardFreeSpace(sdDir.getAbsolutePath());
        }
        return 0;
    }


    /**
     * Get free space of Sdcard.
     * robinsli
     * return -1 means getFreeDataSpace error
     */
    public static long getSdcardFreeSpace(String sdPath) {
        try {
            StatFs sf = new StatFs(sdPath);
            sf.restat(sdPath);
            return sf.getBlockSize() * (long) sf.getAvailableBlocks();
        } catch (IllegalArgumentException e) {
            return -1;
        }
    }

    /**
     * 是否可访问sd卡目录。与{@link #getSDcardDir()}方法搭配使用
     */
    public static boolean hasSDcard() {
        try {
            boolean hasSDCard = false;

            try {
                //android.os.Environment.getExternalStorageState(Environment.java:736)这个接口系统内部会挂，影响后面的执行
                hasSDCard = Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState());
            } catch (Throwable e) {
                hasSDCard = false;
            }

            // 有的中兴手机读取sd卡状态不对，即使sd卡可用但状态却是removed，因此通过向判断sd卡目录是否存在来判断状态
            if (!hasSDCard) {
                if (getSDcardDir() != null && getSDcardDir().exists()) {
                    return true;
                }
            }
            return hasSDCard;
        } catch (Throwable se) {
            se.printStackTrace();
            return false;
        }
    }


    public static File getSDcardDir() {
        try {
            File file = ContextHolder.getAppContext() != null ? ContextHolder.getAppContext().getExternalFilesDir(null) : null;
            if (file == null) {
                file = Environment.getExternalStorageDirectory();
            }
            return file;
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        //只能写两个默认值了
        String strSdPath0 = "/mnt/sdcard";
        File sd0 = new File(strSdPath0);
        if (sd0 != null && sd0.exists()) {
            return sd0;
        }
        String strSdPath1 = "/storage/sdcard0";
        File sd1 = new File(strSdPath1);
        if (sd1 != null && sd1.exists()) {
            return sd1;
        }

        return null;
    }


    public static void closeQuietly(Closeable closeable) {
        if (closeable == null)
            return;
        try {
            closeable.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static ByteBuffer reserveBufferSpace(ByteBuffer bs, int len) {
        if (bs.remaining() < len) {

            int n = (bs.capacity() + (len / 2048 + 1) * 2048);
            ByteBuffer bs2 = null;
            try {
                bs2 = ByteBuffer.allocate(n);
                bs2.put(bs.array(), 0, bs.position());
                return bs2;
            } catch (Throwable e) {
                return bs;
            }
        }
        return bs;
    }

    public boolean releaseByteBuffer(ByteBuffer instance) {
        synchronized (mByteBufferPool) {
            //			LogUtils.d("RobinsliJcePool", "InputStream accCountIn="+accCountIn+",accCountInHit="+accCountInHit+",ratio="+(accCountInHit*100/accCountIn)+",mByteBufferPoolAvailableSize="+mByteBufferPoolAvailableSize+",bufferSize="+instance.limit()+",len="+instance.position());
            if (isInPool(instance)) {
                return true;
            }
            //16K的大buffer，直接gc掉，不要hold住
            if (instance.capacity() > 128 * 1024)
                return true;
            if (mByteBufferPoolAvailableSize < mByteBufferPool.length) {
                instance.clear();
                mByteBufferPool[mByteBufferPoolAvailableSize] = instance;
                mByteBufferPoolAvailableSize++;

                return true;
            }
            return true;
        }
    }

    private boolean isInPool(ByteBuffer instance) {
        for (int i = 0; i < mByteBufferPoolAvailableSize; i++) {
            if (mByteBufferPool[i] == instance) {
                return true;
            }
        }
        return false;
    }


    private int accCountIn = 0;
    private int accCountInHit = 0;


    private byte[] mByteArray = new byte[DEFAULT_BUFFER_SIZE];
    private boolean bByteInUse = false;

    public byte[] accqureByteArray() {
        synchronized (mByteArray) {
            if (bByteInUse == false) {
                bByteInUse = true;
                return mByteArray;
            }
        }
        return new byte[DEFAULT_BUFFER_SIZE];
    }

    private ByteBuffer acquireByteBuffer() {
        synchronized (mByteBufferPool) {
            if (accCountIn != 0 && accCountIn % 20 == 0) {
//				LogUtils.d("RobinsliJcePool", "ByteBufferPool =" + accCountIn + ",accCountInHit=" + accCountInHit + ",ratio="
//						+ (accCountInHit * 100 / accCountIn) + ",mByteBufferPoolAvailableSize=" + mByteBufferPoolAvailableSize);
            }
            accCountIn++;
            if (mByteBufferPoolAvailableSize > 0) {
                accCountInHit++;
                ByteBuffer buffer = getABuffer();
                return buffer;
            } else if (mByteBufferBufferNumber < mMaxPoolSize) {
                accCountInHit++;
                ByteBuffer buffer = ByteBuffer.allocate(DEFAULT_BUFFER_SIZE);
                mByteBufferPool[mByteBufferPoolAvailableSize] = buffer;
                mByteBufferPoolAvailableSize++;
                mByteBufferBufferNumber++;
                buffer = getABuffer();
                return buffer;
            } else {
                return ByteBuffer.allocate(DEFAULT_BUFFER_SIZE);
            }
        }
    }


    private ByteBuffer getABuffer() {
        final int lastPooledIndex = mByteBufferPoolAvailableSize - 1;
        ByteBuffer instance = mByteBufferPool[lastPooledIndex];
        mByteBufferPool[lastPooledIndex] = null;
        mByteBufferPoolAvailableSize--;
        return instance;
    }


}
