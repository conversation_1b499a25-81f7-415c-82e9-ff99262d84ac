package com.abc.common.utils.bugly;

import android.text.TextUtils;

import com.abc.common.BuildConfig;
import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.ICommonCallback;
import com.abc.common.utils.bugly.callback.UpgradeCallback;
import com.tencent.bugly.Bugly;
import com.tencent.bugly.beta.Beta;
import com.tencent.bugly.beta.UpgradeInfo;
import com.tencent.bugly.crashreport.CrashReport;

import java.util.HashMap;
import java.util.Map;

/**
 * BuglyManager
 */
public class BuglyManager {

    private UpgradeInfo upgradeInfo;
    private static UpgradeCallback callback;

    private static BuglyManager sIntance;

    private String mExceptionSuffix = "";


    public static synchronized BuglyManager getInstance() {
        if (sIntance == null)
            sIntance = new BuglyManager();

        return sIntance;
    }

    private BuglyManager() {

    }

    public void init() {
        BuglyManager.getInstance().initBugly("77255e6b18");
    }

    public void initBugly(String appId) {
        Bugly.init(ContextHolder.getAppContext(), appId, BuildConfig.DEBUG);
    }

    public void setExceptionSuffix(String suffix) {
        this.mExceptionSuffix = suffix;
    }


    public void setUserId(Map<String, Object> params, ICommonCallback result) {
        if (params.containsKey("userId")) {
            String userId = (String) params.get("userId");
            Bugly.setUserId(ContextHolder.getAppContext(), userId);
        }
        result.onCallback(null, null);
    }


    public void setUserTag(Map<String, Object> params, ICommonCallback result) {
        if (params.containsKey("userTag")) {
            Integer userTag = (Integer) params.get("userTag");
            if (userTag != null)
                Bugly.setUserTag(ContextHolder.getAppContext(), userTag);
        }
        result.onCallback(true, null);
    }

    public void putUserData(Map<String, Object> params, ICommonCallback result) {
        if (params.containsKey("key") && params.containsKey("value")) {
            String userDataKey = (String) params.get("key");
            String userDataValue = (String) params.get("value");
            Bugly.putUserData(ContextHolder.getAppContext(), userDataKey, userDataValue);
        }
        result.onCallback(null, null);
    }


    public void checkUpgrade(Map<String, Object> params, final ICommonCallback result) {
        boolean isManual = false;
        boolean isSilence = false;
        boolean useCache = true;
        if (params.containsKey("isManual")) {
            isManual = (Boolean) params.get("isManual");
        }
        if (params.containsKey("isSilence")) {
            isSilence = (Boolean) params.get("isSilence");
        }
        if (params.containsKey("useCache")) {
            useCache = (Boolean) params.get("useCache");
        }
        final boolean finalUseCache = useCache;
        callback = new UpgradeCallback() {
            @Override
            public void onUpgrade(UpgradeInfo strategy) {
                if (finalUseCache) {
                    if (strategy != null) {
                        upgradeInfo = strategy;
                    }
                    result.onCallback(upgradeInfo, null);
                } else {
                    result.onCallback(strategy, null);
                }
            }
        };
        Beta.checkUpgrade(isManual, isSilence);
    }


    public void postCatchedException(int category, String errorType, String errorMsg, String stack, Map<String, String> extraInfo, final ICommonCallback result) {
        CrashReport.postException(category, errorType + "_" + this.mExceptionSuffix, errorMsg, stack, extraInfo);
        if (result != null) {
            result.onCallback(null, null);
        }
    }
}