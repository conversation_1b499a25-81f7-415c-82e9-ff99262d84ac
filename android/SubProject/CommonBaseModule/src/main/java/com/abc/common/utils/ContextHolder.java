package com.abc.common.utils;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import java.lang.ref.WeakReference;


public class ContextHolder {
    private static Context sAppContext;


    private static WeakReference<Activity> sCurrentActivity;

    private static WeakReference<Activity> sMainActivity;

    public static Context getAppContext() {
        return sAppContext;
    }

    public static void init(Context appContext) {
        sAppContext = appContext;
    }


    public static Handler uiHandler = new android.os.Handler(Looper.getMainLooper());

    public static Activity getCurrentActivity() {
        if (sCurrentActivity != null) {
            return sCurrentActivity.get();
        }

        return null;
    }

    public static Activity getMainActivity() {
        if (sMainActivity != null)
            return sMainActivity.get();

        return null;
    }

    public static void setMainActivity(Activity activity) {
        sMainActivity = new WeakReference<>(activity);
    }

    public static Activity getCurrentActivityWithDefault(Activity activity) {
        if (sCurrentActivity != null && sCurrentActivity.get() != null) {
            return sCurrentActivity.get();
        }

        return activity;
    }

    public static void setsCurrentActivity(Activity activity) {
        ContextHolder.sCurrentActivity = new WeakReference<Activity>(activity);
    }
}
