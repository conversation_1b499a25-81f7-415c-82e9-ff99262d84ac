package com.abc.common.utils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.CRC32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;



/*
 * Copyright (C) 2005-2010 TENCENT Inc.All Rights Reserved.
 * FileName：ZipUtil.java
 * Description：
 * History：
 * 1.0 mikeyou 2013-1-8 Create
 */

public class ZipUtils
{
	private static final long	MAX_FILE_LENGTH	= 1024 * 1024;
	private static CRC32		mCrc32			= new CRC32();
	private static ZipFile		mZipFile		= null;
	public static final String	EXT				= ".zip";
	private static final String	BASE_DIR		= "";

	// 符号"/"用来作为目录标识判断符
	private static final String	PATH			= "/";
	private static final int	BUFFER			= 1024;

//	public static void resetZipFile()
//	{
//		mZipFile = null;
//	}

	public static boolean unzip(File zipFile, File unzipDirectory, String entryName)
	{
		FileInputStream fins = null;
		try
		{
			fins = new FileInputStream(zipFile);
			return unzip(fins, unzipDirectory, entryName);
		}
		catch (FileNotFoundException e)
		{
			e.printStackTrace();
		}
		finally
		{
			if (fins != null)
			{
				try
				{
					fins.close();
				}
				catch (IOException e)
				{
					e.printStackTrace();
				}
			}
		}

		return false;
	}

//	public static boolean unzip(File zipFile, File unzipDirectory)
//	{
//		return unzip(zipFile, unzipDirectory, null);
//	}
//
//	public static boolean unzip(InputStream ins, File unzipDirectory)
//	{
//		return unzip(ins, unzipDirectory, null);
//	}

	public static void unzip(ZipEntry entry, InputStream in, File unzipDirectory) throws IOException
	{
		BufferedInputStream ins = null;
		BufferedOutputStream ous = null;
		try
		{
			if (!entry.isDirectory())
			{
				ins = new BufferedInputStream(in);

				File file = new File(unzipDirectory, entry.getName());
				ous = new BufferedOutputStream(new FileOutputStream(file));

				byte[] buffer = new byte[1024 * 4];
				int count = 0;
				while ((count = ins.read(buffer)) != -1)
				{
					ous.write(buffer, 0, count);
				}
				ous.flush();
			}
			else
			{
				File file = new File(unzipDirectory, entry.getName());
				file.mkdirs();
			}
		}
		catch (IOException e)
		{
			throw e;
		}
		finally
		{
			FileUtils.closeQuietly(ins);
			FileUtils.closeQuietly(ous);
		}
	}

	public static boolean unzip(InputStream ins, File unzipDirectory, String entryName)
	{
		boolean success = false;

		ZipInputStream zis = null;
		try
		{
			zis = new ZipInputStream(new BufferedInputStream(ins));
			ZipEntry entry;
			while ((entry = zis.getNextEntry()) != null)
			{
				if(entry.getName().contains("../"))
					continue;
				if (!entry.isDirectory())
				{
					if (entryName != null && !entryName.equals(entry.getName()))
						continue;

					File file = new File(unzipDirectory, entry.getName());
					file.getParentFile().mkdirs();
					FileOutputStream fos = new FileOutputStream(file, false);
					byte[] buffer = new byte[1024 * 32];
					int count;
					try
					{
						while ((count = zis.read(buffer)) != -1)
						{
							fos.write(buffer, 0, count);
						}
					}
					catch (IOException e)
					{
						e.printStackTrace();
						throw e;
					}
					finally
					{
						try
						{
							fos.close();
						}
						catch (Exception e2)
						{
							// TODO: handle exception
							e2.printStackTrace();
							throw e2;
						}
					}
				}
				else
				{
					File file = new File(unzipDirectory, entry.getName());
					file.mkdirs();
				}
			}
			success = true;
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				zis.close();
			}
			catch (IOException e)
			{
				e.printStackTrace();
			}
		}

		return success;
	}

//	public static boolean unZipQbs(String inputFilePath, String outputPath)
//	{
//		boolean hasBrokenFile = false;
//		ZipFile zipFile = null;
//		try
//		{
//			zipFile = new ZipFile(inputFilePath);
//			mZipFile = zipFile;
//		}
//		catch (IOException e)
//		{
//			e.printStackTrace();
//		}
//
//		if (zipFile == null)
//		{
//			return true;
//		}
//
//		Enumeration<? extends ZipEntry> entries = zipFile.entries();
//
//		while (entries.hasMoreElements())
//		{
//			ZipEntry entry = entries.nextElement();
//			String name = entry.getName();
//			boolean isDirectory = entry.isDirectory();
//			final long size = entry.getSize();
//			if (!isDirectory && size < MAX_FILE_LENGTH)
//			{
//				try
//				{
//					final long crc = entry.getCrc();
//					if (crc != -1)
//					{
//						boolean broken = checkCrc(crc, zipFile.getInputStream(entry), size);
//						if (broken)
//						{
//							return true;
//						}
//					}
//					// final int index = name.lastIndexOf('/');
//					// String endName = null;
//					// if (index != -1)
//					// {
//					// endName = name.substring(index + 1);
//					// }
//					// else
//					// {
//					// endName = name;
//					// }
//					copyFile(zipFile.getInputStream(entry), outputPath, name);
//				}
//				catch (IOException e)
//				{
//					e.printStackTrace();
//				}
//			}
//		}
//
//		return hasBrokenFile;
//	}

//	private static boolean checkCrc(long crc, InputStream entry, long fileSize)
//	{
//		boolean isBrokenFile = false;
//		BufferedInputStream inBuff = null;
//		mCrc32.reset();
//		long totalLen = 0;
//		try
//		{
//			inBuff = new BufferedInputStream(entry);
//
//			// 缓冲数组
//			byte[] b = new byte[1024 * 5];
//			int len;
//			while ((len = inBuff.read(b)) != -1)
//			{
//				totalLen += len;
//				mCrc32.update(b, 0, len);
//				// 读取出的文件内容比文件实际大小大
//				if (totalLen > fileSize)
//				{
//					return true;
//				}
//			}
//		}
//		catch (IOException e)
//		{
//			e.printStackTrace();
//		}
//		finally
//		{
//			// 关闭流
//			if (inBuff != null)
//				try
//				{
//					inBuff.close();
//				}
//				catch (IOException e)
//				{
//					e.printStackTrace();
//				}
//		}
//
//		if (totalLen != fileSize || crc != mCrc32.getValue())
//		{
//			LogUtils.d("unZip", "bad skin file!");
//			return true;
//		}
//
//		return isBrokenFile;
//	}

//	public static InputStream getIniStreamFromQbsFile(String inputPath, String inputName)
//	{
//		ZipInputStream zis = null;
//		try
//		{
//			FileInputStream fis = new FileInputStream(new File(inputPath + "/" + inputName));
//			zis = new ZipInputStream(new BufferedInputStream(fis));
//		}
//		catch (Exception e)
//		{
//			e.printStackTrace();
//			return null;
//		}
//
//		ZipEntry entry = null;
//		try
//		{
//			while ((entry = zis.getNextEntry()) != null)
//			{
//				boolean isDirectory = entry.isDirectory();
//				if (!isDirectory)
//				{
//					String name = entry.getName();
//					if (!StringUtils.isEmpty(name) && name.endsWith(".ini"))
//					{
//						return zis;
//					}
//				}
//			}
//		}
//		catch (UTFDataFormatException e2)
//		{
//			e2.printStackTrace();
//		}
//		catch (IOException e)
//		{
//			e.printStackTrace();
//		}
//
//		if (zis != null)
//		{
//			try
//			{
//				zis.close();
//			}
//			catch (IOException e)
//			{
//				e.printStackTrace();
//			}
//		}
//
//		// 压缩包里含有中文
//		return getIniStreamFromQbsFile2(inputPath, inputName);
//	}

//	private static InputStream getIniStreamFromQbsFile2(String inputPath, String inputName)
//	{
//		ZipFile zipFile = null;
//		try
//		{
//			zipFile = new ZipFile(inputPath + "/" + inputName);
//			mZipFile = zipFile;
//		}
//		catch (Exception e)
//		{
//			e.printStackTrace();
//			return null;
//		}
//
//		Enumeration<? extends ZipEntry> entries = zipFile.entries();
//
//		while (entries.hasMoreElements())
//		{
//			ZipEntry entry = entries.nextElement();
//			boolean isDirectory = entry.isDirectory();
//			if (!isDirectory)
//			{
//				String name = entry.getName();
//				if (!StringUtils.isEmpty(name) && name.endsWith(".ini"))
//				{
//					try
//					{
//						return zipFile.getInputStream(entry);
//					}
//					catch (IOException e)
//					{
//						e.printStackTrace();
//						break;
//					}
//				}
//			}
//		}
//		return null;
//	}

	// 复制文件
	private static void copyFile(InputStream inputStream, String outputPath, String fileName) throws IOException
	{
		BufferedInputStream inBuff = null;
		BufferedOutputStream outBuff = null;
		try
		{
			// 新建文件输入流并对它进行缓冲
			inBuff = new BufferedInputStream(inputStream);

			String filePath = outputPath + "/" + fileName;

			makeDirs(filePath);

			// 新建文件输出流并对它进行缓冲
			outBuff = new BufferedOutputStream(new FileOutputStream(new File(filePath)));

			// 缓冲数组
			byte[] b = new byte[1024 * 5];
			int len;
			while ((len = inBuff.read(b)) != -1)
			{
				outBuff.write(b, 0, len);
			}
			// 刷新此缓冲的输出流
			outBuff.flush();
		}
		finally
		{
			// 关闭流
			if (inBuff != null)
				inBuff.close();
			if (outBuff != null)
				outBuff.close();
		}
	}

	private static void makeDirs(String filePath)
	{
		int extIndex = filePath.lastIndexOf("/");
		if (extIndex == -1)
		{
			return;
		}
		String dirPath = filePath.substring(0, extIndex);

		File targetFile = new File(dirPath);
		if (targetFile != null && !targetFile.exists())
		{
			targetFile.mkdirs();
		}
	}

	/**
	 * 解压jar
	 * 
	 * @param unzipfile
	 * @param outPathString
	 * @return
	 *         robinsli move from JarPluginManager
	 */
	public static final String	TAG	= "JarPluginManager";

	public static boolean UnZip(String unzipfile, String outPathString, boolean unZipALL, List<File> pluginList)
	{

		ZipInputStream zin = null;
		try
		{
			File zipFile = new File(unzipfile); // 解压缩的文件路径(为了获取路径)
			if (!zipFile.exists())
			{
				LogUtils.d(TAG, "FILE !EXIST");
				return false;
			}

			zin = new ZipInputStream(new FileInputStream(zipFile));
			ZipEntry entry;
			while ((entry = zin.getNextEntry()) != null)
			{
				if (!entry.isDirectory())
				{ // 匹配文件，跳过文件夹
					String filePath = entry.getName();
					Pattern p = Pattern.compile(".*(SO|so)$"); // 匹配RSA后缀的文件
					Matcher m = p.matcher(filePath);
					if (m.matches() || unZipALL)
					{
						int index = filePath.lastIndexOf(File.separatorChar);
						String fileName = filePath.substring(index + 1, filePath.length());
						File file = new File(outPathString + File.separatorChar + fileName);
						if (!file.exists())
						{
							file.createNewFile();
						}
						// file.createNewFile();
						// get the output stream of the file
						FileOutputStream out = new FileOutputStream(file);
						int len;
						byte[] buffer = new byte[1024 * 512];
						if (pluginList != null)
							pluginList.add(file);

						try
						{
							// read (len) bytes into buffer
							while ((len = zin.read(buffer)) != -1)
							{
								// write (len) byte from buffer at the position
								// 0
								out.write(buffer, 0, len);
							}
							out.flush();
						}
						finally
						{
							out.close();
						}
					}
				}
			}// while
		}// try
		catch (IOException e)
		{
			e.printStackTrace();
			return false;
		}
		finally
		{
			if (zin != null)
			{
				try
				{
					zin.close();
				}
				catch (IOException e)
				{
					e.printStackTrace();
				}
			}
		}

		return true;
	}

	/**
	 * 压缩
	 * 
	 * @param srcFile
	 * @throws Exception
	 */
	public static void compress(File srcFile) throws Exception
	{
		String name = srcFile.getName();
		String basePath = srcFile.getParent();
		String destPath = basePath + name + EXT;
		compress(srcFile, destPath);
	}

	public static void compress(File[] srcFiles, String destFile) throws Exception
	{
		// 对输出文件做CRC32校验
		CheckedOutputStream cos = new CheckedOutputStream(new FileOutputStream(destFile), new CRC32());

		ZipOutputStream zos = new ZipOutputStream(cos);

		try {
			for (File file : srcFiles)
			{
				// 递归压缩
				compress(file, zos, BASE_DIR);
			}
		}
		finally {
			zos.flush();
			zos.close();
		}
	}

	/**
	 * 压缩
	 * 
	 * @param srcFile
	 *            源路径
	 * @param destPath
	 *            目标路径
	 * @throws Exception
	 */
	public static void compress(File srcFile, File destFile) throws Exception
	{

		// 对输出文件做CRC32校验
		CheckedOutputStream cos = new CheckedOutputStream(new FileOutputStream(destFile), new CRC32());

		ZipOutputStream zos = new ZipOutputStream(cos);

		compress(srcFile, zos, BASE_DIR);

		zos.flush();
		zos.close();
	}

	/**
	 * 压缩文件
	 * 
	 * @param srcFile
	 * @param destPath
	 * @throws Exception
	 */
	public static void compress(File srcFile, String destPath) throws Exception
	{
		compress(srcFile, new File(destPath));
	}

	/**
	 * 压缩
	 * 
	 * @param srcFile
	 *            源路径
	 * @param zos
	 *            ZipOutputStream
	 * @param basePath
	 *            压缩包内相对路径
	 * @throws Exception
	 */
	private static void compress(File srcFile, ZipOutputStream zos, String basePath) throws Exception
	{
		if (srcFile.isDirectory())
		{
			compressDir(srcFile, zos, basePath);
		}
		else
		{
			compressFile(srcFile, zos, basePath);
		}
	}

	/**
	 * 压缩
	 * 
	 * @param srcPath
	 * @throws Exception
	 */
	public static void compress(String srcPath) throws Exception
	{
		File srcFile = new File(srcPath);

		compress(srcFile);
	}

	/**
	 * 文件压缩
	 * 
	 * @param srcPath
	 *            源文件路径
	 * @param destPath
	 *            目标文件路径
	 * 
	 */
	public static void compress(String srcPath, String destPath) throws Exception
	{
		File srcFile = new File(srcPath);
		compress(srcFile, destPath);
	}

	/**
	 * 压缩目录
	 * 
	 * @param dir
	 * @param zos
	 * @param basePath
	 * @throws Exception
	 */
	private static void compressDir(File dir, ZipOutputStream zos, String basePath) throws Exception
	{

		File[] files = dir.listFiles();

		// 构建空目录
		if (files.length < 1)
		{
			ZipEntry entry = new ZipEntry(basePath + dir.getName() + PATH);

			zos.putNextEntry(entry);
			zos.closeEntry();
		}

		for (File file : files)
		{
			// 递归压缩
			compress(file, zos, basePath + dir.getName() + PATH);
		}
	}

	/**
	 * 文件压缩
	 * 
	 * @param file
	 *            待压缩文件
	 * @param zos
	 *            ZipOutputStream
	 * @param dir
	 *            压缩文件中的当前路径
	 * @throws Exception
	 */
	private static void compressFile(File file, ZipOutputStream zos, String dir) throws Exception
	{

		/**
		 * 压缩包内文件名定义
		 * 
		 * <pre>
		 * 如果有多级目录，那么这里就需要给出包含目录的文件名
		 * 如果用WinRAR打开压缩包，中文名将显示为乱码
		 * </pre>
		 */
		ZipEntry entry = new ZipEntry(dir + file.getName());

		zos.putNextEntry(entry);

		BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file));

		int count;
		byte data[] = new byte[BUFFER];
		while ((count = bis.read(data, 0, BUFFER)) != -1)
		{
			zos.write(data, 0, count);
		}
		bis.close();

		zos.closeEntry();
	}

	/**
	 * 压缩输入字符串为byte[]数组
	 * 
	 * @param source
	 * @return
	 * @throws Exception
	 */
	public static byte[] compressString(String source) throws Exception
	{
		if (source == null)
			return null;

		byte[] compressed;
		ByteArrayOutputStream out = null;
		ZipOutputStream zout = null;
		try
		{
			out = new ByteArrayOutputStream();
			zout = new ZipOutputStream(out);
			zout.putNextEntry(new ZipEntry("0"));
			zout.write(source.getBytes());
			zout.closeEntry();
			compressed = out.toByteArray();
		}
		catch (IOException e)
		{
			compressed = null;
		}
		finally
		{
			if (zout != null)
			{
				try
				{
					zout.close();
				}
				catch (IOException e)
				{
				}
			}
			if (out != null)
			{
				try
				{
					out.close();
				}
				catch (IOException e)
				{
				}
			}
		}
		return compressed;
	}

}
