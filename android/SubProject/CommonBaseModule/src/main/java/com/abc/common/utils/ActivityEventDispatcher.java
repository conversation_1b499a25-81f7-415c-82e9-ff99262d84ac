package com.abc.common.utils;

import android.app.Activity;
import android.content.Intent;
import android.view.View;

import java.util.ArrayList;


public class ActivityEventDispatcher {


    public interface ActivityResultListener {
        boolean onActivityResult(Activity activity, int requestCode, int resultCode, Intent data);
    }


    public static class ActivityLifecycleListener {
        public boolean onDestroy(Activity activity) {
            return false;
        }

        public void onPause(Activity activity) {

        }

        public boolean onNewIntent(Activity activity, Intent intent) {
            return false;
        }

        public void onStop(Activity activity) {

        }

        public void onResume(Activity activity) {

        }
    }


    public static ActivityEventDispatcher instance = new ActivityEventDispatcher();

    private ArrayList<ActivityResultListener> mActivityResultListeners = new ArrayList<>();


    private ArrayList<ActivityLifecycleListener> mActivityLifecycleListener = new ArrayList<>();

    boolean onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        ArrayList<ActivityResultListener> listeners = new ArrayList<>(mActivityResultListeners);
        for (ActivityResultListener listener : listeners) {
            if (listener.onActivityResult(activity, requestCode, resultCode, data)) return true;
        }

        return false;
    }


    public void onPause(Activity activity) {
        ArrayList<ActivityLifecycleListener> listeners = new ArrayList<>(mActivityLifecycleListener);
        for (ActivityLifecycleListener listener : listeners) {
            listener.onPause(activity);
        }
    }

    void onDestroy(Activity activity) {
        ArrayList<ActivityLifecycleListener> listeners = new ArrayList<>(mActivityLifecycleListener);
        for (ActivityLifecycleListener listener : listeners) {
            listener.onDestroy(activity);
        }
    }

    void onNewIntent(Activity activity, Intent intent) {
        ArrayList<ActivityLifecycleListener> listeners = new ArrayList<>(mActivityLifecycleListener);
        for (ActivityLifecycleListener listener : listeners) {
            listener.onNewIntent(activity, intent);
        }
    }


    public void onResume(Activity activity) {
        ArrayList<ActivityLifecycleListener> listeners = new ArrayList<>(mActivityLifecycleListener);
        for (ActivityLifecycleListener listener : listeners) {
            listener.onResume(activity);
        }
    }


    public void addActivityResultListener(ActivityResultListener listener) {
        if (!mActivityResultListeners.contains(listener))
            mActivityResultListeners.add(listener);
    }

    public void removeActivityResultListener(ActivityResultListener listener) {
        mActivityResultListeners.remove(listener);
    }


    public void addActivityLifecycleListener(ActivityLifecycleListener listener) {
        if (!mActivityLifecycleListener.contains(listener))
            mActivityLifecycleListener.add(listener);
    }

    public void removeActivityLifecycleListener(ActivityLifecycleListener listener) {
        mActivityLifecycleListener.remove(listener);
    }

    public void onStop(Activity activity) {
    }

}
