package com.abc.common.utils;

import android.annotation.TargetApi;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.KeyguardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Point;
import android.graphics.Rect;
import android.os.Binder;
import android.os.Build;
import android.os.Environment;
import android.os.PowerManager;
import android.os.StatFs;
import android.provider.Settings.Secure;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.view.Window;
import android.view.WindowManager;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class DeviceUtils {
    private static final String TAG = "DeviceUtils";

    /**
     * The default return value of any method in this class when an
     * error occurs or when processing fails (Currently set to -1). Use this to
     * check if
     * the information about the device in question was successfully obtained.
     */
    public static final int DEVICEINFO_UNKNOWN = -1;

    public static final int SCREEN_DENSITY_LDPI = 120;
    public static final int SCREEN_DENSITY_MDPI = 160;
    public static final int SCREEN_DENSITY_HDPI = 240;
    public static final int SCREEN_DENSITY_XHDPI = 320;
    public static final int SCREEN_DENSITY_XXHDPI = 480;

    // 固件版本1.5 的TelephonyManager中木有定义的常量在这里定义
    private static final int PHONE_TYPE_CDMA = 2;

    private static final int NETWORK_TYPE_HSDPA = 8;

    private static final int NETWORK_TYPE_HSUPA = 9;

    private static final int NETWORK_TYPE_HSPA = 10;


    private static final int NETWORK_TYPE_CDMA = 4;

    private static final int NETWORK_TYPE_EVDO_0 = 5;

    private static final int NETWORK_TYPE_EVDO_A = 6;

    private static final int NETWORK_TYPE_1xRTT = 7;

    public static boolean isMIUIV6 = false;
    public static boolean isEMUI = false;
    public static boolean isEMUI3 = false;
    public static boolean isEMUI4 = false;
    public static boolean isEMUI5 = false;
    public static boolean isEUI = false;
    public static boolean isWildFire = false;
    public static boolean isChaCha = false;
    private static boolean isMIUI = false;
    private static boolean isSmartisanOS = false;
    public static boolean isMeizuM9 = false;
    public static boolean isN882E = false;
    public static boolean isI9000 = false;
    public static boolean isXT800 = false;
    public static boolean isS5360 = false;
    public static boolean isS5830 = false;
    public static boolean isCoolpadF2 = false;
    public static boolean isU2 = false;
    public static boolean isW719 = false;
    public static boolean isW619 = false;
    public static boolean isU8500 = false;
    public static boolean isVivo = false;
    public static boolean isVivoS7 = false;
    public static boolean isGt5830 = false;
    public static boolean isI939 = false;
    public static boolean isHMNote = false;
    public static boolean isMIPAD = false;
    public static boolean isMI4 = false;
    public static boolean isHuaweiG700_t00 = false;
    public static boolean isLetv = false;
    public static boolean isLe = false;
    public static boolean isZUI = false;

    public static boolean isS6 = false;
    public static boolean isS6_Edge = false;

    public static boolean isSamsung = false;

    // surfaceView在dialog中detach会crash的问题
    public static boolean isE15i = false;
    public static boolean isU20I = false;
    public static boolean isHtcHero = false;

    // v880
    public static boolean isV880 = false;
    public static boolean isW9913 = false;
    public static boolean isZteV889D = false;
    public static boolean isS5830i = false;
    public static boolean isC8500 = false;
    public static boolean isK30 = false;
    public static boolean isK860i = false;
    public static boolean isI9300 = false;
    public static boolean isI9108 = false;
    public static boolean isS7562 = false;
    public static boolean isI9100 = false;
    public static boolean isCoolpad5892 = false;
    public static boolean isNX403A = false;
    public static boolean isFind5 = false;
    public static boolean isFind7 = false;
    public static boolean isN5 = false;
    public static boolean isN5x = false;
    public static boolean isBullHead = false;
    public static boolean isShamu = false;
    public static boolean isN6 = false;
    public static boolean isN6P = false;
    // mx4
    public static boolean isMX3 = false;
    public static boolean isMX4 = false;
    public static boolean isXL39h = false;

    public static boolean isMi2S = false;

    public static boolean isMiNote = false;


    public static boolean isHTCm8T = false;

    // 是否为乐蛙os
    public static boolean isLewa = false;

    public static boolean isCoolpad7296 = false;
    public static boolean isASUS_ZenFone_6 = false;

    public static boolean isHUAWEI_P6_U06 = false;
    public static boolean isHUAWEI_H60_L01 = false;
    public static boolean isHUAWEI_MT7 = false;
    public static boolean isHuaweiY511_t00 = false;
    // nubia系列
    public static boolean isZTE_N958St = false;
    public static boolean isZTE_N918St = false;
    public static boolean isZTE_NX511J = false;
    public static boolean isZTE_NX505J = false;

    //oppo
    public static boolean isOppo = false;
    public static boolean isOppoR7s = false;
    public static boolean isOppoR7Plus = false;
    public static boolean isOppoR9Plus = false;
    public static boolean isOppoR9 = false;
    public static boolean isOppoA11 = false;
    public static boolean isOppoFind7 = false;
    public static boolean isOppoR8207 = false;

    public static boolean isDoov = false;
    public static boolean isZUKZ1 = false;
    public static boolean isZTEC2016 = false;

    //google
    public static boolean isGoogleAndroid = false;
    private static boolean isCheckedGoogleAndroid = false;

    // 1.5 sdk
    private static int sSdkVersion = -1;

    private static boolean mMIUIV6SCheckFinished = false;
    private static boolean mMIUISCheckFinished = false;
    private static boolean mSmartisanOSCheckFinished = false;
    private static int mScreenWidth = 0;
    private static int mScreenHeight = 0;
    private static boolean mLargeScrrenCheckFinished = false;
    private static boolean mIsLargeScrren = false;
    public static int mRootStatus = -1;
    private static int mIsArmv5 = -1;
    private static int mIsMT6573 = -1;
    private static int mIsMT6577 = -1;
    private static boolean mIsSamsungCheckFinished = false;

    // android id
    private static String mAndroidId = "";

    public static final boolean sIsCupcake = false;//getSdkVersion() < 4;
    public static final boolean sLessIcecream = false;//getSdkVersion() < 14;
    public static final boolean sLessHoneycomb = false;//getSdkVersion() < 11;
    public static final boolean sLessGingerbread = false;//getSdkVersion() < 9;

    public static final String KVivoDeviceKey = "ro.vivo.os.version";

    private static boolean isFlyme;

    private static boolean mFlymeCheckFinished;

    private static boolean mFlymeV4checkFinished;

    private static boolean isFlymeV4;


    public static final int NETWORK_TYPE_NONE = 0;
    public static final int NETWORK_TYPE_WIFI = 1;
    public static final int NETWORK_TYPE_2G = 2;
    public static final int NETWORK_TYPE_3G = 3;

    // 手机性能
    public static final int DEVICEINFO_LOW_PERF = 0;
    public static final int DEVICEINFO_MIDDLE_PERF = 1;
    public static final int DEVICEINFO_HIGH_PERF = 2;
    private static int sMobilePerformance = DEVICEINFO_UNKNOWN;
    // 评估设备年代
    private static int sDeviceYear = DEVICEINFO_UNKNOWN;

    private static boolean mCheckIsEMUIFinished = false;

    private static boolean mCheckIsEMUI3Finished = false;

    private static boolean mCheckIsEMUI4Finished = false;

    private static boolean mCheckIsEMUI5Finished = false;

    private static boolean mCheckIsEUIFinished = false;

    private static boolean mCheckIsZUIFinished = false;

    private static boolean mMIUIV8SCheckFinished = false;

    private static boolean mMIUIV9SCheckFinished = false;

    private static boolean isMIUIV8 = false;

    private static boolean isMIUIV9 = false;

    private static boolean mIsInSystemMultiWindow = false;
    private static int mSystemMultiWindowState = -1; //0为正常模式，1为分屏模式

    private static int mFrameWidthDp = 0;
    private static int mFrameHeightDp = 0;

    public static final int MINIMUM_TABLET_WIDTH_DP = 590;
    public static final int MINIMUM_FORCE_TABLET_WIDTH_DP = 700;

    public static int mBrowserActiveState = 0;//MO声在前台

    static {
        LogUtils.d("DeviceUtils", Build.MODEL);
        LogUtils.d("DeviceUtils.KVivoDeviceKey = ", getProp(KVivoDeviceKey));


        Context context = ContextHolder.getAppContext();
        if (context != null) {
            mFrameWidthDp = context.getResources().getConfiguration().screenWidthDp;
            mFrameHeightDp = context.getResources().getConfiguration().screenHeightDp;
        }
        if (getDeviceBrand().trim().contains("oppo")) {
            isOppo = true;
        }
        String model = Build.MODEL.trim().toLowerCase();
        if (model.contains("wildfire"))
            isWildFire = true;
        else if (model.contains("hm note"))
            isHMNote = true;
        else if (model.contains("mi 4"))
            isMI4 = true;
        else if (model.contains("mi note"))
            isMiNote = true;
        else if (model.contains("chacha"))
            isChaCha = true;
        else if (model.contains("g700-t00"))
            isHuaweiG700_t00 = true;
        else if (model.contains("y511-t00"))
            isHuaweiY511_t00 = true;
        else if (model.contains("e15i"))
            isE15i = true;
        else if (model.contains("u20i"))
            isU20I = true;
        else if (model.equals("m9"))
            isMeizuM9 = true;
        else if (model.equals("htc hero"))
            isHtcHero = true;
        else if (model.equals("htc m8t"))
            isHTCm8T = true;
        else if (model.equals("zte-u v880"))
            isV880 = true;
        else if (model.contains("zte n882e"))
            isN882E = true;
        else if (model.contains("gt-i9000"))
            isI9000 = true;
        else if (model.contains("xt800"))
            isXT800 = true;
        else if (model.contains("s5360"))
            isS5360 = true;
        else if (model.contains("mi pad")) {
            isMIPAD = true;
        } else if (model.contains("s5830")) {
            isS5830 = true;
            if (model.contains("s5830i")) {
                isS5830i = true;
            }
        } else if (model.contains("w9913"))
            isW9913 = true;
        else if (model.equals("u2"))
            isU2 = true;
        else if (model.contains("w719"))
            isW719 = true;
        else if (model.equalsIgnoreCase("zte v889d"))
            isZteV889D = true;
        else if (model.contains("w619"))
            isW619 = true;
        else if (model.contains("c8500"))
            isC8500 = true;
        else if (model.contains("u8500"))
            isU8500 = true;
        else if (model.contains("gt-s5830"))
            isGt5830 = true;
        else if (model.contains("sm-g925"))
            isS6_Edge = true;
        else if (model.contains("sm-g920"))
            isS6 = true;
        else if (model.contains("sch-i939"))
            isI939 = true;
        else if (model.contains("vivo") || !TextUtils.isEmpty(getProp(KVivoDeviceKey))) {
            isVivo = true;
            LogUtils.d("DeviceUtils", model);
            if (model.contains("s7")) {
                isVivoS7 = true;
            }
        } else if (model.contains("k30")) {
            isK30 = true;
        } else if (model.contains("k860i"))
            isK860i = true;
        else if (model.contains("gt-i9300")) {
            isI9300 = true;
        } else if (model.contains("gt-i9108")) {
            isI9108 = true;
        } else if (model.contains("gt-s7562"))
            isS7562 = true;
        else if (model.contains("gt-i9100"))
            isI9100 = true;
        else if (model.equalsIgnoreCase("m353") || Build.DEVICE.equalsIgnoreCase("mx3")) {
            isMX3 = true;
        } else if (model.equalsIgnoreCase("mx4") || Build.DEVICE.equalsIgnoreCase("mx4")) {
            isMX4 = true;
        } else if (model.equals("xl39h")) {
            isXL39h = true;
        } else if (model.equals("coolpad 5892")) {
            isCoolpad5892 = true;
        } else if (model.equals("nx403a")) {
            isNX403A = true;
        } else if (model.contains("coolpad 8675")) {
            isCoolpadF2 = true;
        } else if (model.equals("find 5")) {
            isFind5 = true;
        } else if (model.equals("find 7")) {
            isFind7 = true;
        } else if (model.equals("nexus 5")) {
            isN5 = true;
        } else if (model.equals("nexus 6")) {
            isN6 = true;
        } else if (model.equals("nexus 6p")) {
            isN6P = true;
        } else if (model.equals("nexus 5x")) {
            isN5x = true;
        } else if (model.contains("aosp on bullhead")) {
            isBullHead = true;
        } else if (model.contains("aosp on shamu")) {
            isShamu = true;
        } else if (model.equals("coolpad 7296")) {
            isCoolpad7296 = true;
        } else if (model.equalsIgnoreCase("asus_t00g")) {
            isASUS_ZenFone_6 = true;
        } else if (model.equals("huawei p6-u06")) {
            isHUAWEI_P6_U06 = true;
        } else if (model.equals("h60-l01")) {
            isHUAWEI_H60_L01 = true;
        } else if (model.equals("h60-l02")) {
            isHUAWEI_H60_L01 = true;
        } else if (model.equals("h60-l03")) {
            isHUAWEI_H60_L01 = true;
        } else if (model.contains("mt7-tl10")) {
            isHUAWEI_MT7 = true;
        } else if (model.contains("mt7-cl00")) {
            isHUAWEI_MT7 = true;
        } else if (model.contains("mt7-tl00")) {
            isHUAWEI_MT7 = true;
        } else if (model.contains("mt7-ul00")) {
            isHUAWEI_MT7 = true;
        } else if (model.equals("n958st")) {
            isZTE_N958St = true;
        } else if (model.equals("n918st")) {
            isZTE_N918St = true;
        } else if (model.equals("nx511j")) {
            isZTE_NX511J = true;
        } else if (model.equals("nx505j")) {
            isZTE_NX505J = true;
        } else if (model.equals("mi 2s")) {
            isMi2S = true;
        } else if (model.matches(".*oppo.*")) {
            isOppo = true;
            if (model.matches(".*oppo[\\s\\/\\_\\&\\|]*r7s.*")) {
                isOppoR7s = true;
            } else if (model.replaceAll("[ |\\/|\\_|\\&|\\|]", "").contains("oppor9plus")) {
                isOppoR9Plus = true;
            } else if (model.replaceAll("[ |\\/|\\_|\\&|\\|]", "").contains("oppor9")) {
                isOppoR9 = true;
            }
        } else if (model.contains("x9007")) {
            String brand = getDeviceBrand();
            if (brand.contains("oppo")) {
                isOppoFind7 = true;
                isOppo = true;
            }
        } else if (model.contains("r8207")) {
            String brand = getDeviceBrand();
            if (brand.contains("oppo")) {
                isOppoR8207 = true;
                isOppo = true;
            }
        } else if (model.contains("a11")) {
            String brand = getDeviceBrand();
            if (brand.contains("oppo")) {
                isOppoA11 = true;
                isOppo = true;
            }
        } else if (model.contains("r9plus")) {
            String brand = getDeviceBrand();
            if (brand.contains("oppo")) {
                isOppoR9Plus = true;
                isOppo = true;
            }
        } else if (model.contains("r9plus")) {
            String brand = getDeviceBrand();
            if (brand.contains("oppo")) {
                isOppoR9Plus = true;
                isOppo = true;
            }
        } else if (model.contains("r7plus")) {
            String brand = getDeviceBrand();
            if (brand.contains("oppo")) {
                isOppoR7Plus = true;
                isOppo = true;
            }
        } else if (model.contains("le")) {
            String brand = getDeviceBrand();
            if (brand.contains("le")) {
                isLe = true;
            }
        } else if (model.contains("doov")) {
            isDoov = true;
        } else if (model.toLowerCase().contains("zuk z1")) {
            isZUKZ1 = true;
        } else if (model.toLowerCase().contains("zte c2016")) {
            isZTEC2016 = true;
        }
        String brand = getDeviceBrand().trim();
        if (brand.contains("letv")) {
            isLetv = true;
        }

        // sHasSmartBar = hasSmartBar();

        String user = Build.USER;
        if (!TextUtils.isEmpty(user)) {
            isLewa = user.trim().toLowerCase().contains("lewa");
        }
    }

    // nubia 截图问题系列机型，这类机型使用picture方式去内核截图时，画不出文字。
    public static boolean isNubiaSeries() {
        return isZTE_NX505J || isZTE_NX511J || isZTE_N958St;
    }


    public static boolean isSamsung() {
        if (!mIsSamsungCheckFinished) {
            if (!TextUtils.isEmpty(Build.BRAND) && Build.BRAND.toLowerCase().contains("samsung")) {
                isSamsung = true;
                mIsSamsungCheckFinished = true;
            }
        }
        return isSamsung;
    }

    public static boolean isGoogleOs() {
        if (!isCheckedGoogleAndroid) {
            String clienidBaseStr = DeviceUtils.getSystemProperty("ro.com.google.clientidbase");
            if (!TextUtils.isEmpty(clienidBaseStr) && clienidBaseStr.contains("google")) {
                isGoogleAndroid = true;
            }
            isCheckedGoogleAndroid = true;
        }

        return isGoogleAndroid;
    }

    public static boolean isMIUI() {
        if (!mMIUISCheckFinished) {
            isMIUI = checkMIUI();
        }
        return isMIUI;
    }

    public static boolean isMIUIV6() {
        if (!mMIUIV6SCheckFinished && isMIUI()) {
            isMIUIV6 = checkMIUIVersion(6);
            mMIUIV6SCheckFinished = true;
        }
        return isMIUIV6;
    }

    public static boolean isMIUIV8() {
        if (!mMIUIV8SCheckFinished && isMIUI()) {
            isMIUIV8 = checkMIUIVersion(8);
            mMIUIV8SCheckFinished = true;
        }
        return isMIUIV8;
    }

    public static boolean isMIUIV9() {
        if (!mMIUIV9SCheckFinished && isMIUI()) {
            isMIUIV9 = checkMIUIVersion(9);
            mMIUIV9SCheckFinished = true;
        }
        return isMIUIV9;
    }

    public static boolean isFlyme() {
        if (!mFlymeCheckFinished) {
            isFlyme = checkFlyme();
        }
        return isFlyme;
    }

    public static boolean isFlymeV4() {
        if (!mFlymeV4checkFinished) {
            isFlymeV4 = checkFlymeOSV4();
        }
        return isFlymeV4;
    }

    private static boolean checkFlyme() {
        boolean isFlyme = Build.ID.trim().toLowerCase().contains("flyme");

        if (!isFlyme) {
            Context context = ContextHolder.getAppContext();
            if (context != null) {
                try {
                    PackageManager pm = context.getPackageManager();
                    PackageInfo info = pm.getPackageInfo("com.meizu.systemwallpaper", 0);
                    if (info != null) {
                        isFlyme = true;
                    }
                    mFlymeCheckFinished = true;
                } catch (Exception e) {
                }
            }
        } else {
            mFlymeCheckFinished = true;
        }
        return isFlyme;
    }

    private static boolean checkFlymeOSV4() {
        String propName = "ro.build.display.id";
        String line = getSystemProperty(propName);
        if (!TextUtils.isEmpty(line)) {
            LogUtils.d(TAG, "flymeOs:" + line);
            int index = line.indexOf("Flyme OS");
            int versionIndex = 0;
            if (index == -1) {
                index = line.indexOf("Flyme");
                versionIndex = index + 6;
            } else {
                versionIndex = index + 9;
            }
            if (index != -1) {
                if (versionIndex < line.length()) {
                    char version = line.charAt(versionIndex);
                    try {
                        int ver = Integer.valueOf(String.valueOf(version));
                        if (ver >= 4) {
                            mFlymeV4checkFinished = true;
                            return true;
                        }
                    } catch (NumberFormatException e) {
                        return false;
                    }
                }
            }
        }
        return false;
    }

    public static String getSystemProperty(String propName) {
        Class<?> clsSystemProperties = tryClassForName("android.os.SystemProperties");
        Method mtdGet = tryGetMethod(clsSystemProperties, "get", String.class);
        return tryInvoke(mtdGet, null, propName);
    }

    public static String getMIUISystemProperty() {
        String property = getSystemProperty("ro.miui.ui.version.name");
        if (TextUtils.isEmpty(property)) {
            return "NUKNOW";
        }
        return property;
    }

    public static boolean isEMUI() {
        if (!mCheckIsEMUIFinished) {
            String verName = getSystemProperty("ro.build.version.emui");
            if (!TextUtils.isEmpty(verName)) {
                isEMUI = verName.toLowerCase(Locale.ENGLISH).startsWith("emotionui");
            }
            mCheckIsEMUIFinished = true;
        }
        return isEMUI;
    }

    public static boolean isEMUI3() {
        if (!mCheckIsEMUI3Finished) {
            String verName = getSystemProperty("ro.build.version.emui");
            if (!TextUtils.isEmpty(verName)) {
                isEMUI3 = verName.toLowerCase(Locale.ENGLISH).startsWith("emotionui_3");
            }
            mCheckIsEMUI3Finished = true;
        }
        return isEMUI3;
    }

    public static boolean isEMUI4() {
        if (!mCheckIsEMUI4Finished) {
            String verName = getSystemProperty("ro.build.version.emui");
            if (!TextUtils.isEmpty(verName)) {
                isEMUI4 = verName.toLowerCase(Locale.ENGLISH).startsWith("emotionui_4");
            }
            mCheckIsEMUI4Finished = true;
        }
        return isEMUI4;
    }

    public static boolean isEMUI5() {
        if (!mCheckIsEMUI5Finished) {
            String verName = getSystemProperty("ro.build.version.emui");
            if (!TextUtils.isEmpty(verName)) {
                isEMUI5 = verName.toLowerCase(Locale.ENGLISH).startsWith("emotionui_5");
            }
            mCheckIsEMUI5Finished = true;
        }
        return isEMUI5;
    }

    // 判断是否是联想ZUI ROM
    public static boolean isZUI() {
        if (!mCheckIsZUIFinished) {
            String verName = getSystemProperty("ro.com.zui.version");
            if (!TextUtils.isEmpty(verName)) {
                isZUI = true;
            }
            mCheckIsZUIFinished = true;
        }
        return isZUI;
    }

    // 判断是否是乐视EUI ROM
    public static boolean isEUI() {
        if (!mCheckIsEUIFinished) {
            String verName = getSystemProperty("ro.letv.release.version");
            if (!TextUtils.isEmpty(verName)) {
                isEUI = true;
            }
            mCheckIsEUIFinished = true;
        }
        return isEUI;
    }

    public static boolean isTOS() {
        String verName = getSystemProperty("ro.qrom.build.brand");
        if (!TextUtils.isEmpty(verName)) {
            return verName.toLowerCase(Locale.ENGLISH).equals("tos");
        }
        return false;
    }

    private static Class<?> tryClassForName(String className) {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            return null;
        }
    }

    private static Method tryGetMethod(Class<?> cls, String name, Class<?>... parameterTypes) {
        try {
            return cls.getDeclaredMethod(name, parameterTypes);
        } catch (Exception e) {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    private static <T> T tryInvoke(Method m, Object object, Object... args) {
        try {
            return (T) m.invoke(object, args);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            return null;
        }
    }

//	private static boolean checkMIUIV6()
//	{
//		String propName = "ro.miui.ui.version.name";
//		String line = getSystemProperty(propName);
//		if (!TextUtils.isEmpty(line))
//		{
//			char version = line.charAt(line.length() - 1);
//			int ver = Integer.valueOf(String.valueOf(version));
//			LogUtils.d("TMYMIUI", "ver=" + ver);
//			if (ver >= 6)
//			{
//				return true;
//			}
//			else
//			{
//				return false;
//			}
//		}
//		else
//		{
//			return false;
//		}
//	}
//
//	private static boolean checkMIUIV8()
//	{
//		String propName = "ro.miui.ui.version.name";
//		String line = getSystemProperty(propName);
//		if (!TextUtils.isEmpty(line))
//		{
//			char version = line.charAt(line.length() - 1);
//			int ver = Integer.valueOf(String.valueOf(version));
//			LogUtils.d("TMYMIUI", "ver=" + ver);
//			if (ver >= 8)
//			{
//				return true;
//			}
//			else
//			{
//				return false;
//			}
//		}
//		else
//		{
//			return false;
//		}
//	}

    private static boolean checkMIUIVersion(int MIUIVersion) {
        String propName = "ro.miui.ui.version.name";
        String line = getSystemProperty(propName);
        if (!TextUtils.isEmpty(line)) {
            char version = line.charAt(line.length() - 1);
            int ver = Integer.valueOf(String.valueOf(version));
            LogUtils.d("TMYMIUI", "ver=" + ver);
            if (ver >= MIUIVersion) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    private static boolean checkMIUI() {
        boolean isMIUI = Build.ID.trim().toLowerCase().contains("miui");

        if (!isMIUI) {
            isMIUI = Build.MANUFACTURER.trim().toLowerCase().contains("xiaomi");
        }
        if (!isMIUI) {
            isMIUI = Build.HOST.trim().toLowerCase().contains("miui");
        }
        if (!isMIUI) {
            Context context = ContextHolder.getAppContext();
            if (context != null) {
                try {
                    PackageManager pm = context.getPackageManager();
                    PackageInfo info = pm.getPackageInfo("com.miui.backup", 0);
                    if (info != null) {
                        isMIUI = true;
                    }
                } catch (Exception e) {
                } finally {
                    mMIUISCheckFinished = true;
                }
            }
        } else {
            mMIUISCheckFinished = true;
        }
        return isMIUI;
    }


    public static boolean isSmartisanOS() {
        if (!mSmartisanOSCheckFinished) {
            isSmartisanOS = checkSmartisanOS();
        }
        return isSmartisanOS;
    }

    private static boolean checkSmartisanOS() {
        boolean isSmartisanOS = Build.HOST.trim().toLowerCase().contains("smartisan");

        if (!isSmartisanOS) {
            Context context = ContextHolder.getAppContext();
            if (context != null) {
                try {
                    PackageManager pm = context.getPackageManager();
                    PackageInfo info = pm.getPackageInfo("com.smartisanos.systemui", 0);
                    if (info != null) {
                        isSmartisanOS = true;
                    }
                    mSmartisanOSCheckFinished = true;
                } catch (Exception e) {
                }
            }
        } else {
            mSmartisanOSCheckFinished = true;
        }
        return isSmartisanOS;
    }

    public static boolean isActiveHardware() {
        if (DeviceUtils.getSdkVersion() >= 16) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * getProcessor:获取CPU的型号
     *
     * @return
     */
    public static String getCPUProcessor() {
        String result = "";
        BufferedReader br = null;
        try {
            Process process = Runtime.getRuntime().exec("/system/bin/cat /proc/cpuinfo");

            InputStream in = process.getInputStream();
            br = new BufferedReader(new InputStreamReader(in));
            String line = "";
            while ((line = br.readLine()) != null) {
                if (line.contains("Processor")) {
                    line = line.substring(line.indexOf(":") + 1);
                    result = StringUtils.removeHeadSpace(line);
                    break;
                }
            }
        } catch (IOException ex) {
            ex.printStackTrace();
        } catch (Throwable t) {
            t.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
        return result;
    }

    /**
     * Helper method for reading values from system files, using a minimised
     * buffer.
     *
     * @param textToMatch - Text in the system files to read for.
     * @param stream      - FileInputStream of the system file being read from.
     * @return A numerical value following textToMatch in specified the system
     * file.
     * -1 in the event of a failure.
     */
    private static int parseFileForValue(String textToMatch, FileInputStream stream) {
        byte[] buffer = new byte[1024];
        try {
            int length = stream.read(buffer);
            for (int i = 0; i < length; i++) {
                if (buffer[i] == '\n' || i == 0) {
                    if (buffer[i] == '\n')
                        i++;
                    for (int j = i; j < length; j++) {
                        int textIndex = j - i;
                        // Text doesn't match query at some point.
                        if (buffer[j] != textToMatch.charAt(textIndex)) {
                            break;
                        }
                        // Text matches query here.
                        if (textIndex == textToMatch.length() - 1) {
                            return extractValue(buffer, j);
                        }
                    }
                }
            }
        } catch (IOException e) {
            // Ignore any exceptions and fall through to return unknown value.
        } catch (NumberFormatException e) {
        }
        return DEVICEINFO_UNKNOWN;
    }

    /**
     * Helper method used by {@link #parseFileForValue(String, FileInputStream)
     * parseFileForValue}. Parses
     * the next available number after the match in the file being read and
     * returns it as an integer.
     *
     * @param index - The index in the buffer array to begin looking.
     * @return The next number on that line in the buffer, returned as an int.
     * Returns
     * DEVICEINFO_UNKNOWN = -1 in the event that no more numbers exist
     * on the same line.
     */
    private static int extractValue(byte[] buffer, int index) {
        while (index < buffer.length && buffer[index] != '\n') {
            if (buffer[index] >= '0' && buffer[index] <= '9') {
                int start = index;
                index++;
                while (index < buffer.length && buffer[index] >= '0' && buffer[index] <= '9') {
                    index++;
                }
                String str = new String(buffer, 0, start, index - start);
                return Integer.parseInt(str);
            }
            index++;
        }
        return DEVICEINFO_UNKNOWN;
    }

    private static boolean checkArmVersion(String version) {
        boolean result = false;
        BufferedReader br = null;
        if (new File("/proc/cpuinfo").exists()) {
            try {
                br = new BufferedReader(new FileReader(new File("/proc/cpuinfo")));
                String aLine;
                while ((aLine = br.readLine()) != null) {
                    aLine = aLine.toLowerCase();
                    if (-1 != aLine.indexOf(version)) {
                        result = true;
                        break;
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (br != null) {
                    try {
                        br.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

            }

        }

        return result;
    }

    /**
     * 是否ARMV7
     *
     * @return
     */
    public static boolean isArmV7() {
        return checkArmVersion("armv7");
    }

    public static boolean isArmV5() {
        if (mIsArmv5 < 0) {
            boolean ret = checkArmVersion("cpu architecture: 5");
            if (ret) {
                mIsArmv5 = 1;
            } else {
                mIsArmv5 = 0;
            }
        }

        return mIsArmv5 > 0;
    }

    public static boolean isMT6573() {
        if (mIsMT6573 < 0) {
            boolean ret = checkArmVersion(": mt6573");
            if (ret) {
                mIsMT6573 = 1;
            } else {
                mIsMT6573 = 0;
            }
        }

        return mIsMT6573 > 0;
    }

    /**
     * 判断当前是否为鸿蒙系统
     * @return
     */
    public static boolean isHarmonyOS() {
        try {
            Class.forName("com.huawei.system.BuildEx");
            // 当前系统为鸿蒙系统
            return true;
        } catch (ClassNotFoundException e) {
            // 当前系统不是鸿蒙系统
            return false;
        }
    }

    public static boolean isMT6577() {
        if (mIsMT6577 < 0) {
            boolean ret = checkArmVersion(": mt6577");
            if (ret) {
                mIsMT6577 = 1;
            } else {
                mIsMT6577 = 0;
            }
        }

        return mIsMT6577 > 0;
    }

    public static boolean isLargeScreen() {
        if (mLargeScrrenCheckFinished) {
            return mIsLargeScrren;
        }
        int shortSizeDp = (int) (Math.min(DeviceUtils.getWidth(), DeviceUtils.getHeight()) * DisplayMetrics.DENSITY_DEFAULT
                / DeviceUtils.getDensityDpi());
        mIsLargeScrren = shortSizeDp >= 360 && shortSizeDp <= 590;
        mLargeScrrenCheckFinished = true;
        return mIsLargeScrren;
    }

    /**
     * 判断是否是大屏手机，短边介于360dp和600dp之间的
     *
     * @param context
     * @return
     */
    public static boolean isPhablet(Context context) {

        int min = getMinEdge();
        float density = context.getResources().getDisplayMetrics().density;
        if (density != 0) {
            return min / density >= 360 && min / density <= 590;
        }
        return false;
    }

    public static int getMinEdge() {
        int width = getWidth();
        int height = getHeight();
        return Math.min(width, height);
    }

    public static int getMaxEdge() {
        int width = getWidth();
        int height = getHeight();
        return Math.max(width, height);
    }

    public static int getScreenDpi(Context ctx) {
        int densityDPI = SCREEN_DENSITY_MDPI;
        try {
            DisplayMetrics dm = new DisplayMetrics();
            WindowManager wm = (WindowManager) ctx.getSystemService(Context.WINDOW_SERVICE);
            wm.getDefaultDisplay().getMetrics(dm);
            if (dm.density == 1) {
                densityDPI = SCREEN_DENSITY_MDPI;
            } else if (dm.density <= 0.75) {
                densityDPI = SCREEN_DENSITY_LDPI;
            } else if (dm.density == 1.5) {
                densityDPI = SCREEN_DENSITY_HDPI;
            } else if (dm.density == 2.0) {
                densityDPI = SCREEN_DENSITY_XHDPI;
            } else if (dm.density > 2.0) {
                densityDPI = SCREEN_DENSITY_XXHDPI;
            }
        } catch (Throwable t) {
            t.printStackTrace();
        }
        return densityDPI;
    }

    /**
     * 获取RAM
     *
     * @return 单位MB
     */
    private static int RAMMemory = -1;

    public static int getTotalRAMMemory() {
        if (RAMMemory > 0)
            return RAMMemory;
        String str1 = "/proc/meminfo";
        String line = "";
        int idx = -1;
        BufferedReader localBufferedReader = null;
        try {
            FileReader fr = new FileReader(str1);
            localBufferedReader = new BufferedReader(fr, 8192);
            while ((line = localBufferedReader.readLine()) != null) {
                idx = line.indexOf("MemTotal:");
                if (-1 != idx) {
                    String size = line.substring(idx + "MemTotal:".length()).trim();
                    if (!TextUtils.isEmpty(size) && size.contains("k")) {
                        RAMMemory = Integer.parseInt(size.substring(0, size.indexOf("k")).trim()) / 1024;
                    }
                    break;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Throwable t) {
            t.printStackTrace();
        } finally {
            if (localBufferedReader != null) {
                try {
                    localBufferedReader.close();
                } catch (IOException ex) {
                    ex.printStackTrace();
                }
            }
        }
        return RAMMemory;
    }

    /**
     * 获取SDK版本号
     *
     * @return
     */
    public static int getSdkVersion() {
        if (-1 == sSdkVersion) {
            sSdkVersion = Integer.parseInt(android.os.Build.VERSION.SDK);
        }
        return sSdkVersion;
    }

    /**
     * 获取设备名称
     *
     * @return
     */
    public static String getDeviceName() {
        return " " + Build.MODEL.replaceAll("[ |\\/|\\_|\\&|\\|]", "") + " ";
    }

    /**
     * 取手机制式
     *
     * @return mTelephonyManager.getPhoneType() Returns a constant indicating
     * the device
     * phone type. This indicates the
     * type of radio used to transmit voice calls.
     * See Also
     * PHONE_TYPE_NONE
     * PHONE_TYPE_GSM
     * PHONE_TYPE_CDMA
     * PHONE_TYPE_SIP
     */
    public static String getPhoneType() {
        // 手机制式: GSM / WCDMA / CDMA
        // 目前TelephonyManager.phoneType只能识别gsm和cdma两种手机制式
        TelephonyManager tm = getTelephonyManager();
        String type = "GSM";
        if (tm != null) {
            int phoneType = tm.getPhoneType();
            if (phoneType == TelephonyManager.PHONE_TYPE_GSM) {
                type = "GSM";
            } else if (phoneType == PHONE_TYPE_CDMA) {
                type = "CDMA";
            }
        }
        return type;
    }

    /**
     * 当前使用的网络类型：
     *
     * @return "GPRS"
     * "EDGE"
     * "UMTS"
     * "HSDPA"
     * "HSUPA"
     * "HSPA"
     * "CDMA"
     * "CDMAEVDO0"
     * "CDMAEVDOA"
     * "CDMA1xRTT"
     * 例如： NETWORK_TYPE_UNKNOWN 网络类型未知 0
     * NETWORK_TYPE_GPRS GPRS网络 1
     * NETWORK_TYPE_EDGE EDGE网络 2
     * NETWORK_TYPE_UMTS UMTS网络 3
     * NETWORK_TYPE_HSDPA HSDPA网络 8
     * NETWORK_TYPE_HSUPA HSUPA网络 9
     * NETWORK_TYPE_HSPA HSPA网络 10
     * NETWORK_TYPE_CDMA CDMA网络,IS95A 或 IS95B. 4
     * NETWORK_TYPE_EVDO_0 EVDO网络, revision 0. 5
     * NETWORK_TYPE_EVDO_A EVDO网络, revision A. 6
     * NETWORK_TYPE_1xRTT 1xRTT网络 7
     */
    public static String getNetworkType() {
        String type = "GPRS";

        int netType = getNetworkTypeInt();

        if (netType == TelephonyManager.NETWORK_TYPE_GPRS) {
            type = "GPRS";
        } else if (netType == TelephonyManager.NETWORK_TYPE_EDGE) {
            type = "EDGE";
        } else if (netType == TelephonyManager.NETWORK_TYPE_UMTS) {
            type = "UMTS";
        } else if (netType == NETWORK_TYPE_HSDPA) {
            type = "HSDPA";
        } else if (netType == NETWORK_TYPE_HSUPA) {
            type = "HSUPA";
        } else if (netType == NETWORK_TYPE_HSPA) {
            type = "HSPA";
        } else if (netType == NETWORK_TYPE_CDMA) {
            type = "CDMA";
        } else if (netType == NETWORK_TYPE_EVDO_0) {
            type = "CDMAEVDO0";
        } else if (netType == NETWORK_TYPE_EVDO_A) {
            type = "CDMAEVDOA";
        } else if (netType == NETWORK_TYPE_1xRTT) {
            type = "CDMA1xRTT";
        }

        return type;
    }

    public static int getNetworkTypeInt() {
        TelephonyManager tm = getTelephonyManager();
        if (tm != null) {
            return tm.getNetworkType();
        }
        return TelephonyManager.NETWORK_TYPE_UNKNOWN;
    }


    /**
     * 获取是否root
     *
     * @return
     */
    public static boolean getIsRootByFile() {
        LogUtils.d(TAG, "getIsRootByFile");
        File f = null;
        final String kSuSearchPaths[] = {"/system/bin/", "/system/xbin/", "/system/sbin/", "/sbin/", "/vendor/bin/"};
        try {
            for (int i = 0; i < kSuSearchPaths.length; i++) {
                f = new File(kSuSearchPaths[i] + "su");
                if (f != null && f.exists()) {
                    LogUtils.d(TAG, kSuSearchPaths[i]);
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 获取是否root
     */
    public static boolean getIsRoot(Context context) {
        if (mRootStatus != -1) {
            return mRootStatus == 1;
        }

        boolean isRoot = getIsRootByFile();
        if (isRoot) {
            mRootStatus = 1;
        } else {
            mRootStatus = 0;
        }

        return isRoot;
    }

    /**
     * 获取可用内存
     *
     * @return
     */
    public static int getRAM() {
        return getTotalRAMMemory();
    }

    /**
     * 获取ROM总容量.
     */
    public static float getROMMemery() {
        StatFs statfs = new StatFs(Environment.getDataDirectory().getPath());
        long AvailableBlockCount = statfs.getAvailableBlocks();
        float avROMsize = statfs.getBlockSize() * AvailableBlockCount / 1024 / 1024;

        long TotalBlockCount = statfs.getBlockCount();
        float totalROMsize = statfs.getBlockSize() * TotalBlockCount / 1024 / 1024;

        LogUtils.v("pobyRom", "av: " + avROMsize + ", total: " + totalROMsize);

        return totalROMsize;
    }

    /****************************************
     * move from DeviceAdapter @kenlai
     ****************************************/

    /*
     * Copyright (C) 2005-2010 TENCENT Inc.All Rights Reserved.
     * FileName：DeviceAdapter.java
     * Description：
     * History：
     * 1.0 levijiang 2013-4-8 Create
     */

    private static boolean sIsHideSmartBarSuccess = false;

    /**
     * 适配机型，主要是对MX2， by levijiang 2013/04/08
     *
     * @param activity
     */
    public static void checkAdapter(Activity activity) {
        // 检查窗口属性
        checkFeature(activity);

        // 隐藏MX2的SmartBar
        checkSmartBarForMX2(activity);

        // 如果MX2隐藏SmartBar失败，则还是用以前全屏的方式适配
        // if (needAdapterForMX2())
        // {
        // FullScreenManager.setFullScreenFlag(activity.getWindow());
        // }
    }

    public static boolean checkFeature(Activity activity) {
        try {
            if (!hasSmartBar()) {
                // try
                // {
                // // 如果不是MX2，就直接隐藏TitleBar
                // activity.requestWindowFeature(Window.FEATURE_NO_TITLE);
                // }
                // catch (Exception e)
                // {
                // e.printStackTrace();
                // }
                return false;
            }
//			else
//			{
//				if (DeviceUtils.getSdkVersion() < 14)
//				{
//                    activity.requestWindowFeature(Window.FEATURE_ACTION_BAR);
//                    return true;
//				}
//			}
        } catch (Exception e) {
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 如果是MX2，隐藏SmartBar
     *
     * @param activity
     */
    private static void checkSmartBarForMX2(Activity activity) {
        if (hasSmartBar()) {
//			int sdkVersion = DeviceUtils.getSdkVersion();
//			if (sdkVersion >= 14)
//			{
            try {
                activity.requestWindowFeature(Window.FEATURE_NO_TITLE);
            } catch (Exception e) {
                e.printStackTrace();
            }
            ApiCompat.hideNavigation(activity);
            sIsHideSmartBarSuccess = true;
//			}
//			else if(sdkVersion >=  11)
//			{
//                ActionBar localActionBar = activity.getActionBar();
//
//                if (localActionBar == null)
//                {
//                     sIsHideSmartBarSuccess = false;
//                }
//
//                try
//                {
//                    ReflectionUtils.invokeInstance(localActionBar, "setTabsShowAtBottom", new Class[] { Boolean.class }, true);
//                }
//                catch (Exception e)
//                {
//                    e.printStackTrace();
//                    sIsHideSmartBarSuccess = false;
//                }
//
//                try
//                {
//                    ReflectionUtils.invokeInstance(localActionBar, "setActionBarViewCollapsable", new Class[] { Boolean.class }, true);
//
//                    localActionBar.hide();
//                }
//                catch (Exception e)
//                {
//                    e.printStackTrace();
//                    sIsHideSmartBarSuccess = false;
//                }
//			}

            if (!sIsHideSmartBarSuccess) {
                try {
                    activity.requestWindowFeature(Window.FEATURE_NO_TITLE);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static boolean IsHideSmartBarSuccess() {
        return sIsHideSmartBarSuccess;
    }

    public static boolean needAdapterForMX() {
        return hasSmartBar() && !IsHideSmartBarSuccess();
    }

    /**
     * you'd better use GdiMeasureImpl.getWidth()/and getHeight() in basemodule.
     */
    @Deprecated
    public static int getScreenWidth() {
        if (mScreenWidth <= 0) {
            getScreenSize();
        }
        return mScreenWidth;
    }

    @Deprecated
    public static int getScreenHeigh() {
        if (mScreenHeight <= 0) {
            getScreenSize();
        }
        return mScreenHeight;
    }

    @Deprecated
    private static void getScreenSize() {
        Context context = ContextHolder.getAppContext();
        android.view.WindowManager manager = (android.view.WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        mScreenWidth = manager.getDefaultDisplay().getWidth();
        mScreenHeight = manager.getDefaultDisplay().getHeight();
    }


    /**
     * 是否为QVGA屏幕
     * Robinsli move from GdiMeasureImpl
     */
    public static boolean isQVGA() {
        if ((getScreenWidth() == 240 && getScreenHeigh() == 320) || (getScreenWidth() == 320 && getScreenHeigh() == 240)) {
            return true;
        }
        return false;
    }

    public static String getAndroidId(Context ctx) {
        if (ctx != null) {
            if (TextUtils.isEmpty(mAndroidId)) {
                try {
                    mAndroidId = Secure.getString(ctx.getContentResolver(), Secure.ANDROID_ID);
                } catch (Throwable e) {
                    e.printStackTrace();
                }

            }
            return mAndroidId;
        } else {
            return "";
        }
    }

    public static TelephonyManager getTelephonyManager() {
        Context context = ContextHolder.getAppContext();
        if (context != null)
            return (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        return null;
    }

    private static boolean sHasSmartBar = false;
    private static boolean isSmartBarStateInit = false;

    public static boolean hasSmartBar() {
        if (isSmartBarStateInit) {
            return sHasSmartBar;
        }
        try {
            // 新型号可用反射调用Build.hasSmartBar()
            Method method = Class.forName("android.os.Build").getMethod("hasSmartBar");
            sHasSmartBar = ((Boolean) method.invoke(null)).booleanValue();
        } catch (Exception e) {
            // 反射不到Build.hasSmartBar()，则用Build.DEVICE判断
            if (Build.DEVICE.equalsIgnoreCase("mx2")) {
                sHasSmartBar = true;
            } else if (Build.DEVICE.equalsIgnoreCase("mx") || Build.DEVICE.equalsIgnoreCase("m9")) {
                sHasSmartBar = false;
            }
        }
        isSmartBarStateInit = true;
        return sHasSmartBar;
    }

    /**
     * 单个app能使用的最大内存
     */
    private static int mMaxAppMemory = -1;

    /**
     * 获得默认设备单个app能使用的最大内存数
     */
    public static int getAppMaxMemory() {
        if (mMaxAppMemory == -1) {
            ActivityManager am = (ActivityManager) ContextHolder.getAppContext().getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
            mMaxAppMemory = am.getMemoryClass();
        }
        return mMaxAppMemory;
    }

    /**
     * 获取当前设备已用内存的比率
     * （返回0表示获取有异常）
     */
    public static float getUsedMemoryUsage(Context context) {
        long total = getTotalRAMMemory();
        long free = getDeviceAvailableMemory(context);
        if (total != 0 && free != 0) {
            return (float) (total - free) / total;
        }
        return 0;
    }

    /**
     * 获取当前设备的可用内存
     */
    public static long getDeviceAvailableMemory(Context context) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (am != null) {
            ActivityManager.MemoryInfo info = new ActivityManager.MemoryInfo();
            am.getMemoryInfo(info);
            return info.availMem / 1024 / 1024;
        } else {
            return 0;
        }
    }

    private final static long GET_IMSI_TIME_SPAN = 1000 * 60 * 10;
    private static String mIMSI;
    private static long mLastGetIMSITime = 0;


    public static String getNewBeeROMName() {
        return getAndroidOsSystemProperties("ro.build.version.newbee.display");
    }

    public static String getAndroidOsSystemProperties(String key) {
        String ret = "";
        try {
            Method systemProperties_get = Class.forName("android.os.SystemProperties").getMethod("get", String.class);
            if ((ret = (String) systemProperties_get.invoke(null, key)) != null)
                return ret;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }

        return ret;
    }

    // 反射调用悬浮窗开关状态
    public static boolean checkOp(Context context, int op) {
        try {
            // AppOpsManager manager = (AppOpsManager)
            // context.getSystemService(Context.APP_OPS_SERVICE);
            Class contextClass = Class.forName("android.content.Context");
            Field contextField = contextClass.getDeclaredField("APP_OPS_SERVICE");
            contextField.setAccessible(true);
            Object Value1 = (Object) contextField.get(contextClass);
            if (Value1 instanceof String) {
                Object[] arrayOfContextClass = new Object[1];
                arrayOfContextClass[0] = Value1;
                Class appOpsParam[] = getParamTypes(contextClass, "getSystemService");
                Method getSystemServiceMethod1 = contextClass.getMethod("getSystemService", appOpsParam);
                Object appOps = getSystemServiceMethod1.invoke(context, arrayOfContextClass);

                // AppOpsManager.MODE_ALLOWED
                Class AppOpsManagerClass = Class.forName("android.app.AppOpsManager");
                Field modelAllowedField = AppOpsManagerClass.getDeclaredField("MODE_ALLOWED");
                modelAllowedField.setAccessible(true);
                int MODE_ALLOWED = modelAllowedField.getInt(AppOpsManagerClass);

                // public int checkOp(int op, int uid, String packageName)
                Field opField = AppOpsManagerClass.getDeclaredField("OP_SYSTEM_ALERT_WINDOW");
                opField.setAccessible(true);
                Object Value2 = (Integer) opField.get(AppOpsManagerClass);
                Object[] arrayOfcheckOp = new Object[3];
                arrayOfcheckOp[0] = Value2;
                arrayOfcheckOp[1] = Binder.getCallingUid();
                arrayOfcheckOp[2] = context.getPackageName();
                Class paramTypes[] = getParamTypes(AppOpsManagerClass, "checkOp");
                paramTypes[0] = Integer.TYPE;
                Method meth = AppOpsManagerClass.getMethod("checkOp", paramTypes);
                Object checkOp = meth.invoke(appOps, arrayOfcheckOp);
                // Object checkOp = Load("android.app.AppOpsManager", "checkOp",
                // arrayOfcheckOp);
                if (checkOp instanceof Integer && (Integer) checkOp == MODE_ALLOWED) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean isAlertWindowOpen(Context context) {
        final int versionCode = Build.VERSION.SDK_INT;
        String systemProperty = getMIUISystemProperty();
        if ("V5".equals(systemProperty) || "V6".equals(systemProperty)) {
            if (versionCode >= 19) {
                if (checkOp(context, 24)) {
                    // 悬浮窗开启
                    return true;
                } else {
                    // 悬浮窗关闭
                    return false;
                }
            } else {
                // LogUtils.d("taoyong", "flag:" + (0x8000000 &
                // context.getApplicationInfo().flags));
                if ((context.getApplicationInfo().flags & (1 << 27)) == 0) {
                    // 悬浮窗关闭
                    return false;
                } else {
                    // 悬浮窗开启
                    return true;
                }
            }
        } else if (versionCode > 17) {
            if (checkOp(context, 24)) {
                // 悬浮窗开启
                return true;
            } else {
                // 悬浮窗关闭
                return false;
            }
        } else {
            // 又不是miui，也不是4.3及以上应该没有这个管理项了
            return true;
        }
    }

    // 获取反射方法参数
    public static Class[] getParamTypes(Class cls, String mName) {
        Class[] cs = null;
        Method[] mtd = cls.getDeclaredMethods();
        for (int i = 0; i < mtd.length; i++) {
            if (!mtd[i].getName().equals(mName)) {
                continue;
            }

            cs = mtd[i].getParameterTypes();
        }
        return cs;
    }

    private static int statusBarHeight = -1;

    public static int getStatusBarHeightFromSystem() {
        if (statusBarHeight > 0) {
            return statusBarHeight;
        }

        Class<?> c = null;
        Object obj = null;
        Field field = null;
        int x = 0;
        try {
            c = Class.forName("com.android.internal.R$dimen");
            obj = c.newInstance();
            field = c.getField("status_bar_height");
            x = Integer.parseInt(field.get(obj).toString());
            statusBarHeight = ContextHolder.getAppContext().getResources().getDimensionPixelSize(x);
        } catch (Exception e1) {
            statusBarHeight = -1;
            e1.printStackTrace();
        }

        if (statusBarHeight < 1) {
            int statebarH_id = ContextHolder.getAppContext().getResources().getIdentifier("statebar_height", "dimen",
                    ContextHolder.getAppContext().getPackageName());
            statusBarHeight = Math.round(ContextHolder.getAppContext().getResources().getDimension(statebarH_id));
        }
        return statusBarHeight;
    }

    private static int sSystenNaviBarHeight = -1;

    public static int getSystemNaviBarHeight() {
        if (sSystenNaviBarHeight != -1) {
            return sSystenNaviBarHeight;
        }
        Point point = getNavigationBarSize(ContextHolder.getAppContext());
        sSystenNaviBarHeight = point.y;
        LogUtils.d(TAG, "getSystemnativebarHeight : " + sSystenNaviBarHeight);
        return sSystenNaviBarHeight;
    }

    public static Point getNavigationBarSize(Context context) {
        Point appUsableSize = getAppUsableScreenSize(context);
        Point realScreenSize = getRealScreenSize(context);

        // navigation bar on the right
        if (appUsableSize.x < realScreenSize.x) {
            return new Point(appUsableSize.y, realScreenSize.x - appUsableSize.x);
        }

        // navigation bar at the bottom
        if (appUsableSize.y < realScreenSize.y) {
            return new Point(appUsableSize.x, realScreenSize.y - appUsableSize.y);
        }

        // navigation bar is not present
        return new Point();
    }

    public static Point getAppUsableScreenSize(Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display display = windowManager.getDefaultDisplay();
        Point size = new Point();
        display.getSize(size);
        return size;
    }

    public static Point getRealScreenSize(Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display display = windowManager.getDefaultDisplay();
        Point size = new Point();

        if (Build.VERSION.SDK_INT >= 17) {
            display.getRealSize(size);
        } else if (Build.VERSION.SDK_INT >= 14) {
            try {
                size.x = (Integer) Display.class.getMethod("getRawWidth").invoke(display);
                size.y = (Integer) Display.class.getMethod("getRawHeight").invoke(display);
            } catch (IllegalAccessException e) {
            } catch (InvocationTargetException e) {
            } catch (NoSuchMethodException e) {
            }
        }

        return size;
    }

    public static int dpToPx(Context context, int dip) {
        DisplayMetrics dm = context.getResources().getDisplayMetrics();
        // int result = (int)
        // TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dip, dm);
        // LogUtils.d("TMYDEVICE", "displayMetrix=" + dm.toString());
        // LogUtils.d("TMYDEVICE", "convert,dip=" + dip + ",px=" + result);
        return (int) (dm.density * dip + 0.5f);
    }

    // maybe useless, remove?
    public static int getWidthHC() {
        Context appContext = ContextHolder.getAppContext();
        if (appContext != null) {
            return dpToPx(appContext, appContext.getResources().getConfiguration().screenWidthDp);
        }
        return 0;
    }

    public static int getScreenMinWidth() {
        int width = -1;
        android.view.WindowManager manager =
                (android.view.WindowManager) ContextHolder.getAppContext().getSystemService(Context.WINDOW_SERVICE);
        if (manager != null) {
            try {
                width = Math.min(manager.getDefaultDisplay().getWidth(), manager.getDefaultDisplay().getHeight());
            } catch (Exception e) {
            }
        }
        return width;
    }

    public static int getScreenMinWidth(android.view.WindowManager manager) {
        int width = -1;
        if (manager != null) {
            try {
                DisplayMetrics dm = new DisplayMetrics();
                manager.getDefaultDisplay().getMetrics(dm);
                width = Math.min(dm.heightPixels, dm.widthPixels);
            } catch (Exception e) {
            }
        }
        return width;
    }

    public static android.view.WindowManager mWm;

    // maybe useless, remove?
    @Deprecated
    public static int getHeightPreHC() {
        try {
            if (mWm == null) {
                mWm = (android.view.WindowManager) ContextHolder.getAppContext().getSystemService(Context.WINDOW_SERVICE);
            }
            return mWm.getDefaultDisplay().getHeight();
        } catch (Exception e) {
            return 0;
        }
    }

    // maybe useless, remove?
    public static int getHeightHC() {
        Context appContext = ContextHolder.getAppContext();
        if (appContext != null) {
            int statusBarHeight = getStatusBarHeightFromSystem();
            LogUtils.d("TMYDEVICE", "getStatusBarHeight()=" + statusBarHeight);
            return dpToPx(appContext, appContext.getResources().getConfiguration().screenHeightDp) + statusBarHeight;
        }
        return 0;
    }

    public static int getHeight() {
        if (mIsInSystemMultiWindow) {
            if (mFrameHeightDp <= 0)
                mFrameHeightDp = ContextHolder.getAppContext().getResources().getConfiguration().screenHeightDp;
            return (int) (mFrameHeightDp * DeviceUtils.getDensity());
        }
//		if (false && getSdkVersion() >= Build.VERSION_CODES.HONEYCOMB_MR2)
//		{
//			return getHeightHC();
//		}
//		else
//		{
        //return getHeightPreHC(); // 存在跨进程耗时问题
        return ContextHolder.getAppContext().getResources().getDisplayMetrics().heightPixels;
//		}
    }

    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
    public static int getDeviceHeight() {
        try {
            if (mWm == null) {
                mWm = (android.view.WindowManager) ContextHolder.getAppContext().getSystemService(Context.WINDOW_SERVICE);
            }
            int heightWithoutVirtualMenuBar = mWm.getDefaultDisplay().getHeight();
            if (getSdkVersion() >= 17) {
                DisplayMetrics dm = new DisplayMetrics();
                mWm.getDefaultDisplay().getRealMetrics(dm);
                int heightWithVirtualMenuBar = dm.heightPixels;
                if (heightWithVirtualMenuBar > heightWithoutVirtualMenuBar) {// 说明有底部的虚拟menubar
                    return heightWithVirtualMenuBar;
                }
            }
            return heightWithoutVirtualMenuBar;
        } catch (Exception e) {
            return 0;
        }
    }

    public static int getWidth() {
        if (mIsInSystemMultiWindow) {
            if (mFrameWidthDp <= 0)
                mFrameWidthDp = ContextHolder.getAppContext().getResources().getConfiguration().screenWidthDp;
            return (int) (mFrameWidthDp * DeviceUtils.getDensity());
        }
//		if (false && getSdkVersion() >= Build.VERSION_CODES.HONEYCOMB_MR2 && !isFlymeV4())
//		{
//			return getWidthHC();
//		}
//		else
//		{
        //return getWidthPreHC();	// getWidthPreHC存在跨进程耗时
        return ContextHolder.getAppContext().getResources().getDisplayMetrics().widthPixels;
//		}
    }

    // maybe useless, remove?

    /**
     * you'd better use GdiMeasureImpl.getWidth()/and getHeight() in basemodule.
     */
    @Deprecated
    public static int getWidthPreHC() {
        // 【95371798】【1次】【6.0.0.1510】【RDM异常上报】【java.lang.NullPointerException】
        // ID： 50672910
        try {
            if (mWm == null) {
                mWm = (android.view.WindowManager) ContextHolder.getAppContext().getSystemService(Context.WINDOW_SERVICE);
            }
            return mWm.getDefaultDisplay().getWidth();
        } catch (Exception e) {
            return 0;
        }
    }

    private static int sDensityDpi = -1;

    public static int getDensityDpi() {
        if (sDensityDpi == -1) {
            try {
                android.view.WindowManager manager = (android.view.WindowManager) ContextHolder.getAppContext().getSystemService(Context.WINDOW_SERVICE);
                DisplayMetrics dm = new DisplayMetrics();
                manager.getDefaultDisplay().getMetrics(dm);
                sDensityDpi = dm.densityDpi;
            } catch (Throwable t) {

            }
        }

        return sDensityDpi;
    }

    private static float sDensity = -1;

    public static float getDensity() {
        if (sDensity < 0) {
            android.view.WindowManager manager = (android.view.WindowManager) ContextHolder.getAppContext().getSystemService(Context.WINDOW_SERVICE);
            DisplayMetrics dm = new DisplayMetrics();
            manager.getDefaultDisplay().getMetrics(dm);
            sDensity = dm.density;
        }

        return sDensity;
    }

    public static boolean isLandscape() {
        if (mIsInSystemMultiWindow) {
            return false;
        }
        Context appContext = ContextHolder.getAppContext();
        Configuration configuration = appContext.getResources().getConfiguration();
        return configuration.orientation == Configuration.ORIENTATION_LANDSCAPE;
    }


    public static int getCanvasWidth() {
        // 两者值上相等，getWidth初始化时可得到
        return getWidth();
    }

    // 状态栏高度
    public static int STATUSBAR_HEIGHT = 0;

    public static boolean sHaveCheckStatusBarHeight = false;

    public static void initStatusBarHeight(int height) {
        STATUSBAR_HEIGHT = height;
    }

    public static void checkStatusBarHeight(Window window) {
        if (sHaveCheckStatusBarHeight)
            return;

        Rect frame = new Rect();
        window.getDecorView().getWindowVisibleDisplayFrame(frame);
        int height = frame.top;
        if (height > 0) {
            STATUSBAR_HEIGHT = height;
            sHaveCheckStatusBarHeight = true;
        }
    }

    /**
     * 获得手机品牌
     *
     * @return
     */
    public static String getDeviceManufacturer() {
        String manufacturer = android.os.Build.MANUFACTURER;
        if (manufacturer == null) {
            manufacturer = "";
        } else {
            manufacturer = manufacturer.toLowerCase();
        }
        return manufacturer;
    }

    /**
     * 获得机型
     */
    public static String getDeviceModel() {
        return Build.MODEL.replaceAll("[ |\\/|\\_|\\&|\\|]", "");
    }

    public static String getDeviceBrand() {
        String brand = android.os.Build.BRAND;
        if (brand == null) {
            brand = "";
        } else {
            brand = brand.toLowerCase();
        }
        return brand;
    }

    /**
     * 判断是否华为emui
     *
     * @return
     */
    public static boolean isEmuiHeighSystem() {
        Intent localIntent = new Intent();
        localIntent.setClassName("com.android.settings", "com.android.settings.Settings$PreferredSettingsActivity");
        return ContextHolder.getAppContext().getPackageManager().queryIntentActivities(localIntent, PackageManager.MATCH_DEFAULT_ONLY).size() > 0;
    }

    public static boolean isSupportImmersiveMode() {
        boolean isSuport = false;
        Map<String, String> rp = DeviceUtils.getIsSupportImmersiveModeData();
        if (rp != null) {
            isSuport = Boolean.valueOf(rp.get("support"));
        }
        return isSuport;
    }

    public static Map<String, String> getIsSupportImmersiveModeData() {
        if (getSdkVersion() >= 19) {
            boolean menucheck = !ViewConfiguration.get(ContextHolder.getAppContext()).hasPermanentMenuKey();
            boolean backcheck = !KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_BACK);
            boolean homecheck = !KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_HOME);
            LogUtils.d(TAG,
                    "getIsSupportImmersiveMode() menu=" + menucheck + " back=" + backcheck + " home=" + homecheck + " api=" + getSdkVersion());
            boolean support = menucheck && backcheck && homecheck;

            if (!support) {
                support = menucheck && (isOppoR7s || isOppoR7Plus || isHUAWEI_MT7);
            }

            String dev = getDeviceModel();
            HashMap<String, String> rp = new HashMap<String, String>();
            rp.put("device", getDeviceBrand() + "_" + dev);
            rp.put("build_model", Build.MODEL);
            rp.put("build_brand", Build.BRAND);
            rp.put("check_menu", "" + menucheck);
            rp.put("check_back", "" + backcheck);
            rp.put("check_home", "" + homecheck);
            rp.put("api", "" + getSdkVersion());
            rp.put("support", "" + support);
            LogUtils.d(TAG, "getIsSupportImmersiveMode() report=" + rp);
            return rp;
        }

        // LogUtils.d(TAG, "getIsSupportImmersiveMode=" + hasNavigationBar +
        // " sdk=" + DeviceUtils.getSdkVersion());
        return null;
    }

    public static String getSysVersion() {
        try {
            return Build.DISPLAY.replaceAll("[&=]", ".");
        } catch (Exception e) {
            // TODO: handle exception
        }
        return Build.DISPLAY;
    }

    /**
     * 在业务场景使用的QBWebView是否需要开启软绘
     * 解决异常上报nDrawDisplayList问题
     *
     * @return
     */
    public static boolean enableQBWebViewLayerTypeSoftware() {
        int sdkVersion = getSdkVersion();
        return sdkVersion == 15 || sdkVersion == 16 || sdkVersion == 18 || sdkVersion == 19;
    }

    public static boolean isScreenOn() {
        try {
            PowerManager powerManager = (PowerManager) ContextHolder.getAppContext().getSystemService(Context.POWER_SERVICE);
            return powerManager.isScreenOn();
        } catch (Exception e) {
            e.printStackTrace();
            return true;
        }
    }

    static boolean supportInlineDarkIcon = false;
    static boolean checkFinished = false;

    public static boolean checkSupportInlineDarkIcon() {
        if (checkFinished) {
            return supportInlineDarkIcon;
        }
        Field field = null;
        try {
            field = Activity.class.getDeclaredField("mDisableStatusBarIconTheme");
        } catch (NoSuchFieldException e) {
            supportInlineDarkIcon = false;
            checkFinished = true;
            return false;
        }
        supportInlineDarkIcon = field != null;
        checkFinished = true;
        return supportInlineDarkIcon;

    }

    public static boolean isScreenLocked() {
        try {
            KeyguardManager keyguardManager = (KeyguardManager) ContextHolder.getAppContext().getSystemService(Context.KEYGUARD_SERVICE);
            return keyguardManager.inKeyguardRestrictedInputMode();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    public static void setFrameWidthDp(int dp) {
        mFrameWidthDp = dp;
    }

    public static void setFrameHeightDp(int dp) {
        mFrameHeightDp = dp;
    }

    public static void setBrowserActiveState(int browserActiveState) {
        // 设置MO声是否在前台0/后台1
        mBrowserActiveState = browserActiveState;
        LogUtils.d("leo", "browserActiveState " + mBrowserActiveState);
    }

    public static boolean isStatusBarHide(Window window) {
        if (window == null) {
            return false;
        }
        return (window.getAttributes().flags & WindowManager.LayoutParams.FLAG_FULLSCREEN) == WindowManager.LayoutParams.FLAG_FULLSCREEN;
    }

    public static boolean isAboveKitkat() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT;
    }


    public static String getProp(String name) {
        String line = null;
        BufferedReader input = null;
        try {
            Process p = Runtime.getRuntime().exec("getprop " + name);
            input = new BufferedReader(new InputStreamReader(p.getInputStream()), 1024);
            line = input.readLine();
            input.close();
        } catch (IOException ex) {
            Log.e(TAG, "Unable to read prop " + name, ex);
            return null;
        } finally {
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return line;
    }
}
