/**
 * 
 */
package com.abc.common.utils;
import android.util.Base64;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;

/*
 * Copyright (C) 2005-2010 TENCENT Inc.All Rights Reserved.
 * FileName：StringUtil.java
 * Description：
 * History：
 * 1.0 kenlai 2013-09-11 Create
 */

public class ByteUtils
{

	// 使用书签同步后台提供的生成算法
	public static int getHashUUID(String url)
	{
		return hashRawString(url);
	}

	public static int hashRawString(String key)
	{
		byte[] bytes = null;
		try
		{
			bytes = key.getBytes("UTF-8");
		}
		catch (UnsupportedEncodingException e)
		{
			bytes = key.getBytes();
		}

		int length = bytes.length;
		int value = 0;
		for (int i = 0; i < length; i++)
		{
			value += bytes[i];
			value += (value << 10);
			value ^= (value >>> 6);
		}

		value += (value << 3);
		value ^= (value >>> 11);
		value += (value << 15);
		return value == 0 ? 1 : Math.abs(value);
	}

	// 这段内容用于猜测字符串的编码格式
	public final static byte	CHARACTER_ENCODING_ASCII	= 0;
	public final static byte	CHARACTER_ENCODING_UTF8		= 1;
	public final static byte	CHARACTER_ENCODING_GB18030	= 2;

	public static byte guessCharacterEncoding(byte[] data)
	{
		byte asciiCount = 0;
		int i = 0;

		for (i = 0; i < data.length;)
		{
			byte c = data[i];

			if ((c & 0x80) == 0)
			{
				asciiCount++;

				i++;
			}
			else if ((c & 0xE0) == 0xC0)
			{
				if (!checkUtf8Char(data, i + 1, 1))
					return CHARACTER_ENCODING_GB18030;

				i += 2;
			}
			else if ((c & 0xF0) == 0xE0)
			{
				if (!checkUtf8Char(data, i + 1, 2))
					return CHARACTER_ENCODING_GB18030;

				i += 3;
			}
			else if ((c & 0xF8) == 0xF0)
			{
				if (!checkUtf8Char(data, i + 1, 3))
					return CHARACTER_ENCODING_GB18030;

				i += 4;
			}
			else if ((c & 0xFC) == 0xF8)
			{
				if (!checkUtf8Char(data, i + 1, 4))
					return CHARACTER_ENCODING_GB18030;

				i += 5;
			}
			else if ((c & 0xC0) == 0x80)
			{
				// 证明这个是UTF-8的第二个字节或者第三个字节
				i++;
			}
			else
			{
				return CHARACTER_ENCODING_GB18030;
			}
		}

		if (asciiCount == data.length)
			return CHARACTER_ENCODING_ASCII;
		else
			return CHARACTER_ENCODING_UTF8;
	}

	private static boolean checkUtf8Char(byte[] data, int offset, int len)
	{
		for (int i = 0; i < len && offset + i < data.length; i++)
		{
			if ((data[offset + i] & 0xC0) != 0x80)
				return false;
		}

		return true;
	}

	// moved from DataTools @kenlai

	public static void Word2Byte(byte[] to, int toIndex, short from)
	{
		to[toIndex] = (byte) (from >> 8);
		to[toIndex + 1] = (byte) (from);
	}

	public static void DWord2Byte(byte[] to, int toIndex, long from)
	{
		to[toIndex] = (byte) (from >> 24);
		to[toIndex + 1] = (byte) (from >> 16);
		to[toIndex + 2] = (byte) (from >> 8);
		to[toIndex + 3] = (byte) (from);
	}

	public static String toHexStr(byte[] key)
	{
		StringBuffer b = new StringBuffer();
		for (int i = 0; i < key.length; i++)
		{
			b.append(toHexStr(key[i]));
		}
		return b.toString();
	}

	public static String toHexStr(byte bValue)
	{
		int i, j, iValue;
		char c1, c2;
		String s = "";

		if (bValue < 0)
			iValue = (int) (256 + bValue);
		else
			iValue = (int) (bValue);
		i = iValue / 16;
		j = iValue - i * 16;

		if (i > 9)
			c1 = (char) (i - 10 + 'A');
		else
			c1 = (char) (i + '0');

		if (j > 9)
			c2 = (char) (j - 10 + 'A');
		else
			c2 = (char) (j + '0');
		Character c = new Character(c1);
		s = s + c.toString();
		c = new Character(c2);
		s = s + c.toString();

		return s;
	}

	/**
	 * 将字节型数据转化为16进制字符串
	 */
	public static String byteToHexString(byte[] bytes)
	{
		if (bytes == null || bytes.length <= 0)
			return null;

		StringBuffer buf = new StringBuffer(bytes.length * 2);

		for (int i = 0; i < bytes.length; i++)
		{
			if (((int) bytes[i] & 0xff) < 0x10)
			{
				buf.append("0");
			}
			buf.append(Long.toString((int) bytes[i] & 0xff, 16));
		}
		return buf.toString();
	}

	/**
	 * 将16进制字符串转化为字节型数据
	 */
	public static byte[] hexStringToByte(String hexString)
	{
		if (hexString == null || hexString.equals("") || hexString.length() % 2 != 0)
		{
			return null;
		}
		byte[] bData = new byte[hexString.length() / 2];
		try
		{
			for (int i = 0; i < hexString.length(); i += 2)
			{
				bData[i / 2] = (byte) (Integer.parseInt(hexString.substring(i, i + 2), 16) & 0xff);
			}
		}
		catch (NumberFormatException e)
		{
			bData = null;
		}
		return bData;
	}

	/**
	 * 将list 数组的byte merge在一起
	 * 
	 * @param list
	 * @return
	 */

	public static byte[] mergeListByteData(ArrayList<byte[]> list)
	{
		if (list == null)
		{
			return null;
		}
		byte[] data = null;
		for (int i = 0, j = list.size(); i < j; i += 2)
		{
			byte[] tempData = null;

			if (i + 1 == j)
			{
				tempData = list.get(i);
			}
			else
			{
				byte[] src = list.get(i);
				byte[] obj = list.get(i + 1);
				tempData = mergeByteData(src, obj);
			}

			data = mergeByteData(tempData, data);
		}
		return data;
	}

	/**
	 * 将给定的两个数组合并，src+obj形式
	 * 
	 * @param src
	 * @param obj
	 * @return
	 */
	public static byte[] mergeByteData(byte[] src, byte[] obj)
	{
		if (src == null || src.length < 0)
			return obj;

		if (obj == null || obj.length < 0)
			return src;

		byte[] data = new byte[src.length + obj.length];
		System.arraycopy(src, 0, data, 0, src.length);
		System.arraycopy(obj, 0, data, src.length, obj.length);
		return data;
	}

	public static boolean isEqual(byte[] src, byte[] dst)
	{
		if (src == null && dst == null)
		{
			return true;
		}

		if (src == null && dst != null)
		{
			return false;
		}

		if (src != null && dst == null)
		{
			return false;
		}

		if (src.length != dst.length)
			return false;

		for (int i = 0; i < src.length; i++)
		{
			byte srcI = src[i];
			byte dstI = dst[i];

			if (srcI != dstI)
			{
				return false;
			}
		}

		return true;
	}

	public static byte[] subByte(byte[] src, int begin, int count)
	{
		int length = src.length;
		if (begin < 0 || begin + count > length)
		{
			return null;
		}

		if (count < 0)
		{
			count = src.length - begin;
		}

		try
		{
			byte[] res = new byte[count];

			for (int i = 0; i < count; i++)
			{
				res[i] = src[i + begin];
			}

			return res;
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}

		return null;
	}

	public static boolean EqualBytes(byte[] bytes1, byte[] bytes2)
	{
		if (bytes1 == null || bytes2 == null)
		{
			return false;
		}

		if (bytes1.length != bytes2.length)
		{
			return false;
		}

		for (int i = 0; i < bytes2.length; i++)
		{
			if (bytes1[i] != bytes2[i])
			{
				return false;
			}
		}

		return true;
	}

	public static byte[] comByte(byte[] bytes1, byte[] bytes2)
	{
		if (bytes1 == null || bytes1.length == 0)
		{
			return bytes2;
		}

		if (bytes2 == null || bytes2.length == 0)
		{
			return bytes1;
		}

		byte[] res = new byte[bytes1.length + bytes2.length];

		for (int i = 0; i < bytes1.length; i++)
		{
			res[i] = bytes1[i];
		}

		for (int i = 0; i < bytes2.length; i++)
		{
			res[i + bytes1.length] = bytes2[i];
		}

		return res;
	}

	public static boolean isAllZeroBytes(byte[] array)
	{
		boolean isInValid = true;

		if (array == null || array.length <= 0)
			return isInValid;

		// 出现非0值表示有效
		for (int i = 0, j = array.length; i < j; i++)
		{
			if (array[i] != 0x0)
			{
				isInValid = false;
				break;
			}
		}

		return isInValid;
	}

	/**
	 * Base64编码
	 * @param data 要编码的字节数组
	 * @return Base64编码后的字符串
	 */
	public static String encodeBase64(byte[] data) {
		if (data == null) {
			return null;
		}
		return Base64.encodeToString(data, Base64.NO_WRAP);
	}

	/**
	 * Base64解码
	 * @param base64String Base64编码的字符串
	 * @return 解码后的字节数组
	 */
	public static byte[] decodeBase64(String base64String) {
		if (base64String == null || base64String.isEmpty()) {
			return null;
		}
		try {
			return Base64.decode(base64String, Base64.NO_WRAP);
		} catch (IllegalArgumentException e) {
			e.printStackTrace();
			return null;
		}
	}

}
