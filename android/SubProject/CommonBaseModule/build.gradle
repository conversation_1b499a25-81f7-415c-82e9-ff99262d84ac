apply plugin: 'com.android.library'

android {
    compileSdkVersion 28
    buildToolsVersion "29.0.2"


    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 28
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            consumerProguardFiles 'consumer-rules.pro'
        }
        profile {
            minifyEnabled false
            consumerProguardFiles 'consumer-rules.pro'
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'androidx.appcompat:appcompat:1.0.2'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.1'



    /////////////////////////Bugly//////////////////////////////////
    implementation 'com.tencent.bugly:crashreport_upgrade:1.4.1'//其中latest.release指代最新版本号，也可以指定明确的版本号，例如1.2.0
    implementation 'com.tencent.bugly:nativecrashreport:3.7.1' //其中latest.release指代最新版本号，也可以指定明确的版本号，例如2.2.0
}
