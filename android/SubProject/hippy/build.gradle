buildscript {
    ext.kotlin_version="1.3.50"
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }

        google()
        jcenter()
//        mavenCentral()
//        maven { url 'https://repo1.maven.org/maven2/' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

//rootProject.allprojects {
//    repositories {
//        google()
//        jcenter()
//        mavenCentral()
//        maven { url 'https://repo1.maven.org/maven2/' }
//    }
//}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion 29
    buildToolsVersion "29.0.2"

    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 29
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'proguard-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        profile {
            minifyEnabled false
            consumerProguardFiles 'proguard-rules.pro'
        }

    }
}

dependencies {
//    implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'])
    compileOnly fileTree(include: ['*.jar', '*.aar'], dir: '../../app/libs')

    compileOnly 'com.tencent.mm.opensdk:wechat-sdk-android-with-mta:+'
    compileOnly project(path: ':CommonBaseModule')

    compileOnly "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.github.bumptech.glide:glide:3.6.1'

    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'me.dm7.barcodescanner:zxing:1.9.13'

    implementation 'com.liulishuo.filedownloader:library:1.7.7'

    implementation('io.socket:socket.io-client:2.0.1') {
        // excluding org.json which is provided by Android
        exclude group: 'org.json', module: 'json'
    }

    //// for local notification////////////////////
    implementation "androidx.media:media:1.1.0"
    implementation "com.google.code.gson:gson:2.8.5"
    ////////////////////////////////////////

    ////////////////////OSS///////////////////////
    api 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.aliyun.dpa:oss-android-sdk:+'

    implementation 'androidx.core:core:1.1.0'
    implementation 'androidx.annotation:annotation:1.0.0'


    //////////////////////////umeng sdk///////////////////////////////
//    implementation 'com.umeng.umsdk:analytics:8.0.2'
    implementation 'com.umeng.umsdk:common:9.4.4'
    implementation  'com.umeng.umsdk:asms:1.4.1'// asms包依赖必选

    //二维码生成
    implementation 'com.google.zxing:core:3.3.0'

    //Markwon for Markdown rendering
    implementation 'io.noties.markwon:core:4.6.2'
    implementation 'io.noties.markwon:html:4.6.2'
    implementation 'io.noties.markwon:image:4.6.2'
    implementation 'io.noties.markwon:linkify:4.6.2'

    //
    implementation 'androidx.exifinterface:exifinterface:1.1.0-beta01'

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'

    //for BlurView 高斯模糊组件
    implementation 'com.eightbitlab:blurview:1.6.3'
//    implementation 'com.eightbitlab:supportrenderscriptblur:1.0.2'

//    implementation project(':android-sdk')
    implementation 'com.caverock:androidsvg-aar:1.4'
}
