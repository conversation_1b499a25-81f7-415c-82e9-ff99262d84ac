<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.abc.hippy">

    <!-- ASR 相关权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />

    <!--Oppo 权限配置-->
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />

    <uses-sdk
        android:targetSdkVersion="28"
        android:minSdkVersion="19" />

    <application android:usesCleartextTraffic="true">
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <meta-data
            android:name="com.xiaomi.mipush.sdk.appid"
            android:value="\${XIAOMI_APPID}" />
        <meta-data
            android:name="com.xiaomi.mipush.sdk.appkey"
            android:value="\${XIAOMI_APPKEY}" />



        <activity android:name="com.abc.hippy.modules.barcodescan.BarcodeScannerActivity"
            android:theme="@style/QRCodeScanTheme.NoActionBar"/>


        <!-- image picker-->
        <provider
            android:name="com.abc.hippy.modules.imagepicker.ImagePickerFileProvider"
            android:authorities="${applicationId}.flutter.image_provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/flutter_image_picker_file_paths" />
        </provider>

        <provider
            android:name="com.abc.hippy.modules.share.ShareExtendProvider"
            android:authorities="${applicationId}.shareextend.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true"/>

        <!-- ABC ASR Service -->
        <service
            android:name="com.abc.hippy.modules.asr.AbcAsrService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="microphone" />
    </application>
</manifest>
