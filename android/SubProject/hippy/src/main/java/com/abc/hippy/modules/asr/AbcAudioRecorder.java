package com.abc.hippy.modules.asr;

import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.media.audiofx.AcousticEchoCanceler;
import android.media.audiofx.AudioEffect;
import android.media.audiofx.NoiseSuppressor;
import android.os.Handler;
import android.os.HandlerThread;

import com.abc.common.utils.LogUtils;
import com.abc.hippy.modules.asr.model.AsrConfig;

/**
 * ABC 音频录制器
 * 支持后台录音和音频处理
 */
public class AbcAudioRecorder {
    private static final String TAG = "AbcAudioRecorder";

    // 固定头部标识 (0xFF, 0xFE)
    private static final byte[] AUDIO_HEADER = new byte[]{(byte) 0xFF, (byte) 0xFE};

    public interface AudioDataCallback {
        void onAudioData(byte[] audioData, int length);

        void onWaveformData(byte[] waveform, int length);
    }

    private AsrConfig config;
    private AudioRecord audioRecord;
    private HandlerThread recordThread;
    private Handler recordHandler;
    private AudioDataCallback audioDataCallback;


    // 音频参数
    private int sampleRate;
    private int channelConfig;
    private int audioFormat;
    private int bufferSize;
    private int minBufferSize;

    // 音量检测
    private volatile float currentVolume = 0.0f;
    private volatile int audioDataCount = 0;

    // 波形数据回调控制
    private long lastWaveformCallbackTime = 0;
    private int waveformCallbackInterval;
    private int readBufferSize; // 实际读取缓冲区大小（小缓冲区）

    private volatile boolean isRecording = false;

    // 音频增强
    private AcousticEchoCanceler echoCanceler;
    private NoiseSuppressor noiseSuppressor;

    public AbcAudioRecorder(AsrConfig config) {
        this.config = config;
        initAudioParams();
    }

    /**
     * 初始化音频参数
     */
    private void initAudioParams() {
        sampleRate = config.getSampleRate();
        channelConfig = config.getChannels() == 1 ? AudioFormat.CHANNEL_IN_MONO : AudioFormat.CHANNEL_IN_STEREO;
        audioFormat = config.getAudioFormat() == 16 ? AudioFormat.ENCODING_PCM_16BIT : AudioFormat.ENCODING_PCM_8BIT;
        bufferSize = config.getBufferSize();

        // 初始化波形数据回调间隔
        waveformCallbackInterval = config.getWaveformCallbackInterval();

        // 计算最小缓冲区大小
        minBufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat);

        // 计算推荐的缓冲区大小（100ms 的音频数据）
        int bytesPerSample = config.getAudioFormat() / 8;
        int recommendedBufferSize = sampleRate * config.getChannels() * bytesPerSample / 10; // 100ms

        // 使用最大值确保缓冲区足够大
        int optimalBufferSize = Math.max(Math.max(bufferSize, minBufferSize), recommendedBufferSize);

        if (bufferSize != optimalBufferSize) {
            LogUtils.w(TAG, String.format("Buffer size adjusted: %d -> %d (min: %d, recommended: %d)",
                    bufferSize, optimalBufferSize, minBufferSize, recommendedBufferSize));
            bufferSize = optimalBufferSize;
        }

        // 基于波形回调间隔计算小缓冲区
        // 例如：33ms 间隔 = 16000 * 1 * 2 * 0.033 = 1056 bytes
        float intervalSeconds = waveformCallbackInterval / 1000.0f;
        readBufferSize = (int) (sampleRate * config.getChannels() * bytesPerSample * intervalSeconds);

        // 确保读取缓冲区不小于最小缓冲区，但也不要太大
        readBufferSize = Math.max(readBufferSize, minBufferSize);
        readBufferSize = Math.min(readBufferSize, bufferSize / 2); // 不超过累积缓冲区的一半

        // 确保是偶数（16位PCM需要）
        if (readBufferSize % 2 != 0) {
            readBufferSize++;
        }

        LogUtils.d(TAG, String.format("Using small read buffer for high-frequency waveform callbacks: %d bytes (%.1fms)",
                readBufferSize, readBufferSize * 1000.0f / (sampleRate * config.getChannels() * bytesPerSample)));

        LogUtils.d(TAG, "Audio params - sampleRate: " + sampleRate +
                ", channels: " + config.getChannels() +
                ", format: " + config.getAudioFormat() +
                ", bufferSize: " + bufferSize +
                ", readBufferSize: " + readBufferSize +
                ", waveformCallbackInterval: " + waveformCallbackInterval +
                ", enableAudioHeader: " + config.isEnableAudioHeader());
    }

    /**
     * 开始录音
     */
    public boolean startRecording() {
        LogUtils.d(TAG, "Starting audio recording");

        try {
            // 创建 AudioRecord
            audioRecord = new AudioRecord(
                    MediaRecorder.AudioSource.MIC,
                    sampleRate,
                    channelConfig,
                    audioFormat,
                    bufferSize
            );

            // 检查 AudioRecord 状态
            if (audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
                LogUtils.e(TAG, "AudioRecord initialization failed");
                return false;
            }

            // 尝试启用音频增强功能
            enableAudioEnhancements();

            // 创建录音线程
            recordThread = new HandlerThread("AudioRecordThread");
            recordThread.start();
            recordHandler = new Handler(recordThread.getLooper());

            // 开始录音
            audioRecord.startRecording();
            audioDataCount = 0;
            isRecording = true;

            // 在录音线程中处理音频数据
            recordHandler.post(this::recordingLoop);

            LogUtils.d(TAG, "Audio recording started successfully");
            return true;

        } catch (Exception e) {
            LogUtils.e(TAG, "Failed to start recording: " + e.getMessage());
            cleanup();
            return false;
        }
    }

    /**
     * 停止录音
     */
    public void stopRecording() {
        isRecording = false;
        LogUtils.d(TAG, "Stopping audio recording");
        cleanup();
    }

    /**
     * 检查是否正在录音
     */
    public boolean isRecording() {
        return isRecording;
    }

    /**
     * 设置音频数据回调
     */
    public void setAudioDataCallback(AudioDataCallback callback) {
        this.audioDataCallback = callback;
    }

    /**
     * 启用音频增强功能
     */
    private void enableAudioEnhancements() {
        try {
            // 回音消除
            if (AcousticEchoCanceler.isAvailable()) {
                echoCanceler = AcousticEchoCanceler.create(audioRecord.getAudioSessionId());
                if (echoCanceler != null) {
                    int result = echoCanceler.setEnabled(true);
                    if (result == AudioEffect.SUCCESS) {
                        LogUtils.d(TAG, "AcousticEchoCanceler enabled successfully");
                    } else {
                        LogUtils.w(TAG, "Failed to enable AcousticEchoCanceler");
                    }
                }
            } else {
                LogUtils.d(TAG, "AcousticEchoCanceler not available");
            }

            // 噪音抑制
            if (NoiseSuppressor.isAvailable()) {
                noiseSuppressor = NoiseSuppressor.create(audioRecord.getAudioSessionId());
                if (noiseSuppressor != null) {
                    int result = noiseSuppressor.setEnabled(true);
                    if (result == AudioEffect.SUCCESS) {
                        LogUtils.d(TAG, "NoiseSuppressor enabled successfully");
                    } else {
                        LogUtils.w(TAG, "Failed to enable NoiseSuppressor");
                    }
                }
            } else {
                LogUtils.d(TAG, "NoiseSuppressor not available");
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "Failed to enable audio enhancements: " + e.getMessage());
        }
    }

    /**
     * 录音循环
     */
    private void recordingLoop() {
        byte[] readBuffer = new byte[readBufferSize]; // 小缓冲区用于频繁读取
        byte[] accumulatedBuffer = new byte[bufferSize * 2]; // 累积缓冲区
        int accumulatedBytes = 0;

        LogUtils.d(TAG, String.format("Recording loop started - readBufferSize: %d bytes, accumulateBufferSize: %d bytes, waveformInterval: %dms",
                readBufferSize, bufferSize, waveformCallbackInterval));

        while (isRecording) {
            try {
                int bytesRead = audioRecord.read(readBuffer, 0, readBuffer.length);

                if (bytesRead > 0) {
                    // 累积音频数据直到达到期望的大小
                    System.arraycopy(readBuffer, 0, accumulatedBuffer, accumulatedBytes, bytesRead);
                    accumulatedBytes += bytesRead;

                    // 控制波形数据回调频率
                    if (audioDataCallback != null) {
                        long currentTime = System.currentTimeMillis();
                        long interval = currentTime - lastWaveformCallbackTime;

                        if (lastWaveformCallbackTime == 0 || interval >= waveformCallbackInterval) {
                            LogUtils.d(TAG, String.format("Waveform callback - interval: %dms, target: %dms",
                                    interval, waveformCallbackInterval));

                            // 使用当前读取的数据进行波形回调
                            audioDataCallback.onWaveformData(readBuffer, bytesRead);
                            lastWaveformCallbackTime = currentTime;
                        }
                    }

                    // 当累积的数据达到期望大小时才发送
                    if (accumulatedBytes >= bufferSize) {
                        // 回调音频数据
                        if (audioDataCallback != null) {
                            // 验证音频数据
                            if (bufferSize % 2 != 0) {
                                LogUtils.w(TAG, "Audio data size is not even: " + bufferSize);
                            }

                            // 每10个数据包打印一次调试信息
                            if (++audioDataCount % 10 == 0) {
                                float durationMs = (float) bufferSize / (sampleRate * config.getChannels() * (config.getAudioFormat() / 8)) * 1000;
                                String headerInfo = config.isEnableAudioHeader() ?
                                    String.format(", with header=%d bytes", bufferSize + AUDIO_HEADER.length) : "";
                                LogUtils.d(TAG, String.format("Audio data #%d: raw=%d bytes%s, %.1fms, volume: %.1f",
                                        audioDataCount, bufferSize, headerInfo, durationMs, currentVolume));
                            }

                            // 创建音频数据包
                            byte[] rawAudioData = new byte[bufferSize];
                            System.arraycopy(accumulatedBuffer, 0, rawAudioData, 0, bufferSize);

                            // 根据配置决定是否添加头部标识
                            byte[] finalAudioData;
                            if (config.isEnableAudioHeader()) {
                                // 参考 JavaScript 逻辑：添加固定头部标识
                                finalAudioData = createAudioPacketWithHeader(rawAudioData);
                            } else {
                                finalAudioData = rawAudioData;
                            }

                            audioDataCallback.onAudioData(finalAudioData, finalAudioData.length);
                        }

                        // 处理剩余数据
                        int remainingBytes = accumulatedBytes - bufferSize;
                        if (remainingBytes > 0) {
                            System.arraycopy(accumulatedBuffer, bufferSize, accumulatedBuffer, 0, remainingBytes);
                        }
                        accumulatedBytes = remainingBytes;
                    }
                } else if (bytesRead == AudioRecord.ERROR_INVALID_OPERATION) {
                    LogUtils.e(TAG, "AudioRecord read error: INVALID_OPERATION");
                    break;
                } else if (bytesRead == AudioRecord.ERROR_BAD_VALUE) {
                    LogUtils.e(TAG, "AudioRecord read error: BAD_VALUE");
                    break;
                }
            } catch (Exception e) {
                LogUtils.e(TAG, "Error in recording loop: " + e.getMessage());
                break;
            }
        }

        LogUtils.d(TAG, "Recording loop ended");
    }

    /**
     * 创建带头部标识的音频数据包
     * 参考 JavaScript 逻辑：
     * const header = new Uint8Array([0xFF, 0xFE]); // 固定头部标识
     * const audioData = data instanceof Uint8Array ? data : new Uint8Array(data.buffer);
     * const packet = new Uint8Array(header.length + audioData.length);
     * packet.set(header, 0);
     * packet.set(audioData, header.length);
     */
    private byte[] createAudioPacketWithHeader(byte[] audioData) {
        // 创建包含头部和音频数据的数据包
        byte[] packet = new byte[AUDIO_HEADER.length + audioData.length];

        // 设置头部标识 (0xFF, 0xFE)
        System.arraycopy(AUDIO_HEADER, 0, packet, 0, AUDIO_HEADER.length);

        // 设置音频数据
        System.arraycopy(audioData, 0, packet, AUDIO_HEADER.length, audioData.length);

        // 每100个数据包打印一次头部信息用于调试
        if (audioDataCount % 100 == 0) {
            LogUtils.d(TAG, String.format("Audio packet #%d: header=[0x%02X, 0x%02X], audioSize=%d, totalSize=%d",
                    audioDataCount,
                    packet[0] & 0xFF, packet[1] & 0xFF,
                    audioData.length, packet.length));
        }

        return packet;
    }

    /**
     * 清理资源
     */
    private void cleanup() {
        // 停止 AudioRecord
        if (audioRecord != null) {
            try {
                if (audioRecord.getRecordingState() == AudioRecord.RECORDSTATE_RECORDING) {
                    audioRecord.stop();
                }
                audioRecord.release();
            } catch (Exception e) {
                LogUtils.e(TAG, "Error stopping AudioRecord: " + e.getMessage());
            }
            audioRecord = null;
        }

        // 释放音频增强
        if (echoCanceler != null) {
            try {
                echoCanceler.setEnabled(false);
                echoCanceler.release();
            } catch (Exception e) {
                LogUtils.e(TAG, "Error releasing echoCanceler: " + e.getMessage());
            }
            echoCanceler = null;
        }

        if (noiseSuppressor != null) {
            try {
                noiseSuppressor.setEnabled(false);
                noiseSuppressor.release();
            } catch (Exception e) {
                LogUtils.e(TAG, "Error releasing noiseSuppressor: " + e.getMessage());
            }
            noiseSuppressor = null;
        }

        // 停止录音线程
        if (recordThread != null) {
            recordThread.quitSafely();
            try {
                recordThread.join(1000);
            } catch (InterruptedException e) {
                LogUtils.e(TAG, "Error joining record thread: " + e.getMessage());
            }
            recordThread = null;
            recordHandler = null;
        }

        LogUtils.d(TAG, "Audio recorder cleanup completed");
    }
}
