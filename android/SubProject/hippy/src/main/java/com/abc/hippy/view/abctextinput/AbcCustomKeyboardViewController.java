package com.abc.hippy.view.abctextinput;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.dom.node.NodeProps;
import com.tencent.mtt.hippy.dom.node.StyleNode;
import com.tencent.mtt.hippy.uimanager.HippyGroupController;
import com.tencent.mtt.hippy.utils.PixelUtil;

@HippyController(name = AbcCustomKeyboardViewController.CLASS_NAME)
public class AbcCustomKeyboardViewController extends HippyGroupController<AbcCustomKeyboardView> {
    public static final String CLASS_NAME = "AbcCustomKeyboardView";

    @Override
    protected View createViewImpl(Context context) {
        return new AbcCustomKeyboardView(context);
    }

    @Override
    public void onAfterUpdateProps(AbcCustomKeyboardView v) {
        super.onAfterUpdateProps(v);
        v.showOrUpdate();
    }

    @Override
    protected StyleNode createNode(boolean isVirtual) {
        return new AbcCustomKeyboardStyleNode();
    }

    @Override
    public void onViewDestroy(AbcCustomKeyboardView view) {
        super.onViewDestroy(view);
        view.onInstanceDestroy(view.getId());
    }


//    @HippyControllerProps(name = "keyboardHeight", defaultType = HippyControllerProps.NUMBER)
//    public void setKeyboardHeight(AbcCustomKeyboardView hippyTextInput, int keyboardHeight) {
//        hippyTextInput.setKeyboardHeight(keyboardHeight);
//    }

//    @HippyControllerProps(name = NodeProps.HEIGHT, defaultType = HippyControllerProps.NUMBER, defaultNumber = 320)
//    public void setKeyboardHeight(AbcCustomKeyboardView keyboardView, float fontSize) {
//        keyboardView.setKeyboardHeight(PixelUtil.dp2px(fontSize));
//    }
}
