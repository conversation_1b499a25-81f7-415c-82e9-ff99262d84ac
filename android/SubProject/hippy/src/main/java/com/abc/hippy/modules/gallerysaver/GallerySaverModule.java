package com.abc.hippy.modules.gallerysaver;

import com.abc.common.utils.RequestPermissionResultDispatcher;
import com.abc.hippy.utils.HippyUtils;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;


@HippyNativeModule(name = GallerySaverModule.CLASSNAME)
public class GallerySaverModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "GallerySaver";
    private GallerySaver mGallerySaver;

    public GallerySaverModule(HippyEngineContext context) {
        super((context));
        mGallerySaver = new GallerySaver();
        RequestPermissionResultDispatcher.instance.addListener(mGallerySaver);
    }


    @HippyMethod(name = "saveImage")
    public void saveImage(HippyMap map, Promise promise) {
        String path = map.getString("path");
        String albumName = map.getString("albumName");
        mGallerySaver.checkPermissionAndSaveFile(map, HippyUtils.createCommonHippyInvokeResultHandler(promise), MediaType.image);
    }


    @HippyMethod(name = "saveVideo")
    public void saveVideo(HippyMap map, Promise promise) {
        mGallerySaver.checkPermissionAndSaveFile(map, HippyUtils.createCommonHippyInvokeResultHandler(promise), MediaType.video);
    }
}
