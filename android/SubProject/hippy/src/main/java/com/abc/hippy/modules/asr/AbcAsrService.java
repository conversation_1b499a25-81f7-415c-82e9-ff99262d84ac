package com.abc.hippy.modules.asr;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import android.os.PowerManager;

import androidx.core.app.NotificationCompat;

import com.abc.common.utils.LogUtils;
import com.abc.hippy.modules.asr.model.AsrConfig;

import java.io.Serializable;

/**
 * ABC ASR 前台服务
 * 支持后台语音识别功能
 */
public class AbcAsrService extends Service {
    private static final String TAG = "AbcAsrService";
    private static final int NOTIFICATION_ID = 1001;
    private static final String CHANNEL_ID = "asr_service_channel";
    private static final String CHANNEL_NAME = "语音识别服务";

    public static final String ACTION_START_ASR = "com.abc.hippy.asr.START_RECORDING";
    public static final String ACTION_STOP_ASR = "com.abc.hippy.asr.STOP";
    public static final String EXTRA_CONFIG = "config";

    // 屏幕常亮相关
    private PowerManager.WakeLock wakeLock;
    private PowerManager powerManager;
    private boolean isWakeLockHeld = false;

    // Binder for local service binding
    public class AsrServiceBinder extends Binder {
        public AbcAsrService getService() {
            return AbcAsrService.this;
        }
    }

    private final IBinder binder = new AsrServiceBinder();
    
    @Override
    public void onCreate() {
        super.onCreate();
        LogUtils.d(TAG, "ASR Service created");

        // 初始化 PowerManager
        initializePowerManager();

        createNotificationChannel();
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            LogUtils.d(TAG, "Received action: " + action);

            switch (action != null ? action : "") {
                case ACTION_START_ASR:
                    Serializable configExtra = intent.getSerializableExtra(EXTRA_CONFIG);
                    if (configExtra instanceof AsrConfig) {
                        startAsrRecognition((AsrConfig) configExtra);
                    }

                    break;
                case ACTION_STOP_ASR:
                    stopAsrRecognition();
                    break;
            }
        }

        return START_STICKY; // 服务被杀死后自动重启
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }
    
    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("用于语音识别后台服务的通知");
            channel.setShowBadge(false);
            channel.setSound(null, null);
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private Notification createNotification(AsrConfig config) {
        String title = config.getServiceNotificationTitle();
        String content = config.getServiceNotificationContent();
        
        // 创建停止服务的 PendingIntent
        Intent stopIntent = new Intent(this, AbcAsrService.class);
        stopIntent.setAction(ACTION_STOP_ASR);
        PendingIntent stopPendingIntent = PendingIntent.getService(
                this, 0, stopIntent, 
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_IMMUTABLE : 0
        );
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle(title)
                .setContentText(content)
                .setSmallIcon(android.R.drawable.ic_btn_speak_now)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setOngoing(true)
                .setAutoCancel(false)
                .addAction(android.R.drawable.ic_media_pause, "停止", stopPendingIntent);
        
        return builder.build();
    }

    /**
     * 开始 ASR 识别服务显示
     */
    private void startAsrRecognition(AsrConfig config) {
        LogUtils.d(TAG, "Starting ASR recognition service");

        // 启动前台服务
        Notification notification = createNotification(config);
        startForeground(NOTIFICATION_ID, notification);

        // 开始录音时保持屏幕常亮
        acquireWakeLock();
    }

    
    /**
     * 停止 ASR 识别服务显示
     */
    private void stopAsrRecognition() {
        LogUtils.d(TAG, "Stopping ASR recognition service");

        // 停止录音时释放屏幕常亮
        releaseWakeLock();

        stopForeground(true);
        stopSelf();
    }

    
    @Override
    public void onDestroy() {
        super.onDestroy();

        // 确保在服务销毁时释放 WakeLock，避免内存泄漏
        releaseWakeLock();

        LogUtils.d(TAG, "ASR Service destroyed");
    }
    
    /**
     * 开始
     */
    public static void startAsrRecording(Context context,  AsrConfig config) {
        Intent intent = new Intent(context, AbcAsrService.class);
        intent.setAction(ACTION_START_ASR);
        intent.putExtra(EXTRA_CONFIG, config);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        }
    }
    
    /**
     * 停止 ASR 服务的静态方法
     */
    public static void stopAsrService(Context context) {
        Intent intent = new Intent(context, AbcAsrService.class);
        intent.setAction(ACTION_STOP_ASR);
        context.startService(intent);
    }

    /**
     * 初始化 PowerManager
     */
    private void initializePowerManager() {
        try {
            powerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
            if (powerManager != null) {
                // 创建 WakeLock，使用 SCREEN_DIM_WAKE_LOCK 保持屏幕常亮但允许变暗
                // 使用 ACQUIRE_CAUSES_WAKEUP 标志在获取时唤醒屏幕
                wakeLock = powerManager.newWakeLock(
                    PowerManager.SCREEN_DIM_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP,
                    TAG + ":AsrScreenWakeLock"
                );

                // 设置 WakeLock 引用计数为 false，避免重复获取
                wakeLock.setReferenceCounted(false);

                LogUtils.d(TAG, "PowerManager initialized successfully");
            } else {
                LogUtils.e(TAG, "Failed to get PowerManager service");
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "Error initializing PowerManager: " + e.getMessage());
        }
    }

    /**
     * 获取 WakeLock，保持屏幕常亮
     */
    private void acquireWakeLock() {
        try {
            if (wakeLock != null && !isWakeLockHeld) {
                // 检查是否有 WAKE_LOCK 权限
                if (checkWakeLockPermission()) {
                    wakeLock.acquire(15 * 60 * 1000L); // 最多持有15分钟，防止意外长时间持有
                    isWakeLockHeld = true;
                    LogUtils.d(TAG, "WakeLock acquired - screen will stay on during recording");
                } else {
                    LogUtils.w(TAG, "WAKE_LOCK permission not granted, cannot keep screen on");
                }
            } else if (isWakeLockHeld) {
                LogUtils.d(TAG, "WakeLock already held, skipping acquire");
            } else {
                LogUtils.w(TAG, "WakeLock is null, cannot acquire");
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "Error acquiring WakeLock: " + e.getMessage());
        }
    }

    /**
     * 释放 WakeLock，恢复屏幕设置
     */
    private void releaseWakeLock() {
        try {
            if (wakeLock != null && isWakeLockHeld) {
                wakeLock.release();
                isWakeLockHeld = false;
                LogUtils.d(TAG, "WakeLock released - screen settings restored");
            } else if (!isWakeLockHeld) {
                LogUtils.d(TAG, "WakeLock not held, skipping release");
            } else {
                LogUtils.w(TAG, "WakeLock is null, cannot release");
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "Error releasing WakeLock: " + e.getMessage());
            // 即使出现异常，也要重置状态标志
            isWakeLockHeld = false;
        }
    }

    /**
     * 检查 WAKE_LOCK 权限
     */
    private boolean checkWakeLockPermission() {
        try {
            int permission = checkCallingOrSelfPermission(android.Manifest.permission.WAKE_LOCK);
            boolean hasPermission = permission == android.content.pm.PackageManager.PERMISSION_GRANTED;

            if (!hasPermission) {
                LogUtils.w(TAG, "WAKE_LOCK permission not granted. Please add the following permission to AndroidManifest.xml:");
                LogUtils.w(TAG, "<uses-permission android:name=\"android.permission.WAKE_LOCK\" />");
            }

            return hasPermission;
        } catch (Exception e) {
            LogUtils.e(TAG, "Error checking WAKE_LOCK permission: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取当前 WakeLock 状态（用于调试）
     */
    public boolean isScreenKeptOn() {
        return isWakeLockHeld;
    }

    /**
     * 手动释放 WakeLock（用于异常情况处理）
     */
    public void forceReleaseWakeLock() {
        LogUtils.d(TAG, "Force releasing WakeLock");
        releaseWakeLock();
    }
}
