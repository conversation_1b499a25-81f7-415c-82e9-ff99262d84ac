package com.abc.hippy.modules.asr;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;

import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.LogUtils;
import com.abc.hippy.modules.asr.model.AsrConfig;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
/**
 * ABC ASR Hippy 模块
 * 提供自定义 ASR 功能的 JavaScript 接口
 */
@HippyNativeModule(name = AbcAsrModule.CLASSNAME)
public class AbcAsrModule extends HippyNativeModuleBase {

    final static String CLASSNAME = "AbcASR";
    private static final String TAG = "AbcAsrModule";

    // 活跃会话管理
    private final ConcurrentHashMap<String, String> mActiveSessions = new ConcurrentHashMap<>();

    // 直接模式的 ASR 管理器
    private AbcAsrManager directAsrManager = null;

    private AsrConfig currentConfig = null;

    private final ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            LogUtils.d(TAG, "ASR Service connected");
            AbcAsrService.AsrServiceBinder binder = (AbcAsrService.AsrServiceBinder) service;
            binder.getService();
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            LogUtils.d(TAG, "ASR Service disconnected");
        }
    };

    public AbcAsrModule(HippyEngineContext context) {
        super(context);
    }

    @Override
    public void initialize() {
        super.initialize();
        LogUtils.d(TAG, "AbcAsrModule initialized");
    }

    /**
     * 初始化 ASR 连接
     *
     * @param params  初始化参数
     * @param promise 返回初始化结果
     */
    @HippyMethod(name = "init")
    public void init(HippyMap params, Promise promise) {
        LogUtils.d(TAG, "Initializing ASR connection");

        try {
            // 构建配置（使用临时sessionId）
            AsrConfig config = buildAsrConfig(params, "init-session");
            currentConfig = config;

            // 根据配置选择初始化模式
            if (config.isEnableForegroundService()) {
                // 服务模式：初始化服务连接但不启动录音
                Intent intent = new Intent(ContextHolder.getAppContext(), AbcAsrService.class);
                ContextHolder.getAppContext().bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE);
                LogUtils.d(TAG, "ASR initialized in service mode");
            }
            LogUtils.d(TAG, "Initializing direct mode");

            directAsrManager = new AbcAsrManager();

            // 在直接模式下，建立WebSocket连接但不开始录音
            boolean success = directAsrManager.initConnection(config);
            if (success) {
                LogUtils.d(TAG, "ASR initialized in direct mode");
                promise.resolve(true);
            } else {
                LogUtils.e(TAG, "Failed to initialize direct mode");
                promise.reject("直接模式初始化失败");
            }

        } catch (Exception e) {
            LogUtils.e(TAG, "Failed to initialize ASR: " + e.getMessage());
            promise.reject("初始化失败: " + e.getMessage());
        }
    }


    /**
     * 开始语音识别
     *
     * @param params  识别参数
     * @param promise 返回会话ID
     */
    @HippyMethod(name = "startRecognize")
    public void startRecognize(HippyMap params, Promise promise) {
        String audioDataName = params.getString("audioDataName");

        // 生成唯一会话ID
        final String sessionId = UUID.randomUUID().toString();

        LogUtils.d(TAG, "Starting recognition with sessionId: " + sessionId);

        try {
            // 更新配置中的sessionId
            if (currentConfig != null) {
                currentConfig.setSessionId(sessionId);
            }

            if (directAsrManager != null && currentConfig != null) {
                LogUtils.d(TAG, "Starting direct recording");
                boolean success = directAsrManager.startRecording(audioDataName);
                if (!success) {
                    LogUtils.e(TAG, "Failed to start direct recording");
                }
            } else {
                LogUtils.e(TAG, "Direct ASR manager or config is null");
            }

            // 添加到活跃会话
            mActiveSessions.put(sessionId, sessionId);

            // 返回会话ID
            HippyMap resultMap = new HippyMap();
            resultMap.pushString("sessionId", sessionId);
            promise.resolve(resultMap);

        } catch (Exception e) {
            LogUtils.e(TAG, "Failed to start recognition: " + e.getMessage());
            promise.reject("启动识别失败: " + e.getMessage());
        }
    }

    @HippyMethod(name = "startService")
    public void startService(HippyMap params, Promise promise) {
        // 开始录音操作
        if (currentConfig != null && currentConfig.isEnableForegroundService()) {
            // 开始 ASR 服务录音
            AbcAsrService.startAsrRecording(ContextHolder.getAppContext(), currentConfig);
        }
        promise.resolve(true);
    }

    @HippyMethod(name = "stopService")
    public void stopService(HippyMap params, Promise promise) {
        // 停止录音
        if (currentConfig != null && currentConfig.isEnableForegroundService()) {
            // 停止 ASR 服务
            AbcAsrService.stopAsrService(ContextHolder.getAppContext());
        }
        promise.resolve(true);
    }

    /**
     * 停止语音识别
     *
     * @param params  包含sessionId
     * @param promise 返回操作结果
     */
    @HippyMethod(name = "stopRecognize")
    public void stopRecognize(HippyMap params, Promise promise) {
        String sessionId = params.getString("sessionId");

        if (sessionId == null || sessionId.isEmpty()) {
            promise.reject("sessionId cannot be empty");
            return;
        }

        LogUtils.d(TAG, "Stopping recognition for session: " + sessionId);

        try {
            if (directAsrManager != null) {
                LogUtils.d(TAG, "Stopping direct recording");
                directAsrManager.stopRecognition();
            }

            // 从活跃会话中移除
            mActiveSessions.remove(sessionId);

            promise.resolve(true);
        } catch (Exception e) {
            LogUtils.e(TAG, "Failed to stop recognition: " + e.getMessage());
            promise.reject("停止识别失败: " + e.getMessage());
        }
    }

    @HippyMethod(name = "emitMessage")
    public void emitMessage(HippyMap params, Promise promise) {
        String event = params.getString("event");
        HippyMap data = params.getMap("data");
        directAsrManager.sendSocketMessage(event, data);
        promise.resolve(true);
    }

    @HippyMethod(name = "eventOn")
    public void eventOn(HippyMap params, Promise promise) {
        String event = params.getString("event");
        directAsrManager.socketOn(event);
        promise.resolve(true);
    }

    @HippyMethod(name = "eventOff")
    public void eventOff(HippyMap params, Promise promise) {
        String event = params.getString("event");
        directAsrManager.socketOff(event);
        promise.resolve(true);
    }

    @HippyMethod(name = "listenerCall")
    public void listenerCall(HippyMap params, Promise promise) {
        directAsrManager.listenerCall();
        promise.resolve(true);
    }

    @HippyMethod(name = "release")
    public void release(HippyMap params, Promise promise) {
        this.destroy();
    }

    /**
     * 构建 ASR 配置
     */
    private AsrConfig buildAsrConfig(HippyMap params, String sessionId) {
        AsrConfig config = new AsrConfig();

        // 基本配置
        config.setSessionId(sessionId);
        config.setServerUrl(params.getString("serverUrl"));

        // 音频配置
        if (params.get("sampleRate") != null) {
            config.setSampleRate(params.getInt("sampleRate"));
        }
        if (params.get("channels") != null) {
            config.setChannels(params.getInt("channels"));
        }
        if (params.get("audioFormat") != null) {
            config.setAudioFormat(params.getInt("audioFormat"));
        }

        // 识别配置
        if (params.get("language") != null) {
            config.setLanguage(params.getString("language"));
        }
        if (params.get("enableVad") != null) {
            config.setEnableVad(params.getBoolean("enableVad"));
        }
        if (params.get("enablePunctuation") != null) {
            config.setEnablePunctuation(params.getBoolean("enablePunctuation"));
        }
        if (params.get("enableNumberConvert") != null) {
            config.setEnableNumberConvert(params.getBoolean("enableNumberConvert"));
        }
        if (params.get("enableDirtyFilter") != null) {
            config.setEnableDirtyFilter(params.getBoolean("enableDirtyFilter"));
        }

        // 静音检测配置
        if (params.get("enableSilentDetection") != null) {
            config.setEnableSilentDetection(params.getBoolean("enableSilentDetection"));
        }
        if (params.get("silentTimeout") != null) {
            config.setSilentTimeout(params.getInt("silentTimeout"));
        }
        if (params.get("silentThreshold") != null) {
            config.setSilentThreshold((float) params.getDouble("silentThreshold"));
        }

        // 前台服务配置
        if (params.get("enableForegroundService") != null) {
            config.setEnableForegroundService(params.getBoolean("enableForegroundService"));
        }
        if (params.get("serviceNotificationTitle") != null) {
            config.setServiceNotificationTitle(params.getString("serviceNotificationTitle"));
        }
        if (params.get("serviceNotificationContent") != null) {
            config.setServiceNotificationContent(params.getString("serviceNotificationContent"));
        }

        // Socket.IO 连接配置
        if (params.get("cookies") != null) {
            config.setCookies(params.getString("cookies"));
        }
        if (params.get("userAgent") != null) {
            config.setUserAgent(params.getString("userAgent"));
        }
        if (params.get("withCredentials") != null) {
            config.setWithCredentials(params.getBoolean("withCredentials"));
        }
        if (params.get("namespace") != null) {
            config.setNamespace(params.getString("namespace"));
        }
        if (params.get("reconnectionDelay") != null) {
            config.setReconnectionDelay(params.getInt("reconnectionDelay"));
        }
        if (params.get("forceWebsockets") != null) {
            config.setForceWebsockets(params.getBoolean("forceWebsockets"));
        }
        if (params.get("path") != null) {
            config.setSocketPath(params.getString("path"));
        }
        if (params.get("connectParams") != null) {
            HippyMap connectParams = params.getMap("connectParams");
            if (connectParams != null) {
                for (String key : connectParams.keySet()) {
                    Object value = connectParams.get(key);
                    if (value != null) {
                        config.getConnectParams().put(key, value.toString());
                    }
                }
            }
        }

        // 处理额外的请求头
        if (params.get("extraHeaders") != null) {
            HippyMap extraHeaders = params.getMap("extraHeaders");
            if (extraHeaders != null) {
                for (String key : extraHeaders.keySet()) {
                    Object value = extraHeaders.get(key);
                    if (value != null) {
                        config.addExtraHeader(key, value.toString());
                    }
                }
            }
        }

        return config;
    }

    /**
     * 模块销毁时清理资源
     */
    @Override
    public void destroy() {
        super.destroy();

        // 解绑服务
        ContextHolder.getAppContext().unbindService(serviceConnection);
        // 清理直接模式管理器
        if (directAsrManager != null) {
            directAsrManager.release();
            directAsrManager = null;
        }

        // 清理所有活跃会话
        mActiveSessions.clear();

        // 重置状态
        currentConfig = null;

        LogUtils.d(TAG, "AbcAsrModule destroyed");
    }
}
