package com.abc.hippy.modules.wxapi;

import android.text.TextUtils;

import com.abc.hippy.utils.HippyUtils;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;


@HippyNativeModule(name = WxApiModule.CLASSNAME)
public class WxApiModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "WxApi";

    public WxApiModule(HippyEngineContext context) {
        super(context);
    }

    @HippyMethod(name = "registerApp")
    public void registerApp(HippyMap params, final Promise promise) {
        String appId = params.getString("appId");
        boolean enableMTA = params.getBoolean("enableMTA");

        if (TextUtils.isEmpty(appId)) {
            promise.resolve(new Exception("appId为空"));
            return;
        }

        WXAPIHandler.getInstance().registerApp(appId, enableMTA, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "sendAuth")
    public void sendAuth(HippyMap params, Promise promise) {
        WXAPIHandler.getInstance().sendAuth(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "isWeChatInstalled")
    public void isWeChatInstalled(HippyMap params, Promise promise) {
        WXAPIHandler.getInstance().checkWeChatInstallation(HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "share")
    public void share(HippyMap params, Promise promise) {
        WXAPIHandler.getInstance().share(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "payment")
    public void payment(HippyMap params, Promise promise) {
        WXAPIHandler.getInstance().payment(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "launchMiniProgram")
    public void launchMiniProgram(HippyMap params, Promise promise) {
        WXAPIHandler.getInstance().launchMiniProgram(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }
}
