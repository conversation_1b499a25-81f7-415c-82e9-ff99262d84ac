package com.abc.hippy.view.abcMarkdown;

import android.content.Context;
import android.text.TextUtils;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.tencent.mtt.hippy.uimanager.HippyViewBase;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;

import io.noties.markwon.Markwon;
import io.noties.markwon.html.HtmlPlugin;
import io.noties.markwon.image.ImagesPlugin;
import io.noties.markwon.linkify.LinkifyPlugin;

/**
 * Markdown渲染视图
 * 
 * 基于Markwon库实现的Markdown内容渲染组件
 * 支持标准Markdown语法、HTML标签、图片和链接
 * 
 * @version 1.0.0
 */
public class MarkdownView extends FrameLayout implements HippyViewBase {
    private NativeGestureDispatcher mGestureDispatcher;
    private String mMarkdownContent;
    private TextView mTextView;
    private Markwon mMarkwon;

    public MarkdownView(Context context) {
        super(context);
        initView(context);
        initMarkwon(context);
    }

    private void initView(Context context) {
        mTextView = new TextView(context);
        mTextView.setTextIsSelectable(true); // 允许文本选择
        mTextView.setPadding(16, 16, 16, 16); // 设置内边距
        
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(
                LayoutParams.MATCH_PARENT, 
                LayoutParams.MATCH_PARENT
        );
        this.addView(mTextView, layoutParams);
    }

    private void initMarkwon(Context context) {
        mMarkwon = Markwon.builder(context)
                .usePlugin(HtmlPlugin.create()) // 支持HTML标签
                .usePlugin(ImagesPlugin.create()) // 支持图片
                .usePlugin(LinkifyPlugin.create()) // 支持链接识别
                .build();
    }

    @Override
    public NativeGestureDispatcher getGestureDispatcher() {
        return mGestureDispatcher;
    }

    @Override
    public void setGestureDispatcher(NativeGestureDispatcher dispatcher) {
        this.mGestureDispatcher = dispatcher;
    }

    /**
     * 设置Markdown内容
     * @param content Markdown格式的文本内容
     */
    public void setMarkdownContent(@Nullable String content) {
        if (mMarkdownContent != null && mMarkdownContent.equals(content)) {
            return; // 内容相同，无需重新渲染
        }
        
        mMarkdownContent = content;
        renderMarkdown();
    }

    /**
     * 获取当前的Markdown内容
     * @return 当前的Markdown内容
     */
    public String getMarkdownContent() {
        return mMarkdownContent;
    }

    /**
     * 渲染Markdown内容到TextView
     */
    private void renderMarkdown() {
        if (TextUtils.isEmpty(mMarkdownContent)) {
            mTextView.setText("");
            return;
        }

        try {
            // 使用Markwon渲染Markdown内容
            mMarkwon.setMarkdown(mTextView, mMarkdownContent);
        } catch (Exception e) {
            // 如果渲染失败，显示原始文本
            mTextView.setText(mMarkdownContent);
            e.printStackTrace();
        }
    }

    /**
     * 设置文本颜色
     * @param color 文本颜色值
     */
    public void setTextColor(int color) {
        if (mTextView != null) {
            mTextView.setTextColor(color);
        }
    }

    /**
     * 设置文本大小
     * @param textSize 文本大小（单位：sp）
     */
    public void setTextSize(float textSize) {
        if (mTextView != null) {
            mTextView.setTextSize(textSize);
        }
    }

    /**
     * 设置背景颜色
     * @param color 背景颜色值
     */
    public void setBackgroundColor(int color) {
        if (mTextView != null) {
            mTextView.setBackgroundColor(color);
        }
    }
}
