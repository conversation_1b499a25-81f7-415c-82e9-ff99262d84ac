package com.abc.hippy.view.gradient;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.views.view.HippyViewGroupController;


@HippyController(name = "LinearGradientView")
public class AbcLinearGradientViewController extends HippyViewGroupController {
    @Override
    protected View createViewImpl(Context context) {
        return new AbcLinearGradientView(context);
    }

    @HippyControllerProps(name = "colors", defaultType = HippyControllerProps.ARRAY)
    public void setColors(AbcLinearGradientView gradientView, HippyArray colors) {
        gradientView.setColors(colors);
    }


    @HippyControllerProps(name = "locations", defaultType = HippyControllerProps.ARRAY)
    public void setLocations(AbcLinearGradientView gradientView, HippyArray locations) {
        gradientView.setLocations(locations);
    }


    @HippyControllerProps(name = "startPoint")
    public void setStartPoint(AbcLinearGradientView gradientView, HippyMap startPoint) {
        gradientView.setStartPosition(startPoint);
    }

    @HippyControllerProps(name = "endPoint")
    public void setEndPoint(AbcLinearGradientView gradientView, HippyMap endPoint) {
        gradientView.setEndPosition(endPoint);
    }

    @HippyControllerProps(name = "useAngle")
    public void setUseAngle(AbcLinearGradientView gradientView, boolean useAngle) {
        gradientView.setUseAngle(useAngle);
    }

    @HippyControllerProps(name = "angleCenter")
    public void setAngleCenter(AbcLinearGradientView gradientView, HippyMap center) {
        gradientView.setAngleCenter(center);
    }

    @HippyControllerProps(name = "angle")
    public void setAngle(AbcLinearGradientView gradientView, float angle) {
        gradientView.setAngle(angle);
    }
}
