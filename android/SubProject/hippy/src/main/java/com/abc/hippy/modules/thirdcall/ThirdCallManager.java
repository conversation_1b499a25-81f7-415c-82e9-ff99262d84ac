package com.abc.hippy.modules.thirdcall;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.abc.common.utils.ActivityEventDispatcher;
import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.LogUtils;
import com.abc.hippy.modules.HostHippyMessageBridge;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;


public class ThirdCallManager {
    private static final String TAG = "ThirdCallManager";

    private static ThirdCallManager sInstance;

    public static synchronized ThirdCallManager getInstance() {
        if (sInstance == null) sInstance = new ThirdCallManager();
        return sInstance;
    }

    private Intent mIntent;
    private boolean mHasRegistered = false;


    private ActivityEventDispatcher.ActivityLifecycleListener mActivityLifecycleListener = new ActivityEventDispatcher.ActivityLifecycleListener() {
        @Override
        public boolean onNewIntent(Activity activity, Intent intent) {
            if (activity != ContextHolder.getMainActivity()) return false;
            mIntent = intent;
            ThirdCallManager.this.emitThirdCallAction();
            return false;
        }

    };

    private ThirdCallManager() {
        ActivityEventDispatcher.instance.addActivityLifecycleListener(mActivityLifecycleListener);
        Activity mainActivity = ContextHolder.getMainActivity();
        if (mainActivity != null) {
            this.mActivityLifecycleListener.onNewIntent(mainActivity, mainActivity.getIntent());
        }
    }


    @HippyMethod(name = "register")
    public void register(HippyMap params, Promise promise) {
        LogUtils.d(TAG, "register() called with: params = [" + params + "], promise = [" + promise + "], mHasRegistered = " + mHasRegistered);
        if (mHasRegistered) return;

        mHasRegistered = true;
        promise.resolve(true);
        ContextHolder.uiHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                emitThirdCallAction();
            }
        }, 500);
    }


    private void emitThirdCallAction() {
        LogUtils.d(TAG, "emitThirdCallAction() called");
        if (this.mIntent == null || !mHasRegistered) return;
        String action = null;
        if (!TextUtils.isEmpty(this.mIntent.getDataString())) {
            action = this.mIntent.getDataString();
        } else if (!TextUtils.isEmpty(this.mIntent.getAction())) {
            action = this.mIntent.getAction();
        }

        if (!TextUtils.isEmpty(action)) {
            HippyMap body = new HippyMap();
            body.pushString("action", this.mIntent.getDataString());
            LogUtils.d(TAG, "emitThirdCallAction() called action = this.mIntent.getDataString()");
            HostHippyMessageBridge.getInstance().onHostMessage("ThirdCall", body);
        }
    }
}
