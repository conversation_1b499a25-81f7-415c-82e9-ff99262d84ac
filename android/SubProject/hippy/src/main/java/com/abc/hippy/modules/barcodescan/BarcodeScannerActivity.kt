package com.abc.hippy.modules.barcodescan

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Handler
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.abc.hippy.R
import com.google.zxing.Result
import me.dm7.barcodescanner.zxing.ZXingScannerView


class BarcodeScannerActivity : Activity(), ZXingScannerView.ResultHandler {

    lateinit var scannerView: me.dm7.barcodescanner.zxing.ZXingScannerView

    companion object {
        val REQUEST_TAKE_PHOTO_CAMERA_PERMISSION = 100
        val TOGGLE_FLASH = 200

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.qr_scan_layout)
        scannerView = findViewById<ZXingScannerView>(R.id.qr_scan_view)
        scannerView.setAutoFocus(true)
        // this paramter will make your HUAWEI phone works great!
        scannerView.setAspectTolerance(0.5f)


        setupControlPanelView()
        setupNavigationBar()

        processArguments()
    }


    private fun processArguments() {
        val titleParams = intent.getStringExtra(BarCodeScanConsts.scanInvokeParamTitle)
        setTitle(if (titleParams.isNotEmpty()) titleParams else "扫描二维码")

        val selectCount = intent.getStringExtra(BarCodeScanConsts.scanInvokeParamSelectedCount)
        val selectTips = intent.getStringExtra(BarCodeScanConsts.scanInvokeParamSelectedTips)
        if (selectCount != null && selectTips != null && selectCount.isNotEmpty() && selectTips.isNotEmpty()) {
            findViewById<TextView>(R.id.qr_scan_count).text = selectCount
            findViewById<TextView>(R.id.select_tips).text = selectTips
        } else {
            findViewById<View>(R.id.qr_scan_count_container).visibility = View.GONE
        }
    }

    private fun setTitle(title: String) {
        this.title = title
        findViewById<TextView>(R.id.title).text = title
    }

    private fun setupNavigationBar() {
        findViewById<ImageView>(R.id.back_btn).setOnClickListener {
            handleWithAction(BarCodeScanConsts.userCancel)
        }
    }

    private fun setupControlPanelView() {
        val imageView = findViewById<ImageView>(R.id.qrcode_scan_flash_btn);

        val scanTips = findViewById<View>(R.id.qr_scan_count_container);

        scanTips.setOnClickListener {
            handleWithAction(BarCodeScanConsts.clickTip);
        }


        imageView.setOnClickListener {
            scannerView.flash = !scannerView.flash
            val drawable = this.resources.getDrawable(if (scannerView.flash) R.drawable.flash_on else R.drawable.flash_off)
            imageView.setImageDrawable(drawable)
        }
    }

    override fun onResume() {
        super.onResume()
        scannerView.setResultHandler(this)
        // start camera immediately if permission is already given
        if (!requestCameraAccessIfNecessary()) {
            scannerView.startCamera()
        }
    }

    override fun onPause() {
        super.onPause()
        scannerView.stopCamera()
    }

    override fun handleResult(result: Result?) {
        Handler().post {
            val intent = Intent()
            intent.putExtra("SCAN_RESULT", result.toString())
            setResult(Activity.RESULT_OK, intent)
            finish()
        }
    }

    private fun handleWithAction(action: String) {
        scannerView.stopCamera()
        val intent = Intent()
        intent.putExtra("SCAN_RESULT_ACTION", action)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    private fun finishWithError(errorCode: String) {
        val intent = Intent()
        intent.putExtra("ERROR_CODE", errorCode)
        setResult(Activity.RESULT_CANCELED, intent)
        finish()
    }

    private fun requestCameraAccessIfNecessary(): Boolean {
        val array = arrayOf(Manifest.permission.CAMERA)
        if (ContextCompat
                        .checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {

            ActivityCompat.requestPermissions(this, array,
                    REQUEST_TAKE_PHOTO_CAMERA_PERMISSION)
            return true
        }
        return false
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        when (requestCode) {
            REQUEST_TAKE_PHOTO_CAMERA_PERMISSION -> {
                if (PermissionUtil.verifyPermissions(grantResults)) {
                    scannerView.startCamera()
                } else {
                    finishWithError("PERMISSION_NOT_GRANTED")
                }
            }
            else -> {
                super.onRequestPermissionsResult(requestCode, permissions, grantResults)
            }
        }
    }
}

object PermissionUtil {

    /**
     * Check that all given permissions have been granted by verifying that each entry in the
     * given array is of the value [PackageManager.PERMISSION_GRANTED].

     * @see Activity.onRequestPermissionsResult
     */
    fun verifyPermissions(grantResults: IntArray): Boolean {
        // At least one result must be checked.
        if (grantResults.size < 1) {
            return false
        }

        // Verify that each required permission has been granted, otherwise return false.
        for (result in grantResults) {
            if (result != PackageManager.PERMISSION_GRANTED) {
                return false
            }
        }
        return true
    }
}
