package com.abc.hippy.view.abcnavigator;

import android.animation.Animator;
import android.animation.Animator.AnimatorListener;
import android.animation.ArgbEvaluator;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.DecelerateInterpolator;

import com.abc.common.utils.LogUtils;
import com.abc.hippy.ABCHippyEngineManager;
import com.abc.hippy.view.WillPopListener.IBackListener;
import com.abc.hippy.view.WillPopListener.WillPopListener;
import com.tencent.mtt.hippy.views.view.HippyViewGroup;
import java.util.ArrayList;

public class ABCNavigatorItemView extends HippyViewGroup implements ScreenEdgePanGestureRecognizer.GestureRecognizerListener {

    private static final String TAG = "ABCNavigatorItemView";
    private static final int KPageAnimDuration = 300;
    private static int MASK_COLOR = Color.argb(100, 0, 0, 0);
    private static int SHADOW_COLOR = Color.argb(250, 0, 0, 0);

    private ABCNavigatorView mNavigator;
    private int mTransitionType = TransitionType.InFromRight;
    private int mPageIndex;

    private boolean mPendingEntryAnimation = true;
    private ArrayList<IBackListener> mBackListeners;
    private boolean mPopAnimation = false;
    private int mBackgroundColor = ABCNavigatorItemView.MASK_COLOR;
    private boolean mMaskEnable = true;
    private boolean mEnableBackgroundColorAnimation = true;
    private boolean mEnableGestureRecognizer = true;
    private Paint mShadowPaint = null;
    private ScreenEdgePanGestureRecognizer mScreenEdgePanGestureRecognizer;
    private ABCHippyEngineManager.IBackPressedListener mBackPressedListener;
    private AnimationsListener mAnimationsListener;

    public ABCNavigatorItemView(Context context) {
        super(context);
        mBackListeners = new ArrayList<>();
    }

    public void setNavigator(ABCNavigatorView navigator) {
        mNavigator = navigator;
    }

    @Override
    public void addView(View view, int i) {
        super.addView(view, i);
        this.performEntryAnimation();
    }

    public View getContentView() {
        return getChildAt(0);
    }

    public void setTransitionType(int mTransitionType) {
        this.mTransitionType = mTransitionType;
    }

    public int getTransitionType() {
        return mTransitionType;
    }

    public void setPageIndex(int mPageIndex) {
        this.mPageIndex = mPageIndex;
    }

    public int getPageIndex() {
        return mPageIndex;
    }

    public void setBackPressedListener(ABCHippyEngineManager.IBackPressedListener listener) {
        mBackPressedListener = listener;
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.performEntryAnimation();
    }

    private int computeCurrentBackgroundColor() {
        View contentView = getContentView();
        if (contentView == null) {
            return 0;
        }

        float fraction = (float) contentView.getLeft() / (float) getWidth();
        int alpha = (int) (Color.alpha(mBackgroundColor) * (1 - fraction));
        int color = Color.argb(alpha, Color.red(mBackgroundColor), Color.green(mBackgroundColor), Color.blue(mBackgroundColor));
        return color;
    }

    @Override
    public void setBackgroundColor(int i) {
        super.setBackgroundColor(i);
        mBackgroundColor = i;
    }

    void performEntryAnimation() {
        View contentView = getContentView();
        if (contentView == null)  {
            return;
        }

        if (!this.mPendingEntryAnimation)
            return;

        this.mPendingEntryAnimation = true;

        if (mPageIndex <= 0)
            return;

        ObjectAnimator animator = null;
        ValueAnimator maskColorAnimator = null;

        int transitionType = this.getTransitionType();
        float fraction = 1.0f;
        if (transitionType == TransitionType.InFromRight) {
            View parentView = (View) getParent();
            if (parentView == null) {
                return;
            }
            final int width = parentView.getWidth();
            if (contentView.getLeft() > 0) {
                fraction = (float) contentView.getX() / (float) width;
                animator = ObjectAnimator.ofFloat(contentView, "translationX", 0, -contentView.getX());
            } else {
                animator = ObjectAnimator.ofFloat(contentView, "translationX", width, 0);
            }
        } else if (transitionType == TransitionType.InFromBottom) {
            animator = ObjectAnimator.ofFloat(contentView, "translationY", ((View) getParent()).getHeight(), 0);
        }

        if (mEnableBackgroundColorAnimation) {
            int backgroundColor = computeCurrentBackgroundColor();
            super.setBackgroundColor(backgroundColor);
            maskColorAnimator = ObjectAnimator.ofInt(this, "backgroundColor", backgroundColor, /*MASK_COLOR*/mBackgroundColor);
        }

        if (mAnimationsListener == null) {
            mAnimationsListener = new AnimationsListener();
        }

        final long duration = (long) (KPageAnimDuration/* * fraction*/);
        if (animator != null) {
            animator.setDuration(duration);
            animator.setInterpolator(new AccelerateDecelerateInterpolator());
            animator.addListener(mAnimationsListener);
            animator.addUpdateListener(mAnimationsListener);
            animator.start();
        }

        if (maskColorAnimator != null) {
            maskColorAnimator.setDuration(duration);
            maskColorAnimator.setEvaluator(new ArgbEvaluator());
            maskColorAnimator.start();
        }
    }

    void pop(boolean animate, final AnimatorListener listener) {
        if (mPopAnimation) return;
        mPopAnimation = true;

        if (!animate) {
            mPopAnimation = false;
            if (listener != null) {
                listener.onAnimationEnd(null);
            }

            return;
        }

        View contentView = getContentView();
        View parentView = (View) getParent();
        if (contentView == null || parentView == null) {
            mPopAnimation = false;
            if (listener != null)
                listener.onAnimationEnd(null);
            return;
        }

        this.mPendingEntryAnimation = true;
        if (mPageIndex <= 0) {
            mPopAnimation = false;
            if (listener != null)
                listener.onAnimationEnd(null);
            return;
        }

        final int startX = contentView.getLeft();
        final int width = parentView.getWidth();
        float fraction = 1.0f;
        ObjectAnimator animator = null;
        ValueAnimator maskColorAnimator = null;
        int transitionType = this.getTransitionType();
        if (transitionType == TransitionType.InFromRight) {
            int distance = width - startX;
            fraction = (float) (distance) / (float) width;
            animator = ObjectAnimator.ofFloat(contentView, "translationX", 0, distance);
        } else if (transitionType == TransitionType.InFromBottom) {
            animator = ObjectAnimator.ofFloat(contentView, "translationY", 0, ((View) getParent()).getHeight());
        }

        if (mEnableBackgroundColorAnimation) {
            int backgroundColor = computeCurrentBackgroundColor();
            super.setBackgroundColor(backgroundColor);
            maskColorAnimator = ObjectAnimator.ofInt(this, "backgroundColor", backgroundColor, Color.TRANSPARENT);
        }

        final long duration = (long) (KPageAnimDuration/* * fraction*/);

        if (mAnimationsListener == null) {
            mAnimationsListener = new AnimationsListener();
        }

        if (animator != null) {
            animator.setDuration(duration);
            // animator.setInterpolator(new AccelerateDecelerateInterpolator());
            animator.setInterpolator(new DecelerateInterpolator());
            animator.addUpdateListener(mAnimationsListener);
            animator.addListener(new AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animator) {
                    if (listener != null)
                        listener.onAnimationStart(animator);
                }

                @Override
                public void onAnimationEnd(Animator animator) {
                    mPopAnimation = false;
                    if (listener != null)
                        listener.onAnimationEnd(animator);
                }

                @Override
                public void onAnimationCancel(Animator animator) {
                    mPopAnimation = false;
                    if (listener != null)
                        listener.onAnimationCancel(animator);
                }

                @Override
                public void onAnimationRepeat(Animator animator) {
                    if (listener != null)
                        listener.onAnimationRepeat(animator);
                }
            });
            animator.start();

            if (maskColorAnimator != null) {
                maskColorAnimator.setDuration(duration);
                maskColorAnimator.setEvaluator(new ArgbEvaluator());
                maskColorAnimator.start();
            }
        } else {
            mPopAnimation = false;
            if (listener != null) {
                listener.onAnimationEnd(null);
            }
        }
    }

    private void resetContentLocation() {
        View contentView = getContentView();
        if (contentView != null) {
            contentView.setTranslationX(0.0f);
            contentView.layout(0, 0, getWidth(), getHeight());
        }
    }

    class AnimationsListener implements AnimatorListener, ValueAnimator.AnimatorUpdateListener {

        @Override
        public void onAnimationStart(Animator animation) {

        }

        @Override
        public void onAnimationEnd(Animator animation) {
            resetContentLocation();
        }

        @Override
        public void onAnimationCancel(Animator animation) {
            resetContentLocation();
        }

        @Override
        public void onAnimationRepeat(Animator animation) {

        }

        @Override
        public void onAnimationUpdate(ValueAnimator animation) {
        }
    }

    private float getBottomContentHeight(ViewGroup rootView) {

        return 0.0f;
    }

    public void addBackListener(IBackListener backListener) {
        if (mBackListeners.contains(backListener)) return;
        mBackListeners.add(backListener);
    }

    public void removeBackListener(IBackListener backListener) {
        mBackListeners.remove(backListener);
    }

    public boolean handleBackEvent(boolean fromEdgeGesture) {
        LogUtils.d(TAG, "handleBackEvent() called, listener.count = " + mBackListeners.size());
        for (IBackListener listener : mBackListeners) {
            if (listener.handleBack(fromEdgeGesture)) return true;
        }
        return false;
    }

    private boolean willPop() {
        if (mBackListeners == null || mBackListeners.size() < 1) {
            return false;
        }
        for (IBackListener listener : mBackListeners) {
            if (listener instanceof WillPopListener) {
                return true;
            }
        }
        return false;
    }

    public boolean isGestureRecognizerEnable() {
        return mEnableGestureRecognizer;
    }

    public void setGestureRecognizerEnable(boolean enableGestureRecognizer) {
        this.mEnableGestureRecognizer = enableGestureRecognizer;
    }

    public void setEnableBackgroundAnimation(boolean enable) {
        this.mEnableBackgroundColorAnimation = enable;
    }

    @Override
    public void onDraw(Canvas canvas) {
        drawShadow(canvas);
        super.onDraw(canvas);
    }

    private void drawShadow(Canvas canvas) {
        if (!mMaskEnable || getTransitionType() != TransitionType.InFromRight) {
            return;
        }

        View contentView = getContentView();
        if (contentView == null || contentView.getX() <= 0) {
            return;
        }

        if (mShadowPaint == null) {
            mShadowPaint = new Paint();
            mShadowPaint.setColor(Color.argb(0, 0, 0, 0));
            mShadowPaint.setStyle(Paint.Style.FILL);
            mShadowPaint.setAntiAlias(true);
        }

        float left = contentView.getX();
        float top = contentView.getTop();
        float right = left + 10;
        float bottom = contentView.getBottom();
        mShadowPaint.setShadowLayer(50, 0, 0, SHADOW_COLOR);
        RectF rectF = new RectF(left, top, right, bottom);
        canvas.drawRect(rectF, mShadowPaint);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (((ViewGroup) getParent()).getChildCount() > 1 && mEnableGestureRecognizer && getTransitionType() == TransitionType.InFromRight) {
            if (mScreenEdgePanGestureRecognizer == null) {
                View targetView = getContentView();
                if (targetView != null) {
                    mScreenEdgePanGestureRecognizer = new ScreenEdgePanGestureRecognizer(targetView);
                    mScreenEdgePanGestureRecognizer.setGestureRecognizerListener(this);
                }
            }

            if (mScreenEdgePanGestureRecognizer != null && mScreenEdgePanGestureRecognizer.onTouchEvent(ev)) {
                return true;
            }
        }
        return super.dispatchTouchEvent(ev);
    }

    @Override
    public void onStartEdgeDragging(View targetView) {
        if (mMaskEnable) {
            super.setBackgroundColor(MASK_COLOR);
        }
    }

    @Override
    public void onEdgeDragging(View targetView, float dx) {
        View parentView = (View) targetView.getParent();
        if (parentView == null) {
            return;
        }

        int x = (int) (targetView.getLeft() + dx);
        if (x < 0 || x > parentView.getWidth()) {
            return;
        }

        final int left = targetView.getLeft() + (int) dx;
        final int top = targetView.getTop();
        final int right = targetView.getRight() + (int) dx;
        final int bottom = targetView.getBottom();
        targetView.layout(left, top, right, bottom);

        if (mMaskEnable) {
            int backgroundColor = computeCurrentBackgroundColor();
            super.setBackgroundColor(backgroundColor);
        }
    }

    void checkIfNeedEntry() {
        if (mNavigator == null || getParent() == null) {
            return;
        }

        if (mNavigator.currentPageItem() != this) {
            performEntryAnimation();
        }
    }

    @Override
    public void onStopEdgeDragging(View targetView, float velocity, boolean cancelled) {
        View parentView = (View) targetView.getParent();
        if (parentView == null) {
            return;
        }
        if (targetView.getX() > (float) parentView.getWidth() / 2.5 || velocity > 1000.0f) {
            if (mBackPressedListener != null) {
                boolean handle = mBackPressedListener.onBackPressed(true);
                if (handle && willPop()) {
                    // 插件内吃掉了返回事件，可能会有弹框出现，此时需要回弹回去，直接弹回去可能因为前进后退动画冲突导致闪烁
                    this.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            checkIfNeedEntry();
                        }
                    }, 100);
                }
            } else {
                pop(true, null);
            }
        } else {
            performEntryAnimation();
        }
    }
}