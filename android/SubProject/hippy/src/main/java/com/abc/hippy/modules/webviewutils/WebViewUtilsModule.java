package com.abc.hippy.modules.webviewutils;

import android.os.Build;
import android.webkit.CookieManager;
import android.webkit.ValueCallback;
import android.webkit.WebView;

import com.abc.common.utils.ContextHolder;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;


@HippyNativeModule(name = WebViewUtilsModule.CLASSNAME)
public class WebViewUtilsModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "WebViewUtils";

    public WebViewUtilsModule(HippyEngineContext context) {
        super(context);
    }

    @HippyMethod(name = "clearCookies")
    public void clearCookies(HippyMap params, final Promise promise) {
        CookieManager cookieManager = CookieManager.getInstance();
        final boolean hasCookies = cookieManager.hasCookies();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            cookieManager.removeAllCookies(
                    new ValueCallback<Boolean>() {
                        @Override
                        public void onReceiveValue(Boolean value) {
                            promise.resolve(hasCookies);
                        }
                    });
        } else {
            cookieManager.removeAllCookie();
            promise.resolve(hasCookies);
        }
    }

    @HippyMethod(name = "setCookie")
    public static void setCookie(HippyMap params, final Promise promise) {
        CookieManager cookieManager = CookieManager.getInstance();
        String url = (String) params.get("url");
        String cookie = (String) params.get("cookie");

        cookieManager.setCookie(url, cookie);
        promise.resolve(true);
    }

    @HippyMethod(name = "getCookie")
    public static void getCookie(final HippyMap params, Promise promise) {
        CookieManager cookieManager = CookieManager.getInstance();
        String url = params.getString("url");
        String cookie = cookieManager.getCookie(url);
        promise.resolve(cookie);
    }

    @HippyMethod(name = "getDefaultUserAgent")
    public static void getDefaultUserAgent(final HippyMap params, final Promise promise) {
        ContextHolder.uiHandler.post(new Runnable() {
            @Override
            public void run() {
                WebView view = new WebView(ContextHolder.getAppContext());
                String userAgentString = view.getSettings().getUserAgentString();
                promise.resolve(userAgentString);
            }
        });
    }
}
