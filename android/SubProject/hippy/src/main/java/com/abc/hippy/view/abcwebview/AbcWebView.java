package com.abc.hippy.view.abcwebview;

import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.FrameLayout;

import com.abc.common.utils.ContextHolder;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.uimanager.HippyViewBase;
import com.tencent.mtt.hippy.uimanager.HippyViewEvent;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;
import com.tencent.mtt.hippy.utils.UrlUtils;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

public class AbcWebView extends FrameLayout implements HippyViewBase {
	protected AbcWebViewInner mWebView;
	private HippyViewEvent mEventOnMessage = null;

	private String mReferer = null;

	public AbcWebView(Context context)
	{
		super(context);
		mWebView = new AbcWebViewInner(context);
		addView(mWebView);
		initWebView();
	}

	private void initWebView()
	{
		mWebView.setWebViewClient(new WebViewClient() {
			HippyViewEvent mEventOnError = new HippyViewEvent("onError");
			HippyViewEvent mEventonLoad = new HippyViewEvent("onLoad");
			HippyViewEvent mEventonLoadEnd = new HippyViewEvent("onLoadEnd");
			HippyViewEvent mEventonLoadStart = new HippyViewEvent("onLoadStart");
			String mMessageUrlPre = "hippy://postMessage?data=";

			Activity activity;
			private Activity getActivity() {
				return ContextHolder.getCurrentActivityWithDefault(activity);
			}

			@Override
			public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
				HippyMap event = new HippyMap();
				if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
					event.pushString("error", (String) error.getDescription());
					event.pushInt("errorCode", error.getErrorCode());
				}
				else {
					event.pushString("error", "unknown error");
					event.pushInt("errorCode", Integer.MAX_VALUE);
				}
				mEventOnError.send(AbcWebView.this, event);
				super.onReceivedError(view, request, error);
			}

			@Override
			public boolean shouldOverrideUrlLoading(WebView view, String url) {
				if (url != null)
				{
					if (url.startsWith(mMessageUrlPre)) {
						postMessage(URLDecoder.decode(url.substring(mMessageUrlPre.length())));
						return true;
					}
					else if (UrlUtils.isWebUrl(url) || UrlUtils.isFileUrl(url)) {
						HashMap<String, String> lStringStringHashMap = new HashMap<>();
						if (!TextUtils.isEmpty(mReferer)) {
							// 设置referer-否则无法调起
							lStringStringHashMap.put("referer", mReferer);
							view.loadUrl(url, lStringStringHashMap);
						} else {
							view.loadUrl(url, lStringStringHashMap);
						}
						return true;
					} else {
						if(url.startsWith("weixin://")){
							Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
							this.getActivity().startActivity(intent);
							return true;
						}
					}
				}
				return super.shouldOverrideUrlLoading(view, url);
			}

			public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
				if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
					return shouldOverrideUrlLoading(view, request.getUrl().toString());
				}
				return super.shouldOverrideUrlLoading(view, request);
			}

			// Handle API 21+
			@TargetApi(Build.VERSION_CODES.LOLLIPOP)
			@Override
			public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
				///获取请求uir
				String url = request.getUrl().toString();
				///获取RequestHeader中的所有 key value
				Map<String, String> lRequestHeaders = request.getRequestHeaders();
				if (lRequestHeaders.containsKey("Referer")) {
					mReferer = lRequestHeaders.get("Referer");
				}
				return super.shouldInterceptRequest(view, request);
			}

			@Override
			public void onPageFinished(WebView view, String url) {
				// android 4.2以下，使用注入js的方式来实现hippy.onMessage。4.2及以上使用addJavaScriptInterface实现
				if (Build.VERSION.SDK_INT < 17)
					view.loadUrl("javascript:hippy={};hippy.onMessage=function(data){location.href='hippy://postMessage?data='+encodeURIComponent(data)}");
				HippyMap event = new HippyMap();
				event.pushString("url", url);
				mEventonLoad.send(AbcWebView.this, event);
				mEventonLoadEnd.send(AbcWebView.this, event);
				super.onPageFinished(view, url);
			}

			@Override
			public void onPageStarted(WebView view, String url, Bitmap favicon) {
				HippyMap event = new HippyMap();
				event.pushString("url", url);
				mEventonLoadStart.send(AbcWebView.this, event);
				super.onPageStarted(view, url, favicon);
			}
		});
		mWebView.setWebChromeClient(new WebChromeClient());
		// 避免安全隐患
		if (Build.VERSION.SDK_INT >= 17) {
			// 为了让网页端可以通过：hippy.postMessage("hello");的方式发送数据给hippy前端
			mWebView.addJavascriptInterface(new AbcWebViewBridge(this), "hippy");
		}
		if (Build.VERSION.SDK_INT < 19)
		{
			mWebView.removeJavascriptInterface("searchBoxJavaBridge_");
			mWebView.removeJavascriptInterface("accessibility");
			mWebView.removeJavascriptInterface("accessibilityTraversal");
		}
	}

	public void postMessage(String msg) {
		if (mEventOnMessage == null)
			mEventOnMessage = new HippyViewEvent("onMessage");

		HippyMap event = new HippyMap();
		event.pushString("data", msg);
		mEventOnMessage.send(this, event);
	}

	@Override
	public NativeGestureDispatcher getGestureDispatcher() {
		return null;
	}

	@Override
	public void setGestureDispatcher(NativeGestureDispatcher dispatcher) {
	}
}
