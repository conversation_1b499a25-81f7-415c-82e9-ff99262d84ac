/* Ten<PERSON> is pleased to support the open source community by making Hippy available.
 * Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.abc.hippy.modules.abcnetwork;

import android.text.TextUtils;

import com.tencent.mtt.hippy.adapter.http.HippyHttpRequest;
import com.tencent.mtt.hippy.adapter.http.HippyHttpResponse;
import com.tencent.mtt.hippy.utils.LogUtils;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * SSE (Server-Sent Events) 适配器
 * 负责建立和管理 SSE 连接，解析 SSE 事件流
 */
public class SseAdapter {
    private static final String TAG = "SseAdapter";

    public interface SseCallback {
        void onConnected(HippyHttpRequest request, HippyHttpResponse response);
        void onEvent(SseEvent event);
        void onError(HippyHttpRequest request, Throwable error);
        void onDisconnected();
    }

    private ExecutorService mExecutorService;

    private static URL toURL(String url) throws MalformedURLException {
        URL _URL = new URL(url);

        // 有个别 URL 在 path 和 querystring 之间缺少 / 符号，需补上
        if (_URL.getPath() == null || "".equals(_URL.getPath())) {
            if (_URL.getFile() != null && _URL.getFile().startsWith("?")) {
                // 补斜杠符号
                int idx = url.indexOf('?');
                if (idx != -1) {
                    StringBuilder sb = new StringBuilder();
                    sb.append(url.substring(0, idx));
                    sb.append('/');
                    sb.append(url.substring(idx));

                    _URL = new URL(sb.toString());
                }
            }

            // 分支走到这里，没有path也没有file，证明为一个没有/的host
            if (_URL.getFile() == null || "".equals(_URL.getFile())) {
                StringBuilder sb = new StringBuilder();
                sb.append(url);
                sb.append("/");

                _URL = new URL(sb.toString());
            }
        }
        return _URL;
    }

    private void execute(Runnable runnable) {
        if (mExecutorService == null) {
            mExecutorService = Executors.newFixedThreadPool(3);
        }
        mExecutorService.execute(runnable);
    }

    public void connectSSE(final HippyHttpRequest request, final SseCallback callback) {
        execute(new Runnable() {
            @Override
            public void run() {
                if (callback == null) {
                    return;
                }
                
                HttpURLConnection connection = null;
                BufferedReader reader = null;
                
                try {
                    connection = createConnection(request);
                    fillHeader(connection, request);
                    fillPostBody(connection, request);

                    // 建立连接
                    connection.connect();
                    
                    // 创建响应对象
                    HippyHttpResponse response = createResponse(connection);
                    
                    // 通知连接成功
                    callback.onConnected(request, response);
                    
                    // 开始读取 SSE 事件流
                    InputStream inputStream = connection.getInputStream();
                    reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
                    
                    String line;
                    SseEvent.Builder eventBuilder = new SseEvent.Builder();
                    
                    while ((line = reader.readLine()) != null) {
                        if (line.isEmpty()) {
                            // 空行表示事件结束，发送事件
                            SseEvent event = eventBuilder.build();
                            if (event.hasData()) {
                                callback.onEvent(event);
                            }
                            eventBuilder = new SseEvent.Builder();
                        } else {
                            // 解析 SSE 字段
                            parseSseLine(line, eventBuilder);
                        }
                    }
                    
                } catch (Throwable e) {
                    LogUtils.e(TAG, "SSE connection error: " + e.getMessage());
                    callback.onError(request, e);
                } finally {
                    try {
                        if (reader != null) {
                            reader.close();
                        }
                    } catch (IOException e) {
                        LogUtils.e(TAG, "Error closing reader: " + e.getMessage());
                    }
                    
                    if (connection != null) {
                        connection.disconnect();
                    }
                    
                    callback.onDisconnected();
                }
            }
        });
    }

    /**
     * 解析 SSE 行数据
     */
    private void parseSseLine(String line, SseEvent.Builder eventBuilder) {
        if (line.startsWith("data:")) {
            String data = line.substring(5);
            if (data.startsWith(" ")) {
                data = data.substring(1);
            }
            eventBuilder.appendData(data);
        } else if (line.startsWith("event:")) {
            String event = line.substring(6);
            if (event.startsWith(" ")) {
                event = event.substring(1);
            }
            eventBuilder.setType(event);
        } else if (line.startsWith("id:")) {
            String id = line.substring(3);
            if (id.startsWith(" ")) {
                id = id.substring(1);
            }
            eventBuilder.setId(id);
        } else if (line.startsWith("retry:")) {
            String retryStr = line.substring(6);
            if (retryStr.startsWith(" ")) {
                retryStr = retryStr.substring(1);
            }
            try {
                int retry = Integer.parseInt(retryStr);
                eventBuilder.setRetry(retry);
            } catch (NumberFormatException e) {
                LogUtils.w(TAG, "Invalid retry value: " + retryStr);
            }
        } else if (line.startsWith(":")) {
            // 注释行，忽略
        } else {
            // 其他格式的行，可能是自定义字段
            int colonIndex = line.indexOf(':');
            if (colonIndex > 0) {
                String field = line.substring(0, colonIndex);
                String value = line.substring(colonIndex + 1);
                if (value.startsWith(" ")) {
                    value = value.substring(1);
                }
                LogUtils.d(TAG, "Custom SSE field: " + field + " = " + value);
            }
        }
    }

    HttpURLConnection createConnection(HippyHttpRequest request) throws Exception {
        if (TextUtils.isEmpty(request.getUrl())) {
            throw new RuntimeException("url is null");
        }
        URL url = toURL(request.getUrl());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        if (TextUtils.isEmpty(request.getMethod())) {
            request.setMethod("GET");
        }
        connection.setRequestMethod(request.getMethod());
        connection.setUseCaches(request.isUseCaches());
        connection.setInstanceFollowRedirects(request.isInstanceFollowRedirects());

        connection.setConnectTimeout(request.getConnectTimeout());
        connection.setReadTimeout(request.getReadTimeout());

        if (request.getMethod().equalsIgnoreCase("POST") || request.getMethod().equalsIgnoreCase("PUT")
                || request.getMethod().equalsIgnoreCase("PATCH")) {
            connection.setDoOutput(true);
        }

        return connection;
    }

    void fillHeader(URLConnection urlConnection, HippyHttpRequest request) {
        Map<String, Object> headerMap = request.getHeaders();
        if (headerMap != null && !headerMap.isEmpty()) {
            Set<String> keySets = headerMap.keySet();
            for (String key : keySets) {
                Object obj = headerMap.get(key);
                if (obj instanceof String) {
                    urlConnection.setRequestProperty(key, (String) obj);
                } else if (obj instanceof List) {
                    List<String> requestProperties = (List<String>) obj;
                    if (requestProperties != null && !requestProperties.isEmpty()) {
                        for (String oneReqProp : requestProperties) {
                            if (!TextUtils.isEmpty(oneReqProp)) {
                                urlConnection.addRequestProperty(key, oneReqProp);
                            }
                        }
                    }
                }
            }
        }
    }

    HippyHttpResponse createResponse(HttpURLConnection urlConnection) throws Exception {
        HippyHttpResponse response = new HippyHttpResponse();
        parseResponseHeaders(urlConnection, response);
        return response;
    }

    void parseResponseHeaders(HttpURLConnection httpConn, HippyHttpResponse response) throws Exception {
        if (httpConn == null)
            return;

        response.setStatusCode(httpConn.getResponseCode());
        response.setRspHeaderMap(httpConn.getHeaderFields());
        response.setResponseMessage(httpConn.getResponseMessage());
    }

    void fillPostBody(HttpURLConnection connection, HippyHttpRequest request) throws IOException {
        if (TextUtils.isEmpty(request.getBody())) {
            return;
        }
        connection.setRequestProperty("Content-Length", request.getBody().getBytes().length + "");
        DataOutputStream out = new DataOutputStream(connection.getOutputStream());
        out.write(request.getBody().getBytes());
        out.flush();
        out.close();
    }

    public void destroyIfNeed() {
        if (mExecutorService != null && !mExecutorService.isShutdown()) {
            mExecutorService.shutdown();
            mExecutorService = null;
        }
    }
}
