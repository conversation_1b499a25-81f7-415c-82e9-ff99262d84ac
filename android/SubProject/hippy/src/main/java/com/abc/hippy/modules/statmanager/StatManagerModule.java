package com.abc.hippy.modules.statmanager;

import com.abc.hippy.utils.HippyUtils;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

/**
 * StatManagerModule
 */

@HippyNativeModule(name = StatManagerModule.CLASSNAME)
public class StatManagerModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "StatManager";

    public StatManagerModule(HippyEngineContext context) {
        super(context);
    }

    @HippyMethod(name = "init")
    public void init(HippyMap params, Promise promise) {
        StatManager.getInstance().init(params, promise);

    }


    @HippyMethod(name = "beginPageView")
    public void beginPageView(HippyMap params, Promise promise) {
        StatManager.getInstance().beginPageView(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "endPageView")
    public void endPageView(HippyMap params, Promise promise) {
        StatManager.getInstance().endPageView(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "logPageView")
    public void logPageView(HippyMap params, Promise promise) {
        StatManager.getInstance().logPageView(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "event")
    public void event(HippyMap params, Promise promise) {
        StatManager.getInstance().event(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }
}
