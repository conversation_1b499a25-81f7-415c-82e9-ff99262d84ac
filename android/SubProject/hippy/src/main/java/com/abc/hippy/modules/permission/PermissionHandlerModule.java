package com.abc.hippy.modules.permission;

import android.content.Context;

import com.abc.common.utils.ContextHolder;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Platform implementation of the permission_handler Flutter plugin.
 *
 * <p>Instantiate this in an add to app scenario to gracefully handle activity and context changes.
 * See {@code com.example.permissionhandlerexample.MainActivity} for an example.
 * <p>
 * stable {@code io.flutter.plugin.common} package.
 */
@HippyNativeModule(name = PermissionHandlerModule.CLASSNAME)
public class PermissionHandlerModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "PermissionHandler";


    private final Context applicationContext;
    private final AppSettingsManager appSettingsManager;
    private final PermissionManager permissionManager;
//    private final ServiceManager serviceManager;


    public PermissionHandlerModule(HippyEngineContext context) {
        super(context);
        this.applicationContext = ContextHolder.getAppContext();
        this.appSettingsManager = new AppSettingsManager();
        this.permissionManager = new PermissionManager();
//        this.serviceManager = new ServiceManager();
    }


    @Override
    public void destroy() {
        super.destroy();
        this.permissionManager.destroy();
    }

    @HippyMethod(name = "checkPermissionStatus")
    public void checkPermissionStatus(HippyMap params, final Promise promise) {
        @PermissionConstants.PermissionGroup final int permission = params.getInt("permissionType");
        permissionManager.checkPermissionStatus(
                permission,
                applicationContext,
                ContextHolder.getCurrentActivity(),
                new PermissionManager.CheckPermissionsSuccessCallback() {

                    @Override
                    public void onSuccess(int permissionStatus) {
                        promise.resolve(permissionStatus);
                    }
                },

                new ErrorCallback() {
                    @Override
                    public void onError(String errorCode, String errorDescription) {
                        promise.reject(errorDescription);
                    }
                });
    }

    @HippyMethod(name = "requestPermission")
    public void requestPermission(HippyMap params, final Promise promise) {
        final int permissionType = params.getInt("permissionType");
        final List<Integer> permissions = Arrays.asList(permissionType);
        permissionManager.requestPermissions(
                permissions,
                ContextHolder.getCurrentActivity(),
                new PermissionManager.RequestPermissionsSuccessCallback() {

                    @Override
                    public void onSuccess(Map<Integer, Integer> results) {
                        if (results.get(permissionType) != null) {
                            promise.resolve(results.get(permissionType));
                        }
                    }
                },
                new ErrorCallback() {
                    @Override
                    public void onError(String errorCode, String errorDescription) {
                        promise.reject(errorDescription);
                    }
                });

    }

    @HippyMethod(name = "openAppSettings")
    public void openAppSettings(HippyMap params, final Promise promise) {
        appSettingsManager.openAppSettings(
                applicationContext,
                new AppSettingsManager.OpenAppSettingsSuccessCallback() {
                    @Override
                    public void onSuccess(boolean appSettingsOpenedSuccessfully) {
                        promise.resolve(appSettingsOpenedSuccessfully);
                    }
                },
                new ErrorCallback() {
                    @Override
                    public void onError(String errorCode, String errorDescription) {
                        promise.reject(errorDescription);
                    }
                });
    }

    @HippyMethod(name = "openAppNotificationSetting")
    public void openAppNotificationSetting(HippyMap params, final Promise promise) {
        appSettingsManager.openAppNotificationSetting(
                applicationContext,
                new AppSettingsManager.OpenAppSettingsSuccessCallback() {
                    @Override
                    public void onSuccess(boolean appSettingsOpenedSuccessfully) {
                        promise.resolve(appSettingsOpenedSuccessfully);
                    }
                },
                new ErrorCallback() {
                    @Override
                    public void onError(String errorCode, String errorDescription) {
                        promise.reject(errorDescription);
                    }
                });
    }
}
