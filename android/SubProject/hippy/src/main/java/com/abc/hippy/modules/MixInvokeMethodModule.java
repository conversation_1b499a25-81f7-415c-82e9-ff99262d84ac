package com.abc.hippy.modules;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import androidx.core.content.FileProvider;

import com.abc.common.utils.AppInfo;
import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.FileUtils;
import com.abc.common.utils.ImageUtils;
import com.abc.common.utils.Md5Utils;
import com.abc.common.utils.Size;
import com.abc.common.utils.ZipUtils;
import com.abc.hippy.utils.ArgumentUtils;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import java.io.File;


@HippyNativeModule(name = MixInvokeMethodModule.CLASSNAME)
public class MixInvokeMethodModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "MixInvokeMethod";

    public MixInvokeMethodModule(HippyEngineContext context) {
        super(context);
    }

    @HippyMethod(name = "invokeMethod")
    public void invokeMethod(String methodName, HippyMap params, Promise promise) {
        switch (methodName) {
            case "md5WithFile":
                this.md5WithFile(params, promise);
                break;
            case "install":
                this.install(params, promise);
                break;
            case "getAssetAsString":
                this.getAssetAsString(params, promise);
                break;
            case "unzip":
                this.unzip(params, promise);
                break;

            case "getImageSize":
                this.getImageSize(params, promise);
                break;
            case "reload":
                this.reload(params, promise);
                break;
            case "setHippyDebug":
                this.setHippyDebug(params, promise);
                break;

            case "setLogDebugLevel":
                this.setLogDebugLevel(params, promise);
                break;

            case "exitApp":
                this.exitApp();
                break;
            case "setGrayFlag":
                this.setGrayFlag(params, promise);
                break;

            case "hippyFirstFrameReady":
                this.hippyFirstFrameReady(params, promise);
                break;

            default:
                break;
        }
    }

    private void getImageSize(HippyMap params, Promise promise) {
        String path = params.getString("path");
        if (TextUtils.isEmpty(path)) {
            promise.reject("缺少参数 path");
            return;
        }

        Size size = ImageUtils.getBitmapSize(path);

        HippyMap map = new HippyMap();
        map.pushInt("width", size.width);
        map.pushInt("height", size.height);

        promise.resolve(map);
    }

    private void md5WithFile(HippyMap params, final Promise promise) {
        final String file = params.getString("file");
        if (TextUtils.isEmpty(file)) {
            promise.reject("缺少参数 file");
            return;
        }

        try {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    String md5 = Md5Utils.getMD5(new File(file));
                    promise.resolve(md5);
                }
            }).start();

        } catch (Exception e) {
            promise.reject(e.getMessage());
        }
    }

    private void install(HippyMap params, Promise promise) {
        String file = params.getString("file");
        if (TextUtils.isEmpty(file)) {
            promise.reject("缺少参数 file");
            return;
        }

        File apkFile = new File(file);
        Intent intent;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            intent = new Intent(Intent.ACTION_INSTALL_PACKAGE);
            intent = new Intent(Intent.ACTION_VIEW);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            Uri contentUri = FileProvider.getUriForFile(ContextHolder.getAppContext(), AppInfo.getPackageName() + ".FileProvider", apkFile);
            intent.setDataAndType(contentUri, "application/vnd.android.package-archive");
        } else {
            intent = new Intent(Intent.ACTION_VIEW);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setDataAndType(Uri.fromFile(apkFile), "application/vnd.android.package-archive");
        }

        if (ContextHolder.getCurrentActivity() != null)
            ContextHolder.getCurrentActivity().startActivity(intent);

        promise.resolve(null);
    }


    private void getAssetAsString(HippyMap params, Promise promise) {
        String asset = params.getString("asset");
        if (TextUtils.isEmpty(asset)) {
            promise.reject("缺少参数 asset");
            return;
        }

        promise.resolve(FileUtils.readAssetFileAsString(asset));
    }


    private void unzip(HippyMap params, final Promise promise) {
        final String zipFile = params.getString("zipFile");
        final String outDir = params.getString("outDir");
        if (TextUtils.isEmpty(zipFile)) {
            promise.reject("缺少参数 zipFile");
            return;
        }

        if (TextUtils.isEmpty(outDir)) {
            promise.reject("缺少参数 outDir");
            return;
        }

        try {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    boolean ret = ZipUtils.unzip(new File(zipFile), new File(outDir), null);
                    promise.resolve(ret);
                }
            }).start();

        } catch (Exception e) {
            promise.reject(e.getMessage());
        }
    }

    private void reload(HippyMap params, Promise promise) {
        HostHippyMessageBridge.getInstance().onHippyRequestInvokeHostMethod("reload", null);
        promise.resolve(true);
    }

    private void setHippyDebug(HippyMap params, Promise promise) {
        HostHippyMessageBridge.getInstance().onHippyRequestInvokeHostMethod("setHippyDebug", ArgumentUtils.mapFromHippyMap(params));
        promise.resolve(true);
    }

    private void setLogDebugLevel(HippyMap params, Promise promise) {
        HostHippyMessageBridge.getInstance().onHippyRequestInvokeHostMethod("setLogDebugLevel", ArgumentUtils.mapFromHippyMap(params));
        promise.resolve(true);
    }


    private void exitApp() {
        System.exit(0);
    }

    private void setGrayFlag(HippyMap params, Promise promise) {
        HostHippyMessageBridge.getInstance().onHippyRequestInvokeHostMethod("setGrayFlag", ArgumentUtils.mapFromHippyMap(params));
        promise.resolve(true);
    }


    private void hippyFirstFrameReady(HippyMap params, Promise promise) {
        HostHippyMessageBridge.getInstance().onHippyRequestInvokeHostMethod("hippyFirstFrameReady", ArgumentUtils.mapFromHippyMap(params));
        promise.resolve(true);
    }
}
