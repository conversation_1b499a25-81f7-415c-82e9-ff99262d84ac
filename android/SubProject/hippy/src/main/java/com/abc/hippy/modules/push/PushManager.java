package com.abc.hippy.modules.push;

import android.app.Activity;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import com.abc.common.utils.ActivityEventDispatcher;
import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.ICommonCallback;
import com.abc.common.utils.LogUtils;
import com.abc.common.utils.bugly.BuglyManager;
import com.abc.hippy.modules.HostHippyMessageBridge;
import com.abc.hippy.utils.ArgumentUtils;
import com.tencent.mtt.hippy.common.HippyMap;

import java.util.Map;


/**
 * FakePushPlugin
 */
public class PushManager implements ActivityEventDispatcher.ActivityResultListener {
    final static String CLASSNAME = "Push";
    private static final String TAG = "PushManager";


    private static PushManager sInstance;
    private boolean mHasInit;

    public static synchronized PushManager getInstance() {
        if (sInstance == null)
            sInstance = new PushManager();

        return sInstance;
    }

    private static final String METHOD_ARENOTIFICATIONSENABLED = "areNotificationsEnabled";
    private static final String METHOD_OPENNOTIFICATIONSSETTINGS = "openNotificationsSettings";
    private static final String METHOD_STARTWORK = "startWork";
    private static final String METHOD_STOPWORK = "stopWork";
    private static final String METHOD_GETDEVICETOKEN = "getDeviceToken";
    private static final String METHOD_GETPUSHCHANNELID = "getPushChannelId";
    private static final String METHOD_BINDACCOUNT = "bindAccount";
    private static final String METHOD_UNBINDACCOUNT = "unbindAccount";
    private static final String METHOD_BINDTAGS = "bindTags";
    private static final String METHOD_UNBINDTAGS = "unbindTags";

    private static final String METHOD_ONRECEIVEDEVICETOKEN = "onReceiveDeviceToken";
    private static final String METHOD_ONRECEIVEMESSAGE = "onReceiveMessage";
    private static final String METHOD_ONRECEIVENOTIFICATION = "onReceiveNotification";
    private static final String METHOD_ONLAUNCHNOTIFICATION = "onLaunchNotification";
    private static final String METHOD_ONRESUMENOTIFICATION = "onResumeNotification";

    private static final String ARGUMENT_KEY_ENABLEDEBUG = "enableDebug";
    private static final String ARGUMENT_KEY_ACCOUNT = "account";
    private static final String ARGUMENT_KEY_TAGS = "tags";

    public static final String ARGUMENT_KEY_RESULT_TITLE = "title";
    public static final String ARGUMENT_KEY_RESULT_CONTENT = "content";
    public static final String ARGUMENT_KEY_RESULT_CUSTOMCONTENT = "customContent";
    public static final String ARGUMENT_KEY_RESULT_URL = "url";

    private IPushManager mPushManager;

    private ActivityEventDispatcher.ActivityLifecycleListener mActivityLifecycleListener = new ActivityEventDispatcher.ActivityLifecycleListener() {
        @Override
        public boolean onNewIntent(Activity activity, Intent intent) {
            boolean res = mPushManager.handleNotificationClickedFromIntent(intent);
            if (res && ContextHolder.getMainActivity() != null) {
                ContextHolder.getMainActivity().setIntent(intent);
            }
            return res;
        }


        @Override
        public boolean onDestroy(Activity activity) {
            if (activity == ContextHolder.getMainActivity())
                mPushManager.onActivityDestroyed();
            return false;
        }

        @Override
        public void onPause(Activity activity) {

        }
    };


    public void setPushManager(IPushManager manager){
        this.mPushManager = manager;
        this.mPushManager.setListener(mPushManagerListener);
    }

    private final IPushManagerListener mPushManagerListener = new IPushManagerListener() {
        @Override
        public void onRegisterSuccess(String deviceToken) {
            Log.d(TAG, "onRegisterSuccess() called with: deviceToken = [" + deviceToken + "]");

            PushManager.this.invokeMethod(METHOD_ONRECEIVEDEVICETOKEN, deviceToken);
        }

        @Override
        public void onRegisterFailed(String error) {
            Log.d(TAG, "onRegisterFailed() called with: error = [" + error + "]");

        }

        @Override
        public void onLaunchNotification(String customContent) {
            Log.d(TAG, "onLaunchNotification() called with: customContent = [" + customContent + "]");
            PushManager.this.invokeMethod(METHOD_ONLAUNCHNOTIFICATION, customContent);
        }

        @Override
        public void onResumeNotification(String customContent) {
            Log.d(TAG, "onResumeNotification() called with: customContent = [" + customContent + "]");
            PushManager.this.invokeMethod(METHOD_ONRESUMENOTIFICATION, customContent);
        }

        @Override
        public void onReceiveMessage(Map<String, Object> map) {
            Log.d(TAG, "onReceiveMessage() called with: map = [" + map + "]");
            PushManager.this.invokeMethod(METHOD_ONRECEIVEMESSAGE, map != null ? ArgumentUtils.toHippyMap(map) : new HippyMap());
        }

        @Override
        public void onReceiveNotification(Map<String, Object> map) {
            Log.d(TAG, "onReceiveNotification() called with: map = [" + map + "]");
            PushManager.this.invokeMethod(METHOD_ONRECEIVENOTIFICATION, map != null ? ArgumentUtils.toHippyMap(map) : new HippyMap());
        }
    };

    private PushManager() {
        createNotificationChannel();
        ActivityEventDispatcher.instance.addActivityResultListener(this);
        ActivityEventDispatcher.instance.addActivityLifecycleListener(mActivityLifecycleListener);
    }


    private void openNotificationsSettings(HippyMap params, final ICommonCallback result) {
        Intent intent = new Intent();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
            intent.putExtra(Settings.EXTRA_APP_PACKAGE, ContextHolder.getAppContext().getPackageName());
//            intent.putExtra(Settings.EXTRA_CHANNEL_ID, registrar.context().getApplicationInfo().uid);
        } else {
            intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
            intent.putExtra("app_package", ContextHolder.getAppContext().getPackageName());
            intent.putExtra("app_uid", ContextHolder.getAppContext().getApplicationInfo().uid);
        }
        if (intent.resolveActivity(ContextHolder.getAppContext().getPackageManager()) != null) {
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.setData(Uri.fromParts("package", ContextHolder.getAppContext().getPackageName(), null));
        }
        if (ContextHolder.getCurrentActivity() != null)
            ContextHolder.getCurrentActivity().startActivity(intent);

        result.onCallback(null, null);
    }
    public void startWork(HippyMap params, final ICommonCallback result) {
        Log.d(TAG, "startWork() called with: params = [" + params + "], result = [" + result + "], intent = " + ContextHolder.getMainActivity().getIntent());
        if (this.mHasInit) return;
        this.mHasInit = true;
        boolean debugBool = params.getBoolean(ARGUMENT_KEY_ENABLEDEBUG);
        boolean enableDebug = debugBool;
        mPushManager.start(ContextHolder.getAppContext(), enableDebug, ContextHolder.getMainActivity().getIntent());
        result.onCallback(null, null);
    }

    public void getDeviceToken(HippyMap params, final ICommonCallback result) {
        result.onCallback(mPushManager.getDeviceToken(), null);
    }


    public void getPushChannelId(HippyMap params, ICommonCallback result) {
        result.onCallback(mPushManager.getPushChannelId(), null);
    }


    // --- ActivityResultListener
    @Override
    public boolean onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        return false;
    }

    private void createNotificationChannel() {
        // Create the NotificationChannel, but only on API 26+ because
        // the NotificationChannel class is new and not in the support library
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Log.i(TAG, "createNotificationChannel");
            String name = "默认通知";
            String description = "用于显示运营消息，系统通知";
            int importance = NotificationManager.IMPORTANCE_DEFAULT;
            NotificationChannel channel = new NotificationChannel(PushConfig.PUSH_CHANNEL_ID_DEFAULT, name, importance);
            channel.setDescription(description);
            // Register the channel with the system; you can't change the importance
            // or other notification behaviors after this
            NotificationManager notificationManager = ContextHolder.getAppContext().getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    void invokeMethod(String eventName, Object params) {
        try {
            HippyMap body = new HippyMap();
            body.pushString("methodName", eventName);
            body.pushObject("args", params);
            HostHippyMessageBridge.getInstance().onHostMessage("Push", body);
        } catch (Exception e) {
            try {
                BuglyManager.getInstance().postCatchedException(8, "PushManager.invokeMethod", "PushManager.invokeMethod params " + params.toString(), null, null, null);
            } catch (Throwable t) {

            }
        }
    }
}
