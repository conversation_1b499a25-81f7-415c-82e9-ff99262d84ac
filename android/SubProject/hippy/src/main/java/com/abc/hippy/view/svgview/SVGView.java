package com.abc.hippy.view.svgview;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.abc.common.utils.ContextHolder;
import com.tencent.mtt.hippy.uimanager.HippyViewBase;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;

/**
 * @version 1.1.0
 */
public class SVGView extends FrameLayout implements HippyViewBase {
    private static final String TAG = "SVGView";
    private NativeGestureDispatcher mGestureDispatcher;
    private String mContent;

    private final ImageView mImageView;


    public SVGView(Context context) {
        super(context);

        mImageView = new ImageView(context);
        this.addView(mImageView, new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
    }

    @Override
    public NativeGestureDispatcher getGestureDispatcher() {
        return mGestureDispatcher;
    }

    @Override
    public void setGestureDispatcher(NativeGestureDispatcher dispatcher) {
        this.mGestureDispatcher = dispatcher;
    }

    public void setContent(String content) {
        if (mContent != null && mContent.equals(content)) return;
        mContent = content;

        this.reloadImage();
    }

    private void reloadImage() {
        SVGManager.instance().getSVGDrawable(mContent, getWidth(), getHeight(), (drawable) -> ContextHolder.uiHandler.post(() -> mImageView.setBackgroundDrawable(drawable)));
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldWidth, int oldHeight) {
        super.onSizeChanged(w, h, oldWidth, oldHeight);
        this.reloadImage();
    }
}
