/* <PERSON><PERSON> is pleased to support the open source community by making Hippy available.
 * Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.abc.hippy.modules.abcnetwork;

import com.tencent.mtt.hippy.adapter.http.HippyHttpRequest;
import com.tencent.mtt.hippy.utils.LogUtils;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * SSE 连接管理类
 * 负责管理单个 SSE 连接的生命周期
 */
public class SseConnection {
    private static final String TAG = "SseConnection";
    
    private String connectionId;
    private HippyHttpRequest request;
    private AtomicBoolean isConnected = new AtomicBoolean(false);
    private AtomicBoolean isDisconnecting = new AtomicBoolean(false);
    private String lastEventId;
    private long createTime;
    private long connectTime;

    public SseConnection(String connectionId, HippyHttpRequest request) {
        this.connectionId = connectionId;
        this.request = request;
        this.createTime = System.currentTimeMillis();
    }

    public String getConnectionId() {
        return connectionId;
    }

    public HippyHttpRequest getRequest() {
        return request;
    }

    public boolean isConnected() {
        return isConnected.get();
    }

    public void setConnected(boolean connected) {
        this.isConnected.set(connected);
        if (connected) {
            this.connectTime = System.currentTimeMillis();
        }
    }

    public boolean isDisconnecting() {
        return isDisconnecting.get();
    }

    public String getLastEventId() {
        return lastEventId;
    }

    public void setLastEventId(String lastEventId) {
        this.lastEventId = lastEventId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public long getConnectTime() {
        return connectTime;
    }

    public long getConnectionDuration() {
        if (connectTime > 0) {
            return System.currentTimeMillis() - connectTime;
        }
        return 0;
    }

    /**
     * 断开连接
     */
    public void disconnect() {
        if (isDisconnecting.compareAndSet(false, true)) {
            LogUtils.d(TAG, "Disconnecting SSE connection: " + connectionId);
            isConnected.set(false);
            // 这里可以添加实际的连接断开逻辑
            // 由于我们使用的是 HttpURLConnection，连接会在读取线程结束时自动断开
        }
    }

    /**
     * 检查连接是否应该重连
     */
    public boolean shouldReconnect() {
        return !isDisconnecting.get() && !isConnected.get();
    }

    @Override
    public String toString() {
        return "SseConnection{" +
                "connectionId='" + connectionId + '\'' +
                ", url='" + (request != null ? request.getUrl() : "null") + '\'' +
                ", isConnected=" + isConnected.get() +
                ", isDisconnecting=" + isDisconnecting.get() +
                ", lastEventId='" + lastEventId + '\'' +
                ", connectionDuration=" + getConnectionDuration() + "ms" +
                '}';
    }
}
