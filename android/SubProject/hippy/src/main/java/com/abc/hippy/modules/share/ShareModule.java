package com.abc.hippy.modules.share;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.RequestPermissionResultDispatcher;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import java.io.File;
import java.util.ArrayList;
import java.util.List;


/**
 * Plugin method host for presenting a share sheet via Intent
 */


@HippyNativeModule(name = ShareModule.CLASSNAME)
public class ShareModule extends HippyNativeModuleBase implements RequestPermissionResultDispatcher.IListener {

    final static String CLASSNAME = "Share";

    /// the authorities for FileProvider
    private static final int CODE_ASK_PERMISSION = 100;
    private static final String CHANNEL = "com.zt.shareextend/share_extend";

    private List<String> list;
    private String type;
    private String sharePanelTitle;
    private String subject;


    public ShareModule(HippyEngineContext context) {
        super(context);
        RequestPermissionResultDispatcher.instance.addListener(this);

    }

    @HippyMethod(name = "share")
    public void share(HippyMap params, Promise promise) {
        // Android does not support showing the share sheet at a particular point on screen.
        HippyArray array = params.getArray("list");
        if (array != null) {
            ArrayList<String> arrayList = new ArrayList<>();
            for (int i = 0; i < array.size(); ++i) {
                arrayList.add(array.getString(i));
            }

            list = arrayList;
        }

        type = params.getString("type");
        sharePanelTitle = params.getString("sharePanelTitle");
        subject = params.getString("subject");
        share(list, type, sharePanelTitle, subject);
        promise.resolve(null);
    }

    private void share(List<String> list, String type, String sharePanelTitle, String subject) {
        ArrayList<Uri> uriList = new ArrayList<>();
        ;

        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("Non-empty list expected");
        }
        Intent shareIntent = new Intent();
        shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        shareIntent.putExtra(Intent.EXTRA_SUBJECT, subject);

        if ("text".equals(type)) {
            shareIntent.setAction(Intent.ACTION_SEND);
            shareIntent.putExtra(Intent.EXTRA_TEXT, list.get(0));
            shareIntent.setType("text/plain");
        } else {
            if (ShareUtils.shouldRequestPermission(list)) {
                if (!checkPermission()) {
                    requestPermission();
                    return;
                }
            }

            for (String path : list) {
                File f = new File(path);
                Uri uri = ShareUtils.getUriForFile(getActivity(), f);
                uriList.add(uri);
            }

            if ("image".equals(type)) {
                shareIntent.setType("image/*");
            } else if ("video".equals(type)) {
                shareIntent.setType("video/*");
            } else {
                shareIntent.setType("application/*");
            }
            if (uriList.size() == 1) {
                shareIntent.setAction(Intent.ACTION_SEND);
                shareIntent.putExtra(Intent.EXTRA_STREAM, uriList.get(0));
            } else {
                shareIntent.setAction(Intent.ACTION_SEND_MULTIPLE);
                shareIntent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, uriList);
            }
        }
        startChooserActivity(shareIntent, sharePanelTitle, uriList);
    }


    private void startChooserActivity(Intent shareIntent, String sharePanelTitle, ArrayList<Uri> uriList) {
        Intent chooserIntent = Intent.createChooser(shareIntent, sharePanelTitle);
        ShareUtils.grantUriPermission(ContextHolder.getCurrentActivity(), uriList, chooserIntent);

        if (getActivity() != null) {
            getActivity().startActivity(chooserIntent);
        }
    }

    private boolean checkPermission() {
        return ContextCompat.checkSelfPermission(getActivity(), Manifest.permission.WRITE_EXTERNAL_STORAGE)
                == PackageManager.PERMISSION_GRANTED;
    }

    private void requestPermission() {
        ActivityCompat.requestPermissions(getActivity(), new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, CODE_ASK_PERMISSION);
    }

    @Override
    public boolean onRequestPermissionsResult(int requestCode, String[] perms, int[] grantResults) {
        if (requestCode == CODE_ASK_PERMISSION && grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            share(list, type, sharePanelTitle, subject);
        }
        return false;
    }

    private Activity getActivity() {
        return ContextHolder.getCurrentActivityWithDefault(ContextHolder.getMainActivity());
    }
}
