package com.abc.hippy.view.audioview;


import android.content.Context;
import android.text.TextUtils;

import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.HippyInstanceContext;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.javascriptmodules.EventDispatcher;
import com.tencent.mtt.hippy.uimanager.HippyViewBase;
import com.tencent.mtt.hippy.views.view.HippyViewGroup;


public class ABCAudioView extends HippyViewGroup implements ABCAudioPlayManager.AudioManagerListener, HippyViewBase
{
    private ABCAudioPlayManager mAudioPlayerManager		= null;
    private int					mUniqPlayId				= 0;
    private boolean				mOnPlayStartCallBack	= true;
    private boolean				mOnPlayProgressCallBack	= true;
    private boolean				mOnPlayResumeCallBack	= true;
    private boolean				mOnPlayPauseCallBack	= true;
    private boolean				mOnPlayCompleteCallBack		= true;
    private boolean				mOnPlayErrorCallBack		= true;
    private boolean				mAutoPlay		= false;
    private String				mCurrentPlayAudio		= "";


    HippyEngineContext mHippyContext;



    public ABCAudioView(Context context)
    {
        super(context);
        mAudioPlayerManager = ABCAudioPlayManager.getInstance();
        mUniqPlayId = ABCAudioPlayManager.globalUiqPlayId();
        mHippyContext = ((HippyInstanceContext) context).getEngineContext();
    }



    //播放地址
    public boolean setAudioPlayUrl(String sAudioUrl)
    {
        mCurrentPlayAudio = sAudioUrl;
        return  mAudioPlayerManager.setAudioPlayUrl(mUniqPlayId, sAudioUrl, this); //TODO 这个view被mAudioPlayerManager持有了
    }
    //自动播放
    public boolean setAudioAutoPlay(boolean autoPlay)
    {
        mAutoPlay = autoPlay;//mAudioPlayerManager.setAudioPlayUrl(mUniqPlayId, sAudioUrl, this);
        if (mAutoPlay && !TextUtils.isEmpty(mCurrentPlayAudio))
            mAudioPlayerManager.playAudio(mUniqPlayId);
        return true;
    }
    //播放
    public boolean playAudio()
    {
        return mAudioPlayerManager.playAudio(mUniqPlayId);
    }
    //seekTo
    public boolean seekTo(int seekToPos)
    {
        return mAudioPlayerManager.seekTo(mUniqPlayId,seekToPos);
    }
    //pause
    public boolean pauseAudio()
    {
        return mAudioPlayerManager.pauseAudio(mUniqPlayId);
    }
    //TODO is this need
    public boolean stopAudio()
    {
        return mAudioPlayerManager.stopAudio(mUniqPlayId);
    }
    public void setPlaybackSpeed(float speed) {
        mAudioPlayerManager.setPlaybackSpeed(speed);
    }
    public boolean releaseAudio()
    {
        return mAudioPlayerManager.releaseAudio(mUniqPlayId);
    }
    //播放状态回调
    public void setOnPlayStart(boolean onPlayStartCallBack)
    {
        mOnPlayStartCallBack = onPlayStartCallBack;
    }

    public void setOnPlayPause(boolean onPlayPauseCallBack)
    {
        mOnPlayPauseCallBack = onPlayPauseCallBack;
    }

    public void setOnPlayResume(boolean onPlayResumeCallBack)
    {
        mOnPlayResumeCallBack = onPlayResumeCallBack;
    }

    public void setOnPlayProgress(boolean onPlayProgressCallBack)
    {
        mOnPlayProgressCallBack = onPlayProgressCallBack;
    }

    public void setOnPlayComplete(boolean onPlayCompleteCallBack)
    {
        mOnPlayCompleteCallBack = onPlayCompleteCallBack;
    }
    public void setOnPlayError(boolean onPlayErrorCallBack)
    {
        mOnPlayErrorCallBack = onPlayErrorCallBack;
    }



    @Override
    public void onPlayStart(String playAudioUrl)
    {
        if(mOnPlayStartCallBack)
        {
            HippyMap hippyMap = new HippyMap();
            hippyMap.pushString("currentSrc",playAudioUrl);
            hippyMap.pushInt("size", 0 ); //TODO not specific yet
            hippyMap.pushInt("current", mAudioPlayerManager.currentPlayAudioPosition() );
            hippyMap.pushInt("length",mAudioPlayerManager.currentPlayAudioDuration());

            mHippyContext.getModuleManager().getJavaScriptModule(EventDispatcher.class).receiveUIComponentEvent(getId(),
                    "onPlaying", hippyMap);
        }
    }

    @Override
    public void onPlayPause(String playAudioUrl)
    {
        if(mOnPlayPauseCallBack)
        {
            HippyMap hippyMap = new HippyMap();
            hippyMap.pushString("currentSrc",playAudioUrl);
            hippyMap.pushInt("size", 0 ); //TODO not specific yet
            hippyMap.pushInt("current", mAudioPlayerManager.currentPlayAudioPosition() );
            hippyMap.pushInt("length",mAudioPlayerManager.currentPlayAudioDuration());
            mHippyContext.getModuleManager().getJavaScriptModule(EventDispatcher.class).receiveUIComponentEvent(getId(),
                    "onPause", hippyMap);
        }
    }

    @Override
    public void onPlayResume(String playAudioUrl)
    {
        if(mOnPlayResumeCallBack)
        {
            HippyMap hippyMap = new HippyMap();
            hippyMap.pushString("currentSrc",playAudioUrl);
            hippyMap.pushInt("size", 0 ); //TODO not specific yet
            hippyMap.pushInt("current", mAudioPlayerManager.currentPlayAudioPosition() );
            hippyMap.pushInt("length",mAudioPlayerManager.currentPlayAudioDuration());
            mHippyContext.getModuleManager().getJavaScriptModule(EventDispatcher.class).receiveUIComponentEvent(getId(),
                    "onPlaying", hippyMap);
        }
    }

    @Override
    public void onPlayError(String playAudioUrl,int what, int extra)
    {
        if(mOnPlayErrorCallBack)
        {
            HippyMap hippyMap = new HippyMap();
            hippyMap.pushString("currentSrc",playAudioUrl);
            hippyMap.pushInt("what",what);
            hippyMap.pushInt("extra",extra);
            mHippyContext.getModuleManager().getJavaScriptModule(EventDispatcher.class).receiveUIComponentEvent(getId(),
                    "onError", hippyMap);
        }
    }

    @Override
    public void onPlayComplete(String playAudioUrl)
    {
        if(mOnPlayCompleteCallBack)
        {
            HippyMap hippyMap = new HippyMap();
            hippyMap.pushString("playAudioUrl",playAudioUrl);
            mHippyContext.getModuleManager().getJavaScriptModule(EventDispatcher.class).receiveUIComponentEvent(getId(),
                    "onEnded", hippyMap);
        }
    }

    @Override
    public void onPlayBuffering(String playAudioUrl)
    {

    }

    @Override
    public void onPlayProgress(String playAudioUrl,int currentPlayTimeMs, int audioPlayTotalTimeMs)
    {
        if(mOnPlayProgressCallBack)
        {
            HippyMap hippyMap = new HippyMap();
            hippyMap.pushInt("playTimeMs",currentPlayTimeMs);
            hippyMap.pushInt("currentTime", currentPlayTimeMs );
            hippyMap.pushInt("length",audioPlayTotalTimeMs);
            mHippyContext.getModuleManager().getJavaScriptModule(EventDispatcher.class).receiveUIComponentEvent(getId(),
                    "onTimeupdate", hippyMap);
        }
    }
}
