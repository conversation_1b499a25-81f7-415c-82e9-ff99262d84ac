package com.abc.hippy.modules.asr.model;

/**
 * ASR 状态类
 */
public class AsrState {
    public enum State {
        IDLE,           // 空闲
        CONNECTING,     // 连接中
        CONNECTED,      // 已连接
        READY,          // 准备就绪（配置已设置）
        RECORDING,      // 录音中
        RECOGNIZING,    // 识别中
        STOPPING,       // 停止中
        STOPPED,        // 已停止
        ERROR,          // 错误
        DISCONNECTED    // 已断开连接
    }
    
    private String sessionId;
    private State state;
    private String message;
    private long timestamp;
    private float volume;
    private float db;
    
    public AsrState() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public AsrState(String sessionId, State state) {
        this.sessionId = sessionId;
        this.state = state;
        this.timestamp = System.currentTimeMillis();
    }
    
    public AsrState(String sessionId, State state, String message) {
        this.sessionId = sessionId;
        this.state = state;
        this.message = message;
        this.timestamp = System.currentTimeMillis();
    }
    
    // Getters and Setters
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public State getState() {
        return state;
    }
    
    public void setState(State state) {
        this.state = state;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public float getVolume() {
        return volume;
    }
    
    public void setVolume(float volume) {
        this.volume = volume;
    }
    
    public float getDb() {
        return db;
    }
    
    public void setDb(float db) {
        this.db = db;
    }
    
    public boolean isError() {
        return state == State.ERROR;
    }
    
    public boolean isRecording() {
        return state == State.RECORDING || state == State.RECOGNIZING;
    }
    
    public boolean isConnected() {
        return state == State.CONNECTED || state == State.READY || state == State.RECORDING || state == State.RECOGNIZING;
    }
    
    public boolean isStopped() {
        return state == State.STOPPED || state == State.IDLE || state == State.DISCONNECTED;
    }
    
    @Override
    public String toString() {
        return "AsrState{" +
                "sessionId='" + sessionId + '\'' +
                ", state=" + state +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                ", volume=" + volume +
                ", db=" + db +
                '}';
    }
}
