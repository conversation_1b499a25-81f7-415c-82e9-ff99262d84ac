package com.abc.hippy.view.qrview;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.ColorInt;
import androidx.annotation.Nullable;

import com.abc.common.utils.ContextHolder;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.tencent.mtt.hippy.uimanager.HippyViewBase;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;

import java.util.Hashtable;

/**
 * 滚轮选择器
 * <p>
 * WheelPicker
 * <p>
 * New project structure
 * @version 1.1.0
 */
public class QRView extends FrameLayout implements HippyViewBase {
    private NativeGestureDispatcher mGestureDispatcher;
    private String mContent;
    private int mColor;

    private ImageView mImageView;

    private Runnable mCreateQRImageRunnable;

    public QRView(Context context) {
        super(context);

        mImageView = new ImageView(context);
        this.addView(mImageView, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));

        mCreateQRImageRunnable = new Runnable() {
            @Override
            public void run() {
                if (TextUtils.isEmpty(mContent)) return;
                Bitmap bitmap = QRView.createQRCodeBitmap(mContent, getWidth(), getHeight(), "UTF-8", "H", "0", mColor, Color.TRANSPARENT);

                mImageView.setBackgroundDrawable(new BitmapDrawable(ContextHolder.getAppContext().getResources(), bitmap));
            }
        };
    }

    @Override
    public NativeGestureDispatcher getGestureDispatcher() {
        return mGestureDispatcher;
    }

    @Override
    public void setGestureDispatcher(NativeGestureDispatcher dispatcher) {
        this.mGestureDispatcher = dispatcher;
    }

    public void setColor(int color) {
        if (mColor == color) return;
        mColor = color;

        this.reloadQRImage();
    }

    public void setContent(String content) {
        if (mContent != null && mContent.equals(content)) return;
        mContent = content;

        this.reloadQRImage();
    }

    private void reloadQRImage() {
        ContextHolder.uiHandler.removeCallbacks(mCreateQRImageRunnable);
        if (TextUtils.isEmpty(mContent)) return;

        ContextHolder.uiHandler.postDelayed(mCreateQRImageRunnable, 100);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldWidth, int oldHeight) {
        super.onSizeChanged(w, h, oldWidth, oldHeight);
        this.reloadQRImage();
    }

    public static Bitmap createQRCodeBitmap(String content, int width, int height,
                                            @Nullable String character_set, @Nullable String error_correction, @Nullable String margin,
                                            @ColorInt int color_black, @ColorInt int color_white) {

        /* 1.参数合法性判断 */
        if (TextUtils.isEmpty(content)) { // 字符串内容判空
            return null;
        }

        if (width < 0 || height < 0) { // 宽和高都需要>=0
            return null;
        }

        try {
            /* 2.设置二维码相关配置,生成BitMatrix(位矩阵)对象 */
            Hashtable<EncodeHintType, String> hints = new Hashtable<>();

            if (!TextUtils.isEmpty(character_set)) {
                hints.put(EncodeHintType.CHARACTER_SET, character_set); // 字符转码格式设置
            }

            if (!TextUtils.isEmpty(error_correction)) {
                hints.put(EncodeHintType.ERROR_CORRECTION, error_correction); // 容错级别设置
            }

            if (!TextUtils.isEmpty(margin)) {
                hints.put(EncodeHintType.MARGIN, margin); // 空白边距设置
            }
            BitMatrix bitMatrix = new QRCodeWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);

            /* 3.创建像素数组,并根据BitMatrix(位矩阵)对象为数组元素赋颜色值 */
            int[] pixels = new int[width * height];
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    if (bitMatrix.get(x, y)) {
                        pixels[y * width + x] = color_black; // 黑色色块像素设置
                    } else {
                        pixels[y * width + x] = color_white; // 白色色块像素设置
                    }
                }
            }

            /* 4.创建Bitmap对象,根据像素数组设置Bitmap每个像素点的颜色值,之后返回Bitmap对象 */
            Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            bitmap.setPixels(pixels, 0, width, 0, 0, width, height);
            return bitmap;
        } catch (WriterException e) {
            e.printStackTrace();
        }

        return null;
    }
}
