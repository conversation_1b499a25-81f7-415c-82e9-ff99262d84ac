package com.abc.hippy.modules.statmanager;

import android.app.Activity;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.util.Log;

import com.abc.common.utils.ActivityEventDispatcher;
import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.ICommonCallback;
import com.abc.hippy.utils.HippyUtils;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.umeng.analytics.MobclickAgent;
import com.umeng.commonsdk.UMConfigure;
import com.umeng.commonsdk.statistics.common.DeviceConfig;

import java.util.HashMap;

@HippyNativeModule(name = StatManager.CLASSNAME)
public class StatManager extends ActivityEventDispatcher.ActivityLifecycleListener {
    final static String CLASSNAME = "StatManager";
    private boolean mHasInit = false;

    private static StatManager sInstance;

    static public synchronized StatManager getInstance() {
        if (sInstance == null) {
            sInstance = new StatManager();
        }
        return sInstance;
    }


    private StatManager() {
        ActivityEventDispatcher.instance.addActivityLifecycleListener(this);
    }


    @Override
    public void onResume(Activity activity) {
        MobclickAgent.onResume(activity);
    }

    @Override
    public void onPause(Activity activity) {
        MobclickAgent.onPause(activity);
    }

    public void init(HippyMap params, Promise promise) {
        if (mHasInit) {
            promise.resolve(true);
            return;
        }
        mHasInit = true;

        this.initSetup(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    public void event(HippyMap params, Promise promise) {
        this.event(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }


    private static String getMetadata(Context context, String name) {
        try {
            ApplicationInfo appInfo = context.getPackageManager().getApplicationInfo(
                    context.getPackageName(), PackageManager.GET_META_DATA);
            if (appInfo.metaData != null) {
                return appInfo.metaData.getString(name);
            }
        } catch (PackageManager.NameNotFoundException e) {
        }

        return null;
    }


    private void initSetup(HippyMap params, ICommonCallback result) {
        String appKey = params.getString("key");
        String channel = params.getString("channel");
        boolean logEnable = params.getBoolean("logEnable");
        boolean encrypt = params.getBoolean("encrypt");
        boolean reportCrash = params.getBoolean("reportCrash");

//        Log.d("UM", "initSetup: " + appKey);
//    Log.d("UM", "channel: " +  getMetadata(activity, "INSTALL_CHANNEL"));

        UMConfigure.setLogEnabled(logEnable);
        UMConfigure.init(activity(), appKey, channel, UMConfigure.DEVICE_TYPE_PHONE,
                null);
        UMConfigure.setEncryptEnabled(encrypt);

        MobclickAgent.setScenarioType(activity(), MobclickAgent.EScenarioType.E_UM_NORMAL);

        MobclickAgent.setSessionContinueMillis(30000L);
        MobclickAgent.setCatchUncaughtExceptions(reportCrash);

        MobclickAgent.setPageCollectionMode(MobclickAgent.PageMode.MANUAL);


        MobclickAgent.onResume(activity());
        result.onCallback(true, null);
    }

    private Context activity() {
        return ContextHolder.getCurrentActivity();
    }

    public void beginPageView(HippyMap params, ICommonCallback result) {
        String name = (String) params.getString("name");
//        Log.d("UM", "beginPageView: " + name);
        MobclickAgent.onResume(activity());
        MobclickAgent.onPageStart(name);
        result.onCallback(null, null);
    }

    public void endPageView(HippyMap params, ICommonCallback result) {
        String name = params.getString("name");
//        Log.d("UM", "endPageView: " + name);
        MobclickAgent.onPageEnd(name);
        MobclickAgent.onPause(activity());
        result.onCallback(null, null);
    }

    public void logPageView(HippyMap params, ICommonCallback result) {
        // MobclickAgent.onProfileSignIn((String)params.get("name"));
        // Session间隔时长,单位是毫秒，默认Session间隔时间是30秒,一般情况下不用修改此值
//    Long seconds = Double.valueOf(params.get("seconds")).longValue();
//    MobclickAgent.setSessionContinueMillis(seconds);
        result.onCallback(true, null);
    }

    public void event(HippyMap params, ICommonCallback resultt) {
        String name = (String) params.getString("name");
        String label = (String) params.getString("label");

        HashMap<String, String> map = null;
        HippyMap hippyMap = params.getMap("map");
        if (hippyMap != null) {
            map = new HashMap<>();
            for (String key : hippyMap.keySet()) {
                map.put(key, hippyMap.getString(key));
            }
        }

        if (map != null) {
            MobclickAgent.onEvent(activity(), name, map);
        } else if (label != null) {
            MobclickAgent.onEvent(activity(), name, label);
        } else {
            MobclickAgent.onEvent(activity(), name);
        }
        resultt.onCallback(null, null);
    }

    public static String[] getTestDeviceInfo(Context context) {
        String[] deviceInfo = new String[2];
        try {
            if (context != null) {
                deviceInfo[0] = DeviceConfig.getDeviceIdForGeneral(context);
                deviceInfo[1] = DeviceConfig.getMac(context);
                Log.d("UM", deviceInfo[0]);
                Log.d("UM", deviceInfo[1]);
            }
        } catch (Exception e) {
        }
        return deviceInfo;
    }
}
