package com.abc.hippy.modules.permission;

import android.app.Activity;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.abc.common.utils.ContextHolder;

import java.util.List;


final class MethodCallHandlerImpl {
    private final Context applicationContext;
    private final AppSettingsManager appSettingsManager;
    private final PermissionManager permissionManager;
    private final ServiceManager serviceManager;

    MethodCallHandlerImpl(
            Context applicationContext,
            AppSettingsManager appSettingsManager,
            PermissionManager permissionManager,
            ServiceManager serviceManager) {
        this.applicationContext = applicationContext;
        this.appSettingsManager = appSettingsManager;
        this.permissionManager = permissionManager;
        this.serviceManager = serviceManager;
    }

    @Nullable
    private Activity mActivity;

    private Activity activity() {
        return ContextHolder.getCurrentActivityWithDefault(this.mActivity);
    }


    public void setActivity(@Nullable Activity activity) {
        this.mActivity = activity;
    }


}
