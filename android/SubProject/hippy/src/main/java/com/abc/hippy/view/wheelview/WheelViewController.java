package com.abc.hippy.view.wheelview;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.uimanager.HippyViewController;

import java.util.ArrayList;


@HippyController(name = "Wheel")
public class WheelViewController extends HippyViewController<WheelView> {
    @Override
    protected View createViewImpl(Context context) {
        return new WheelView(context);
    }

    @HippyControllerProps(name = "initialIndex", defaultNumber = -1, defaultType = HippyControllerProps.NUMBER)
    public void setInitialIndex(WheelView wheelView, int initialIndex) {
        wheelView.setInitialIndex(initialIndex);
    }


    @HippyControllerProps(name = "items", defaultType = HippyControllerProps.ARRAY, defaultString = "")
    public void setItems(WheelView wheelView, HippyArray items) {
        ArrayList<String> strItems = new ArrayList<>();
        for (int i = 0; i < items.size(); ++i) {
            strItems.add(items.getString(i));
        }

        wheelView.setData(strItems);
    }

}
