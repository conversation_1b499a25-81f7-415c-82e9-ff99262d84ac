/* Ten<PERSON> is pleased to support the open source community by making Hippy available.
 * Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.abc.hippy.modules.abcnetwork;

import android.text.TextUtils;
import android.webkit.CookieManager;
import android.webkit.CookieSyncManager;

import com.abc.hippy.modules.HostHippyMessageBridge;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.adapter.http.HippyHttpRequest;
import com.tencent.mtt.hippy.adapter.http.HippyHttpResponse;
import com.tencent.mtt.hippy.adapter.http.HttpHeader;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;
import com.tencent.mtt.hippy.utils.ContextHolder;
import com.tencent.mtt.hippy.utils.LogUtils;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by feihe
 */
@HippyNativeModule(name = "AbcNetwork")
public class AbcNetworkModule extends HippyNativeModuleBase {
    // 使用CookieManager之前，需要初始化CookieSyncManager，单例的
    private static CookieSyncManager mCookieSyncManager;

    private HttpAdapter mHttpAdapter;
    private SseAdapter mSseAdapter;
    private Map<String, SseConnection> mSseConnections = new HashMap<>();

    public AbcNetworkModule(HippyEngineContext context) {
        super(context);

        mHttpAdapter = new HttpAdapter();
        mSseAdapter = new SseAdapter();
    }

    private void hippyMapToRequestHeaders(HippyHttpRequest request, HippyMap map) {
        if (request == null || map == null) {
            return;
        }

        Set<String> keys = map.keySet();
        for (String oneKey : keys) {
            Object valueObj = map.get(oneKey);
            if (valueObj instanceof HippyArray) {
                HippyArray oneHeaderArray = (HippyArray) valueObj;
                List<String> headerValueArray = new ArrayList<>();
                for (int i = 0; i < oneHeaderArray.size(); i++) {
                    Object oneHeaderValue = oneHeaderArray.get(i);
                    if (oneHeaderValue instanceof Number) {
                        headerValueArray.add(oneHeaderValue + "");
                    } else if (oneHeaderValue instanceof Boolean) {
                        headerValueArray.add(oneHeaderValue + "");
                    } else if (oneHeaderValue instanceof String) {
                        headerValueArray.add((String) oneHeaderValue);
                    } else {
                        LogUtils.e("hippy_console", "Unsupported Request Header List Type");
                    }
                }

                if (!headerValueArray.isEmpty()) {
                    request.addHeader(oneKey, headerValueArray);
                }
            } else {
                LogUtils.e("hippy_console", "Unsupported Request Header Type, Header Field Should All be an Array!!!");
            }
        }
    }

    @HippyMethod(name = "downloadFile")
    public void fetch(final HippyMap request, final Promise promise) {
        if (request == null) {
            promise.reject("invalid request param");
            return;
        }

        String url = request.getString("url");
        final String method = request.getString("method");
        final String taskId = request.getString("taskId");
        final String filePath = request.getString("filePath");
        if (TextUtils.isEmpty(url) || TextUtils.isEmpty(method)) {
            promise.reject("no valid url for request");
            return;
        }

        if (TextUtils.isEmpty(taskId)) {
            promise.reject("no taskId");
            return;
        }

        if (TextUtils.isEmpty(filePath)) {
            promise.reject("no filePath");
            return;
        }

        HippyHttpRequest httpRequest = new HippyHttpRequest();
        httpRequest.setConnectTimeout(10 * 1000);
        httpRequest.setReadTimeout(10 * 1000);
        String redirect = request.getString("redirect");
        if (!TextUtils.isEmpty(redirect) && TextUtils.equals("follow", redirect)) {
            httpRequest.setInstanceFollowRedirects(true);
        } else {
            httpRequest.setInstanceFollowRedirects(false);
        }
        httpRequest.setUseCaches(false);
        httpRequest.setMethod(method);
        httpRequest.setUrl(url);
        HippyMap headers = request.getMap("headers");
        if (headers != null) {
            hippyMapToRequestHeaders(httpRequest, headers);
        }
        String body = request.getString("body");
        httpRequest.setBody(body);

        this.mHttpAdapter.sendRequest(httpRequest, new HttpTaskCallbackImpl(promise, taskId, filePath));
    }

    /**
     * 获取指定url下的所有cookie
     *
     * @param url     指定url，其实也就是指定作用域，如：http://3g.qq.com
     * @param promise 指定url下的所有cookie，如：eqid=deleted;bd_traffictrace=012146;BDSVRTM=418
     */
    @HippyMethod(name = "getCookie")
    public void getCookie(String url, Promise promise) {
        String cookie = getCookieManager().getCookie(url);
        promise.resolve(cookie);
    }

    /**
     * 设置指定url下的Cookie
     *
     * @param url      指定url，其实也就是指定作用域，如：http://3g.qq.com
     * @param keyValue cookie key-value键值对集合，多个以分号";"隔开，如：name=harryguo。或者：name=harryguo;gender:male
     * @param expires  默认为空，过期时间，格式与http协议头response里的Set-Cookie相同，如：Thu, 08-Jan-2020 00:00:00 GMT
     */
    @HippyMethod(name = "setCookie")
    public void setCookie(String url, String keyValue, String expires) {
        if (!TextUtils.isEmpty(url) && !TextUtils.isEmpty(keyValue)) {
            // 单个cookie
            if (!TextUtils.isEmpty(expires)) {
                keyValue = keyValue + ";expires=" + expires;
                getCookieManager().setCookie(url, keyValue);
            } else // 多个cookie
                saveCookie2Manager(url, keyValue);

            mCookieSyncManager.sync();
        }
    }

    private void saveCookie2Manager(String url, HippyArray cookies) {
        if (cookies != null) {
            CookieManager cookieManager = getCookieManager();
            for (int i = 0; i < cookies.size(); i++) {
                String cookieStr = (String) cookies.get(i);
                saveCookie2Manager(url, cookieStr);
            }
        }
    }

    private void saveCookie2Manager(String url, String cookies) {
        if (cookies != null) {
            cookies = cookies.replaceAll("\\s+", "");
            String[] cookieItems = cookies.split(";");
            CookieManager cookieManager = getCookieManager();
            for (String cookie : cookieItems)
                cookieManager.setCookie(url, cookie);
        }
    }

    private static CookieManager getCookieManager() {
        if (mCookieSyncManager == null) {
            mCookieSyncManager = CookieSyncManager.createInstance(ContextHolder.getAppContext());

            CookieManager cookieManager = CookieManager.getInstance();
            cookieManager.setAcceptCookie(true);
            cookieManager.removeSessionCookie();
        }
        return CookieManager.getInstance();
    }

    @HippyMethod(name = "connectSSE")
    public void connectSSE(final HippyMap request, final Promise promise) {
        if (request == null) {
            promise.reject("invalid request param");
            return;
        }

        String url = request.getString("url");
        final String method = request.getString("method");
        final String connectionId = request.getString("connectionId");
        if (TextUtils.isEmpty(url) || TextUtils.isEmpty(connectionId)) {
            promise.reject("no valid url or connectionId for SSE request");
            return;
        }

        // 检查是否已存在相同的连接
        if (mSseConnections.containsKey(connectionId)) {
            promise.reject("SSE connection with id " + connectionId + " already exists");
            return;
        }

        // 创建 SSE 请求
        HippyHttpRequest httpRequest = new HippyHttpRequest();
        httpRequest.setConnectTimeout(30 * 1000); // SSE 连接超时设置为30秒
        httpRequest.setReadTimeout(0); // SSE 需要长连接，不设置读取超时
        httpRequest.setInstanceFollowRedirects(true);
        httpRequest.setUseCaches(false);

        // 设置 HTTP 方法，如果未指定则默认使用 GET（保持向后兼容）
        if (TextUtils.isEmpty(method)) {
            httpRequest.setMethod("GET");
        } else {
            httpRequest.setMethod(method);
        }

        httpRequest.setUrl(url);

        // 设置 SSE 必需的请求头
        httpRequest.addHeader("Accept", "text/event-stream");
        httpRequest.addHeader("Cache-Control", "no-cache");

        // 处理自定义请求头
        HippyMap headers = request.getMap("headers");
        if (headers != null) {
            HippyMap potentialHeaders = headers.getMap("_headers");
            if (potentialHeaders != null) {
                headers = potentialHeaders;
            }
            hippyMapToRequestHeaders(httpRequest, headers);
        }

        // 处理请求体（用于 POST、PUT 等方法）
        String body = request.getString("body");
        if (!TextUtils.isEmpty(body)) {
            httpRequest.setBody(body);
        }

        // 创建 SSE 连接对象
        SseConnection sseConnection = new SseConnection(connectionId, httpRequest);
        mSseConnections.put(connectionId, sseConnection);

        // 发起 SSE 连接
        this.mSseAdapter.connectSSE(httpRequest, new SseCallbackImpl(promise, connectionId, sseConnection));
    }

    @HippyMethod(name = "disconnectSSE")
    public void disconnectSSE(final HippyMap request, final Promise promise) {
        if (request == null) {
            promise.reject("invalid request param");
            return;
        }

        String connectionId = request.getString("connectionId");
        if (TextUtils.isEmpty(connectionId)) {
            promise.reject("no connectionId for SSE disconnect");
            return;
        }

        SseConnection connection = mSseConnections.get(connectionId);
        if (connection == null) {
            promise.reject("SSE connection with id " + connectionId + " not found");
            return;
        }

        // 断开连接
        connection.disconnect();
        mSseConnections.remove(connectionId);

        promise.resolve("SSE connection disconnected");
    }

    /**
     * 获取所有 SSE 连接状态
     */
    @HippyMethod(name = "getSseConnections")
    public void getSseConnections(final HippyMap request, final Promise promise) {
        HippyArray connections = new HippyArray();

        for (Map.Entry<String, SseConnection> entry : mSseConnections.entrySet()) {
            SseConnection connection = entry.getValue();
            HippyMap connectionInfo = new HippyMap();
            connectionInfo.pushString("connectionId", connection.getConnectionId());
            connectionInfo.pushString("url", connection.getRequest().getUrl());
            connectionInfo.pushBoolean("isConnected", connection.isConnected());
            connectionInfo.pushLong("createTime", connection.getCreateTime());
            connectionInfo.pushLong("connectTime", connection.getConnectTime());
            connectionInfo.pushLong("connectionDuration", connection.getConnectionDuration());
            if (connection.getLastEventId() != null) {
                connectionInfo.pushString("lastEventId", connection.getLastEventId());
            }

            connections.pushMap(connectionInfo);
        }

        HippyMap result = new HippyMap();
        result.pushArray("connections", connections);
        result.pushInt("totalCount", mSseConnections.size());

        promise.resolve(result);
    }

    /**
     * 模块销毁时清理资源
     */
    @Override
    public void destroy() {
        super.destroy();

        // 断开所有 SSE 连接
        for (SseConnection connection : mSseConnections.values()) {
            connection.disconnect();
        }
        mSseConnections.clear();

        // 清理适配器资源
        if (mHttpAdapter != null) {
            mHttpAdapter.destroyIfNeed();
        }
        if (mSseAdapter != null) {
            mSseAdapter.destroyIfNeed();
        }

        LogUtils.d("AbcNetworkModule", "AbcNetworkModule destroyed, all SSE connections closed");
    }

    private static class HttpTaskCallbackImpl implements HttpAdapter.HttpTaskCallback {

        private Promise mPromise;
        private String mTaskId;
        private String mFileName;

        HttpTaskCallbackImpl(Promise promise, String taskId, String fileName) {
            mPromise = promise;
            this.mTaskId = taskId;
            this.mFileName = fileName;
        }

        @Override
        public void onTaskSuccess(HippyHttpRequest request, HippyHttpResponse response) throws Exception {
            int contentLength = -1;
            HippyMap headerMap = new HippyMap();
            if (response.getRspHeaderMaps() != null && !response.getRspHeaderMaps().isEmpty()) {
                Set<String> keys = response.getRspHeaderMaps().keySet();
                for (String oneKey : keys) {
                    List<String> value = response.getRspHeaderMaps().get(oneKey);
                    HippyArray oneHeaderFiled = new HippyArray();
                    if (value != null && !value.isEmpty()) {
                        boolean hasSetCookie = false;
                        for (int i = 0; i < value.size(); i++) {
                            String valueStr = value.get(i);
                            oneHeaderFiled.pushString(valueStr);
                            if (HttpHeader.RSP.SET_COOKIE.equalsIgnoreCase(oneKey)) {
                                hasSetCookie = true;
                                getCookieManager().setCookie(request.getUrl(), valueStr);
                            }
                            if (HttpHeader.RSP.CONTENT_LENGTH.equalsIgnoreCase(oneKey)) {
                                contentLength = Integer.parseInt(valueStr);
                            }
                        }
                        if (hasSetCookie)
                            mCookieSyncManager.sync();
                    }

                    headerMap.pushArray(oneKey, oneHeaderFiled);
                }
            }

            if (response.getInputStream() != null) {
                InputStream inputStream = response.getInputStream();

                byte[] buffer = new byte[1024 * 1024];
                FileOutputStream stream = new FileOutputStream(mFileName);
                int currentLen = 0;
                do {
                    int len = inputStream.read(buffer);
                    if (len < 0) break;

                    if (len == 0) continue;

                    currentLen += len;
                    stream.write(buffer, 0, len);


                    HippyMap map = new HippyMap();
                    map.pushString("taskId", mTaskId);
                    map.pushInt("receivedBytes", currentLen);
                    map.pushInt("totalBytes", contentLength);
                    HostHippyMessageBridge.getInstance().onHostMessage("downloadFileCallback", map);
                } while (true);
            }

            HippyMap respMap = new HippyMap();
            respMap.pushInt("statusCode", response.getStatusCode());
            respMap.pushString("statusLine", response.getResponseMessage());
            respMap.pushString("filePath", mFileName);


            respMap.pushMap("respHeaders", headerMap);


            mPromise.resolve(respMap);
        }

        @Override
        public void onTaskFailed(HippyHttpRequest request, Throwable error) {
            if (error != null) {
                mPromise.resolve(error.getMessage());
            }
        }
    }

    /**
     * SSE 连接回调实现
     */
    private static class SseCallbackImpl implements SseAdapter.SseCallback {
        private Promise mPromise;
        private String mConnectionId;
        private SseConnection mConnection;
        private boolean mPromiseResolved = false;

        SseCallbackImpl(Promise promise, String connectionId, SseConnection connection) {
            mPromise = promise;
            mConnectionId = connectionId;
            mConnection = connection;
        }

        @Override
        public void onConnected(HippyHttpRequest request, HippyHttpResponse response) {
            // 更新连接状态
            mConnection.setConnected(true);

            // 通知连接状态变化
            HippyMap statusMap = new HippyMap();
            statusMap.pushString("connectionId", mConnectionId);
            statusMap.pushString("status", "connected");
            HostHippyMessageBridge.getInstance().onHostMessage("sseStatusChanged", statusMap);
        }

        @Override
        public void onEvent(SseEvent event) {
            // 保存最后的事件 ID
            if (event.getId() != null) {
                mConnection.setLastEventId(event.getId());
            }

            // 收到 SSE 事件，发送到 JS 层
            HippyMap eventMap = new HippyMap();
            eventMap.pushString("connectionId", mConnectionId);
            eventMap.pushString("type", event.getType());
            eventMap.pushString("data", event.getData());
            if (event.getId() != null) {
                eventMap.pushString("id", event.getId());
            }
            if (event.getRetry() > 0) {
                eventMap.pushInt("retry", event.getRetry());
            }

            HostHippyMessageBridge.getInstance().onHostMessage("sseEvent", eventMap);
        }

        @Override
        public void onError(HippyHttpRequest request, Throwable error) {
            // 连接错误
            if (!mPromiseResolved) {
                mPromise.reject(error != null ? error.getMessage() : "SSE connection failed");
                mPromiseResolved = true;
            }

            // 通知连接状态变化
            HippyMap statusMap = new HippyMap();
            statusMap.pushString("connectionId", mConnectionId);
            statusMap.pushString("status", "error");
            statusMap.pushString("error", error != null ? error.getMessage() : "Unknown error");
            HostHippyMessageBridge.getInstance().onHostMessage("sseStatusChanged", statusMap);
        }

        @Override
        public void onDisconnected() {
            if (!mPromiseResolved) {
                HippyMap respMap = new HippyMap();
                respMap.pushString("connectionId", mConnectionId);
                mPromise.resolve(respMap);
                mPromiseResolved = true;
            }
            // 连接断开
            HippyMap statusMap = new HippyMap();
            statusMap.pushString("connectionId", mConnectionId);
            statusMap.pushString("status", "disconnected");
            HostHippyMessageBridge.getInstance().onHostMessage("sseStatusChanged", statusMap);
        }
    }
}
