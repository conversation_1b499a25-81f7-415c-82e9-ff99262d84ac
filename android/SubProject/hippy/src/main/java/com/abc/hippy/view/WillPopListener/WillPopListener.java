package com.abc.hippy.view.WillPopListener;

import android.content.Context;

import com.abc.common.utils.LogUtils;
import com.abc.hippy.utils.UIUtils;
import com.abc.hippy.view.abcnavigator.ABCNavigatorItemView;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.HippyInstanceContext;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.javascriptmodules.EventDispatcher;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;
import com.tencent.mtt.hippy.views.view.HippyViewGroup;

public class WillPopListener extends HippyViewGroup implements IBackListener {
    private final HippyEngineContext mHippyContext;
    private static final String TAG = "WillPopListener";

    public WillPopListener(Context context) {
        super(context);

        mHippyContext = ((HippyInstanceContext) context).getEngineContext();
    }

    @Override
    public void setGestureDispatcher(NativeGestureDispatcher nativeGestureDispatcher) {
        super.setGestureDispatcher(nativeGestureDispatcher);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();


        ABCNavigatorItemView item = UIUtils.findNearestParentView(this, ABCNavigatorItemView.class);
        LogUtils.d(TAG, "onAttachedToWindow() called item = " + item);
        if (item != null) {
            item.addBackListener(this);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();

        ABCNavigatorItemView item = UIUtils.findNearestParentView(this, ABCNavigatorItemView.class);

        LogUtils.d(TAG, "onDetachedFromWindow() called item = " + item);
        if (item != null) {
            item.removeBackListener(this);
        }
    }

    @Override
    public boolean handleBack(boolean fromEdgeGesture) {
        HippyMap hippyMap = new HippyMap();
        hippyMap.pushBoolean("fromEdgeGesture", fromEdgeGesture);
        mHippyContext.getModuleManager().getJavaScriptModule(EventDispatcher.class).receiveUIComponentEvent(getId(), "onWillPop", hippyMap);
        return true;
    }
}