package com.abc.hippy.view.abcwebview;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.uimanager.HippyViewController;

@HippyController(name = AbcWebViewController.CLASS_NAME)
public class AbcWebViewController extends HippyViewController<AbcWebView> {
    public static final String CLASS_NAME = "AbcWebView";

    @HippyControllerProps(name = "url", defaultType = HippyControllerProps.STRING, defaultString = "")
    public void loadUrl(AbcWebView view, String url) {
        if (!TextUtils.isEmpty(url))
            view.mWebView.loadUrl(url);
    }

    @Override
    public void dispatchFunction(AbcWebView view, String functionName, HippyArray var) {
        super.dispatchFunction(view, functionName, var);
        switch (functionName) {
            case "loadUrl":
                if (var != null) {
                    String url = var.getString(0);
                    loadUrl(view, url);
                }
                break;
        }
    }

    @Override
    protected View createViewImpl(Context context) {
        return new AbcWebView(context);
    }

    @HippyControllerProps(name = "source", defaultType = HippyControllerProps.MAP)
    public void source(AbcWebView webView, HippyMap info) {
        if (info != null) {
            String userAgent = info.getString("userAgent");
            if (!TextUtils.isEmpty(userAgent))
                webView.mWebView.getSettings().setUserAgentString(userAgent);
            String uri = info.getString("uri");
            if (!TextUtils.isEmpty(uri)) {
                String method = info.getString("method");
                if ("POST".equalsIgnoreCase(method)) {
                    String body = info.getString("body");
                    webView.mWebView.postUrl(uri, body == null ? null : body.getBytes());
                } else {
                    webView.mWebView.loadUrl(uri);
                }
            } else {
                String html = info.getString("html");
                if (!TextUtils.isEmpty(html)) {
                    String baseUrl = info.getString("baseUrl");
                    if (!TextUtils.isEmpty(baseUrl))
                        webView.mWebView.loadDataWithBaseURL(baseUrl, html, "text/html; charset=utf-8", "UTF-8", null);
                    else
                        webView.mWebView.loadData(html, "text/html; charset=utf-8", "UTF-8");
                }
            }
        }
    }

    public void onViewDestroy(AbcWebView webView) {
        webView.mWebView.destroy();
    }

    @Override
    protected boolean handleGestureBySelf() {
        return true;
    }
}
