// Copyright 2019 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package com.abc.hippy.modules.imagepicker;

import android.app.Activity;
import android.content.Intent;
import android.os.Environment;

import com.abc.common.utils.ActivityEventDispatcher;
import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.ICommonCallback;
import com.abc.common.utils.RequestPermissionResultDispatcher;
import com.abc.hippy.utils.HippyUtils;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import java.io.File;

@HippyNativeModule(name = ImagePickerModule.CLASSNAME)
public class ImagePickerModule extends HippyNativeModuleBase {

    final static String CLASSNAME = "ImagePicker";

    public ImagePickerModule(HippyEngineContext context) {
        super(context);

        Activity activity = ContextHolder.getCurrentActivity();
        this.setup(activity);
    }


    private class LifeCycleObserver
            extends ActivityEventDispatcher.ActivityLifecycleListener {
        private final Activity thisActivity;

        LifeCycleObserver(Activity activity) {
            this.thisActivity = activity;
        }

        @Override
        public boolean onDestroy(Activity activity) {
            if (thisActivity == activity && ContextHolder.getAppContext() != null) {
                ActivityEventDispatcher.instance.removeActivityLifecycleListener(this);
            }
            return false;
        }

        @Override
        public void onStop(Activity activity) {
            if (thisActivity == activity) {
                delegate.saveStateBeforeResult();
            }
        }

        @Override
        public void onPause(Activity activity) {

        }

        @Override
        public boolean onNewIntent(Activity activity, Intent intent) {
            return false;
        }
    }

    static final String METHOD_CALL_IMAGE = "pickImage";
    static final String METHOD_CALL_VIDEO = "pickVideo";
    private static final String METHOD_CALL_RETRIEVE = "retrieve";

    private static final String CHANNEL = "plugins.flutter.io/image_picker";

    private static final int SOURCE_CAMERA = 0;
    private static final int SOURCE_GALLERY = 1;

    private ImagePickerDelegate delegate;
    private Activity activity;
    private LifeCycleObserver observer;

    private void setup(
            final Activity activity) {
        this.activity = activity;
        this.delegate = constructDelegate(activity);

        observer = new LifeCycleObserver(activity);

        ActivityEventDispatcher.instance.addActivityLifecycleListener(observer);
        RequestPermissionResultDispatcher.instance.addListener(delegate);
        ActivityEventDispatcher.instance.addActivityResultListener(delegate);
    }

    public void destroy() {
        super.destroy();
        ActivityEventDispatcher.instance.removeActivityLifecycleListener(observer);
        ActivityEventDispatcher.instance.removeActivityResultListener(delegate);
        RequestPermissionResultDispatcher.instance.removeListener(delegate);

        delegate = null;
    }

    private final ImagePickerDelegate constructDelegate(final Activity setupActivity) {
        final ImagePickerCache cache = new ImagePickerCache(setupActivity);

        final File externalFilesDirectory =
                setupActivity.getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        final ExifDataCopier exifDataCopier = new ExifDataCopier();
        final ImageResizer imageResizer = new ImageResizer(externalFilesDirectory, exifDataCopier);
        return new ImagePickerDelegate(setupActivity, externalFilesDirectory, imageResizer, cache);
    }

    @HippyMethod(name = "pickImage")
    public void pickImage(HippyMap params, Promise promise) {
        if (activity == null) {
            promise.reject("image_picker plugin requires a foreground activity.");
            return;
        }
        ICommonCallback result = HippyUtils.createCommonHippyInvokeResultHandler(promise);
        int imageSource = params.getInt("source");
        switch (imageSource) {
            case SOURCE_GALLERY:
                delegate.chooseImageFromGallery(params, result);
                break;
            case SOURCE_CAMERA:
                delegate.takeImageWithCamera(params, result);
                break;
            default:
                throw new IllegalArgumentException("Invalid image source: " + imageSource);
        }
    }
}
