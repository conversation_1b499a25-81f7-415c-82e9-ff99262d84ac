package com.abc.hippy.view.abctextinput;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.accessibility.AccessibilityEvent;
import android.widget.EditText;

import com.abc.hippy.ABCHippyEngineManager;
import com.tencent.mtt.hippy.HippyInstanceContext;
import com.tencent.mtt.hippy.HippyInstanceLifecycleEventListener;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;
import com.tencent.mtt.hippy.views.view.HippyViewGroup;

import java.util.ArrayList;


public class AbcCustomKeyboardView extends HippyViewGroup implements HippyInstanceLifecycleEventListener {
    private static final String TAG = "AbcCustomKeyboardView";
    private int mKeyboardHeight = -1;

    public interface IAbcCustomKeyboardViewListener {
        void onHeightChanged(AbcCustomKeyboardView view, int height);
    }

    private final AbcCustomKeyboardViewHostView mContentView;
    private EditText mEditText;

    private IAbcCustomKeyboardViewListener mListener;

    public AbcCustomKeyboardView(Context context) {
        super(context);

        HippyInstanceContext hippyInstanceContext = (HippyInstanceContext) context;
        hippyInstanceContext.getEngineContext().addInstanceLifecycleEventListener(this);

        mContentView = new AbcCustomKeyboardViewHostView(context);
    }

    public View getContentView() {
        return mContentView;
    }

    public void setListener(IAbcCustomKeyboardViewListener listener) {
        this.mListener = listener;
    }

    public int getKeyboardHeight() {
        return mKeyboardHeight;
    }

    @Override
    public void setGestureDispatcher(NativeGestureDispatcher nativeGestureDispatcher) {
        super.setGestureDispatcher(nativeGestureDispatcher);
    }

    @Override
    public void onInstanceLoad(int i) {
        showOrUpdate();
    }

    @Override
    public void onInstanceResume(int i) {
        showOrUpdate();
    }

    @Override
    public void onInstancePause(int i) {
        this.hide();
    }

    @Override
    public void onInstanceDestroy(int i) {
        HippyInstanceContext hippyInstanceContext = (HippyInstanceContext) getContext();
        hippyInstanceContext.getEngineContext().removeInstanceLifecycleEventListener(this);

        this.hide();
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        Log.d(TAG, "onLayout() called with: changed = [" + changed + "], l = [" + l + "], t = [" + t + "], r = [" + r + "], b = [" + b + "]");

        LayoutParams lp = mContentView.getLayoutParams();
        if (lp != null) {
            lp.height = b - t;
        }

        mKeyboardHeight = b - t;

        if (mListener != null) {
            mListener.onHeightChanged(this, b - t);
        }
    }

    @Override
    public void addView(View child, int index) {
        mContentView.addView(child, index);
    }

    @Override
    public int getChildCount() {
        return mContentView.getChildCount();
    }

    @Override
    public View getChildAt(int index) {
        return mContentView.getChildAt(index);
    }

    @Override
    public void removeView(View child) {
        mContentView.removeView(child);
    }

    @Override
    public void removeViewAt(int index) {
        View child = getChildAt(index);
        mContentView.removeView(child);
    }

    public void addChildrenForAccessibility(ArrayList<View> outChildren) {

    }

    @Override
    public boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent event) {
        return false;
    }

    public void showOrUpdate() {
//        ABCHippyEngineManager.IInputAccessoryViewMounter viewMounter = this.viewMounter();
//
//        if (viewMounter != null) {
//            viewMounter.onShowInputAccessoryView(mContentView);
//        }
    }

    public void hide() {
//        ABCHippyEngineManager.IInputAccessoryViewMounter viewMounter = this.viewMounter();
//        if (viewMounter != null) {
//            viewMounter.onHideInputAccessoryView(mContentView);
//        }
    }

    private ABCHippyEngineManager.IInputAccessoryViewMounter viewMounter() {
        HippyInstanceContext hippyInstanceContext = (HippyInstanceContext) getContext();
        if (hippyInstanceContext.getEngineManager() instanceof ABCHippyEngineManager) {
            return ((ABCHippyEngineManager) hippyInstanceContext.getEngineManager()).getInputAccessoryViewMounter();
        }

        return null;
    }

    public void setEditText(EditText editText) {
        this.mEditText = editText;
    }

    public EditText getEditText() {
        return this.mEditText;
    }
}

class AbcCustomKeyboardViewHostView extends HippyViewGroup {
    public AbcCustomKeyboardViewHostView(Context context) {
        super(context);
    }
}
