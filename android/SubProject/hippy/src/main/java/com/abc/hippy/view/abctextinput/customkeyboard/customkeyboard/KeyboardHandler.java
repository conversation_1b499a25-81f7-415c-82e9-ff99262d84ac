/*
 * 用于实现Android hippy端自定义键盘
 */
package com.abc.hippy.view.abctextinput.customkeyboard.customkeyboard;

import android.view.View;

import com.abc.hippy.view.abctextinput.AbcCustomKeyboardView;
import com.abc.hippy.view.abctextinput.AbcTextInputImpl;
import com.abc.hippy.view.abctextinput.customkeyboard.KeyboardManager;

public class KeyboardHandler {
    public KeyboardHandler() {
    }

    public void bindToEditor(AbcTextInputImpl abcTextInput, AbcCustomKeyboardView customKeyboardView) {
        KeyboardManager.getInstance().bindToEditor(abcTextInput, customKeyboardView);
    }

    public void unbindToEditor(AbcTextInputImpl abcTextInput) {
        KeyboardManager.getInstance().unbindToEditor(abcTextInput);
    }

    public void destroy() {
    }

    public void onFocusChanged(View v, boolean hasFocus) {
        KeyboardManager.getInstance().onFocusChanged(v, hasFocus);
    }
}

