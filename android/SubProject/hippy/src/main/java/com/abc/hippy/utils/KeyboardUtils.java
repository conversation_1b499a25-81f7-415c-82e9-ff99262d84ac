package com.abc.hippy.utils;

import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ScrollView;

import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.LogUtils;

import java.util.ArrayList;

public class KeyboardUtils {
    private static final String TAG = "KeyboardUtils";

    /**
     * 根据当前编辑框在scrollview的里的位置进行调整,使编辑框显示在可见区域
     *
     * @param activity activity
     */
    public static void adjustKeyboard(Activity activity) {
        FrameLayout content = activity.findViewById(android.R.id.content);
        //找到所有输入框
        ArrayList<EditText> allEts = new ArrayList<>();
        findAllEditText(allEts, content);
        EditText focusEt = null;
        //找到焦点EditText
        for (EditText et : allEts) {
            if (et.isFocused()) {
                focusEt = et;
                break;
            }
        }

        if (focusEt != null) {
            ScrollView scrollView = getVerticalScrollView(focusEt);
            if (scrollView == null) return;

            int delta = computeMoveDelta(focusEt, scrollView);
            LogUtils.d(TAG, "adjustKeyboard() called with: activity = [" + activity + "], delta = " + delta);
            if (delta > 0) {
                scrollView.smoothScrollBy(0, delta);
            }
        }
    }

    /**
     * 收集所有的编辑框
     *
     * @param list  用于收集group中的编辑框
     * @param group 目标View
     */
    private static void findAllEditText(ArrayList<EditText> list, ViewGroup group) {
        for (int i = 0; i < group.getChildCount(); i++) {
            View v = group.getChildAt(i);
            if (v instanceof EditText && v.getVisibility() == View.VISIBLE) {
                list.add((EditText) v);
            } else if (v instanceof ViewGroup) {
                findAllEditText(list, (ViewGroup) v);
            }
        }
    }

    /**
     * 获取最近的一个scrollview
     *
     * @param view 子view
     * @return 最近一个ScrollView|null
     */
    private static ScrollView getVerticalScrollView(View view) {
        ViewParent parent = view.getParent();
        do {
            if (parent instanceof ScrollView) {
                return (ScrollView) parent;
            }
            parent = parent.getParent();
        } while (parent != null);

        return null;
    }

    /**
     * 计算childView需要移动的偏移量以使childView显示出来
     *
     * @param childView        要显示的目标view
     * @param parentScrollView 父scrollView
     * @return 偏移量, 如果不需要移动, 返回0
     */
    private static int computeMoveDelta(View childView, ScrollView parentScrollView) {
        Rect scrollBounds = new Rect();
        parentScrollView.getDrawingRect(scrollBounds);
        int top = 0;
        View view = childView;
        while (view != parentScrollView) {
            top += view.getTop();
            view = (View) view.getParent();
        }
        int bottom = top - childView.getTop() + childView.getBottom();
        int delta = bottom - scrollBounds.bottom;

        return Math.max(delta, 0);
    }

    private static InputMethodManager getInputMethodManager() {
        return (InputMethodManager) ContextHolder.getAppContext().getSystemService(Context.INPUT_METHOD_SERVICE);
    }

    public static void hideInputMethod(View view) {
        InputMethodManager imm = getInputMethodManager();

        imm.hideSoftInputFromWindow(view.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
    }
}
