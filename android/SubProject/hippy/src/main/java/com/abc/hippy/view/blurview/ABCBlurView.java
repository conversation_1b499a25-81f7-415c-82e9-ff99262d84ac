package com.abc.hippy.view.blurview;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.abc.common.utils.ContextHolder;
import com.tencent.mtt.hippy.uimanager.HippyViewBase;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;

import java.util.HashMap;
import java.util.Map;

import eightbitlab.com.blurview.BlurView;
import eightbitlab.com.blurview.RenderScriptBlur;

/**
 * 高斯模糊View
 */
public class ABCBlurView extends FrameLayout implements HippyViewBase {
    private final BlurView mBlurView;
    private static Map<String, Integer> sBlurType= new HashMap<String, Integer>(){{
        put("light", Color.argb(51, 255, 255, 255));
        put("xlight", Color.argb(191, 255, 255, 255));
        put("dark", Color.argb(163, 12,12, 12));
    }};

    private String mBlurType;
    private int mBlurAmount;
    private int mOverlayColor;

    private NativeGestureDispatcher mGestureDispatcher;


    public ABCBlurView(Context context) {
        super(context);
        this.mBlurAmount = 10;
        this.mBlurType = "dark";
        this.mOverlayColor = -1;

        mBlurView = new BlurView(context);
        View decorView = ContextHolder.getCurrentActivity().getWindow().getDecorView();
        ViewGroup rootView = decorView.findViewById(android.R.id.content);
        Drawable windowBackground = decorView.getBackground();
        mBlurView.setupWith(rootView)
                .setFrameClearDrawable(windowBackground)
                .setBlurAlgorithm(new RenderScriptBlur(context))
                .setBlurRadius(this.mBlurAmount)
                .setHasFixedTransformationMatrix(false);
        this.addView(mBlurView, new ViewGroup.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
        this.updateEffect();
    }

    public void setBlurType(String blurType) {
        this.mBlurType = blurType;
        this.updateEffect();
    }

    public void setBlurAmount(int blurAmount) {
        this.mBlurAmount = blurAmount;
        this.updateEffect();
    }

    public void setOverlayColor(int overlayColor) {
        this.mOverlayColor = overlayColor;
    }


    private void updateEffect() {
        mBlurView.setBlurRadius((int)(this.mBlurAmount * 0.8));
        if (this.mOverlayColor != -1) {
            mBlurView.setOverlayColor(this.mOverlayColor);
        }
        else {
            Integer color = sBlurType.get(this.mBlurType);
                if (color!= null)
            mBlurView.setOverlayColor(color);
        }

        mBlurView.invalidate();
    }

    @Override
    public NativeGestureDispatcher getGestureDispatcher() {
        return mGestureDispatcher;
    }

    @Override
    public void setGestureDispatcher(NativeGestureDispatcher dispatcher) {
        this.mGestureDispatcher = dispatcher;
    }
}
