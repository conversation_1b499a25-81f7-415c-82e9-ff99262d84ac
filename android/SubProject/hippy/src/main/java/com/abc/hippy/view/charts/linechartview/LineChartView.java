package com.abc.hippy.view.charts.linechartview;

import android.content.Context;
import android.graphics.Color;

import com.abc.hippy.utils.HippyUtils;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.components.AxisBase;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.ValueFormatter;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.utils.ColorTemplate;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.uimanager.HippyViewBase;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;
import com.tencent.mtt.hippy.utils.PixelUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;

/**
 * 滚轮选择器
 * <p>
 * WheelPicker
 *
 * <AUTHOR> 2015-12-12
 * <AUTHOR> 2016-06-17
 * 更新项目结构
 * <p>
 * New project structure
 * @version 1.1.0
 */
public class LineChartView extends LineChart implements HippyViewBase {
    private static final String TAG = "LineChartView";
    private NativeGestureDispatcher mGestureDispatcher;
    private HippyMap mXAxisParmas;
    private HippyMap mLeftAxisParmas;
    private HippyMap mRightAxisParmas;

    private SimpleDateFormat mXAxisFormat;


    public LineChartView(Context context) {
        super(context);

        this.getDescription().setEnabled(false);
        this.setTouchEnabled(true);

        LineChart chart = this;
        chart.setDragDecelerationFrictionCoef(0.9f);

        // enable scaling and dragging
        chart.setDragEnabled(false);
        chart.setScaleEnabled(false);
        chart.setDrawGridBackground(false);
        chart.setHighlightPerDragEnabled(true);

        // set an alternative background color
        chart.setBackgroundColor(Color.WHITE);
//        chart.setViewPortOffsets(0f, 0f, 0f, 0f);

        Legend l = chart.getLegend();
        l.setEnabled(false);

        XAxis xAxis = chart.getXAxis();
        xAxis.setPosition(XAxis.XAxisPosition.TOP_INSIDE);
        xAxis.setTextSize(10f);
        xAxis.setTextColor(Color.WHITE);
        xAxis.setDrawAxisLine(false);
        xAxis.setDrawGridLines(true);
        xAxis.setTextColor(Color.rgb(255, 192, 56));
        xAxis.setCenterAxisLabels(false);

        mXAxisFormat = new SimpleDateFormat("MM.dd", Locale.CHINA);
        xAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                return mXAxisFormat.format(new Date((long) value));
            }
        });

        YAxis leftAxis = chart.getAxisLeft();
        leftAxis.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART);
        leftAxis.setTextColor(ColorTemplate.getHoloBlue());
        leftAxis.setDrawGridLines(true);


        YAxis rightAxis = chart.getAxisRight();
        rightAxis.setEnabled(false);
    }

    @Override
    public NativeGestureDispatcher getGestureDispatcher() {
        return mGestureDispatcher;
    }

    @Override
    public void setGestureDispatcher(NativeGestureDispatcher dispatcher) {
        this.mGestureDispatcher = dispatcher;
    }

    public void setData(HippyArray items) {

        ArrayList<ILineDataSet> dataSets = new ArrayList<>();

        for (int i = 0; i < items.size(); ++i) {
            HippyMap item = (HippyMap) items.get(i);
            String label = item.getString("label");
            String axisDependency = item.getString("axisDependency");


            float lineWidth = 1.0f;
            if (item.get("lineWidth") != null) {
                lineWidth = (float) item.getDouble("lineWidth");
            }

            HippyArray rawValues = (HippyArray) item.get("values");


            ArrayList<Entry> values = new ArrayList<>();
            if (rawValues != null && rawValues.size() > 0) {
                for (int j = 0; j < rawValues.size(); ++j) {
                    HippyMap value = (HippyMap) rawValues.get(j);
                    double x = value.getDouble("x");
                    double y = value.getDouble("y");
                    Entry entry = new Entry((float) x, (float) y);
                    values.add(entry);
                }
            }


            if (label == null) label = "";
            LineDataSet dataSet = new LineDataSet(values, label);
            if ("left".equals(axisDependency))
                dataSet.setAxisDependency(YAxis.AxisDependency.LEFT);


            if (item.get("circleColor") != null) {
                int circleColor = item.getInt("circleColor");
                dataSet.setCircleColor(circleColor);
            }

            if (item.get("drawCircles") != null) {
                boolean drawCircles = item.getBoolean("drawCircles");
                dataSet.setDrawCircles(drawCircles);
            }

            if (item.get("circleRadius") != null) {
                double circleRadius = item.getDouble("circleRadius");
                dataSet.setCircleRadius((float) circleRadius);
            }


            if (item.get("lineColor") != null) {
                int lineColor = item.getInt("lineColor");
                dataSet.setColor(lineColor);
            }

            dataSet.setDrawValues(false);
            dataSet.setAxisDependency(YAxis.AxisDependency.LEFT);

            dataSet.setValueTextColor(ColorTemplate.getHoloBlue());
            dataSet.setLineWidth(lineWidth);

            dataSet.setFillAlpha(65);
            dataSet.setFillColor(Color.rgb(244, 0, 0));
            dataSet.setHighLightColor(Color.rgb(244, 0, 0));
            dataSet.setDrawCircleHole(false);

            dataSets.add(dataSet);
        }

        LineData data = new LineData(dataSets);
        data.setValueTextColor(Color.WHITE);
        data.setValueTextSize(9f);
        this.setData(data);
        this.invalidate();
    }

    public void setXAxis(HippyMap params) {
        mXAxisParmas = params;


        this.updateAxisWithAttributes(this.getXAxis(), params);
    }

    public void setLeftAxis(HippyMap params) {
        mLeftAxisParmas = params;

        this.updateAxisWithAttributes(this.getAxisLeft(), params);
    }

    public void setRightAxis(HippyMap params) {
        mRightAxisParmas = params;

        this.updateAxisWithAttributes(this.getAxisRight(), params);
    }

    private void updateAxisWithAttributes(AxisBase axis, HippyMap attributes) {
        if (axis instanceof XAxis) {
            XAxis xAxis = (XAxis) axis;
            String position = attributes.getString("labelPosition");
            switch (position) {
                case "topInside":
                    xAxis.setPosition(XAxis.XAxisPosition.TOP_INSIDE);
                    break;
                case "bottomInside":
                    xAxis.setPosition(XAxis.XAxisPosition.BOTTOM_INSIDE);
                    break;
                case "top":
                    xAxis.setPosition(XAxis.XAxisPosition.TOP);
                    break;
                case "bottom":
                    xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
                    break;
            }


            HippyMap valueFormatterParams = attributes.getMap("valueFormatter");
            if (valueFormatterParams != null) {
                String type = valueFormatterParams.getString("type");
                String format = valueFormatterParams.getString("format");
                if ("time".equals(type)) {
                    mXAxisFormat = new SimpleDateFormat(format, Locale.CHINA);
                }
            }
        }

        if (axis instanceof YAxis) {
            YAxis yAxis = (YAxis) axis;
            String position = attributes.getString("labelPosition");
            if (position.equals("inside")) {
                yAxis.setPosition(YAxis.YAxisLabelPosition.INSIDE_CHART);
            } else if (position.equals("outside")) {
                yAxis.setPosition(YAxis.YAxisLabelPosition.OUTSIDE_CHART);
            }
        }


        if (attributes.get("axisMinimum") != null) {
            axis.setAxisMinimum((float) attributes.getDouble("axisMinimum"));
        }

        if (attributes.get("axisMaximum") != null) {
            axis.setAxisMaximum((float) attributes.getDouble("axisMaximum"));
        }

        if (attributes.get("granularity") != null) {
            axis.setGranularity(attributes.getInt("granularity"));
        }

        HippyMap labelFontStyle = attributes.getMap("labelFontStyle");
        if (labelFontStyle != null) {
            float fontSize = 14;

            int fontSizeNum = labelFontStyle.getInt("fontSize");
            if (fontSizeNum > 0) {
                fontSize = fontSizeNum;
            }
            axis.setTextSize(fontSize);

            String fontFamily = labelFontStyle.getString("fontFamily");
            String fontWeight = labelFontStyle.getString("fontWeight");
            axis.setTypeface(HippyUtils.createTypeface(fontFamily, fontWeight));
            if (labelFontStyle.get("color") != null) {
                int color = labelFontStyle.getInt("color");
                axis.setTextColor(color);
            }
        }
    }
}
