package com.abc.hippy.modules.localnotificiation.models.styles;

import java.util.ArrayList;

public class InboxStyleInformation extends DefaultStyleInformation {
    public Boolean htmlFormatLines;
    public ArrayList<String> lines;
    public String contentTitle;
    public Boolean htmlFormatContentTitle;
    public String summaryText;
    public Boolean htmlFormatSummaryText;

    public InboxStyleInformation(Boolean htmlFormatTitle, Boolean htmlFormatBody, String contentTitle, Boolean htmlFormatContentTitle, String summaryText, Boolean htmlFormatSummaryText, ArrayList<String> lines, Boolean htmlFormatLines) {
        super(htmlFormatTitle, htmlFormatBody);
        this.contentTitle = contentTitle;
        this.htmlFormatContentTitle = htmlFormatContentTitle;
        this.summaryText = summaryText;
        this.htmlFormatSummaryText = htmlFormatSummaryText;
        this.lines = lines;
        this.htmlFormatLines = htmlFormatLines;
    }
}