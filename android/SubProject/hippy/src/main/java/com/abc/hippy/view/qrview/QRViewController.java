package com.abc.hippy.view.qrview;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.uimanager.HippyViewController;


@HippyController(name = "QRView")
public class QRViewController extends HippyViewController<QRView> {
    @Override
    protected View createViewImpl(Context context) {
        return new QRView(context);
    }

    @HippyControllerProps(name = "content")
    public void setContent(QRView chartView, String content) {
        chartView.setContent(content);
    }


    @HippyControllerProps(name = "color")
    public void setColor(QRView chartView, int color) {
        chartView.setColor(color);
    }
}
