package com.abc.hippy.view.abcnavigator;

import android.animation.Animator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.StateListDrawable;
import android.util.Log;
import android.view.View;

import com.abc.common.utils.LogUtils;
import com.abc.hippy.ABCHippyEngineManager;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.HippyInstanceContext;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.javascriptmodules.EventDispatcher;
import com.tencent.mtt.hippy.views.view.HippyViewGroup;

public class ABCNavigatorView extends HippyViewGroup implements ABCHippyEngineManager.IBackPressedListener {
    private static final String TAG = "ABCNavigatorView";
    private final HippyEngineContext mHippyContext;
    private boolean mOnKeyBackEnable = false;

    public ABCNavigatorView(Context context) {
        super(context);
        mHippyContext = ((HippyInstanceContext) context).getEngineContext();

        //以下设置focused的颜色，解决一个诡异问题，门诊》处方添加>激活自定义键盘后返回出现绿色边框的现象，通过调查发现textinput onblur后，
        // ABCNavigatorView处于焦点状态，但还没有查明绿色是谁设置的，先这么设置一下
        int[] mNormalState = new int[]{};
        int[] mFocusedSate = new int[]{android.R.attr.state_focused, android.R.attr.state_enabled};

        StateListDrawable drawable = new StateListDrawable();
        ColorDrawable pressedDrawable = new ColorDrawable(Color.TRANSPARENT);
        drawable.addState(mFocusedSate, pressedDrawable);

        ColorDrawable normalDrawable = new ColorDrawable(Color.TRANSPARENT);
        drawable.addState(mNormalState, normalDrawable);
        setBackgroundDrawable(drawable);
    }


    @Override
    public void addView(View view, int i) {
        super.addView(view, i);
        ABCNavigatorItemView itemView = ((ABCNavigatorItemView) view);
        itemView.setNavigator(this);
        itemView.setPageIndex(i);
        itemView.setBackPressedListener(this);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        ABCHippyEngineManager manager = (ABCHippyEngineManager) ((HippyInstanceContext) getContext()).getEngineManager();
        manager.addBackPressedListener(this);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        ABCHippyEngineManager manager = (ABCHippyEngineManager) ((HippyInstanceContext) getContext()).getEngineManager();
        manager.removeBackPressedListener(this);
    }

    @Override
    public void onAnimationEnd(Animator animator) {
        super.onAnimationEnd(animator);
    }


    @Override
    protected void onLayout(boolean change, int l, int t, int r, int b) {
        int childCount = getChildCount();
        for (int i = 0; i < childCount; i++) {
            getChildAt(i).layout(0, 0, getWidth(), getHeight());
        }
    }

    public void pop(boolean animate, Animator.AnimatorListener listener) {
        ABCNavigatorItemView view = (ABCNavigatorItemView) getChildAt(getChildCount() - 1);
        if (animate) {
            view.pop(animate, listener);
        } else {
            if (listener != null) {
                listener.onAnimationEnd(null);
            }
        }
    }


    ABCNavigatorItemView currentPageItem() {
        if (getChildCount() >= 1) {
            return (ABCNavigatorItemView) getChildAt(getChildCount() - 1);
        }

        return null;
    }

    @Override
    public boolean onBackPressed(boolean fromEdgeGesture) {
        ABCNavigatorItemView currentItem = this.currentPageItem();
        LogUtils.d(TAG, "onBackPressed() called currentItem = " + currentItem);
        if (currentItem != null && currentItem.handleBackEvent(fromEdgeGesture))
            return true;

        if (!fromEdgeGesture && mOnKeyBackEnable) {
            mHippyContext.getModuleManager().getJavaScriptModule(EventDispatcher.class).receiveUIComponentEvent(getId(), "onKeyBack", new HippyMap());
            return true;
        }

        if (getChildCount() > 1) {
            ABCNavigatorItemView view = (ABCNavigatorItemView) getChildAt(getChildCount() - 1);
            view.pop(true,
                    new Animator.AnimatorListener() {
                        @Override
                        public void onAnimationStart(Animator animator) {

                        }

                        @Override
                        public void onAnimationEnd(Animator animator) {
                            HippyMap hippyMap = new HippyMap();
                            mHippyContext.getModuleManager().getJavaScriptModule(EventDispatcher.class).receiveUIComponentEvent(getId(), "onPopFromEdgeGesture", hippyMap);
                        }

                        @Override
                        public void onAnimationCancel(Animator animator) {

                        }

                        @Override
                        public void onAnimationRepeat(Animator animator) {

                        }
                    });

            return true;
        }

        return false;
    }

    public void setOnKeyBackEnable(boolean enable) {
        this.mOnKeyBackEnable = enable;
    }
}