package com.abc.hippy.view.TouchListener;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import android.widget.FrameLayout;

import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.dom.node.NodeProps;
import com.tencent.mtt.hippy.uimanager.HippyViewBase;
import com.tencent.mtt.hippy.uimanager.HippyViewEvent;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;
import com.tencent.mtt.hippy.views.view.HippyViewGroup;

public class TouchListenerView extends HippyViewGroup {
    public TouchListenerView(Context context) {
        super(context);
    }


    @Override
    public void setGestureDispatcher(NativeGestureDispatcher nativeGestureDispatcher) {
        super.setGestureDispatcher(nativeGestureDispatcher);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (mGestureDispatcher == null) return super.onInterceptTouchEvent(ev);

        int action = ev.getAction() & MotionEvent.ACTION_MASK;
        boolean result = super.onInterceptTouchEvent(ev);

        switch (action) {
            case MotionEvent.ACTION_DOWN: {
                mGestureDispatcher.handle(NodeProps.ON_TOUCH_DOWN, ev.getX(), ev.getY(), ev);
                break;
            }

            case MotionEvent.ACTION_MOVE: {
                mGestureDispatcher.handle(NodeProps.ON_TOUCH_MOVE, ev.getX(), ev.getY(), ev);
                break;
            }

            case MotionEvent.ACTION_UP: {
                mGestureDispatcher.handle(NodeProps.ON_TOUCH_END, ev.getX(), ev.getY(), ev);
                break;
            }
            case MotionEvent.ACTION_CANCEL: {
                mGestureDispatcher.handle(NodeProps.ON_TOUCH_CANCEL, ev.getX(), ev.getY(), ev);
                break;
            }
        }

        return result;
    }
}