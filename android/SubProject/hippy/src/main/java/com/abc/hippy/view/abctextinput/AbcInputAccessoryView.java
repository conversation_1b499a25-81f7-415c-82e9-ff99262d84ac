package com.abc.hippy.view.abctextinput;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.accessibility.AccessibilityEvent;

import com.abc.hippy.ABCHippyEngineManager;
import com.tencent.mtt.hippy.HippyInstanceContext;
import com.tencent.mtt.hippy.HippyInstanceLifecycleEventListener;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;
import com.tencent.mtt.hippy.views.view.HippyViewGroup;

import java.util.ArrayList;


public class AbcInputAccessoryView extends HippyViewGroup implements HippyInstanceLifecycleEventListener {
    private static final String TAG = "AbcInputAccessoryView";
    private final AbcInputAccessoryViewHostView mHostView;

    public AbcInputAccessoryView(Context context) {
        super(context);

        HippyInstanceContext hippyInstanceContext = (HippyInstanceContext) context;
        hippyInstanceContext.getEngineContext().addInstanceLifecycleEventListener(this);

        mHostView = new AbcInputAccessoryViewHostView(context);
    }


    @Override
    public void setGestureDispatcher(NativeGestureDispatcher nativeGestureDispatcher) {
        super.setGestureDispatcher(nativeGestureDispatcher);
    }

    @Override
    public void onInstanceLoad(int i) {
        showOrUpdate();
    }

    @Override
    public void onInstanceResume(int i) {
        showOrUpdate();
    }

    @Override
    public void onInstancePause(int i) {
        this.hide();
    }

    @Override
    public void onInstanceDestroy(int i) {
        HippyInstanceContext hippyInstanceContext = (HippyInstanceContext) getContext();
        hippyInstanceContext.getEngineContext().removeInstanceLifecycleEventListener(this);

        this.hide();
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        Log.d(TAG, "onLayout() called with: changed = [" + changed + "], l = [" + l + "], t = [" + t + "], r = [" + r + "], b = [" + b + "]");

        ABCHippyEngineManager.IInputAccessoryViewMounter viewMounter = this.viewMounter();

        if (viewMounter != null) {
            viewMounter.onUpdateInputAccessoryView(mHostView, b - t);
        }
    }

    @Override
    public void addView(View child, int index) {
        mHostView.addView(child, index);
    }

    @Override
    public int getChildCount() {
        return mHostView.getChildCount();
    }

    @Override
    public View getChildAt(int index) {
        return mHostView.getChildAt(index);
    }

    @Override
    public void removeView(View child) {
        mHostView.removeView(child);
    }

    @Override
    public void removeViewAt(int index) {
        View child = getChildAt(index);
        mHostView.removeView(child);
    }

    public void addChildrenForAccessibility(ArrayList<View> outChildren) {

    }

    @Override
    public boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent event) {
        return false;
    }

    public void showOrUpdate() {
        ABCHippyEngineManager.IInputAccessoryViewMounter viewMounter = this.viewMounter();

        if (viewMounter != null) {
            viewMounter.onShowInputAccessoryView(mHostView);
        }
    }

    public void hide() {
        ABCHippyEngineManager.IInputAccessoryViewMounter viewMounter = this.viewMounter();
        if (viewMounter != null) {
            viewMounter.onHideInputAccessoryView(mHostView);
        }
    }

    private ABCHippyEngineManager.IInputAccessoryViewMounter viewMounter() {
        HippyInstanceContext hippyInstanceContext = (HippyInstanceContext) getContext();
        if (hippyInstanceContext.getEngineManager() instanceof ABCHippyEngineManager) {
            return ((ABCHippyEngineManager) hippyInstanceContext.getEngineManager()).getInputAccessoryViewMounter();
        }

        return null;
    }
}

class AbcInputAccessoryViewHostView extends HippyViewGroup {
    public AbcInputAccessoryViewHostView(Context context) {
        super(context);
    }

//    @Override
//    protected void onSizeChanged(final int w, final int h, int oldw, int oldh) {
//        super.onSizeChanged(w, h, oldw, oldh);
//        if (getChildCount() > 0) {
//            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
//                getChildAt(0).layout(getChildAt(0).getLeft(), getChildAt(0).getTop(), getChildAt(0).getLeft() + w, getChildAt(0).getTop() + h);
//            }
//
//            HippyInstanceContext hippyInstanceContext = (HippyInstanceContext) getContext();
//            if (hippyInstanceContext != null && hippyInstanceContext.getEngineContext() != null) {
//                final HippyEngineContext engineContext = hippyInstanceContext.getEngineContext();
//                if (engineContext.getThreadExecutor() != null) {
//                    final int id = getChildAt(0).getId();
//                    final int width = w;
//                    final int height = h;
//                    engineContext.getThreadExecutor().postOnDomThread(new Runnable() {
//                        @Override
//                        public void run() {
//                            if (engineContext != null && engineContext.getDomManager() != null) {
//                                engineContext.getDomManager().updateNodeSize(id, width, height);
//                            }
//                        }
//                    });
//
//                }
//
//            }
//        }
//    }
//
//    @Override
//    public void requestDisallowInterceptTouchEvent(boolean disallowIntercept) {
//
//    }
}
