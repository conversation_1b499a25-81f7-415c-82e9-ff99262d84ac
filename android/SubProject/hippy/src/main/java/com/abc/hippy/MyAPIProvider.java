package com.abc.hippy;

import android.content.Context;

import com.abc.hippy.modules.FileModule;
import com.abc.hippy.modules.MixInvokeMethodModule;
import com.abc.hippy.modules.abcnetwork.AbcNetworkModule;
import com.abc.hippy.modules.alipay.AliPayModule;
import com.abc.hippy.modules.asr.AbcAsrModule;
import com.abc.hippy.modules.audiorecorder.AudioRecorderModule;
import com.abc.hippy.modules.barcodescan.BarcodeScanModule;
import com.abc.hippy.modules.bugly.BuglyModule;
import com.abc.hippy.modules.pdf.PdfModule;
import com.abc.hippy.modules.share.ShareModule;
import com.abc.hippy.modules.urllauncher.UrlLauncherModule;
import com.abc.hippy.modules.webviewutils.WebViewUtilsModule;
import com.abc.hippy.modules.gallerysaver.GallerySaverModule;
import com.abc.hippy.modules.imagepicker.ImagePickerModule;
import com.abc.hippy.modules.localnotificiation.LocalNotificationModule;
import com.abc.hippy.modules.oss.OssModule;
import com.abc.hippy.modules.permission.PermissionHandlerModule;
import com.abc.hippy.modules.push.PushModule;
import com.abc.hippy.modules.socketio.SocketIOModule;
import com.abc.hippy.modules.statmanager.StatManagerModule;
import com.abc.hippy.modules.thirdcall.ThirdCall;
import com.abc.hippy.modules.wxapi.WxApiModule;
import com.abc.hippy.view.TouchListener.TouchListenerViewController;
import com.abc.hippy.view.WillPopListener.WillPopListenerController;
import com.abc.hippy.view.abcnavigator.ABCNavigatorItemViewController;
import com.abc.hippy.view.abcnavigator.ABCNavigatorViewController;
import com.abc.hippy.view.abcscrollview.AbcScrollViewController;
import com.abc.hippy.view.abctextinput.AbcCustomKeyboardViewController;
import com.abc.hippy.view.abctextinput.AbcInputAccessoryViewController;
import com.abc.hippy.view.abctextinput.AbcTextInputImplController;
import com.abc.hippy.view.abctextinput.AbcTextInputViewController;
import com.abc.hippy.view.abcwebview.AbcWebViewController;
import com.abc.hippy.view.abcMarkdown.MarkdownViewController;
import com.abc.hippy.view.audioview.ABCAudioViewController;
import com.abc.hippy.view.blurview.ABCBlurViewController;
import com.abc.hippy.view.captureview.CaptureViewController;
import com.abc.hippy.view.charts.linechartview.LineChartViewController;
import com.abc.hippy.view.charts.piechartview.PieChartViewController;
import com.abc.hippy.view.gradient.AbcLinearGradientViewController;
import com.abc.hippy.view.list.ReverseListItemViewController;
import com.abc.hippy.view.list.ReverseListViewController;
import com.abc.hippy.view.pinchimage.PinchImageViewController;
import com.abc.hippy.view.qrview.QRViewController;
import com.abc.hippy.view.svgview.SVGViewController;
import com.abc.hippy.view.wheelview.WheelViewController;
import com.tencent.mtt.hippy.HippyAPIProvider;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.common.Provider;
import com.tencent.mtt.hippy.modules.javascriptmodules.HippyJavaScriptModule;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;
import com.tencent.mtt.hippy.uimanager.HippyViewController;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MyAPIProvider implements HippyAPIProvider {
    private final Context mContext;

    MyAPIProvider(Context context) {
        mContext = context;
    }

    /**
     * 接口：用来让JavaScript调用Java层的接口
     */
    @Override
    public Map<Class<? extends HippyNativeModuleBase>, Provider<? extends HippyNativeModuleBase>> getNativeModules(final HippyEngineContext context) {
        Map<Class<? extends HippyNativeModuleBase>, Provider<? extends HippyNativeModuleBase>> modules = new HashMap<>();

        this.addModule(modules, FileModule.class, context);
        this.addModule(modules, WxApiModule.class, context);
        this.addModule(modules, BarcodeScanModule.class, context);
        this.addModule(modules, AbcNetworkModule.class, context);
        this.addModule(modules, MixInvokeMethodModule.class, context);
        this.addModule(modules, SocketIOModule.class, context);
        this.addModule(modules, PushModule.class, context);
        this.addModule(modules, ThirdCall.class, context);
        this.addModule(modules, LocalNotificationModule.class, context);
        this.addModule(modules, OssModule.class, context);
        this.addModule(modules, ImagePickerModule.class, context);
        this.addModule(modules, AudioRecorderModule.class, context);
        this.addModule(modules, PermissionHandlerModule.class, context);
        this.addModule(modules, StatManagerModule.class, context);
        this.addModule(modules, BuglyModule.class, context);
        this.addModule(modules, GallerySaverModule.class, context);
        this.addModule(modules, WebViewUtilsModule.class, context);
        this.addModule(modules, ShareModule.class, context);
        this.addModule(modules, AliPayModule.class, context);
        this.addModule(modules, UrlLauncherModule.class, context);
        this.addModule(modules, PdfModule.class, context);
        this.addModule(modules, AbcAsrModule.class, context);

        return modules;
    }


    private void addModule(Map<Class<? extends HippyNativeModuleBase>, Provider<? extends HippyNativeModuleBase>> modules, final Class<? extends HippyNativeModuleBase> clz, final HippyEngineContext context) {
        modules.put(clz, new Provider<HippyNativeModuleBase>() {
            @Override
            public HippyNativeModuleBase get() {
                try {
                    Constructor<?> constructor = clz.getConstructor(HippyEngineContext.class);
                    return (HippyNativeModuleBase) constructor.newInstance(context);
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InstantiationException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }

                return null;
            }
        });
    }

    /**
     * 接口：Java层用来调用JavaScript里的同名接口
     */
    @Override
    public List<Class<? extends HippyJavaScriptModule>> getJavaScriptModules() {
        return null;
    }

    /**
     * 接口：用来构造各种JavaScript需要的自定义的View组件
     */
    @Override
    public List<Class<? extends HippyViewController>> getControllers() {
        List<Class<? extends HippyViewController>> components = new ArrayList<>();
        components.add(ReverseListViewController.class);
        components.add(ReverseListItemViewController.class);
        components.add(TouchListenerViewController.class);
        components.add(ABCNavigatorViewController.class);
        components.add(ABCNavigatorItemViewController.class);
        components.add(ABCAudioViewController.class);
        components.add(WheelViewController.class);
        components.add(AbcTextInputViewController.class);
        components.add(AbcTextInputImplController.class);
        components.add(AbcInputAccessoryViewController.class);
        components.add(AbcCustomKeyboardViewController.class);
        components.add(WillPopListenerController.class);
        components.add(LineChartViewController.class);
        components.add(PieChartViewController.class);
        components.add(QRViewController.class);
        components.add(CaptureViewController.class);
        components.add(AbcWebViewController.class);
        components.add(ABCBlurViewController.class);
        components.add(AbcLinearGradientViewController.class);
        components.add(PinchImageViewController.class);
        components.add(SVGViewController.class);
        components.add(AbcScrollViewController.class);
        components.add(MarkdownViewController.class);

        return components;
    }
}
