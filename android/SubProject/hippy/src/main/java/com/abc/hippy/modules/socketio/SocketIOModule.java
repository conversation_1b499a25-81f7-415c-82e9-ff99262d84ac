package com.abc.hippy.modules.socketio;

import android.text.TextUtils;

import com.abc.common.utils.LogUtils;
import com.abc.hippy.modules.HostHippyMessageBridge;
import com.abc.hippy.utils.ArgumentUtils;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import org.json.JSONObject;

import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.socket.client.IO;
import io.socket.client.Manager;
import io.socket.client.Socket;
import io.socket.emitter.Emitter;
import io.socket.engineio.client.Transport;


@HippyNativeModule(name = SocketIOModule.CLASSNAME)
public class SocketIOModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "SocketIO";
    private static final String TAG = "SocketIOModule";
    private HashMap<String, Socket> mSessions = new HashMap<>();

    public SocketIOModule(HippyEngineContext context) {
        super(context);
    }

    @HippyMethod(name = "create")
    public void create(String sessionId, String url, HippyMap params, Promise promise) {
        String kCookie = "cookies";
        String kNamespace = "namespace";

        try {
            boolean forceWebsockets = params.getBoolean("forceWebsockets");
            final String cookies = params.getString(kCookie);

            final String namespace = params.getString(kNamespace);

            final HippyMap extraHeaders = params.getMap("extraHeaders");
            int reconnectionDelay = -1;
            if (params.get("reconnectionDelay") != null) {
                reconnectionDelay = params.getInt("reconnectionDelay");
            }

            IO.Options options = new IO.Options();
            if (forceWebsockets) {
                options.transports = new String[]{"websocket"};
            }
            if (reconnectionDelay > 0)
                options.reconnectionDelay = reconnectionDelay;

            if (params.get("path") != null) {
                options.path = params.getString("path");
            }

            if (!TextUtils.isEmpty(namespace)) {
                url = url + namespace;
            }

            LogUtils.d(TAG, "create() called with: sessionId = [" + sessionId + "], url = [" + url + "], params = [" + params + "], promise = [" + promise + "], path = [" + options.path + "]");
            Socket socket = IO.socket(url, options);
            // Called upon transport creation.
            socket.io().on(Manager.EVENT_TRANSPORT, new Emitter.Listener() {
                @Override
                public void call(Object... args) {
                    Transport transport = (Transport) args[0];

                    transport.on(Transport.EVENT_REQUEST_HEADERS, new Emitter.Listener() {
                        @Override
                        public void call(Object... args) {
                            @SuppressWarnings("unchecked")
                            Map<String, List<String>> headers = (Map<String, List<String>>) args[0];
                            // modify request headers
                            if (!TextUtils.isEmpty(cookies))
                                headers.put("Cookie", Arrays.asList(cookies));

                            if (extraHeaders != null) {
                                for (String key : extraHeaders.keySet()) {
                                    headers.put(key, Arrays.asList(extraHeaders.get(key).toString()));
                                }
                            }
                        }
                    });

                    transport.on(Transport.EVENT_RESPONSE_HEADERS, new Emitter.Listener() {
                        @Override
                        public void call(Object... args) {
//                            @SuppressWarnings("unchecked")
//                            Map<String, List<String>> headers = (Map<String, List<String>>) args[0];
                            // access response headers
//                            String cookie = headers.get("Set-Cookie").get(0);
                        }
                    });
                }
            });
            this.mSessions.put(sessionId, socket);
        } catch (URISyntaxException e) {
            e.printStackTrace();
            promise.resolve(e.getMessage());
        }

        promise.resolve(true);
    }

    @HippyMethod(name = "connect")
    public void connect(String sessionId, Promise promise) {
        LogUtils.d(TAG, "connect() called with: sessionId = [" + sessionId + "], promise = [" + promise + "]");
        Socket socket = this.mSessions.get(sessionId);
        if (socket == null) {
            promise.reject("在connect之前请先create");
            return;
        }

        socket.connect();
        promise.resolve(true);
    }

    @HippyMethod(name = "on")
    public void on(final String sessionId, final String eventName, Promise promise) {
        LogUtils.d(TAG, "on() called with: sessionId = [" + sessionId + "], eventName = [" + eventName + "], promise = [" + promise + "]");
        Socket socket = this.mSessions.get(sessionId);
        if (socket == null) {
            promise.reject("请先create");
            return;
        }

        socket.on(eventName, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                LogUtils.d(TAG, eventName + "call() called with: args = [" + args + "]");
                HippyMap body = new HippyMap();
                body.pushString("sessionId", sessionId);
                body.pushString("name", eventName);
                if (args != null && args.length > 0) {
                    try {
                        ArgumentUtils.parseObjectGotoMap(body, "items", args);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                HostHippyMessageBridge.getInstance().onHostMessage("socketIOOnEvent", body);
            }
        });

        promise.resolve(true);
    }


    @HippyMethod(name = "off")
    public void off(String sessionId, String eventName, Promise promise) {
        LogUtils.d(TAG, "on() called with: sessionId = [" + sessionId + "], eventName = [" + eventName + "], promise = [" + promise + "]");
        Socket socket = this.mSessions.get(sessionId);
        if (socket == null) {
            promise.reject("请先create");
            return;
        }

        socket.off(eventName);

        promise.resolve(true);
    }

    @HippyMethod(name = "emit")
    public void emit(String sessionId, String eventName, HippyMap params, Promise promise) {
        Socket socket = this.mSessions.get(sessionId);
        if (socket == null) {
            promise.reject("请先create");
            return;
        }
        try {
            String json = ArgumentUtils.objectToJson(params);
            socket.emit(eventName, new JSONObject(json));
        } catch (Exception e) {
            e.printStackTrace();
        }

        promise.resolve(true);
    }

    @HippyMethod(name = "disconnect")
    public void disconnect(String sessionId, Promise promise) {
        Socket socket = this.mSessions.get(sessionId);
        if (socket == null) {
            promise.reject("请先create");
            return;
        }

        socket.disconnect();
        this.mSessions.remove(sessionId);
        promise.resolve(true);
    }
}
