package com.abc.hippy.view.abcnavigator;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.views.view.HippyViewGroupController;

@HippyController(name = ABCNavigatorItemViewController.CLASS_NAME)
public class ABCNavigatorItemViewController extends HippyViewGroupController {
    static final String CLASS_NAME = "ABCNavigatorItem";

    @Override
    protected View createViewImpl(Context context) {
        return new ABCNavigatorItemView(context);
    }


    @HippyControllerProps(name = "transitionType", defaultNumber = 0, defaultType = HippyControllerProps.NUMBER)
    public void setTransitionType(ABCNavigatorItemView view, int transitionType) {
        view.setTransitionType(transitionType);
    }

    @HippyControllerProps(name = "enableBackgroundAnimation", defaultBoolean = false, defaultType = HippyControllerProps.BOOLEAN)
    public void setEnableBackgroundAnimation(ABCNavigatorItemView view, boolean animation) {
        view.setEnableBackgroundAnimation(animation);
    }

    @HippyControllerProps(name = "enableGestureRecognizer", defaultBoolean = true, defaultType = HippyControllerProps.BOOLEAN)
    public void setEnableGestureRecognizer(ABCNavigatorItemView view, boolean enableGestureRecognizer) {
        view.setGestureRecognizerEnable(enableGestureRecognizer);
    }
}
