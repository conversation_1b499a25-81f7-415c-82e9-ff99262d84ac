package com.abc.hippy.modules;


import com.tencent.mtt.hippy.common.HippyMap;

import java.util.ArrayList;
import java.util.Map;

/**
 * 中转flutter到hippy双向事件
 */
public class HostHippyMessageBridge {

    public interface IHostMessageListener {
        void onHostMessage(String messageName, HippyMap body);
    }


    public interface IReloadRequestListener {
        void onHippyRequestInvokeHostMethod(String methodName, Map<String, Object> params);
    }


    private static HostHippyMessageBridge gInstance;

    public static synchronized HostHippyMessageBridge getInstance() {
        if (gInstance == null)
            gInstance = new HostHippyMessageBridge();

        return gInstance;
    }

    private ArrayList<IHostMessageListener> mHostMessageListeners = new ArrayList<>();

    private ArrayList<IReloadRequestListener> mReloadRequestListener = new ArrayList<>();


    public synchronized void addHostMessageListener(IHostMessageListener listener) {
        if (!mHostMessageListeners.contains(listener))
            mHostMessageListeners.add(listener);
    }

    public synchronized void removeHostMessageListener(IHostMessageListener listener) {
        mHostMessageListeners.remove(listener);
    }


    public synchronized void addHippyReloadRequestListener(IReloadRequestListener listener) {
        if (!mReloadRequestListener.contains(listener))
            mReloadRequestListener.add(listener);
    }

    public synchronized void removeHippyReloadRequestListener(IReloadRequestListener listener) {
        mReloadRequestListener.remove(listener);
    }

    public void onHostMessage(String message, HippyMap body) {
        for (IHostMessageListener listener : this.mHostMessageListeners) {
            listener.onHostMessage(message, body);
        }
    }

    public void onHippyRequestInvokeHostMethod(String methodName, Map<String, Object> params) {
        for (IReloadRequestListener listener : this.mReloadRequestListener) {
            listener.onHippyRequestInvokeHostMethod(methodName, params);
        }
    }
}
