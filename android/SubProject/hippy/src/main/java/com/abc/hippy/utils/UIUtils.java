package com.abc.hippy.utils;

import android.content.Context;
import android.graphics.Rect;
import android.view.Display;
import android.view.View;
import android.view.ViewParent;

import com.abc.common.utils.DeviceUtils;
import com.tencent.mtt.hippy.utils.ContextHolder;
import com.tencent.mtt.hippy.utils.LogUtils;

public class UIUtils {
    private static Rect mRect = new Rect();

    /**
     * 返回RootView的高度,要注意即使全屏,他應該也少了一個狀態欄的高度
     */
    public static int getRootViewHeight(View view) {
        int height = -1;
        View rootView = view.getRootView().findViewById(android.R.id.content);
        if (rootView != null)
            return rootView.getHeight();

        return -1;
//        if (rootView == null) {
//            return height;
//        }
//        // 问题ID： 106874510 某些奇葩手机ROM调用此方法会报错，做下捕获吧
//        try {
//            rootView.getWindowVisibleDisplayFrame(mRect);
//        } catch (Throwable e) {
//            LogUtils.d("InputMethodStatusMonitor:", "getWindowVisibleDisplayFrame failed !" + e);
//            e.printStackTrace();
//        }
//
//        int visibleHeight = mRect.bottom - mRect.top;
//        if (visibleHeight < 0) {
//            return -1;
//        }
//        return visibleHeight;
    }

    public static <T extends View> T findNearestParentView(View view, Class<T> clz) {
        View current = view;
        do {

            if (clz.isInstance(current)) {
                return (T) current;
            }
            ViewParent parent = current.getParent();
            if (!(parent instanceof View)) break;

            current = (View) parent;
        } while (true);

        return null;
    }
}
