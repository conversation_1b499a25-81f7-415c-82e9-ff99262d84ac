/* <PERSON><PERSON> is pleased to support the open source community by making Hippy available.
 * Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.abc.hippy.view.wheelview.events;

import android.view.View;

import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.uimanager.HippyViewEvent;


public class WheelViewSelectChanged extends HippyViewEvent {
    public static final String EVENT_NAME = "onSelectChanged";

    private View mTarget;

    public WheelViewSelectChanged(View target) {
        super(EVENT_NAME);
        mTarget = target;
    }

    public void send(int position) {
        HippyMap map = new HippyMap();
        map.pushInt("index", position);
        super.send(mTarget, map);
    }
}
