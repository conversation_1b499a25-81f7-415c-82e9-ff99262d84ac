package com.abc.hippy.view.charts.piechartview;

import android.content.Context;
import android.graphics.Color;

import com.abc.common.utils.LogUtils;
import com.abc.hippy.view.charts.piechartview.events.PieChartViewSelected;
import com.github.mikephil.charting.animation.Easing;
import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.listener.OnChartValueSelectedListener;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.uimanager.HippyViewBase;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;

import java.util.ArrayList;

public class PieChartView extends PieChart implements HippyViewBase {
    private static final String TAG = "PieChartView";
    private NativeGestureDispatcher mGestureDispatcher;
    private HippyArray mData = new HippyArray();

    private boolean mDrawValuesEnable = false;

    private PieChartViewSelected mItemSelectedEmitter = new PieChartViewSelected(this);

    public PieChartView(Context context) {
        super(context);

        PieChart chart = this;

        chart.setNoDataText("无内容显示");
        chart.getDescription().setEnabled(false);    //设置pieChart图表的描述
        chart.setBackgroundColor(Color.TRANSPARENT);      //设置pieChart图表背景色
        chart.setRotationEnabled(false);             //可以手动旋转
        chart.setDragDecelerationFrictionCoef(0.95f);//设置pieChart图表转动阻力摩擦系数[0,1]
        chart.setHighlightPerTapEnabled(true);       //设置piecahrt图表点击Item高亮是否可用
        chart.setDrawCenterText(false);               // 设置中心文字
        chart.animateY(800, Easing.EaseInOutQuad);// 设置pieChart图表展示动画效果
        chart.setDrawEntryLabels(false);    //显示扇形区域文字-label
        chart.setHoleRadius(80); //设置内外圆环比例

        Legend l = this.getLegend();
        l.setEnabled(false);                    //是否启用图列（true：下面属性才有意义
        l.setVerticalAlignment(Legend.LegendVerticalAlignment.TOP);
        l.setHorizontalAlignment(Legend.LegendHorizontalAlignment.RIGHT);
        l.setOrientation(Legend.LegendOrientation.VERTICAL);
        l.setForm(Legend.LegendForm.DEFAULT); //设置图例的形状
        l.setFormSize(1f);                      //设置图例的大小
        l.setFormToTextSpace(0f);              //设置每个图例实体中标签和形状之间的间距
        l.setDrawInside(false);
        l.setWordWrapEnabled(false);              //设置图列换行(注意使用影响性能,仅适用legend位于图表下面)
//        l.setXEntrySpace(0f);                  //设置图例实体之间延X轴的间距（setOrientation = HORIZONTAL有效）
//        l.setYEntrySpace(0f);                  //设置图例实体之间延Y轴的间距（setOrientation = VERTICAL 有效）
//        l.setYOffset(0f);                      //设置比例块Y轴偏移量
//        l.setTextSize(0f);                      //设置图例标签文本的大小
//        l.setTextColor(Color.parseColor("#333333"));//设置图例标签文本的颜色


        //监听点击事件
        chart.setOnChartValueSelectedListener(new OnChartValueSelectedListener() {
            @Override
            public void onValueSelected(Entry entry, Highlight highlight) {
                int index = (int) highlight.getX();
                HippyMap item = (HippyMap) mData.get(index);
                mItemSelectedEmitter.send(item);
            }

            @Override
            public void onNothingSelected() {
                LogUtils.d("***********", "关闭");
            }
        });
    }

    @Override
    public NativeGestureDispatcher getGestureDispatcher() {
        return mGestureDispatcher;
    }

    @Override
    public void setGestureDispatcher(NativeGestureDispatcher dispatcher) {
        this.mGestureDispatcher = dispatcher;
    }

    public void setData(HippyArray items) {
        this.mData = items;

        ArrayList<PieEntry> pieEntryList = new ArrayList();//数据列表
        ArrayList<Integer> colors = new ArrayList();//颜色列表

        for (int i = 0; i < items.size(); ++i) {
            HippyMap item = (HippyMap) items.get(i);
            String label = item.getString("label");
            int value = item.getInt("value");
            int color = item.getInt("color");
            colors.add(color);
            pieEntryList.add(new PieEntry(value, label, item));
        }

        PieDataSet pieDataSet = new PieDataSet(pieEntryList, "图表名称");
        pieDataSet.setSliceSpace(3f);           //设置饼状Item之间的间隙
        pieDataSet.setSelectionShift(0f);      //设置饼状Item被选中时变化的距离
        pieDataSet.setColors(colors);           //为DataSet中的数据匹配上颜色集(饼图Item颜色)
        //最终数据 PieData
        PieData pieData = new PieData(pieDataSet);
        pieData.setDrawValues(this.mDrawValuesEnable);            //设置是否显示数据实体(百分比，true:以下属性才有意义)
//        pieData.setValueTextColor(Color.BLUE);  //设置所有DataSet内数据实体（百分比）的文本颜色
//        pieData.setValueTextSize(12f);          //设置所有DataSet内数据实体（百分比）的文本字体大小
//        pieData.setValueFormatter(new PercentFormatter());//设置所有DataSet内数据实体（百分比）的文本字体格式

        this.setData(pieData);
        this.highlightValues(null);
        this.invalidate();                    //将图表重绘以显示设置的属性和数据
    }

    public void _setPieChartPercent(int percent) {
        int _percent = 80;
        if (percent != 0){
            _percent = percent*100;
        }
       this.setHoleRadius(Math.min(100, _percent));
    }

    public void _setDrawValuesEnabled(boolean status) {
       this.mDrawValuesEnable = status;
       PieData pieData = this.getData();
       this.setData(pieData);
    }

    public void _setDrawEntryLabelsEnabled(boolean status) {
       this.setDrawEntryLabels(status);
    }

    public void _setRotationEnabled(boolean status) {
      this.setRotationEnabled(status);
    }
}
