package com.abc.hippy.modules.pdf;

import android.content.Intent;
import android.net.Uri;

import com.abc.common.utils.ContextHolder;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import java.util.Objects;

@HippyNativeModule(name = PdfModule.CLASSNAME)
public class PdfModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "Pdf";

    public PdfModule(HippyEngineContext context) {
        super((context));
    }

    @HippyMethod(name = "open")
    public void open(HippyMap map, Promise promise) {
        final String url = map.getString("url");
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setData(Uri.parse(url));
        Objects.requireNonNull(ContextHolder.getMainActivity()).startActivity(intent);
        promise.resolve(true);
    }
}
