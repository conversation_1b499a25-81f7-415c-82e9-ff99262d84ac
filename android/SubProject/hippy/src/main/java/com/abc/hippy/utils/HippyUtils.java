package com.abc.hippy.utils;

import android.graphics.Typeface;

import com.abc.common.utils.ICommonCallback;
import com.tencent.mtt.hippy.modules.Promise;

public class HippyUtils {
    public static ICommonCallback createCommonHippyInvokeResultHandler(final Promise promise) {
        return new ICommonCallback() {
            @Override
            public void onCallback(Object obj, Exception error) {
                if (error != null) {
                    promise.reject(error.getMessage());
                    return;
                }
                promise.resolve(obj);
            }
        };
    }


    public static Typeface createTypeface(String fontFamily, String fontWeightString) {
        int fontWeightNumeric = fontWeightString != null ? parseFontWeight(fontWeightString) : -1;
        int fontWeight = -1;
        if (fontWeightNumeric >= 500 || "bold".equals(fontWeight)) {
            fontWeight = Typeface.BOLD;
        } else if ("normal".equals(fontWeightString) || fontWeightNumeric != -1) {
            fontWeight = Typeface.NORMAL;
        }

        return Typeface.create(fontFamily, fontWeight);
    }

    private static int parseFontWeight(String fontWeightString) {
        // This should be much faster than using regex to verify input and Integer.parseInt
        return fontWeightString.length() == 3 && fontWeightString.endsWith("00") && fontWeightString.charAt(0) <= '9'
                && fontWeightString.charAt(0) >= '1' ? 100 * (fontWeightString.charAt(0) - '0') : -1;
    }

}
