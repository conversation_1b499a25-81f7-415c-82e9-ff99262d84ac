package com.abc.hippy.view.list;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.HippyRootView;
import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.uimanager.ControllerManager;
import com.tencent.mtt.hippy.uimanager.HippyViewController;
import com.tencent.mtt.hippy.uimanager.ListItemRenderNode;
import com.tencent.mtt.hippy.uimanager.RenderNode;
import com.tencent.mtt.hippy.views.list.HippyListItemView;

/**
 * Copyright (C) 2022 ABCYun
 * FileName: ReverseListItemViewController
 * Author: jianglei
 * Date: 2022/6/6
 * Description:
 */

@HippyController(name = ReverseListItemViewController.CLASS_NAME, isLazyLoad = true)
public class ReverseListItemViewController extends HippyViewController<HippyListItemView> {
    public static final String CLASS_NAME = "ReverseListViewItem";

    @Override
    protected View createViewImpl(Context context) {
        return new HippyListItemView(context);
    }

    @Override
    public RenderNode createRenderNode(int id, HippyMap props, String className, HippyRootView hippyRootView, ControllerManager controllerManager, boolean lazy) {
        return new ListItemRenderNode(id, props, className, hippyRootView, controllerManager, lazy);
    }
}