package com.abc.hippy.modules.urllauncher;

import android.os.Bundle;

import com.abc.common.utils.ContextHolder;
import com.abc.hippy.utils.ArgumentUtils;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import java.util.Map;


@HippyNativeModule(name = UrlLauncherModule.CLASSNAME)
public class UrlLauncherModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "UrlLauncher";

    private UrlLauncher urlLauncher;
    public UrlLauncherModule(HippyEngineContext context) {
        super((context));
        this.urlLauncher = new UrlLauncher(ContextHolder.getAppContext(), ContextHolder.getMainActivity());
    }


    @HippyMethod(name = "canLaunch")
    public void canLaunch(HippyMap map, Promise promise) {
        final String url = map.getString("url");
        promise.resolve(this.urlLauncher.canLaunch(url));
    }


    @HippyMethod(name = "launch")
    public void launch(HippyMap map, Promise promise) {
        final String url = map.getString("url");

        final boolean useWebView =  map.getBoolean("useWebView");
        final boolean enableJavaScript =  map.getBoolean("enableJavaScript");
        final boolean enableDomStorage = map.getBoolean("enableDomStorage");
        HippyMap headers = map.getMap("headers");
        final Map<String, Object> headersMap = ArgumentUtils.mapFromHippyMap(headers);
        final Bundle headersBundle = extractBundle(headersMap);

        UrlLauncher.LaunchStatus launchStatus =
                urlLauncher.launch(url, headersBundle, useWebView, enableJavaScript, enableDomStorage);

        if (launchStatus == UrlLauncher.LaunchStatus.NO_ACTIVITY) {
            promise.reject("NO_ACTIVITY, Launching a URL requires a foreground activity.");
        } else if (launchStatus == UrlLauncher.LaunchStatus.ACTIVITY_NOT_FOUND) {
            promise.reject("ACTIVITY_NOT_FOUND," + String.format("No Activity found to handle intent { %s }", url));
        } else {
            promise.resolve(true);
        }
    }

    @HippyMethod(name = "closeWebView")
    public void closeWebView(HippyMap map, Promise promise) {
        final String url = map.getString("url");
        promise.resolve(this.urlLauncher.canLaunch(url));
    }

    private static Bundle extractBundle(Map<String, Object> headersMap) {
        final Bundle headersBundle = new Bundle();
        if (headersMap== null)
            return headersBundle;
        for (String key : headersMap.keySet()) {
            final Object value = headersMap.get(key);
            headersBundle.putString(key, value!=null?value.toString():"");
        }
        return headersBundle;
    }
}
