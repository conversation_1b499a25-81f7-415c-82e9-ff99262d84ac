package com.abc.hippy.view.svgview;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.uimanager.HippyViewController;


@HippyController(name = "SVGView")
public class SVGViewController extends HippyViewController<SVGView> {
    @Override
    protected View createViewImpl(Context context) {
        return new SVGView(context);
    }

    @HippyControllerProps(name = "content")
    public void setContent(SVGView chartView, String content) {
        chartView.setContent(content);
    }
}
