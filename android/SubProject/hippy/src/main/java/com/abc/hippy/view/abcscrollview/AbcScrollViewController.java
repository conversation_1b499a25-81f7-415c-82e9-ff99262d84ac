package com.abc.hippy.view.abcscrollview;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.abc.hippy.view.common.HippyNestedScrollComponent;
import com.abc.hippy.view.common.HippyNestedScrollHelper;
import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.uimanager.HippyGroupController;
import com.tencent.mtt.hippy.utils.PixelUtil;

@SuppressWarnings({"deprecation", "unused", "rawtypes"})
@HippyController(name = AbcScrollViewController.CLASS_NAME)
public class AbcScrollViewController<T extends ViewGroup & AbcScrollView> extends
    HippyGroupController {

  protected static final String SCROLL_TO = "scrollTo";
  private static final String SCROLL_TO_WITHOPTIONS = "scrollToWithOptions";

  public static final String CLASS_NAME = "AbcScrollView";


  @Override
  protected View createViewImpl(Context context, HippyMap iniProps) {
    boolean enableScrollEvent = false;
    boolean isHorizontal = false;
    if (iniProps != null) {
      isHorizontal = iniProps.getBoolean("horizontal");
      enableScrollEvent = iniProps.getBoolean("onScroll");
    }

    View scrollView;
    if (isHorizontal) {
      scrollView = new AbcHorizontalScrollView(context);
    } else {
      scrollView = new AbcVerticalScrollView(context);
    }
    ((AbcScrollView)scrollView).setScrollEventEnable(enableScrollEvent);
    return scrollView;
  }

  @Override
  protected View createViewImpl(Context context) {
    return null;
  }

  @HippyControllerProps(name = "scrollEnabled", defaultType = HippyControllerProps.BOOLEAN, defaultBoolean = true)
  public void setScrollEnabled(AbcScrollView view, boolean flag) {
    view.setScrollEnabled(flag);
  }

  @HippyControllerProps(name = "showScrollIndicator", defaultType = HippyControllerProps.BOOLEAN)
  public void setShowScrollIndicator(AbcScrollView view, boolean flag) {
    view.showScrollIndicator(flag);
  }

  @HippyControllerProps(name = "onScrollBeginDrag", defaultType = HippyControllerProps.BOOLEAN)
  public void setScrollBeginDragEventEnable(AbcScrollView view, boolean flag) {
    view.setScrollBeginDragEventEnable(flag);
  }

  @HippyControllerProps(name = "onScrollEndDrag", defaultType = HippyControllerProps.BOOLEAN)
  public void setScrollEndDragEventEnable(AbcScrollView view, boolean flag) {
    view.setScrollEndDragEventEnable(flag);
  }

  @HippyControllerProps(name = "onMomentumScrollBegin", defaultType = HippyControllerProps.BOOLEAN)
  public void setMomentumScrollBeginEventEnable(AbcScrollView view, boolean flag) {
    view.setMomentumScrollBeginEventEnable(flag);
  }

  @HippyControllerProps(name = "onMomentumScrollEnd", defaultType = HippyControllerProps.BOOLEAN)
  public void setMomentumScrollEndEventEnable(AbcScrollView view, boolean flag) {
    view.setMomentumScrollEndEventEnable(flag);
  }

  @HippyControllerProps(name = "flingEnabled", defaultType = HippyControllerProps.BOOLEAN, defaultBoolean = true)
  public void setFlingEnabled(AbcScrollView view, boolean flag) {
    view.setFlingEnabled(flag);
  }

  @HippyControllerProps(name = "contentOffset4Reuse")
  public void setContentOffset4Reuse(AbcScrollView view, HippyMap offsetMap) {
    view.setContentOffset4Reuse(offsetMap);
  }

  @HippyControllerProps(name = "pagingEnabled", defaultType = HippyControllerProps.BOOLEAN)
  public void setPagingEnabled(AbcScrollView view, boolean pagingEnabled) {
    view.setPagingEnabled(pagingEnabled);
  }

  @HippyControllerProps(name = "scrollEventThrottle", defaultType = HippyControllerProps.NUMBER, defaultNumber = 30.0D)
  public void setScrollEventThrottle(AbcScrollView view, int scrollEventThrottle) {
    view.setScrollEventThrottle(scrollEventThrottle);
  }

  @HippyControllerProps(name = "scrollMinOffset", defaultType = HippyControllerProps.NUMBER, defaultNumber = 5)
  public void setScrollMinOffset(AbcScrollView view, int scrollMinOffset) {
    view.setScrollMinOffset(scrollMinOffset);
  }

  @HippyControllerProps(name = "initialContentOffset", defaultType = HippyControllerProps.NUMBER, defaultNumber = 0)
  public void setInitialContentOffset(AbcScrollView view, int offset) {
    view.setInitialContentOffset((int)PixelUtil.dp2px(offset));
  }

  @HippyControllerProps(name = "scrollOffsetSyncGroup", defaultType = HippyControllerProps.STRING, defaultString = "")
  public void setScrollOffsetSyncGroup(AbcScrollView view, String groupName) {
    view.setScrollOffsetSyncGroup(groupName);
  }


  @HippyControllerProps(name = HippyNestedScrollComponent.PROP_PRIORITY, defaultType =
          HippyControllerProps.STRING, defaultString = HippyNestedScrollComponent.PRIORITY_SELF)
  public void setNestedScrollPriority(T view, String priorityName) {
    if (view instanceof HippyNestedScrollComponent) {
      HippyNestedScrollComponent sc = (HippyNestedScrollComponent) view;
      HippyNestedScrollComponent.Priority priority = HippyNestedScrollHelper.priorityOf(priorityName);
      sc.setNestedScrollPriority(HippyNestedScrollComponent.DIRECTION_ALL, priority);
    }
  }

  @HippyControllerProps(name = HippyNestedScrollComponent.PROP_LEFT_PRIORITY, defaultType =
          HippyControllerProps.STRING, defaultString = HippyNestedScrollComponent.PRIORITY_SELF)
  public void setNestedScrollLeftPriority(T view, String priorityName) {
    if (view instanceof HippyNestedScrollComponent) {
      HippyNestedScrollComponent.Priority priority = HippyNestedScrollHelper.priorityOf(priorityName);
      ((HippyNestedScrollComponent) view).setNestedScrollPriority(HippyNestedScrollComponent.DIRECTION_LEFT, priority);
    }
  }

  @HippyControllerProps(name = HippyNestedScrollComponent.PROP_TOP_PRIORITY, defaultType =
          HippyControllerProps.STRING, defaultString = HippyNestedScrollComponent.PRIORITY_SELF)
  public void setNestedScrollTopPriority(T view, String priorityName) {
    if (view instanceof HippyNestedScrollComponent) {
      HippyNestedScrollComponent.Priority priority = HippyNestedScrollHelper.priorityOf(priorityName);
      ((HippyNestedScrollComponent) view).setNestedScrollPriority(HippyNestedScrollComponent.DIRECTION_TOP, priority);
    }
  }

  @HippyControllerProps(name = HippyNestedScrollComponent.PROP_RIGHT_PRIORITY, defaultType =
          HippyControllerProps.STRING, defaultString = HippyNestedScrollComponent.PRIORITY_SELF)
  public void setNestedScrollRightPriority(T view, String priorityName) {
    if (view instanceof HippyNestedScrollComponent) {
      HippyNestedScrollComponent.Priority priority = HippyNestedScrollHelper.priorityOf(priorityName);
      ((HippyNestedScrollComponent) view).setNestedScrollPriority(HippyNestedScrollComponent.DIRECTION_RIGHT, priority);
    }
  }

  @HippyControllerProps(name = HippyNestedScrollComponent.PROP_BOTTOM_PRIORITY, defaultType =
          HippyControllerProps.STRING, defaultString = HippyNestedScrollComponent.PRIORITY_SELF)
  public void setNestedScrollBottomPriority(T view, String priorityName) {
    if (view instanceof HippyNestedScrollComponent) {
      HippyNestedScrollComponent.Priority priority = HippyNestedScrollHelper.priorityOf(priorityName);
      ((HippyNestedScrollComponent) view).setNestedScrollPriority(HippyNestedScrollComponent.DIRECTION_BOTTOM, priority);
    }
  }

  @Override
  public void onBatchComplete(View view) {
    super.onBatchComplete(view);

    if (view instanceof AbcScrollView) {
      ((AbcScrollView)view).scrollToInitContentOffset();
    }
  }

  @Override
  public void dispatchFunction(View view, String functionName, HippyArray args) {
    //noinspection unchecked
    super.dispatchFunction(view, functionName, args);
    if (view instanceof AbcScrollView) {

      if (TextUtils.equals(SCROLL_TO, functionName)) {
        int destX = Math.round(PixelUtil.dp2px(args.getDouble(0)));
        int destY = Math.round(PixelUtil.dp2px(args.getDouble(1)));
        boolean animated = args.getBoolean(2);

        if (animated) {
          ((AbcScrollView) view).callSmoothScrollTo(destX, destY, 0);//用默认的动画事件
        } else {
          view.scrollTo(destX, destY);
        }
      }
      if (TextUtils.equals(SCROLL_TO_WITHOPTIONS, functionName) && args != null
          && args.size() > 0) {
        HippyMap hippyMap = args.getMap(0); //取第一个元素
        int destX = Math.round(PixelUtil.dp2px(hippyMap.getInt("x")));
        int destY = Math.round(PixelUtil.dp2px(hippyMap.getInt("y")));
        int duration = hippyMap.getInt("duration");
        if (duration > 0) {
          ((AbcScrollView) view).callSmoothScrollTo(destX, destY, duration);//用默认的动画事件
        } else {
          view.scrollTo(destX, destY);
        }
      }
    }
  }


}
