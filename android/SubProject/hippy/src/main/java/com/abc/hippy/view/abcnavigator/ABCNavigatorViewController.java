package com.abc.hippy.view.abcnavigator;

import android.animation.Animator;
import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.uimanager.HippyGroupController;

@HippyController(name = ABCNavigatorViewController.CLASS_NAME)
public class ABCNavigatorViewController extends HippyGroupController<ABCNavigatorView> {
    static final String CLASS_NAME = "ABCNavigator";

    private final static String POP = "pop";

    @Override
    protected View createViewImpl(Context context) {
        return new ABCNavigatorView(context);
    }

    @HippyControllerProps(name = "onKeyBack", defaultType = HippyControllerProps.BOOLEAN, defaultBoolean = false)
    public void setOnKeyBackEnable(ABCNavigatorView view, boolean enable) {
        view.setOnKeyBackEnable(enable);
    }

    @Override
    public void dispatchFunction(final ABCNavigatorView view, String functionName, HippyArray params, final Promise promise) {

        switch (functionName) {
            case POP: {
                Object animateObj = params.get(0);
                boolean animate = animateObj instanceof Boolean ? (Boolean) animateObj : true;
                view.pop(animate, new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animator) {

                    }

                    @Override
                    public void onAnimationEnd(Animator animator) {
                        promise.resolve(true);
                    }

                    @Override
                    public void onAnimationCancel(Animator animator) {
                        promise.resolve(true);
                    }

                    @Override
                    public void onAnimationRepeat(Animator animator) {

                    }
                });
            }
            break;

            default:
                break;
        }
    }
}
