package com.abc.hippy.modules.push;

import com.abc.hippy.utils.HippyUtils;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;


@HippyNativeModule(name = PushModule.CLASSNAME)
public class PushModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "Push";

    public PushModule(HippyEngineContext context) {
        super(context);
    }

    @HippyMethod(name = "start")
    public void start(HippyMap params, Promise promise) {
        PushManager.getInstance().startWork(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "getDeviceToken")
    public void getDeviceToken(HippyMap params, Promise promise) {
        PushManager.getInstance().getDeviceToken(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "getPushChannelId")
    public void getPushChannelId(HippyMap params, Promise promise) {
        PushManager.getInstance().getPushChannelId(params, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }
}
