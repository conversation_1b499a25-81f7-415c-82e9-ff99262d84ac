package com.abc.hippy.modules.asr.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * ASR 配置类
 */
public class AsrConfig implements Serializable {
    // WebSocket 连接配置
    private String serverUrl;
    private String sessionId;
    private int connectTimeout = 10000; // 连接超时时间（毫秒）
    private int readTimeout = 30000; // 读取超时时间（毫秒）

    // 音频配置
    private int sampleRate = 16000; // 采样率
    private int channels = 1; // 声道数
    private int audioFormat = 16; // 音频格式（位深度）
    private int bufferSize = 3200; // 缓冲区大小 (100ms = 16000 * 1 * 2 / 10)
    private boolean enableAudioHeader = true; // 是否启用音频数据头部标识 (0xFF, 0xFE)

    // 识别配置
    private String language = "zh-CN"; // 语言
    private boolean enableVad = true; // 是否启用VAD
    private boolean enablePunctuation = true; // 是否启用标点符号
    private boolean enableNumberConvert = true; // 是否启用数字转换
    private boolean enableDirtyFilter = true; // 是否启用脏话过滤

    // 静音检测配置
    private boolean enableSilentDetection = false; // 是否启用静音检测
    private int silentTimeout = 5000; // 静音超时时间（毫秒）
    private float silentThreshold = -40.0f; // 静音阈值（分贝）

    // 音量回调配置
    private int volumeCallbackInterval = 100; // 音量回调间隔（毫秒）

    // 波形数据回调配置
    private int waveformCallbackInterval = 33; // 波形数据回调间隔（毫秒），默认30Hz

    // 前台服务配置
    private boolean enableForegroundService = true; // 是否启用前台服务
    private String serviceNotificationTitle = "语音识别服务"; // 服务通知标题
    private String serviceNotificationContent = "正在进行语音识别..."; // 服务通知内容

    // Socket.IO 连接配置
    private Map<String, String> extraHeaders = new HashMap<>(); // 额外的请求头
    private String cookies; // Cookie 字符串
    private String userAgent = "AbcAsrClient/android"; // 用户代理
    private boolean withCredentials = true; // 是否携带凭证
    private String namespace; // Socket.IO 命名空间
    private int reconnectionDelay = -1; // 重连延迟时间（毫秒）
    private boolean forceWebsockets = true; // 是否强制使用 WebSocket
    private String socketPath = "/api/asr/socket.io"; // Socket.IO 路径
    private Map<String, String> connectParams = new HashMap<>(); // 查询参数

    public Map<String, String> getConnectParams() {
        return connectParams;
    }

    public void setConnectParams(Map<String, String> connectParams) {
        this.connectParams = connectParams;
    }

    public AsrConfig() {
    }

    public AsrConfig(String serverUrl, String sessionId) {
        this.serverUrl = serverUrl;
        this.sessionId = sessionId;
    }

    // Getters and Setters
    public String getServerUrl() {
        return serverUrl;
    }

    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public int getSampleRate() {
        return sampleRate;
    }

    public void setSampleRate(int sampleRate) {
        this.sampleRate = sampleRate;
    }

    public int getChannels() {
        return channels;
    }

    public void setChannels(int channels) {
        this.channels = channels;
    }

    public int getAudioFormat() {
        return audioFormat;
    }

    public void setAudioFormat(int audioFormat) {
        this.audioFormat = audioFormat;
    }

    public int getBufferSize() {
        return bufferSize;
    }

    public void setBufferSize(int bufferSize) {
        this.bufferSize = bufferSize;
    }

    public boolean isEnableAudioHeader() {
        return enableAudioHeader;
    }

    public void setEnableAudioHeader(boolean enableAudioHeader) {
        this.enableAudioHeader = enableAudioHeader;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public boolean isEnableVad() {
        return enableVad;
    }

    public void setEnableVad(boolean enableVad) {
        this.enableVad = enableVad;
    }

    public boolean isEnablePunctuation() {
        return enablePunctuation;
    }

    public void setEnablePunctuation(boolean enablePunctuation) {
        this.enablePunctuation = enablePunctuation;
    }

    public boolean isEnableNumberConvert() {
        return enableNumberConvert;
    }

    public void setEnableNumberConvert(boolean enableNumberConvert) {
        this.enableNumberConvert = enableNumberConvert;
    }

    public boolean isEnableDirtyFilter() {
        return enableDirtyFilter;
    }

    public void setEnableDirtyFilter(boolean enableDirtyFilter) {
        this.enableDirtyFilter = enableDirtyFilter;
    }

    public boolean isEnableSilentDetection() {
        return enableSilentDetection;
    }

    public void setEnableSilentDetection(boolean enableSilentDetection) {
        this.enableSilentDetection = enableSilentDetection;
    }

    public int getSilentTimeout() {
        return silentTimeout;
    }

    public void setSilentTimeout(int silentTimeout) {
        this.silentTimeout = silentTimeout;
    }

    public float getSilentThreshold() {
        return silentThreshold;
    }

    public void setSilentThreshold(float silentThreshold) {
        this.silentThreshold = silentThreshold;
    }

    public int getVolumeCallbackInterval() {
        return volumeCallbackInterval;
    }

    public void setVolumeCallbackInterval(int volumeCallbackInterval) {
        this.volumeCallbackInterval = volumeCallbackInterval;
    }

    public int getWaveformCallbackInterval() {
        return waveformCallbackInterval;
    }

    public void setWaveformCallbackInterval(int waveformCallbackInterval) {
        this.waveformCallbackInterval = waveformCallbackInterval;
    }

    public boolean isEnableForegroundService() {
        return enableForegroundService;
    }

    public void setEnableForegroundService(boolean enableForegroundService) {
        this.enableForegroundService = enableForegroundService;
    }

    public String getServiceNotificationTitle() {
        return serviceNotificationTitle;
    }

    public void setServiceNotificationTitle(String serviceNotificationTitle) {
        this.serviceNotificationTitle = serviceNotificationTitle;
    }

    public String getServiceNotificationContent() {
        return serviceNotificationContent;
    }

    public void setServiceNotificationContent(String serviceNotificationContent) {
        this.serviceNotificationContent = serviceNotificationContent;
    }

    public Map<String, String> getExtraHeaders() {
        return extraHeaders;
    }

    public void setExtraHeaders(Map<String, String> extraHeaders) {
        this.extraHeaders = extraHeaders;
    }

    public void addExtraHeader(String key, String value) {
        if (this.extraHeaders == null) {
            this.extraHeaders = new HashMap<>();
        }
        this.extraHeaders.put(key, value);
    }

    public String getCookies() {
        return cookies;
    }

    public void setCookies(String cookies) {
        this.cookies = cookies;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public boolean isWithCredentials() {
        return withCredentials;
    }

    public void setWithCredentials(boolean withCredentials) {
        this.withCredentials = withCredentials;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public int getReconnectionDelay() {
        return reconnectionDelay;
    }

    public void setReconnectionDelay(int reconnectionDelay) {
        this.reconnectionDelay = reconnectionDelay;
    }

    public boolean isForceWebsockets() {
        return forceWebsockets;
    }

    public void setForceWebsockets(boolean forceWebsockets) {
        this.forceWebsockets = forceWebsockets;
    }

    public String getSocketPath() {
        return socketPath;
    }

    public void setSocketPath(String socketPath) {
        this.socketPath = socketPath;
    }

    @Override
    public String toString() {
        return "AsrConfig{" +
                "serverUrl='" + serverUrl + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", sampleRate=" + sampleRate +
                ", language='" + language + '\'' +
                ", enableVad=" + enableVad +
                ", enableSilentDetection=" + enableSilentDetection +
                ", silentTimeout=" + silentTimeout +
                '}';
    }
}
