package com.abc.hippy.view.audioview;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.uimanager.HippyGroupController;


@HippyController(name = "ABCAudioView")
public class ABCAudioViewController extends HippyGroupController<ABCAudioView> {
    //AudioView支持的操作
    private static final String ACATION_PLAY = "play";
    private static final String ACATION_PAUSE = "pause";
    private static final String ACATION_SEEKTO = "seek";
    private static final String ACATION_STOP = "stop";
    private static final String SET_PLAYBACK_SPEED = "setPlaybackSpeed";
    private static final String ACATION_RELEASE = "release";

    @Override
    protected View createViewImpl(Context context) {
        return new ABCAudioView(context);
    }

    @HippyControllerProps(name = "src", defaultType = HippyControllerProps.STRING, defaultString = "")
    public void setUrl(ABCAudioView hippyAudioView, String url) {
        hippyAudioView.setAudioPlayUrl(url);
    }

    @HippyControllerProps(name = "autoPlay", defaultType = HippyControllerProps.BOOLEAN, defaultBoolean = false)
    public void setAutoPlay(ABCAudioView hippyAudioView, boolean autoPlay) {
        hippyAudioView.setAudioAutoPlay(autoPlay);
    }

    @HippyControllerProps(name = "onPlayStart", defaultType = "boolean")
    public void setOnPlayStart(ABCAudioView hippyAudioView, boolean change) {
        hippyAudioView.setOnPlayStart(change);
    }

    @HippyControllerProps(name = "onPlayProgress", defaultType = "boolean")
    public void setOnPlayProgress(ABCAudioView hippyAudioView, boolean change) {
        hippyAudioView.setOnPlayProgress(change);
    }

    @HippyControllerProps(name = "onPlayPause", defaultType = "boolean")
    public void setOnPlayPause(ABCAudioView hippyAudioView, boolean change) {
        hippyAudioView.setOnPlayPause(change);
    }

    @HippyControllerProps(name = "onPlayResume", defaultType = "boolean")
    public void setOnPlayResume(ABCAudioView hippyAudioView, boolean change) {
        hippyAudioView.setOnPlayResume(change);
    }

    @HippyControllerProps(name = "onPlayComplete", defaultType = "boolean")
    public void setOnPlayComplete(ABCAudioView hippyAudioView, boolean change) {
        hippyAudioView.setOnPlayComplete(change);
    }

    @HippyControllerProps(name = "setOnPlayError", defaultType = "boolean")
    public void setOnPlayError(ABCAudioView hippyAudioView, boolean change) {
        hippyAudioView.setOnPlayError(change);
    }

    @Override
    public void dispatchFunction(final ABCAudioView abcAudioView, String functionName, final HippyArray var) {
        switch (functionName) {
            case ACATION_PLAY:
                if (var.getObject(0) != null && !TextUtils.isEmpty(var.getString(0))) {
                    abcAudioView.setAudioPlayUrl(var.getString(0));
                }
                abcAudioView.playAudio();
                break;
            case ACATION_PAUSE:
                abcAudioView.pauseAudio();
                break;
            case ACATION_RELEASE:
                abcAudioView.releaseAudio();
                break;
            case ACATION_SEEKTO:
                if (var.getObject(0) != null && var.getInt(0) > 0)
                    abcAudioView.seekTo(var.getInt(0));
                break;
            case ACATION_STOP:
                abcAudioView.stopAudio();
                break;
            case SET_PLAYBACK_SPEED:
                if (var.getObject(0) != null && var.getInt(0) > 0)
                    abcAudioView.setPlaybackSpeed(var.getInt(0));
                break;
            default:
                super.dispatchFunction(abcAudioView, functionName, var);
        }
    }
}
