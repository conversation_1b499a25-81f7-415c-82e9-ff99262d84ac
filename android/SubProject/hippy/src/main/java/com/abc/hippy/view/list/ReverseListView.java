package com.abc.hippy.view.list;

import android.content.Context;

import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.uimanager.HippyViewEvent;
import com.tencent.mtt.hippy.utils.PixelUtil;
import com.tencent.mtt.hippy.views.list.HippyListView;
import com.tencent.mtt.supportui.views.recyclerview.LinearLayoutManager;

/**
 * Copyright (C) 2022 ABCYun
 * FileName: ReverseListView
 * Author: jianglei
 * Date: 2022/5/30
 * Description: reverse list view
 */
public class ReverseListView extends HippyListView {

    private LinearLayoutManager mLayoutManager = null;

    private OnContentSizeChangedEvent mOnContentSizeChangedEvent;

    public ReverseListView(Context context, int orientation) {
        super(context, orientation);
        if (mLayout instanceof LinearLayoutManager) {
            mLayoutManager = (LinearLayoutManager) mLayout;
            mLayoutManager.setReverseLayout(true);
        }
    }

    public ReverseListView(Context context) {
        super(context);
        if (mLayout instanceof LinearLayoutManager) {
            mLayoutManager = (LinearLayoutManager) mLayout;
            mLayoutManager.setReverseLayout(true);
        }
    }

    @Override
    protected HippyMap generateScrollEvent() {
        HippyMap event = super.generateScrollEvent();
        HippyMap contentSize = new HippyMap();
        int width = getWidth();
        int height = mState.mTotalHeight;//getAdapter().getListTotalHeight();
        contentSize.pushDouble("width", PixelUtil.px2dp(width));
        contentSize.pushDouble("height", PixelUtil.px2dp(height));
        event.pushMap("contentSize", contentSize);
        return event;
    }

    @Override
    protected void dispatchLayout() {
        super.dispatchLayout();

        int width = getWidth();
        int height = mState.mTotalHeight;//getAdapter().getListTotalHeight();

        HippyMap contentSize = new HippyMap();
        contentSize.pushDouble("width", PixelUtil.px2dp(width));
        contentSize.pushDouble("height", PixelUtil.px2dp(height));

        getOnContentSizeChanged().send(this, contentSize);
    }

    @Override
    protected int getAnimatedScrollYPos(int yIndex) {
        return -(getHeightAfter(yIndex) + getOffsetY());
    }

    protected OnContentSizeChangedEvent getOnContentSizeChanged() {
        if (mOnContentSizeChangedEvent == null) {
            mOnContentSizeChangedEvent = new ReverseListView.OnContentSizeChangedEvent("onContentSizeChanged");
        }
        return mOnContentSizeChangedEvent;
    }

    protected class OnContentSizeChangedEvent extends HippyViewEvent {
        public OnContentSizeChangedEvent(String eventName) {
            super(eventName);
        }
    }
}
