package com.abc.hippy.view.charts.linechartview;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.uimanager.HippyViewController;


@HippyController(name = "LineChartView")
public class LineChartViewController extends HippyViewController<LineChartView> {
    @Override
    protected View createViewImpl(Context context) {
        return new LineChartView(context);
    }

    @HippyControllerProps(name = "xAxis")
    public void setXAxis(LineChartView chartView, HippyMap params) {
        chartView.setXAxis(params);
    }


    @HippyControllerProps(name = "leftAxis")
    public void setLeftAxis(LineChartView chartView, HippyMap params) {
        chartView.setLeftAxis(params);
    }

    @HippyControllerProps(name = "rightAxis")
    public void setRightAxis(LineChartView chartView, HippyMap params) {
        chartView.setRightAxis(params);
    }


    @HippyControllerProps(name = "data", defaultType = HippyControllerProps.ARRAY)
    public void setData(LineChartView chartView, HippyArray items) {
        chartView.setData(items);
    }
}
