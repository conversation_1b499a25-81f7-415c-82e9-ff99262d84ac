package com.abc.hippy.view.blurview;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.views.view.HippyViewGroupController;


@HippyController(name = "BlurView")
public class ABCBlurViewController extends HippyViewGroupController {
    @Override
    protected View createViewImpl(Context context) {
        return new ABCBlurView(context);
    }

    @HippyControllerProps(name = "blurType")
    public void setBlurType(ABCBlurView blurView, String blurType) {
        blurView.setBlurType(blurType);
    }


    @HippyControllerProps(name = "blurAmount")
    public void setBlurAmount(ABCBlurView blurView, int blurAmount) {
        blurView.setBlurAmount(blurAmount);
    }

    @HippyControllerProps(name = "reducedTransparencyFallbackColor")
    public void setReducedTransparencyFallbackColor(ABCBlurView blurView, int reducedTransparencyFallbackColor) {
//        blurView.setOverlayColor(reducedTransparencyFallbackColor);
    }

    @HippyControllerProps(name = "androidOverlayColor")
    public void setOverlayColor(ABCBlurView blurView, int overlayColor) {
        blurView.setOverlayColor(overlayColor);
    }
}
