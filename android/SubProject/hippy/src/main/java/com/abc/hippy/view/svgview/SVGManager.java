package com.abc.hippy.view.svgview;

import android.graphics.drawable.Drawable;
import android.graphics.drawable.PictureDrawable;
import android.text.TextUtils;
import android.util.LruCache;

import com.caverock.androidsvg.SVG;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SVGManager {
    public interface IGetSVGDrawableCallback {
        void onDrawable(Drawable drawable);
    }

    private final static SVGManager sInstance = new SVGManager();

    LruCache<String, SVG> mSVGCache;
    LruCache<String, Drawable> mSVGDrawableCache;
    ExecutorService mExecutor;


    static public SVGManager instance() {
        return sInstance;
    }

    private SVGManager() {
        mSVGCache = new LruCache<>(32);
        mSVGDrawableCache = new LruCache<>(32);

        mExecutor = Executors.newSingleThreadExecutor();
    }


    public void getSVGDrawable(String svgContent, int width, int height, final IGetSVGDrawableCallback callback) {
        mExecutor.execute(() -> {
            Drawable drawable = null;
            do {
                if (TextUtils.isEmpty(svgContent) || width <= 0 || height <= 0) {
                    break;
                }
                SVG svg = mSVGCache.get(svgContent);
                if (svg == null) {
                    try {
                        svg = SVG.getFromString(svgContent);
                        mSVGCache.put(svgContent, svg);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                if (svg == null) break;

                String key = svg.hashCode() + "_" + width + "_" + height;
                drawable = mSVGDrawableCache.get(key);
                if (drawable == null) {
                    try {
                        drawable = new PictureDrawable(svg.renderToPicture(width, height));
                        mSVGDrawableCache.put(key, drawable);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } while (false);
            if (callback != null) callback.onDrawable(drawable);
        });
    }
}