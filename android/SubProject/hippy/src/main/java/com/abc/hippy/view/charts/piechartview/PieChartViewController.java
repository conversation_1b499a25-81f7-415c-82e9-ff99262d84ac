package com.abc.hippy.view.charts.piechartview;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.uimanager.HippyViewController;

@HippyController(name = "PieChartView")
public class PieChartViewController extends HippyViewController {
    @Override
    protected View createViewImpl(Context context) {
        return new PieChartView(context);
    }

    @HippyControllerProps(name = "data", defaultType = HippyControllerProps.ARRAY)
    public void setData(PieChartView chartView, HippyArray items) {
        chartView.setData(items);
    }

    /**
     * 修改内圆半径比例
     * @param chartView PieChartView
     * @param percent Boolean
     */
    @HippyControllerProps(name = "pieChartPercent")
    public void setPieChartPercent(PieChartView chartView, int percent) {
        chartView._setPieChartPercent(percent);
    }


    /**
     * 扇形区域是否显示数值
     * @param chartView PieChartView
     * @param status Boolean
     */
    @HippyControllerProps(name = "drawValuesEnabled", defaultBoolean = false)
    public void setDrawValuesEnabled(PieChartView chartView, boolean status) {
        chartView._setDrawValuesEnabled(status);
    }


    /**
     * 扇形区域是否显示标题
     * @param chartView PieChartView
     * @param status Boolean
     */
    @HippyControllerProps(name = "drawEntryLabelsEnabled", defaultBoolean = false)
    public void setDrawEntryLabelsEnabled(PieChartView chartView, boolean status) {
        chartView._setDrawEntryLabelsEnabled(status);
    }


    /**
     * 扇形区域是否可旋转
     * @param chartView PieChartView
     * @param status Boolean
     */
    @HippyControllerProps(name = "rotationEnabled", defaultBoolean = false)
    public void setRotationEnabled(PieChartView chartView, boolean status) {
        chartView._setRotationEnabled(status);
    }

}
