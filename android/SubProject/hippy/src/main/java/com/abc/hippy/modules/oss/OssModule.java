package com.abc.hippy.modules.oss;

import android.text.TextUtils;
import android.util.Log;

import com.abc.hippy.modules.HostHippyMessageBridge;
import com.abc.hippy.utils.HippyUtils;
import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback;
import com.alibaba.sdk.android.oss.callback.OSSProgressCallback;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import java.util.HashMap;

import static com.abc.common.utils.ZipUtils.TAG;


@HippyNativeModule(name = OssModule.CLASSNAME)
public class OssModule extends HippyNativeModuleBase {

    final static String CLASSNAME = "OSS";
    HashMap<String, OssFileUploader> mTasks = new HashMap<>();

    public OssModule(HippyEngineContext context) {
        super(context);
    }


    @HippyMethod(name = "init")
    public void init(HippyMap params, Promise promise) {
        String endpoint = params.getString("endpoint");
        String accessKeyId = params.getString("accessKeyId");
        String accessKeySecret = params.getString("accessKeySecret");
        String securityToken = params.getString("securityToken");
        if (endpoint.isEmpty()) {
            promise.reject("endpoint不能为空");
            return;
        }

        if (accessKeyId.isEmpty()) {
            promise.reject("accessKeyId不能为空");
            return;
        }
        if (accessKeySecret.isEmpty()) {
            promise.reject("accessKeySecret不能为空");
            return;
        }

        if (securityToken.isEmpty()) {
            promise.reject("securityToken不能为空");
            return;
        }

        OssManager.getInstance().init(accessKeyId, accessKeySecret, securityToken, endpoint, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "upload")
    public void upload(HippyMap params, Promise promise) {
        final String bucketName = params.getString("bucketName");
        final String objectKey = params.getString("objectKey");
        final String file = params.getString("file");
        final String id = params.getString("id");

        if (TextUtils.isEmpty(bucketName)) {
            promise.reject("bucketName不能为空");
            return;
        }
        if (TextUtils.isEmpty(objectKey)) {
            promise.reject("objectKey不能为空");
            return;
        }

        if (TextUtils.isEmpty(file)) {
            promise.reject("file不能为空");
            return;
        }


        Log.d(TAG, "upload() called with: params = [" + params + "], promise = [" + promise + "]");
        OssFileUploader uploader = new OssFileUploader(id, bucketName, objectKey
                , file);
        uploader.start(new OSSProgressCallback<PutObjectRequest>() {
                           @Override
                           public void onProgress(PutObjectRequest request, long currentSize, long totalSize) {
                               HippyMap params = new HippyMap();
                               params.pushLong("currentSize", currentSize);
                               params.pushLong("totalSize", totalSize);
                               OssModule.this.invokeMethod("onProgress", params, id);
                           }
                       },
                new OSSCompletedCallback<PutObjectRequest, PutObjectResult>() {
                    @Override
                    public void onSuccess(PutObjectRequest request, PutObjectResult result) {
                        HippyMap params = new HippyMap();
                        String url = "https://" + bucketName + "." + OssManager.getInstance().endpoint + "/" + objectKey;
                        params.pushString("url", url);
                        OssModule.this.invokeMethod("onSuccess", params, id);
                        mTasks.remove(id);
                    }

                    @Override
                    public void onFailure(PutObjectRequest request, ClientException clientException, ServiceException serviceException) {
                        HippyMap params = new HippyMap();
                        String error = "";
                        if (clientException != null)
                            error += clientException.getMessage();
                        if (serviceException != null)
                            error += serviceException.getMessage();
                        params.pushString("error", error);
                        OssModule.this.invokeMethod("onFailure", params, id);
                        mTasks.remove(id);
                    }
                }
        );

        mTasks.put(id, uploader);
        promise.resolve(true);
    }


    private void invokeMethod(String eventName, HippyMap params, String taskId) {
        HippyMap map = new HippyMap();
        map.pushString("taskId", taskId);
        map.pushString("methodName", eventName);
        map.pushMap("args", params);
        HostHippyMessageBridge.getInstance().onHostMessage("OSSCallback", map);
    }
}