package com.abc.hippy.modules.bugly;

import android.text.TextUtils;

import com.abc.common.utils.bugly.BuglyManager;
import com.abc.hippy.utils.ArgumentUtils;
import com.abc.hippy.utils.HippyUtils;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import java.util.Map;


@HippyNativeModule(name = BuglyModule.CLASSNAME)
public class BuglyModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "Bugly";

    public BuglyModule(HippyEngineContext context) {
        super(context);
    }

    @HippyMethod(name = "init")
    public void init(HippyMap params, Promise promise) {
        BuglyManager.getInstance().init();
        promise.resolve(true);
    }

    @HippyMethod(name = "setUserId")
    public void setUserId(HippyMap params, Promise promise) {
        BuglyManager.getInstance().setUserId(ArgumentUtils.mapFromHippyMap(params), HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "setUserTag")
    public void setUserTag(HippyMap params, Promise promise) {
        BuglyManager.getInstance().setUserTag(ArgumentUtils.mapFromHippyMap(params), HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "putUserData")
    public void putUserData(HippyMap params, Promise promise) {
        BuglyManager.getInstance().putUserData(ArgumentUtils.mapFromHippyMap(params), HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "checkUpgrade")
    public void checkUpgrade(HippyMap params, Promise promise) {
        BuglyManager.getInstance().checkUpgrade(ArgumentUtils.mapFromHippyMap(params), HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }

    @HippyMethod(name = "postCatchedException")
    public void postCatchedException(HippyMap params, Promise promise) {

        String exceptionName = "";
        String message = "";
        String stack = "";
        Map<String, String> extraInfo = null;
        if (params.containsKey("exception_name")) {
            exceptionName = (String) params.get("exception_name");
        }

        if (params.containsKey("crash_message")) {
            message = (String) params.get("crash_message");
        }
        if (params.containsKey("crash_stack")) {
            stack = (String) params.get("crash_stack");
        }
        if (TextUtils.isEmpty(stack) && params.containsKey("crash_detail")) {
            stack = (String) params.get("crash_detail");
        }

        if (params.get("extra_info") instanceof HippyMap) {
            extraInfo = (Map) ArgumentUtils.mapFromHippyMap((HippyMap) params.get("extra_info"));
        }

        BuglyManager.getInstance().postCatchedException(5, TextUtils.isEmpty(exceptionName) ? "HippyJSException" : exceptionName, message, stack, extraInfo, HippyUtils.createCommonHippyInvokeResultHandler(promise));
    }
}
