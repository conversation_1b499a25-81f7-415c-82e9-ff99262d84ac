package com.abc.hippy.modules.audiorecorder;

import android.Manifest;
import android.content.pm.PackageManager;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.os.Environment;
import android.util.Base64;
import android.util.Log;

import com.abc.common.utils.ContextHolder;
import com.abc.hippy.modules.HostHippyMessageBridge;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import java.io.IOException;
import java.util.Calendar;
import java.util.Date;

/**
 * AudioRecorderModule
 */
@HippyNativeModule(name = AudioRecorderModule.CLASSNAME)
public class AudioRecorderModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "AudioRecorder";

    private boolean isRecording = false;
    private static final String LOG_TAG = "AudioRecorder";
    private MediaRecorder mRecorder = null;
    private static String mFilePath = null;
    private Date startTime = null;
    private String mExtension = "";
    private WavRecorder wavRecorder;

    // Streaming variables
    private boolean mIsStreaming = false;
    private AudioRecord mAudioRecord = null;
    private Thread mStreamingThread = null;

    public AudioRecorderModule(HippyEngineContext context) {
        super(context);
    }

    @HippyMethod(name = "start")
    public void start(HippyMap params, Promise promise) {
        Log.d(LOG_TAG, "Start");
        String path = params.getString("path");
        mExtension = params.getString("extension");
        startTime = Calendar.getInstance().getTime();
        if (path != null) {
            mFilePath = path;
        } else {
            String fileName = String.valueOf(startTime.getTime());
            mFilePath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/" + fileName + mExtension;
        }
        Log.d(LOG_TAG, mFilePath);
        startRecording();
        isRecording = true;
        promise.resolve(true);
    }

    @HippyMethod(name = "stop")
    public void stop(HippyMap params, Promise promise) {
        Log.d(LOG_TAG, "Stop");
        try {
            stopRecording();
        } catch (Throwable e) {
            e.printStackTrace();
        }
        long duration = Calendar.getInstance().getTime().getTime() - startTime.getTime();
        Log.d(LOG_TAG, "Duration : " + String.valueOf(duration));
        isRecording = false;
        HippyMap recordingResult = new HippyMap();
        recordingResult.pushLong("duration", duration);
        recordingResult.pushString("path", mFilePath);
        recordingResult.pushString("audioOutputFormat", mExtension);
        promise.resolve(recordingResult);
    }

    @HippyMethod(name = "isRecording")
    public void stop(Promise promise) {
        Log.d(LOG_TAG, "isRecording");
        promise.resolve(isRecording);
    }

    @HippyMethod(name = "hasPermissions")
    public void hasPermissions(HippyMap params, Promise promise) {
        Log.d(LOG_TAG, "Get hasPermissions");
        PackageManager pm = ContextHolder.getAppContext().getPackageManager();
//        int hasStoragePerm = pm.checkPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE, context.getPackageName());
        int hasRecordPerm = pm.checkPermission(Manifest.permission.RECORD_AUDIO, ContextHolder.getAppContext().getPackageName());
//        boolean hasPermissions = hasStoragePerm == PackageManager.PERMISSION_GRANTED && hasRecordPerm == PackageManager.PERMISSION_GRANTED;
        boolean hasPermissions = hasRecordPerm == PackageManager.PERMISSION_GRANTED;
        promise.resolve(hasPermissions);
    }

    @HippyMethod(name = "startStream")
    public void startStream(HippyMap params, final Promise promise) {
        PackageManager pm = ContextHolder.getAppContext().getPackageManager();
        int hasRecordPerm = pm.checkPermission(Manifest.permission.RECORD_AUDIO, 
            ContextHolder.getAppContext().getPackageName());
        if (hasRecordPerm != PackageManager.PERMISSION_GRANTED) {
            promise.reject("RECORD_AUDIO permission not granted");
            return;
        }

        int sampleRate = 16000;
        int channelConfig = AudioFormat.CHANNEL_IN_MONO;
        int audioFormat = AudioFormat.ENCODING_PCM_16BIT;
        int bufferSize = AudioRecord.getMinBufferSize(sampleRate, channelConfig, audioFormat);
        
        try {
            mAudioRecord = new AudioRecord(MediaRecorder.AudioSource.MIC, sampleRate, 
                                          channelConfig, audioFormat, bufferSize);
            
            if (mAudioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
                promise.reject("AudioRecord initialization failed");
                return;
            }
            
            mIsStreaming = true;
            mAudioRecord.startRecording();

            mStreamingThread = new Thread(() -> {
                byte[] buffer = new byte[bufferSize];
                while (mIsStreaming) {
                    int bytesRead = mAudioRecord.read(buffer, 0, bufferSize);
                    if (bytesRead > 0) {
                        String base64Data = Base64.encodeToString(buffer, 0, bytesRead, Base64.NO_WRAP);
                        HippyMap event = new HippyMap();
                        event.pushString("data", base64Data);
                        invokeMethod("onAudioChunk", event);
                    }
                }
            });
            
            mStreamingThread.start();
            promise.resolve(true);
        } catch (IllegalArgumentException e) {
            promise.reject("AudioRecord creation failed: " + e.getMessage());
        }
    }
    
    @HippyMethod(name = "stopStream")
    public void stopStream(Promise promise) {
        if (mAudioRecord != null) {
            mIsStreaming = false;
            try {
                mAudioRecord.stop();
                mAudioRecord.release();
            } catch (IllegalStateException e) {}
            mAudioRecord = null;
        }
        
        if (mStreamingThread != null) {
            try {
                mStreamingThread.join();
            } catch (InterruptedException e) {}
            mStreamingThread = null;
        }
        
        promise.resolve(true);
    }

    private void startRecording() {
        if (isOutputFormatWav()) {
            startWavRecording();
        } else {
            startNormalRecording();
        }
    }

    private void startNormalRecording() {
        mRecorder = new MediaRecorder();
        mRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);
        mRecorder.setOutputFormat(getOutputFormatFromString(mExtension));
        mRecorder.setOutputFile(mFilePath);
        mRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);

        try {
            mRecorder.prepare();
        } catch (IOException e) {
            Log.e(LOG_TAG, "prepare() failed");
        }

        mRecorder.start();
    }

    private void startWavRecording() {
        wavRecorder = new WavRecorder(ContextHolder.getAppContext(), mFilePath);
        wavRecorder.startRecording();
    }

    private void stopRecording() {
        if (isOutputFormatWav()) {
            stopWavRecording();
        } else {
            stopNormalRecording();
        }
    }

    private void stopNormalRecording() {
        if (mRecorder != null) {
            mRecorder.stop();
            mRecorder.reset();
            mRecorder.release();
            mRecorder = null;
        }
    }

    private void stopWavRecording() {
        wavRecorder.stopRecording();
    }

    private int getOutputFormatFromString(String outputFormat) {
        switch (outputFormat) {
            case ".aac":
                //解决Android录制的音频ios播放不了的问题
                return MediaRecorder.OutputFormat.AAC_ADTS;
            default:
                return MediaRecorder.OutputFormat.MPEG_4;
        }
    }

    private boolean isOutputFormatWav() {
        return mExtension.equals(".wav");
    }

    private void invokeMethod(String eventName, HippyMap params) {
        HippyMap map = new HippyMap();
        map.pushString("methodName", eventName);
        map.pushMap("args", params);
        HostHippyMessageBridge.getInstance().onHostMessage("AudioCallback", map);
    }
}