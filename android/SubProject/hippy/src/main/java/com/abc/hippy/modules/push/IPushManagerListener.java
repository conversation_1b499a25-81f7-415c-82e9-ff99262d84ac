package com.abc.hippy.modules.push;

import android.content.Context;

import java.util.Map;

public interface IPushManagerListener {
    // 注册成功
    void onRegisterSuccess(String deviceToken);

    //注册失败
    void onRegisterFailed(String error);

    //通知栏push启动
    void onLaunchNotification(String customContent);

    void onResumeNotification(String customContent);

    void onReceiveMessage(Map<String, Object> map);

    void onReceiveNotification(Map<String, Object> map);


}

