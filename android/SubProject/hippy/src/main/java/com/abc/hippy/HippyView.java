package com.abc.hippy;


import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.os.Build;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;

import com.abc.common.utils.AppInfo;
import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.DeviceUtils;
import com.abc.common.utils.LogUtils;
import com.abc.common.utils.bugly.BuglyManager;
import com.abc.hippy.adapter.MyImageLoader;
import com.abc.hippy.modules.HostHippyMessageBridge;
import com.abc.hippy.modules.badgenumberlibrary.BadgeNumberManager;
import com.abc.hippy.utils.KeyboardUtils;
import com.tencent.mtt.hippy.HippyAPIProvider;
import com.tencent.mtt.hippy.HippyEngine;
import com.tencent.mtt.hippy.HippyRootView;
import com.tencent.mtt.hippy.adapter.exception.HippyExceptionHandlerAdapter;
import com.tencent.mtt.hippy.common.HippyJsException;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.dom.node.TypeFaceUtil;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

interface IKeyboardVisibleListener {

    void onKeyboardVisibleChanged(boolean visible, int keyboardHeight);
}

class KeyboardVisibleMonitor implements ViewTreeObserver.OnGlobalLayoutListener {
    private static final String TAG = "HippyView";
    private boolean mIsKeyBoardShow;
    private int mLastKeyboardHeightHeight;
    private WeakReference<View> mView;

    private IKeyboardVisibleListener mListener;

    KeyboardVisibleMonitor(View view) {
        mView = new WeakReference<>(view);
    }


    public void setListener(IKeyboardVisibleListener listener) {
        this.mListener = listener;
    }

    @Override
    public void onGlobalLayout() {
        View view = mView.get();
        if (view == null) return;


        int rootViewVisibleHeight = view.getHeight();
        int screenHeight = DeviceUtils.getDeviceHeight(); //屏幕高度
        if (rootViewVisibleHeight == -1 || screenHeight == -1) //如果有失败直接返回 //TODO...仔细检查下这里的逻辑
        {
            return;
        }

        final float threshold = 0.1f;
//        LogUtils.d(TAG, "onGlobalLayout() called rootViewVisibleHeight = " + rootViewVisibleHeight + ", screenHeight = " + screenHeight);
        int heightDifference = screenHeight - rootViewVisibleHeight;

        Rect rect = new Rect();
        view.getWindowVisibleDisplayFrame(rect);
        int _screenHeight = view.getRootView().getHeight();
        int keyboardHeight = _screenHeight - rect.bottom;
        int _keyboardHeight = 0;
        if (heightDifference == 0) {
            _keyboardHeight = keyboardHeight;
        }

        this.onKeyboardVisibleChanged(heightDifference > screenHeight * threshold || keyboardHeight>100, _keyboardHeight);
    }

    ///键盘显示隐藏切换
    private void onKeyboardVisibleChanged(boolean visible, int keyboardHeight) {
        LogUtils.e("HippyView", "onKeyboardVisibleChanged visible = " + visible);
        if (mIsKeyBoardShow == visible && mLastKeyboardHeightHeight == keyboardHeight) return;

        mIsKeyBoardShow = visible;
        mLastKeyboardHeightHeight = keyboardHeight;
        if (mListener != null) {
            mListener.onKeyboardVisibleChanged(visible, keyboardHeight);
        }
    }
}

public class HippyView extends LinearLayout implements HostHippyMessageBridge.IHostMessageListener, IKeyboardVisibleListener {
    private static final String TAG = "HippyView";
    private ABCHippyEngineManager mHippyEngine;
    private HippyRootView mHippyView;
    private FrameLayout mCustomInputToolBar;
    private Runnable mHideInputToolBarTimer;
    private KeyboardVisibleMonitor mKeyboardVisibleListener;
    private boolean mKeyboardVisible;
    private int mKeyboardHeight;
    private int mInputAccessoryViewHeight;

    public HippyView(@NonNull Context context) {
        super(context);

        setOrientation(LinearLayout.VERTICAL);


        mKeyboardVisibleListener = new KeyboardVisibleMonitor(this);
        mKeyboardVisibleListener.setListener(this);
        getRootView().getViewTreeObserver().addOnGlobalLayoutListener(mKeyboardVisibleListener);

        HostHippyMessageBridge.getInstance().addHostMessageListener(this);
    }

    public void destroy() {
        if (mHippyEngine != null) {
            mHippyEngine.destroyEngine();
            mHippyEngine = null;
        }

        HostHippyMessageBridge.getInstance().removeHostMessageListener(this);

        if (getRootView() != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            getRootView().getViewTreeObserver().removeOnGlobalLayoutListener(mKeyboardVisibleListener);
        }
    }

    ///键盘显示隐藏切换
    public void onKeyboardVisibleChanged(boolean visible, int keyboardHeight) {
        this.mKeyboardHeight = keyboardHeight;
        setMCustomInputToolBarLayout();
        LogUtils.d(TAG, "onKeyboardVisibleChanged() called with: visible = [" + visible + "]");
        mKeyboardVisible = visible;
        if (mHippyEngine != null) {
            HippyMap hippyMap = new HippyMap();
            hippyMap.pushBoolean("visible", visible);
            mHippyEngine.sendEvent("keyboardVisibleChanged", hippyMap);
        }

        this.updateCustomInputToolBarVisible();

        if (mKeyboardVisible) {
            ContextHolder.uiHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    KeyboardUtils.adjustKeyboard((Activity) (HippyView.this.getContext()));
                }
            }, 300);
        }
    }

    private void updateCustomInputToolBarVisible() {
        if (mCustomInputToolBar != null) {
            mCustomInputToolBar.setVisibility(mKeyboardVisible && mCustomInputToolBar.getChildCount() > 0 ? View.VISIBLE : View.GONE);
        }
    }

    public void load(final String rootDir, boolean debugMode, final HashMap<String, Object> params) {
        String assetScheme = "assets://";
        final String assetRootDir = rootDir.startsWith(assetScheme) ? rootDir.substring(assetScheme.length()) : null;

        //设置自定义字体路径
        if (assetRootDir != null) {
            TypeFaceUtil.addAssetFontsDir(assetRootDir + "/assets/fonts/");
        } else {
            TypeFaceUtil.addFontsDir(rootDir + "/assets/fonts/");
        }

        // 初始化hippy引擎
        HippyEngine.EngineInitParams initParams = new HippyEngine.EngineInitParams();
        // 必须：宿主（Hippy的使用者）的Context
        // 若存在多个Activity加载多个业务jsbundle的情况，则这里初始化引擎时建议使用Application的Context
        initParams.context = getContext();
        // 必须：图片加载器
        initParams.imageLoader = new MyImageLoader();

        // 可选：是否设置为debug模式，默认为false。调试模式下，所有jsbundle都是从debug server上下载
        initParams.debugMode = debugMode;

        // 可选：是否打印引擎的完整的log。默认为false
        initParams.enableLog = true;
        // 可选：debugMode = false 时必须设置coreJSAssetsPath或coreJSFilePath（debugMode = true时，所有jsbundle都是从debug server上下载）
//        initParams.coreJSAssetsPath = "vendor.android.js";

        if (assetRootDir != null) {
            initParams.coreJSAssetsPath = assetRootDir + "/vendor.android.js";
        } else {
            initParams.coreJSFilePath = rootDir + "/vendor.android.js";
        }

//        LogUtils.d("HippyView", "initParams.coreJSAssetsPath file = " + new File(initParams.coreJSAssetsPath).exists());
        // 可选：异常处理器
        initParams.exceptionHandler = new HippyExceptionHandlerAdapter() {
            // JavaScript执行异常
            @Override
            public void handleJsException(HippyJsException exception) {
                LogUtils.e("hippy", exception.getMessage() + exception.getStack());
                BuglyManager.getInstance().postCatchedException(5, "HippyJSException", exception.getMessage(), exception.getStack(), null, null);
            }

            // Native代码执行异常：包括sdk和业务定制代码
            @Override
            public void handleNativeException(Exception exception, boolean haveCaught) {
                LogUtils.e("hippy", exception.getMessage());
                String stackStr ="";
                try {
                    StringWriter sw = new StringWriter();
                    exception.printStackTrace(new PrintWriter(sw));
                    stackStr  = sw.toString();
                }catch (Exception e) {

                }

                BuglyManager.getInstance().postCatchedException(5, "HippyJSNativeException", exception.getMessage(), stackStr, null, null);
            }

            // JavaScript代码Trace，业务层一般不需要
            @Override
            public void handleBackgroundTracing(String details) {
                LogUtils.e("hippy", details);
            }
        };
        List<HippyAPIProvider> providers = new ArrayList<>();
        providers.add(new MyAPIProvider(getContext()));
        // 可选：自定义的，用来提供Native modules、JavaScript modules、View controllers的管理器。1个或多个
        initParams.providers = providers;

        // 根据EngineInitParams创建引擎实例
        mHippyEngine = ABCHippyEngineManager.create(initParams);


        mHippyEngine.setInputAccessoryViewMounter(new ABCHippyEngineManager.IInputAccessoryViewMounter() {
            @Override
            public void onShowInputAccessoryView(View view) {
                LogUtils.d(TAG, "onShowInputAccessoryView view = " + view);
                HippyView.this.stopDelayedTimerToHideInputToolBar();
                if (mCustomInputToolBar != null) {
                    mCustomInputToolBar.removeAllViews();
                    mCustomInputToolBar.addView(view, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                    HippyView.this.updateCustomInputToolBarVisible();
                }
            }

            @Override
            public void onUpdateInputAccessoryView(View view, int height) {
                LogUtils.d(TAG, "onUpdateInputAccessoryView() called with: view = [" + view + "], height = [" + height + "]");
                mInputAccessoryViewHeight = height;
                setMCustomInputToolBarLayout();
            }

            @Override
            public void onHideInputAccessoryView(View view) {
                LogUtils.d(TAG, "onHideInputAccessoryView() called with: view = [" + view + "]");
                HippyView.this.startDelayedTimerToHideInputToolBar(view);

            }
        });
        // 异步初始化Hippy引擎
        mHippyEngine.initEngine(new HippyEngine.EngineListener() {
            // Hippy引擎初始化完成

            /**
             * @param  statusCode
             *         status code from initializing procedure
             * @param  msg
             *         Message from initializing procedure
             */
            @Override
            public void onInitialized(HippyEngine.EngineInitStatus statusCode, String msg) {
                if (statusCode != HippyEngine.EngineInitStatus.STATUS_OK)
                    LogUtils.e("MyActivity", "hippy engine init failed code:" + statusCode + ", msg=" + msg);
                // else
                {
                    // 2/3. 加载hippy前端模块

                    HippyEngine.ModuleLoadParams loadParams = new HippyEngine.ModuleLoadParams();
                    // 必须：该Hippy模块将要挂在的Activity or Dialog的context
                    loadParams.context = getContext();
						/*
						  必须：指定要加载的Hippy模块里的组件（component）。componentName对应的是js文件中的"appName"，比如：
						  var hippy = new Hippy({
						      appName: "Demo",
						      entryPage: App
						  });
						  */
                    loadParams.componentName = "abcyun";
						/*
						  可选：二选一设置。自己开发的业务模块的jsbundle的assets路径（assets路径和文件路径二选一，优先使用assets路径）
						  debugMode = false 时必须设置jsAssetsPath或jsFilePath（debugMode = true时，所有jsbundle都是从debug server上下载）
						 */


                    if (assetRootDir != null) {
                        loadParams.jsAssetsPath = assetRootDir + "/index.android.js";
                    } else {
                        loadParams.jsFilePath = rootDir + "/index.android.js";
                    }
//                    loadParams.jsAssetsPath = "index.android.js";
						/*
						  可选：二选一设置。自己开发的业务模块的jsbundle的文件路径（assets路径和文件路径二选一，优先使用assets路径）
						  debugMode = false 时必须设置jsAssetsPath或jsFilePath（debugMode = true时，所有jsbundle都是从debug server上下载）
						 */
//                    loadParams.jsFilePath = null;
                    // 可选：发送给Hippy前端模块的参数
                    loadParams.jsParams = new HippyMap();
                    loadParams.jsParams.pushObject("appVersion", AppInfo.getAppVersion());
                    loadParams.jsParams.pushObject("osVersion", Build.VERSION.RELEASE);

//                    loadParams.jsParams.pushObject("appVersion", AppInfo.getAppVersion());

                    if (params != null) {
                        for (Map.Entry<String, Object> entry : params.entrySet()) {
                            Object value = entry.getValue();
                            if (value instanceof Integer) {
                                loadParams.jsParams.pushInt(entry.getKey(), (Integer) value);
                            }
                            if (value instanceof Map) {
                                Map map = (Map) value;
                                HippyMap hippyMap = new HippyMap();
                                for (Object key : map.keySet()) {
                                    if (key instanceof String)
                                        hippyMap.pushObject((String) key, map.get(key));
                                }
                                loadParams.jsParams.pushObject(entry.getKey(), hippyMap);
                            } else {
                                loadParams.jsParams.pushObject(entry.getKey(), value);
                            }
                        }
                    }
                    // 加载Hippy前端模块
                    mHippyView = mHippyEngine.loadModule(loadParams);

                    LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 0);
                    lp.weight = 1;
                    addView(mHippyView, lp);

                    mCustomInputToolBar = new FrameLayout(getContext());
                    lp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 0);
                    //FIXME:这里有个诡异的问题，如果mHippyView为全屏在，首次打开app,打开隐私保护链接时，顶部标题栏显示不出来
                    lp.bottomMargin = 1;
                    addView(mCustomInputToolBar, lp);
                }
            }
        });
    }

    private void setMCustomInputToolBarLayout() {
        if (mCustomInputToolBar != null) {
            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) mCustomInputToolBar.getLayoutParams();
            lp.height = mInputAccessoryViewHeight;
            lp.bottomMargin = mKeyboardHeight;
            mCustomInputToolBar.setLayoutParams(lp);
        }
    }
    private void startDelayedTimerToHideInputToolBar(final View view) {
        LogUtils.d(TAG, "startDelayedTimerToHideInputToolBar() called with: view = [" + view + "]");
        ContextHolder.uiHandler.removeCallbacks(mHideInputToolBarTimer);
        mHideInputToolBarTimer = new Runnable() {
            @Override
            public void run() {
                if (mCustomInputToolBar != null) {
                    mCustomInputToolBar.removeView(view);
                }

                HippyView.this.updateCustomInputToolBarVisible();
                HippyView.this.mHideInputToolBarTimer = null;
            }
        };
        ContextHolder.uiHandler.postDelayed(mHideInputToolBarTimer, 100);
    }

    private void stopDelayedTimerToHideInputToolBar() {
        if (mHideInputToolBarTimer != null) {
            ContextHolder.uiHandler.removeCallbacks(mHideInputToolBarTimer);
            mHideInputToolBarTimer = null;
        }
    }

    @Override
    public void onHostMessage(String messageName, HippyMap body) {
        if (mHippyEngine != null)
            mHippyEngine.sendEvent(messageName, body);
    }

    public boolean onBackPressed() {
        return mHippyEngine.onBackPressed(null);
    }

    public void onPause() {
        if (mHippyEngine != null)
            mHippyEngine.sendEvent("appEnterBackground", null);

//        if (mHippyView != null)
//            mHippyView.onPause();
    }

    public void onResume() {
        // 启动时清空角标
        BadgeNumberManager.from(ContextHolder.getAppContext()).setBadgeNumber(0);
        mHippyEngine.sendEvent("appEnterForeground", null);
    }
}
