package com.abc.hippy.adapter;

import com.abc.common.utils.LogUtils;
import com.tencent.mtt.hippy.adapter.exception.HippyExceptionHandlerAdapter;
import com.tencent.mtt.hippy.common.HippyJsException;


/**
 * @Description:
 * @author: edsheng
 * @date: 2018/9/6 12:53
 * @version: V1.0
 * 2019/3/26 harryguo注释：
 * 老的异常捕获器。将被废弃
 */
@Deprecated
public class MyExceptionHandler implements HippyExceptionHandlerAdapter {
    @Override
    public void handleJsException(HippyJsException exception) {
        LogUtils.e("hippyerror", exception.getMessage() + exception.getStack());
    }

    @Override
    public void handleNativeException(Exception exception, boolean haveCaught) {

    }

    @Override
    public void handleBackgroundTracing(String details) {

    }
}
