/*
 * <AUTHOR>
 * 用于实现Android hippy端自定义键盘
 */

package com.abc.hippy.view.abctextinput.customkeyboard;

import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.text.InputType;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.LogUtils;
import com.abc.hippy.R;
import com.abc.hippy.utils.KeyboardUtils;
import com.abc.hippy.view.abctextinput.AbcCustomKeyboardView;
import com.tencent.mtt.hippy.utils.PixelUtil;

import java.lang.ref.WeakReference;
import java.lang.reflect.Method;


public class KeyboardManager implements AbcCustomKeyboardView.IAbcCustomKeyboardViewListener {
    protected static final String TAG = "KeyboardManager";

    private static KeyboardManager gInstance;


    private KeyboardViewContainer mKeyboardContainer;

    private FrameLayout.LayoutParams mKeyboardContainerLayoutParams;
    private final int kDefaultKeyBoardHeight = 161;


    private Runnable mHideSoftKeyboardRunnable;

    private WeakReference<EditText> mFocusEditText;

    public static synchronized KeyboardManager getInstance() {
        if (gInstance == null) {
            gInstance = new KeyboardManager();
        }

        return gInstance;
    }


    private Activity currentActivity() {
        return ContextHolder.getCurrentActivity();
    }


    private KeyboardManager() {
        mKeyboardContainer = new KeyboardViewContainer(this.currentActivity());
        mKeyboardContainerLayoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup
                .LayoutParams.WRAP_CONTENT);
        mKeyboardContainerLayoutParams.gravity = Gravity.BOTTOM;
    }


    private ViewGroup getRootView() {
        return (ViewGroup) (this.currentActivity()).getWindow().getDecorView();
    }

    private View getContentView() {
        return this.getRootView().findViewById(android.R.id.content);
    }


    public void bindToEditor(EditText editText, AbcCustomKeyboardView keyboard) {
        LogUtils.d(TAG, "bindToEditor() called with: editText = [" + editText + "], keyboard = [" + keyboard + "]");
        setShowSoftInputOnFocus(editText, false);
        editText.setTag(R.id.bind_keyboard_2_editor, keyboard);
    }

    public void unbindToEditor(EditText editText) {
        LogUtils.d(TAG, "unbindToEditor() called with: editText = [" + editText + "]");
        setShowSoftInputOnFocus(editText, true);
        editText.setTag(R.id.bind_keyboard_2_editor, null);
        if (mFocusEditText != null && mFocusEditText.get() == editText) {
            hideSoftKeyboard(true);
        }
    }

    public void onFocusChanged(final View v, boolean hasFocus) {
        LogUtils.d(TAG, "onFocusChanged() called with: v = [" + v + "], hasFocus = [" + hasFocus + "]");
        if (mFocusEditText != null && mFocusEditText.get() == v && !hasFocus) {
            mFocusEditText = null;
        }

        if (v instanceof EditText) {
            if (hasFocus) {
                KeyboardUtils.hideInputMethod(v);
                showSoftKeyboard((EditText) v);
//                this.stopHideSoftKeyboardTimer();
//                v.postDelayed(new Runnable() {
//                    @Override
//                    public void run() {
//                        showSoftKeyboard((EditText) v);
//                    }
//                }, 100);
            } else {
                hideSoftKeyboard(false);
            }
        }
    }

    private AbcCustomKeyboardView getBindKeyboard(EditText editText) {
        if (editText != null) {
            return (AbcCustomKeyboardView) editText.getTag(R.id.bind_keyboard_2_editor);
        }
        return null;
    }

    private void initKeyboard(AbcCustomKeyboardView keyboard) {
        mKeyboardContainer.setKeyboard(keyboard);
    }

    private void showSoftKeyboard(EditText editText) {
        LogUtils.d(TAG, "showSoftKeyboard() called with: editText = [" + editText + "]");
        AbcCustomKeyboardView keyboard = getBindKeyboard(editText);
        if (keyboard == null) {
            LogUtils.e(TAG, "edit text not bind to keyboard");
            return;
        }

        keyboard.setListener(this);

        mFocusEditText = new WeakReference<>(editText);
        setShowSoftInputOnFocus(editText, false);

        this.stopHideSoftKeyboardTimer();
        initKeyboard(keyboard);
        mKeyboardContainer.setVisibility(View.VISIBLE);
        final ViewGroup rootView = this.getRootView();
        if (mKeyboardContainer.getParent() != null) {
            ((ViewGroup) mKeyboardContainer.getParent()).removeView(mKeyboardContainer);
        }

        rootView.addView(mKeyboardContainer, mKeyboardContainerLayoutParams);

        int keyboardHeight = keyboard.getKeyboardHeight();
        if (keyboardHeight <= 0)
            keyboardHeight = (int) PixelUtil.dp2px(kDefaultKeyBoardHeight);

        this.adjustKeyboardWithHeight(keyboardHeight);
    }

    private void hideSoftKeyboard(boolean now) {
        LogUtils.d(TAG, "hideSoftKeyboard() called with: now = [" + now + "]");
        if (now)
            this.doHideSoftKeyboard();
        else
            this.startHideSoftKeyboardTimer();
    }

    private void adjustKeyboardWithHeight(int keyboardHeight) {
        LogUtils.d(TAG, "adjustKeyboardWithHeight() called with: keyboardHeight = [" + keyboardHeight + "]");
        //手机开启了虚拟导航栏时，需要将键盘提到导航栏上面
        View navigationBarBackground = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            navigationBarBackground = this.getRootView().findViewById(android.R.id.navigationBarBackground);
        }
        if (navigationBarBackground != null && navigationBarBackground.getVisibility() == View.VISIBLE) {
            mKeyboardContainerLayoutParams.bottomMargin = navigationBarBackground.getHeight();
        }

        mKeyboardContainer.setVisibility(View.VISIBLE);
        mKeyboardContainerLayoutParams.height = keyboardHeight;
        mKeyboardContainer.setLayoutParams(mKeyboardContainerLayoutParams);


        final View contentView = this.getContentView();
        if (contentView.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) contentView.getLayoutParams();
            lp.bottomMargin = keyboardHeight;
            contentView.setLayoutParams(lp);
        }
    }

    private void startHideSoftKeyboardTimer() {
        this.stopHideSoftKeyboardTimer();
        mHideSoftKeyboardRunnable = new Runnable() {
            @Override
            public void run() {
                KeyboardManager.this.doHideSoftKeyboard();
            }
        };

        ContextHolder.uiHandler.postDelayed(mHideSoftKeyboardRunnable, 100);
    }


    private void doHideSoftKeyboard() {
        this.stopHideSoftKeyboardTimer();
        mKeyboardContainer.removeAllViews();
        final ViewGroup rootView = KeyboardManager.this.getRootView();
        final View contentView = KeyboardManager.this.getContentView();
        rootView.removeView(mKeyboardContainer);

        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) contentView.getLayoutParams();
        lp.bottomMargin = 0;
        contentView.setLayoutParams(lp);
    }

    private void stopHideSoftKeyboardTimer() {
        if (mHideSoftKeyboardRunnable != null)
            ContextHolder.uiHandler.removeCallbacks(mHideSoftKeyboardRunnable);

        mHideSoftKeyboardRunnable = null;
    }

    private static void setShowSoftInputOnFocus(EditText editText, boolean show) {
        LogUtils.d(TAG, "setShowSoftInputOnFocus() called with: editText = [" + editText + "], show = [" + show + "]");
        int sdkInt = Build.VERSION.SDK_INT;
        if (sdkInt >= 11) {
            try {
                Class<EditText> cls = EditText.class;
                Method setShowSoftInputOnFocus;
                setShowSoftInputOnFocus = cls.getMethod("setShowSoftInputOnFocus", boolean.class);
                setShowSoftInputOnFocus.setAccessible(true);
                setShowSoftInputOnFocus.invoke(editText, show);
            } catch (SecurityException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            editText.setInputType(InputType.TYPE_NULL);
        }
    }

    public void destroy() {
        hideSoftKeyboard(false);
    }

    @Override
    public void onHeightChanged(AbcCustomKeyboardView view, int height) {
        LogUtils.d(TAG, "onHeightChanged() called with: view = [" + view + "], height = [" + height + "]");
        if (mFocusEditText == null || mFocusEditText.get() != view.getEditText()) return;
        this.adjustKeyboardWithHeight(height);
    }
}


class KeyboardViewContainer extends FrameLayout {
    public KeyboardViewContainer(Context context) {
        super(context);
    }


    public void setKeyboard(AbcCustomKeyboardView keyboard) {
        //        mCustomKeyboardView.setListener(this);
        View contentView = keyboard.getContentView();
        if (contentView.getParent() != null)
            ((ViewGroup) contentView.getParent()).removeView(contentView);

        this.addView(contentView);
    }
}
