package com.abc.hippy.modules.wxapi;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;
import android.util.Log;

import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.ICommonCallback;
import com.abc.common.utils.LogUtils;
import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.modelmsg.SendAuth;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMiniProgramObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.tencent.mm.opensdk.modelpay.PayReq;
import com.tencent.mm.opensdk.modelpay.PayResp;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.tencent.mm.opensdk.utils.ILog;
import com.tencent.mtt.hippy.common.HippyMap;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.HashMap;


public class WXAPIHandler {
    private static final String TAG = "WXAPIHandler";
    public static final String CHANNEL_NAME = "cn.abcyun.clinic/wxapi";
    public static final String METHOD_REGISTER_APP = "registerApp";
    public static final String METHOD_CHECK_WECHAT_INSTALLATION = "checkWechatInstallation";
    public static final String METHOD_SEND_AUTH = "sendAuth";
    public static final String METHOD_SEND_SHARE = "share";
    public static final String PARAM_APP_ID = "appId";
    public static final String PARAM_IS_USE_MTA = "isUseMTA";

    public static final String RESULT_TAG_SUCCESS = "success";
    public static final String RESULT_TAG_ERROR = "error";

    private static WXAPIHandler sInstance = null;


    public static WXAPIHandler getInstance() {
        if (sInstance == null) {
            synchronized (WXAPIHandler.class) {
                if (sInstance == null) {
                    sInstance = new WXAPIHandler();
                }
            }
        }
        return sInstance;
    }


    //    NSMutableDictionary<NSString*, OnRecvAuthRsp>* _waitRecvAuthRsp;

    private HashMap<String, ICommonCallback> _waitRecvAuthRsp = new HashMap<>();

    private HashMap<String, ICommonCallback> _waitRecvPayRsp = new HashMap<>();

    private IWXAPI mWXAPI;

    private WXAPIHandler() {
    }


    public void registerApp(String appId, boolean enableMTA, ICommonCallback callback) {
        Log.d(TAG, "registerApp() called with: appId = [" + appId + "], enableMTA = [" + enableMTA + "], callback = [" + callback + "]");
        if (mWXAPI == null) {
            mWXAPI = WXAPIFactory.createWXAPI(ContextHolder.getAppContext(), null, enableMTA);
            mWXAPI.setLogImpl(new ILog() {
                @Override
                public void v(String s, String s1) {
                    Log.d(TAG, "v() called with: s = [" + s + "], s1 = [" + s1 + "]");
                }

                @Override
                public void d(String s, String s1) {
                    Log.d(TAG, "d() called with: s = [" + s + "], s1 = [" + s1 + "]");
                }

                @Override
                public void i(String s, String s1) {
                    Log.d(TAG, "i() called with: s = [" + s + "], s1 = [" + s1 + "]");

                }

                @Override
                public void w(String s, String s1) {
                    Log.d(TAG, "w() called with: s = [" + s + "], s1 = [" + s1 + "]");

                }

                @Override
                public void e(String s, String s1) {
                    Log.d(TAG, "e() called with: s = [" + s + "], s1 = [" + s1 + "]");

                }
            });
            boolean isRegistered = mWXAPI.registerApp(appId);
            callback.onCallback(isRegistered, null);
        } else {
            callback.onCallback(true, null);
        }
    }

    void checkWeChatInstallation(ICommonCallback result) {
        if (mWXAPI == null) {
            result.onCallback(false, new Exception("need register app first"));
            return;
        }
        result.onCallback(mWXAPI.isWXAppInstalled(), null);
    }

    public void sendAuth(HippyMap arguments, ICommonCallback result) {
        if (mWXAPI == null) {
            result.onCallback(null, new Exception("need register app first"));
            return;
        }

        SendAuth.Req req = new SendAuth.Req();
        req.scope = arguments.getString("scope");
        req.state = arguments.getString("state");
        String openId = arguments.getString("openId");
        if (TextUtils.isEmpty(openId)) {
            req.openId = openId;
        }
//        Log.i("WXAPIHandler", "sendAuth... state:" + req.state + ",scope:" + req.scope);


        if (!mWXAPI.sendReq(req)) {
            result.onCallback(null, new Exception("sendReq失败"));
            return;
        }

        this._waitRecvAuthRsp.put(req.state, result);

    }


    private static byte[] bitmapToByteArray(Bitmap bitmap) {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, output);
        return output.toByteArray();
    }

    /**
     *
     * @param scene 场景值
     * @param url 链接
     * @param title 标题
     * @param desc 描述信息
     * @param iconUrl 图片信息
     * @param thumbSize 图片压缩尺寸 默认值为 128 最大为178
     * @return SendMessageToWX.Req
     */
    private SendMessageToWX.Req createWXWebpageSendReq(Integer scene, String url, String title, String desc, String iconUrl, Integer thumbSize) {
        Bitmap thumbBmp = null;
        if (!TextUtils.isEmpty(iconUrl)) {
            try {
                InputStream imageStream = new FileInputStream(iconUrl);
                Bitmap bitmap = BitmapFactory.decodeStream(imageStream);
                thumbBmp = Bitmap.createScaledBitmap(bitmap, thumbSize, thumbSize, true);
                bitmap.recycle();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        WXMediaMessage msg = new WXMediaMessage();
        WXWebpageObject webpage = new WXWebpageObject();
        webpage.webpageUrl = url;
        msg.title = title;
        msg.description = desc;
        msg.mediaObject = webpage;
        if (thumbBmp != null) {
            msg.thumbData = bitmapToByteArray(thumbBmp);
        }
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("webpage");
        req.message = msg;
        req.scene = scene;
        return req;
    }

    /**
     *
     * @param scene
     * @param iconUrl
     * @return
     */
    private SendMessageToWX.Req createWXImageSendReq(Integer scene, String iconUrl, HippyMap finalThumbSizeObj) {
        InputStream imageStream = null;
        try {
            imageStream = new FileInputStream(iconUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }

        Bitmap bmp = BitmapFactory.decodeStream(imageStream);
        WXImageObject imgObj = new WXImageObject(bmp);
        WXMediaMessage msg = new WXMediaMessage();
        msg.mediaObject = imgObj;

        //设置缩略图
        final int thumbWidth = finalThumbSizeObj.getInt("width");
        final int thumbHeight = finalThumbSizeObj.getInt("height");
        Bitmap thumbBmp = Bitmap.createScaledBitmap(bmp, thumbWidth, thumbHeight, true);
        bmp.recycle();
        msg.thumbData = bitmapToByteArray(thumbBmp);
        //构造一个Req
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("img");
        req.message = msg;
        req.scene = scene;
        return req;
    }

    /**
     *
     * @param webpageUrl
     * @param userName
     * @param path
     * @param withShareTicket
     * @param title
     * @param desc
     * @param iconUrl
     * @return
     */
    private SendMessageToWX.Req createWXMiniProgramSendReq(String webpageUrl, String userName, String path, boolean withShareTicket,String title, String desc, String iconUrl, Integer thumbSize) {
        WXMiniProgramObject miniProgramObj = new WXMiniProgramObject();
        if (!TextUtils.isEmpty(webpageUrl)) {
            miniProgramObj.webpageUrl = webpageUrl;
        }
        miniProgramObj.miniprogramType = WXMiniProgramObject.MINIPTOGRAM_TYPE_RELEASE;
        miniProgramObj.userName = userName;
        miniProgramObj.path = path;

        Bitmap thumbBmp = null;
        if (!TextUtils.isEmpty(iconUrl)) {
            try {
                InputStream imageStream = new FileInputStream(iconUrl);
                Bitmap bitmap = BitmapFactory.decodeStream(imageStream);
                thumbBmp = Bitmap.createScaledBitmap(bitmap, thumbSize, thumbSize, true);
                bitmap.recycle();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        WXMediaMessage msg = new WXMediaMessage(miniProgramObj);
        msg.title = title;
        msg.description = desc;
        if (thumbBmp != null) {
            msg.thumbData = bitmapToByteArray(thumbBmp);
        }
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("miniProgram");
        req.message = msg;
        req.scene = SendMessageToWX.Req.WXSceneSession;  // 目前只支持会话

        return req;
    }

    ///分享
    public void share(HippyMap arguments, final ICommonCallback result) {
        LogUtils.d(TAG, "share() called with: arguments = [" + arguments + "], result = [" + result + "]");
        if (mWXAPI == null) {
            result.onCallback(null, new Exception("need register app first"));
            return;
        }
        final String url = arguments.getString("url");
        final String title = arguments.getString("title");
        final String desc = arguments.getString("desc");
        final String iconUrl = arguments.getString("iconUrl");
        final int scene = arguments.getInt("scene");
        String transaction = arguments.getString("transaction"); //分享方式：webpage|img|miniProgram  default:webpage
        //兼容前端不上传的情况
        if (transaction == null) {
            transaction = "webpage";
        }

        HippyMap thumbSizeObj = arguments.getMap("thumbSize"); //
        if (thumbSizeObj == null) {
            thumbSizeObj = new HippyMap();
            thumbSizeObj.pushInt("width", 128);
            thumbSizeObj.pushInt("height", 128);
        }

        //小程序分享相关参数
        final String webpageUrl = arguments.getString("webpageUrl");
        final String userName = arguments.getString("userName");
        final String path = arguments.getString("path");
        final Boolean withShareTicket = arguments.getBoolean("withShareTicket");

        final String finalTransaction = transaction;
        final HippyMap finalThumbSizeObj = thumbSizeObj;
        new Thread(new Runnable() {
            @Override
            public void run() {

                SendMessageToWX.Req req = new SendMessageToWX.Req();

                switch (finalTransaction) {
                    case "img": {
                        req = createWXImageSendReq(scene, iconUrl, finalThumbSizeObj);
                        break;
                    }
                    case "miniProgram": {
                        int thumbSizeWidth = finalThumbSizeObj.getInt("width");
                        req = createWXMiniProgramSendReq(webpageUrl, userName, path, withShareTicket, title, desc, iconUrl, thumbSizeWidth);
                        break;
                    }
                    case "webpage":
                    default:{
                        int thumbSizeWidth = finalThumbSizeObj.getInt("width");
                        req = createWXWebpageSendReq(scene, url, title, desc, iconUrl, thumbSizeWidth);
                    }
                }

                final boolean ret = mWXAPI.sendReq(req);
                LogUtils.d(TAG, "share() called with: share ret = " + ret + ",url = " + url + ", iconUrl = " + iconUrl);

                ContextHolder.uiHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        result.onCallback(ret, null);
                    }
                });

            }
        }).start();

        //设置发送到朋友
//        req.scene = SendMessageToWX.Req.WXSceneTimeline;    //设置发送到朋友圈
    }

    ///分享
    public void payment(HippyMap arguments, final ICommonCallback result) {
        LogUtils.d(TAG, "payment() called with: arguments = [" + arguments + "], result = [" + result + "]");
        if (mWXAPI == null) {
            result.onCallback(null, new Exception("need register app first"));
            return;
        }
        String appId = arguments.getString("appId");
        String partnerId = arguments.getString("partnerId");
        String prepayId = arguments.getString("prepayId");
        String packageValue = arguments.getString("packageValue");
        String nonceStr = arguments.getString("nonceStr");
        String timeStamp = arguments.getString("timestamp");
        String sign = arguments.getString("sign");


        PayReq request = new PayReq();
        request.appId = appId;
        request.partnerId = partnerId;
        request.prepayId = prepayId;
        request.packageValue = packageValue;
        request.nonceStr = nonceStr;
        request.timeStamp = timeStamp;
        request.sign = sign;
        final boolean ret = mWXAPI.sendReq(request);
        if (!ret) {
            result.onCallback(null, new Exception("sendReq失败"));
            return;
        }
        this._waitRecvPayRsp.put(prepayId, result);
    }

    public void launchMiniProgram(HippyMap arguments, final ICommonCallback result) {
        String userName = arguments.getString("userName");
        String path = arguments.getString("path");
        int miniProgramType = arguments.getInt("miniProgramType");

        WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
        req.userName = userName; // 填小程序原始id
        req.path = path;                  ////拉起小程序页面的可带参路径，不填默认拉起小程序首页，对于小游戏，可以只传入 query 部分，来实现传参效果，如：传入 "?foo=bar"。
        if (path == null) {
            req.path = "";
        }
        req.miniprogramType = miniProgramType;// 可选打开 开发版，体验版和正式版
        final boolean ret = mWXAPI.sendReq(req);
        if (!ret) {
            result.onCallback(null, new Exception("launchMiniProgram sendReq失败"));
        }
    }


    private String buildTransaction(final String type) {
        LogUtils.d(TAG, "buildTransaction() called with: type = [" + type + "]");
        return (type == null) ? String.valueOf(System.currentTimeMillis()) : type + System.currentTimeMillis();
    }

    public boolean handleIntent(Intent intent, IWXAPIEventHandler iwxapiEventHandler) {
        LogUtils.d(TAG, "handleIntent() called with: intent = [" + intent + "], iwxapiEventHandler = [" + iwxapiEventHandler + "]");
        if (mWXAPI == null) {
            return false;
        }
        return mWXAPI.handleIntent(intent, iwxapiEventHandler);
    }

    public void handleWXRsp(BaseResp baseResp) {
        LogUtils.d(TAG, "handleWXRsp() called with: baseResp = [" + baseResp + "]");
        if (baseResp instanceof SendAuth.Resp) {
            SendAuth.Resp rsp = (SendAuth.Resp) baseResp;
            HippyMap rspJs = new HippyMap();
            try {
                rspJs.pushInt("errCode", rsp.errCode);
                rspJs.pushString("code", rsp.code);
                rspJs.pushString("state", rsp.state);
                rspJs.pushString("lang", rsp.lang);
                rspJs.pushString("country", rsp.country);
                rspJs.pushString("errStr", rsp.errStr);
                rspJs.pushString("openId", rsp.openId);
                rspJs.pushString("url", rsp.url);
                rspJs.pushInt("type", rsp.getType());
            } catch (Exception e) {
                e.printStackTrace();
                Log.e("WXAPIHandler", "error:" + e.toString());
            }
            Log.i("WXAPIHandler", "invokeMethod onAuthResponse ... data:" + rspJs.toString());
            try {
                ICommonCallback callback = this._waitRecvAuthRsp.get(rsp.state);
                this._waitRecvAuthRsp.remove(rsp.state);
                if (callback != null) {
                    callback.onCallback(rspJs, null);
                }
            } catch (Exception e) {
                e.printStackTrace();
                Log.e("WXAPIHandler", "invokeMethod error:" + e.toString());
            }
        } else if (baseResp.getType() == ConstantsAPI.COMMAND_PAY_BY_WX) {
            PayResp rsp = (PayResp) baseResp;
            ICommonCallback callback = this._waitRecvPayRsp.get(rsp.prepayId);
            this._waitRecvPayRsp.remove(rsp.prepayId);
            if (callback != null) {
                HippyMap rspJs = new HippyMap();
                rspJs.pushString("prepayId", rsp.prepayId);
                rspJs.pushString("returnKey", rsp.returnKey);
                rspJs.pushString("extData", rsp.extData);
                rspJs.pushString("errStr", rsp.errStr);
                rspJs.pushString("openId", rsp.openId);
                rspJs.pushInt("errCode", rsp.errCode);
                rspJs.pushInt("type", rsp.getType());

                callback.onCallback(rspJs, null);
            }
        }
    }

    public void handleWXReq(BaseReq baseReq) {
        Log.d(TAG, "handleWXReq() called with: baseReq = [" + baseReq + "]");
    }
}
