package com.abc.hippy.view.pinchimage;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.uimanager.HippyGroupController;


@HippyController(name = "PinchImageView")
public class PinchImageViewController extends HippyGroupController<PinchImageView> {
    @Override
    protected View createViewImpl(Context context) {
        return new PinchImageView(context);
    }

    @HippyControllerProps(name = "src", defaultType = HippyControllerProps.STRING)
    public void setUrl(PinchImageView view, String src) {
        view.setUrl(src);
    }

    @HippyControllerProps(name = "maxScale", defaultType = HippyControllerProps.NUMBER)
    public void setMaxScale(PinchImageView view, float maxScale) {
        view.setMaxScale(maxScale);
    }

    @HippyControllerProps(name = "doubleClickScale", defaultType = HippyControllerProps.NUMBER)
    public void setDoubleClickScale(PinchImageView view, float doubleClickScale) {
        view.setDoubleClickScale(doubleClickScale);
    }


    @Override
    public void dispatchFunction(final PinchImageView view, String functionName, HippyArray params, final Promise promise) {
        switch (functionName) {
            case "resetToInitialState":
                view.resetToInitialState();
                promise.resolve(true);
                break;
            default:
                break;
        }
    }

}
