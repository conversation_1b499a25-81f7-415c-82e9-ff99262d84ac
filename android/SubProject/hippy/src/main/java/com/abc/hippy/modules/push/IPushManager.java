package com.abc.hippy.modules.push;

import android.content.Context;
import android.content.Intent;

public interface IPushManager {

    boolean handleNotificationClickedFromIntent(Intent intent);

    void onActivityDestroyed();
    void setListener(IPushManagerListener mPushManagerListener);

    void start(Context appContext, boolean enableDebug, Intent intent);

    String getDeviceToken();

    Object getPushChannelId();
}