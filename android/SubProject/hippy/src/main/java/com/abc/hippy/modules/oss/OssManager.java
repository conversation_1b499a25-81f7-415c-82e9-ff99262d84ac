package com.abc.hippy.modules.oss;

import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;

import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.ICommonCallback;
import com.abc.common.utils.LogUtils;
import com.alibaba.sdk.android.oss.OSSClient;
import com.alibaba.sdk.android.oss.common.auth.OSSStsTokenCredentialProvider;

class OssManager {
    private static final String TAG = "OssManager";
    static OssManager sInstance;

    public static synchronized OssManager getInstance() {
        if (sInstance == null)
            sInstance = new OssManager();
        return sInstance;
    }


    HandlerThread workerThread;
    Handler workerHandler;

    OSSClient oss;

    String endpoint;


    public void init(String accessKeyId, String accessKeySecret, String securityToken, final String endpoint, final ICommonCallback callback) {

        if (workerThread != null) {
            LogUtils.w(TAG, "OssManager.init 已经初始化");

            OSSStsTokenCredentialProvider credentialProvider = new OSSStsTokenCredentialProvider(accessKeyId, accessKeySecret, securityToken);
            this.oss.updateCredentialProvider(credentialProvider);

            if (callback != null) {
                callback.onCallback(null, null);
            }
            return;
        }

        this.endpoint = endpoint;

        Log.d(TAG, "init() called with: accessKeyId = [" + accessKeyId + "], accessKeySecret = [" + accessKeySecret + "], securityToken = [" + securityToken + "], endpoint = [" + endpoint + "], callback = [" + callback + "]");

        workerThread = new HandlerThread("OSSThread");
        workerThread.start();
        workerHandler = new Handler(workerThread.getLooper());

        final OSSStsTokenCredentialProvider credentialProvider = new OSSStsTokenCredentialProvider(accessKeyId, accessKeySecret, securityToken);

        workerHandler.post(new Runnable() {
            @Override
            public void run() {
                oss = new OSSClient(ContextHolder.getAppContext(), endpoint, credentialProvider);
                if (callback != null) {
                    callback.onCallback(null, null);
                }
            }
        });
    }
}