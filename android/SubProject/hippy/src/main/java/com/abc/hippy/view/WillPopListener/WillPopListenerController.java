package com.abc.hippy.view.WillPopListener;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.views.view.HippyViewGroupController;

@HippyController(name = com.abc.hippy.view.WillPopListener.WillPopListenerController.CLASS_NAME)
public class WillPopListenerController extends HippyViewGroupController {
    static final String CLASS_NAME = "WillPopListener";

    @Override
    protected View createViewImpl(Context context) {
        return new WillPopListener(context);
    }
}
