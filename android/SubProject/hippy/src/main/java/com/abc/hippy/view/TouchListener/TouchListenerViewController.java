package com.abc.hippy.view.TouchListener;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.views.view.HippyViewGroupController;

@HippyController(name = com.abc.hippy.view.TouchListener.TouchListenerViewController.CLASS_NAME)
public class TouchListenerViewController extends HippyViewGroupController {
    static final String CLASS_NAME = "TouchListenerView";

    @Override
    protected View createViewImpl(Context context) {
        return new TouchListenerView(context);
    }
}
