/* <PERSON><PERSON> is pleased to support the open source community by making Hippy available.
 * Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.abc.hippy.modules.abcnetwork;

import android.text.TextUtils;

/**
 * SSE 事件对象
 * 表示从服务器接收到的 Server-Sent Events 事件
 */
public class SseEvent {
    private String type;
    private String data;
    private String id;
    private int retry;

    private SseEvent(Builder builder) {
        this.type = builder.type;
        this.data = builder.data.toString();
        this.id = builder.id;
        this.retry = builder.retry;
    }

    public String getType() {
        return type != null ? type : "message";
    }

    public String getData() {
        return data != null ? data : "";
    }

    public String getId() {
        return id;
    }

    public int getRetry() {
        return retry;
    }

    public boolean hasData() {
        return !TextUtils.isEmpty(data);
    }

    @Override
    public String toString() {
        return "SseEvent{" +
                "type='" + type + '\'' +
                ", data='" + data + '\'' +
                ", id='" + id + '\'' +
                ", retry=" + retry +
                '}';
    }

    /**
     * SSE 事件构建器
     */
    public static class Builder {
        private String type;
        private StringBuilder data = new StringBuilder();
        private String id;
        private int retry = 0;

        public Builder setType(String type) {
            this.type = type;
            return this;
        }

        public Builder appendData(String data) {
            if (this.data.length() > 0) {
                this.data.append('\n');
            }
            this.data.append(data);
            return this;
        }

        public Builder setId(String id) {
            this.id = id;
            return this;
        }

        public Builder setRetry(int retry) {
            this.retry = retry;
            return this;
        }

        public SseEvent build() {
            return new SseEvent(this);
        }
    }
}
