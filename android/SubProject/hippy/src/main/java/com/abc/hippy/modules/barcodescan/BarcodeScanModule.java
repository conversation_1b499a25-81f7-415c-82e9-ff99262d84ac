package com.abc.hippy.modules.barcodescan;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.abc.common.utils.ActivityEventDispatcher;
import com.abc.common.utils.ContextHolder;
import com.abc.common.utils.ICommonCallback;
import com.abc.hippy.utils.HippyUtils;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;


@HippyNativeModule(name = BarcodeScanModule.CLASSNAME)
public class BarcodeScanModule extends HippyNativeModuleBase implements ActivityEventDispatcher.ActivityResultListener {
    final static String CLASSNAME = "BarcodeScan";
    private static final String TAG = "BarcodeScanModule";

    private ICommonCallback result;

    public BarcodeScanModule(HippyEngineContext context) {
        super(context);
        Log.d(TAG, "BarcodeScanModule() called with: context = [" + context + "]");

        ActivityEventDispatcher.instance.addActivityResultListener(this);
    }


    @Override
    public void destroy() {
        super.destroy();
        Log.d(TAG, "destroy() called");

        ActivityEventDispatcher.instance.removeActivityResultListener(this);
    }

    @HippyMethod(name = "scan")
    public void scan(HippyMap params, Promise promise) {

        this.result = HippyUtils.createCommonHippyInvokeResultHandler(promise);
        this.showBarcodeView(params);
    }

    private void showBarcodeView(HippyMap params) {
        Activity currentActivity = ContextHolder.getCurrentActivity();
        Intent intent = new Intent(currentActivity, BarcodeScannerActivity.class);
        Bundle bundle = new Bundle();

        for (String key : params.keySet()) {
            bundle.putString(key, params.getString(key));
        }
        intent.putExtras(bundle);

        if (currentActivity != null)
            currentActivity.startActivityForResult(intent, 100);
    }


    private HippyMap packResult(String barcode, String action, String error) {
        HippyMap map = new HippyMap();
        if (!TextUtils.isEmpty(action))
            map.pushString("action", action);

        if (!TextUtils.isEmpty(barcode))
            map.pushString("barCode", barcode);

        if (!TextUtils.isEmpty(error))
            map.pushString("error", error);

        return map;
    }


    @Override
    public boolean onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        if (requestCode == 100) {
            HippyMap result;
            if (data != null) {
                String barcode = data.getStringExtra("SCAN_RESULT");
                String action = data.getStringExtra("SCAN_RESULT_ACTION");
                String error = data.getStringExtra("ERROR_CODE");

                result = packResult(barcode, action, error);
            } else {
                result = packResult(null, BarCodeScanConsts.userCancel, null);
            }

            if (this.result != null) {
                this.result.onCallback(result, null);
            }

            return true;
        }
        return false;
    }
}
