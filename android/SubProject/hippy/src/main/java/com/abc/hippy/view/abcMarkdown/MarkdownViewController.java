package com.abc.hippy.view.abcMarkdown;

import android.content.Context;
import android.graphics.Color;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.annotation.HippyControllerProps;
import com.tencent.mtt.hippy.uimanager.HippyViewController;

/**
 * Markdown视图控制器
 * 
 * 用于管理MarkdownView的属性和生命周期
 * 支持通过Hippy框架设置Markdown内容、文本样式等属性
 * 
 * @version 1.0.0
 */
@HippyController(name = "MarkdownView")
public class MarkdownViewController extends HippyViewController<MarkdownView> {

    @Override
    protected View createViewImpl(Context context) {
        return new MarkdownView(context);
    }

    /**
     * 设置Markdown内容
     * @param markdownView MarkdownView实例
     * @param content Markdown格式的文本内容
     */
    @HippyControllerProps(name = "content")
    public void setContent(MarkdownView markdownView, String content) {
        markdownView.setMarkdownContent(content);
    }

    /**
     * 设置文本颜色
     * @param markdownView MarkdownView实例
     * @param color 文本颜色值
     */
    @HippyControllerProps(name = "textColor")
    public void setTextColor(MarkdownView markdownView, int color) {
        markdownView.setTextColor(color);
    }

    /**
     * 设置文本大小
     * @param markdownView MarkdownView实例
     * @param textSize 文本大小（单位：sp）
     */
    @HippyControllerProps(name = "textSize")
    public void setTextSize(MarkdownView markdownView, float textSize) {
        markdownView.setTextSize(textSize);
    }

    /**
     * 设置背景颜色
     * @param markdownView MarkdownView实例
     * @param color 背景颜色值
     */
    @HippyControllerProps(name = "backgroundColor")
    public void setBackgroundColor(MarkdownView markdownView, int color) {
        markdownView.setBackgroundColor(color);
    }

    /**
     * 设置文本颜色（支持字符串格式）
     * @param markdownView MarkdownView实例
     * @param colorString 颜色字符串，如 "#FF0000" 或 "red"
     */
    @HippyControllerProps(name = "textColorString")
    public void setTextColorString(MarkdownView markdownView, String colorString) {
        try {
            int color = Color.parseColor(colorString);
            markdownView.setTextColor(color);
        } catch (IllegalArgumentException e) {
            // 如果颜色格式不正确，使用默认黑色
            markdownView.setTextColor(Color.BLACK);
        }
    }

    /**
     * 设置背景颜色（支持字符串格式）
     * @param markdownView MarkdownView实例
     * @param colorString 颜色字符串，如 "#FFFFFF" 或 "white"
     */
    @HippyControllerProps(name = "backgroundColorString")
    public void setBackgroundColorString(MarkdownView markdownView, String colorString) {
        try {
            int color = Color.parseColor(colorString);
            markdownView.setBackgroundColor(color);
        } catch (IllegalArgumentException e) {
            // 如果颜色格式不正确，使用透明背景
            markdownView.setBackgroundColor(Color.TRANSPARENT);
        }
    }
}
