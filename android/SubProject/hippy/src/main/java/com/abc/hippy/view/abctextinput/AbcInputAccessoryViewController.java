package com.abc.hippy.view.abctextinput;

import android.content.Context;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.dom.node.StyleNode;
import com.tencent.mtt.hippy.uimanager.HippyGroupController;

@HippyController(name = AbcInputAccessoryViewController.CLASS_NAME)
public class AbcInputAccessoryViewController extends HippyGroupController<AbcInputAccessoryView> {
    public static final String CLASS_NAME = "AbcInputAccessoryView";

    @Override
    protected View createViewImpl(Context context) {
        return new AbcInputAccessoryView(context);
    }

    @Override
    public void onAfterUpdateProps(AbcInputAccessoryView v) {
        super.onAfterUpdateProps(v);
        v.showOrUpdate();
    }

    @Override
    protected StyleNode createNode(boolean isVirtual) {
        return new AbcInputAccessoryStyleNode();
    }

    @Override
    public void onViewDestroy(AbcInputAccessoryView view) {
        super.onViewDestroy(view);
        view.onInstanceDestroy(view.getId());
    }
}
