package com.abc.hippy.modules.thirdcall;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.abc.common.utils.ActivityEventDispatcher;
import com.abc.common.utils.ContextHolder;
import com.abc.hippy.modules.HostHippyMessageBridge;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;


@HippyNativeModule(name = ThirdCall.CLASSNAME)
public class ThirdCall extends HippyNativeModuleBase {
    final static String CLASSNAME = "ThirdCall";

    public ThirdCall(HippyEngineContext context) {
        super(context);
    }


    @HippyMethod(name = "register")
    public void register(HippyMap params, Promise promise) {
        ThirdCallManager.getInstance().register(params, promise);
    }
}
