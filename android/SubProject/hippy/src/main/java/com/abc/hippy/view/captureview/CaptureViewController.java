package com.abc.hippy.view.captureview;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.tencent.mtt.hippy.annotation.HippyController;
import com.tencent.mtt.hippy.common.HippyArray;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.views.view.HippyViewGroup;
import com.tencent.mtt.hippy.views.view.HippyViewGroupController;


@HippyController(name = "CaptureView")
public class CaptureViewController extends HippyViewGroupController {
    @Override
    protected View createViewImpl(Context context) {
        return new CaptureView(context);
    }

    @Override
    public void dispatchFunction(final HippyViewGroup view, String functionName, HippyArray params, Promise promise) {
        switch (functionName) {
            case "captureToFile":
                if (promise != null) {
                    String path = (String) params.get(0);
                    if (TextUtils.isEmpty(path)) {
                        promise.reject("path不能为空");
                        return;
                    }

                    boolean result = ((CaptureView) view).captureToFile(path);
                    promise.resolve(result);
                }
                break;
            default:
                break;
        }
    }
}
