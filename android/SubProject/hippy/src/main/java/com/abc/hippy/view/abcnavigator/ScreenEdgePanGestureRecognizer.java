package com.abc.hippy.view.abcnavigator;

import android.os.Build;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;

import com.abc.common.utils.DeviceUtils;

/**
 * Copyright (C) 2022 ABCYun
 * FileName: ScreenEdgePanGestureRecognizer
 * Author: jianglei
 * Date: 2022/6/21
 * Description: 扣边手势识别
 */
public class ScreenEdgePanGestureRecognizer {

    public static final String TAG = "ScreenEdgePanGestureRecognizer";

    public static interface GestureRecognizerListener {
        public void onStartEdgeDragging(View targetView);

        public void onEdgeDragging(View targetView, float dx);

        public void onStopEdgeDragging(View targetView, float velocity, boolean cancelled);
    }

    static final int EDGE_DISTANCE = DeviceUtils.getScreenWidth() / 15;
    static final int INVALID_POINTER = -1;

    private View mTargetView = null;
    private GestureRecognizerListener mListener;
    private boolean mPressIn = false;
    private int mLastX = -1;
    private boolean mDragging = false;
    private int mScrollPointerId = INVALID_POINTER;
    private VelocityTracker mVelocityTracker;
    final int mMaxFlingVelocity;
    final int mTouchSlop;

    public ScreenEdgePanGestureRecognizer(View targetView) {
        this.mTargetView = targetView;

        final ViewConfiguration vc = ViewConfiguration.get(targetView.getContext());
        mTouchSlop = vc.getScaledTouchSlop();
        mMaxFlingVelocity = vc.getScaledMaximumFlingVelocity();
    }

    public void setGestureRecognizerListener(GestureRecognizerListener listener) {
        this.mListener = listener;
    }

    public boolean onTouchEvent(MotionEvent ev) {
        //LogUtils.d(TAG, "onTouchEvent: " + ev.toString());
        boolean handle = false;
        if (mVelocityTracker == null) {
            mVelocityTracker = VelocityTracker.obtain();
        }
        mVelocityTracker.addMovement(ev);

        int[] location = new int[2];
        mTargetView.getLocationOnScreen(location);
        final int locationX = location[0];
        final int action = ev.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                mScrollPointerId = ev.getPointerId(0);
                if (locationX + ev.getX() < EDGE_DISTANCE) {
                    mPressIn = true;
                    mLastX = (int) (ev.getRawX() + 0.5f);
                    //handle = true;
                }
                break;
            case MotionEvent.ACTION_MOVE:
                if (mPressIn) {
//                    final int index = ev.findPointerIndex(mScrollPointerId);
//                    if (index < 0) {
//                        return false;
//                    }
                    final int x = (int) (ev.getRawX() + 0.5f);
                    if (!mDragging) {
                        final float dx = x - mLastX;
                        if (dx >= mTouchSlop) {
                            mDragging = true;
                            cancelChildMotionEvent(ev);
                            if (mListener != null) {
                                mListener.onStartEdgeDragging(mTargetView);
                            }
                            mLastX = x;
                        }
                    } else {
                        float dx = x - mLastX;
                        if (mListener != null) {
                            mListener.onEdgeDragging(mTargetView, dx);
                        }
                        mLastX = x;
                    }
                    handle = true;
                }
                break;
            case MotionEvent.ACTION_UP:
                mVelocityTracker.computeCurrentVelocity(1000, mMaxFlingVelocity);
                final float xvel = mVelocityTracker.getXVelocity(mScrollPointerId);
                if (mDragging) {
                    if (mListener != null) {
                        mListener.onStopEdgeDragging(mTargetView, xvel, false);
                    }
                    handle = true;
                }
                mPressIn = false;
                mDragging = false;
                mLastX = -1;
                mVelocityTracker.clear();
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_OUTSIDE:
                if (mDragging) {
                    if (mListener != null) {
                        mListener.onStopEdgeDragging(mTargetView, 0, true);
                    }
                    handle = true;
                }
                mPressIn = false;
                mDragging = false;
                mLastX = -1;
                mVelocityTracker.clear();
                break;
            default:
                break;
        }
        return handle;
    }

    private void cancelChildMotionEvent(MotionEvent ev) {
        if (!(mTargetView instanceof ViewGroup)) {
            return;
        }

        MotionEvent cancelEvent = MotionEvent.obtain(ev);
        cancelEvent.setAction(MotionEvent.ACTION_CANCEL);
        final int childCount = ((ViewGroup) mTargetView).getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = ((ViewGroup) mTargetView).getChildAt(i);
            child.dispatchTouchEvent(cancelEvent);
        }
    }
}
