package com.abc.hippy.modules.oss;

import android.util.Log;

import com.abc.common.utils.LogUtils;
import com.abc.hippy.modules.HostHippyMessageBridge;
import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback;
import com.alibaba.sdk.android.oss.callback.OSSProgressCallback;
import com.alibaba.sdk.android.oss.internal.OSSAsyncTask;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.tencent.mtt.hippy.common.HippyMap;


public class OssFileUploader {
    private static final String TAG = "OssFileUploader";

    private String id;
    private String bucketName;
    private String objectKey;
    private String file;

    private OSSAsyncTask<PutObjectResult> task;


    OssFileUploader(String id, String bucketName, String objectKey, String file) {
        this.id = id;
        this.bucketName = bucketName;
        this.objectKey = objectKey;
        this.file = file;
    }


    void start(OSSProgressCallback progressCallback, OSSCompletedCallback<PutObjectRequest, PutObjectResult> completedCallback) {
        Log.d(TAG, "start() called");

        // 构造上传请求。
        PutObjectRequest put = new PutObjectRequest(bucketName, objectKey, file);

        // 异步上传时可以设置进度回调。
        put.setProgressCallback(progressCallback);

        task = OssManager.getInstance().oss.asyncPutObject(put, completedCallback);
    }

    public void cancel() {
        LogUtils.d(TAG, "cancel");
        if (task == null) {
            task.cancel();
            task = null;
        }
    }
}