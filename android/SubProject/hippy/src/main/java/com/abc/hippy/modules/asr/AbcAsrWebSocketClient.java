package com.abc.hippy.modules.asr;

import android.os.Build;

import com.abc.common.utils.LogUtils;
import com.abc.hippy.modules.asr.model.AsrConfig;
import org.json.JSONObject;

import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import android.text.TextUtils;

import io.socket.client.IO;
import io.socket.client.Manager;
import io.socket.client.Socket;
import io.socket.emitter.Emitter;

/**
 * ASR WebSocket 客户端
 * 基于 Socket.IO 实现的 WebSocket 客户端，用于与 ASR 服务器通信
 */
public class AbcAsrWebSocketClient {
    private static final String TAG = "AbcAsrWebSocketClient";

    private AsrConfig config;
    private Socket socket;

    public AbcAsrWebSocketClient(AsrConfig config) {
        this.config = config;
        initSocketIO(config);
    }

    public void on(String event, Emitter.Listener listener) {
        if (socket != null) {
            socket.on(event, listener);
        }
    }

    public void off(String event, Emitter.Listener listener) {
        if (socket != null) {
            socket.off(event, listener);
        }
    }

    private void initSocketIO(AsrConfig config) {
        try {
            IO.Options options = new IO.Options();

            // 配置传输方式
            if (config != null && config.isForceWebsockets()) {
                options.transports = new String[]{"websocket"};
                LogUtils.d(TAG, "Force WebSocket transport enabled");
            }

            // 配置自动重连
            options.reconnection = true;
            if (config != null && config.getReconnectionDelay() > 0) {
                options.reconnectionDelay = config.getReconnectionDelay();
                LogUtils.d(TAG, "Reconnection delay set to: " + config.getReconnectionDelay() + "ms");
            }

            // 配置 Socket.IO 路径
            if (config != null && !TextUtils.isEmpty(config.getSocketPath())) {
                options.path = config.getSocketPath();
                LogUtils.d(TAG, "Socket.IO path set to: " + config.getSocketPath());
            }

            // 配置query
            if (config != null && config.getConnectParams() != null && !config.getConnectParams().isEmpty()) {
                Map<String, String> connectParams = config.getConnectParams();
                StringBuilder query = new StringBuilder();
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    connectParams.forEach((key, value) -> {
                        query.append(key).append("=").append(value).append("&");
                    });
                }
                options.query = query.toString();
                LogUtils.d(TAG, "Session ID added to query: " + config.getSessionId());
            }

            // 使用服务器 URL，如果配置中没有则使用默认地址
            String socketUrl = "";
            if (config != null && !TextUtils.isEmpty(config.getServerUrl())) {
                socketUrl = config.getServerUrl();
            }

            // 添加命名空间
            if (config != null && !TextUtils.isEmpty(config.getNamespace()) && !TextUtils.isEmpty(socketUrl)) {
                socketUrl = socketUrl + config.getNamespace();
                LogUtils.d(TAG, "Namespace added: " + config.getNamespace());
            }

            LogUtils.d(TAG, "Initializing Socket.IO with URL: " + socketUrl);
            LogUtils.d(TAG, "Socket.IO options - transports: " + Arrays.toString(options.transports) +
                    ", reconnection: " + options.reconnection +
                    ", path: " + options.path +
                    ", reconnectionDelay: " + options.reconnectionDelay);

            // 设置是否携带凭证
            if (config != null) {
                LogUtils.d(TAG, "WithCredentials setting: " + config.isWithCredentials());
            }

            // 设置请求头和 Cookie
            if (config != null) {
                options.extraHeaders = new HashMap<>();
                // 添加会话ID到请求头
                if (config.getSessionId() != null) {
                    options.extraHeaders.put("Session-Id", Arrays.asList(config.getSessionId()));
                }

                // 添加用户代理
                if (config.getUserAgent() != null) {
                    options.extraHeaders.put("User-Agent", Arrays.asList(config.getUserAgent()));
                }

                // 添加 Cookie
                if (config.getCookies() != null && !config.getCookies().isEmpty()) {
                    options.extraHeaders.put("Cookie", Arrays.asList(config.getCookies()));
                }

                // 添加额外的自定义请求头
                if (config.getExtraHeaders() != null && !config.getExtraHeaders().isEmpty()) {
                    for (Map.Entry<String, String> entry : config.getExtraHeaders().entrySet()) {
                        options.extraHeaders.put(entry.getKey(), Arrays.asList(entry.getValue()));
                    }
                }
            }

            // 添加默认的客户端标识头
            options.extraHeaders.put("X-Client-Type", Arrays.asList("Android"));
            options.extraHeaders.put("X-ASR-Version", Arrays.asList("2.0"));
            // 强制创建新连接，避免和SocketIOModule冲突
            options.forceNew = true;

            socket = IO.socket(socketUrl, options);


            setupTransportHeaders();
            LogUtils.d(TAG, "Socket.IO initialization completed successfully");
        } catch (URISyntaxException e) {
            LogUtils.e(TAG, "Failed to initialize Socket.IO: " + e.getMessage());
        }
    }

    /**
     * 设置传输层请求头和 Cookie
     */
    private void setupTransportHeaders() {
        socket.io().on(Manager.EVENT_ERROR, args -> LogUtils.e(TAG, "❌ Socket.IO error: " + args[0]));

        socket.on(Socket.EVENT_CONNECT, args -> LogUtils.d(TAG, "🔌 Connected to Socket.IO server，sessionId: " + socket.id()));

        socket.on(Socket.EVENT_CONNECT_ERROR, args -> LogUtils.e(TAG, "❌ Failed to connect to Socket.IO server: " + args[0]));

        socket.on(Socket.EVENT_DISCONNECT, args -> LogUtils.d(TAG, "🔌 Disconnected from Socket.IO server"));


        socket.on("error", args -> LogUtils.d(TAG, "🔌 Disconnected from Socket.IO server:" + Arrays.toString(args)));
    }


    /**
     * 连接 Socket.IO
     */
    public boolean connect() {
        LogUtils.d(TAG, "🚀 Starting Socket.IO connection process...");
        if (socket != null) {
            LogUtils.d(TAG, "🔌 Calling socket.connect()...");
            socket.connect();
            return true;
        } else {
            LogUtils.e(TAG, "❌ Socket is null, cannot connect");
            return false;
        }
    }

    /**
     * 发送 Socket.IO 消息
     */
    public void sendSocketMessage(String event, JSONObject data) {
        if (socket != null && socket.connected()) {
            LogUtils.d(TAG, "📤 Emitting event '" + event + "' with data: " + (data == null ? "" : data.toString()));
            socket.emit(event, data);
        }
    }

    public void sendSocketBuffer(String event, byte[] data) {
        if (socket != null && socket.connected()) {
            LogUtils.d(TAG, "📤 Emitting event '" + event + "' with buffer data");
            socket.emit(event, data);
        }
    }

    /**
     * 断开连接
     */
    public void disconnect() {
        LogUtils.d(TAG, "Disconnecting Socket.IO");

        if (socket != null) {
            socket.disconnect();
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        disconnect();
        if (socket != null) {
            socket.off(); // 移除所有监听器
            socket = null;
        }
    }
}
