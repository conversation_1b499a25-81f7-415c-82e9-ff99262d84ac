package com.abc.hippy.view.abctextinput;

import android.content.Context;
import android.view.MotionEvent;
import android.view.View;

import com.tencent.mtt.hippy.dom.node.NodeProps;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;
import com.tencent.mtt.hippy.views.view.HippyViewGroup;

public class AbcTextInputView extends HippyViewGroup {

    private AbcTextInputImpl mTextInputImpl;
    private AbcCustomKeyboardView mCustomKeyboard;

    public AbcTextInputView(Context context) {
        super(context);
    }


    @Override
    public void addView(View child, int index) {
        super.addView(child, index);

        if (child instanceof AbcTextInputImpl) {
            this.mTextInputImpl = (AbcTextInputImpl) child;
        } else if (child instanceof AbcCustomKeyboardView) {
            this.mCustomKeyboard = (AbcCustomKeyboardView) child;
        }

        this.updateCustomKeyboard();
    }

    private void updateCustomKeyboard() {
        if (this.mTextInputImpl != null) {
            this.mTextInputImpl.setCustomKeyboard(this.mCustomKeyboard);
        }
    }

    @Override
    public int getChildCount() {
        return super.getChildCount();
    }

    @Override
    public View getChildAt(int index) {
        return super.getChildAt(index);
    }

    @Override
    public void removeView(View child) {
        if (this.mTextInputImpl != null && this.mTextInputImpl.hasFocus())
            this.mTextInputImpl.blur();

        super.removeView(child);

        if (child instanceof AbcTextInputImpl) {
            this.mTextInputImpl = null;
        } else if (child instanceof AbcCustomKeyboardView) {
            this.mCustomKeyboard = null;
        }

        this.updateCustomKeyboard();
    }

    @Override
    public void setGestureDispatcher(NativeGestureDispatcher nativeGestureDispatcher) {
        super.setGestureDispatcher(nativeGestureDispatcher);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (mGestureDispatcher == null) return super.onInterceptTouchEvent(ev);

        int action = ev.getAction() & MotionEvent.ACTION_MASK;
        boolean result = super.onInterceptTouchEvent(ev);

        switch (action) {
            case MotionEvent.ACTION_DOWN: {
                mGestureDispatcher.handle(NodeProps.ON_TOUCH_DOWN, ev.getX(), ev.getY(), ev);
                break;
            }

            case MotionEvent.ACTION_MOVE: {
                mGestureDispatcher.handle(NodeProps.ON_TOUCH_MOVE, ev.getX(), ev.getY(),ev);
                break;
            }

            case MotionEvent.ACTION_UP: {
                mGestureDispatcher.handle(NodeProps.ON_TOUCH_END, ev.getX(), ev.getY(), ev);
                break;
            }
            case MotionEvent.ACTION_CANCEL: {
                mGestureDispatcher.handle(NodeProps.ON_TOUCH_CANCEL, ev.getX(), ev.getY(), ev);
                break;
            }
        }

        return result;
    }
}