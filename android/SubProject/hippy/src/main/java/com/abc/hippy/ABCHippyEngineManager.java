package com.abc.hippy;

import android.view.View;

import com.abc.hippy.modules.HostHippyMessageBridge;
import com.tencent.mtt.hippy.HippyEngine;
import com.tencent.mtt.hippy.HippyNormalEngineManager;
import com.tencent.mtt.hippy.bridge.bundleloader.HippyBundleLoader;
import com.tencent.mtt.hippy.bridge.libraryloader.LibraryLoader;
import com.tencent.mtt.hippy.utils.ContextHolder;
import com.tencent.mtt.hippy.utils.LogUtils;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;


public class ABCHippyEngineManager extends HippyNormalEngineManager {

    public interface IBackPressedListener {
        boolean onBackPressed(boolean fromEdgeGesture);
    }

    public interface IInputAccessoryViewMounter {
        void onShowInputAccessoryView(View view);

        void onUpdateInputAccessoryView(View view, int height);

        void onHideInputAccessoryView(View view);
    }

    private ABCHippyEngineManager(EngineInitParams var1, HippyBundleLoader var2) {
        super(var1, var2);
    }

    private ArrayList<IBackPressedListener> mBackPressedListener = new ArrayList<>();

    private IInputAccessoryViewMounter mInputAccessoryViewMounter;

    public IInputAccessoryViewMounter getInputAccessoryViewMounter() {
        return mInputAccessoryViewMounter;
    }

    public void setInputAccessoryViewMounter(IInputAccessoryViewMounter mInputAccessoryViewMounter) {
        this.mInputAccessoryViewMounter = mInputAccessoryViewMounter;
    }

    public static ABCHippyEngineManager create(HippyEngine.EngineInitParams params) {
        if (params == null) {
            throw new RuntimeException("Hippy: initParams must no be null");
        } else {
            LogUtils.enableDebugLog(params.enableLog);
            params.check();
            ContextHolder.initAppContext(params.context);
            LibraryLoader.loadLibraryIfNeed(params.soLoader);
            return new ABCHippyEngineManager(params, (HippyBundleLoader) null);
        }
    }


    public void addBackPressedListener(IBackPressedListener listener) {
        if (!mBackPressedListener.contains(listener))
            mBackPressedListener.add(listener);
    }

    public void removeBackPressedListener(IBackPressedListener listener) {
        mBackPressedListener.remove(listener);
    }

    @Override
    public void onDevBundleLoadReady(InputStream bundle) {
        super.onDevBundleLoadReady(bundle);
    }


    @Override
    public void onExitDebugMode() {
        HashMap<String, Object> params = new HashMap<>();
        params.put("enable", false);
        HostHippyMessageBridge.getInstance().onHippyRequestInvokeHostMethod("setHippyDebug", params);

        HostHippyMessageBridge.getInstance().onHippyRequestInvokeHostMethod("reload", params);
    }

    @Override
    public boolean onBackPressed(BackPressHandler backPressHandler) {
        boolean handled = false;
        for (IBackPressedListener listener : mBackPressedListener) {
            handled |= listener.onBackPressed(false);
        }

        return handled || super.onBackPressed(backPressHandler);
    }
}
