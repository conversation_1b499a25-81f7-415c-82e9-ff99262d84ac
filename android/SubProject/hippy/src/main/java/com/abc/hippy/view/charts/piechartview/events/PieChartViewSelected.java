package com.abc.hippy.view.charts.piechartview.events;

import android.view.View;

import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.uimanager.HippyViewEvent;

public class PieChartViewSelected extends HippyViewEvent {

    public static final String EVENT_NAME = "onSelectChanged";

    private View mTarget;

    public PieChartViewSelected(View target) {
        super(EVENT_NAME);
        mTarget = target;
    }

    public void send(HippyMap item) {
        super.send(mTarget, item);
    }
}
