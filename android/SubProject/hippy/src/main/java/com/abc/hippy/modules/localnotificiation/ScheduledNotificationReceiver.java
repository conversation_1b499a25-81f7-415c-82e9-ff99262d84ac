package com.abc.hippy.modules.localnotificiation;

import android.app.Notification;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import androidx.core.app.NotificationManagerCompat;

import com.abc.hippy.modules.localnotificiation.models.NotificationDetails;
import com.abc.hippy.modules.localnotificiation.utils.StringUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 24/3/18.
 */

public class ScheduledNotificationReceiver extends BroadcastReceiver {


    @Override
    public void onReceive(final Context context, Intent intent) {
        NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
        String notificationDetailsJson = intent.getStringExtra(LocalNotificationModule.NOTIFICATION_DETAILS);
        boolean repeat = intent.getBooleanExtra(LocalNotificationModule.REPEAT, false);

        // TODO: remove this branching logic as it's legacy code to fix an issue where notifications weren't reporting the correct time
        if(StringUtils.isNullOrEmpty(notificationDetailsJson)) {
            Notification notification = intent.getParcelableExtra(LocalNotificationModule.NOTIFICATION);
            notification.when = System.currentTimeMillis();
            int notificationId = intent.getIntExtra(LocalNotificationModule.NOTIFICATION_ID,
                    0);
            notificationManager.notify(notificationId, notification);
            if (repeat) {
                return;
            }
            LocalNotificationModule.removeNotificationFromCache(notificationId, context);
        } else {
            Gson gson = LocalNotificationModule.buildGson();
            Type type = new TypeToken<NotificationDetails>() {
            }.getType();
            NotificationDetails notificationDetails  = gson.fromJson(notificationDetailsJson, type);
            LocalNotificationModule.showNotification(context, notificationDetails);
            if (repeat) {
                return;
            }
            LocalNotificationModule.removeNotificationFromCache(notificationDetails.id, context);
        }

    }

}
