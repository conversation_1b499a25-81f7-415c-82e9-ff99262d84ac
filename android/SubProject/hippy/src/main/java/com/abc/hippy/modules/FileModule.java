package com.abc.hippy.modules;

import com.abc.common.utils.FileUtils;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;
import com.tencent.mtt.hippy.utils.ContextHolder;

import java.io.File;


@HippyNativeModule(name = FileModule.CLASSNAME)
public class FileModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "FileManager";

    public FileModule(HippyEngineContext context) {
        super(context);
    }

    @HippyMethod(name = "delete")
    public void deleteFile(String file, Promise promise) {
        FileUtils.deleteQuietly(new File(file));
        promise.resolve(true);
    }

    @HippyMethod(name = "exists")
    public void exists(String file, Promise promise) {
        boolean exist = new File(file).exists();
        promise.resolve(exist);
    }

    @HippyMethod(name = "writeAsString")
    public void writeAsString(String file, String content, String encode, Promise promise) {
        try {
            FileUtils.writeStringToFile(new File(file), content, encode);
            promise.resolve(true);
        } catch (Exception e) {
            promise.reject(e.getMessage());
        }
    }


    @HippyMethod(name = "readAsString")
    public void readAsString(String file, String encode, Promise promise) {
        try {
            String content = FileUtils.readFileAsString(new File(file), encode);
            promise.resolve(content);
        } catch (Exception e) {
            promise.reject(e.getMessage());
        }
    }

    @HippyMethod(name = "mkdirs")
    public void mkdirs(String dirs, Promise promise) {
        try {
            FileUtils.forceMkdir(new File(dirs));
            promise.resolve(true);
        } catch (Exception e) {
            promise.reject(e.getMessage());
        }
    }


    @HippyMethod(name = "mv")
    public void mv(String from, String to, Promise promise) {
        try {
            FileUtils.copyMoveFile(from, to);
            promise.resolve(true);
        } catch (Exception e) {
            promise.reject(e.getMessage());
        }
    }

    @HippyMethod(name = "getApplicationDocumentsDirectory")
    public void getApplicationDocumentsDirectory(Promise promise) {
        try {
            String dir = FileUtils.getDataDir().getAbsolutePath();
            promise.resolve(dir);
        } catch (Exception e) {
            promise.reject(e.getMessage());
        }
    }


    @HippyMethod(name = "getTemporaryDirectory")
    public void getTemporaryDirectory(Promise promise) {
        try {
            String dir = ContextHolder.getAppContext().getCacheDir().getAbsolutePath();
            promise.resolve(dir);
        } catch (Exception e) {
            promise.reject(e.getMessage());
        }
    }
}
