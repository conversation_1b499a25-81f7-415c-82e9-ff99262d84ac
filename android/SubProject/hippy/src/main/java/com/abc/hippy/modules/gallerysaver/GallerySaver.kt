package com.abc.hippy.modules.gallerysaver

import android.Manifest
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import com.abc.common.utils.ContextHolder
import com.abc.common.utils.ICommonCallback
import com.abc.common.utils.RequestPermissionResultDispatcher
import com.tencent.mtt.hippy.common.HippyMap


enum class MediaType { image, video }
/**
 * Class holding implementation of saving images and videos
 */
class GallerySaver :
        RequestPermissionResultDispatcher.IListener {

    private var pendingResult: ICommonCallback? = null
    private var mediaType: MediaType? = null
    private var filePath: String = ""
    private var albumName: String = ""


    /**
     * Saves image or video to device
     *
     * @param params - method call params
     * @param result     - result to be set when saving operation finishes
     * @param mediaType    - media type
     */
    fun checkPermissionAndSaveFile(
            params: HippyMap,
            result: ICommonCallback,
            mediaType: MediaType
    ) {
        filePath = params.getString(KEY_PATH)?.toString() ?: ""
        albumName = params.getString(KEY_ALBUM_NAME)?.toString() ?: ""
        this.mediaType = mediaType
        this.pendingResult = result

        if (isWritePermissionGranted()) {
            saveMediaFile()
        } else {
            ActivityCompat.requestPermissions(
                    ContextHolder.getMainActivity(),
                    arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                    REQUEST_EXTERNAL_IMAGE_STORAGE_PERMISSION
            )

        }
    }

    private fun isWritePermissionGranted(): Boolean {
        return PackageManager.PERMISSION_GRANTED ==
                ActivityCompat.checkSelfPermission(
                        ContextHolder.getMainActivity(), Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
    }

    private fun saveMediaFile() {
        ContextHolder.uiHandler.post {
            try {
                if (mediaType == MediaType.video) {
                    FileUtils.insertVideo(ContextHolder.getMainActivity().contentResolver, filePath, albumName)
                } else {
                    FileUtils.insertImage(ContextHolder.getMainActivity().contentResolver, filePath, albumName)
                }

                finishWithSuccess()
            } catch (e: java.lang.Exception) {
                finishWithError(e.message ?: "unknown error");
            }
        }
    }

    private fun finishWithSuccess() {
        if (pendingResult != null) {
            pendingResult!!.onCallback(true, null)
            pendingResult = null
        }
    }

    private fun finishWithError(error: String) {
        if (pendingResult != null) {
            pendingResult!!.onCallback(null, Exception(error))
            pendingResult = null
        }
    }

    override fun onRequestPermissionsResult(
            requestCode: Int, permissions: Array<String>, grantResults: IntArray
    ): Boolean {
        val permissionGranted = grantResults.isNotEmpty()
                && grantResults[0] == PackageManager.PERMISSION_GRANTED

        if (requestCode == REQUEST_EXTERNAL_IMAGE_STORAGE_PERMISSION) {
            if (permissionGranted) {
                saveMediaFile()
            } else {
                finishWithError("无相册访问权限")
            }
        } else {
            return false
        }
        return true
    }

    companion object {

        private const val REQUEST_EXTERNAL_IMAGE_STORAGE_PERMISSION = 2408

        private const val KEY_PATH = "path"
        private const val KEY_ALBUM_NAME = "albumName"
    }
}