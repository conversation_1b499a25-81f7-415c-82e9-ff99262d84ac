package com.abc.hippy.view.captureview;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;

import com.abc.common.utils.FileUtils;
import com.tencent.mtt.hippy.views.view.HippyViewGroup;

import java.io.File;

/**
 * 支持view截图
 */
public class CaptureView extends HippyViewGroup {
    public CaptureView(Context context) {
        super(context);
    }

    public boolean captureToFile(String path) {
        Bitmap bitmap = Bitmap.createBitmap(this.getWidth(), this.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        this.draw(canvas);

        return FileUtils.SUCCESS == FileUtils.saveImage(new File(path), bitmap);
    }
}
