package com.abc.hippy.modules.alipay;

import com.abc.common.utils.ContextHolder;
import com.abc.hippy.utils.ArgumentUtils;
import com.alipay.sdk.app.PayTask;
import com.tencent.mtt.hippy.HippyEngineContext;
import com.tencent.mtt.hippy.annotation.HippyMethod;
import com.tencent.mtt.hippy.annotation.HippyNativeModule;
import com.tencent.mtt.hippy.common.HippyMap;
import com.tencent.mtt.hippy.modules.Promise;
import com.tencent.mtt.hippy.modules.nativemodules.HippyNativeModuleBase;

import java.util.Map;


@HippyNativeModule(name = AliPayModule.CLASSNAME)
public class AliPayModule extends HippyNativeModuleBase {
    final static String CLASSNAME = "AliPay";

    public AliPayModule(HippyEngineContext context) {
        super(context);
    }


    @HippyMethod(name = "payment")
    public void payment(final HippyMap params, final Promise promise) {
        // 必须异步调用
        Thread payThread = new Thread(new Runnable() {
            @Override
            public void run() {
                String orderInfo = params.getString("orderInfo");
                PayTask alipay = new PayTask(ContextHolder.getCurrentActivity());
                Map<String, String> result = alipay.payV2(orderInfo, true);

                HippyMap resultMap = ArgumentUtils.toHippyMap((Map) result);
                promise.resolve(resultMap);

            }
        });

        payThread.start();
    }
}
