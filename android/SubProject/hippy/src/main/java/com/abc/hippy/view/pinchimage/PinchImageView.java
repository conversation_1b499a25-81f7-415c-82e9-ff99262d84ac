package com.abc.hippy.view.pinchimage;

import android.content.Context;
import android.widget.FrameLayout;

import com.tencent.mtt.hippy.uimanager.HippyViewBase;
import com.tencent.mtt.hippy.uimanager.NativeGestureDispatcher;

public class PinchImageView extends FrameLayout implements HippyViewBase {

    private PinchImageViewImpl mImageViewImpl;
    private NativeGestureDispatcher mGestureDispatcher;

    public PinchImageView(Context context) {
        super(context);

        this.mImageViewImpl = new PinchImageViewImpl(context);
        this.addView(this.mImageViewImpl, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
    }

    public void setUrl(String src) {
        this.mImageViewImpl.setUrl(src);
    }

    public void setDoubleClickScale(float doubleClickScale) {
        this.mImageViewImpl.setDoubleClickScale(doubleClickScale);
    }

    public void setMaxScale(float maxScale) {
        this.mImageViewImpl.setMaxScale(maxScale);
    }

    public void resetToInitialState() {
        this.mImageViewImpl.reset();
    }

    @Override
    public NativeGestureDispatcher getGestureDispatcher() {
        return mGestureDispatcher;
    }

    @Override
    public void setGestureDispatcher(NativeGestureDispatcher dispatcher) {
        this.mGestureDispatcher = dispatcher;
    }
}