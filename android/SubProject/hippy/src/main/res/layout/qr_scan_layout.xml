<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">


    <me.dm7.barcodescanner.zxing.ZXingScannerView
        android:id="@+id/qr_scan_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:borderColor="#FF08BB88" />

    <FrameLayout
        android:id="@+id/navigator"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_gravity="top">

        <ImageView
            android:id="@+id/back_btn"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="start|center_vertical"
            android:padding="14dp"
            android:src="@drawable/back" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="扫码"
            android:textColor="#FFFFFFFF"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/qrcode_scan_flash_btn"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="end|center_vertical"
            android:layout_marginRight="16dp"
            android:src="@drawable/flash_off" />

    </FrameLayout>


    <LinearLayout
        android:id="@+id/qr_scan_count_container"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="171dp"
        android:background="@drawable/scan_count_bg"
        android:orientation="horizontal"
        android:paddingLeft="26dp"
        android:paddingRight="26dp">


        <TextView
            android:id="@+id/select_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@android:color/transparent"
            android:text="已选择"
            android:textColor="@android:color/black" />

        <TextView
            android:id="@+id/qr_scan_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:text="20"
            android:textAlignment="center"
            android:textColor="#FF3333"
            android:textFontWeight="500" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:text="种"
            android:textColor="@android:color/black" />

    </LinearLayout>

</FrameLayout>
