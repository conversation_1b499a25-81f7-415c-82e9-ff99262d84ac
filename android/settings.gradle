pluginManagement {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/mavenCentral' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven {
            url 'http://developer.huawei.com/repo/'
            allowInsecureProtocol = true
        }

        gradlePluginPortal()
        google()
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/mavenCentral' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven {
            url 'http://developer.huawei.com/repo/'
            allowInsecureProtocol = true
        }
        google()
        mavenCentral()
    }
}

rootProject.name = "AbcyunClinic"
include ':app'

include ':CommonBaseModule'
project(':CommonBaseModule').projectDir = new File(rootProject.projectDir, "SubProject/CommonBaseModule")

include ':hippy'
project(':hippy').projectDir = new File(rootProject.projectDir, "SubProject/hippy")

//include ':android-sdk'
//project(':android-sdk').projectDir = new File('/Users/<USER>/Develop/Work/Hippy/hippy_abcyun/android/sdk')
//project(':android-sdk').projectDir = new File('/Users/<USER>/Develop/Work/Hippy/Hippy-2.13.7/android/sdk')
