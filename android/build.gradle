// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.kotlin_version = '1.3.50'

    repositories {
        // 添加阿里云 maven 地址
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/mavenCentral' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven {
            url 'http://developer.huawei.com/repo/'
            allowInsecureProtocol = true
        }

        google()
        mavenCentral()

        flatDir {
            dirs 'SubProject'
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.tencent.android.tpns:tpnsplugin:1.8.0"
    }

    subprojects {
        project.configurations.all {
            resolutionStrategy.eachDependency { details ->
                if (details.requested.group == 'com.android.support'
                        && !details.requested.name.contains('multidex')) {
                    details.useVersion "27.1.1"
                }

                if (details.requested.group == 'androidx.core'
                        && !details.requested.name.contains('androidx')) {
                    details.useVersion "1.1.0"
                }
                if (details.requested.group == 'androidx.appcompat'
                        && !details.requested.name.contains('androidx')) {
                    details.useVersion "1.0.2"
                }
            }
        }
    }
}

plugins {
    id 'com.android.application' version '7.1.3' apply false
    id 'com.android.library' version '7.1.3' apply false
}

rootProject.buildDir = './build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}

subprojects {
    project.evaluationDependsOn(':app')
//    project.evaluationDependsOn(':CommonBaseModule')
}

task clean(type: Delete) {
    delete rootProject.buildDir
}