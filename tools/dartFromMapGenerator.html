<!DOCTYPE html>
<html>
	<head>
        <meta charset="utf-8" />
		<title>Dart fromMap Generator</title>
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
        <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js" integrity="sha384-ZMP7rVo3mIykV+2+9J3UJ46jBk0WLaUAdn689aCwoqbBJiSnjAK/l8WvCWPIPm49" crossorigin="anonymous"></script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>
        <style type="text/css">
            textarea.data-area {
                margin-top: 32px;
                height: 320px;
            }

            .calc-btn {
                margin-top: 16px;
            }

            .result-card {
                margin-top: 16px;
            }
        </style>
	</head>
	<body>
        <div class="container">
            <textarea id="data-area" class="form-control data-area"></textarea>
            <div class="row">
                <button id="from-map-btn" type="button" class="btn btn-primary calc-btn float-right">fromMap</button>
            </div>
            <div class="row">
                <button id="to-map-btn" type="button" class="btn btn-primary calc-btn float-right">toMap</button>
            </div>
            <div class="card-body result-card" id="result-card"></div>
        </div>
        <script type="text/javascript">
            function escapeHtml(unsafe) {
                return unsafe
                     .replace(/&/g, "&amp;")
                     .replace(/</g, "&lt;")
                     .replace(/>/g, "&gt;")
                     .replace(/"/g, "&quot;")
                     .replace(/'/g, "&#039;");
            }

            $('#from-map-btn').click(function() {
                let data = $('#data-area').val();
                let lines = data.split('\n');
                let declaration_finder = /(\w+) +([\w_]+);/;
                let list_declaration_finder = /List<(\w+)> +([\w_]+);/;
                let result_lines = [];
                lines.forEach(line => {
                    let result = declaration_finder.exec(line);
                    let result_line = line;
                    if(result) {
                        let declaration_type = result[1];
                        let declaration_name = result[2];
                        if (declaration_type === 'int' || declaration_type === 'String' || declaration_type === 'bool') {
                            result_line = 'this.' + declaration_name + ' = map[\'' + declaration_name + '\'];';
                        } else if (declaration_type === 'double') {
                            result_line = 'this.' + declaration_name + ' = map[\'' + declaration_name + '\']?.toDouble();';
                        } else if (declaration_type === 'DateTime'){
                            result_line = 'this.' + declaration_name + ' = map[\'' + declaration_name + '\'] != null ? ' + declaration_type + '.parse(map[\'' + declaration_name + '\'])?.toLocal() : null;';
                        } else {
                            result_line = 'this.' + declaration_name + ' = map[\'' + declaration_name + '\'] != null ? ' + declaration_type + '.fromMap(map[\'' + declaration_name + '\']) : null;';
                        }
                    } else {
                        let list_result = list_declaration_finder.exec(line);
                        if (list_result) {
                            let declaration_type = list_result[1];
                            let declaration_name = list_result[2];
                            if (declaration_type === 'int' || declaration_type === 'String' || declaration_type === 'bool' || declaration_type === 'double') {
                                result_line = escapeHtml('this.' + declaration_name + ' = List<String>.from(map[\'' + declaration_name + '\']);');
                            } else {
                                let sublines = [];
                                sublines.push(escapeHtml(`if (map['${declaration_name}'] != null) {`));
                                sublines.push(escapeHtml(`    \tvar tmp = map['${declaration_name}'].map((item) => ${declaration_type}.fromMap(item)).toList();`));
                                sublines.push(escapeHtml(`    this.${declaration_name} = List<${declaration_type}>.from(tmp);`));
                                sublines.push(escapeHtml(`    }`));
                                result_line = sublines.join('<br>');
                            }
                        }

                    }
                    result_lines.push(result_line);
                });
                $('#result-card').html(result_lines.join('<br>'));
            })

            $('#to-map-btn').click(function() {
                let data = $('#data-area').val();
                let lines = data.split('\n');
                let declaration_finder = /(\w+) +([\w_]+);/;
                let list_declaration_finder = /List<(\w+)> +([\w_]+);/;
                let result_lines = [escapeHtml('Map<String, dynamic> toMap() {'), escapeHtml('var map = <String, dynamic>{};')];
                lines.forEach(line => {
                    let result = declaration_finder.exec(line);
                    let result_line = line;
                    if(result) {
                        let declaration_type = result[1];
                        let declaration_name = result[2];
                        if (declaration_type === 'int' || declaration_type === 'String' || declaration_type === 'double' || declaration_type === 'bool') {
                            result_line = 'map[\'' + declaration_name + '\'] = ' + declaration_name + ';';
                        } else if (declaration_type === 'DateTime'){
                            result_line = 'map[\'' + declaration_name + '\'] = ' + declaration_name + '?.toIso8601String();';
                        } else {
                            result_line = 'map[\'' + declaration_name + '\'] = ' + declaration_name + '?.toMap();';
                        }
                    } else {
                        let list_result = list_declaration_finder.exec(line);
                        if (list_result) {
                            let declaration_type = list_result[1];
                            let declaration_name = list_result[2];
                            if (declaration_type === 'int' || declaration_type === 'String' || declaration_type === 'double' || declaration_type === 'bool') {
                                result_line = 'map[\'' + declaration_name + '\'] = ' + declaration_name + ';';
                            } else {
                                let sublines = [];
                                sublines.push(escapeHtml(`if (this.${declaration_name} != null) {`));
                                sublines.push(escapeHtml(`   map['${declaration_name}'] = this.${declaration_name}.map((item) => item.toMap()).toList();`));
                                sublines.push(escapeHtml(`    }`));
                                result_line = sublines.join('<br>');
                            }
                        }

                    }
                    result_lines.push(result_line);
                });
                result_lines.push('return map;');
                result_lines.push('}');

                $('#result-card').html(result_lines.join('<br>'));
            })
        </script>
	</body>
</html>
