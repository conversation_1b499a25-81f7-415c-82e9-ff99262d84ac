#!/usr/bin/python
# -*- coding: UTF-8 -*-

import sys
import subprocess
reload(sys)
sys.setdefaultencoding('utf-8')

lines=sys.stdin.readlines()

strBuf=[]

for line in lines:
    delimer ="LogBody:"
    pos=line.find(delimer)
    msgBody=line
    if pos >= 0:
        msgBody = line[pos + len(delimer):-1]
        strBuf.append(msgBody)
    else:
        strBuf.append(line)
        # print "msgBody={0}".format(msgBody)




def setClipboardData(data):
 p = subprocess.Popen('pbcopy', env={'LANG': 'en_US.UTF-8'}, stdin=subprocess.PIPE)
 p.stdin.write(data.encode("utf-8"))
 p.stdin.close()
 retcode = p.wait()


print 'output:\n'
print ''.join(strBuf)
setClipboardData(''.join(strBuf))
