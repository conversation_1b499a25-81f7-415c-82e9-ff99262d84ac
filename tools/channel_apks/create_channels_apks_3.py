#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import importlib
import sys
import json
import os
import shutil

# 环境配置
ANDROID_BUILD_HOME = '~/android/sdk/build-tools/27.0.0'
APKTOOLJAR = '~/bin/apktool.jar'
outputDir = 'output'


class BuildConfig:
    def __init__(self):
        self.apkName = ''
        self.channels = []  # ChannelItem

    def loadFromDict(self, json):
        self.apkName = json['apkName']
        for item in json['channels']:
            self.channels.append(ChannelItem(item))


class ChannelItem:
    def __init__(self, json):
        self.key = json['key']
        self.description = json['description']


def fileNameWithoutExt(path):
    basename = os.path.basename(path)
    return os.path.splitext(basename)[0]


def unzipApk(apkPath, outputDir):
    print('apkPath:' + apkPath)
    print('outputDir:' + outputDir)
    basename = fileNameWithoutExt(apkPath)

    cpCmd = 'cp {} {}'.format(apkPath, outputDir)

    cdCmd = 'cd ' + outputDir

    unzipCmd = 'java -jar -Duser.language=en -Duser.home={} {} d -f {}'.format(ANDROID_BUILD_HOME, APKTOOLJAR,
                                                                               os.path.basename(
                                                                                   apkPath));

    # unzipCmd = 'unzip {} -d {}'.format(apkPath, fileNameWithoutExt(apkPath));
    fullCmd = cpCmd + ' && ' + cdCmd + ' && ' + unzipCmd
    print('fullCmd=' + fullCmd)
    os.system(fullCmd)

    outputZipDir = outputDir + '/' + basename;

    targetMETA = outputZipDir + "/build/apk/META-INF/";
    cpCmd = "mkdir -p {} && cp -rf {} {}".format(targetMETA, outputZipDir + "/original/META-INF/services", targetMETA);
    os.system(cpCmd);
    print("cpCmd1 = " + cpCmd)

    cpCmd = "cp -rf {} {}".format(outputZipDir + "/original/META-INF/*.kotlin_module", targetMETA);
    os.system(cpCmd);

    print("cpCmd2 = " + cpCmd);
    return outputZipDir;


def loadConfig():
    with open("./config.json", "r") as load_f:
        load_dict = json.load(load_f)
        config = BuildConfig()
        config.loadFromDict(load_dict)
        return config


def prepareEnv():
    if os.path.exists(outputDir):
        shutil.rmtree(outputDir, ignore_errors=True)

    os.mkdir(outputDir)


def zipAndSignApk(srcDir, apkName, outputDir, keyStore):
    outputUnSignedApkDir = apkName + '_unsiged.apk'
    outputSignedApkDir = apkName + '.apk'
    print('正在在生成未签名包{0}...'.format(os.path.basename(outputUnSignedApkDir)))
    zipCmd = 'cd ' + srcDir + ' && zip -r -o ../' + outputUnSignedApkDir + ' .'

    print("zipCmd = {0}".format(zipCmd))

    if os.system(zipCmd) != 0:
        print('生成未签名包{0}失败'.format(os.path.basename(outputUnSignedApkDir)))
        return False

    cdCmd = 'cd ' + outputDir
    apkCmd = 'java -jar -Duser.language=en -Duser.home={} {} b -o {} {}'.format(ANDROID_BUILD_HOME, APKTOOLJAR,
                                                                                outputUnSignedApkDir, srcDir)

    apkTotalComd = cdCmd + ' && ' + apkCmd
    print("apkTotalComd={0}".format(apkTotalComd))
    os.system(apkTotalComd)

    # signCmd = 'jarsigner -verbose -keystore ' + keyStore + ' -storepass kd3tkz6U9F2RVz  -keypass sk3Y3bSegxQP86 -signedjar ' + outputSignedApkDir + ' ' + outputUnSignedApkDir + ' abcyun'
    signCmd = 'apksigner sign --v1-signing-enabled true --v2-signing-enabled true --v3-signing-enabled true --ks ' + keyStore + ' --ks-pass pass:kd3tkz6U9F2RVz --key-pass pass:sk3Y3bSegxQP86 --in ' + outputUnSignedApkDir + ' --out ' + outputSignedApkDir
    delUnSignedApkCmd = 'rm ' + outputUnSignedApkDir
    delSignedApkIdSigCmd = 'rm ' + outputSignedApkDir + '.idsig'

    signCmd = cdCmd + ' && ' + signCmd + ' && ' + delUnSignedApkCmd + ' && ' + delSignedApkIdSigCmd
    print("signCmd = {0}".format(signCmd))
    if os.system(signCmd) != 0:
        print('生成签名包{0}失败'.format(os.path.basename(outputSignedApkDir)))
        return False
    print('生成签名包{0}成功'.format(os.path.basename(outputSignedApkDir)))

    return True


def main():
    pwd = sys.path[0]
    importlib.reload(sys)

    prepareEnv()
    config = loadConfig()
    srcApkPath = './' + config.apkName
    apkUnzipDir = unzipApk(srcApkPath, outputDir=outputDir)

    keyStore = pwd + '/abcyun_clinic.keystore'
    print('apkUnzipDir:' + apkUnzipDir)
    apkName = fileNameWithoutExt(srcApkPath)
    for channelItem in config.channels:
        with open(apkUnzipDir + '/assets/channel.txt', 'w') as f:
            f.write(channelItem.key)

        zipAndSignApk(pwd + '/' + apkUnzipDir, apkName + '_' + channelItem.key + '_' + channelItem.description,
                      pwd + '/' + outputDir, keyStore)

    shutil.rmtree(apkUnzipDir)
    os.system('rm ' + outputDir + '/' + os.path.basename(srcApkPath))

if __name__ == '__main__':
    main()
