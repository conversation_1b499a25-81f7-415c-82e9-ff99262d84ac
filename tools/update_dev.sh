#!/bin/bash




token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbXBsb3llZUlkIjoiZWYxNjllOTAxMzBmNGJhOTliYTM3ZTgwMDJmODdhMTAiLCJlbXBsb3llZU5hbWUiOiLkvZXpo54iLCJjbGluaWNJZCI6IjhlODNjNDA5NjM2NDRhZWZhMmQ4YmRhZDBlMDA1NTVmIiwiY2xpbmljVHlwZSI6MiwiY2hhaW5JZCI6IjBiZjg5OWI3ZGM3ODRiZTM4ZDRkYWI4OTk1YTczZTExIiwibG9naW5XYXkiOiJwYXNzd29yZCIsImNzcmZUb2tlbiI6IlJ5N3MxaTROV1l1R2tPTW9rb3dROFNhOENhWUFhV0VrIiwiaWF0IjoxNjAwODI1MDI1fQ.-L11X-PhuM9Cz4rTCfq5scPpD53Pg0qK6c7B0LjBjnk"

content=`cat upgrade_info_android.txt`

curl -X PUT 'http://dev.abczs.cn/api/v2/mobile/upgrade/update/1' \
        -H 'Connection: keep-alive' \
        -H 'Accept: application/json, text/plain, */*' \
        -H 'x-csrf-token: 88ZBu5nZBocmCcMOqc2sQigCOEw2qoYc' \
        -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36' \
        -H 'Content-Type: application/json;charset=UTF-8' \
        -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
        -H "Cookie: _abcyun_token_=${token};" \
        --data-binary "${content}" \
        --compressed \
        --insecure


content=`cat upgrade_info_ios.txt`
curl -X PUT 'http://dev.abczs.cn/api/v2/mobile/upgrade/update/2' \
    -H 'Connection: keep-alive' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'x-csrf-token: 88ZBu5nZBocmCcMOqc2sQigCOEw2qoYc' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H "Cookie: _abcyun_token_=${token};" \
    --data-binary "${content}" \
    --compressed \
    --insecure


