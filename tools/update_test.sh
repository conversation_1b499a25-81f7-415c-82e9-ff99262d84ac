#!/bin/bash




token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlbXBsb3llZUlkIjoiNTA0YTk0NTYzYmE5NDIxZDkxMzRlZTMyMTZmNGQ0ODQiLCJlbXBsb3llZU5hbWUiOiLkvZXpo54iLCJjbGluaWNJZCI6IjFlODA3MTg2YzYzYzQ3OWFiYzRkOWUwMDQyOWRiYWFhIiwiY2xpbmljVHlwZSI6MiwiY2hhaW5JZCI6IjYyOGYyYzAyZDkwYzQ4MGZhMjZmYmVkM2Q1NzllYmMyIiwibG9naW5XYXkiOiJwYXNzd29yZCIsImNzcmZUb2tlbiI6IlFCVGFqRUp4SEVxNGV5YW9XTUtvSVNHcUlRNGNTZWlJIiwiaWF0IjoxNjAwODIzMDY0fQ.VpxlGpzfyxaKVg4b4CWKUls-ved7Q9YtWMbnYAYfKz8"

content=`cat upgrade_info_android.txt`

curl -X PUT 'https://test.abczs.cn/api/v2/mobile/upgrade/update/1' \
        -H 'Connection: keep-alive' \
        -H 'Accept: application/json, text/plain, */*' \
        -H 'x-csrf-token: 88ZBu5nZBocmCcMOqc2sQigCOEw2qoYc' \
        -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36' \
        -H 'Content-Type: application/json;charset=UTF-8' \
        -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
        -H "Cookie: _abcyun_token_=${token};" \
        --data-binary "${content}" \
        --compressed \
        --insecure


content=`cat upgrade_info_ios.txt`
curl -X PUT 'https://test.abczs.cn/api/v2/mobile/upgrade/update/2' \
    -H 'Connection: keep-alive' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'x-csrf-token: 88ZBu5nZBocmCcMOqc2sQigCOEw2qoYc' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36' \
    -H 'Content-Type: application/json;charset=UTF-8' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
    -H "Cookie: _abcyun_token_=${token};" \
    --data-binary "${content}" \
    --compressed \
    --insecure


