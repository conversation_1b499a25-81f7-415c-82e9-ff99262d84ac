import React from "react";
import { Text, View } from "@hippy/react";
import { ABCNetworkPageContentStatus, NetworkView } from "../../base-ui/base-page";
import { HospitalRegisterDetail } from "../../base-business/data/hospital-register/bean";
import { HospitalAgent } from "./hospital-agent";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { ListSettingItem, ListSettingItemStyle, ListSettingRadiosItem } from "../../base-ui/views/list-setting-item";
import { showBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { SizedBox } from "../../base-ui";
import { ABCUtils } from "../../base-ui/utils/utils";
import { SafeAreaBottomView } from "../../base-ui/safe_area_view";
import { BottomSheetHelper } from "../../base-ui/abc-app-library";
import abcI18Next from "../../language/config";

interface PatientHospitalDetailViewProps {
    orderId?: string;

    editable?: boolean;
    onChange?(arg: HospitalRegisterDetail): void;
    hospitalDetail?: HospitalRegisterDetail;
}

interface PatientHospitalDetailViewStatus {}

export default class PatientHospitalDetailView extends NetworkView<PatientHospitalDetailViewProps, PatientHospitalDetailViewStatus> {
    private _longProtectInfo?: HospitalRegisterDetail;
    constructor(props: PatientHospitalDetailViewProps) {
        super(props);
    }

    componentDidMount(): void {
        if (!!this.props?.orderId) {
            this.setContentStatus(ABCNetworkPageContentStatus.loading);
            HospitalAgent.getHospitalRegDetail(this.props.orderId ?? "")
                .then((rsp) => {
                    this._longProtectInfo = rsp;
                    if (!this.props.editable) this.props.onChange?.(rsp);
                    this.setContentStatus(ABCNetworkPageContentStatus.show_data);
                })
                .catch((e) => {
                    this.setContentStatus(ABCNetworkPageContentStatus.error, e);
                });
        } else {
            this._longProtectInfo = this.props?.hospitalDetail;
        }
    }

    private showHospitalDetail(): void {
        showBottomSheet(this.createDialogView());
    }

    renderContent(): JSX.Element {
        if (!this._longProtectInfo) return <View />;
        const { statusDisplayName } = this._longProtectInfo;
        return (
            <View
                style={{
                    backgroundColor: Colors.S2,
                    paddingHorizontal: Sizes.listHorizontalMargin,
                    textAlign: "center",
                }}
                onClick={this.showHospitalDetail.bind(this)}
            >
                <ListSettingItem
                    title={"住院信息"}
                    titleStyle={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22 })}
                    content={`${!!this._longProtectInfo?.id ? "长护门诊" : "普通门诊"}${
                        !!statusDisplayName ? "(" + statusDisplayName + ")" : ""
                    }`}
                    contentStyle={{ alignItems: "flex-end" }}
                    contentTextStyle={TextStyles.t16NT6.copyWith({ lineHeight: Sizes.dp22 })}
                    itemStyle={ListSettingItemStyle.expandIcon}
                    rightArrowColor={Colors.T6}
                    height={Sizes.dp58}
                />
            </View>
        );
    }

    createDialogView(): JSX.Element {
        const { editable } = this.props;
        if (!this._longProtectInfo) return <View />;
        const {
            statusDisplayName,
            chargeType,
            dischargeReason,
            directDoctorName,
            registerDoctorName,
            registerNurseName,
            hasThroatCut,
            inpatientDays,
            amountTotal,
            registerTime,
            dischargeTime,
            clinicTypeName,
        } = this._longProtectInfo;
        return (
            <View style={{ flex: 1 }}>
                {BottomSheetHelper.createTitleBar(
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={TextStyles.t18MT1}>{`住院信息`}</Text>
                        {!!statusDisplayName && (
                            <View
                                style={{
                                    backgroundColor: Colors.bg3,
                                    borderRadius: Sizes.dp2,
                                    padding: Sizes.dp2,
                                    marginLeft: Sizes.dp6,
                                }}
                            >
                                <Text style={[TextStyles.t12NM]}>{`${statusDisplayName}`}</Text>
                            </View>
                        )}
                    </View>,
                    true
                )}
                <View style={{ paddingHorizontal: Sizes.dp16, backgroundColor: Colors.white }}>
                    <View>
                        <ListSettingRadiosItem
                            style={{ justifyContent: "space-between" }}
                            title={"就诊类型"}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            options={["长护门诊", "普通门诊"]}
                            enable={editable}
                            check={clinicTypeName}
                            marginBetweenItem={Sizes.dp24}
                            bottomLine={true}
                            onChanged={(item) => {
                                this._longProtectInfo!.id = item == "长护门诊" ? this._longProtectInfo?.__id : "";
                                this.props.onChange?.(this._longProtectInfo!);
                            }}
                        />

                        <ListSettingItem
                            title={"结算类型"}
                            content={chargeType}
                            contentStyle={{ alignItems: "flex-end" }}
                            bottomLine={true}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            contentTextStyle={TextStyles.t16NB}
                        />

                        <ListSettingItem
                            title={"在院状态"}
                            contentStyle={{ alignItems: "flex-end" }}
                            contentBuilder={() => {
                                return (
                                    <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                                        <Text style={[TextStyles.t16NB]}>{`${statusDisplayName}`}</Text>
                                        {!!dischargeReason && <SizedBox width={Sizes.dp4} />}
                                        {!!dischargeReason && <Text style={[TextStyles.t16NB]}>{`${dischargeReason}`}</Text>}
                                    </View>
                                );
                            }}
                            bottomLine={true}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                        />
                        <ListSettingItem
                            title={"责任医生"}
                            content={directDoctorName}
                            contentStyle={{ alignItems: "flex-end" }}
                            bottomLine={true}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            contentTextStyle={TextStyles.t16NB}
                        />
                        <ListSettingItem
                            title={"登记医生"}
                            content={registerDoctorName}
                            contentStyle={{ alignItems: "flex-end" }}
                            bottomLine={true}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            contentTextStyle={TextStyles.t16NB}
                        />
                        <ListSettingItem
                            title={"登记护士"}
                            content={registerNurseName}
                            contentStyle={{ alignItems: "flex-end" }}
                            bottomLine={true}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            contentTextStyle={TextStyles.t16NB}
                        />
                        <ListSettingItem
                            title={"气管切割"}
                            content={hasThroatCut ? "是" : "否"}
                            contentStyle={{ alignItems: "flex-end" }}
                            bottomLine={true}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            contentTextStyle={TextStyles.t16NB}
                        />
                        <ListSettingItem
                            title={"在院天数"}
                            content={inpatientDays?.toString()}
                            contentStyle={{ alignItems: "flex-end" }}
                            contentBuilder={() => {
                                return (
                                    <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                                        <Text style={[TextStyles.t16NB]}>{`${inpatientDays}`}</Text>
                                        {!!registerTime && <SizedBox width={Sizes.dp4} />}
                                        {!!registerTime && (
                                            <Text style={[TextStyles.t16NB]}>{`(${registerTime.format("yyyy-MM-dd")}入院${
                                                !!dischargeTime ? "~" + dischargeTime.format("yyyy-MM-dd") + "出院)" : ")"
                                            }`}</Text>
                                        )}
                                    </View>
                                );
                            }}
                            bottomLine={true}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                        />
                        <ListSettingItem
                            title={"累计费用"}
                            content={`${abcI18Next.t("￥")}${ABCUtils.formatPrice(amountTotal ?? 0)}`}
                            contentStyle={{ alignItems: "flex-end" }}
                            bottomLine={true}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            contentTextStyle={TextStyles.t16NB}
                        />
                    </View>
                    <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
                </View>
            </View>
        );
    }
}
