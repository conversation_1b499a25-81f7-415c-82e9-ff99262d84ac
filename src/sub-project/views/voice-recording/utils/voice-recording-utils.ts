/**
 * 语音录制组件工具函数和常量
 * create date 2024/06/16
 */
import { Dimensions } from "@hippy/react";
import { Sizes } from "../../../theme";
import type { ASRConfig } from "./types";

export const VoiceRecordingOverlayViewLocal = "voiceRecordingOverlayViewLocal";
export const TAG = "VoiceRecordingOverlayView";

/**
 * 获取默认ASR配置
 */

export const getDefaultASRConfig = (socketUrl: string, _params: any): ASRConfig => ({
    serverUrl: socketUrl,
    language: "zh-CN",
    sampleRate: 16000,
    channels: 1,
    audioFormat: 16,
    enableVad: true,
    enablePunctuation: true,
    enableNumberConvert: true,
    enableDirtyFilter: true,
    enableSilentDetection: true,
    reconnectionDelay: 1000,
    silentTimeout: 5000,
    silentThreshold: -40.0,
    enableForegroundService: true,
    serviceNotificationTitle: "语音识别服务",
    serviceNotificationContent: "正在进行语音识别...",
    // 添加缺失的回调间隔配置
    volumeCallbackInterval: 100,        // 音量回调间隔（毫秒）
    waveformCallbackInterval: 100,      // 波形数据回调间隔（毫秒），与音频数据处理频率一致
    extraHeaders: _params.extraHeaders,
    cookies: _params.cookies,
    connectParams: _params.connectParams,
    forceWebsockets: _params.forceWebsockets,
    namespace: _params.namespace,
    path: "/api/asr/socket.io/",
});

/**
 * 获取默认位置
 */
export const getDefaultPosition = () => ({
    top: Sizes.dp80,
    left: Dimensions.get("window").width - Sizes.dp80,
});

/**
 * 格式化时长显示
 * @param seconds 秒数
 * @returns 格式化后的时长字符串 (mm:ss)
 */
export const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
};

/**
 * 显示剩余时长
 * @param seconds
 * @param totalSeconds
 */
export const formatRemainDuration = (seconds: number, totalSeconds: number): string => {
    const remainSeconds = totalSeconds - seconds;
    return formatDuration(remainSeconds);
};

/**
 * 确保位置在屏幕范围内
 * @param position 位置对象
 * @param width 组件宽度
 * @param height 组件高度
 * @returns 安全的位置对象
 */
export const ensureSafePosition = (
    position: { top: number; left: number } | null,
    width: number,
    height: number
): { top: number; left: number } => {
    const screenWidth = Dimensions.get("window").width;
    const screenHeight = Dimensions.get("window").height;

    const defaultPos = getDefaultPosition();

    if (!position) {
        return defaultPos;
    }

    const safeTop = Math.max(0, Math.min(position.top, screenHeight - height));
    const safeLeft = Math.max(0, Math.min(position.left, screenWidth - width));

    return { top: safeTop, left: safeLeft };
};

/**
 * 解析保存的位置数据
 * @param positionData 位置数据字符串
 * @returns 解析后的位置对象或null
 */
export const parsePositionData = (positionData: string | null): { top: number; left: number } | null => {
    if (!positionData) {
        return null;
    }

    try {
        return JSON.parse(positionData);
    } catch (e) {
        console.warn(TAG, "Failed to parse saved position, using default:", e);
        return null;
    }
};
