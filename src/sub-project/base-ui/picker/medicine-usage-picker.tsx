/**
 * create by deng<PERSON><PERSON>
 * desc: 西药用法的picker
 * create date 2020/4/23
 */
import { <PERSON><PERSON><PERSON>, MedicineUsageAst, Usage } from "../../outpatient/data/western-medicine-config";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../../theme";
import React from "react";
import { Dimensions, ScrollView, Style, Text, View } from "@hippy/react";
import { InfusionMedicine, WesternMedicine } from "../../../assets/medicine_usage/western-medicine-config";
import { showBottomSheet, showModel } from "../dialog/bottom_sheet";
import _ from "lodash";
import ChineseMedicine from "../../../assets/medicine_usage/chinese-medicine-config";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { ABCNavigator } from "../views/abc-navigator";
import { ChineseMedicineUsageInfo, ChineseUsageItemInfo } from "../../outpatient/data/chinese-medicine-config";
import { DialogBuilder, DialogIndex } from "../dialog/dialog-builder";
import { AbcTextInput, KeyboardBuilder } from "../views/abc-text-input";
import { pxToDp } from "../utils/ui-utils";
import { GridView } from "../views/grid-view";
import { AbcView } from "../views/abc-view";
import { AirPharmacyUsageOptions, MedicineScopeId, UsageScopeId } from "../../charge/data/charge-bean-air-pharmacy";
import { DeviceUtils } from "../utils/device-utils";
import { BaseComponent } from "../base-component";
import { SafeAreaBottomView } from "../safe_area_view";
import FontWeight from "../../theme/font-weights";
import { ExternalConfig, ExternalFreqOptions, ExternalZhenJiuRequirement } from "../../../assets/medicine_usage/outpatient-external-config";
import { SizedBox } from "../index";
import { Toast } from "../dialog/toast";
import { NumberKeyboardBuilder } from "../views/keyboards/number-keyboard";
import { StringUtils } from "../utils/string-utils";
import { userCenter } from "../../user-center";
import { LogUtils } from "../../common-base-module/log";

interface TagProps {
    text: string;
    subText?: string;
    type: string;
    info?: string;
    select: boolean;
    textStyle?: any;
    style?: object;
    borderColor?: Color;
    backgroundColor?: Color;
    onClick?: () => void;
}

class Tag extends React.Component<TagProps> {
    static defaultProps = {
        text: "",
        select: false,
        style: {},
        textStyle: TextStyles.t14NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp22 }),
        borderColor: Colors.transparent,
        backgroundColor: Colors.white,
    };

    constructor(props: TagProps) {
        super(props);
    }

    render() {
        const { text, textStyle, backgroundColor, borderColor, type, info, style, select, subText } = this.props;
        if (!!subText) return this._renderOtherView();
        return (
            <AbcView
                style={[
                    {
                        height: Sizes.dp38,
                        justifyContent: "center",
                        backgroundColor: backgroundColor,
                        borderWidth: Sizes.dp1,
                        borderColor: borderColor,
                        borderRadius: Sizes.dp4,
                        ...style,
                    },
                    select ? { backgroundColor: Colors.tagBg } : {},
                ]}
                onClick={this.props.onClick?.bind(this, { info: text, type, obj: info })}
            >
                <Text
                    style={[textStyle, { textAlign: "center" }, select ? { fontWeight: FontWeight.medium, color: Colors.mainColor } : {}]}
                >
                    {text}
                </Text>
            </AbcView>
        );
    }

    private _renderOtherView(): JSX.Element {
        const { text, textStyle, backgroundColor, borderColor, type, info, style, select, subText } = this.props;
        return (
            <AbcView
                style={[
                    {
                        height: Sizes.dp38,
                        justifyContent: "center",
                        backgroundColor: backgroundColor,
                        borderWidth: Sizes.dp1,
                        borderColor: borderColor,
                        borderRadius: Sizes.dp4,
                        ...style,
                    },
                    select ? { backgroundColor: Colors.tagBg } : {},
                ]}
                onClick={this.props.onClick?.bind(this, { info: text, type, obj: info })}
            >
                <Text
                    style={[textStyle, { textAlign: "center", fontWeight: FontWeight.medium }, select ? { color: Colors.mainColor } : {}]}
                >
                    {text}
                </Text>
                <Text style={[TextStyles.t12NT6, { textAlign: "center" }, select ? { color: Colors.mainColor } : {}]}>{subText ?? ""}</Text>
            </AbcView>
        );
    }
}

export class TagPanel {
    static createTagPanel(
        list: Array<any>,
        layout?: number,
        init?: Usage,
        title?: string,
        options?: { tagBgColor?: string; textInput?: AbcTextInput; maxLength?: number; keyBoardBg?: Color; isShowTitle?: boolean },
        crossAxisSpacing?: number
    ): JSX.Element {
        const { width } = Dimensions.get("window");
        let itemWidth: Style;
        if (_.isNumber(layout)) {
            itemWidth = {
                width: (width - Sizes.dp32 - (layout - 1) * Sizes.dp8) / layout,
            };
        } else {
            itemWidth = {
                minWidth: Sizes.dp80,
            };
        }
        const _panelList = new Map();
        const _panelListView = [];
        const _status = !_.isEmpty(list?.[0]?.en);

        list.map((item, index) => {
            const { type, name } = item;

            let _name: string;
            if (!_.isEmpty(item.en)) {
                _name = item.en;
            } else {
                _name = name;
            }
            const ItemView = (
                <Tag
                    key={index}
                    style={{ ...itemWidth, height: _status ? Sizes.dp52 : Sizes.dp36 }}
                    select={init?.name == item.name}
                    text={_name}
                    subText={!!item.en ? name : undefined}
                    type={type}
                    info={item}
                    backgroundColor={options?.tagBgColor}
                    onClick={async () => {
                        let _value: ChineseUsageItemInfo | undefined = undefined;
                        if (item.name == "其他") {
                            _value = await MedicineUsagePicker.showCustomUsageDialog({
                                title: title,
                                maxLength: options?.maxLength,
                                value: !list?.find((t) => t?.name == init?.name) ? init?.name : undefined,
                            });
                            if (!!_value) {
                                if (options?.textInput) {
                                    options?.textInput?.setValue(_value.name, { shouldChange: true });
                                } else {
                                    ABCNavigator.pop(_value);
                                }
                            } else {
                                if (options?.textInput) {
                                    options?.textInput?.focus();
                                }
                            }
                        } else if (item.isCustom) {
                            _value = await MedicineUsagePicker.showCustomFreqDialog({
                                title: title,
                                info: item,
                            });
                            if (!!_value) {
                                if (options?.textInput) {
                                    options?.textInput?.setValue(_value.name, { shouldChange: true });
                                } else {
                                    ABCNavigator.pop(_value);
                                }
                            } else {
                                if (options?.textInput) {
                                    options?.textInput?.focus();
                                }
                            }
                        } else {
                            if (options?.textInput) {
                                options?.textInput?.setValue(_name ?? item.name, { shouldChange: true });
                                options?.textInput.finishEditing();
                                if (item.name == "【自备】") {
                                    await Toast.show(`备注为【自备】，该药品将不会纳入划价收费`, { warning: true });
                                }
                            } else {
                                ABCNavigator.pop(item);
                            }
                        }
                    }}
                />
            );
            if (_panelList.has(type)) {
                _panelList.set(type, _panelList.get(type).concat(ItemView));
            } else {
                _panelList.set(type, [ItemView]);
            }
        });
        let _index = 0;
        const lastIndex = _panelList.size - 1;
        for (const value of _panelList.values()) {
            _panelListView.push(
                <View
                    key={_index}
                    style={{
                        ...(_index != lastIndex
                            ? {
                                  borderBottomWidth: Sizes.dpHalf,
                                  borderColor: Colors.bdColor,
                              }
                            : {}),
                        ...Sizes.paddingLTRB(0, _index == 0 ? 0 : Sizes.dp11, 0, Sizes.dp13),
                    }}
                >
                    <GridView
                        itemHeight={_status ? Sizes.dp52 : Sizes.dp36}
                        crossAxisCount={layout ?? 2}
                        crossAxisSpacing={crossAxisSpacing ?? 0}
                        mainAxisSpacing={Sizes.dp8}
                    >
                        {value}
                    </GridView>
                </View>
            );
            _index++;
        }

        return (
            <View
                style={{
                    backgroundColor: Colors.prescriptionBg,
                }}
            >
                <View
                    style={{
                        backgroundColor: options?.keyBoardBg ?? Colors.keyboardBg,
                        borderTopLeftRadius: Sizes.dp6,
                        borderTopRightRadius: Sizes.dp6,
                    }}
                >
                    <SizedBox height={Sizes.dp16} />
                    {!!options?.isShowTitle && (
                        <View>
                            <Text
                                numberOfLines={1}
                                style={[
                                    TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
                                    {
                                        textAlign: "center",
                                        flexShrink: 1,
                                    },
                                ]}
                            >
                                {title ?? ""}
                            </Text>
                            <SizedBox height={Sizes.dp12} />
                        </View>
                    )}
                    <ScrollView
                        style={{
                            height: pxToDp(325),
                            paddingHorizontal: Sizes.dp16,
                            backgroundColor: options?.keyBoardBg ?? Colors.keyboardBg,
                        }}
                    >
                        {_panelListView}
                    </ScrollView>
                </View>
            </View>
        );
    }

    /**
     * 按照分类一行一行展示
     * @param list
     * @param layout
     * @param init
     * @param title
     * @param options
     * @param crossAxisSpacing
     */
    static createMultipleRowTagPanel(
        list: Array<any>,
        layout?: number,
        initIndex?: Map<number, number>,
        title?: string,
        options?: {
            tagBgColor?: string;
            textInput?: AbcTextInput;
            maxLength?: number;
            height?: number;
            onChange?: (value: any) => void;
        },
        crossAxisSpacing?: number
    ): JSX.Element {
        const { width } = Dimensions.get("window");
        const _panelList = new Map();
        const _panelListView = [];
        let colRowView: JSX.Element;
        const _status = !_.isEmpty(list?.[0]?.en);
        list.map((item, index) => {
            const { type, name, onChange } = item;
            colRowView = (
                <View style={[index < list.length - 1 ? { marginBottom: Sizes.dp16 } : {}]}>
                    <Text style={[TextStyles.t16NT1.copyWith({ color: Colors.t2 }), { textAlign: "center" }]}>{item.name ?? ""}</Text>
                    <SizedBox height={Sizes.dp12} />
                    <View style={{ flex: 1 }}>
                        <GridView
                            itemHeight={_status ? Sizes.dp52 : Sizes.dp36}
                            crossAxisCount={item.col ?? 2}
                            crossAxisSpacing={crossAxisSpacing ?? Sizes.dp8}
                            mainAxisSpacing={Sizes.dp8}
                        >
                            {(item.options as { name: string; id?: string }[])?.map((sub, subIndex) => {
                                return (
                                    <Tag
                                        key={subIndex}
                                        style={{
                                            width:
                                                !!item.col && _.isNumber(item.col)
                                                    ? (width - Sizes.dp32 - (item.col - 1) * Sizes.dp8) / item.col
                                                    : Sizes.dp80,
                                            height: _status ? Sizes.dp52 : Sizes.dp36,
                                        }}
                                        select={subIndex == initIndex?.get(index)}
                                        text={sub.name}
                                        subText={!!item.en ? name : undefined}
                                        type={type}
                                        info={sub.name}
                                        backgroundColor={options?.tagBgColor}
                                        onClick={async () => {
                                            let _value: ChineseUsageItemInfo | undefined = undefined;
                                            if (!!onChange) {
                                                onChange(sub.id);
                                            } else if (sub.name == "其他") {
                                                _value = await MedicineUsagePicker.showCustomUsageDialog({
                                                    title: title,
                                                    maxLength: options?.maxLength,
                                                });
                                                if (!!_value) {
                                                    if (options?.textInput) {
                                                        options?.textInput?.setValue(_value.name, { shouldChange: true });
                                                    } else {
                                                        ABCNavigator.pop(_value);
                                                    }
                                                } else {
                                                    if (options?.textInput) {
                                                        options?.textInput?.focus();
                                                    }
                                                }
                                            } else {
                                                if (options?.textInput) {
                                                    options?.textInput?.setValue(!!item.prefix ? item.prefix + sub.name : sub.name, {
                                                        shouldChange: true,
                                                    });
                                                    options?.textInput.finishEditing();
                                                    if (sub.name == "【自备】") {
                                                        await Toast.show(`备注为【自备】，该药品将不会纳入划价收费`, { warning: true });
                                                    }
                                                } else if (!!options?.onChange) {
                                                    options?.onChange(sub);
                                                } else {
                                                    ABCNavigator.pop(item);
                                                }
                                            }
                                        }}
                                    />
                                );
                            })}
                        </GridView>
                    </View>
                </View>
            );
            if (_panelList.has(type)) {
                _panelList.set(type, _panelList.get(type).concat(colRowView));
            } else {
                _panelList.set(type, [colRowView]);
            }
        });
        let _index = 0;
        const lastIndex = _panelList.size - 1;
        for (const value of _panelList.values()) {
            _panelListView.push(
                <View
                    key={_index}
                    style={{
                        ...(_index != lastIndex
                            ? {
                                  borderBottomWidth: Sizes.dpHalf,
                                  borderColor: Colors.bdColor,
                              }
                            : {}),
                        ...Sizes.paddingLTRB(0, _index == 0 ? 0 : Sizes.dp11, 0, Sizes.dp13),
                    }}
                >
                    {value}
                </View>
            );
            _index++;
        }

        return (
            <View style={{ backgroundColor: Colors.keyboardBg }}>
                <SizedBox height={Sizes.dp16} />
                <ScrollView
                    style={{
                        height: options?.height ?? pxToDp(325),
                        paddingHorizontal: Sizes.dp16,
                        backgroundColor: Colors.keyboardBg,
                    }}
                >
                    {_panelListView}
                </ScrollView>
            </View>
        );
    }

    /**
     * 一行展示多列
     * @param list
     * @param layout
     * @param init
     * @param title
     * @param options
     * @param crossAxisSpacing
     */
    static createMultipleColumnTagPanel(
        list: Array<any>,
        layout?: number,
        init?: Usage,
        title?: string,
        options?: { tagBgColor?: string; textInput?: AbcTextInput; maxLength?: number },
        crossAxisSpacing?: number
    ): JSX.Element {
        const { width } = Dimensions.get("window");
        const colWidth: Style = {
            width: (width - list.length * Sizes.dpHalf) / list.length,
        };
        const _panelList = new Map();
        const _panelListView = [];
        let colRowView: JSX.Element;
        const _status = !_.isEmpty(list?.[0]?.en);
        list.map((item, index) => {
            const { type, name } = item;
            colRowView = (
                <View
                    style={[
                        index < list.length - 1 ? { borderRightWidth: Sizes.dpHalf, borderRightColor: Colors.dividerLineColor } : {},
                        { paddingHorizontal: Sizes.dp16 },
                    ]}
                >
                    <Text style={[TextStyles.t16NT1.copyWith({ color: Colors.t2 }), { textAlign: "center" }]}>{item.name ?? ""}</Text>
                    <SizedBox height={Sizes.dp12} />
                    <View style={{ flex: 1 }}>
                        <GridView
                            itemHeight={_status ? Sizes.dp52 : Sizes.dp36}
                            crossAxisCount={item.col ?? 2}
                            crossAxisSpacing={crossAxisSpacing ?? Sizes.dp8}
                            mainAxisSpacing={Sizes.dp8}
                        >
                            {(item.options as { name: string }[])?.map((sub, subIndex) => {
                                return (
                                    <Tag
                                        key={subIndex}
                                        style={{
                                            width: (colWidth.width - Sizes.dp32 - (item.col - 1) * Sizes.dp8) / item.col,
                                            height: _status ? Sizes.dp52 : Sizes.dp36,
                                        }}
                                        select={false}
                                        text={sub.name}
                                        subText={!!item.en ? name : undefined}
                                        type={type}
                                        info={sub.name}
                                        backgroundColor={options?.tagBgColor}
                                        onClick={async () => {
                                            let _value: ChineseUsageItemInfo | undefined = undefined;
                                            if (sub.name == "其他") {
                                                _value = await MedicineUsagePicker.showCustomUsageDialog({
                                                    title: title,
                                                    maxLength: options?.maxLength,
                                                });
                                                if (!!_value) {
                                                    if (options?.textInput) {
                                                        options?.textInput?.setValue(_value.name, { shouldChange: true });
                                                    } else {
                                                        ABCNavigator.pop(_value);
                                                    }
                                                } else {
                                                    if (options?.textInput) {
                                                        options?.textInput?.focus();
                                                    }
                                                }
                                            } else {
                                                if (options?.textInput) {
                                                    options?.textInput?.setValue(sub.name, { shouldChange: true });
                                                    if (index == list.length - 1) {
                                                        options?.textInput.finishEditing();
                                                    }
                                                    if (sub.name == "【自备】") {
                                                        await Toast.show(`备注为【自备】，该药品将不会纳入划价收费`, { warning: true });
                                                    }
                                                } else {
                                                    ABCNavigator.pop(item);
                                                }
                                            }
                                        }}
                                    />
                                );
                            })}
                        </GridView>
                    </View>
                </View>
            );
            if (_panelList.has(type)) {
                _panelList.set(type, _panelList.get(type).concat(colRowView));
            } else {
                _panelList.set(type, [colRowView]);
            }
        });
        let _index = 0;
        const lastIndex = _panelList.size - 1;
        for (const value of _panelList.values()) {
            _panelListView.push(
                <View
                    key={_index}
                    style={{
                        ...(_index != lastIndex
                            ? {
                                  borderBottomWidth: Sizes.dpHalf,
                                  borderColor: Colors.bdColor,
                              }
                            : {}),
                        ...Sizes.paddingLTRB(0, _index == 0 ? 0 : Sizes.dp11, 0, Sizes.dp13),
                        flexDirection: "row",
                    }}
                >
                    {value}
                </View>
            );
            _index++;
        }

        return (
            <View style={{ backgroundColor: Colors.keyboardBg }}>
                <SizedBox height={Sizes.dp16} />
                <ScrollView
                    style={{
                        height: pxToDp(325),
                        backgroundColor: Colors.keyboardBg,
                    }}
                >
                    {_panelListView}
                </ScrollView>
            </View>
        );
    }

    /**
     * 按照分类一行一行展示
     * @param list
     * @param layout
     * @param init
     * @param title
     * @param options
     * @param crossAxisSpacing
     */
    // static createMultipleRowTagPanel(
    //     list: Array<any>,
    //     layout?: number,
    //     init?: Usage,
    //     title?: string,
    //     options?: { tagBgColor?: string; textInput?: AbcTextInput; maxLength?: number },
    //     crossAxisSpacing?: number
    // ): JSX.Element {
    //     const { width } = Dimensions.get("window");
    //     const _panelList = new Map();
    //     const _panelListView = [];
    //     let colRowView: JSX.Element;
    //     const _status = !_.isEmpty(list?.[0]?.en);
    //     list.map((item, index) => {
    //         const { type, name } = item;
    //         colRowView = (
    //             <View style={[index < list.length - 1 ? { marginBottom: Sizes.dp16 } : {}]}>
    //                 <Text style={[TextStyles.t16NT1.copyWith({ color: Colors.t2 }), { textAlign: "center" }]}>{item.name ?? ""}</Text>
    //                 <SizedBox height={Sizes.dp12} />
    //                 <View style={{ flex: 1 }}>
    //                     <GridView
    //                         itemHeight={_status ? Sizes.dp52 : Sizes.dp36}
    //                         crossAxisCount={item.col ?? 2}
    //                         crossAxisSpacing={crossAxisSpacing ?? Sizes.dp8}
    //                         mainAxisSpacing={Sizes.dp8}
    //                     >
    //                         {(item.options as { name: string }[])?.map((sub, subIndex) => {
    //                             return (
    //                                 <Tag
    //                                     key={subIndex}
    //                                     style={{
    //                                         width:
    //                                             !!item.col && _.isNumber(item.col)
    //                                                 ? (width - Sizes.dp32 - (item.col - 1) * Sizes.dp8) / item.col
    //                                                 : Sizes.dp80,
    //                                         height: _status ? Sizes.dp52 : Sizes.dp36,
    //                                     }}
    //                                     select={false}
    //                                     text={sub.name}
    //                                     subText={!!item.en ? name : undefined}
    //                                     type={type}
    //                                     info={sub.name}
    //                                     backgroundColor={options?.tagBgColor}
    //                                     onClick={async () => {
    //                                         let _value: ChineseUsageItemInfo | undefined = undefined;
    //                                         if (sub.name == "其他") {
    //                                             _value = await MedicineUsagePicker.showCustomUsageDialog({
    //                                                 title: title,
    //                                                 maxLength: options?.maxLength,
    //                                             });
    //                                             if (!!_value) {
    //                                                 if (options?.textInput) {
    //                                                     options?.textInput?.setValue(_value.name, { shouldChange: true });
    //                                                 } else {
    //                                                     ABCNavigator.pop(_value);
    //                                                 }
    //                                             } else {
    //                                                 if (options?.textInput) {
    //                                                     options?.textInput?.focus();
    //                                                 }
    //                                             }
    //                                         } else {
    //                                             if (options?.textInput) {
    //                                                 options?.textInput?.setValue(!!item.prefix ? item.prefix + sub.name : sub.name, {
    //                                                     shouldChange: true,
    //                                                 });
    //                                                 options?.textInput.finishEditing();
    //                                                 if (sub.name == "【自备】") {
    //                                                     await Toast.show(`备注为【自备】，该药品将不会纳入划价收费`, { warning: true });
    //                                                 }
    //                                             } else {
    //                                                 ABCNavigator.pop(item);
    //                                             }
    //                                         }
    //                                     }}
    //                                 />
    //                             );
    //                         })}
    //                     </GridView>
    //                 </View>
    //             </View>
    //         );
    //         if (_panelList.has(type)) {
    //             _panelList.set(type, _panelList.get(type).concat(colRowView));
    //         } else {
    //             _panelList.set(type, [colRowView]);
    //         }
    //     });
    //     let _index = 0;
    //     const lastIndex = _panelList.size - 1;
    //     for (const value of _panelList.values()) {
    //         _panelListView.push(
    //             <View
    //                 key={_index}
    //                 style={{
    //                     ...(_index != lastIndex
    //                         ? {
    //                               borderBottomWidth: Sizes.dpHalf,
    //                               borderColor: Colors.bdColor,
    //                           }
    //                         : {}),
    //                     ...Sizes.paddingLTRB(0, _index == 0 ? 0 : Sizes.dp11, 0, Sizes.dp13),
    //                 }}
    //             >
    //                 {value}
    //             </View>
    //         );
    //         _index++;
    //     }
    //
    //     return (
    //         <View style={{ backgroundColor: Colors.keyboardBg }}>
    //             <SizedBox height={Sizes.dp16} />
    //             <ScrollView
    //                 style={{
    //                     height: pxToDp(325),
    //                     paddingHorizontal: Sizes.dp16,
    //                     backgroundColor: Colors.keyboardBg,
    //                 }}
    //             >
    //                 {_panelListView}
    //             </ScrollView>
    //         </View>
    //     );
    // }
}

interface RefactorTagProps {
    list: Array<any>;
    layout?: number;
    init?: string;
    clickEvent?: (name: string) => void;
}

export class RefactorTag extends BaseComponent<RefactorTagProps> {
    render(): JSX.Element {
        const { list, layout, init, clickEvent } = this.props;
        const _panelList = new Map();
        const _panelListView = [];

        list.map((item, index) => {
            const { type, name } = item;
            let _name;
            if (!_.isEmpty(item.en)) {
                _name = `${item.en}  ${name}`;
            } else {
                _name = name;
            }
            const _selected = init == item.name;
            const ItemView = (
                <AbcView
                    key={index}
                    style={[
                        {
                            paddingVertical: Sizes.dp8,
                            backgroundColor: _selected ? Colors.theme2Mask8 : Colors.bg1,
                            borderRadius: Sizes.dp4,
                        },
                    ]}
                    onClick={() => {
                        clickEvent?.(item.name);
                    }}
                >
                    <Text
                        style={[
                            _selected ? TextStyles.t14MM : TextStyles.t14NT2.copyWith({ color: Colors.t2 }),
                            { alignSelf: "center", lineHeight: Sizes.dp20 },
                        ]}
                    >
                        {_name ?? ""}
                    </Text>
                </AbcView>
            );
            if (_panelList.has(type)) {
                _panelList.set(type, _panelList.get(type).concat(ItemView));
            } else {
                _panelList.set(type, [ItemView]);
            }
        });
        let _index = 0;
        const lastIndex = _panelList.size - 1;
        for (const value of _panelList.values()) {
            _panelListView.push(
                <View
                    key={_index}
                    style={{
                        ...(_index != lastIndex
                            ? {
                                  borderBottomWidth: 1,
                                  borderColor: Colors.dividerLineColor,
                              }
                            : {}),
                        ...Sizes.paddingLTRB(0, Sizes.dp12),
                    }}
                >
                    <GridView itemHeight={Sizes.dp36} crossAxisCount={layout ?? 2} crossAxisSpacing={Sizes.dp8} mainAxisSpacing={Sizes.dp8}>
                        {value}
                    </GridView>
                </View>
            );
            _index++;
        }

        return <View style={{ minHeight: pxToDp(260) }}>{_panelListView}</View>;
    }
}

export class MedicineUsagePicker {
    static async chooseWesternMedicineUsage(initial?: Usage): Promise<Usage> {
        const { usage } = WesternMedicine;
        const _usage = usage.map((item) => JsonMapper.deserialize(Usage, item));
        _usage.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        const view = TagPanel.createTagPanel(_usage, 4, initial, "用法");
        return await this._showPanel({ title: "用法", view: view });
    }

    static async chooseWesternMedicineFreq(initial?: Freq): Promise<Freq> {
        const { freq } = WesternMedicine;
        const _freq = freq.map((item) => JsonMapper.deserialize(Freq, item));
        const view = TagPanel.createTagPanel(_freq, 3, initial, "频率");
        return await this._showPanel({ title: "频率", view: view });
    }

    //选择中药用法
    static async chooseChineseMedicineUsage(initial?: ChineseUsageItemInfo): Promise<ChineseUsageItemInfo | undefined> {
        const { usages } = ChineseMedicine;
        const _usages = usages.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        _usages.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));

        const view = TagPanel.createTagPanel(_usages, 4, initial, "用法");
        return await this._showPanel({ title: "用法", view: view });
    }

    //选择中药煎法
    static async chooseChineseSpecialRequirement(initial?: ChineseUsageItemInfo): Promise<any> {
        const { specialRequirement } = ChineseMedicine;
        const _specialRequirement = specialRequirement.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));

        const view = TagPanel.createTagPanel(_specialRequirement, 3, initial, "煎法", { tagBgColor: Colors.bg1 });
        return await showModel({ title: "煎法", view: view, showRadius: true });
    }

    //选择每日剂量
    static async chooseChineseMedicineDailyDosage(initial?: ChineseUsageItemInfo): Promise<ChineseUsageItemInfo | undefined> {
        const { dailyDosage } = ChineseMedicine;
        const _dailyDosage = dailyDosage.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        _dailyDosage.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        const view = TagPanel.createTagPanel(_dailyDosage, 4, initial, "每日剂量");
        return await this._showPanel({ title: "每日剂量", view: view });
    }

    //每日次数
    static async chooseChineseMedicineFreq(
        initial?: ChineseUsageItemInfo,
        usage?: string,
        isAirPharmacy?: boolean
    ): Promise<ChineseUsageItemInfo | undefined> {
        let freq = ChineseMedicine.freq;
        if (usage == "制膏") {
            freq = isAirPharmacy ? ChineseMedicine.freqWithZhiGaoAir : ChineseMedicine.freqWithZhiGao;
        } else if (usage == "制丸") {
            freq = isAirPharmacy ? ChineseMedicine.freqWithZhiGaoAir : ChineseMedicine.freqWithZhiGao;
        } else if (usage == "打粉") {
            freq = isAirPharmacy ? ChineseMedicine.freqWithZhiGaoAir : ChineseMedicine.freqWithZhiGao;
        }
        const _freq = freq.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        _freq.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));

        const view = TagPanel.createTagPanel(_freq, 4, initial, "每日次数");
        return await this._showPanel({ title: "每日次数", view: view });
    }

    //每日次数
    static async chooseChineseMedicineUsageDays(initial?: ChineseUsageItemInfo, usage?: string): Promise<ChineseUsageItemInfo | undefined> {
        let usageDays = ChineseMedicine.usageDays;
        if (usage == "制膏") {
            usageDays = ChineseMedicine.usageDays;
        } else if (usage == "制丸") {
            usageDays = ChineseMedicine.usageDays;
        } else if (usage == "打粉") {
            usageDays = ChineseMedicine.usageDays;
        }
        const _usageDays = usageDays.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        _usageDays.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));

        const view = TagPanel.createTagPanel(_usageDays, 4, initial, "服用天数");
        return await showModel({ title: "服用天数", view: view });
    }

    //每次用量
    static async chooseChineseMedicineUsageLevel(
        initial?: ChineseUsageItemInfo,
        usage?: string,
        isAirPharmacy?: boolean,
        medicineStateScopeId?: string
    ): Promise<ChineseUsageItemInfo> {
        let usageLevel = ChineseMedicine.usageLevel;
        if (usage == "制膏") {
            usageLevel =
                isAirPharmacy && medicineStateScopeId == MedicineScopeId.daiZhuang.toString()
                    ? ChineseMedicine.usageLevelWithZhiGaoAirBag
                    : ChineseMedicine.usageLevelWithZhiGao;
        } else if (usage == "制丸") {
            usageLevel = isAirPharmacy ? ChineseMedicine.usageLevelWithZhiWanAir : ChineseMedicine.usageLevelWithZhiWan;
        } else if (usage == "打粉") {
            usageLevel = isAirPharmacy ? ChineseMedicine.usageLevelWithDaFenAir : ChineseMedicine.usageLevelWithDaFen;
        }
        const _usageLevel = usageLevel.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        _usageLevel.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));

        const view = TagPanel.createTagPanel(_usageLevel, 4, initial, "每次用量");
        return await showModel({ title: "每次用量", view: view });
    }

    quChong(str: string): string {
        let newStr = "";
        const len = str.length;
        for (let i = 0; i < len; i++) {
            if (newStr.indexOf(str[i]) == -1) {
                newStr = newStr + str[i];
            }
        }
        return newStr;
    }

    static showCustomUsageDialog(options: {
        title?: string;
        value?: string;
        enableDefaultToolBar?: boolean;
        maxLength?: number;
        rightCustomization?: (callback: (text: string) => void) => JSX.Element | JSX.Element[]; // 右侧自定义组件
    }): Promise<ChineseUsageItemInfo> {
        let _customValue: string;
        _customValue = options.value ?? ""; // 确定前未触发onChangeText则使用默认值
        const _dialog = new DialogBuilder();
        _dialog.title = options.title ?? "";
        _dialog.positive = "确认";
        _dialog.negative = "取消";
        let _ref: AbcTextInput | null = null;
        _dialog.content = (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        height: Sizes.dp54,
                        borderRadius: Sizes.dp4,
                        backgroundColor: Colors.bg1,
                    },
                ]}
            >
                <AbcTextInput
                    ref={(ref) => {
                        _ref = ref;
                    }}
                    autoFocus={true}
                    multiline={false}
                    resizeMode={true}
                    style={{
                        flex: 1,
                        height: Sizes.dp54,
                        lineHeight: Sizes.dp54,
                        paddingHorizontal: DeviceUtils.isAndroid() ? 0 : 6,
                        ...TextStyles.t16NB,
                    }}
                    maxLength={options?.maxLength}
                    placeholder={`请输入${options.title}`}
                    placeholderTextColor={Colors.T4}
                    onChangeText={(text: string) => {
                        _customValue = text;
                    }}
                    defaultValue={options.value}
                    enableDefaultToolBar={options.enableDefaultToolBar}
                />
                {options.rightCustomization &&
                    options.rightCustomization((text: string) => {
                        let addStr = "";
                        addStr = addStr.replace(/，/gi, ",");
                        addStr = addStr.replace("；", ";");
                        for (let i = 0; i < text.length; i++) {
                            if (!_customValue.includes(text[i])) {
                                addStr += text[i];
                            }
                        }
                        _ref?.setValue(!!_customValue ? _customValue + addStr : text);
                        _customValue = _customValue + addStr;
                    })}
            </View>
        );
        _dialog.onClick = (index) => {
            if (index == DialogIndex.negative) {
                ABCNavigator.pop();
                return;
            }
            if (index == DialogIndex.positive) ABCNavigator.pop(JsonMapper.deserialize(ChineseUsageItemInfo, { name: _customValue }));
        };
        return _dialog.show();
    }

    static showCustomFreqDialog(options: {
        title?: string;
        value?: string;
        enableDefaultToolBar?: boolean;
        maxLength?: number;
        info?: any;
    }): Promise<ChineseUsageItemInfo> {
        const _dialog = new DialogBuilder();
        _dialog.title = options.title ?? "";
        _dialog.positive = "确认";
        _dialog.negative = "取消";
        const _info: Freq = { ...options.info };
        let unit = "",
            hMultiple = 1;
        switch (_info.namePYFirst) {
            case "NT1C": {
                unit = "天";
                hMultiple = 24;
                break;
            }
            case "NZ1C": {
                unit = "周";
                hMultiple = 24 * 7;
                break;
            }
            case "NXS1C": {
                unit = "小时";
                hMultiple = 1;
                break;
            }
        }
        const _CustomFreqInputView: React.FC = () => {
            const [time, setTime] = React.useState(_info.time);
            const [en, setEn] = React.useState(_info.en);
            return (
                <View style={[ABCStyles.rowAlignCenter]}>
                    <Text style={[TextStyles.t16NT1, { marginRight: Sizes.dp12 }]}>每</Text>
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                height: Sizes.dp54,
                                borderRadius: Sizes.dp4,
                                backgroundColor: Colors.bg1,
                            },
                        ]}
                    >
                        <AbcTextInput
                            autoFocus={true}
                            multiline={false}
                            resizeMode={true}
                            style={{
                                flex: 1,
                                height: Sizes.dp54,
                                width: Sizes.dp128,
                                lineHeight: Sizes.dp54,
                                paddingHorizontal: DeviceUtils.isAndroid() ? 0 : 6,
                                textAlign: "center",
                                ...TextStyles.t16NB,
                            }}
                            maxLength={2}
                            placeholderTextColor={Colors.T4}
                            onChangeText={(text: string) => {
                                _info.time = Number(text) * hMultiple;
                                const sameItem = WesternMedicine.freq.find((item) => item.time == _info.time);
                                if (!!sameItem) {
                                    _info.en = sameItem.en;
                                } else {
                                    _info.en = StringUtils.replaceMiddleChars(options.info.en ?? "", text);
                                }
                                setTime(_info.time);
                                setEn(_info.en);
                            }}
                            defaultValue={""}
                            enableDefaultToolBar={false}
                            customKeyboardBuilder={new NumberKeyboardBuilder()}
                        />
                    </View>
                    <Text style={[TextStyles.t16NT1, { marginLeft: Sizes.dp12 }]}>{`${unit}`}1次</Text>
                    <View style={[{ marginLeft: Sizes.dp12 }]}>
                        <Text style={[TextStyles.t16NT1]}>{!!time ? en : ""}</Text>
                    </View>
                </View>
            );
        };
        _dialog.content = <_CustomFreqInputView />;
        _dialog.onClick = (index) => {
            if (index == DialogIndex.negative) {
                ABCNavigator.pop();
                return;
            }
            if (index == DialogIndex.positive) ABCNavigator.pop(JsonMapper.deserialize(ChineseUsageItemInfo, { ..._info, name: _info.en }));
        };
        return _dialog.show();
    }

    private static _showPanel<T>(options: { title?: string; view: JSX.Element | JSX.Element[] }): Promise<T> {
        /**
         * @description border-style兼容ios上单边小数布局异常问题
         */
        const view = (
            <View
                style={{
                    backgroundColor: Colors.panelBg,
                    borderTopRightRadius: Math.floor(Sizes.dp6),
                    borderTopLeftRadius: Math.floor(Sizes.dp6),
                }}
            >
                <View>{options.view}</View>
                <SafeAreaBottomView />
            </View>
        );
        return showBottomSheet(view);
    }
}

export class WesternMedicineUsageKeyboard<T> implements KeyboardBuilder {
    initial?: T;
    private customUsages: any[] = [];
    private cloneUsage: any[] = [];
    private isLoaded = false;

    constructor(initial?: T) {
        this.initial = initial;
        // 在构造函数中初始化，但不阻塞构造函数
        this.loadCustomUsages();
    }

    private async loadCustomUsages() {
        try {
            const { usage } = WesternMedicine;
            this.cloneUsage = _.cloneDeep(usage);
            const customUsagesResult = await userCenter.getClinicCustomUsagesConfig();
            if (customUsagesResult?.customUsages && customUsagesResult.customUsages.length > 0) {
                this.customUsages = customUsagesResult.customUsages;
                // 处理自定义用法
                this.customUsages.forEach((customUsage: any) => {
                    // 检查是否已存在相同名称的用法
                    if (!this.cloneUsage.find((item: Usage) => item.name === customUsage.name)) {
                        // 查找after指定的项目的索引
                        const afterIndex = this.cloneUsage.findIndex((item: Usage) => item.name === customUsage.after);
                        if (afterIndex !== -1) {
                            // 如果找到了after指定的项目，将自定义用法插入到该项目后面
                            this.cloneUsage.splice(afterIndex + 1, 0, {
                                name: customUsage.name!,
                                type: customUsage.type!,
                                latin: customUsage.latin!,
                            });
                        }
                    }
                });
            }
            this.isLoaded = true;
        } catch (e) {
            LogUtils.e("加载自定义用法失败:", e);
            this.isLoaded = true;
        }
    }

    build(textInput: AbcTextInput): JSX.Element {
        const { usage } = WesternMedicine;
        // 使用原始用法或已处理的自定义用法
        const usageData = this.isLoaded && this.cloneUsage.length > 0 ? this.cloneUsage : usage;
        const _usage = usageData.map((item) => JsonMapper.deserialize(Usage, item));
        _usage.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        return TagPanel.createTagPanel(_usage, 4, this.initial, "用法", { textInput: textInput, maxLength: 8 });
    }

    keyboardHeight(): number {
        return pxToDp(325);
    }
}

export class WesternMedicineFreqKeyboard extends WesternMedicineUsageKeyboard<Freq> {
    build(textInput: AbcTextInput): JSX.Element {
        const { freq } = WesternMedicine;
        const _freq = freq.map((item) => JsonMapper.deserialize(Freq, item));
        return TagPanel.createTagPanel(_freq, 3, this.initial, "频率", { textInput }, Sizes.dp8);
    }
}

export class WesternMedicineRemarkKeyboard extends WesternMedicineUsageKeyboard<ChineseUsageItemInfo> {
    isShowSelfProvided?: boolean; //是否显示自备
    constructor(inital?: ChineseUsageItemInfo, isShowSelfProvided?: boolean) {
        super(inital);
        this.isShowSelfProvided = isShowSelfProvided;
    }
    build(textInput: AbcTextInput): JSX.Element {
        const { remarkList } = WesternMedicine;
        const _remark = remarkList.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        _remark.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        if (this.isShowSelfProvided) {
            _remark.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "【自备】" }));
        }

        return TagPanel.createTagPanel(_remark, 2, this.initial, "备注", { textInput, maxLength: 10 }, Sizes.dp8);
    }
}

export class WesternMedicineAstKeyboard extends WesternMedicineUsageKeyboard<MedicineUsageAst> {
    build(textInput: AbcTextInput): JSX.Element {
        const { astList } = WesternMedicine;
        const _ast = astList.map((item) => JsonMapper.deserialize(MedicineUsageAst, item));
        return TagPanel.createTagPanel(_ast, 3, this.initial, "", { textInput }, Sizes.dp8);
    }
}

export class ChineseMedicineUsageKeyboard extends WesternMedicineUsageKeyboard<ChineseUsageItemInfo> {
    usageList?: AirPharmacyUsageOptions[];
    constructor(initial?: ChineseUsageItemInfo, usageList?: AirPharmacyUsageOptions[]) {
        super(initial);
        this.usageList = usageList;
    }
    build(textInput: AbcTextInput): JSX.Element {
        const { usages } = ChineseMedicine;
        const _usages = usages.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        _usages.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        let existUsageList;
        if (!!this.usageList) {
            existUsageList = this.usageList?.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        }

        return TagPanel.createTagPanel(existUsageList ?? _usages, 4, this.initial, "用法", { textInput, maxLength: 10 });
    }
}

export class ChineseSpecialRequirementKeyboard extends WesternMedicineUsageKeyboard<ChineseUsageItemInfo> {
    build(textInput: AbcTextInput): JSX.Element {
        const { specialRequirement } = ChineseMedicine;
        const _specialRequirement = specialRequirement.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));

        return TagPanel.createTagPanel(_specialRequirement, 3, this.initial, "煎法", { tagBgColor: Colors.bg1, textInput });
    }
}

export class ChineseMedicineDailyDosageKeyboard extends WesternMedicineUsageKeyboard<ChineseUsageItemInfo> {
    dosageList?: AirPharmacyUsageOptions[];
    constructor(initial?: ChineseUsageItemInfo, dosageList?: AirPharmacyUsageOptions[]) {
        super(initial);
        this.dosageList = dosageList;
    }
    build(textInput: AbcTextInput): JSX.Element {
        const { dailyDosage } = ChineseMedicine;
        const _dailyDosage = dailyDosage.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        _dailyDosage.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        let airDosageList;
        if (!!this.dosageList) {
            airDosageList = this.dosageList?.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        }
        return TagPanel.createTagPanel(airDosageList ?? _dailyDosage, 4, this.initial, "每日剂量", { textInput, maxLength: 10 });
    }
}

export class ChineseMedicineFreqKeyboard extends WesternMedicineUsageKeyboard<ChineseUsageItemInfo> {
    usage?: string;
    isAirPharmacy?: boolean;
    medicineStateScopeId?: MedicineScopeId;
    usageScopeId?: UsageScopeId;
    _freqList?: AirPharmacyUsageOptions[];
    constructor(
        initial?: ChineseUsageItemInfo,
        usage?: string,
        isAirPharmacy?: boolean,
        medicineStateScopeId?: MedicineScopeId,
        usageScopeId?: UsageScopeId,
        freqList?: AirPharmacyUsageOptions[]
    ) {
        super(initial);
        this.usage = usage;
        this.isAirPharmacy = isAirPharmacy;
        this.medicineStateScopeId = medicineStateScopeId;
        this.usageScopeId = usageScopeId;
        this._freqList = freqList;
    }
    build(textInput: AbcTextInput): JSX.Element {
        let freqList: ChineseUsageItemInfo[] = [];
        freqList = ChineseMedicine.freq;
        let airFreqList;
        if (this.isAirPharmacy) {
            const { usageScopeId, medicineStateScopeId } = this;
            const UsageInfoMap = ChineseMedicineUsageInfo.GetChineseUsageParam;
            const value = UsageInfoMap(medicineStateScopeId) || UsageInfoMap(usageScopeId);
            if (value && value.freq) {
                freqList = value.freq;
            }
        }
        const _freq = freqList.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        _freq.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        if (!!this._freqList) {
            airFreqList = this._freqList?.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        }

        return TagPanel.createTagPanel(airFreqList ?? _freq, 4, this.initial, "每日次数", { textInput, maxLength: 10 });
    }
}

//服用量
export class ChineseMedicineUsageDoseKeyboard extends ChineseMedicineFreqKeyboard {
    medicineStateScopeId?: MedicineScopeId;
    usageScopeId?: UsageScopeId;
    specification?: string;
    isAirPharmacy?: boolean;
    _usageLevelList?: AirPharmacyUsageOptions[];
    constructor(
        initial?: ChineseUsageItemInfo,
        usage?: string,
        isAirPharmacy?: boolean,
        medicineStateScopeId?: MedicineScopeId,
        usageScopeId?: UsageScopeId,
        specification?: string,
        usageLevelList?: AirPharmacyUsageOptions[]
    ) {
        super(initial);
        this.usage = usage;
        this.isAirPharmacy = isAirPharmacy;
        this.medicineStateScopeId = medicineStateScopeId;
        this.usageScopeId = usageScopeId;
        this.specification = specification;
        this._usageLevelList = usageLevelList;
    }
    build(textInput: AbcTextInput): JSX.Element {
        let usageLevelList: ChineseUsageItemInfo[] = [],
            airUsageLevelList;
        const { usageLevel, zhiGaoUsageLevel, zhiWanUsageLevel, daFenUsageLevel, keLiChongFuUsageLevel } = ChineseMedicine;
        switch (this.usage) {
            case "制膏":
                usageLevelList = zhiGaoUsageLevel;
                break;
            case "制丸":
                usageLevelList = zhiWanUsageLevel;
                break;
            case "打粉":
                usageLevelList = daFenUsageLevel;
                break;
            default:
                usageLevelList = usageLevel;
        }
        if (!!this.specification && this.specification.indexOf("颗粒") > -1 && this.usage === "冲服") {
            usageLevelList = keLiChongFuUsageLevel;
        }
        if (this.isAirPharmacy) {
            const { usageScopeId, medicineStateScopeId } = this;
            const UsageInfoMap = ChineseMedicineUsageInfo.GetChineseUsageParam;
            // 空中药房不同的 制法会对应不同的服用量
            const value = UsageInfoMap(medicineStateScopeId) || UsageInfoMap(usageScopeId);
            if (value && value.usageLevel) {
                usageLevelList = value.usageLevel;
            }
        }
        const _usageLevel = usageLevelList.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        _usageLevel.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        if (!!this._usageLevelList) {
            airUsageLevelList = this._usageLevelList?.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        }

        return TagPanel.createTagPanel(airUsageLevelList ?? _usageLevel, 4, this.initial, "每日用量", { textInput, maxLength: 10 });
    }
}

//服用天数
export class ChineseMedicineUsageDaysKeyboard extends WesternMedicineUsageKeyboard<ChineseUsageItemInfo> {
    isAirPharmacy?: boolean;
    constructor(initial?: ChineseUsageItemInfo, isAirPharmacy?: boolean) {
        super(initial);
        this.isAirPharmacy = isAirPharmacy;
    }
    build(textInput: AbcTextInput): JSX.Element {
        const { usageDays } = ChineseMedicine;
        const _usageDose = usageDays.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, item));
        if (!this.isAirPharmacy) _usageDose.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        return TagPanel.createTagPanel(_usageDose, 4, this.initial, "服用天数", { textInput, maxLength: 10 });
    }
}

export class ExternalMedicineFreqKeyboard extends WesternMedicineFreqKeyboard {
    build(textInput: AbcTextInput): JSX.Element {
        const _freq = ExternalFreqOptions.map((item) => JsonMapper.deserialize(Freq, item));
        return TagPanel.createTagPanel(_freq, 3, this.initial, "", { textInput, maxLength: 10 });
    }
}

export class ExternalMedicineCountTypeKeyboard extends WesternMedicineFreqKeyboard {
    build(textInput: AbcTextInput): JSX.Element {
        const _list = [{ name: "按穴位计数" }, { name: "手动计数" }];
        return TagPanel.createTagPanel(_list, 2, this.initial, "", { textInput, maxLength: 10 });
    }
}

export class ExternalMedicineRequirementKeyboard extends WesternMedicineUsageKeyboard<{ name?: string }> {
    build(textInput: AbcTextInput): JSX.Element {
        const requirement = ExternalConfig.requirement.list.map((it) => ({ name: "贴敷" + it.label }));
        requirement.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        return TagPanel.createTagPanel(requirement, 3, this.initial, "", { textInput, maxLength: 30 });
    }
}

//外治处方--针刺和艾灸备注
export class ExternalMedicineAcuRequirementKeyboard extends WesternMedicineUsageKeyboard<{ name?: string }> {
    build(textInput: AbcTextInput): JSX.Element {
        const requirement = ExternalZhenJiuRequirement.map((it) => ({ name: it.name }));
        requirement.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        return TagPanel.createTagPanel(requirement, 3, this.initial, "", { textInput, maxLength: 30 });
    }
}

export class InfusionMedicineFreqKeyboard extends WesternMedicineUsageKeyboard<Usage> {
    build(textInput: AbcTextInput): JSX.Element {
        const { usage } = InfusionMedicine;
        const _usage = usage.map((item) => JsonMapper.deserialize(Usage, item));
        return TagPanel.createTagPanel(_usage, 3, this.initial, "", { textInput, maxLength: 8 }, Sizes.dp8);
    }
}

export class ChineseAirPharmacyKeLiKeyboard extends WesternMedicineUsageKeyboard<{ name?: string }> {
    build(textInput: AbcTextInput): JSX.Element {
        const airParticlesBagsList = ["1", "2", "3", "4", "6"];
        const _bagsList = airParticlesBagsList.map((item) => ({ name: item }));
        return TagPanel.createTagPanel(_bagsList, 3, this.initial, "", { textInput }, Sizes.dp8);
    }
    keyboardHeight(): number {
        return pxToDp(200);
    }
}

export interface MultiLineParam {
    label?: string;
    col?: number;
    layout?: string;
    options?: Array<any>;
    prefix?: string;
    dividerLine?: boolean;
    children?: MultiLineParam[];
    selectedIndex?: number;
    onChange?(arg1?: any): void;
}
export class MultiLineKeyboard extends WesternMedicineUsageKeyboard<MultiLineParam> {
    params?: MultiLineParam;
    constructor(props?: MultiLineParam) {
        super(props);
        this.params = props;
    }
    build(textInput: AbcTextInput): JSX.Element {
        if (!_.isEmpty(this.params?.children)) {
            const initIndexMap = new Map();
            const options = this.params?.children!.map((it, index) => {
                initIndexMap.set(index, it.selectedIndex);
                return {
                    name: it.label,
                    col: it.col,
                    prefix: it.prefix,
                    options: it.options?.map((t) => ({ name: t.toString(), id: t })),
                    onChange: it.onChange,
                };
            });

            return TagPanel.createMultipleRowTagPanel(options ?? [], 2, initIndexMap, "", {
                textInput: textInput,
                maxLength: 8,
            });
        } else {
            const options = this.params?.dividerLine ? this.params?.options : this.params?.options?.map((item) => ({ name: item }));
            return TagPanel.createTagPanel(
                options ?? [],
                this.params?.col,
                undefined,
                "",
                { textInput: textInput, maxLength: 8 },
                Sizes.dp8
            );
        }
    }

    keyboardHeight(): number {
        return pxToDp(325);
    }
}

export class MultiplePharmacyTypeKeyboard extends WesternMedicineUsageKeyboard<ChineseUsageItemInfo> {
    pharmacyList?: string[];
    constructor(initial?: ChineseUsageItemInfo, pharmacyList?: string[]) {
        super(initial);
        this.pharmacyList = pharmacyList;
    }
    build(textInput: AbcTextInput): JSX.Element {
        const _pharmacyList = this.pharmacyList?.map((item) => JsonMapper.deserialize(ChineseUsageItemInfo, { name: item }));

        return TagPanel.createTagPanel(
            _pharmacyList!,
            2,
            this.initial,
            "药品来源",
            { textInput, maxLength: 10, isShowTitle: true },
            Sizes.dp8
        );
    }
}
