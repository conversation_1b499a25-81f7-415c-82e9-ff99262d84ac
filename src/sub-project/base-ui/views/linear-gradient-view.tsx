/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-10-23
 *
 * @description 用于实现背景渐变色
 * 注：只能当作背景View使用，不能包含子View
 * 用法: 当作背景使用
 *                     <LinearGradientView
                     style={{ width: Sizes.dp100, height: Sizes.dp100 }}
                     startPoint={{ x: 0, y: 0 }}
                     endPoint={{ x: 1, y: 0 }}
                     colors={[Colors.red, "#3b5998", "#192f6a"]}
                     locations={[0, 0.4, 1.0]}
                     angle={45}
                     useAngle={true}
                     angleCenter={{ x: 0.5, y: 0.5 }}
                     />
 */

import React from "react";
import { colorParse, Style, View } from "@hippy/react";
import { AbcViewProps, Color, flattenStyles } from "../../theme";
import { Version } from "../utils/version-utils";
import { AppInfo } from "../../base-business/config/app-info";

interface LinearGradientViewProps extends AbcViewProps {
    style?: Style | Style[];
    startPoint: { x: number; y: number };
    endPoint: { x: number; y: number };
    locations?: number[];
    useAngle?: boolean;
    angleCenter?: { x: number; y: number };
    angle?: number;
    colors: Color[];

    fallbackColor?: Color; //由于该组件2.0.1才支持，当不支持时，退化为使用背景色
}

export class LinearGradientView extends React.Component<LinearGradientViewProps, {}> {
    private instance: HTMLDivElement | null = null;

    /**
     * @ignore
     */
    constructor(props: LinearGradientViewProps) {
        super(props);
    }

    /**
     * @ignore
     */
    public render(): JSX.Element {
        if (this.props.children != undefined) throw "LinearGradientView不能包含子View";
        const { fallbackColor, style, colors, ...otherProps } = this.props;
        const lessThan201 = new Version(AppInfo.appVersion).compareTo(new Version("2.0.1")) < 0;
        if (lessThan201) {
            return <View style={[flattenStyles(this.props.style), { backgroundColor: fallbackColor }]} />;
        }

        return (
            <div
                // @ts-ignore
                nativeName="LinearGradientView"
                ref={(ref: HTMLDivElement) => {
                    this.instance = ref;
                }}
                // @ts-ignore
                style={style}
                // @ts-ignore
                {...otherProps}
                // @ts-ignore
                colors={colors.map((color) => colorParse(color))}
            />
        );
    }
}
