/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/17
 *
 * @description
 */
import _ from "lodash";
import { Toast } from "../dialog/toast";
import { StringUtils } from "./string-utils";

const VCITY = {
    11: "北京",
    12: "天津",
    13: "河北",
    14: "山西",
    15: "内蒙古",
    21: "辽宁",
    22: "吉林",
    23: "黑龙江",
    31: "上海",
    32: "江苏",
    33: "浙江",
    34: "安徽",
    35: "福建",
    36: "江西",
    37: "山东",
    41: "河南",
    42: "湖北",
    43: "湖南",
    44: "广东",
    45: "广西",
    46: "海南",
    50: "重庆",
    51: "四川",
    52: "贵州",
    53: "云南",
    54: "西藏",
    61: "陕西",
    62: "甘肃",
    63: "青海",
    64: "宁夏",
    65: "新疆",
    71: "台湾",
    81: "香港",
    82: "澳门",
    91: "国外",
};

/*
 * 身份证15位编码规则：dddddd yymmdd xx p
 * dddddd：6位地区编码
 * yymmdd: 出生年(两位年)月日，如：910215
 * xx: 顺序编码，系统产生，无法确定
 * p: 性别，奇数为男，偶数为女
 *
 * 身份证18位编码规则：dddddd yyyymmdd xxx y
 * dddddd：6位地区编码
 * yyyymmdd: 出生年(四位年)月日，如：19910215
 * xxx：顺序编码，系统产生，无法确定，奇数为男，偶数为女
 * y: 校验码，该位数值可通过前17位计算获得
 *
 * 前17位号码加权因子为 Wi = [ 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 ]
 * 验证位 Y = [ 1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2 ]
 * 如果验证码恰好是10，为了保证身份证是十八位，那么第十八位将用X来代替
 * 校验位计算公式：Y_P = mod( ∑(Ai×Wi),11 )
 * i为身份证号码1...17 位; Y_P为校验码Y所在校验码数组位置
 */
function isCardNo(card: string) {
    //身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
    const reg = /(^\d{15}$)|(^\d{17}(\d|X|x)$)/;
    return reg.test(card);
}

function checkProvince(card: string, vcity?: object) {
    if (!vcity) vcity = VCITY;
    const province = card.substr(0, 2);
    return vcity.hasOwnProperty(province);
}

function verifyBirthday(year: number, month: number, day: number, birthday: Date) {
    const now = new Date();
    const now_year = now.getFullYear();
    //年月日是否合理
    if (birthday.getFullYear() == year && birthday.getMonth() + 1 == month && birthday.getDate() == day) {
        //判断年份的范围（0岁到100岁之间)
        const time = now_year - year;
        return time >= 0 && time <= 150;
    }
    return false;
}

function checkParity(card: string) {
    //15位转18位
    card = changeFivteenToEighteen(card);
    const len = card.length;
    if (len == 18) {
        const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        const arrCh = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
        let cardTemp = 0;
        for (let i = 0; i < 17; i++) {
            //@ts-ignore
            cardTemp += card.substr(i, 1) * arrInt[i];
        }
        const valnum = arrCh[cardTemp % 11];
        return valnum == card.substr(17, 1).toLocaleUpperCase();
    }
    return false;
}

function checkBirthday(card: string) {
    const len = card.length;
    //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
    if (len == 15) {
        const re_fifteen = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
        const arr_data = card.match(re_fifteen);
        const year = arr_data?.[2];
        const month = arr_data?.[3];
        const day = arr_data?.[4];
        const birthday = new Date("19" + year + "/" + month + "/" + day);
        return verifyBirthday(Number(`19${year}`), Number(month), Number(day), birthday);
    }
    //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
    if (len == 18) {
        const re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/;
        const arr_data = card.match(re_eighteen);
        const year = arr_data?.[2];
        const month = arr_data?.[3];
        const day = arr_data?.[4];
        const birthday = new Date(year + "/" + month + "/" + day);
        return verifyBirthday(Number(year), Number(month), Number(day), birthday);
    }
    return false;
}

function changeFivteenToEighteen(card: string) {
    if (card.length == 15) {
        const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        const arrCh = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
        let cardTemp = 0,
            i;
        card = card.substr(0, 6) + "19" + card.substr(6, card.length - 6);
        for (i = 0; i < 17; i++) {
            //@ts-ignore
            cardTemp += card.substr(i, 1) * arrInt[i];
        }
        card += arrCh[cardTemp % 11];
        return card;
    }
    return card;
}

// 港澳台居住证 —— 前6位为行政区划代码，第7至14位为出生日期码，第15至17位为顺序码，第17位也可以辨认性别，奇数为男，偶数为女，第18位为校验码。
// 香港居民使用810000、澳门居民使用820000、台湾居民使用830000
export function validateResidence(code: string): boolean {
    // 没有填 就不验证
    if (!code) {
        return true;
    }
    if (/^8[123]0000(?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[\dX]$/.test(code)) {
        return true;
    } else {
        return false;
    }
}

//验证香港居民身份证 -- 共8位。前面1位大写英文字母+中间6位数字，正整数+（+后面1位数字或大写英文字母+）。（示例：C000001（9））
export function validateHKIdCard(code: string): boolean {
    // 没有填 就不验证
    if (!code) {
        return true;
    }

    if (/^[A-Z][0-9]{6}(?:[\\( \\（][0-9A-Z][\\)\\）]|[0-9A-Z])$/.test(code)) {
        return true;
    } else {
        return false;
    }
}

//验证澳门居民身份证
// 旧：前面1位数字或大写英文字母+/ +6位数字+ / +后面1位数字或大写英文字母（示例：M/000001/C），正整数。
// 新：前面1位数字或大写英文字母+6位数字+后面1位数字或大写英文字母（示例：P000002C），正整数。
export function validateAoMenIdCard(code: string): boolean {
    // 没有填 就不验证
    if (!code) {
        return true;
    }
    if (/^[A-Z0-9]\d{6}(?:[\\( \\（][0-9A-Z][\\)\\）]|[0-9A-Z])$/.test(code) || /^[A-Z0-9]\/\d{6}\/[0-9A-Z]$/.test(code)) {
        return true;
    } else {
        return false;
    }
}

//验证台湾居民身份证 -- 共10位，前面1位大写英文字母+后面9位数字，正整数。
export function validateTaiWanIdCard(code: string): boolean {
    // 没有填 就不验证
    if (!code) {
        return true;
    }
    if (/^[A-Z][0-9]{9}$/.test(code)) {
        return true;
    } else {
        return false;
    }
}

//验证港澳居民来往内地通行证 香港：H+(8 | 10)位数字，澳门：M+(8 | 10)位数字
export function validateHKAndAoMenToInlandPassCard(code: string): boolean {
    // 没有填 就不验证
    if (!code) {
        return true;
    }
    if (/^[HMhm]\d{8}(\d{2})?$/.test(code)) {
        return true;
    } else {
        return false;
    }
}

//验证台湾居民来往内地通行证 8位数字
export function validateTaiwanToInlandPassCard(code: string): boolean {
    // 没有填 就不验证
    if (!code) {
        return true;
    }
    if (/^[0-9]{8}$/.test(code)) {
        return true;
    } else {
        return false;
    }
}

//验证往来港澳通行证
// 旧：共9位，首字母C+后面8位数字，正整数。
// 新：共9位，首字母C+1位英文字母+7位数字，正整数。
export function validateToHKAndAoMenPassCard(code: string): boolean {
    // 没有填 就不验证
    if (!code) {
        return true;
    }
    if (/^[Cc][a-zA-Z][0-9]{7}$/.test(code) || /^[Cc][0-9]{8}$/.test(code)) {
        return true;
    } else {
        return false;
    }
}

//验证往来台湾通行证
// 旧：共9位，首字母T+后面8位数字，正整数。新：共9位，首字母L+后面8位数字，正整数。
export function validateToTaiWanPassCard(code: string): boolean {
    // 没有填 就不验证
    if (!code) {
        return true;
    }
    if (/^[TtLl][0-9]{8}$/.test(code)) {
        return true;
    } else {
        return false;
    }
}

//验证护照号码
// 共9位，大写英文字母+数字，正整数。
export function validatePassport(code: string): boolean {
    // 没有填 就不验证
    if (!code) {
        return true;
    }

    if (/^[A-Z0-9]{9}$/.test(code)) {
        return true;
    } else {
        return false;
    }
}

// 军官证
// 共8位，前面固定填入“军”+后面7位数字，正整数。
export function validateOfficerCard(card: string): boolean {
    // 没有填 就不验证
    if (!card) {
        return true;
    }

    if (/^军\d{7}$/.test(card)) {
        return true;
    } else {
        return false;
    }
}

// 验证警官证
// 最多10位，大写英文字母或数字，正整数。
export function validatePoliceCard(card: string): boolean {
    // 没有填 就不验证
    if (!card) {
        return true;
    }

    if (/^[A-Z0-9]{1,10}$/.test(card)) {
        return true;
    } else {
        return false;
    }
}

// 验证外国人永久居留证 -- 长度为15位，前3位英文字母(仅支持大写)
// 旧：共15位，前面3位大写英文字母+后面12位数字，正整数。新：共18位，前面3位大写英文字母+后面15位数字。
export function validateForeignerResidenceCard(card: string): boolean {
    // 没有填 就不验证
    if (!card) {
        return true;
    }

    if (/^[A-Z]{3}\d{12}$/.test(card) || /^[A-Z]{3}\d{15}$/.test(card)) {
        return true;
    } else {
        return false;
    }
}
export function validateInternationalIdCard(card: string): boolean {
    // 没有填 就不验证
    if (!card) {
        return true;
    }

    if (/^[A-Z0-9]{1,18}$/.test(card)) {
        return true;
    } else {
        return false;
    }
}
// 其它证件
export function validateOtherCard(card: string): boolean {
    // 没有填 就不验证
    if (!card) {
        return false;
    }

    if (`${card}`.length <= 20) {
        return true;
    } else {
        return false;
    }
}

export class ValidatorUtils {
    static validatePhoneNumber(mobile?: string, allowEmpty = false, countryCode?: string): boolean {
        if (allowEmpty && _.isEmpty(mobile)) return true;

        if (_.isEmpty(mobile)) {
            Toast.show("请输入手机号", { warning: true });
            return false;
        }

        if (!StringUtils.validateMobile(mobile!, countryCode)) {
            Toast.show("请输入正确的手机号", { warning: true });

            return false;
        }

        return true;
    }

    static validateIdCard(params: { idCard?: string; type?: string }, allowEmpty = false, tipsContent?: string): boolean {
        const { idCard, type = "身份证" } = params;
        if (allowEmpty && _.isEmpty(idCard)) return true;

        if (_.isEmpty(idCard)) {
            Toast.show("请输入证件号码", { warning: true });
            return false;
        }

        if (type !== "身份证") {
            switch (type) {
                case "港澳台居住证":
                    if (!validateResidence(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateResidence(idCard!);
                case "香港居民身份证":
                    if (!validateHKIdCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateHKIdCard(idCard!);
                case "澳门居民身份证":
                    if (!validateAoMenIdCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateAoMenIdCard(idCard!);
                case "台湾居民身份证":
                    if (!validateTaiWanIdCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateTaiWanIdCard(idCard!);
                case "港澳居民来往内地通行证":
                    if (!validateHKAndAoMenToInlandPassCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateHKAndAoMenToInlandPassCard(idCard!);
                case "台湾居民来往内地通行证":
                    if (!validateTaiwanToInlandPassCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateTaiwanToInlandPassCard(idCard!);
                case "往来港澳通行证":
                    if (!validateToHKAndAoMenPassCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateToHKAndAoMenPassCard(idCard!);
                case "往来台湾通行证":
                    if (!validateToTaiWanPassCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateToTaiWanPassCard(idCard!);
                case "护照":
                    if (!validatePassport(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validatePassport(idCard!);
                case "外国人永久居留证":
                    if (!validateForeignerResidenceCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateForeignerResidenceCard(idCard!);
                case "军官证":
                    if (!validateOfficerCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateOfficerCard(idCard!);
                case "警官证":
                    if (!validatePoliceCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validatePoliceCard(idCard!);
                case "国际身份证":
                    if (!validateInternationalIdCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateInternationalIdCard(idCard!);
                case "其它":
                    if (!validateOtherCard(idCard!)) {
                        Toast.show(`${type ?? ""}格式错误`, { warning: true });
                    }
                    return validateOtherCard(idCard!);
            }
        }
        // 字母开头（国际身份证），不验证
        if (/^[a-zA-Z]/.test(idCard!)) {
            return true;
        }

        // 澳门8位身份证
        if (/^[157]\d{6}[0-9a-zA-Z]/.test(idCard!) && idCard?.length === 8) {
            return true;
        }

        //校验长度，类型
        if (!isCardNo(idCard!)) {
            Toast.show(`${tipsContent ?? ""}身份证号输入有误`, { warning: true });
            return false;
        }
        //检查省份
        if (!checkProvince(idCard!)) {
            Toast.show(`${tipsContent ?? ""}身份证号输入有误`, { warning: true });
            return false;
        }
        //校验生日
        if (!checkBirthday(idCard!)) {
            Toast.show(`${tipsContent ?? ""}身份证号输入有误`, { warning: true });
            return false;
        }
        //检验位的检测
        if (!checkParity(idCard!)) {
            Toast.show(`${tipsContent ?? ""}身份证号输入有误`, { warning: true });
            return false;
        }

        return true;
    }

    static getBirthWithIdCard(idCard?: string): Date | undefined {
        if (!idCard) return undefined;
        if (!isCardNo(idCard)) return undefined;
        const len = idCard.length;
        //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
        if (len == 15) {
            const re_fifteen = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
            const arr_data = idCard.match(re_fifteen);
            const year = arr_data?.[2];
            const month = arr_data?.[3];
            const day = arr_data?.[4];
            const birthday = new Date("19" + year + "/" + month + "/" + day);
            if (verifyBirthday(Number(`19${year}`), Number(month), Number(day), birthday)) {
                return birthday;
            }
        }
        //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
        if (len == 18) {
            const re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/;
            const arr_data = idCard.match(re_eighteen);
            const year = arr_data?.[2];
            const month = arr_data?.[3];
            const day = arr_data?.[4];
            const birthday = new Date(year + "/" + month + "/" + day);
            if (verifyBirthday(Number(year), Number(month), Number(day), birthday)) {
                return birthday;
            }
        }
        return undefined;
    }
    /*
     * 身份证15位编码规则：dddddd yymmdd xx p
     * dddddd：6位地区编码
     * yymmdd: 出生年(两位年)月日，如：910215
     * xx: 顺序编码，系统产生，无法确定
     * p: 性别，奇数为男，偶数为女
     *
     * 身份证18位编码规则：dddddd yyyymmdd xxx y
     * dddddd：6位地区编码
     * yyyymmdd: 出生年(四位年)月日，如：19910215
     * xxx：顺序编码，系统产生，无法确定，奇数为男，偶数为女
     * y: 校验码，该位数值可通过前17位计算获得
     *
     * 前17位号码加权因子为 Wi = [ 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 ]
     * 验证位 Y = [ 1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2 ]
     * 如果验证码恰好是10，为了保证身份证是十八位，那么第十八位将用X来代替
     * 校验位计算公式：Y_P = mod( ∑(Ai×Wi),11 )
     * i为身份证号码1...17 位; Y_P为校验码Y所在校验码数组位置
     */
    static isCardNo(card?: string): boolean {
        if (!card) return false;
        //身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
        const reg = /(^\d{15}$)|(^\d{17}(\d|X|x)$)/;
        return reg.test(card);
    }
}
