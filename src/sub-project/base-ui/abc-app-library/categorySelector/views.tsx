/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description 类别选择相关的组件
 * <AUTHOR>
 * @CreateDate 2024/4/15
 * @Copyright 成都字节流科技有限公司© 2024
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { AbcIndeterminateCheckbox, IndeterminateCheckboxStatus } from "../../views/abc-checkbox";
import { AbcView } from "../../views/abc-view";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { isUndefined } from "lodash";
import { IconFontView } from "../../index";

interface GoodsTypeClassificationItemViewProps<T> {
    enable?: boolean;
    text: string;
    selected?: boolean;
    checked?: boolean;
    checkStatus?: IndeterminateCheckboxStatus;

    showSelectBg?: boolean;

    info: T;

    /**
     * 选中某一项
     */
    onChecked?(arg1: T): void;
}

export const SubClassificationItemView = <T,>(props: GoodsTypeClassificationItemViewProps<T>): JSX.Element => {
    const { text, selected, checked, enable = true, info, checkStatus, showSelectBg, onChecked } = props;
    return (
        <AbcView
            style={[
                ABCStyles.rowAlignCenter,
                { paddingVertical: Sizes.dp10, paddingLeft: Sizes.dp16 },
                !!showSelectBg ? { backgroundColor: Colors.white } : {},
            ]}
            onClick={() => {
                enable && onChecked?.(info);
            }}
        >
            {isUndefined(checkStatus) ? (
                <View
                    style={{
                        borderRadius: Sizes.dp8,
                        borderColor: !!checked ? Colors.transparent : Colors.P1,
                        width: Sizes.dp16,
                        height: Sizes.dp16,
                        borderWidth: 1,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                    onClick={() => {
                        enable && onChecked?.(info);
                    }}
                >
                    {!!checked && (
                        <IconFontView
                            name={"Chosen"}
                            size={Sizes.dp16}
                            color={Colors.mainColor}
                            style={{ width: Sizes.dp16, height: Sizes.dp16 }}
                        />
                    )}
                </View>
            ) : (
                <AbcIndeterminateCheckbox
                    stateless={true}
                    check={selected}
                    checkStatus={checkStatus}
                    onChange={() => {
                        enable && onChecked?.(info);
                    }}
                />
            )}

            <Text
                style={[
                    enable ? { color: Colors.T1 } : { color: Colors.T2 },
                    TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp24 }),
                    { paddingLeft: Sizes.dp8, flex: 1 },
                ]}
                numberOfLines={1}
            >
                {text}
            </Text>
        </AbcView>
    );
};
