/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2022/7/25
 * @Copyright 成都字节流科技有限公司© 2022
 */

import React from "react";
import { Style, StyleSheet, View } from "@hippy/react";

import { BaseComponent } from "../../base-component";
import { AbcTag } from "../common/abc-tag";
import { ABCStyles, Colors, flattenStyles, Sizes, TextStyles } from "../../../theme";
import { Spacer } from "../../index";
import sizes from "../../../theme/sizes";
import { TimeRangePickerDialog, TimeRangePickerDialogProps } from "./time-picker-dialog";
import { Range } from "../../utils/value-holder";
import { TimeUtils } from "../../../common-base-module/utils";
import { TherapyTimePickerDialog } from "../../../registration/dentistry/appointment/views/therapy-time-picker-dialog";
import { RegistrationDesignatedTime } from "../../../registration/data/bean";

interface AbcTimeRangeProps extends TimeRangePickerDialogProps {
    style?: Style | Style[];
    onChange?(range: Range<Date>, timeOfDay?: string): void;
    /**
     * 是否可编辑
     */
    enable?: boolean;
    /**
     * 最小服务时长
     */
    serviceMinMinutes?: number;
    /**
     * 最大服务时长
     */
    serviceMaxMinutes?: number;
    /**
     * 时间列表
     */
    schedulingTimeList?: RegistrationDesignatedTime;
    isCustomTime?: boolean; //是否自定义时间列表（即外部传入时间）
    showErrorBorder?: boolean;
    emptyContent?: string;
}

interface AbcTimeRangeStates {
    selected: boolean;
    date?: Range<Date>;
}

//@ts-ignore
const defaultStyle = StyleSheet.create({
    abcTag: {
        backgroundColor: Colors.whiteSmoke,
        borderWidth: Sizes.dpHalf,
        borderColor: Colors.transparent,
        borderRadius: Sizes.dp4,
        ...sizes.marginLTRB(0),
        ...Sizes.paddingLTRB(Sizes.dp12, Sizes.dp8),
    },
    abcTagSelected: {
        backgroundColor: Colors.theme2_08,
        borderColor: Colors.mainColor,
    },
    abcErrorTag: {
        borderColor: Colors.errorBorder,
    },
});

export class AbcTimeRangeView extends BaseComponent<AbcTimeRangeProps, AbcTimeRangeStates> {
    static defaultProps = {
        enable: true,
        isCustomTime: true,
    };
    constructor(props: AbcTimeRangeProps) {
        super(props);
        let stateDate = props.date;
        if (
            !!props.date?.start &&
            !!props.date?.end &&
            props.date.start.getTime() == props.date.end.getTime() &&
            props.date.start.getTime() == TimeUtils.getStartOfDate(props.date.start).getTime()
        ) {
            const currentTime = new Date(`${props.date.start.format("yyyy/MM/dd")} ${new Date().format("HH:mm:ss")}`);
            stateDate = new Range<Date>(currentTime, currentTime);
        }
        this.state = {
            selected: false,
            date: stateDate,
        };
    }

    UNSAFE_componentWillReceiveProps(nextProps: Readonly<AbcTimeRangeProps>): void {
        let nextPropsDate = nextProps.date;
        if (
            !!nextProps.date?.start &&
            !!nextProps.date?.end &&
            nextProps.date.start.getTime() == nextProps.date.end.getTime() &&
            nextProps.date.start.getTime() == TimeUtils.getStartOfDate(nextProps.date.start).getTime()
        ) {
            const currentTime = new Date(`${nextProps.date.start.format("yyyy/MM/dd")} ${new Date().format("HH:mm:ss")}`);
            nextPropsDate = new Range<Date>(currentTime, currentTime);
        }
        this.setState({ date: nextPropsDate });
    }

    createAbcTagStyle(): Style {
        const { style, showErrorBorder } = this.props;
        let _abcTagStyle = Object.assign(defaultStyle.abcTag, { ...flattenStyles(style) });
        if (this.state.selected) {
            _abcTagStyle = Object.assign(
                { ...defaultStyle.abcTag },
                {
                    ...defaultStyle.abcTagSelected,
                }
            );
        }
        if (showErrorBorder) {
            _abcTagStyle = Object.assign(
                { ...defaultStyle.abcTag },
                {
                    ...defaultStyle.abcErrorTag,
                }
            );
        }
        return _abcTagStyle;
    }

    public async handleTimeClick(): Promise<void> {
        const { onChange, serviceMinMinutes, schedulingTimeList, isCustomTime, serviceMaxMinutes } = this.props;
        this.setState({ selected: true });
        let result: { date: Range<Date>; timeOfDay?: string } | undefined;
        //是否自定义时间列表
        if (!!isCustomTime) {
            const _result = await TimeRangePickerDialog.show({
                ...this.props,
                date: this.state.date,
            });
            if (!!_result) {
                result = { date: _result };
            }
        } else {
            if (!schedulingTimeList?.getScheduleIntervals()?.length) {
                this.setState({ selected: false });
                return;
            }
            result = await TherapyTimePickerDialog.show<{ date: Range<Date>; timeOfDay?: string }>({
                serviceMinMinutes: serviceMinMinutes ?? 30,
                serviceMaxMinutes: serviceMaxMinutes ?? 240,
                date: this.state.date,
                schedulingTimeList: schedulingTimeList,
            });
        }
        this.setState({ selected: false });
        if (!!result?.date) {
            this.setState({ date: result?.date });
            onChange?.(result?.date, result?.timeOfDay);
        }
    }

    render(): JSX.Element {
        const { date } = this.state;
        const { enable, emptyContent } = this.props;
        let text = emptyContent ?? "";
        if (!emptyContent || !!date) {
            text = `${date?.start?.format("HH:mm") ?? "开始"}-${date?.end?.format("HH:mm") ?? "结束"}`;
        }
        return (
            <View style={[ABCStyles.rowAlignCenter]}>
                <AbcTag
                    checked={true}
                    style={[this.createAbcTagStyle()]}
                    textStyle={[
                        TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22, color: !!date?.start && !!enable ? Colors.T1 : Colors.T4 }),
                        { textAlign: "center" },
                    ]}
                    text={text}
                    onClick={() => {
                        !!enable && this.handleTimeClick();
                    }}
                    textNumberOfLines={1}
                />
                <Spacer />
            </View>
        );
    }
}
