/**
 * create by dengjie
 * desc:
 * create date 2021/1/6
 */
import { ABCApiNetwork } from "../../net";
import {
    DEFAULT_DOCTOR_ID,
    RegistrationDesignatedTime,
    RegistrationDetail,
    RegistrationDoctorEnableCategories,
    RegistrationDoctorSchedule,
    RegistrationFormItem,
    RegistrationKanbanDailyViewDetail,
    RegistrationNearestCreated,
    RegistrationRevisitStatus,
    RegistrationsAppointmentConfig,
    RegistrationsDoctorFeeItemsRsp,
    RegistrationsPatientApplyAuditItem,
    RegistrationStatus,
    RegistrationVisitSource,
} from "./bean";
import { fromJsonToDate, fromJsonToMap, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { TimeUtils } from "../../common-base-module/utils";
import { Subject } from "rxjs";
import { CouponPromotion, GiftPromotion, PatientCardPromotion, PatientPointsInfo, Promotion } from "../../charge/data/charge-beans";
import { ClinicDoctorInfo, Patient, PatientAddress } from "../../base-business/data/beans";
import _ from "lodash";
import { Range } from "../../base-ui/utils/value-holder";
import { TherapySignInStatus } from "../appointment/data/appointment-bean";
import { PatientRegAuditStatus } from "../../patients/data/crm-bean";
import { RegistrationType } from "../dentistry/data/bean";
import { ignore } from "../../common-base-module/global";

class StatSummary {
    count?: number;
    amount?: number;
}

export class GetRegistrationListRsp {
    diagnosedCount?: number;
    hadSignInCount?: number;
    keyword?: string;
    keywords?: string;
    limit?: number;
    offset?: number;
    refundedCount?: number;
    @JsonProperty({ type: Array, clazz: RegistrationDetail })
    rows?: Array<RegistrationDetail>;
    statSummary?: StatSummary;
    totalCount?: number;
    waitingDiagnoseCount?: number;
    waitingSignInCount?: number;
}

interface Pay {
    fee?: number;
    memberId?: string;
    receivable?: number;
}

export class RegistrationDetailReq {
    allergicHistory?: string;
    chiefComplaint?: string;
    familyHistory?: string;
    pastHistory?: string;
    personalHistory?: string;
    physicalExamination?: string;
    presentHistory?: string;
    epidemiologicalHistory?: string;

    pay?: Pay;

    signIn?: TherapySignInStatus;

    @JsonProperty({ type: Array, clazz: Promotion })
    promotions?: Promotion[];

    @JsonProperty({ type: Array, clazz: CouponPromotion })
    couponPromotions?: CouponPromotion[];

    @JsonProperty({ type: Array, clazz: GiftPromotion })
    giftRulePromotions?: GiftPromotion[];

    @JsonProperty({ type: Array, clazz: PatientCardPromotion })
    patientCardPromotions?: PatientCardPromotion[];

    @JsonProperty({ type: PatientPointsInfo })
    patientPointsInfo?: PatientPointsInfo;

    @JsonProperty({ type: RegistrationFormItem })
    registrationFormItem?: RegistrationFormItem;

    @JsonProperty({ type: Patient })
    patient?: Patient;

    visitSourceId?: string;
    visitSourceFrom?: string;
    visitSourceRemark?: string;
    revisitStatus?: RegistrationRevisitStatus;

    doctorId?: string;
    registrationType?: number;
}

export class RegistrationsDoctorListItem {
    diagnosedCount?: number;
    expiredCount?: number;
    id?: string;
    name?: string;
    refundedCount?: number;
    reservedCount?: number;
    restCount?: number;
    status?: number;
    totalCount?: number;
    waitingDiagnoseCount?: number;
    waitingSignInCount?: number;
    doctorId?: string;
    doctorName?: string;
}

export class GetRegistrationsDoctorList {
    dayAfterTomorrowTotalCount?: number;
    diagnosedCount?: number;
    expiredCount?: number;
    refundedCount?: number;
    reservedCount?: number;
    restCount?: number;
    @JsonProperty({ type: Array, clazz: RegistrationsDoctorListItem })
    rows?: RegistrationsDoctorListItem[];
    today?: Date;
    todayTotalCount?: number;
    tomorrowTotalCount?: number;
    totalCount?: number;
    waitingDiagnoseCount?: number;
    waitingSignInCount?: number;
}

export interface ShiftsListItem {
    available: number;
    end: string;
    orderNo: number;
    restCount: number;
    start: string;
    timeOfDay: string;
    type: number; // 号源类型

    _end?: string;
    _start?: string;
}

export class ScheduleIntervalsItem {
    end!: string;
    start!: string;
    timeOfDay!: string;
    list?: ShiftsListItem[];
}

export class DoctorShifts {
    canReserve?: number;
    dayOfWeek?: string;
    restCountToday?: number;

    @JsonProperty({ type: Array, clazz: ScheduleIntervalsItem })
    scheduleIntervals?: ScheduleIntervalsItem[];
    // list?: ShiftsListItem[];

    @JsonProperty({ fromJson: fromJsonToDate })
    registerStartTime?: Date;

    @JsonProperty({ fromJson: fromJsonToDate })
    workingDate?: Date;
}

export class DoctorShiftsListItem {
    doctorId?: string;
    doctorName?: string;
    registrationFee?: number;
    revisitedRegistrationFee?: number; //复诊金额
    shifts?: DoctorShifts[];
}

export class GetDoctorShiftsListRsp {
    departmentId?: string;
    departmentName?: string;

    @JsonProperty({ type: Array, clazz: DoctorShiftsListItem })
    rows?: DoctorShiftsListItem[];

    serviceType?: number;

    @JsonProperty({ fromJson: fromJsonToDate })
    today?: Date;
}

class GetDoctorOrderNoRsp {
    timeOfDay?: string;
    today?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    reserveDate?: Date;
    orderNo?: number;
    isReserved?: number;
    isAdditional?: number; // 0 1  （1表示+号）
    @JsonProperty({ fromJson: fromJsonToMap })
    timeOfDayTotalCountMap?: Map<string, number>;
    @JsonProperty({
        fromJson: (json) => {
            return new Range<string>(json.start, json.end);
        },
    })
    reserveTime?: Range<string>;
}

export class GetPatientOrdersVisitSourceListRsp {
    @JsonProperty({ type: Array, clazz: RegistrationVisitSource })
    rows?: RegistrationVisitSource[];
}

export class GetOutpatientRevisitStatusRsp {
    doctorId?: string;
    patientId?: string;
    revisitStatus?: number;
    @JsonProperty({ toJson: fromJsonToDate })
    lastDiagnosedTime?: Date;

    /**
     * 是否X天内首诊(反之再诊)
     * @param options
     */
    isFirstRevisitWithinDays(options: { reserveDate?: Date; effectiveDays: number }): boolean {
        if (!this.lastDiagnosedTime) {
            return true;
        }
        const { reserveDate = new Date(), effectiveDays } = options;
        const reserveTime = new Date(reserveDate);
        const _reserveDate = new Date(reserveTime.getFullYear(), reserveTime.getMonth(), reserveTime.getDate());
        const lastTime = new Date(this.lastDiagnosedTime);
        const lastDate = new Date(lastTime.getFullYear(), lastTime.getMonth(), lastTime.getDate());
        const differenceDays = Math.round(Math.abs((Number(_reserveDate) - Number(lastDate)) / (24 * 60 * 60 * 1000)));
        return differenceDays + 1 > effectiveDays;
    }
}

export class GetClinicAllDoctorRegFeeListRsp {
    clinicId?: string;

    //复诊挂号费是否和初诊不同。0：否；1：是
    isDiffForRevisited?: number;

    //诊所默认初诊挂号成本价
    regCostUnitPrice?: number;

    //诊所默认初诊挂号销售价
    regUnitPrice?: number;

    //诊所默认复诊挂号成本价
    revisitedRegCostUnitPrice?: number;

    //诊所默认复诊挂号销售价
    revisitedRegUnitPrice?: number;

    //医生挂号费列表
    @JsonProperty({ type: Array, clazz: ClinicDoctorInfo })
    employeeRegistrationFees?: ClinicDoctorInfo[];

    /**
     * 将费用分组
     * -departmentId
     * --doctorId
     * --detail[]
     */
    groupingForRegistrationCategory(): Map<string, Map<string, ClinicDoctorInfo[]>> {
        const employeeRegistrationFeesForCategory: Map<string, Map<string, ClinicDoctorInfo[]>> = new Map();
        this.employeeRegistrationFees?.forEach((item) => {
            const { departmentId = "", doctorId = "" } = item;
            const _sameDepartment = employeeRegistrationFeesForCategory.get(departmentId) ?? new Map();
            const _sameDoctor = _sameDepartment.get(doctorId) ?? [];
            _sameDoctor.push(item);
            _sameDepartment.set(doctorId, _sameDoctor);
            employeeRegistrationFeesForCategory.set(departmentId, _sameDepartment);
        });
        return employeeRegistrationFeesForCategory;
    }
}

class GetRegistrationQueryApplyAuditRsp {
    @JsonProperty({ type: Array, clazz: RegistrationsPatientApplyAuditItem })
    rows?: RegistrationsPatientApplyAuditItem[];
}

class RegistrationAuditPay {
    fee?: number;
    memberId?: string;
    payMode?: number;
    paySubMode?: number;
    payType?: number;
    receivable?: number;
    refundOriginalPayMode?: number;
    useMemberFlag?: number;
}
class RegistrationAuditItem {
    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    isReserved?: number;
    orderNo?: number;
    registrationProductIds?: (string | undefined)[];
    @JsonProperty({ fromJson: fromJsonToDate })
    reserveDate?: Date;
    reserveTime?: Range<string>;
}
class RegistrationAuditReq {
    @JsonProperty({ type: Array, clazz: Promotion })
    promotions?: Promotion[];

    @JsonProperty({ type: Array, clazz: CouponPromotion })
    couponPromotions?: CouponPromotion[];

    @JsonProperty({ type: Array, clazz: GiftPromotion })
    giftRulePromotions?: GiftPromotion[];

    @JsonProperty({ type: Array, clazz: PatientCardPromotion })
    patientCardPromotions?: PatientCardPromotion[];

    @JsonProperty({ type: PatientPointsInfo })
    patientPointsInfo?: PatientPointsInfo;

    @JsonProperty({ type: RegistrationAuditPay })
    pay?: RegistrationAuditPay;
    @JsonProperty({ type: RegistrationAuditItem })
    registrationFormItem?: RegistrationAuditItem;
    revisitStatus?: RegistrationRevisitStatus;
    visitSourceFrom?: string;
    visitSourceId?: string;
    visitSourceRemark?: string;
}

export class GetRegistrationKanbanDetailRsp {
    notRefundedCount?: number;
    reserveTotalCount?: number;
    hadSignInCount?: number;
    waitingSignInCount?: number;
    waitingDiagnoseCount?: number;
    diagnosedCount?: number;
    refundedCount?: number;
    @JsonProperty({ type: Array, clazz: RegistrationKanbanDailyViewDetail })
    dailyViews?: RegistrationKanbanDailyViewDetail[];

    /**
     * 看板特有字段
     */
    @JsonProperty({ type: Array, clazz: RegistrationKanbanDailyViewDetail })
    kanBanDailyViews?: RegistrationKanbanDailyViewDetail[];
}

export class RegistrationAgent {
    static changeObserver = new Subject<RegistrationDetail>();
    static registrationManageAuditObserver = new Subject<string>();

    static async getRegistrationList(params: {
        offset: number;
        limit?: number;
        date?: Date;
        doctorId?: string;
        departmentId?: string;
        keyword?: string;
        displayStatus?: RegistrationStatus;
        patientId?: string;
        end?: Date;
    }): Promise<GetRegistrationListRsp> {
        const { date, displayStatus, offset, end, ...others } = params;
        ignore(end);
        return ABCApiNetwork.get("registrations/itemlist", {
            queryParameters: {
                offset: offset ?? 0,
                limit: 20,
                date: date ? TimeUtils.formatDate(date) : undefined,
                displayStatus: displayStatus ? displayStatus : "",
                ...others,
            },
            clazz: GetRegistrationListRsp,
            clearUndefined: true,
        });
    }

    static async getRegistrationDetail(id: string): Promise<RegistrationDetail> {
        return ABCApiNetwork.get(`registrations/${id}`, { clazz: RegistrationDetail }).then((rsp) => {
            const {
                addressCityId,
                addressCityName,
                addressDetail,
                addressDistrictId,
                addressDistrictName,
                addressGeo,
                addressProvinceId,
                addressProvinceName,
            } = rsp.patient!;
            if (rsp.chargeSheet) {
                if (!rsp.chargeSheet.patient) {
                    rsp.chargeSheet.patient = rsp.patient;
                }
            }

            rsp.patient!.address = JsonMapper.deserialize(PatientAddress, {
                addressCityId: addressCityId,
                addressCityName: addressCityName,
                addressDetail: addressDetail,
                addressDistrictId: addressDistrictId,
                addressDistrictName: addressDistrictName,
                addressGeo: addressGeo,
                addressProvinceId: addressProvinceId,
                addressProvinceName: addressProvinceName,
            });
            return rsp;
        });
    }

    static async getTherapyRegistrationList(params: {
        offset: number;
        limit?: number;
        date?: Date;
        doctorId?: string;
        departmentId?: string;
        keyword?: string;
        displayStatus?: RegistrationStatus;
        patientId?: string;
    }): Promise<GetRegistrationListRsp> {
        const { date, ...others } = params;
        return ABCApiNetwork.get("nurse/therapy-registrations", {
            queryParameters: { limit: 20, date: TimeUtils.formatDate(date ?? new Date()), ...others },
            clazz: GetRegistrationListRsp,
            clearUndefined: true,
        });
    }

    static savePatientRegistration(params: { id: string; detail: RegistrationDetailReq }): Promise<RegistrationDetail> {
        const { id, detail } = params;
        const { registrationFormItem, ...others } = detail;
        const { reserveDate, doctorId, orderNo, ...othersReg } = registrationFormItem!;

        let isDesignatedTime = true; // 判断是否为指定时间
        // 预约不走这块逻辑（如果当前排班时段刚好满足下面之一，则会导致orderNo没有，导致报错）
        if (!registrationFormItem?.isReserved) {
            if (_.isEqual(registrationFormItem?.reserveTime, new Range<string>("00:00", "12:00"))) {
                isDesignatedTime = false;
            } else if (_.isEqual(registrationFormItem?.reserveTime, new Range<string>("12:00", "18:00"))) {
                isDesignatedTime = false;
            } else if (_.isEqual(registrationFormItem?.reserveTime, new Range<string>("18:00", "24:00"))) {
                isDesignatedTime = false;
            }
        }
        return ABCApiNetwork.put(`registrations/${id}`, {
            body: {
                registrationFormItem: {
                    reserveDate: TimeUtils.formatDate(reserveDate),
                    ...othersReg,
                    doctorId: doctorId,
                    orderNo: doctorId && isDesignatedTime ? orderNo : "",
                },
                ...others,
            },
            clazz: RegistrationDetail,
        }).then((rsp) => {
            RegistrationAgent.changeObserver.next();
            return rsp;
        });
    }

    static cancelPatientRegistration(id: string): Promise<void> {
        return ABCApiNetwork.put(`registrations/${id}/cancel`).then(() => {
            RegistrationAgent.changeObserver.next();
        });
    }

    static getRegistrationsDoctorList(date?: Date, registrationType = 0): Promise<GetRegistrationsDoctorList> {
        return ABCApiNetwork.get("registrations/doctorlist", {
            queryParameters: { date: TimeUtils.formatDate(date ?? new Date()), limit: 99999, offset: 0, registrationType },
            clazz: GetRegistrationsDoctorList,
        });
    }

    /**
     * 挂号计算当前展示号数
     * @param params
     * @param isDentistryClinic
     * @param registrationType
     * @param ignoreOrderNo
     */
    static getDoctorOrderNo(
        params: RegistrationFormItem,
        isDentistryClinic = false,
        registrationType?: RegistrationType,
        ignoreOrderNo = true
    ): Promise<RegistrationFormItem> {
        const { reserveDate, orderNo, reserveTime, ...others } = params;
        return ABCApiNetwork.post("registrations/orderno", {
            body: {
                registrationFormItem: {
                    isReserved: 0,
                    reserveDate: TimeUtils.formatDate(reserveDate ?? new Date()),
                    orderNo: ignoreOrderNo ? "" : orderNo,
                    reserveTime: reserveTime,
                    ...others,
                },
                registrationType,
            },
            clazz: GetDoctorOrderNoRsp,
        }).then((rsp) => {
            return isDentistryClinic
                ? _.assign(params, rsp)
                : _.assign(params, rsp, { reserveTime, _registrationReserveTime: rsp?.reserveTime });
        });
    }

    static getDoctorShiftsList(departmentId?: string, start?: Date): Promise<DoctorShiftsListItem[] | undefined> {
        return ABCApiNetwork.get("registrations/doctor/shifts", {
            queryParameters: { departmentId: departmentId ?? "", start: TimeUtils.formatDate(start) },
            clazz: GetDoctorShiftsListRsp,
        }).then((rsp) => rsp.rows);
    }

    static createRegistrationSheet(params: RegistrationDetailReq): Promise<RegistrationDetail> {
        const { registrationFormItem, ...others } = params;
        const { reserveDate, doctorId, orderNo, ...othersReg } = registrationFormItem!;

        let isDesignatedTime = true; // 判断是否为指定时间
        if (_.isEqual(registrationFormItem?.reserveTime, new Range<string>("00:00", "12:00"))) {
            isDesignatedTime = false;
        } else if (_.isEqual(registrationFormItem?.reserveTime, new Range<string>("12:00", "18:00"))) {
            isDesignatedTime = false;
        } else if (_.isEqual(registrationFormItem?.reserveTime, new Range<string>("18:00", "24:00"))) {
            isDesignatedTime = false;
        }

        return ABCApiNetwork.post("registrations", {
            body: {
                registrationFormItem: {
                    reserveDate: TimeUtils.formatDate(reserveDate),
                    ...othersReg,
                    doctorId: doctorId,
                    orderNo: doctorId && isDesignatedTime ? orderNo : "",
                },
                ...others,
            },
            clazz: RegistrationDetail,
        }).then((rsp) => {
            RegistrationAgent.changeObserver.next(rsp);
            return rsp;
        });
    }

    static _registrationAppointmentConfig?: RegistrationsAppointmentConfig;

    static getClinicRegistrationAppointmentConfig(useCache?: boolean): Promise<RegistrationsAppointmentConfig> {
        if (this._registrationAppointmentConfig && useCache) {
            return new Promise<RegistrationsAppointmentConfig>((resolve) => resolve(this._registrationAppointmentConfig));
        }
        return ABCApiNetwork.get("registrations/appointment", { clazz: RegistrationsAppointmentConfig }).then((rsp) => {
            this._registrationAppointmentConfig = rsp;
            return rsp;
        });
    }

    static getDoctorShiftsListForApp(params: {
        doctorId: string;
        departmentId: string;
        timeRange: Range<Date>;
    }): Promise<DoctorShiftsListItem> {
        const { doctorId, timeRange, departmentId } = params;
        return ABCApiNetwork.get(`registrations/doctor/shifts/${doctorId}/for-app`, {
            queryParameters: {
                start: TimeUtils.formatDate(timeRange.start),
                end: TimeUtils.formatDate(timeRange.end),
                departmentId,
            },
            clazz: DoctorShiftsListItem,
        });
    }

    static getPatientOrdersVisitSourceList(): Promise<RegistrationVisitSource[]> {
        return ABCApiNetwork.get("patientorders/visit-source/", { clazz: GetPatientOrdersVisitSourceListRsp }).then(
            (rsp) => rsp.rows ?? []
        );
    }

    // 获取指定医生排班信息
    static getRegistrationDesignatedTime(params: {
        departmentId: string;
        doctorId: string;
        forNormalRegistration: number; // 默认预约为0  挂号为1
        workingDate: string;
        registrationType: number; // 挂号预约 0 理疗预约 1
        registrationCategory?: number;
    }): Promise<RegistrationDesignatedTime> {
        const { doctorId, departmentId, registrationType, registrationCategory = 0, ...others } = params;
        return ABCApiNetwork.get(`registrations/doctor/shifts/${doctorId}`, {
            queryParameters: { ...others, departmentId, registrationType, registrationCategory },
            clazz: RegistrationDesignatedTime,
        });
    }

    /**
     * 获取当前挂号医生诊费
     * @param params
     */
    static getRegistrationReturnVisitFee(params: {
        departmentId: string;
        doctorId: string;
        registrationCategory?: number;
    }): Promise<RegistrationsDoctorFeeItemsRsp> {
        const { ...others } = params;
        return ABCApiNetwork.get(`registrations/employee-registration-fees/clinic/doctor/registration-fee`, {
            queryParameters: { ...others },
            clazz: RegistrationsDoctorFeeItemsRsp,
        });
    }

    /**
     * 获取患者的初复诊状态
     * @param params
     */
    static async getOutpatientRevisitStatus(params: { doctorId: string; patientId: string }): Promise<GetOutpatientRevisitStatusRsp> {
        return ABCApiNetwork.get(`outpatients/patients/${params.patientId}/revisit-status`, {
            queryParameters: {
                ...params,
            },
            clazz: GetOutpatientRevisitStatusRsp,
        });
    }

    /**
     * 获取诊所所有医生挂号费列表
     */
    static async getClinicAllDoctorRegFeeList(): Promise<GetClinicAllDoctorRegFeeListRsp> {
        return ABCApiNetwork.get("registrations/employee-registration-fees", { clazz: GetClinicAllDoctorRegFeeListRsp });
    }

    /**
     * 获取挂号待办列表
     */
    static async getRegistrationQueryApplyAudit(): Promise<GetRegistrationQueryApplyAuditRsp> {
        return ABCApiNetwork.get("registrations/query/apply-audit", { clazz: GetRegistrationQueryApplyAuditRsp });
    }

    /**
     * 获取患者最近的预约结果
     * @param patientId
     */
    static async getRegistrationQueryNearestCreated(patientId: string): Promise<RegistrationNearestCreated> {
        return ABCApiNetwork.get(`registrations/query/patient/${patientId}/wait-audit/nearest-created`, {
            clazz: RegistrationNearestCreated,
        });
    }

    /**
     * 拒绝、去预约患者预约处理
     * @param id
     * @param body
     */
    static async putRegistrationPatientManageAudit(
        id: string,
        body?: {
            auditStatus: PatientRegAuditStatus;
            registrationReq?: RegistrationAuditReq;
        }
    ): Promise<RegistrationNearestCreated> {
        const { registrationReq, ...others } = body!;
        let bodyParams = {
            ...body,
        };
        if (!!registrationReq) {
            const { registrationFormItem, ...otherParams } = registrationReq!;
            const { reserveDate, ...othersReg } = registrationFormItem!;
            bodyParams = {
                registrationReq: {
                    registrationFormItem: {
                        reserveDate: TimeUtils.formatDate(reserveDate),
                        ...othersReg,
                    },
                    ...otherParams,
                },
                ...others,
            };
        }

        return ABCApiNetwork.put(`registrations/manage/${id}/audit`, {
            body: bodyParams,
            clazz: RegistrationNearestCreated,
        })
            .then((rsp) => {
                this.registrationManageAuditObserver.next();
                return rsp;
            })
            .catch((e) => {
                this.registrationManageAuditObserver.next();
                throw e;
            });
    }

    /**
     * 获取固定号源模式看板详情
     * @param options
     */
    static async getRegistrationFixedOrderKanbanDetail(options: {
        start?: Date;
        end?: Date;
        registrationType: number;
        doctorIds: string[];
    }): Promise<GetRegistrationKanbanDetailRsp> {
        const { start = new Date(), end = start, doctorIds, ...others } = options;
        return ABCApiNetwork.post("registrations/query/quick-list/fixed-order/kan-ban", {
            body: {
                start: start.format("yyyy-MM-dd"),
                end: end.format("yyyy-MM-dd"),
                doctorIds,
                ...others,
            },
            clazz: GetRegistrationKanbanDetailRsp,
        }).then((rsp) => {
            //前置不指定医生
            const _rsp = JsonMapper.deserialize(GetRegistrationKanbanDetailRsp, { ...rsp });
            _rsp.dailyViews = _rsp.dailyViews?.map((dv) => {
                dv.dailyEmployeeList = dv.dailyEmployeeList?.sort((de1, de2) => {
                    const index1 = doctorIds.indexOf(de1.employeeId ?? "");
                    const index2 = doctorIds.indexOf(de2.employeeId ?? "");
                    if (index1 > index2) {
                        return 1;
                    } else if (index1 < index2) {
                        return -1;
                    } else {
                        return 0;
                    }
                });
                dv.dailyEmployeeList = dv.dailyEmployeeList?.sort((de1, de2) => {
                    if (de1.employeeId == DEFAULT_DOCTOR_ID && de2.employeeId != DEFAULT_DOCTOR_ID) {
                        return -1;
                    } else if (de1.employeeId != DEFAULT_DOCTOR_ID && de2.employeeId == DEFAULT_DOCTOR_ID) {
                        return 1;
                    } else {
                        return 0;
                    }
                });
                return dv;
            });

            return _rsp;
        });
    }

    /**
     * 获取挂号灵活时间模式看板详情
     * @param options
     */
    static async getRegistrationFlexibleTimeKanbanDetail(options: {
        start?: Date;
        end?: Date;
        registrationType: number;
        doctorIds: string[];
    }): Promise<GetRegistrationKanbanDetailRsp> {
        const { start = new Date(), end = start, doctorIds, ...others } = options;
        return ABCApiNetwork.post("registrations/query/quick-list/flexible-time/kan-ban", {
            body: {
                start: start.format("yyyy-MM-dd"),
                end: end.format("yyyy-MM-dd"),
                doctorIds,
                ...others,
            },
            clazz: GetRegistrationKanbanDetailRsp,
        }).then((rsp) => {
            //前置不指定医生
            const _rsp = JsonMapper.deserialize(GetRegistrationKanbanDetailRsp, { ...rsp });
            _rsp.kanBanDailyViews = _rsp.kanBanDailyViews?.map((dv) => {
                dv.dailyEmployeeList = dv.dailyEmployeeList?.sort((de1, de2) => {
                    const index1 = doctorIds.indexOf(de1.employeeId ?? "");
                    const index2 = doctorIds.indexOf(de2.employeeId ?? "");
                    if (index1 > index2) {
                        return 1;
                    } else if (index1 < index2) {
                        return -1;
                    } else {
                        return 0;
                    }
                });
                dv.dailyEmployeeList = dv.dailyEmployeeList?.sort((de1, de2) => {
                    if (de1.employeeId == DEFAULT_DOCTOR_ID && de2.employeeId != DEFAULT_DOCTOR_ID) {
                        return -1;
                    } else if (de1.employeeId != DEFAULT_DOCTOR_ID && de2.employeeId == DEFAULT_DOCTOR_ID) {
                        return 1;
                    } else {
                        return 0;
                    }
                });
                return dv;
            });

            return _rsp;
        });
    }

    /**
     * 根据patientOrderId查询预约信息
     * @param patientOrderId
     * @param registrationType
     */
    static async queryRegistrationListByPatientOrderId(patientOrderId: string, registrationType: number): Promise<RegistrationFormItem[]> {
        const rsp: { rows: RegistrationFormItem[] } = await ABCApiNetwork.get(`registrations/query/list/patientOrder/${patientOrderId}`, {
            queryParameters: {
                registrationType,
            },
        });
        return rsp?.rows ?? [];
    }

    /**
     * 获取医生排班列表情况
     * @param params
     */
    static async getRegistrationDoctorScheduleStatus(params: {
        date?: string;
        registrationType?: number;
        scene?: number;
    }): Promise<RegistrationDoctorSchedule> {
        return await ABCApiNetwork.get("registrations/doctor/schedule/status", {
            queryParameters: params,
            clazz: RegistrationDoctorSchedule,
        });
    }
    /**
     * 获取医生号种开通情况
     *
     * @param params
     */
    static async getRegistrationDoctorEnableCategories(params?: {
        departmentId?: string;
        doctorId?: string;
    }): Promise<RegistrationDoctorEnableCategories> {
        return await ABCApiNetwork.get("registrations/employee-registration-fees/clinic/doctor/enable-categories", {
            queryParameters: params,
            clazz: RegistrationDoctorEnableCategories,
            clearUndefined: true,
        });
    }
}
