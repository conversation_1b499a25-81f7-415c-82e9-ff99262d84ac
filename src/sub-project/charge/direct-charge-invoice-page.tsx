/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/6/9
 *
 * @description 直接收费
 *
 */
import { IconFontView, SizedBox } from "../base-ui";
import { Text, View } from "@hippy/react";
import { ChargeInvoiceDetailData, ChargeInvoiceSource, ChargeInvoiceType, ChargeSourceFormType, ChargeStatus } from "./data/charge-beans";
import React from "react";
import { DirectChargeInvoicePageBloc } from "./direct-charge-invoice-page-bloc";
import { BlocBuilder } from "../bloc";
import { ListSettingItemStyle } from "../base-ui/views/list-setting-item";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";

import _, { cloneDeep } from "lodash";
import { ChargeInvoiceEditView } from "./charge-invoice-edit-view";
import { BaseComponent } from "../base-ui/base-component";
import { ABCUtils } from "../base-ui/utils/utils";
import { AbcPopMenu, MenuItem, PopMenu } from "../base-ui/views/pop-menu";
import WillPopListener from "../base-ui/views/will-pop-listener";
import { TreeDotView } from "../base-ui/iconfont/iconfont-view";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../base-ui/base-page";
import { ScrollToErrorViewState, ScrollToFocusItemState } from "./charge-invoice-edit-view-bloc";
import { AbcScrollView } from "../base-ui/views/abc-scroll-view";
import { AbcView } from "../base-ui/views/abc-view";
import AbcPatientCardInfoView from "../outpatient/views/new-patient-Info-view";
import { OutpatientHistoryDialogResult } from "../outpatient/outpatient-history-dialog";
import { ChargeAddMedicineButton } from "./view/charge-add-medicine-button";
import { AbcToolBar, AbcToolBarButtonStyle1, AbcToolBarButtonStyle2 } from "../base-ui/abc-app-library/tool-bar-button/tool-bar";
import { KeyboardListenerView } from "../base-ui/views/keyboard-listener-view";
import abcI18Next from "../language/config";
import { ClinicDoctorInfo, HistoryPermissionModuleType } from "../base-business/data/beans";
import { ItemCardSelection, ItemCardSelectionProps } from "../base-ui/selection/item-card-selection";
import { ChargeProductEmployeeView } from "./view/charge-product-employee-view";
import { userCenter } from "../user-center";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { AbcBannerTips } from "../base-ui/abc-banner-tips/abc-banner-tips";
import { PatientOrderLockType } from "../base-business/data/patient-order/patient-order-bean";
import { DeviceUtils } from "../base-ui/utils/device-utils";

interface DirectChargeInvoicePageProps {
    localDraftId?: string;
    networkDraftChargeId?: string;
    detailData?: ChargeInvoiceDetailData;
}

enum DirectChargeMenuItemValue {
    deleteDraft, //删除
    shareCharge, //挂单
    closeChargeSheet, //关闭
    send, //推送
    save, //保存
}

export class DirectChargeInvoicePage extends BaseBlocNetworkPage<DirectChargeInvoicePageProps, DirectChargeInvoicePageBloc> {
    bloc: DirectChargeInvoicePageBloc;
    private _scrollView: AbcScrollView | null = null;

    constructor(props: DirectChargeInvoicePageProps) {
        super(props);

        this.bloc = new DirectChargeInvoicePageBloc(this.props);
        this.addDisposable(this.bloc);
    }

    componentDidMount(): void {
        this.bloc.state
            .subscribe((state) => {
                let status = ABCNetworkPageContentStatus.show_data;
                if (state.loadingDetailData) {
                    status = ABCNetworkPageContentStatus.loading;
                } else if (state.loadingDetailDataError) {
                    status = ABCNetworkPageContentStatus.error;
                }
                this.setContentStatus(status, state.loadingDetailDataError);
            })
            .addToDisposableBag(this);

        this.bloc.editBloc.state
            .subscribe((state) => {
                if (state instanceof ScrollToErrorViewState && state.focusErrorViewKey) {
                    this._scrollView?.scrollChildToVisible(state.focusErrorViewKey, "firstChild", "lastChild");
                } else if (state instanceof ScrollToFocusItemState && !!state.currentFocusChargeFormItem?.compareKey()) {
                    this._scrollView?.scrollChildToVisible(state.currentFocusChargeFormItem?.compareKey(), "firstChild", "lastChild");
                }
            })
            .addToDisposableBag(this);
    }

    getAppBarTitle(): string {
        return "零售收费";
    }

    getBottomSafeAreaColor(): Color {
        return (this.bloc.detailData?.chargeForms?.length ?? 0) > 0 ? Colors.white : Colors.prescriptionBg;
    }

    getStatusBarColor(): Color {
        return Colors.panelBg;
    }

    getAppBarBgColor(): Color {
        return Colors.panelBg;
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    getRightAppBarIcons(): JSX.Element[] {
        const bloc = this.bloc,
            state = bloc.currentState,
            detailData = state.detailData;
        if (!detailData) return [];
        return [
            <View key={"all"} style={ABCStyles.rowAlignCenter}>
                <View key={"setting"} style={{ marginRight: Sizes.dp8 }}>
                    {this.chargeSettingIcons()}
                </View>
            </View>,
        ];
        // return [<BlocBuilder key={"getRightAppBarIcons"} bloc={this.bloc} condition={() => true} build={() => this._renderRightIcons()} />];
    }

    private _createMenuItemsList(): MenuItem<number>[] {
        const state = this.bloc.currentState;
        const { detailData, canPushChargeOrder, hasChanged } = state;
        const menuItems: MenuItem<number>[] = [];

        if (!detailData) return menuItems;
        const localDraft = detailData.isLocalDraft ?? false;

        const netDraft = detailData.isNetworkDraft ?? false;
        const isRetail = detailData.source == ChargeInvoiceSource.retail;
        const reCharge = detailData?.reCharge__ ?? false;
        const { canEditChargeSheet } = detailData!;
        if (state.hasContent && (!detailData.isNetworkDraft || reCharge)) {
            menuItems.push(
                new MenuItem({
                    value: DirectChargeMenuItemValue.shareCharge,
                    icon: <IconFontView name={"set"} color={Colors.T1} size={Sizes.dp24} />,
                    text: "挂单",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        if (state.detailData?.canClose()) {
            menuItems.push(
                new MenuItem({
                    value: DirectChargeMenuItemValue.closeChargeSheet,
                    icon: <IconFontView name={"trash"} color={Colors.T1} size={Sizes.dp24} />,
                    text: "关闭",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        /// 本地草稿，零售挂单才能删除
        if (localDraft || (netDraft && isRetail)) {
            menuItems.push(
                new MenuItem({
                    value: DirectChargeMenuItemValue.deleteDraft,
                    icon: <IconFontView name={"trash"} color={Colors.T1} size={Sizes.dp24} />,
                    text: "删除",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        if (canPushChargeOrder && canEditChargeSheet) {
            menuItems.push(
                new MenuItem({
                    value: DirectChargeMenuItemValue.send,
                    icon: <IconFontView name={"send"} color={Colors.T1} size={Sizes.dp24} style={{ bottom: -Sizes.dp1 }} />,
                    text: "推送",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }
        if (!!detailData?.id && hasChanged) {
            menuItems.push(
                new MenuItem({
                    value: DirectChargeMenuItemValue.save,
                    icon: <IconFontView name={"save"} color={Colors.T1} size={Sizes.dp24} style={{ bottom: -Sizes.dp1 }} />,
                    text: "保存",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        return menuItems;
    }

    private async operateSettingIcon(menuItems: MenuItem<number>[]): Promise<void> {
        const select = await AbcPopMenu.show(
            menuItems,
            { x: Sizes.dp24, y: Sizes.dp42 },
            { x: Sizes.dp30, y: Sizes.dp42 },
            { fullGrid: true }
        );
        switch (select) {
            case DirectChargeMenuItemValue.shareCharge: {
                this.bloc.requestSaveDraft();
                break;
            }
            case DirectChargeMenuItemValue.closeChargeSheet: {
                this.bloc.requestCloseChargeSheet();
                break;
            }
            case DirectChargeMenuItemValue.deleteDraft: {
                this.bloc.requestDeleteDraft();
                break;
            }
            case DirectChargeMenuItemValue.send: {
                this.bloc.requestSend();
                break;
            }
            case DirectChargeMenuItemValue.save: {
                this.bloc.requestSaveChargeInvoice();
                break;
            }
        }
    }

    chargeSettingIcons(): JSX.Element[] {
        const state = this.bloc.currentState;
        const _key = 0;
        if (state.detailData == undefined || state.detailData.orderIsLocking) return [<View key={_key} />];

        const menuItems: MenuItem<number>[] = this._createMenuItemsList();

        if (_.isEmpty(menuItems)) return [<View key={_key} />];
        return [
            <View
                key={_key}
                style={{ paddingHorizontal: Sizes.dp8 }}
                collapsable={false}
                onClick={() => this.operateSettingIcon(menuItems)}
            >
                <TreeDotView color={Colors.T1} />
            </View>,
        ];
    }

    _renderRightIcons(): JSX.Element {
        const state = this.bloc.currentState;
        const detailData = state.detailData;
        if (detailData == undefined) return <View />;

        const localDraft = detailData.isLocalDraft ?? false;

        const netDraft = detailData.isNetworkDraft ?? false;
        const isRetail = detailData.source == ChargeInvoiceSource.retail;
        const deleteDraft = 1;
        const shareCharge = 2; //挂单
        const closeChargeSheet = 3;

        const menuItems: MenuItem<number>[] = [];

        const reCharge = detailData?.reCharge__ ?? false;

        if (state.hasContent && (!detailData.isNetworkDraft || reCharge)) {
            menuItems.push(
                new MenuItem({
                    value: shareCharge,
                    icon: <IconFontView name={"share_2"} color={Colors.white} size={Sizes.dp16} />,
                    text: "挂单",
                })
            );
        }

        if (state.detailData?.canClose()) {
            menuItems.push(
                new MenuItem({
                    value: closeChargeSheet,
                    icon: <IconFontView name={"cross_circle"} color={Colors.white} size={Sizes.dp16} />,
                    text: "关闭",
                })
            );
        }

        /// 本地草稿，零售挂单才能删除
        if (localDraft || (netDraft && isRetail)) {
            menuItems.push(
                new MenuItem({
                    value: deleteDraft,
                    icon: <IconFontView name={"trash"} color={Colors.white} size={Sizes.dp16} />,
                    text: "删除",
                })
            );
        }

        if (_.isEmpty(menuItems)) return <View />;

        return (
            <AbcView
                style={{ paddingHorizontal: Sizes.dp16 }}
                collapsable={false}
                onClick={async () => {
                    const select = await PopMenu.show(menuItems, { x: Sizes.dp10, y: Sizes.dp40 });
                    if (select == deleteDraft) {
                        this.bloc.requestDeleteDraft();
                    } else if (select == shareCharge) {
                        this.bloc.requestSaveDraft();
                    } else if (select == closeChargeSheet) {
                        this.bloc.requestCloseChargeSheet();
                    }
                }}
            >
                <TreeDotView />
            </AbcView>
        );
    }

    onBackClick(): void {
        this.bloc.requestBack();
    }

    renderContent(): JSX.Element {
        const detailData = this.bloc.currentState.detailData;
        if (!detailData) return <View />;
        const {
            hasChanged,
            diagnoseCount,
            airPharmacyConfig,
            virtualPharmacyConfig,
            canSeePatientHistory,
            canSeePatientMobile,
            showSellerRequiredHint,
            isDentistry,
            doctorDisplayStr,
            nurseDisplayStr,
            treatmentChargeForm,
            consultantList,
            matchClinicDoctorInfo,
            shebaoRefundException,
        } = this.bloc.currentState;
        const { canEditChargeSheet, orderIsLocking, lockCopy, patientOrderLocks, chargeLockOrder } = detailData;
        const editable = detailData.type !== ChargeInvoiceType.therapy || !_.isEmpty(detailData.patient?.name);
        const canSwitchPatient = canEditChargeSheet && !(detailData.reCharge__ ?? false) && editable;
        const isExistAirPrescription = !!detailData?.getChargeForm(ChargeSourceFormType.airPharmacyAndVirtual);
        const showAirBtn = detailData?.source != ChargeInvoiceSource.therapy; //执行站的单子不能添加空中药房
        const hasModify = detailData?.canEditChargeSheet && (!detailData?.status || detailData?.status == ChargeStatus.draft);
        const consultantInitIndex = consultantList?.findIndex((t) => t?.employeeId == detailData?.consultantId);
        // 如果本单有收费异常内容，又正在收费中，仅展示收费中横幅
        const isExistChargeInProgress =
            patientOrderLocks?.find((t) => t.businessKey == PatientOrderLockType.chargeSheetPay && !!t.status) || chargeLockOrder;
        const isOho = DeviceUtils.isOhos();
        return (
            <View style={{ flex: 1, ...(isOho ? {} : { backgroundColor: Colors.prescriptionBg }) }}>
                {!isExistChargeInProgress && detailData.isSheBaoAbnormal && (
                    <AbcBannerTips tips={`医保${shebaoRefundException ? "退费" : "收费"}异常，请前往客户端处理`} />
                )}
                {!isExistChargeInProgress && detailData.isNotSheBaoAbnormal && (
                    <AbcBannerTips
                        tips={"收费单收费异常，请及时处理"}
                        onMaskClick={() => {
                            this.bloc.requestHandleChargeAbnormal(!detailData.isNotSheBaoAbnormal);
                        }}
                    />
                )}
                {hasChanged && <WillPopListener onWillPop={() => this.bloc.requestBack()} />}
                {orderIsLocking && !!lockCopy.tips && (
                    <AbcBannerTips
                        tips={lockCopy.tips}
                        cancelText={lockCopy.isCancel ? "支付遇到问题？" : ""}
                        cancelOperate={() => this.bloc.requestCancelPayment()}
                    />
                )}
                <AbcScrollView ref={(ref) => (this._scrollView = ref)} style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                    <View ref={"firstChild"} collapsable={false} style={{ height: Sizes.dp1 }} />
                    {/*<PatientInfoViewWithModifyAndHistory*/}
                    {/*    patient={detailData?.patient}*/}
                    {/*    patientSwitchable={canSwitchPatient}*/}
                    {/*    onChanged={this.bloc.requestUpdatePatient.bind(this.bloc)}*/}
                    {/*    copyEnable={canEditChargeSheet}*/}
                    {/*    editable={editable}*/}
                    {/*    onCopyHistory={(result) => this.bloc.editBloc.requestCopyHistoryPrescription(result)}*/}
                    {/*    showHistoryBtn={canSeePatientHistory}*/}
                    {/*/>*/}
                    <AbcPatientCardInfoView
                        isEditing={editable && !orderIsLocking}
                        patient={detailData.patient}
                        diagnoseCount={diagnoseCount}
                        patientSwitchable={canSwitchPatient}
                        hideCopyHistory={true}
                        onChange={(patient) => {
                            this.bloc.requestUpdatePatient(patient);
                        }}
                        onClearPatient={() => {
                            this.bloc.requestClearPatient();
                        }}
                        onChangePatientHistory={(result: OutpatientHistoryDialogResult) => {
                            this.bloc.editBloc.requestCopyHistoryPrescription(result); // 复制病历或处方
                        }}
                        isCanSeePatientHistoryInCashier={canSeePatientHistory}
                        type={HistoryPermissionModuleType.cashier}
                        canSeePatientMobileInCashier={canSeePatientMobile}
                        canEditPatientInfo={!orderIsLocking}
                    />

                    <_SellerView
                        detailData={detailData}
                        showSellerRequiredHint={showSellerRequiredHint}
                        doctorList={matchClinicDoctorInfo}
                        onSelectSeller={(item) => this.bloc.requestSelectItemSeller(item)}
                    />
                    {isDentistry && (
                        <ChargeProductEmployeeView
                            showDoctorItem={!!treatmentChargeForm?.length}
                            showConsultantItem={!!consultantList?.length}
                            doctorNurseDisplayStr={`${doctorDisplayStr}${!!nurseDisplayStr ? "、" : ""}${nurseDisplayStr}` ?? ""}
                            consultantDisplayStr={detailData?.consultantName ?? ""}
                            canModifyDoctorNurse={hasModify}
                            canModifyConsultant={hasModify}
                            consultantList={consultantList}
                            consultantInitIndex={consultantInitIndex}
                            modifyProductEmployee={() => this.bloc.requestModifyProductEmployee()}
                            modifyConsultant={(index) => this.bloc.requestModifyConsultant(index)}
                        />
                    )}
                    {canEditChargeSheet && _.isEmpty(this.bloc.editBloc.currentState.detailData?.chargeForms) && (
                        <ChargeAddMedicineButton
                            canUseAirPharmacy={airPharmacyConfig?.canUseAirPharmacy}
                            isExistAirPrescription={isExistAirPrescription}
                            isVirtualPharmacyOpen={!!virtualPharmacyConfig?.isVirtualPharmacyOpen}
                            addMedicine={() => this.bloc.editBloc.requestAddMedicine()}
                            addAirPharmacyPrescriptionForm={() => this.bloc.editBloc.requestAddAirPharmacyPrescriptionForm()}
                            isExistAirBtn={showAirBtn}
                        />
                    )}

                    <ChargeInvoiceEditView
                        bloc={this.bloc.editBloc}
                        canUseAirPharmacy={airPharmacyConfig?.canUseAirPharmacy}
                        isExistAirPrescription={isExistAirPrescription}
                        isVirtualPharmacyOpen={!!virtualPharmacyConfig?.isVirtualPharmacyOpen}
                    />
                    <SizedBox height={Sizes.dp32} />
                    <View ref={"lastChild"} collapsable={false} style={{ height: Sizes.dp1 }} />
                </AbcScrollView>
                <_BottomBar />
            </View>
        );
    }
}

interface _SellerViewProps {
    detailData?: ChargeInvoiceDetailData;
    showSellerRequiredHint?: boolean;
    doctorList?: ClinicDoctorInfo[];
    onSearchSeller?: (value?: string) => void;
    onSelectSeller?: (item?: ClinicDoctorInfo) => void;
}

const _SellerView: React.FC<_SellerViewProps> = ({ detailData, showSellerRequiredHint, doctorList, onSelectSeller }) => {
    const sourceOfExecutionStation = detailData?.source == ChargeInvoiceSource.therapy; // 执行站创建的收费单 不可更改销售员
    const isLockingOrder = detailData?.orderIsLocking;
    const searchListByKeyword = (searchText?: string): ClinicDoctorInfo[] => {
        const list = cloneDeep(doctorList) ?? [];
        let filterResult: ClinicDoctorInfo[] = [];
        const keyword = (searchText ?? "").toLowerCase();
        if (keyword.length === 0) {
            filterResult = list;
        }
        filterResult = list
            .filter(
                (item) =>
                    item.doctorName!.toLowerCase().indexOf(keyword) >= 0 ||
                    item.doctorNamePyFirst!.toLowerCase().indexOf(keyword) >= 0 ||
                    item.doctorNamePy!.toLowerCase().indexOf(keyword) >= 0
            )
            .sort((item) => (item.doctorId == userCenter.employee?.id ? -1 : 1)); // 当前医生置顶显示
        filterResult.splice(
            0,
            0,
            JsonMapper.deserialize(ClinicDoctorInfo, {
                doctorName: "不指定",
                departmentName: "",
                doctorNamePyFirst: "",
                doctorNamePy: "",
                doctorId: "",
                departmentId: "",
            })
        );
        return filterResult;
    };
    const options: ItemCardSelectionProps = {
        airTestKey: "开单人",
        cardItemList: [
            {
                title: "开单人",
                contentHint: "选择开单人",
                content: `${detailData?.sellerName ?? ""}${detailData?.defaultDepartmentName}` ?? "",
                showErrorBorder: showSellerRequiredHint,
                style: { paddingVertical: Sizes.dp5 },
                itemStyle: isLockingOrder || sourceOfExecutionStation ? ListSettingItemStyle.normal : ListSettingItemStyle.expandIcon,
                onClick: !isLockingOrder && !sourceOfExecutionStation ? () => ({}) : undefined,
                list: doctorList,
                type: 2,
                displayOption: {
                    title: (data) => {
                        return data["doctorName"];
                    },
                    subTitle: (data) => {
                        return data["departmentName"];
                    },
                },
                searchHint: "输入销售员姓名或姓名字母搜索",
                filterList: (value) => searchListByKeyword(value),
                onSelectItem: (item) => onSelectSeller?.(item),
                selected: (item) => detailData?.sellerId == item.doctorId && detailData?.sellerDepartmentId == item.departmentId,
            },
        ],
    };
    return <ItemCardSelection {...options} />;
};

interface _BottomBarProps {}

class _BottomBar extends BaseComponent<_BottomBarProps> {
    static contextType = DirectChargeInvoicePageBloc.Context;

    constructor(props: _BottomBarProps) {
        super(props);
    }

    render() {
        const bloc = DirectChargeInvoicePageBloc.fromContext(this.context);
        const state = bloc.currentState;
        const { detailData, chargeConfig, calculating } = state;
        if (_.isEmpty(detailData?.chargeForms)) return <View />;
        const toolBarItem: JSX.Element[] = [];

        const hasChargeForm = !_.isEmpty(detailData!.chargeForms);
        const singleBargain = chargeConfig?.singleBargainSwitch === 1 || chargeConfig?.bargainSwitch === 1;
        const isLockingOrder = detailData?.orderIsLocking;

        if (hasChargeForm && singleBargain) {
            toolBarItem.push(
                <AbcToolBarButtonStyle2
                    text={"议价"}
                    showIndicator={calculating}
                    onClick={() => bloc.requestSingleBargain()}
                    disable={isLockingOrder}
                />
            );
        }

        toolBarItem.push(
            <AbcToolBarButtonStyle1
                text={`收费`}
                showIndicator={calculating}
                onClick={() => this._onChargeBtnTap()}
                disable={isLockingOrder}
            />
        );

        return (
            <KeyboardListenerView>
                <View style={[ABCStyles.rowAlignCenter, ABCStyles.topLine, { backgroundColor: Colors.white, paddingVertical: Sizes.dp12 }]}>
                    {this._renderDiscountSummaryView()}
                    <View style={{ flexGrow: 1 }}>
                        {toolBarItem.length && (
                            <AbcToolBar showMoreBtn={true} showCount={2} direction={"right"} lineHeight={0} toolbarHeight={Sizes.dp44}>
                                {toolBarItem}
                            </AbcToolBar>
                        )}
                    </View>
                </View>
            </KeyboardListenerView>
        );
    }

    private _renderDiscountSummaryView(): JSX.Element {
        const detailData = DirectChargeInvoicePageBloc.fromContext(this.context).currentState.detailData;
        if (!detailData) return <View />;

        const needPayFee = detailData.chargeSheetSummary?.needPayFee;

        // 优惠议价费用
        const { oddFee = 0 } = detailData.chargeSheetSummary ?? {};
        const discountFee =
            oddFee +
            detailData.promotionsTotalPrice +
            detailData.computedTotalDeductPrice.totalDeductPrice +
            -(detailData.patientPointsInfo?.checkedDeductionPrice ?? 0) +
            detailData.computedDiscountPricce(detailData.couponPromotions ?? []) +
            detailData.computedDiscountPricce(detailData.giftRulePromotions ?? []) +
            (detailData.chargeSheetSummary?.draftAdjustmentFee ?? 0) +
            (detailData.chargeSheetSummary?.outpatientAdjustmentFee ?? 0);
        const isLockingOrder = detailData.orderIsLocking;
        return (
            <View style={{ paddingLeft: Sizes.dp16 }}>
                <View style={[{ flexDirection: "row", alignItems: "flex-end", marginBottom: Sizes.dp5 }]}>
                    <Text style={[TextStyles.t13NT1.copyWith({ lineHeight: Sizes.dp14 })]}>合计：</Text>
                    <Text style={[TextStyles.t13NM.copyWith({ lineHeight: Sizes.dp14 })]} numberOfLines={1}>
                        {abcI18Next.t("¥")}
                    </Text>
                    <Text style={[TextStyles.t18MM.copyWith({ lineHeight: Sizes.dp20 })]} numberOfLines={1}>
                        {ABCUtils.formatPrice(needPayFee ?? 0)}
                    </Text>
                </View>
                <AbcView
                    style={[{ flexDirection: "row", alignItems: "flex-end" }]}
                    onClick={() => {
                        !isLockingOrder &&
                            DirectChargeInvoicePageBloc.fromContext(this.context).editBloc.requestShowDiscountSummaryDetail();
                    }}
                >
                    <Text style={[TextStyles.t13NT1.copyWith({ lineHeight: Sizes.dp14 })]}>优惠/议价：</Text>
                    <Text style={[TextStyles.t13NT1.copyWith({ color: Colors.R2, lineHeight: Sizes.dp14 })]}>
                        {ABCUtils.formatPriceWithRMB(discountFee)}
                    </Text>
                    {!isLockingOrder && <IconFontView name={"arrow_up"} color={Colors.R2} size={Sizes.dp12} />}
                </AbcView>
            </View>
        );
    }

    private _onChargeBtnTap() {
        DirectChargeInvoicePageBloc.fromContext(this.context).requestCharge();
    }
}
