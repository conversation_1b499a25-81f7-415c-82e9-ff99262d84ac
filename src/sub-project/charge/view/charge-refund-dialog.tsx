import { DialogIndex } from "../../base-ui/dialog/dialog-builder";
import React from "react";
import { Text, View } from "@hippy/react";
import { showStateLogoConfirmDialog, showStateLogoQueryDialog } from "./state-logo-dialog-builder";
import { Sizes, TextStyles } from "../../theme";
import { AssetImageView } from "../../base-ui/views/asset-image-view";
import { BaseComponent } from "../../base-ui/base-component";
import { SizedBox } from "../../base-ui";

export class ChargeRefundDialog {
    static async showQueryPopup(params: { logo: string; title: string; content: string }): Promise<boolean | undefined> {
        const dialogIndex = await showStateLogoQueryDialog(
            <WithStateLogoTitle logo={params.logo} title={params.title} />,
            params.content,
            "确认",
            "取消",
            "center"
        );
        if (dialogIndex != DialogIndex.positive) return false;
        return true;
    }

    static async showConfirmPopup(params: { logo: string; title: string; content: string }): Promise<boolean | undefined> {
        const dialogResult = await showStateLogoConfirmDialog({
            title: <WithStateLogoTitle logo={params.logo} title={params.title} />,
            content: params.content,
        });
        if (dialogResult != DialogIndex.positive) return false;
        return true;
    }
}

interface WithStateLogoTitleProps {
    logo?: string;
    title: string;
}
class WithStateLogoTitle extends BaseComponent<WithStateLogoTitleProps> {
    render(): JSX.Element {
        const { logo, title } = this.props;
        return (
            <View style={{ flex: 1, alignItems: "center" }}>
                {!!logo && (
                    <AssetImageView
                        name={`${logo}`}
                        style={{
                            width: Sizes.dp42,
                            height: Sizes.dp42,
                        }}
                    />
                )}
                {logo && <SizedBox height={Sizes.dp16} />}
                <Text style={[TextStyles.t18MB.copyWith({ lineHeight: Sizes.dp28 })]}>{`${title}`}</Text>
            </View>
        );
    }
}
