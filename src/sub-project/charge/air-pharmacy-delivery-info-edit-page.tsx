/**
 * create by deng<PERSON>e
 * desc: 快递信息填写页面
 * create date 2020/6/16
 */

import React from "react";
import { Text, View } from "@hippy/react";
import { DeliveryInfo, DeliveryPayType, Patient } from "../base-business/data/beans";
import { JsonMapper, JsonProperty } from "../common-base-module/json-mapper/json-mapper";
import { ListSettingItem, ListSettingItemStyle } from "../base-ui/views/list-setting-item";
import { Colors, Sizes, TextStyles } from "../theme";
import { ABCUtils } from "../base-ui/utils/utils";
import { ChargeForm } from "./data/charge-beans";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { AirPharmacyDeliveryInfoEditPageBloc } from "./air-pharmacy-delivery-info-edit-page-bloc";
import { BaseBlocPage } from "../base-ui/base-page";
import { Abc<PERSON>utton } from "../base-ui/views/abc-button";
import { AirPharmacyCalculateForm } from "./data/charge-bean-air-pharmacy";
import { DeviceUtils } from "../base-ui/utils/device-utils";

const kMinTitleWidth = Sizes.dp93;

export class AirPharmacyDeliveryInfoEditResultResult {
    @JsonProperty({ type: DeliveryInfo })
    deliverInfo?: DeliveryInfo;
}

export interface AirPharmacyDeliveryInfoEditPageProps {
    create?: boolean; //创建地址
    patient: Patient;
    payTypeMutable?: boolean;
    addressMutable?: boolean;
    deliverFeeEnable?: boolean; //是否编辑快递费
    chargeForm: ChargeForm; //空中药房处方单
    otherAirPharmacyCalculateForm?: AirPharmacyCalculateForm[]; //除了当前处方外，其余空中药房处方
    airPharmacySort?: number;
}

export class AirPharmacyDeliveryInfoEditPage extends BaseBlocPage<
    AirPharmacyDeliveryInfoEditPageProps,
    AirPharmacyDeliveryInfoEditPageBloc
> {
    bloc: AirPharmacyDeliveryInfoEditPageBloc;

    static show(options: AirPharmacyDeliveryInfoEditPageProps): Promise<AirPharmacyDeliveryInfoEditResultResult | undefined> {
        return ABCNavigator.navigateToPage(<AirPharmacyDeliveryInfoEditPage {...options} />);
    }

    constructor(props: AirPharmacyDeliveryInfoEditPageProps) {
        super(props);
        this.bloc = new AirPharmacyDeliveryInfoEditPageBloc(props);
    }

    getAppBarTitle(): string {
        return "快递信息";
    }

    getRightAppBarIcons(): JSX.Element[] {
        return [
            <AbcButton
                key={`finish`}
                text={"保存"}
                style={{
                    backgroundColor: Colors.mainColor,
                    paddingHorizontal: Sizes.dp12,
                    paddingVertical: Sizes.dp5,
                    borderRadius: Sizes.dp4,
                }}
                textStyle={TextStyles.t14MB.copyWith({ color: Colors.white, lineHeight: Sizes.dp20 })}
                onClick={() => this.bloc.requestSubmit()}
                pressColor={Colors.mainColorPress}
            />,
        ];
    }
    getBottomSafeAreaColor(): string {
        return Colors.prescriptionBg;
    }

    onBackClick(): void {
        ABCNavigator.pop(
            JsonMapper.deserialize(AirPharmacyDeliveryInfoEditResultResult, {
                deliverInfo: this.bloc.currentState.deliveryInfo,
            })
        );
    }

    private getPrescriptionChineseIndex(): string {
        const { otherAirPharmacyCalculateForm, chargeForm, airPharmacySort } = this.props;
        chargeForm.sort = airPharmacySort;
        const sameAirPharmacy =
            otherAirPharmacyCalculateForm?.filter(
                (t) => t.vendorId === chargeForm?.vendorId && t.usageScopeId == chargeForm?.usageScopeId
            ) ?? [];

        const combineManufacturer = [chargeForm, ...sameAirPharmacy].sort((a, b) => (a?.sort ?? 0) - (b?.sort ?? 0));
        return combineManufacturer?.map((item) => ABCUtils.toChineseNum((item.sort ?? 0) + 1)).join("、");
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState,
            deliveryInfo = state.deliveryInfo;
        if (deliveryInfo == null) return <View />;

        const { deliveryRule, loadingDeliveryRule } = state;
        let { deliverFeeEnable } = this.props;
        const { otherAirPharmacyCalculateForm, chargeForm } = this.props;

        let payType;
        if (deliveryInfo.deliveryPayType != undefined) {
            payType = deliveryInfo.deliveryPayType == DeliveryPayType.freightCollect ? "到付" : "寄付";
        }

        if (deliveryInfo.deliveryPayType == DeliveryPayType.freightCollect) {
            deliverFeeEnable = false;
        }

        const isShowCombineTips =
            (otherAirPharmacyCalculateForm?.filter(
                (t) =>
                    t.vendorId == chargeForm.vendorId &&
                    t.usageScopeId == chargeForm.usageScopeId &&
                    t.deliveryInfo?.addressCityId == chargeForm.deliveryInfo?.addressCityId &&
                    t.deliveryInfo?.addressProvinceId == chargeForm.deliveryInfo?.addressProvinceId &&
                    t.deliveryInfo?.addressDistrictId == chargeForm.deliveryInfo?.addressDistrictId
            ).length ?? 0) > 0;

        return (
            <View style={{ flex: 1, ...(DeviceUtils.isOhos() ? {} : { backgroundColor: Colors.prescriptionBg }) }}>
                <View
                    style={{
                        backgroundColor: Colors.white,
                        paddingHorizontal: Sizes.listHorizontalMargin,
                    }}
                >
                    <ListSettingItem
                        itemStyle={ListSettingItemStyle.expandIcon}
                        bottomLine={true}
                        title={"收货地址"}
                        titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                        minTitleWidth={kMinTitleWidth}
                        starTitle={"*"}
                        contentBuilder={() => {
                            return !!deliveryInfo?.displayAddress() ? (
                                <View style={[{ flex: 1, paddingVertical: Sizes.dp16 }]}>
                                    <Text style={[TextStyles.t16NT1]}>
                                        {`${deliveryInfo?.deliveryName ?? ""} ${deliveryInfo?.deliveryMobile ?? ""}`}
                                    </Text>
                                    <Text style={[TextStyles.t12NT6, { marginTop: Sizes.dp4, lineHeight: Sizes.dp20 }]}>
                                        {`${deliveryInfo?.displayAddress() ?? ""}`}
                                    </Text>
                                </View>
                            ) : (
                                <View />
                            );
                        }}
                        onClick={() => {
                            if (state.addressMutable) this.bloc.requestSelectDeliveryAddress();
                        }}
                        showErrorBorder={state.addressMutable && state.showErrorHint && !deliveryInfo?.displayAddress()}
                        arrowStyle={{ alignSelf: "flex-start", paddingVertical: Sizes.dp16, marginTop: Sizes.dp1 }}
                        style={{ alignItems: "flex-start" }}
                    />
                    <ListSettingItem
                        itemStyle={ListSettingItemStyle.expandIcon}
                        starTitle={"*"}
                        bottomLine={true}
                        title={"快递公司"}
                        titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                        height={Sizes.dp54}
                        minTitleWidth={kMinTitleWidth}
                        content={deliveryInfo?.deliveryCompany?.name}
                        contentHint={"选择快递公司"}
                        onClick={() => {
                            this.bloc.requestSelectDeliveryCompany();
                        }}
                        showErrorBorder={state.showErrorHint && deliveryInfo.deliveryCompany == null}
                    />
                    <ListSettingItem
                        itemStyle={state.payTypeMutable ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                        starTitle={"*"}
                        bottomLine={true}
                        title={"付款方式"}
                        titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                        height={Sizes.dp54}
                        minTitleWidth={kMinTitleWidth}
                        content={payType}
                        contentHint={"选择付款方式"}
                        onClick={() => {
                            if (state.payTypeMutable) this.bloc.requestSelectPayType();
                        }}
                        showErrorBorder={state.payTypeMutable && state.showErrorHint && deliveryInfo.deliveryPayType == null}
                    />

                    {deliverFeeEnable && (
                        <ListSettingItem
                            title={"快递费用"}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            height={Sizes.dp54}
                            minTitleWidth={kMinTitleWidth}
                            contentBuilder={() => (
                                <View
                                    style={{
                                        paddingVertical: Sizes.dp16,
                                        flex: 1,
                                        justifyContent: "center",
                                        minHeight: Sizes.listItemHeight,
                                    }}
                                >
                                    <Text style={TextStyles.t16NT1} numberOfLines={1}>
                                        {ABCUtils.formatPrice(state.deliveryInfo?.deliveryFee__ ?? 0)}
                                    </Text>
                                    {deliveryRule?.ruleInfo && (
                                        <Text
                                            style={{
                                                ...TextStyles.t12NT6,
                                                marginBottom: Sizes.dp4,
                                            }}
                                        >
                                            {deliveryRule.ruleInfo}
                                        </Text>
                                    )}
                                    {!deliveryRule && !loadingDeliveryRule && (
                                        <Text
                                            style={{
                                                ...TextStyles.t12NR2,
                                                marginBottom: Sizes.dp4,
                                            }}
                                        >
                                            该地址无法送达，请更换药房或联系ABC客服
                                        </Text>
                                    )}
                                </View>
                            )}
                        />
                    )}
                    {isShowCombineTips && (
                        <View style={{ paddingVertical: Sizes.listHorizontalMargin }}>
                            <Text style={[TextStyles.t12NT6]}>
                                {`中药处方${this.getPrescriptionChineseIndex()}供应商同为${
                                    this.props?.chargeForm?.vendorName
                                }，将合并发货，只计算一次快递费用`}
                            </Text>
                        </View>
                    )}
                </View>
            </View>
        );
    }
}
