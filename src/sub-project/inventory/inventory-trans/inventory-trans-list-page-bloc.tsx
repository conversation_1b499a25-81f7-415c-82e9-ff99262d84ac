/**
 * create by <PERSON><PERSON>
 * desc: 调拨列表页面 bloc
 * create date 2021/3/25
 */

import React from "react";
import { Bloc, BlocEvent } from "../../bloc";
import { EventName } from "../../bloc/bloc";
import { InventoryTransDateFieldType, InventoryTransDetail, InventoryTransMode, InventoryTransType } from "./data/inventory-trans-bean";
import { BaseLoadingState } from "../../bloc/bloc-helper";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { GetInventoryTransListReq, GetInventoryTransListRsp, InventoryTransAgent } from "./data/inventory-trans-agent";
import { ABCError } from "../../common-base-module/common-error";
import { userCenter } from "../../user-center";
import _ from "lodash";
import { ignore } from "../../common-base-module/global";
import { Inventory<PERSON>raftManage } from "./data/inventory-draft-manage";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { InventoryTransDetailInvoice } from "./inventory-trans-detail-invoice";
import { InventoryMedicineSearchPage } from "../Inventory-medicine-search-page";
import { GoodsInfo } from "../../base-business/data/beans";
import { Filters } from "../../base-ui/searchBar/search-bar-bean";
import { Range } from "../../base-ui/utils/value-holder";
import { runFuncBeforeCheckExpired } from "../../views/clinic-edition";
import { __filterRelativePharmacyData, InventoryApiErrorCode, InventoryClinicConfig } from "../data/inventory-bean";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { GoodsAgent, PharmacyListItem } from "../../data/goods/goods-agent";
import { InventoryAgent } from "../data/inventory-agent";
import { DirectoryType, InventoryOutStatus } from "../inventory-out/inventory-out-data/inventory-out-bean";
import { Sizes } from "../../theme";
import { ChainClinicsInfo, ClinicAgent, EmployeesMeConfig } from "../../base-business/data/clinic-agent";
import { InventoryDirectoryDialog, InventoryDirectoryDialogState } from "../views/inventory-directory-dialog";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../base-ui/dialog/dialog-builder";
import { ABCApiError } from "../../net";

export class State extends BaseLoadingState {
    loading = true;
    detail?: GetInventoryTransListRsp;

    draftList: InventoryTransDetail[] = [];

    /**
     * 本地草稿列表
     */
    localDraft: InventoryTransDetail[] = [];

    get localDifferenceDraftList(): InventoryTransDetail[] {
        return [...this.draftList, ...this.localDraft].filter(
            (item) => !this.draftList.find((itt) => [item.__draftId, item.id].includes(itt.id))
        );
    }

    params: GetInventoryTransListReq = {
        begDate: undefined,
        endDate: undefined,
        limit: 20,
        dateField: InventoryTransDateFieldType.createDate,
    };
    _params = _.cloneDeep(this.params);
    lastSearchDetail?: GetInventoryTransListRsp;

    chainClinicsList?: ChainClinicsInfo[];
    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;
    currentPharmacy?: PharmacyListItem;
    pharmacyList?: PharmacyListItem[];
    transferMode?: number = InventoryTransMode.interStoreTransfer; //调拨方式（默认店间调拨）
    //调拨方式
    transferModeList = [
        {
            name: "店间调拨",
            id: InventoryTransMode.interStoreTransfer,
        },
        {
            name: "店内调拨",
            id: InventoryTransMode.inStoreTransfer,
        },
    ];

    employeesMeConfig?: EmployeesMeConfig;
    //能否查看调拨药品价格
    get canSeePrice(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.inventory?.isCanSeeTransGoodsPrice;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }

    get transList(): InventoryTransDetail[] {
        return this.detail?.rows ?? [];
    }

    get hasMore(): boolean {
        if (!this.lastSearchDetail) return true;
        return this.transList.length < (this.detail?.count ?? 0) && (this.lastSearchDetail.rows?.length ?? 0) == this.params.limit;
    }

    get filterHasChange(): boolean {
        return _.isEqual(this.params, this._params);
    }
}

export class InventoryTransListPageBloc extends Bloc<_Event, State> {
    private _loadDataTrigger: Subject<number> = new Subject<number>();
    _transferModeTrigger: Subject<number> = new Subject<number>();

    static fromContext(context: InventoryTransListPageBloc): InventoryTransListPageBloc {
        return context;
    }

    constructor(options?: { type?: InventoryTransMode }) {
        super();

        this.innerState.transferMode = options?.type ?? InventoryTransMode.interStoreTransfer;
        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventAddTransOrder, this._mapEventAddTransOrder);
        map.set(_EventReload, this._mapEventReload);
        map.set(_EventLoadMore, this._mapEventLoadMore);
        map.set(_EventSearchMedicine, this._mapEventSearchMedicine);
        map.set(_EventChangeFilter, this._mapEventChangeFilter);
        map.set(_EventChangeTransferMode, this._mapEventChangeTransferMode);
        map.set(_EventDeleteInventoryTransDraft, this._mapEventDeleteInventoryTransDraft);

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private formatChainAdminName(list: GetInventoryTransListRsp): GetInventoryTransListRsp {
        list.rows = list.rows?.map((item) => {
            if (userCenter.clinic && !userCenter.clinic.isNormalClinic && !_.isEmpty(userCenter.clinic.chainId)) {
                if (item.fromOrgan && !_.isEmpty(item.fromOrgan?.id) && userCenter.clinic.chainId == item.fromOrgan.id) {
                    item.fromOrgan.shortName = "总部";
                } else if (item.toOrgan && !_.isEmpty(item.toOrgan?.id) && userCenter.clinic.chainId == item.toOrgan.id) {
                    item.toOrgan.shortName = "总部";
                }
            }
            return item;
        });
        return list;
    }

    private resetLoadInfo(): void {
        this.innerState.startLoading();
        this.update();
        this.innerState.detail = undefined;
        this.innerState.lastSearchDetail = undefined;
        this.innerState.draftList = [];
    }

    private _includeDraft(): boolean {
        return this.innerState.filterHasChange;
    }

    private async _initPageConfig(): Promise<void> {
        //获取登录人员权限配置
        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig(true).catchIgnore();
        this.innerState.chainClinicsList = await ClinicAgent.getChainClinics().catchIgnore();
        this.innerState.pharmacyInfoConfig = await userCenter.getInventoryChainConfig(false).catchIgnore();
        this.innerState.currentPharmacy = InventoryAgent.getCurrentPharmacy();
        const pharmacyConfig = await GoodsAgent.getPharmacyList().catchIgnore();
        this.innerState.pharmacyList = pharmacyConfig?.rows;
        //单店只有店内调拨，没有店间调拨
        if (userCenter.clinic?.isNormalClinic) {
            this.innerState.transferModeList = this.innerState.transferModeList?.filter((t) => t.id == InventoryTransMode.inStoreTransfer);
        }
    }
    private _initPageTrigger(): void {
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    const innerState = this.innerState,
                        offset = this.innerState.transList.length - this.innerState.draftList.length;
                    const { goodsName, ...others } = this.innerState.params;
                    ignore(goodsName);
                    if (!innerState.lastSearchDetail && this._includeDraft()) {
                        return Promise.all([
                            InventoryDraftManage.clinicInstance.getAllDrafts(),
                            InventoryTransAgent.getInventoryTransDraftList(),
                        ])
                            .then((allRsp) => {
                                const [rsp, onlineDraft] = allRsp;
                                this.innerState.localDraft =
                                    __filterRelativePharmacyData(rsp, this.innerState.currentPharmacy)
                                        ?.map((item) => item?.toInventoryTransListRows())
                                        ?.filter((t) => t?.transType == this.innerState.transferMode) ?? [];
                                const _result = new GetInventoryTransListRsp();
                                _result.rows = [];
                                //追加线上草稿
                                _result.rows = _result.rows
                                    ?.concat(
                                        __filterRelativePharmacyData(onlineDraft?.rows, this.innerState.currentPharmacy)?.map((item) =>
                                            item?.toInventoryTransListRows()
                                        ) ?? []
                                    )
                                    ?.filter((t) => t?.transType == this.innerState.transferMode);
                                _result.__fromDraft = true;
                                this.innerState.draftList = _result.rows?.sort((a, b) => {
                                    if ((a?.lastModifiedDate?.getTime() ?? 0) < (b?.lastModifiedDate?.getTime() ?? 0)) {
                                        return 1;
                                    } else if ((a?.lastModifiedDate?.getTime() ?? 0) > (b?.lastModifiedDate?.getTime() ?? 0)) {
                                        return -1;
                                    } else {
                                        return 0;
                                    }
                                });
                                return _result;
                            })
                            .then((rsp) => this.formatChainAdminName(rsp));
                    }
                    return InventoryTransAgent.getInventoryTransList({
                        offset,
                        ...others,
                        pharmacyNo: this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy
                            ? this.innerState.currentPharmacy?.no
                            : undefined,
                        pharmacyType: this.innerState.currentPharmacy?.type,
                        transType: this.innerState.transferMode,
                        transInPharmacyNo: this.innerState.params.transInPharmacyNo,
                        transOutPharmacyNo: this.innerState.params.transOutPharmacyNo,
                    })
                        .then((rsp) => this.formatChainAdminName(rsp))
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.stopLoading();
                if (rsp instanceof ABCError) {
                    this.innerState.setLoadingError(rsp);
                } else {
                    this.innerState.lastSearchDetail = rsp;
                    if (rsp.__fromDraft) {
                        this.innerState.detail = rsp;
                        this._loadDataTrigger.next();
                    } else if (!rsp.__fromDraft) {
                        this.innerState.detail = JsonMapper.deserialize(GetInventoryTransListRsp, {
                            ...rsp,
                            rows: (this.innerState.detail?.rows ?? [])?.concat(rsp.rows ?? []),
                        });
                    }
                }
                this.update();
            })
            .addToDisposableBag(this);
        this.addDisposable(this._loadDataTrigger);
        this._loadDataTrigger.next();

        InventoryDraftManage.clinicInstance.draftObserver
            .pipe(
                switchMap((localdraft) => {
                    return Promise.all([
                        InventoryDraftManage.clinicInstance.getAllDrafts(),
                        !!localdraft ? undefined : InventoryTransAgent.getInventoryTransDraftList(),
                    ])
                        .then((allRsp) => {
                            const [rsp, onlineDraft] = allRsp;
                            this.innerState.localDraft =
                                __filterRelativePharmacyData(rsp, this.innerState.currentPharmacy)
                                    ?.map((item) => item?.toInventoryTransListRows())
                                    ?.filter((t) => t?.transType == this.innerState.transferMode) ?? [];
                            const _result = new GetInventoryTransListRsp();
                            _result.rows = [];
                            //追加线上草稿
                            _result.rows = _result.rows
                                ?.concat(
                                    __filterRelativePharmacyData(onlineDraft?.rows, this.innerState.currentPharmacy)?.map((item) =>
                                        item?.toInventoryTransListRows()
                                    ) ?? []
                                )
                                ?.filter((t) => t?.transType == this.innerState.transferMode);
                            _result.__fromDraft = true;
                            this.innerState.draftList = _result.rows?.sort((a, b) => {
                                if ((a?.lastModifiedDate?.getTime() ?? 0) < (b?.lastModifiedDate?.getTime() ?? 0)) {
                                    return 1;
                                } else if ((a?.lastModifiedDate?.getTime() ?? 0) > (b?.lastModifiedDate?.getTime() ?? 0)) {
                                    return -1;
                                } else {
                                    return 0;
                                }
                            });
                            return _result;
                        })
                        .then((rsp) => this.formatChainAdminName(rsp));
                })
            )
            .subscribe((rsp) => {
                this.innerState.stopLoading();
                if (rsp instanceof ABCError) {
                    this.innerState.setLoadingError(rsp);
                } else {
                    let arr = this.innerState.detail?.rows ?? [];
                    _.remove(arr, (item) => item.__draftId || item.isOnlineDraft);
                    arr = (rsp.rows ?? []).concat(arr ?? []);
                    this.innerState.draftList = rsp.rows ?? [];

                    this.innerState.detail = JsonMapper.deserialize(GetInventoryTransListRsp, {
                        ...this.innerState.detail,
                        rows: arr,
                    });
                }
                this.update();
            })
            .addToDisposableBag(this);

        InventoryTransAgent.InventoryTransPublisher.subscribe(() => {
            this.dispatch(new _EventReload());
        }).addToDisposableBag(this);
    }
    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        await this._initPageConfig();
        this._initPageTrigger();
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventAddTransOrder(/*event: _EventAddTransOrder*/): AsyncGenerator<State> {
        const { pharmacyInfoConfig, transferModeList, pharmacyList, transferMode, currentPharmacy, localDifferenceDraftList } =
                this.innerState,
            isOpenMultiplePharmacy = pharmacyInfoConfig?.isOpenMultiplePharmacy;
        //是否是店内调拨
        const inStoreTransfer = transferMode == InventoryTransMode.inStoreTransfer;
        const canSelectPharmacyList = pharmacyInfoConfig?.inventoryPharmacyList()?.filter((t) => t?.status == 1 && !!t?.enableTrans);
        //调入、调出库房中必须有一方指定本库房，任意一方已选择其他库房，另一方库房默认为本库房，切换时也仅有本库房选项
        const inventoryOutTypeList: any[] =
            transferMode == InventoryTransMode.interStoreTransfer
                ? [
                      {
                          title: "选择调拨类型",
                          type: DirectoryType.TypeOfAllocation,
                          list: [
                              {
                                  id: InventoryTransType.transTo,
                                  name: "从本店调出",
                              },
                              {
                                  id: InventoryTransType.transFrom,
                                  name: "调入本店",
                              },
                          ],
                          layout: 3,
                          crossAxisSpacing: Sizes.dp8,
                      },
                  ]
                : [];
        if (isOpenMultiplePharmacy) {
            inventoryOutTypeList.unshift({
                title: "选择调拨方式",
                type: DirectoryType.ModeOfAllocation,
                list: transferModeList,
                layout: 3,
                crossAxisSpacing: Sizes.dp8,
            });
            inventoryOutTypeList.push({
                title: inStoreTransfer ? "确认调出库房" : "调入/调出库房",
                type: DirectoryType.OutletPharmacy,
                list: canSelectPharmacyList,
                layout: 3,
                crossAxisSpacing: Sizes.dp8,
                isShow: inStoreTransfer,
                mustSelectInfo: inStoreTransfer && !_.isUndefined(currentPharmacy?.no) ? currentPharmacy : undefined,
            });
            if (inStoreTransfer) {
                inventoryOutTypeList.push({
                    title: "确认调入库房",
                    type: DirectoryType.TransferPharmacy,
                    list: canSelectPharmacyList,
                    layout: 3,
                    crossAxisSpacing: Sizes.dp8,
                    isShow: true,
                    mustSelectInfo: !_.isUndefined(currentPharmacy?.no) ? currentPharmacy : undefined,
                });
            }
        }

        const result = await showBottomPanel<InventoryDirectoryDialogState>(
            <InventoryDirectoryDialog
                dialogList={inventoryOutTypeList}
                dialogTitle={"选择调拨类型"}
                allocationMode={this.innerState.transferMode}
                pharmacyNo={!inStoreTransfer ? this.innerState.currentPharmacy?.no : undefined}
            />,
            {
                topMaskHeight: pxToDp(200),
            }
        );
        // const result = await ChooseTransTypeView.show();
        if (_.isUndefined(result)) return;
        const selectPharmacyInfo = pharmacyList?.find((t) => t.no == result?.pharmacyNo);
        //调入药房
        const transferPharmacyInfo = pharmacyList?.find((t) => t.no == result?.transferPharmacyNo);
        let hasWaitDraft = !!localDifferenceDraftList.length;

        //新增草稿时判断是否有可恢复草稿
        if (hasWaitDraft) {
            const result = await showQueryDialog("提示", "当前存在未处理草稿，是否恢复？");
            hasWaitDraft = result == DialogIndex.positive;
        }
        if (result?.allocationType == InventoryTransType.transFrom) {
            //调入
            ABCNavigator.navigateToPage(
                <InventoryTransDetailInvoice
                    type={InventoryTransType.transFrom}
                    pharmacy={selectPharmacyInfo}
                    transType={result.allocationMode}
                    draftId={hasWaitDraft ? localDifferenceDraftList[0]?.__draftId : undefined}
                />
            );
        } else if (result?.allocationType == InventoryTransType.transTo) {
            //调出
            ABCNavigator.navigateToPage(
                <InventoryTransDetailInvoice
                    type={InventoryTransType.transTo}
                    pharmacy={selectPharmacyInfo}
                    transType={result.allocationMode}
                    draftId={hasWaitDraft ? localDifferenceDraftList[0]?.__draftId : undefined}
                />
            );
        }
        if (result?.allocationMode == InventoryTransMode.inStoreTransfer) {
            //店内调拨
            ABCNavigator.navigateToPage(
                <InventoryTransDetailInvoice
                    pharmacy={selectPharmacyInfo}
                    transType={result.allocationMode}
                    transferPharmacy={transferPharmacyInfo}
                    draftId={hasWaitDraft ? localDifferenceDraftList[0]?.__draftId : undefined}
                />
            );
        }
    }

    private async *_mapEventReload(/*event: _EventReload*/): AsyncGenerator<State> {
        if (this.innerState.loading) return;
        this.resetLoadInfo();
        this._loadDataTrigger.next();
    }

    private async *_mapEventLoadMore(/*event: _EventLoadMore*/): AsyncGenerator<State> {
        if (this.innerState.loading || !this.innerState.hasMore) return;
        this.innerState.startLoading();
        this.update();
        this._loadDataTrigger.next();
    }

    private async *_mapEventSearchMedicine(/*event: _EventSearchMedicine*/): AsyncGenerator<State> {
        const result = await ABCNavigator.navigateToPage<GoodsInfo>(
            <InventoryMedicineSearchPage placeholder={"请输入药品名称/条形码"} callback={(goodsInfo) => ABCNavigator.pop(goodsInfo)} />
        );
        if (result) {
            this.resetLoadInfo();
            this.innerState.params.withGoodsId = result.id;
            this.innerState.params.goodsName = result.displayName;
            this._loadDataTrigger.next();
        }
    }

    private async *_mapEventChangeFilter(event: _EventChangeFilter): AsyncGenerator<State> {
        const timeF = event.filters.filters?.[0]?.filters?.[0];
        const transInF = event.filters.filters?.[1]?.filters?.[0];
        const transOutF = event.filters.filters?.[2]?.filters?.[0];
        const timeRange = timeF?.timeRange ?? (timeF?.date ? new Range<Date>(timeF?.date, timeF?.date) : undefined);
        const interStoreTransfer = this.innerState.transferMode == InventoryTransMode.interStoreTransfer;
        if (timeRange) {
            this.innerState.params.begDate = timeRange.start;
            this.innerState.params.endDate = timeRange.end;
        } else {
            this.innerState.params.begDate = undefined;
            this.innerState.params.endDate = undefined;
        }
        if (transOutF) {
            this.innerState.params.toOrganId = interStoreTransfer ? transOutF.info.id : undefined;
            this.innerState.params.transInPharmacyNo = !interStoreTransfer ? transOutF.info?.no : undefined;
        }

        if (transInF) {
            this.innerState.params.fromOrganId = interStoreTransfer ? transInF.info.id : undefined;
            this.innerState.params.transOutPharmacyNo = !interStoreTransfer ? transInF.info?.no : undefined;
        }
        this.innerState.startLoading();
        this.resetLoadInfo();
        this._loadDataTrigger.next();
    }

    private async *_mapEventChangeTransferMode(): AsyncGenerator<State> {
        const innerState = this.innerState;
        const initIndex = innerState.transferModeList?.findIndex((item) => item.id == innerState.transferMode) ?? -1;
        const result = await showOptionsBottomSheet({
            title: "选择调拨",
            options: innerState.transferModeList?.map((item) => item.name),
            initialSelectIndexes: new Set<number>([initIndex]),
        });
        if (_.isUndefined(result)) return;
        this.innerState.transferMode = innerState.transferModeList[result[0]]?.id;
        this._transferModeTrigger.next(this.innerState.transferMode);
        this.resetLoadInfo();
        this._loadDataTrigger.next();
        this.update();
    }

    private async *_mapEventDeleteInventoryTransDraft(event: _EventDeleteInventoryTransDraft): AsyncGenerator<State> {
        if (!event?.detail?.__draftId) return;
        const result = await showQueryDialog("", "是否删除该调拨单");
        if (result == DialogIndex.positive) {
            const inventoryTransDraftManage = InventoryDraftManage.clinicInstance;
            if (event.detail.status == InventoryOutStatus.onlineDraft) {
                // 删除线上数据
                InventoryTransAgent.deleteInventoryTransDraft(event.detail.id ?? "")
                    .then((rsp) => {
                        if (rsp?.code == 200) {
                            inventoryTransDraftManage.draftObserver.next();
                        }
                    })
                    .catch((error) => {
                        if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.deleted) {
                            showConfirmDialog("提示", error?.detail?.error?.message, "知道了");
                            inventoryTransDraftManage.draftObserver.next();
                        }
                    });
            } else {
                inventoryTransDraftManage.removeDraft(event.detail.__draftId);
            }
        }
    }

    @runFuncBeforeCheckExpired()
    public requestAddTransOrder(): void {
        this.dispatch(new _EventAddTransOrder());
    }

    public requestReload(): void {
        this.dispatch(new _EventReload());
    }

    public requestLoadMore(): void {
        this.dispatch(new _EventLoadMore());
    }

    public requestSearchMedicine(text: string): void {
        if (!text?.length || !this.innerState.params.withGoodsId) {
            this.dispatch(new _EventSearchMedicine());
        } else {
            delete this.innerState.params.withGoodsId;
            delete this.innerState.params.goodsName;
            this.dispatch(new _EventReload());
        }
    }

    public requestChangeFilter(filters: Filters): void {
        this.dispatch(new _EventChangeFilter(filters));
    }

    requestChangeTransferMode(): void {
        this.dispatch(new _EventChangeTransferMode());
    }

    //删除草稿
    requestDeleteInventoryTransDraft(detail?: InventoryTransDetail): void {
        this.dispatch(new _EventDeleteInventoryTransDraft(detail));
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventAddTransOrder extends _Event {}

class _EventReload extends _Event {}

class _EventLoadMore extends _Event {}

class _EventSearchMedicine extends _Event {}

class _EventChangeFilter extends _Event {
    filters: Filters;

    constructor(filters: Filters) {
        super();
        this.filters = filters;
    }
}

class _EventChangeTransferMode extends _Event {}

class _EventDeleteInventoryTransDraft extends _Event {
    detail?: InventoryTransDetail;
    constructor(detail?: InventoryTransDetail) {
        super();
        this.detail = detail;
    }
}
