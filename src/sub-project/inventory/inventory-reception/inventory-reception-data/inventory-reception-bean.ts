/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2023/5/17
 * @Copyright 成都字节流科技有限公司© 2023
 */
import { fromJsonToDate, JsonMapper, JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";
import { GoodsBatchInfo, InventoryPharmacyInfo } from "../../data/inventory-bean";
import {
    InventoryOutOrgan,
    InventoryOutSheetComment,
    InventoryOutSheetLogs,
    InventoryOutToOrgan,
    PickingDoctors,
} from "../../inventory-out/inventory-out-data/inventory-out-bean";
import { GoodsInfo } from "../../../base-business/data/beans";

export enum InventoryReceptionOrderType {
    all = -1, //全部
    receiveIn = 20, // 20: "领用入库",
    receiveOut = 1, // 1: "领用出库",
    receiveReIn = 10, //领用退回出库
    receiveReOut = 30, //领用退回入库
    FIX_RECEPTION_OUT_ORDER = 60, // 修正领用出库单
    FIX_RECEPTION_BACK_OUT_ORDER = 61, // 修正领用退出单
    FIX_RECEPTION_IN_ORDER = 62, // 修正领用入库单
    FIX_RECEPTION_BACK_IN_ORDER = 63, // 修正领用退入单
}

export enum InventoryReceptionOrderStatus {
    draft = -10, //草稿状态
    waitOutConfirm = 0, // 0: "领用出库方确认",
    waitInConfirmed = 1, // 1: "领用入库方确认",
    completed = 2, // 2: "已完成",
    waitAudit = 5, // 5: "待审核",
    refused = 9, // 9: "已拒绝",
    revoke = 31, //31: "撤回",
    refund = 40, // 40: "退完"
    onlineDraft = -30, //线上草稿
}

export enum InventoryReceptionEnterType {
    manual = 1, //手动
    onlineDraft = 2, //线上草稿
}

export class ReceiveDepartment {
    chainId?: string; // 连锁id
    clinicId?: string; // 门店id
    id?: string; // 部门id
    name?: string; // 部门名称
    type?: number; // 部门类型
    isDefault?: number; // 是否默认
    tagId?: string; // 标签id
}

export class StockIoBatchDetailList {
    batchId?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    expiryDate?: Date;
    packageCostPrice?: number;
    packageCount?: number;
    pieceCount?: number;
    pieceNum?: number;
    batchNo?: string;
    //以下字段自定义为业务使用
    selected?: boolean;
    id?: string;
    returnLeftPackageCount?: number; //可退数量 大单位
    returnLeftPieceCount?: number; //可退数量 小单位
    @JsonProperty({ fromJson: fromJsonToDate })
    inDate?: Date; //入库日期
    totalCostPrice?: number; //总成本价
}

export class InventoryReceptionOrderDetailBatch {
    id?: string; //单据id
    goodsId?: string; //单据id
    batchId?: string; // 批次号
    batchNo?: string; // 批号
    receptSourceId?: string; //如果是退回，对应入领用退回的单据ITEM的id
    packageCount?: number; // 包装数量
    pieceCount?: number; // 件装数量
    applicationPackageCount?: number; // 申请包装数量
    applicationPieceCount?: number; // 申请件装数量
    packageCostPrice?: number; // 包装成本价
    totalCostPrice?: number; // 总成本价
    returnLeftPackageCount?: number; //可退数量 大单位
    returnLeftPieceCount?: number; //可退数量 小单位
    returnPackageCount?: number; //已退数量 大单位
    returnPieceCount?: number; //已退数量 小单位
    @JsonProperty({ fromJson: fromJsonToDate })
    expiryDate?: Date; // 有效期
    parentId?: string; // 父级id
    selected?: boolean; //是否选中
    stockPieceCount?: number;
    stockPackageCount?: number;
}
export class OutGoodsStock {
    outPackageCount?: number;
    outPieceCount?: number;
    packageCount?: number;
    pieceCount?: number;
    stockPackageCount?: number;
    stockPieceCount?: number;
    dispGoodsCount?: string; //自定义，用于直接显示
}

export class InventoryReceptionOrderDetailList {
    goodsId?: string; // 商品id
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModified?: Date; // 最后修改时间
    @JsonProperty({ type: Array, clazz: InventoryReceptionOrderDetailBatch })
    batchs?: InventoryReceptionOrderDetailBatch[]; // 批次列表
    @JsonProperty({ type: GoodsInfo })
    goods?: GoodsInfo; // 商品信息
    packageCount?: number; // 包装数量
    pieceCount?: number; // 件装数量
    applicationPackageCount?: number; // 申请包装数量
    applicationPieceCount?: number; // 申请件装数量
    packageCostPrice?: number; // 包装成本价
    totalCostPrice?: number; // 总成本价

    //------------------业务上使用------------------//
    packagePrice?: number;
    piecePrice?: number;
    pieceNum?: number;
    batchId?: string;
    batchNo?: string;
    __batchInfo?: GoodsBatchInfo;
    @JsonProperty({ type: Array, clazz: StockIoBatchDetailList })
    stockIoBatchDetailList?: StockIoBatchDetailList[]; //利用后台实际发生的进销存列表，数量已经是可以退单据上的数量(没和批次的实际库存取min)
    @JsonProperty({ clazz: OutGoodsStock })
    outGoodsStock?: OutGoodsStock; //出库方的库存
    @JsonProperty({ clazz: OutGoodsStock })
    inGoodsStock?: OutGoodsStock; //入库方的库存

    //出库方可出数量和锁库（用于出库确认时判断，是否超出可出数量）自定义
    _stockCountAndLock?: {
        lockingPackageCount?: number;
        lockingPieceCount?: number;
        outPackageCount?: number;
        outPieceCount?: number;
    };

    get outCountWithPackage(): number {
        return this.packageCount || 0;
    }

    compareKey(): string {
        return `${this.goods?.id}${this.batchId}`;
    }

    get receptionOutDisplay(): string {
        let str = "";
        const totalApplicationPackageCount = this.batchs?.reduce((pre, cur) => pre + (cur?.applicationPackageCount ?? 0), 0),
            totalApplicationPieceCount = this.batchs?.reduce((pre, cur) => pre + (cur?.applicationPieceCount ?? 0), 0);
        if (!this.goods?.packageUnit) {
            str +=
                (totalApplicationPackageCount ?? 0) * (this.goods?.pieceNum ?? 0) +
                (totalApplicationPieceCount ?? 0) +
                (this.goods?.pieceUnit ?? "");
        } else {
            if (!!totalApplicationPackageCount) {
                str += `${totalApplicationPackageCount}${this.goods?.packageUnit}`;
            }
            if (!!this.goods?.pieceUnit && !!totalApplicationPieceCount) {
                str += `${totalApplicationPieceCount}${this.goods?.pieceUnit}`;
            }
            //大小单位都没有，显示0
            if (!totalApplicationPackageCount && !totalApplicationPieceCount) {
                str = `0${this.goods?.packageUnit}`;
            }
        }

        return str;
    }

    //可退数量
    get returnLeftDisplay(): string {
        let str = "";
        const totalReturnPackageCount = this.batchs?.reduce((pre, cur) => pre + (cur?.returnLeftPackageCount ?? 0), 0),
            totalReturnPieceCount = this.batchs?.reduce((pre, cur) => pre + (cur?.returnLeftPieceCount ?? 0), 0);
        if (!this.goods?.packageUnit) {
            str +=
                (totalReturnPackageCount ?? 0) * (this.goods?.pieceNum ?? 0) + (totalReturnPieceCount ?? 0) + (this.goods?.pieceUnit ?? "");
        } else {
            if (!!totalReturnPackageCount) {
                str += `${totalReturnPackageCount}${this.goods?.packageUnit}`;
            }
            if (!!this.goods?.pieceUnit && !!totalReturnPieceCount) {
                str += `${totalReturnPieceCount}${this.goods?.pieceUnit}`;
            }
            //大小单位都没有，显示0
            if (!totalReturnPackageCount && !totalReturnPieceCount) {
                str = `0${this.goods?.packageUnit}`;
            }
        }

        return str;
    }

    //填写领出数量
    get outCountDisplay(): string {
        let str = "";
        const totalPackageCount = this.batchs?.reduce((pre, cur) => pre + (cur?.packageCount ?? 0), 0),
            totalPieceCount = this.batchs?.reduce((pre, cur) => pre + (cur?.pieceCount ?? 0), 0);
        const pieceNum = this.goods?.pieceNum ?? 0;
        if (!this.goods?.packageUnit) {
            str += (totalPackageCount ?? 0) * pieceNum + (totalPieceCount ?? 0) + (this.goods?.pieceUnit ?? "");
        } else {
            if (!!totalPackageCount) {
                str += `${totalPackageCount}${this.goods?.packageUnit}`;
            }
            if (!!this.goods?.pieceUnit && !!totalPieceCount) {
                str += `${totalPieceCount}${this.goods?.pieceUnit}`;
            }
            //大小单位都没有，显示0
            if (!totalPackageCount && !totalPieceCount) {
                str = `0${this.goods?.packageUnit}`;
            }
        }

        return str;
    }

    //请领数量
    get applicationCountDisplay(): string {
        const batchs = this.batchs?.[0];
        let str = "";
        if (!this.goods?.packageUnit) {
            str +=
                (batchs?.applicationPackageCount ?? 0) * (this.goods?.pieceNum ?? 0) +
                (batchs?.applicationPieceCount ?? 0) +
                (this.goods?.pieceUnit ?? "");
        } else {
            if (!!batchs?.applicationPackageCount) {
                str += `${batchs?.applicationPackageCount}${this.goods?.packageUnit}`;
            }
            if (!!this.goods?.pieceUnit && !!batchs?.applicationPieceCount) {
                str += `${batchs?.applicationPieceCount}${this.goods?.pieceUnit}`;
            }
            //大小单位都没有，显示0
            if (!batchs?.applicationPackageCount && !batchs?.applicationPieceCount) {
                str = `0${this.goods?.packageUnit}`;
            }
        }

        return str;
    }

    //领入方库存
    get receiveInStockDisplay(): string {
        if (!!this.inGoodsStock?.dispGoodsCount) return this.inGoodsStock?.dispGoodsCount;
        let str = "";
        if (!this.goods?.packageUnit) {
            const ConversionToPiece =
                (this.inGoodsStock?.packageCount ?? 0) * (this.goods?.pieceNum ?? 0) + (this.inGoodsStock?.pieceCount ?? 0);
            str += `${ConversionToPiece ?? 0}${this.goods?.pieceUnit}`;
        } else {
            if (!!this.inGoodsStock?.packageCount) {
                str += `${this.inGoodsStock?.packageCount}${this.goods?.packageUnit}`;
            }
            if (this.goods?.pieceUnit && !!this.inGoodsStock?.pieceCount) {
                str += `${this.inGoodsStock?.pieceCount}${this.goods?.pieceUnit}`;
            }
            //大小单位都没有，显示0
            if (!this.inGoodsStock?.packageCount && !this.inGoodsStock?.pieceCount) {
                str = `0${this.goods?.packageUnit}`;
            }
        }
        return str;
    }

    //领出方库存
    get receiveOutStockDisplay(): string {
        if (!!this.outGoodsStock?.dispGoodsCount) return this.outGoodsStock?.dispGoodsCount;
        let str = "";
        if (!this.goods?.packageUnit) {
            const ConversionToPiece =
                (this.outGoodsStock?.packageCount ?? 0) * (this.goods?.pieceNum ?? 0) + (this.outGoodsStock?.pieceCount ?? 0);
            str += `${ConversionToPiece ?? 0}${this.goods?.pieceUnit}`;
        } else {
            if (!!this.outGoodsStock?.packageCount) {
                str += `${this.outGoodsStock?.packageCount}${this.goods?.packageUnit}`;
            }
            if (this.goods?.pieceUnit && !!this.outGoodsStock?.pieceCount) {
                str += `${this.outGoodsStock?.pieceCount}${this.goods?.pieceUnit}`;
            }
            //大小单位都没有，显示0
            if (!this.outGoodsStock?.packageCount && !this.outGoodsStock?.pieceCount) {
                str = `0${this.goods?.packageUnit}`;
            }
        }
        return str;
    }

    //领用退回-实际退回数量
    get returnCountDisplay(): string {
        let str = "";
        const totalPackageCount = this.batchs?.reduce((pre, cur) => {
                const { packageCount = 0, pieceCount = 0, returnLeftPackageCount = 0, returnLeftPieceCount = 0 } = cur;
                if (
                    packageCount * (this.goods?.pieceNum ?? 0) + pieceCount >
                    returnLeftPackageCount * (this.goods?.pieceNum ?? 0) + returnLeftPieceCount
                ) {
                    return pre + returnLeftPackageCount;
                } else {
                    return pre + packageCount;
                }
            }, 0),
            totalPieceCount = this.batchs?.reduce((pre, cur) => {
                const { packageCount = 0, pieceCount = 0, returnLeftPackageCount = 0, returnLeftPieceCount = 0 } = cur;
                if (
                    packageCount * (this.goods?.pieceNum ?? 0) + pieceCount >
                    returnLeftPackageCount * (this.goods?.pieceNum ?? 0) + returnLeftPieceCount
                ) {
                    return pre + returnLeftPieceCount;
                } else {
                    return pre + pieceCount;
                }
            }, 0);
        const pieceNum = this.goods?.pieceNum ?? 0;
        if (!this.goods?.packageUnit) {
            str += ((totalPackageCount ?? 0) * pieceNum + (totalPieceCount ?? 0)).toFixed(2).toString() + `${this.goods?.pieceUnit ?? ""}`;
        } else {
            if (!!totalPackageCount) {
                str += `${totalPackageCount.toFixed(2).toString()}${this.goods?.packageUnit}`;
            }
            if (!!this.goods?.pieceUnit && !!totalPieceCount) {
                str += `${totalPieceCount.toFixed(2).toString()}${this.goods?.pieceUnit}`;
            }
            //大小单位都没有，显示0
            if (!totalPackageCount && !totalPieceCount) {
                str = `0${this.goods?.packageUnit}`;
            }
        }

        return str;
    }
}

export class InventoryReceptionOrderListItem {
    id?: string; // 出库单id
    type?: InventoryReceptionOrderType; // 领用单类型
    get disableAmendmentOrder(): boolean {
        return (
            this.type == InventoryReceptionOrderType.FIX_RECEPTION_OUT_ORDER ||
            this.type == InventoryReceptionOrderType.FIX_RECEPTION_BACK_OUT_ORDER ||
            this.type == InventoryReceptionOrderType.FIX_RECEPTION_IN_ORDER ||
            this.type == InventoryReceptionOrderType.FIX_RECEPTION_BACK_IN_ORDER
        );
    }
    orderNo?: string; // 出库单号 CK2019122500011
    status?: InventoryReceptionOrderStatus; // 出库单状态
    statusName?: string; // 出库单状态

    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date; // 创建日期
    createdBy?: InventoryOutToOrgan; // 创建人

    inConfirmed?: Date; // 盘点单的审核时间
    inConfirmBy?: InventoryOutToOrgan; // 调入审核人

    outConfirmed?: Date; // 出库单的审核时间
    outConfirmBy?: InventoryOutToOrgan; // 调出审核人

    reviewBy?: InventoryOutToOrgan; // 审核人
    @JsonProperty({ fromJson: fromJsonToDate })
    reviewed?: Date; // 审核时间

    inPharmacy?: InventoryPharmacyInfo; // 调入药房
    outPharmacy?: InventoryPharmacyInfo; // 调出药房

    receiveDepartment?: ReceiveDepartment; //领用科室
    receiveDepartmentId?: string; //领用科室ID

    receptSourceOrderId?: string; // 领用退回出库/领用退回入库 指定要退回的领用单ID，需要原领处方确认
    receptSourceOrder?: InventoryReceptionOrderListItem; //原领用单

    toUserId?: string; // 领用人id
    toUser?: {
        id?: string; // 领用人id
        name?: string; // 领用人姓名
    };

    amount?: number; //金额
    kindCount?: number; // 品种数量
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModified?: Date; // 最后修改时间

    @JsonProperty({ type: Array, clazz: InventoryReceptionOrderDetailList })
    list?: InventoryReceptionOrderDetailList[]; // 调拨单列表
    @JsonProperty({ type: Array, clazz: InventoryOutSheetLogs })
    logs?: InventoryOutSheetLogs[]; // 调拨单日志
    organ?: InventoryOutOrgan; // 调出药房

    __draftId?: string; // 草稿id

    get orderTypeDisplay(): string {
        switch (this.type) {
            case InventoryReceptionOrderType.receiveIn:
                return "领用入库";
            case InventoryReceptionOrderType.receiveOut:
                return "领用出库";
            case InventoryReceptionOrderType.receiveReIn:
                return "领用退回入库";
            case InventoryReceptionOrderType.receiveReOut:
                return "领用退回出库";
            default:
                return "";
        }
    }

    get orderReceptionFlowDisplay(): string {
        let str = "";
        const isReceiveIn =
            (this.type ?? 0) == InventoryReceptionOrderType.receiveIn || (this.type ?? 0) == InventoryReceptionOrderType.receiveOut;
        str += `${isReceiveIn ? "领用" : "退回"}  `;
        if (!!this.outPharmacy) {
            str += this.outPharmacy.name;
        }
        if (!!this.inPharmacy) {
            str += "→";
            str += this.inPharmacy.name;
        }

        return str;
    }

    /**
     * 当前单据关注的门店
     */
    get currentPharmacy(): InventoryPharmacyInfo | undefined {
        if (this.type === InventoryReceptionOrderType.receiveIn) {
            return this.inPharmacy;
        } else if (this.type === InventoryReceptionOrderType.receiveOut) {
            return this.outPharmacy;
        }
    }

    get isOnlineDraft(): boolean {
        return this.status == InventoryReceptionOrderStatus.onlineDraft;
    }
}

export class InventoryReceptionOrderDetail extends InventoryReceptionOrderListItem {
    count?: number;
    @JsonProperty({ type: Array, clazz: InventoryOutSheetComment })
    comment?: InventoryOutSheetComment[];
}
export class ReceptionPickingDoctors {
    departmentId?: string; // 科室 ID
    departmentName?: string; // 科室名称
    isDefault?: number; //
    employeeId?: string; // 领用人 ID
    employeeName?: string; // 领用人名称

    get sourceDisplay(): string {
        if (!this.departmentId?.length) return "";
        let display = `${this.employeeName}`;
        if (this.departmentName) {
            display = ` ${this.employeeName} - ${display}`;
        }
        return display;
    }
}
export class ReceptionPickingDoctorsWithDepartment extends ReceptionPickingDoctors {
    @JsonProperty({ type: Array, clazz: ReceptionPickingDoctors })
    doctors?: ReceptionPickingDoctors[];
}

export class BatchInfoItem {
    batchId?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    expiryDate?: Date;
    packageCostPrice?: number;
    packageCount?: number;
    pieceCount?: number;
    pieceNum?: number;
    batchNo?: string;
    selected?: boolean;
    _packageCount?: number; // 用于记录原始值
    _pieceCount?: number; // 用于记录原始值
    packageUnit?: string;
    pieceUnit?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    inDate?: Date;
    totalCostPrice?: number;
}

class ReceptionRevokeBatch extends BatchInfoItem {
    parentId?: string;
    receptSourceId?: string; //申请单的itemId，是由申请单的哪个itemId指定的批次，领用出库房确认时填写
}
class ReceptionRevokeList {
    goodsId?: string;
    @JsonProperty({ type: Array, clazz: ReceptionRevokeBatch })
    batchs?: ReceptionRevokeBatch[];
}
export class ReceptionRevokeReq {
    comment?: string;
    lastModified?: Date;
    @JsonProperty({ type: Array, clazz: ReceptionRevokeList })
    list?: ReceptionRevokeList[];
    pass?: number;
}

export class DepartmentUserInfo {
    departmentId?: string; // 科室 ID
    departmentName?: string; // 科室名称
    isDefault?: number; //
    employeeId?: string; // 领用人 ID
    employeeName?: string; // 领用人名称
    children?: PickingDoctors[];
}
export class ReceiveDepartmentUserParams {
    departmentIds?: string[]; //科室id集合
    employeeIds?: string[]; //领用人id集合
}
export class ReceiveDepartmentUser {
    receiveDepartmentId?: string;
    toUserId?: string;
    _departmentUserStr?: string;
}

export class InventoryReceptionDraft {
    status?: number;
    @JsonProperty({ type: Array, clazz: InventoryReceptionOrderDetailList })
    list?: InventoryReceptionOrderDetailList[];
    orderNo?: string;
    kindCount?: number; // 品种数量
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModified?: Date; // 最后修改时间
    comment?: {
        content?: string;
        employeeId?: string;
        employeeName?: string;
        time?: Date;
    }[];
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    @JsonProperty({ clazz: InventoryOutToOrgan })
    createdBy?: InventoryOutToOrgan; // 创建人
    type?: InventoryReceptionOrderType;
    inPharmacy?: InventoryPharmacyInfo; // 调入药房
    outPharmacy?: InventoryPharmacyInfo; // 调出药房

    __draftId?: string;

    toInventoryReceptionListRows(): InventoryReceptionOrderListItem {
        const receptionListRow = JsonMapper.deserialize(InventoryReceptionOrderListItem, {
            status: this.status ?? InventoryReceptionOrderStatus.onlineDraft,
            type: this.type,
            lastModified: this.lastModified,
            __draftId: this.__draftId,
            inPharmacy: this.inPharmacy,
            outPharmacy: this.outPharmacy,
            created: this.created,
            createdBy: this.createdBy,
        });
        return receptionListRow;
    }
}
/**
 * 领用单草稿详情
 */
export class InventoryReceptionDraftDetail {
    amount?: number;
    amountExcludingTax?: number;
    comment?: {
        content?: string;
        employeeId?: string;
        employeeName?: string;
        time?: Date;
    }[];
    count?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    @JsonProperty({ clazz: InventoryOutToOrgan })
    createdBy?: InventoryOutToOrgan; // 创建人
    id?: string;
    @JsonProperty({ clazz: InventoryOutToOrgan })
    inConfirmBy?: InventoryOutToOrgan;
    inConfirmed?: Date;
    inPharmacy?: InventoryPharmacyInfo; // 调入药房
    kindCount?: number; // 品种数量
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModified?: Date; // 最后修改时间
    @JsonProperty({ clazz: InventoryOutToOrgan })
    lastModifiedBy?: InventoryOutToOrgan;
    @JsonProperty({ type: Array, clazz: InventoryReceptionOrderDetailList })
    list?: InventoryReceptionOrderDetailList[];
    @JsonProperty({ type: Array, clazz: InventoryOutSheetLogs })
    logs?: InventoryOutSheetLogs[]; // 调拨单日志
    orderNo?: string;
    organ?: InventoryOutOrgan; // 调出药房
    @JsonProperty({ clazz: InventoryOutToOrgan })
    outConfirmBy?: InventoryOutToOrgan;
    outConfirmed?: Date; // 出库单的审核时间
    outPharmacy?: InventoryPharmacyInfo; // 调出药房
    receiveDepartment?: ReceiveDepartment; //领用科室
    receiveDepartmentId?: string; //领用科室ID
    receptSourceOrderId?: string; // 领用退回出库/领用退回入库 指定要退回的领用单ID，需要原领处方确认
    receptSourceOrder?: InventoryReceptionOrderListItem; //原领用单
    @JsonProperty({ fromJson: fromJsonToDate })
    recepted?: Date; // 领用单的确认时间
    receptedBy?: InventoryOutToOrgan; // 领用单的确认人
    reviewBy?: InventoryOutToOrgan; // 审核人
    @JsonProperty({ fromJson: fromJsonToDate })
    reviewed?: Date; // 审核时间
    sourceInPharmacy?: InventoryPharmacyInfo;
    sourceOutPharmacy?: InventoryPharmacyInfo;
    status?: InventoryReceptionOrderStatus;
    statusName?: string;
    toUserId?: string; // 领用人id
    toUser?: {
        id?: string; // 领用人id
        name?: string; // 领用人姓名
    };
    type?: InventoryReceptionOrderType;
    _localDraftDetail?: InventoryReceptionDraft;

    get localDraftDetail(): InventoryReceptionDraft {
        if (!this._localDraftDetail) {
            this.toTranLocalDraft();
        }
        //@ts-ignore
        return this._localDraftDetail;
    }

    set localDraftDetail(data: InventoryReceptionDraft) {
        this._localDraftDetail = data;
    }

    toInventoryReceptionListRows(): InventoryReceptionOrderListItem {
        const result = this.localDraftDetail.toInventoryReceptionListRows();
        result.id = this.id;
        return result;
    }

    /**
     * 云草稿转换为本地草稿
     */
    toTranLocalDraft(): void {
        this.localDraftDetail = JsonMapper.deserialize(InventoryReceptionDraft, {
            status: InventoryReceptionOrderStatus.onlineDraft,
            type: this.type,
            orderNo: this.orderNo,
            lastModified: this.lastModified,
            created: this.created,
            createdBy: this.createdBy,
            __draftId: this.id,
            inPharmacy: this.inPharmacy,
            outPharmacy: this.outPharmacy,
        });
    }
}

export class InventoryReceptionDraftRsp {
    keyword?: string;
    limit?: number;
    offset?: number;
    total?: number;
    @JsonProperty({ type: Array, clazz: InventoryReceptionDraftDetail })
    rows?: InventoryReceptionDraftDetail[];
}
