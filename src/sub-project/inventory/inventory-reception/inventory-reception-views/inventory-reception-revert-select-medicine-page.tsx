/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2023/5/18
 * @Copyright 成都字节流科技有限公司© 2023
 */
import React, { useRef, useState } from "react";
import { Text, View } from "@hippy/react";
import {
    BatchInfoItem,
    InventoryReceptionOrderDetail,
    InventoryReceptionOrderDetailBatch,
    InventoryReceptionOrderDetailList,
    InventoryReceptionOrderType,
    StockIoBatchDetailList,
} from "../inventory-reception-data/inventory-reception-bean";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { AbcCheckbox, AbcIndeterminateCheckbox, IndeterminateCheckboxStatus } from "../../../base-ui/views/abc-checkbox";
import { BasePage, SizedBox, Spacer, ToolBar, ToolBarButtonStyle1 } from "../../../base-ui";
import _ from "lodash";
import { errorSummary, TimeUtils } from "../../../common-base-module/utils";
import { ABCUtils } from "../../../base-ui/utils/utils";
import { AbcSet } from "../../../base-ui/utils/abc-set";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import { DialogIndex, showQueryDialog } from "../../../base-ui/dialog/dialog-builder";
import colors from "../../../theme/colors";
import WillPopListener from "../../../base-ui/views/will-pop-listener";
import { AbcListView } from "../../../base-ui/list/abc-list-view";
import { DeviceUtils } from "../../../base-ui/utils/device-utils";
import { InventoryAgent } from "../../data/inventory-agent";
import { userCenter } from "../../../user-center";
import { AbcView } from "../../../base-ui/views/abc-view";
import { InventoryReceptionMedicineBatchPage } from "./inventory-reception-medicine-batch-page";
import { showBottomPanel } from "../../../base-ui/abc-app-library/panel";
import { JsonMapper } from "../../../common-base-module/json-mapper/json-mapper";
import { Toast } from "../../../base-ui/dialog/toast";
import { InventoryReceptionAgent, PostInventoryReceptionDetail } from "../inventory-reception-data/inventory-reception-agent";
import URLProtocols from "../../../url-dispatcher/url-protocols";
import { PharmacyListItem } from "../../../data/goods/goods-agent";
import { InventoryClinicConfig } from "../../data/inventory-bean";
import { ClinicAgent } from "../../../base-business/data/clinic-agent";
import abcI18Next from "../../../language/config";

interface InventoryReceptionRevertSelectMedicinePageProps {
    inInfo: InventoryReceptionOrderDetail;
}

interface InventoryReceptionRevertSelectMedicinePageStates {
    detailData: InventoryReceptionOrderDetail;
    selectMedicine: AbcSet<InventoryReceptionOrderDetailList>;
    currentPharmacy?: PharmacyListItem;
    pharmacyInfoConfig?: InventoryClinicConfig;
    stockReceptionChainReview?: boolean;
}
export class InventoryReceptionRevertSelectMedicinePage extends BasePage<
    InventoryReceptionRevertSelectMedicinePageProps,
    InventoryReceptionRevertSelectMedicinePageStates
> {
    private _canViewPrice?: boolean;
    constructor(props: InventoryReceptionRevertSelectMedicinePageProps) {
        super(props);
        const cloneDetailDate = _.cloneDeep(props.inInfo);
        // 将batchs与stockIoBatchDetailList中的packageCount与pieceCount处理成可退的数量
        cloneDetailDate.list = cloneDetailDate.list?.map((item) => {
            item.batchs = item.batchs?.map((t) => {
                // 退回--后台返回的batchs与stockIoBatchDetailList中退回数据不一致，退回采用stockIoBatchDetailList数据，但是为了传给后台数据方便，前端采用的batchs，所以需要将batchs中数据统一
                const batchItem = item.stockIoBatchDetailList?.find((k) => k.batchId == t.batchId);
                if (!!batchItem) {
                    t.returnLeftPackageCount = batchItem?.returnLeftPackageCount;
                    t.returnLeftPieceCount = batchItem?.returnLeftPieceCount;
                }
                t.packageCount = t.returnLeftPackageCount;
                t.pieceCount = t.returnLeftPieceCount;
                return t;
            });
            item.stockIoBatchDetailList = item.stockIoBatchDetailList?.map((t) => {
                t.packageCount = t.returnLeftPackageCount;
                t.pieceCount = t.returnLeftPieceCount;
                return t;
            });
            return item;
        });
        this.state = {
            detailData: cloneDetailDate,
            selectMedicine: new AbcSet<InventoryReceptionOrderDetailList>(),
        };
    }

    getAppBarTitle(): string {
        return "选择退回药品";
    }

    componentDidMount(): void {
        const currentPharmacy = InventoryAgent.getCurrentPharmacy();
        const pharmacyInfoConfig = userCenter.inventoryClinicConfig;
        // 拉取诊所配置
        ClinicAgent.getInventoryChainConfig().then((config) => {
            const stockReceptionChainReview = !!config.chainReview?.stockReceptionChainReview;
            this.setState({
                stockReceptionChainReview,
            });
        });
        ClinicAgent.getEmployeesMeConfig(true)
            .then((res) => {
                this._canViewPrice = !!res?.employeeDataPermission?.inventory?.isCanSeeObtainGoodsPrice;
            })
            .catchIgnore();
        super.componentDidMount();
        this.setState({
            currentPharmacy,
            pharmacyInfoConfig,
        });
    }

    async onBackClick(): Promise<void> {
        const { selectMedicine } = this.state;
        if (selectMedicine.size) {
            const result = await showQueryDialog("", "返回上一步（选择领用单），已选择的药品将清空");
            if (result == DialogIndex.positive) {
                ABCNavigator.pop();
            }
        } else {
            ABCNavigator.pop();
        }
    }

    modifySelectAllMedicines(status: boolean): void {
        const { detailData, selectMedicine } = this.state;
        if (status) {
            detailData.list?.forEach((medicine) => {
                selectMedicine.add(medicine);
            });
        } else {
            detailData.list?.forEach((medicine) => {
                selectMedicine.delete(medicine);
            });
        }
        this.setState({ selectMedicine: selectMedicine });
    }

    modifySelectMedicines(medicine: InventoryReceptionOrderDetailList): void {
        const { selectMedicine } = this.state;
        //判断是否存在选中列表中
        if (selectMedicine.has(medicine)) {
            selectMedicine.delete(medicine);
        } else {
            selectMedicine.add(medicine);
        }
        this.setState({
            selectMedicine,
        });
    }

    async requestFinishInventoryOut(options: {
        detailData: InventoryReceptionOrderDetail;
        selectList?: InventoryReceptionOrderDetailList[];
    }): Promise<void> {
        const cloneInfo = _.cloneDeep(options?.detailData);
        cloneInfo!.list = options?.selectList;
        cloneInfo?.list?.map((item) => {
            item.batchs?.map((batch) => {
                batch.applicationPackageCount = batch?.returnLeftPackageCount ?? 0;
                batch.applicationPieceCount = batch?.returnLeftPieceCount ?? 0;
                if (batch?.batchId == "不指定批次") batch.batchId = undefined;
                return batch;
            });
            return item;
        });
        //如果未填写退货数量，弹出提示
        // if (!this.checkReturnQuantity()) return;
        const { currentPharmacy, pharmacyInfoConfig, stockReceptionChainReview } = this.state;
        const isOpenMultiplePharmacy = pharmacyInfoConfig?.isOpenMultiplePharmacy;
        let tips = "";
        //从本库房退出
        if (_.isUndefined(currentPharmacy?.no) || !isOpenMultiplePharmacy) {
            tips = `确认退回后将锁定库存并发送${cloneInfo?.outPharmacy?.name ?? "出库库房"}确认，待${
                cloneInfo?.outPharmacy?.name ?? "出库库房"
            }入库后扣除，确认退回吗？`;
        } else {
            if (currentPharmacy?.no == this.state.detailData.outPharmacy?.no) {
                if (stockReceptionChainReview && userCenter.clinic?.isChainSubClinic) {
                    tips = !!cloneInfo?.inPharmacy?.name
                        ? `领用退回单将在总部审核后发送给${cloneInfo.inPharmacy?.name}确认，确认提交吗？`
                        : "领用退回单将在总部审核后退入库存，确认提交吗？";
                } else {
                    tips = `${
                        isOpenMultiplePharmacy
                            ? `提交后将发送${cloneInfo?.inPharmacy?.name ?? "入库库房"}审核，`
                            : "提交后将会实时更新库存信息，"
                    }确认提交吗？`;
                }
            } else if (currentPharmacy?.no == this.state.detailData.inPharmacy?.no) {
                if (stockReceptionChainReview && userCenter.clinic?.isChainSubClinic) {
                    tips = `领用退回单将在总部审核后发送给${cloneInfo?.outPharmacy?.name}确认，确认提交吗？`;
                } else {
                    tips = `确认退回后将锁定库存并发送${cloneInfo?.outPharmacy?.name ?? "出库库房"}确认，待${
                        cloneInfo?.outPharmacy?.name ?? "出库库房"
                    }入库后扣除，确认退回吗？`;
                }
            }
        }
        const result = await showQueryDialog("提示", tips);
        if (result != DialogIndex.positive) return;
        // receptSourceId是batchs中的id
        cloneInfo?.list?.map((item) => {
            item.batchs?.map((batch) => {
                batch.receptSourceId = batch.id;
                batch.applicationPackageCount = batch?.packageCount;
                batch.applicationPieceCount = batch?.pieceCount;
                return batch;
            });
            return item;
        });
        //根据领用单的type决定当前是退回出库还是退回入库
        //领用入库（20）---退回出库（10）
        //领用出库（1）---退回入库（30）

        let _type;
        if (_.isUndefined(currentPharmacy?.no) || !isOpenMultiplePharmacy) {
            //汇总库房或者单店库房默认按照退回入库处理
            _type = InventoryReceptionOrderType.receiveReOut;
        } else {
            if (currentPharmacy?.no == this.state.detailData.outPharmacy?.no) {
                _type = InventoryReceptionOrderType.receiveReOut;
            } else if (currentPharmacy?.no == this.state.detailData.inPharmacy?.no) {
                _type = InventoryReceptionOrderType.receiveReIn;
            }
        }

        InventoryReceptionAgent.postInventoryReceptionDetail(
            JsonMapper.deserialize(PostInventoryReceptionDetail, {
                list: cloneInfo?.list,
                inPharmacyNo: isOpenMultiplePharmacy ? cloneInfo?.outPharmacy?.no : currentPharmacy?.no,
                outPharmacyNo: isOpenMultiplePharmacy ? cloneInfo?.inPharmacy?.no : undefined,
                receptSourceOrderId: cloneInfo?.id,
                type: isOpenMultiplePharmacy ? _type : InventoryReceptionOrderType.receiveReOut,
                currentPharmacyNo: currentPharmacy?.no,
            })
        )
            .then(() => {
                Toast.show("保存成功");
                ABCNavigator.popUntil(URLProtocols.INVENTORY_RECEPTION_LIST, URLProtocols.INVENTORY_RECEPTION_LIST);
            })
            .catch((error) => {
                Toast.show(errorSummary(error), { warning: true });
            });
    }

    navToWriteQuantityPage(): void {
        const { selectMedicine, detailData } = this.state;
        const selectList: InventoryReceptionOrderDetailList[] = [];
        selectMedicine.forEach((item) => {
            selectList.push(item);
        });
        this.requestFinishInventoryOut({ detailData, selectList });
        // ABCNavigator.navigateToPage(<InventoryReceptionRevertMedicineModifyPage inInfo={detailData} selectList={selectList} />);
    }

    renderContent(): JSX.Element {
        const { selectMedicine, detailData } = this.state;
        const inInfo = detailData;
        const checkStatus = selectMedicine.size
            ? selectMedicine.size == inInfo.list?.length
                ? IndeterminateCheckboxStatus.all
                : IndeterminateCheckboxStatus.some
            : IndeterminateCheckboxStatus.none;

        if (!DeviceUtils.isIOS() && !inInfo) return <View />;

        return (
            <View style={{ backgroundColor: colors.white, flex: 1 }}>
                <WillPopListener
                    onWillPop={() => {
                        this.onBackClick();
                    }}
                />
                <View>
                    <View style={[ABCStyles.bottomLine, { paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp10 }]}>
                        <View style={ABCStyles.rowAlignCenter}>
                            <Text style={[TextStyles.t16MB1, { flexShrink: 1, lineHeight: Sizes.dp24 }]} numberOfLines={1}>
                                {inInfo.orderNo ?? ""}
                            </Text>
                            <Spacer />
                            {!!this._canViewPrice && (
                                <Text style={[TextStyles.t16NT1, { marginLeft: Sizes.dp8, lineHeight: Sizes.dp24 }]} numberOfLines={1}>
                                    {`${abcI18Next.t("¥")}${ABCUtils.formatMoney(inInfo.amount ?? 0, true)}`}
                                </Text>
                            )}
                        </View>

                        <SizedBox height={Sizes.dp8} />

                        <View style={ABCStyles.rowAlignCenter}>
                            <Text style={[TextStyles.t16NT4.copyWith({ lineHeight: Sizes.dp20 }), { marginRight: Sizes.dp6 }]}>
                                {`${inInfo?.outPharmacy?.name ?? ""}${inInfo?.inPharmacy?.name ? "→" + inInfo?.inPharmacy?.name : ""}`}
                            </Text>
                            <Spacer />
                            <Text style={[TextStyles.t14NT4, { marginLeft: Sizes.dp8, lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                                {TimeUtils.formatDate(inInfo?.created) ?? ""}
                            </Text>
                            <Text style={[TextStyles.t14NT4, { marginLeft: Sizes.dp4, lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                                {inInfo.createdBy?.name ?? ""}
                            </Text>
                        </View>
                    </View>
                    <View
                        style={[
                            ABCStyles.bottomLine,
                            ABCStyles.rowAlignCenter,
                            { paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp12 },
                        ]}
                        onClick={() => {
                            this.modifySelectAllMedicines(checkStatus != IndeterminateCheckboxStatus.all);
                        }}
                    >
                        <AbcIndeterminateCheckbox
                            size={Sizes.dp16}
                            style={{ marginRight: Sizes.dp5 }}
                            enable={!!inInfo.list?.length}
                            check={checkStatus != IndeterminateCheckboxStatus.none} // 选中的
                            checkStatus={checkStatus}
                            onChange={(statue) => {
                                this.modifySelectAllMedicines(statue);
                            }}
                        />
                        <Text style={TextStyles.t16MT1}>全选</Text>
                    </View>
                </View>

                <AbcListView
                    style={{ flex: 1 }}
                    scrollEventThrottle={300}
                    numberOfRows={inInfo?.list?.length ?? 0}
                    dataSource={inInfo?.list ?? []}
                    getRowKey={(index) => inInfo.list?.[index].compareKey() ?? ""}
                    renderRow={(item) => (
                        <CheckMedicineItem
                            checked={selectMedicine?.has(item)}
                            medicineInfo={item}
                            onClick={(item) => {
                                this.modifySelectMedicines(item);
                            }}
                        />
                    )}
                />

                <ToolBar>
                    <ToolBarButtonStyle1
                        text={`${inInfo?.type == InventoryReceptionOrderType.receiveIn ? "确认退回" : "提交"}`}
                        onClick={
                            selectMedicine.size
                                ? () => {
                                      this.navToWriteQuantityPage();
                                  }
                                : undefined
                        }
                    />
                </ToolBar>
            </View>
        );
    }
}

interface CheckMedicineItemProps {
    checked?: boolean;
    medicineInfo: InventoryReceptionOrderDetailList;
    onClick?(info: InventoryReceptionOrderDetailList): void;
}
const CheckMedicineItem: React.FC<CheckMedicineItemProps> = (props) => {
    const { checked, onClick } = props;
    const [medicineItem, setMedicineItem] = useState(props?.medicineInfo);
    const initMedicineBatch = useRef(props?.medicineInfo?.batchs),
        initMedicineItem = useRef(_.cloneDeep(props?.medicineInfo));

    //是否指定批次
    const isNotBatch = medicineItem.batchs?.some((item) => !item?.batchId || item.batchId == "不指定批次");
    const [notBatch, setNoBatch] = useState(isNotBatch);

    //是否可以选择批次
    const canSelectBatch =
        initMedicineBatch.current?.some((t) => !t?.batchId) && !!initMedicineItem.current?.stockIoBatchDetailList?.length;
    const _SelectBatches = async (info: InventoryReceptionOrderDetailList) => {
        //唤起批次选择
        const selectBatches: BatchInfoItem[] = [];
        if (!!info.batchs?.some((t) => !!t.batchId && t.batchId != "不指定批次")) {
            info.batchs?.forEach((item) => {
                selectBatches.push(
                    JsonMapper.deserialize(BatchInfoItem, {
                        ...item,
                        packageCount: item?.packageCount,
                        pieceCount: item?.pieceCount,
                    })
                );
            });
        } else {
            info.batchs?.forEach((item) => {
                selectBatches.push(
                    JsonMapper.deserialize(BatchInfoItem, {
                        ...item,
                        selected: item?.selected ?? true,
                        _packageCount: initMedicineBatch?.current?.[0]?.returnLeftPackageCount ?? 0,
                        _pieceCount: initMedicineBatch?.current?.[0]?.returnLeftPieceCount ?? 0,
                        packageCount: _.isUndefined(item?.selected)
                            ? initMedicineBatch?.current?.[0]?.returnLeftPackageCount
                            : item?.packageCount,
                        pieceCount: _.isUndefined(item?.selected)
                            ? initMedicineBatch?.current?.[0]?.returnLeftPieceCount ?? 0
                            : item?.pieceCount,
                    })
                );
            });
        }

        const result = await showBottomPanel<StockIoBatchDetailList[]>(
            <InventoryReceptionMedicineBatchPage
                detail={initMedicineItem.current}
                selectBatches={selectBatches}
                canSelectBatch={canSelectBatch}
                totalPrice={info?.totalCostPrice}
            />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );
        if (!result) return;
        const initBatches = { ...medicineItem?.batchs?.[0] };
        medicineItem.batchs = result?.map((item) => {
            return JsonMapper.deserialize(InventoryReceptionOrderDetailBatch, {
                ...initBatches,
                packageCount: item?.packageCount ?? 0,
                pieceCount: item?.pieceCount ?? 0,
                batchId: item?.batchId ?? "",
                batchNo: item?.batchNo ?? "",
                expiryDate: item?.expiryDate,
                selected: item?.selected,
                returnLeftPackageCount: item?.returnLeftPackageCount ?? 0,
                returnLeftPieceCount: item?.returnLeftPieceCount ?? 0,
            });
        });
        setNoBatch(medicineItem.batchs?.some((item) => !item?.batchId || item.batchId == "不指定批次"));
        setMedicineItem(_.cloneDeep(medicineItem));
    };

    return (
        <View
            style={{ backgroundColor: Colors.white, flex: 1 }}
            onClick={() => {
                onClick?.(medicineItem);
            }}
        >
            <View style={[ABCStyles.bottomLine, { marginHorizontal: Sizes.dp16, paddingVertical: Sizes.dp10 }]}>
                <View style={[ABCStyles.rowAlignCenter]}>
                    <AbcCheckbox
                        size={Sizes.dp16}
                        style={{ marginRight: Sizes.dp5 }}
                        check={checked} // 选中的
                        onChange={() => {
                            onClick?.(medicineItem);
                        }}
                    />
                    <Text style={[TextStyles.t16MT1, { flexShrink: 1, lineHeight: Sizes.dp24 }]} numberOfLines={1}>
                        {medicineItem.goods?.displayName ?? ""}
                    </Text>
                    <Text
                        style={[TextStyles.t14NT4, { paddingLeft: Sizes.dp4, paddingRight: Sizes.dp8, lineHeight: Sizes.dp20 }]}
                        numberOfLines={1}
                    >
                        {`${medicineItem.goods?.packageSpec ?? ""}`}
                    </Text>
                    <Spacer />
                    {!checked && (
                        <Text style={[TextStyles.t14NT1, { lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                            {`可退${initMedicineItem.current.returnLeftDisplay ?? ""}`}
                        </Text>
                    )}
                    {!!medicineItem?.stockIoBatchDetailList?.length && checked && (
                        <AbcView
                            style={[
                                ABCStyles.bottomLine,
                                {
                                    borderColor: Colors.bdColor,
                                    paddingBottom: Sizes.dp2,
                                },
                            ]}
                            onClick={() => _SelectBatches(medicineItem)}
                        >
                            <Text style={[TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp20 })]}>
                                {`${medicineItem?.returnCountDisplay}`}
                            </Text>
                        </AbcView>
                    )}
                </View>

                <SizedBox height={Sizes.dp8} />
                <View style={[ABCStyles.rowAlignCenter]}>
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={[TextStyles.t14NT6, { lineHeight: Sizes.dp20, flexShrink: 1 }]} numberOfLines={1}>
                            {`厂家 ${
                                _.isEmpty(medicineItem.goods?.manufacturer) && !medicineItem.goods?.manufacturer
                                    ? "--"
                                    : medicineItem.goods?.manufacturer
                            }`}
                        </Text>
                    </View>
                    <Spacer />
                    {checked && (
                        <Text style={[TextStyles.t14NT6, { lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                            {`可退${initMedicineItem.current.returnLeftDisplay ?? ""}`}
                        </Text>
                    )}
                </View>
                <View style={{ paddingTop: Sizes.dp4 }}>
                    {!notBatch &&
                        medicineItem.batchs?.map((item, index) => {
                            let actualRetirement = "";
                            const actualFillCount =
                                    (item?.packageCount ?? 0) * (medicineItem?.goods?.pieceNum ?? 0) + (item?.pieceCount ?? 0),
                                returnLeftCount =
                                    (item?.returnLeftPackageCount ?? 0) * (medicineItem?.goods?.pieceNum ?? 0) +
                                    (item?.returnLeftPieceCount ?? 0);
                            let useReturnLeftCount = false;
                            if (actualFillCount > returnLeftCount) {
                                useReturnLeftCount = true;
                            }
                            if (!!item?.packageCount) {
                                actualRetirement += `${useReturnLeftCount ? item.returnLeftPackageCount : item.packageCount}${
                                    medicineItem?.goods?.packageUnit ?? ""
                                }`;
                            }
                            if (!!medicineItem?.goods?.pieceUnit && !!item?.pieceCount) {
                                actualRetirement += `${useReturnLeftCount ? item.returnLeftPieceCount : item.pieceCount}${
                                    medicineItem?.goods?.pieceUnit ?? ""
                                }`;
                            }
                            return (
                                <View
                                    key={index}
                                    style={[
                                        ABCStyles.rowAlignCenter,
                                        {
                                            paddingVertical: Sizes.dp2,
                                        },
                                    ]}
                                >
                                    <View style={[ABCStyles.rowAlignCenter]}>
                                        <Text style={[TextStyles.t14NT6, { lineHeight: Sizes.dp20, flexShrink: 1 }]} numberOfLines={1}>
                                            {`批次 ${!!item.batchId ? item.batchId : "--"}`}
                                        </Text>
                                        <SizedBox width={Sizes.dp12} />
                                        <Text style={[TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 })]}>
                                            {`效期 ${!!item?.expiryDate ? TimeUtils.formatDate(item?.expiryDate) : "--"}`}
                                        </Text>
                                    </View>

                                    <Spacer />
                                    <Text style={[TextStyles.t14NT6, { lineHeight: Sizes.dp20, flexShrink: 1 }]} numberOfLines={1}>
                                        {`${actualRetirement}`}
                                    </Text>
                                </View>
                            );
                        })}
                </View>
                {notBatch && (
                    <Text style={[TextStyles.t14NT6, { lineHeight: Sizes.dp20, flexShrink: 1, marginTop: Sizes.dp4 }]} numberOfLines={1}>
                        未指定批次
                    </Text>
                )}
            </View>
        </View>
    );
};
