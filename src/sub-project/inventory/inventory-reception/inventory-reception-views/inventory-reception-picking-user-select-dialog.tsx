/**
 * 成都字节星球科技公司
 * <AUTHOR>
 * @date 2021-04-02
 *
 * @description 选择领用人
 */

import React from "react";
import { Text, View } from "@hippy/react";
import _ from "lodash";
import { BaseBlocNetworkView } from "../../../base-ui/base-page";
import { InventoryReceptionPickingUserSelectDialogBloc } from "./inventory-reception-picking-user-select-dialog-bloc";
import { ReceptionPickingDoctors } from "../inventory-reception-data/inventory-reception-bean";
import { BottomSheetHelper, showBottomSheet } from "../../../base-ui/dialog/bottom_sheet";
import { BlocHelper } from "../../../bloc/bloc-helper";
import { pxToDp } from "../../../base-ui/utils/ui-utils";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { AbcListView } from "../../../base-ui/list/abc-list-view";
import { AbcView } from "../../../base-ui/views/abc-view";
import { RightArrowView } from "../../../base-ui/iconfont/iconfont-view";

interface InventoryReceptionPickingUserSelectDialogProps {}

export class InventoryReceptionPickingUserSelectDialog extends BaseBlocNetworkView<
    InventoryReceptionPickingUserSelectDialogProps,
    InventoryReceptionPickingUserSelectDialogBloc
> {
    public static show(): Promise<ReceptionPickingDoctors> {
        return showBottomSheet(<InventoryReceptionPickingUserSelectDialog />);
    }

    constructor(props: InventoryReceptionPickingUserSelectDialogProps) {
        super(props);
        this.bloc = new InventoryReceptionPickingUserSelectDialogBloc();
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this);
    }
    // 当是总店时 只显示出库人
    private _renderBottomList(): JSX.Element {
        const state = this.bloc.currentState,
            doctors = _.first(state.sourceList)?.doctors ?? [];
        return (
            <View
                style={{
                    flexDirection: "row",
                    height: pxToDp(480),
                    backgroundColor: Colors.white,
                }}
            >
                <AbcListView
                    scrollEventThrottle={300}
                    numberOfRows={doctors.length}
                    dataSource={doctors}
                    getRowKey={(index) => index.toString()}
                    renderRow={(data) => {
                        return this._renderSubTypeItemRow(data);
                    }}
                />
            </View>
        );
    }

    renderContent(): JSX.Element {
        return (
            <View>
                {BottomSheetHelper.createTitleBar("选择领用人")}
                {this.bloc.currentState.sourceList?.length == 1 ? (
                    this._renderBottomList()
                ) : (
                    <View
                        style={{
                            flexDirection: "row",
                            height: pxToDp(480),
                            backgroundColor: Colors.white,
                        }}
                    >
                        <View style={{ flex: 1 }}>{this.renderDepartmentType()}</View>
                        <View
                            style={{
                                backgroundColor: Colors.dividerLineColor,
                                alignSelf: "stretch",
                                width: Sizes.dpHalf,
                            }}
                        />
                        <View style={{ flex: 1 }}>{this._renderSubType()}</View>
                    </View>
                )}
            </View>
        );
    }

    /**
     * 领料人来源一级类型（领料人所在科室）
     * @private
     */
    private renderDepartmentType(): JSX.Element {
        const { sourceTypes } = this.bloc.currentState;
        return (
            <AbcListView
                scrollEventThrottle={300}
                numberOfRows={sourceTypes.length}
                dataSource={sourceTypes}
                getRowKey={(index) => index.toString()}
                renderRow={(data) => {
                    return this._renderPrimaryItemRow(data);
                }}
            />
        );
    }

    private _renderPrimaryItemRow(data: ReceptionPickingDoctors): JSX.Element {
        const state = this.bloc.currentState;
        const { currentSource } = state;
        return (
            <AbcView
                style={{
                    backgroundColor: data.departmentId === currentSource?.departmentId ? Colors.D2 : undefined,
                    height: Sizes.listItemHeight,
                    flexDirection: "row",
                    alignItems: "center",
                    paddingHorizontal: Sizes.dp16,
                    ...ABCStyles.bottomLine,
                }}
                onClick={() => this.bloc.requestSelectPrimaryType(data)}
            >
                <View style={{ flex: 1 }}>
                    <Text style={TextStyles.t16NT1}>{data.departmentName!}</Text>
                </View>
                {state.subTypes && <RightArrowView />}
            </AbcView>
        );
    }

    /**
     * 领料人来源二级类型（领用人）
     * @private
     */
    private _renderSubType(): JSX.Element {
        const { subTypes } = this.bloc.currentState;
        return (
            <AbcListView
                scrollEventThrottle={300}
                numberOfRows={subTypes.length}
                dataSource={subTypes}
                getRowKey={(index) => index.toString()}
                renderRow={(data) => {
                    return this._renderSubTypeItemRow(data);
                }}
            />
        );
    }

    private _renderSubTypeItemRow(data: ReceptionPickingDoctors): JSX.Element {
        const { currentSource } = this.bloc.currentState;
        return (
            <AbcView
                style={{
                    backgroundColor: currentSource?.employeeName === data.employeeId ? Colors.D2 : undefined,
                    height: Sizes.listItemHeight,
                    flexDirection: "row",
                    alignItems: "center",
                    paddingHorizontal: Sizes.dp16,
                    ...ABCStyles.bottomLine,
                }}
                onClick={() => this.bloc.requestSelectSubType(data)}
            >
                <Text style={TextStyles.t16NT1}>{data.employeeName!}</Text>
            </AbcView>
        );
    }
}
