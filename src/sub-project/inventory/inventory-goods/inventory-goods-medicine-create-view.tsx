/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/31
 *
 * @description
 */
import { BaseBlocComponent, BaseComponent } from "../../base-ui/base-component";
import { InventoryInMedicineCreateViewBloc, ScrollToFocusItemState } from "../inventory-in/inventory-in-medicine-create-view-bloc";
import { ScrollView, Style, Text, View } from "@hippy/react";
import React from "react";
import { ListSettingEditItem, ListSettingItem, ListSettingItemStyle, ListSettingRadiosItem } from "../../base-ui/views/list-setting-item";
import { ABCStyles, ABCStyleSheet, Color, Colors, flattenStyles, Sizes, TextStyles } from "../../theme";
import { CustomInput } from "../../base-ui/input/custom-input";
import { LengthLimitingTextInputFormatter, NumberOnlyFormatter, PrecisionLimitFormatter } from "../../base-ui/utils/formatter";
import { InventoryConst } from "../inventory-const";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { TextStyle } from "../../theme/text-styles";
import { GroupDivider } from "../../base-ui/divider-line";
import { Const } from "../../base-ui/utils/consts";
import { NumberUtils } from "../../common-base-module/utils";
import { AbcTextInput, KeyboardType } from "../../base-ui/views/abc-text-input";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import _, { isNil } from "lodash";
import { IconFontView, SizedBox, Spacer } from "../../base-ui";
import { AbcSwitch } from "../../base-ui/switch/abc-switch";
import { AbcView } from "../../base-ui/views/abc-view";
import { SheBaoCodeCardView } from "./inventory-shebao-national-code-search-page-";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import abcI18Next from "../../language/config";
import { showConfirmDialog } from "../../base-ui/dialog/dialog-builder";
import { userCenter } from "../../user-center";
import { SubClinicPricePriceMode } from "../../base-business/data/beans";
import { ListSettingEditItemInventoryInTagView } from "../inventory-in/inventory-in-views/inventory-in-tag-dialog";

const styles = ABCStyleSheet.create({
    groupContainer: {
        paddingHorizontal: Sizes.listHorizontalMargin,
        backgroundColor: Colors.white,
    },
});

enum ListSettingItemType {
    listSettingItem = "ListSettingItem",
    listSettingEditItem = "ListSettingEditItem",
    listSettingRadiosItem = "ListSettingRadiosItem",
}

interface InventoryGoodsMedicineCreateViewProps {
    bloc: InventoryInMedicineCreateViewBloc;
}

export class InventoryGoodsMedicineCreateView extends BaseBlocComponent<
    InventoryGoodsMedicineCreateViewProps,
    InventoryInMedicineCreateViewBloc
> {
    private _scrollView: ScrollView | null = null;

    private _inputHeight = Sizes.dp22;

    constructor(props: InventoryGoodsMedicineCreateViewProps) {
        super(props);
        this.bloc = props.bloc;
    }

    protected takeBlocOwnership(): boolean {
        return false;
    }

    componentWillReceiveProps(nextProps: Readonly<InventoryGoodsMedicineCreateViewProps> /*, nextContext: any*/): void {
        this.bloc = nextProps.bloc;
    }

    componentDidMount(): void {
        this.bloc.state
            .subscribe(async (state) => {
                if (state instanceof ScrollToFocusItemState) {
                    await delayed(300).toPromise();
                    await this._makeFocusItemVisible();
                }
            })
            .addToDisposableBag(this);
    }

    /**
     * 基础模块
     */
    renderBasicModuleView(): JSX.Element {
        const state = this.bloc.currentState;
        const { goodsInfo, showErrorHint, createGoodsTypeSettingItemTitleStr } = state;
        const basicModuleInfoList = [
            {
                show: goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isChineseMedicine,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "通用名",
                starTitle: "*",
                ref: state.cadnKey,
                content: goodsInfo.medicineCadn ?? "",
                contentHint: "输入通用名",
                onClick: () => this.bloc.requestSearchCadn(),
                showErrorBorder: showErrorHint && _.isEmpty(goodsInfo.medicineCadn),
                itemStyle: ListSettingItemStyle.expandIcon,
            },
            {
                show: goodsInfo?.isGoods,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: createGoodsTypeSettingItemTitleStr.NameTitle,
                content: goodsInfo.name ?? "",
                contentHint: `输入${createGoodsTypeSettingItemTitleStr.NameTitle}`,
                onChanged: (value: string) => this.bloc.requestUpdateName(value),
                showErrorBorder: showErrorHint && _.isEmpty(goodsInfo.name),
            },
            {
                show: goodsInfo.isMaterial,
                itemStyleType: ListSettingItemType.listSettingItem,
                ref: state.nameKey,
                contentNumberOfLine: 1,
                title: createGoodsTypeSettingItemTitleStr.NameTitle,
                starTitle: goodsInfo.isMaterial || goodsInfo.isGoods ? "*" : undefined,
                content: goodsInfo.name ?? "",
                contentHint: `输入${createGoodsTypeSettingItemTitleStr.NameTitle}`,
                onClick: () => this.bloc.requestSearchName(),
                showErrorBorder: showErrorHint && _.isEmpty(goodsInfo.name),
                itemStyle: ListSettingItemStyle.expandIcon,
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                starTitle: "*",
                title: createGoodsTypeSettingItemTitleStr.goodsTypeTitle,
                contentHint: `选择${createGoodsTypeSettingItemTitleStr.goodsTypeTitle}`,
                style: ABCStyles.bottomLine,
                content: goodsInfo.displayTypeName,
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => this.bloc.requestUpdateMedicineGoodsTypeId(),
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "二级分类",
                content: goodsInfo.customTypeName,
                contentHint: "选择二级分类",
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => this.bloc.requestSelectCustomTypesForm(),
            },
            {
                show: goodsInfo.isMedicalMaterial,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "进口/国产",
                content: goodsInfo.origin ?? "",
                contentHint: "选择进口/国产",
                onClick: () => this.bloc.requestUpdateOrigin(),

                itemStyle: ListSettingItemStyle.expandIcon,
            },
            {
                show: goodsInfo.isWesternMedicine || goodsInfo.isChineseWesternMedicine || goodsInfo.isChineseMedicine,
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: goodsInfo.displayPharmacologicSource().pharmacologicName,
                content: goodsInfo.pharmacologicsName,
                contentHint: `选择${goodsInfo.displayPharmacologicSource().pharmacologicName}`,
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => {
                    this.bloc.requestSelectPharmacologic(goodsInfo.displayPharmacologicSource().pharmacologicSourceType);
                },
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent,
                itemStyleType: ListSettingItemType.listSettingItem,
                ref: state.nameKey,
                contentNumberOfLine: 1,
                title: createGoodsTypeSettingItemTitleStr.NameTitle,
                starTitle: goodsInfo.isMaterial || goodsInfo.isGoods ? "*" : undefined,
                content: goodsInfo.name ?? "",
                contentHint: `输入${createGoodsTypeSettingItemTitleStr.NameTitle}`,
                onClick: () => this.bloc.requestSearchName(),

                itemStyle: ListSettingItemStyle.expandIcon,
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent,
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "剂型",
                content: goodsInfo._dosageFormTypeDisplay,
                contentHint: "选择剂型",
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => this.bloc.requestSelectPharmaceuticalForm(),
            },
            {
                show: goodsInfo?.isGoods,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: createGoodsTypeSettingItemTitleStr.manufacturerFullTitle,
                content: goodsInfo.manufacturerFull ?? "",
                contentHint: `输入${createGoodsTypeSettingItemTitleStr.manufacturerFullTitle}`,
                onChanged: (value: string) => this.bloc.requestUpdateManufacturer(value),
            },
            {
                show: !goodsInfo?.isGoods,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: createGoodsTypeSettingItemTitleStr.manufacturerFullTitle,
                content: goodsInfo.manufacturerFull ?? "",
                contentHint: `输入${createGoodsTypeSettingItemTitleStr.manufacturerFullTitle}名称`,
                onClick: () => this.bloc.requestSearchManufacturerFull("manufacturerFull"),
                itemStyle: ListSettingItemStyle.expandIcon,
            },
            {
                show: goodsInfo.isMedicalMaterial,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "注册证名",
                contentHint: "输入注册证名",
                content: goodsInfo.certificateName ?? "",
                onChanged: (certificateName: string) => this.bloc.requestUpdateCertificateName(certificateName),
            },
            {
                show:
                    goodsInfo.isMedicineWestAndChinesePatent ||
                    goodsInfo.isGoods ||
                    goodsInfo.isMedicalMaterial ||
                    goodsInfo.isMaterialDisinfectant,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "批准文号",
                contentHint: "输入批准文号",
                content: goodsInfo.medicineNmpn ?? "",
                onChanged: (medicineNmpn: string) => this.bloc.requestUpdateNmpn(medicineNmpn),
            },
            {
                itemStyleType: ListSettingItemType.listSettingEditItem,
                ref: state.barCodeKey,
                title: "条码",
                content: goodsInfo.barCode ?? "",
                keyboardType: "numeric",
                formatter: [NumberOnlyFormatter()],
                onChanged: (value: string) => this.bloc.requestUpdateBarCode(value),
                endIcon: () => (
                    <AbcView
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                marginLeft: Sizes.dp24,
                                height: Sizes.dp30,
                                borderLeftWidth: Sizes.dpHalf,
                                borderColor: Colors.bdColor,
                                borderRadius: DeviceUtils.isIOS() ? 1 : undefined, // 解决显示BUG
                            },
                        ]}
                        onClick={() => {
                            this.bloc.requestScanQRCode();
                        }}
                    >
                        <IconFontView name={"scan"} size={Sizes.dp18} color={Colors.mainColor} style={{ marginLeft: Sizes.dp16 }} />
                    </AbcView>
                ),
            },
            {
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "商品编码",
                contentHint: "系统生成或自定义",
                content: goodsInfo.shortId ?? "",
                onChanged: (shortId: string) => this.bloc.requestUpdateMedicineShortId(shortId),
                maxLength: 20,
            },
            {
                show: goodsInfo.isChineseMedicine,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "计价单位",
                starTitle: "*",
                contentHint: "选择计价单位",
                content: goodsInfo.pieceUnit,
                onClick: () => this.bloc.requestSelectPieceUnit(),
                showErrorBorder: showErrorHint && goodsInfo.pieceUnit == undefined,
                itemStyle: ListSettingItemStyle.expandIcon,
            },
        ];

        const basicModuleInfoListEnd = [
            {
                show: true,
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "标签",
                ref: state.tagKey,
                itemStyle: ListSettingItemStyle.expandIcon,
                contentBuilder: () => {
                    return <ListSettingEditItemInventoryInTagView selectTagList={goodsInfo.goodsTagList} />;
                },
                onClick: () => {
                    this.bloc.requestSelectTag();
                },
            },
            {
                show: true,
                maxLength: 20,
                ref: state.remarkKey,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "备注",
                content: goodsInfo.remark,
                contentHint: "输入备注",
                onChanged: (text: string) => {
                    this.bloc.requestUpdateMark(text);
                },
            },
        ];

        return (
            <View style={styles.groupContainer}>
                {basicModuleInfoList.map((basicModuleInfo) => {
                    if (basicModuleInfo.show == false) return;
                    switch (basicModuleInfo.itemStyleType) {
                        case ListSettingItemType.listSettingItem:
                            return (
                                <ListSettingItem
                                    key={basicModuleInfo.title}
                                    ref={basicModuleInfo.ref}
                                    title={basicModuleInfo.title}
                                    starTitle={basicModuleInfo.starTitle}
                                    content={basicModuleInfo.content}
                                    contentHint={basicModuleInfo.contentHint}
                                    onClick={basicModuleInfo.onClick}
                                    showErrorBorder={basicModuleInfo.showErrorBorder}
                                    itemStyle={basicModuleInfo.itemStyle}
                                    bottomLine={true}
                                />
                            );
                        case ListSettingItemType.listSettingEditItem:
                            return (
                                <ListSettingEditItem
                                    key={basicModuleInfo.title}
                                    ref={basicModuleInfo.ref}
                                    title={basicModuleInfo.title}
                                    starTitle={basicModuleInfo.starTitle}
                                    content={basicModuleInfo.content}
                                    contentHint={basicModuleInfo.contentHint}
                                    onChanged={basicModuleInfo.onChanged}
                                    endIcon={basicModuleInfo.endIcon}
                                    maxLength={basicModuleInfo.maxLength}
                                    bottomLine={true}
                                    keyboardType={basicModuleInfo.keyboardType as KeyboardType}
                                    formatter={basicModuleInfo.formatter}
                                />
                            );
                    }
                })}

                <View ref={state.packageKey} collapsable={false}>
                    <_PackageView />
                </View>

                {basicModuleInfoListEnd.map((basicModuleInfo) => {
                    if (basicModuleInfo.show == false) return;
                    switch (basicModuleInfo.itemStyleType) {
                        case ListSettingItemType.listSettingItem:
                            return (
                                <ListSettingItem
                                    key={basicModuleInfo.title}
                                    ref={basicModuleInfo.ref}
                                    title={basicModuleInfo.title}
                                    content={basicModuleInfo.content}
                                    contentHint={basicModuleInfo.contentHint}
                                    onClick={basicModuleInfo.onClick}
                                    itemStyle={basicModuleInfo.itemStyle}
                                    contentBuilder={basicModuleInfo.contentBuilder}
                                    bottomLine={true}
                                />
                            );
                        case ListSettingItemType.listSettingEditItem:
                            return (
                                <ListSettingEditItem
                                    key={basicModuleInfo.title}
                                    ref={basicModuleInfo.ref}
                                    title={basicModuleInfo.title}
                                    content={basicModuleInfo.content}
                                    contentHint={basicModuleInfo.contentHint}
                                    onChanged={basicModuleInfo.onChanged}
                                    maxLength={basicModuleInfo.maxLength}
                                    bottomLine={true}
                                />
                            );
                    }
                })}
            </View>
        );
    }

    /**
     * 定价模块
     */
    renderPricingModuleView(): JSX.Element {
        const state = this.bloc.currentState;
        const { goodsInfo, showErrorHint, isHospital, isPurchaseMarkup } = state;
        // 进价加成模式下，零售价格和拆零价格只读
        const isReadOnly = goodsInfo.priceType == SubClinicPricePriceMode.purchaseMarkup;
        const startWithTitle = isReadOnly ? undefined : "*";
        const pricingModuleInfoList = [
            {
                show:
                    (goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isChineseMedicine || goodsInfo.isMedicalMaterial) &&
                    isPurchaseMarkup,
                itemStyleType: ListSettingItemType.listSettingItem,
                ref: state.priceTypeKey,
                title: "定价模式",
                content: goodsInfo._priceTypeDisplay,
                contentHint: "选择定价模式",
                style: [
                    ABCStyles.bottomLine,
                    { borderBottomColor: showErrorHint && goodsInfo.priceType == undefined ? Colors.errorBorder : Colors.dividerLineColor },
                ],
                itemStyle: ListSettingItemStyle.expandIcon,
                showErrorBorder: showErrorHint && goodsInfo.priceType == undefined,
                onClick: () => this.bloc.requestSelectPriceType(),
            },
            {
                show:
                    (goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isChineseMedicine || goodsInfo.isMedicalMaterial) &&
                    isPurchaseMarkup &&
                    goodsInfo.priceType == SubClinicPricePriceMode.purchaseMarkup,
                itemStyleType: ListSettingItemType.listSettingItem,
                ref: state.priceMakeupPercentKey,
                height: Sizes.dp48,
                title: "加成率",
                titleStyle: TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
                contentStyle: [ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp14 }],
                showErrorBorder: showErrorHint && goodsInfo.priceMakeupPercent == undefined,
                contentBuilder: () => {
                    return (
                        <View style={[ABCStyles.rowAlignCenterSpaceBetween, { flex: 1 }]}>
                            <AbcTextInput
                                style={{
                                    ...TextStyles.t14NT1.copyWith({
                                        color: goodsInfo.priceMakeupPercent == undefined ? Colors.T4 : Colors.T1,
                                    }),
                                    flex: 1,
                                }}
                                placeholder={"0"}
                                value={goodsInfo.priceMakeupPercent?.toString()}
                                keyboardType={"numeric"}
                                multiline={false}
                                formatter={[NumberOnlyFormatter(), LengthLimitingTextInputFormatter(5)]}
                                onChangeText={(value) => this.bloc.requestSelectMarkupRate(Number(value))}
                            />
                            <Text style={[TextStyles.t16NT6, { marginLeft: Sizes.dp2 }]}>%</Text>
                        </View>
                    );
                },
            },
            {
                show: goodsInfo.isMedicalMaterial || goodsInfo.isGoods || goodsInfo.isMaterial,
                itemStyleType: ListSettingItemType.listSettingRadiosItem,
                title: "允许对外销售",
                contentStyle: { marginLeft: Sizes.listHorizontalMargin },
                titleStyle: TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
                options: ["是", "否"],
                marginBetweenItem: Sizes.dp48,
                check: goodsInfo.isSell == 0 ? "否" : "是",
                enable: true,
                onChanged: (value: string) => this.bloc.requestUpdateIsSell(value == "是" ? 1 : 0),
            },
            {
                show: (goodsInfo.isMedicalMaterial || goodsInfo.isGoods || goodsInfo.isMaterialDisinfectant) && !!goodsInfo.isSell,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                ref: state.packagePriceKey,
                title: "销售价",
                starTitle: "*",
                contentHint: "输入销售价",
                keyboardType: "numeric",
                content: NumberUtils.formatMaxFixed(goodsInfo.packagePrice),
                formatter: [PrecisionLimitFormatter(Const.defaultPricePrescription), LengthLimitingTextInputFormatter(7)],
                onChanged: (value: string) => {
                    const price = parseFloat(value);
                    this.bloc.requestUpdatePackagePrice(isNaN(price) ? undefined : price);
                },
                showErrorBorder: state.showErrorHint && state.goodsInfo.packagePrice == null,
            },
            {
                show: goodsInfo.isChineseMedicine,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "零售价格",
                starTitle: startWithTitle,
                showErrorBorder: isReadOnly ? false : state.showErrorHint && goodsInfo.piecePrice == undefined,
                contentBuilder: () => {
                    return (
                        <AbcView style={ABCStyles.rowAlignCenter}>
                            <Text style={[TextStyles.t16NT6, { marginRight: Sizes.dp4 }]}>{abcI18Next.t("¥")}</Text>
                            <AbcTextInput
                                keyboardType={"numeric"}
                                placeholder={"输入零售价格"}
                                style={{
                                    ...TextStyles.t16NT1,
                                    maxWidth: goodsInfo.piecePrice ? Sizes.dp80 : undefined,
                                    height: this._inputHeight,
                                    marginRight: Sizes.dp4,
                                }}
                                editable={!isReadOnly}
                                syncTextOnBlur={true}
                                multiline={false}
                                value={!isNil(goodsInfo.piecePrice) ? goodsInfo.piecePrice?.toString() : ""}
                                onChangeText={(value) => this.bloc.requestUpdatePiecePrice(StringUtils.parseFloat(value)!)}
                                formatter={[
                                    PrecisionLimitFormatter(Const.chineseMedicineSellPricePrecision),
                                    LengthLimitingTextInputFormatter(7),
                                ]}
                            />
                            {goodsInfo.pieceUnit && <Text style={[TextStyles.t16NT6, {}]}>{`/${goodsInfo.pieceUnit}`}</Text>}
                        </AbcView>
                    );
                },
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                ref: state.dismountPriceKey,
                title: "零售价格",
                starTitle: startWithTitle,
                contentHint: "输入零售价格",
                formatter: [PrecisionLimitFormatter(Const.chineseMedicineSellPricePrecision), LengthLimitingTextInputFormatter(7)],
                keyboardType: "numeric",
                content: !isNil(goodsInfo.packagePrice) ? goodsInfo.packagePrice?.toString() : "",
                onChanged: (value: string) => {
                    this.bloc.requestUpdatePackagePrice(StringUtils.parseFloat(value)!);
                },
                showErrorBorder: isReadOnly ? false : state.showErrorHint && goodsInfo.packagePrice == undefined,
                editable: !isReadOnly,
            },
            {
                show:
                    !!goodsInfo.isSell &&
                    (goodsInfo.isMedicineWestAndChinesePatent ||
                        goodsInfo.isMedicalMaterial ||
                        goodsInfo.isMaterialDisinfectant ||
                        goodsInfo.isGoods),
                itemStyleType: ListSettingItemType.listSettingRadiosItem,
                title: "是否拆零",
                titleStyle: TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
                options: ["是", "否"],
                marginBetweenItem: Sizes.dp48,
                check: goodsInfo.dismounting == 0 ? "否" : "是",
                enable: true,
                onChanged: (value: string) => this.bloc.requestUpdateDismounting(value == "是" ? 1 : 0),
            },
            {
                show:
                    !!goodsInfo.isSell &&
                    (goodsInfo.isMedicineWestAndChinesePatent ||
                        goodsInfo.isMedicalMaterial ||
                        goodsInfo.isMaterialDisinfectant ||
                        goodsInfo.isGoods) &&
                    goodsInfo.dismounting !== 0,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                ref: state.dismountPriceKey,
                title: "拆零价",
                starTitle: startWithTitle,
                contentHint: "输入拆零价",
                keyboardType: "numeric",
                content: `${NumberUtils.formatMaxFixed(goodsInfo.piecePrice)}${goodsInfo.pieceUnit ? `/${goodsInfo.pieceUnit}` : ""}`,
                formatter: [PrecisionLimitFormatter(Const.chineseMedicineSellPricePrecision), LengthLimitingTextInputFormatter(7)],
                onChanged: (value: string) => {
                    const price = parseFloat(value);
                    this.bloc.requestUpdatePiecePrice(isNaN(price) ? undefined : price);
                },
                showErrorBorder: isReadOnly ? false : state.showErrorHint && state.goodsInfo.piecePrice == null,
                editable: !isReadOnly,
            },
        ];
        return (
            <View style={styles.groupContainer}>
                {pricingModuleInfoList.map((pricingModuleInfo) => {
                    if (pricingModuleInfo.show == false) return;
                    switch (pricingModuleInfo.itemStyleType) {
                        case ListSettingItemType.listSettingItem:
                            return (
                                <ListSettingItem
                                    key={pricingModuleInfo.title}
                                    ref={pricingModuleInfo.ref}
                                    title={pricingModuleInfo.title}
                                    starTitle={pricingModuleInfo.starTitle}
                                    content={pricingModuleInfo.content}
                                    contentHint={pricingModuleInfo.contentHint}
                                    onClick={pricingModuleInfo.onClick}
                                    showErrorBorder={pricingModuleInfo.showErrorBorder}
                                    itemStyle={pricingModuleInfo.itemStyle}
                                    contentBuilder={pricingModuleInfo.contentBuilder}
                                    bottomLine={true}
                                />
                            );
                        case ListSettingItemType.listSettingEditItem:
                            return (
                                <ListSettingEditItem
                                    ref={pricingModuleInfo.ref}
                                    title={pricingModuleInfo.title}
                                    content={pricingModuleInfo.content}
                                    contentHint={pricingModuleInfo.contentHint}
                                    onChanged={pricingModuleInfo.onChanged}
                                    showErrorBorder={pricingModuleInfo.showErrorBorder}
                                    bottomLine={true}
                                    editable={pricingModuleInfo.editable}
                                    keyboardType={(pricingModuleInfo.keyboardType ?? "default") as KeyboardType}
                                    formatter={pricingModuleInfo.formatter}
                                />
                            );
                        case ListSettingItemType.listSettingRadiosItem:
                            return (
                                <ListSettingRadiosItem
                                    key={pricingModuleInfo.title}
                                    title={pricingModuleInfo.title}
                                    titleStyle={pricingModuleInfo.titleStyle}
                                    options={pricingModuleInfo.options as string[]}
                                    marginBetweenItem={pricingModuleInfo.marginBetweenItem}
                                    check={pricingModuleInfo.check}
                                    enable={pricingModuleInfo.enable}
                                    onChanged={pricingModuleInfo.onChanged}
                                    bottomLine={true}
                                />
                            );
                    }
                })}
                {!(goodsInfo.isMaterialLogistics || goodsInfo.isMaterialFixedAssets) && <_MedicineInOutTax />}
                {/*{(goodsInfo.isMedicineWestAndChinesePatent ||*/}
                {/*    goodsInfo.isChineseMedicine ||*/}
                {/*    goodsInfo.isMedicalMaterial ||*/}
                {/*    goodsInfo.isGoods) && (*/}
                {/*    <ListSettingItem*/}
                {/*        style={ABCStyles.bottomLine}*/}
                {/*        title={"利润分类"}*/}
                {/*        content={goodsInfo._profitCategoryTypeDisplay}*/}
                {/*        contentHint={"选择利润分类"}*/}
                {/*        itemStyle={ListSettingItemStyle.expandIcon}*/}
                {/*        onClick={() => this.bloc.requestSelectProfitCategorization()}*/}
                {/*        bottomLine={true}*/}
                {/*    />*/}
                {/*)}*/}
                {!!isHospital && (
                    <ListSettingItem
                        title={"费用类型"}
                        starTitle={"*"}
                        bottomLine={true}
                        content={state.goodsInfo?.feeTypeName}
                        contentTextStyle={TextStyles.t16NT1}
                        contentHint={"请选择费用类型"}
                        onClick={() => {
                            this.bloc.requestModifyGoodsInfoFeeType();
                        }}
                    />
                )}
            </View>
        );
    }

    /**
     * 医保模块
     */
    renderHealthInsuranceModuleView(): JSX.Element {
        const state = this.bloc.currentState;
        const { goodsInfo, medicalFeeGradeStr, covidDictFlagStr, sheBaoCodeSearchGoodsSelectItem, defaultMedicalInsuranceCodeStr } = state;
        return (
            <View style={styles.groupContainer}>
                {(goodsInfo.isWesternMedicine ||
                    goodsInfo.isChineseWesternMedicine ||
                    goodsInfo.isChineseMedicine ||
                    goodsInfo.isMedicalMaterial ||
                    goodsInfo.isMaterialDisinfectant) && (
                    <View>
                        <ListSettingItem
                            ref={state.sheBaoCodeKey}
                            title={"医保对码"}
                            onClick={() => this.bloc.requestUpdateMedicineSheBaoCode()}
                            itemStyle={ListSettingItemStyle.expandIcon}
                            bottomLine={!sheBaoCodeSearchGoodsSelectItem}
                            contentBuilder={() => {
                                const textColor =
                                    defaultMedicalInsuranceCodeStr !== "选择医保对码" &&
                                    defaultMedicalInsuranceCodeStr !== "不刷医保/暂无编码"
                                        ? Colors.T1
                                        : Colors.t4;
                                return (
                                    <View style={[ABCStyles.rowAlignCenter, { flex: 1, backgroundColor: Colors.S2 }]}>
                                        <View style={[ABCStyles.rowAlignCenter, { marginRight: Sizes.dp4, flexShrink: 1 }]}>
                                            <Text style={[TextStyles.t16NT1.copyWith({ color: textColor })]} numberOfLines={1}>
                                                {defaultMedicalInsuranceCodeStr}
                                            </Text>
                                        </View>

                                        {!!medicalFeeGradeStr && goodsInfo.shebao?.nationalCodeId && (
                                            <View
                                                style={[
                                                    ABCStyles.rowAlignCenter,
                                                    Sizes.paddingLTRB(Sizes.dp8, Sizes.dp2, Sizes.dp8, Sizes.dp2),
                                                    {
                                                        justifyContent: "center",
                                                        backgroundColor: Colors.sheBaoTag_bg,
                                                        borderRadius: Sizes.dp2,
                                                        marginRight: Sizes.dp4,
                                                    },
                                                ]}
                                            >
                                                <Text style={TextStyles.t12NT6}>{medicalFeeGradeStr}</Text>
                                            </View>
                                        )}

                                        {!!covidDictFlagStr && goodsInfo.shebao?.nationalCodeId && (
                                            <View
                                                style={[
                                                    ABCStyles.rowAlignCenter,
                                                    Sizes.paddingLTRB(Sizes.dp2, Sizes.dp2, Sizes.dp2, Sizes.dp2),
                                                    {
                                                        backgroundColor: Colors.sheBaoTag_bg,
                                                        borderRadius: Sizes.dp2,
                                                        marginRight: Sizes.dp4,
                                                    },
                                                ]}
                                            >
                                                <Text style={TextStyles.t12NT6}>{covidDictFlagStr}</Text>
                                            </View>
                                        )}
                                    </View>
                                );
                            }}
                        />
                        {goodsInfo.shebao?.nationalCodeId && (
                            <SheBaoCodeCardView
                                style={Sizes.marginLTRB(0, 0, 0, Sizes.dp8)}
                                sheBaoCardInfo={sheBaoCodeSearchGoodsSelectItem}
                                onClick={() => this.bloc.requestUpdateMedicineSheBaoCode()}
                            />
                        )}
                    </View>
                )}

                {(goodsInfo.isMedicineWestAndChinesePatent ||
                    goodsInfo.isChineseMedicine ||
                    goodsInfo.isMedicalMaterial ||
                    goodsInfo.isMaterialDisinfectant) && (
                    <ListSettingItem
                        style={ABCStyles.bottomLine}
                        title={"医保支付"}
                        startIcon={() => (
                            <AbcView
                                style={{ marginLeft: Sizes.dp4, marginRight: Sizes.dp16 }}
                                onClick={() =>
                                    showConfirmDialog(
                                        "",
                                        "统筹支付：结算时优先使用医保统筹基金进行支付\n" +
                                            "个账支付：结算时优先使用个人账户进行支付\n" +
                                            "不使用医保支付：仅对码，但收费时不使用医保进行支付",
                                        "确认",
                                        "",
                                        "left"
                                    ).then()
                                }
                            >
                                <IconFontView name={"info"} size={Sizes.dp16} color={Colors.P3} />
                            </AbcView>
                        )}
                        content={goodsInfo.displaySheBaoPayModeName}
                        contentHint={"选择医保支付"}
                        itemStyle={ListSettingItemStyle.expandIcon}
                        onClick={() => this.bloc.requestSelectMedicalInsurancePayment()}
                        bottomLine={true}
                    />
                )}
            </View>
        );
    }

    /**
     * 扩展模块
     */
    renderClassificationModuleView(): JSX.Element {
        const state = this.bloc.currentState;
        const { goodsInfo, showErrorHint, isOpenMultiplePharmacy, isLocalPharmacy } = state;

        const classificationModuleInfoList = [
            {
                show:
                    userCenter.clinic?.isDrugstoreButler &&
                    (goodsInfo.isMedicineWestAndChinesePatent ||
                        goodsInfo.isChineseMedicine ||
                        goodsInfo.isMedicalMaterial ||
                        goodsInfo.isMaterialDisinfectant ||
                        goodsInfo.isGoods),
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "所属经营范围",
                content: goodsInfo._businessScopeDisplayName,
                contentHint: "选择所属经营范围",
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => this.bloc.requestSelectBusinessScope(),
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isChineseMedicine,
                itemStyleType: ListSettingItemType.listSettingItem,
                ref: state.otcTypeKey,
                title: "处方/OTC",
                content: goodsInfo._otcTypeDisplay,
                contentHint: "请选择处方OTC",
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => this.bloc.requestSelectOTCForm(),
                showErrorBorder: showErrorHint && goodsInfo.otcType == undefined,
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "精麻毒放",
                content: goodsInfo._ingredientDisplay,
                contentHint: "请选择精麻毒放",
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => this.bloc.requestSelectNarcoticsDangerousDrugsRelease(),
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "抗菌药物",
                content: goodsInfo._antibioticDisplay,
                contentHint: "请选择抗菌药物",
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => this.bloc.requestSelectAntibiotics(),
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent && !!goodsInfo.antibiotic,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "DDD值",
                style: ABCStyles.bottomLine,
                contentBuilder: () => {
                    const _unitArr = ["μg", "mg", "g", "U", "万U", "MU"];
                    return (
                        <View style={ABCStyles.rowAlignCenter}>
                            <CustomInput
                                borderType={"none"}
                                style={{ width: Sizes.dp70 }}
                                type={"numeric"}
                                placeholder={"0"}
                                alwaysShowUtil={true}
                                value={goodsInfo.dddOfAntibiotic}
                                unit={goodsInfo.unitOfAntibiotic}
                                formatter={PrecisionLimitFormatter(3)}
                                onChange={(antibiotic) =>
                                    this.bloc.requestUpdateDefinedDailyDose({
                                        dddOfAntibiotic: Number(antibiotic),
                                        unitOfAntibiotic: goodsInfo.unitOfAntibiotic,
                                    })
                                }
                                alwaysHideUnit={false}
                                unitList={_unitArr}
                                onChangeUnit={(unitOfAntibiotic) =>
                                    this.bloc.requestUpdateDefinedDailyDose({
                                        dddOfAntibiotic: goodsInfo.dddOfAntibiotic,
                                        unitOfAntibiotic: unitOfAntibiotic,
                                    })
                                }
                                startUnitListView={
                                    <View
                                        style={{
                                            flexDirection: "row",
                                            marginLeft: Sizes.dp6,
                                            marginRight: Sizes.dp4,
                                            paddingVertical: Sizes.dp8,
                                        }}
                                    >
                                        <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp20 })}>
                                            {"单位切换"}
                                        </Text>
                                    </View>
                                }
                                unitListItemStyle={{ width: undefined, paddingHorizontal: Sizes.dp12 }}
                                disableColor={Colors.t3}
                            />
                            <Spacer />
                        </View>
                    );
                },
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isChineseMedicine,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "国家基药",
                content: goodsInfo._baseMedicineTypeDisplay,
                contentHint: "请选择国家基药",
                style: ABCStyles.bottomLine,
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => this.bloc.requestSelectBaseMedicineType(),
            },
            {
                show: goodsInfo.isMedicalMaterial,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "医疗器械分类",
                content: goodsInfo._deviceTypeDisplay,
                contentHint: "请选择医疗器械分类",
                contentStyle: { marginLeft: Sizes.listHorizontalMargin },
                style: ABCStyles.bottomLine,
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => this.bloc.requestSelectMedicalMaterialDeviceType(),
            },
            {
                show:
                    goodsInfo.isMedicineWestAndChinesePatent ||
                    goodsInfo.isChineseMedicine ||
                    goodsInfo.isMedicalMaterial ||
                    goodsInfo.isMaterialDisinfectant,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "上市许可持有人",
                content: goodsInfo.mha ?? "",
                contentHint: "输入上市许可持有人",
                contentStyle: { marginLeft: Sizes.listHorizontalMargin },
                onClick: () => this.bloc.requestSearchManufacturerFull("mha"),
                itemStyle: ListSettingItemStyle.expandIcon,
            },
            {
                show:
                    goodsInfo.isMedicineWestAndChinesePatent ||
                    goodsInfo.isChineseMedicine ||
                    goodsInfo.isMedicalMaterial ||
                    goodsInfo.isMaterialDisinfectant,
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "养护分类",
                content: goodsInfo._maintainTypeDisplay,
                contentHint: "选择养护分类",
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => this.bloc.requestSelectMaintenanceClassification(),
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isMedicalMaterial || goodsInfo.isMaterialDisinfectant,
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "存储条件",
                content: goodsInfo.storage,
                contentHint: "选择存储条件",
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => this.bloc.requestSelectStorageConditions(),
            },
            {
                show:
                    goodsInfo.isMedicineWestAndChinesePatent ||
                    goodsInfo.isChineseMedicine ||
                    goodsInfo.isMedicalMaterial ||
                    goodsInfo.isMaterialDisinfectant,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "保质期",
                contentHint: "输入保质期",
                contentStyle: { marginLeft: Sizes.dp5 },
                content: goodsInfo.shelfLife?.toString() ?? "",
                formatter: [NumberOnlyFormatter(), LengthLimitingTextInputFormatter(6)],
                keyboardType: "phone-pad",
                onChanged: (value: string) => this.bloc.requestSelectExpirationDate(Number(value)),
                bottomLine: true,
                endIcon: () => (
                    <AbcView style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end" }]}>
                        <Text style={TextStyles.t16NT1}>{"个月"}</Text>
                    </AbcView>
                ),
            },
            {
                show:
                    goodsInfo.isMedicineWestAndChinesePatent ||
                    goodsInfo.isMedicalMaterial ||
                    goodsInfo.isGoods ||
                    goodsInfo.isMaterialDisinfectant,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "批准文号有效期",
                content: goodsInfo._medicineNmpnExpiryDate,
                contentHint: "请选择批准文号有效期",
                itemStyle: ListSettingItemStyle.expandIcon,
                style: ABCStyles.bottomLine,
                onClick: () => this.bloc.requestSelectNpmnExpiryDate(),
            },
            {
                show:
                    (isLocalPharmacy && userCenter.clinic?.isChainAdminClinic) ||
                    (userCenter.clinic?.isNormalClinic && !isOpenMultiplePharmacy),
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "柜号",
                content: goodsInfo.position,
                contentHint: "输入柜号",
                onChanged: (text: string) => {
                    this.bloc.requestModifyPosition(text);
                },
            },
        ];

        return (
            <View style={styles.groupContainer}>
                {classificationModuleInfoList.map((classificationModuleInfo) => {
                    if (classificationModuleInfo.show == false) return;
                    switch (classificationModuleInfo.itemStyleType) {
                        case ListSettingItemType.listSettingItem:
                            return (
                                <ListSettingItem
                                    key={classificationModuleInfo.title}
                                    ref={classificationModuleInfo.ref}
                                    title={classificationModuleInfo.title}
                                    content={classificationModuleInfo.content}
                                    contentHint={classificationModuleInfo.contentHint}
                                    onClick={classificationModuleInfo.onClick}
                                    itemStyle={classificationModuleInfo.itemStyle}
                                    contentBuilder={classificationModuleInfo.contentBuilder}
                                    bottomLine={true}
                                />
                            );
                        case ListSettingItemType.listSettingEditItem:
                            return (
                                <ListSettingEditItem
                                    title={classificationModuleInfo.title}
                                    content={classificationModuleInfo.content}
                                    contentHint={classificationModuleInfo.contentHint}
                                    onChanged={classificationModuleInfo.onChanged}
                                    endIcon={classificationModuleInfo.endIcon}
                                    bottomLine={true}
                                />
                            );
                    }
                })}
            </View>
        );
    }

    renderContent(): JSX.Element {
        const { goodsInfo } = this.bloc.currentState;
        return (
            <View style={{ flex: 1 }}>
                <ScrollView ref={(ref) => (this._scrollView = ref)}>
                    <View style={{ height: 1, backgroundColor: Colors.white }} collapsable={false} ref={"firstChild"} />
                    {this.renderBasicModuleView()}
                    <GroupDivider />
                    {this.renderClassificationModuleView()}
                    {!(goodsInfo.isMaterialLogistics || goodsInfo.isMaterialFixedAssets) && <GroupDivider />}
                    {!(goodsInfo.isMaterialLogistics || goodsInfo.isMaterialFixedAssets) && this.renderPricingModuleView()}
                    {!(goodsInfo.isMaterialLogistics || goodsInfo.isMaterialFixedAssets || goodsInfo.isGoods) && <GroupDivider />}
                    {!(goodsInfo.isMaterialLogistics || goodsInfo.isMaterialFixedAssets || goodsInfo.isGoods) &&
                        this.renderHealthInsuranceModuleView()}

                    <View style={{ height: 1 }} collapsable={false} ref={"lastChild"} />
                </ScrollView>
            </View>
        );
    }

    private async _makeFocusItemVisible(): Promise<void> {
        const focusItemKey = this.bloc.currentState.focusKey;
        this._scrollView?.scrollChildToVisible(focusItemKey!, "firstChild", "lastChild");
    }
}

class _PackageView extends BaseComponent {
    static contextType = InventoryInMedicineCreateViewBloc.Context;

    render() {
        const { goodsInfo, showErrorHint } = InventoryInMedicineCreateViewBloc.fromContext(this.context).currentState;
        const _bloc = InventoryInMedicineCreateViewBloc.fromContext(this.context);
        if (goodsInfo.isChineseMedicine) {
            return (
                <ListSettingEditItem
                    title={"规格"}
                    contentHint={"输入药品规格"}
                    formatter={LengthLimitingTextInputFormatter(20)}
                    keyboardType={"default"}
                    content={goodsInfo.extendSpec ? goodsInfo.extendSpec : ""}
                    onChanged={(value) => _bloc.requestUpdateExtendSpec(value)}
                    bottomLine={true}
                />
            );
        } else if (goodsInfo.isMaterial || goodsInfo.isGoods) {
            return (
                <View style={{ backgroundColor: Colors.white, paddingVertical: Sizes.dp12, justifyContent: "center" }}>
                    <ListSettingItem height={Sizes.dp24} title={"规格"} content={"规格*最小包装数量/包装单位"} />
                    <Text style={[TextStyles.t12NT6, { marginLeft: Sizes.dp80 }]}>{"示例：5mm * 10  支 / 盒"}</Text>
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            ABCStyles.bottomLine,
                            { marginLeft: Sizes.dp62, marginTop: Sizes.dp14, justifyContent: "flex-start" },
                        ]}
                    >
                        <AbcTextInput
                            style={{
                                textAlign: "center",
                                ...ABCStyles.bottomLine,
                                ...TextStyles.t14NT1,
                                width: Sizes.dp54,
                                height: Sizes.dp28,
                                paddingHorizontal: DeviceUtils.isAndroid() ? Sizes.dp6 : 0,
                                borderBottomColor: Colors.P3,
                            }}
                            multiline={false}
                            defaultValue={goodsInfo.materialSpec ? goodsInfo.materialSpec?.toString() : ""}
                            placeholder={"规格"}
                            placeholderTextColor={Colors.P1}
                            onChangeText={(value) => _bloc.requestUpdateMaterialSpec(value)}
                        />

                        <Text style={[TextStyles.t16NT6, { marginHorizontal: Sizes.dp2 }]}>{"*"}</Text>

                        <CustomInput
                            value={goodsInfo.pieceNum}
                            formatter={[LengthLimitingTextInputFormatter(8), PrecisionLimitFormatter(0)]}
                            placeholder={"数量"}
                            placeholderColor={Colors.P1}
                            error={showErrorHint && !Boolean(goodsInfo.pieceNum)}
                            style={{ width: Sizes.dp54, height: Sizes.dp28 }}
                            borderType={"bottom"}
                            onChange={(value) => _bloc.requestUpdatePieceNum(StringUtils.parseFloat(value)!)}
                        />
                        <_RadiusButton
                            width={Sizes.dp48}
                            text={goodsInfo.pieceUnit ?? "单位"}
                            error={showErrorHint && _.isEmpty(goodsInfo.pieceUnit)}
                            textStyle={goodsInfo.pieceUnit != undefined ? undefined : TextStyles.t14NP1}
                            borderColor={showErrorHint && _.isEmpty(goodsInfo.pieceUnit) ? Colors.errorBorder : undefined}
                            onClick={() => _bloc.requestSelectPieceUnit()}
                        />

                        <Text style={TextStyles.t16NT6}>{"/"}</Text>

                        <_RadiusButton
                            text={goodsInfo.packageUnit ?? "包装"}
                            error={showErrorHint && _.isEmpty(goodsInfo.packageUnit)}
                            textStyle={goodsInfo.packageUnit ? undefined : TextStyles.t14NP1}
                            borderColor={showErrorHint && _.isEmpty(goodsInfo.packageUnit) ? Colors.errorBorder : undefined}
                            width={Sizes.dp48}
                            onClick={() => _bloc.requestSelectPackageUnit()}
                        />
                    </View>
                </View>
            );
        } else if (goodsInfo.specType != null && goodsInfo.specType == 1) {
            // for 容量有效成分
            return (
                <View style={{ justifyContent: "center", paddingVertical: Sizes.dp12, backgroundColor: Colors.white }}>
                    <ListSettingItem
                        height={Sizes.dp24}
                        itemStyle={ListSettingItemStyle.expandIcon}
                        title={"规格"}
                        content={"容量:成分含量*制剂/包装"}
                        contentHint={"请选择"}
                        onClick={() => _bloc.requestMedicineSelectSpecification()}
                    />

                    <Text style={[TextStyles.t12NT6, { marginLeft: Sizes.dp80 }]}>{"示例： 1ml : 0.5mg * 8支 / 盒"}</Text>

                    <View style={[ABCStyles.rowAlignCenter, { flex: 1, marginTop: Sizes.dp14, justifyContent: "flex-start" }]}>
                        <SizedBox width={Sizes.dp62} />
                        <CustomInput
                            value={goodsInfo.componentContentNum}
                            formatter={PrecisionLimitFormatter(InventoryConst.medicineDosageNumPrescription)}
                            placeholder={"容量"}
                            placeholderColor={Colors.P1}
                            style={{ width: Sizes.dp64 }}
                            borderType={"bottom"}
                            onChange={(value) => _bloc.requestUpdateComponentNum(StringUtils.parseFloat(value)!)}
                        />

                        <_RadiusButton
                            width={Sizes.dp54}
                            text={goodsInfo.componentContentUnit ?? "单位"}
                            textStyle={goodsInfo.componentContentUnit ? undefined : TextStyles.t14NP1}
                            onClick={() => _bloc.requestSelectComponentUnit()}
                        />

                        <Text style={TextStyles.t16NT2}>:</Text>

                        <CustomInput
                            borderType={"bottom"}
                            placeholder={"成分含量"}
                            placeholderColor={Colors.P1}
                            value={goodsInfo.medicineDosageNum}
                            style={{ width: Sizes.dp64, height: Sizes.dp28, marginLeft: Sizes.dp4 }}
                            formatter={PrecisionLimitFormatter(InventoryConst.medicineDosageNumPrescription)}
                            onChange={(value) => _bloc.requestUpdateDosageNum(StringUtils.parseFloat(value)!)}
                        />

                        <_RadiusButton
                            width={Sizes.dp54}
                            text={goodsInfo.medicineDosageUnit ?? "单位"}
                            textStyle={goodsInfo.medicineDosageUnit ? undefined : TextStyles.t14NP1}
                            borderColor={showErrorHint && _.isEmpty(goodsInfo.pieceUnit) ? Colors.R2 : undefined}
                            onClick={() => _bloc.requestSelectDosageUnit()}
                        />

                        <Text style={TextStyles.t16NT6}>*</Text>
                    </View>

                    <View style={[ABCStyles.rowAlignCenter, { flex: 1, justifyContent: "flex-start" }]}>
                        <SizedBox width={Sizes.dp62} />
                        <CustomInput
                            value={goodsInfo.pieceNum}
                            formatter={[LengthLimitingTextInputFormatter(8), PrecisionLimitFormatter(0)]}
                            placeholder={"制剂"}
                            placeholderColor={Colors.P1}
                            error={showErrorHint && goodsInfo.pieceNum == undefined}
                            style={{ width: Sizes.dp64, height: Sizes.dp28 }}
                            borderType={"bottom"}
                            onChange={(value) => _bloc.requestUpdatePieceNum(StringUtils.parseFloat(value)!)}
                        />

                        <_RadiusButton
                            width={Sizes.dp54}
                            text={goodsInfo.pieceUnit ?? "单位"}
                            error={showErrorHint && goodsInfo.pieceNum == undefined}
                            textStyle={goodsInfo.pieceUnit != undefined ? undefined : TextStyles.t14NP1}
                            borderColor={showErrorHint && _.isEmpty(goodsInfo.pieceUnit) ? Colors.errorBorder : undefined}
                            onClick={() => _bloc.requestSelectPieceUnit()}
                        />

                        <Text style={TextStyles.t16NT6}>/</Text>

                        <_RadiusButton
                            width={Sizes.dp54}
                            text={goodsInfo.packageUnit ?? "包装"}
                            textStyle={goodsInfo.packageUnit ? undefined : TextStyles.t14NP1}
                            borderColor={showErrorHint && _.isEmpty(goodsInfo.packageUnit) ? Colors.errorBorder : undefined}
                            onClick={() => _bloc.requestSelectPackageUnit()}
                        />
                    </View>
                </View>
            );
        } else {
            return (
                <View style={[{ backgroundColor: Colors.white, paddingVertical: Sizes.dp12, justifyContent: "center" }]}>
                    <ListSettingItem
                        height={Sizes.dp24}
                        itemStyle={ListSettingItemStyle.expandIcon}
                        title={"规格"}
                        content={"剂量*小包装数/包装"}
                        contentHint={"请选择"}
                        onClick={() => _bloc.requestMedicineSelectSpecification()}
                    />

                    <Text style={[TextStyles.t12NT6, { marginLeft: Sizes.dp80 }]}>{"示例：50mg * 10片 / 盒"}</Text>

                    <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp14 }]}>
                        <View style={[ABCStyles.rowAlignCenter, { flex: 1, justifyContent: "flex-end" }]}>
                            <CustomInput
                                value={goodsInfo.medicineDosageNum}
                                formatter={PrecisionLimitFormatter(InventoryConst.medicineDosageNumPrescription)}
                                placeholder={"剂量"}
                                placeholderColor={Colors.P1}
                                style={{ width: Sizes.dp54 }}
                                borderType={"bottom"}
                                onChange={(value) => _bloc.requestUpdateDosageNum(StringUtils.parseFloat(value)!)}
                            />

                            <_RadiusButton
                                text={goodsInfo.medicineDosageUnit ?? "单位"}
                                textStyle={goodsInfo.medicineDosageUnit ? undefined : TextStyles.t14NP1}
                                width={Sizes.dp48}
                                onClick={() => _bloc.requestSelectDosageUnit()}
                            />

                            <Text style={TextStyles.t16NT6}>*</Text>

                            <CustomInput
                                value={goodsInfo.pieceNum}
                                formatter={[LengthLimitingTextInputFormatter(8), PrecisionLimitFormatter(0)]}
                                placeholder={"小包装数"}
                                placeholderColor={Colors.P1}
                                error={showErrorHint && goodsInfo.pieceNum == undefined}
                                style={{ width: Sizes.dp60, height: Sizes.dp28 }}
                                borderType={"bottom"}
                                onChange={(value) => _bloc.requestUpdatePieceNum(StringUtils.parseFloat(value)!)}
                            />

                            <_RadiusButton
                                width={Sizes.dp48}
                                text={goodsInfo.pieceUnit ?? "单位"}
                                error={showErrorHint && _.isEmpty(goodsInfo.pieceUnit)}
                                textStyle={goodsInfo.pieceUnit != undefined ? undefined : TextStyles.t14NP1}
                                borderColor={showErrorHint && _.isEmpty(goodsInfo.pieceUnit) ? Colors.errorBorder : undefined}
                                onClick={() => _bloc.requestSelectPieceUnit()}
                            />
                            <Text style={TextStyles.t16NT6}>/</Text>

                            <_RadiusButton
                                text={goodsInfo.packageUnit ?? "包装"}
                                error={showErrorHint && _.isEmpty(goodsInfo.packageUnit)}
                                textStyle={goodsInfo.packageUnit ? undefined : TextStyles.t14NP1}
                                borderColor={showErrorHint && _.isEmpty(goodsInfo.packageUnit) ? Colors.errorBorder : undefined}
                                width={Sizes.dp48}
                                onClick={() => _bloc.requestSelectPackageUnit()}
                            />
                        </View>
                    </View>
                </View>
            );
        }
    }
}

interface _RadiusButtonProps {
    text: string;
    borderColor?: Color;
    textStyle?: TextStyle;
    width?: number;
    error?: boolean;
    style?: Style | Style[];

    onClick?(): void;
}

export class _RadiusButton extends React.Component<_RadiusButtonProps> {
    constructor(props: _RadiusButtonProps) {
        super(props);
    }

    render(): JSX.Element {
        const { borderColor, width, text, textStyle, error, onClick, style } = this.props;
        return (
            <View
                style={{
                    width: width,
                    height: Sizes.dp28,
                    borderBottomWidth: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    minWidth: Sizes.dp36,
                    marginLeft: Sizes.dp4,
                    marginRight: Sizes.dp4,
                    paddingHorizontal: Sizes.dp4,
                    borderColor: borderColor ?? (onClick ? Colors.P1 : Colors.mainColor),
                    backgroundColor: error ? Colors.errorBorderBg : onClick ? Colors.transparent : Colors.D2,
                    ...flattenStyles(flattenStyles(style)),
                }}
                onClick={onClick}
            >
                <Text style={textStyle ?? TextStyles.t14NT1}>{text}</Text>
            </View>
        );
    }
}

class _MedicineInOutTax extends BaseComponent {
    static contextType = InventoryInMedicineCreateViewBloc.Context;

    render() {
        const _bloc = InventoryInMedicineCreateViewBloc.fromContext(this.context);
        const { goodsInfo, isOpenPriceAdjustmentApplication } = _bloc.currentState;
        const _openMedicineInOutTax = goodsInfo.defaultInOutTax == 1;
        const isDrugstoreButler = userCenter.clinic?.isDrugstoreButler;
        // 是否可以编辑会员价（调价申请未开通并且零售价格存在的情况下）（因为app暂时未做调价审批流）
        const canEditMemberPrice = !isOpenPriceAdjustmentApplication && !!goodsInfo.packagePrice;
        // 提示会员价需在零售价格存在的情况下
        const showMemberPriceHint = !isOpenPriceAdjustmentApplication && !goodsInfo.packagePrice;
        return (
            <View>
                <View
                    style={[ABCStyles.rowAlignCenter, ABCStyles.bottomLine, { paddingVertical: Sizes.dp12, backgroundColor: Colors.white }]}
                >
                    <View style={ABCStyles.rowAlignCenter}>
                        <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>{"默认进/销项税率"}</Text>
                        <AbcView
                            style={{ marginLeft: Sizes.dp4 }}
                            onClick={() => showConfirmDialog("", "请在「管理-定价和税率」设置").then()}
                        >
                            <IconFontView name={"info"} size={Sizes.dp16} color={Colors.P3} />
                        </AbcView>
                    </View>
                    <Spacer />
                    <AbcSwitch checked={_openMedicineInOutTax} onChange={(status) => _bloc.requestEnableDefaultInOutTax(status ? 1 : 0)} />
                </View>
                {isDrugstoreButler && (
                    <View>
                        <ListSettingItem
                            title={"会员价"}
                            content={isOpenPriceAdjustmentApplication ? "" : goodsInfo?.memberPriceDesp}
                            onClick={() => canEditMemberPrice && _bloc.requestSelectMemberPrice()}
                            itemStyle={canEditMemberPrice ? ListSettingItemStyle.expandIcon : undefined}
                            bottomLine={true}
                            contentHint={isOpenPriceAdjustmentApplication ? "已开启调价审核，请前往客户端调价" : ""}
                            contentHintTextStyle={isOpenPriceAdjustmentApplication ? TextStyles.t16NT6 : undefined}
                            startIcon={() => {
                                if (!showMemberPriceHint) return <View />;
                                return (
                                    <AbcView
                                        style={{ marginLeft: Sizes.dp4 }}
                                        onClick={() => showConfirmDialog("", "请先设置零售价").then()}
                                    >
                                        <IconFontView name={"info"} size={Sizes.dp16} color={Colors.P3} />
                                    </AbcView>
                                );
                            }}
                        />
                    </View>
                )}
                {!(goodsInfo.isMaterialLogistics || goodsInfo.isMaterialFixedAssets) && (
                    <View style={[ABCStyles.rowAlignCenter, ABCStyles.bottomLine, { height: Sizes.listItemHeight }]}>
                        <Text style={[TextStyles.t16NT2.copyWith({ color: Colors.t2 }), { marginRight: Sizes.dp16 }]}>{"进项税率"}</Text>
                        <AbcTextInput
                            editable={!_openMedicineInOutTax}
                            style={{
                                width: Sizes.dp48,
                                height: Sizes.dp20,
                                ...TextStyles.t14NT1.copyWith({ color: _openMedicineInOutTax ? Colors.T4 : Colors.T1 }),
                            }}
                            placeholder={"0"}
                            value={goodsInfo.inTaxRat?.toString()}
                            keyboardType={"numeric"}
                            multiline={false}
                            onChangeText={(value) => _bloc.requestUpdateInTaxRat(StringUtils.parseInt(value))}
                        />
                        <Text style={[TextStyles.t16NT6, { marginLeft: Sizes.dp2 }]}>%</Text>
                        <Text style={{ flex: 1, textAlign: "center" }}>-</Text>
                        <View style={ABCStyles.rowAlignCenter}>
                            <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>{"销项税率"}</Text>
                            <SizedBox width={Sizes.dp16} />
                            <AbcTextInput
                                editable={!_openMedicineInOutTax}
                                style={{
                                    width: Sizes.dp48,
                                    height: Sizes.dp20,
                                    ...TextStyles.t14NT1.copyWith({ color: _openMedicineInOutTax ? Colors.T4 : Colors.T1 }),
                                }}
                                multiline={false}
                                keyboardType={"numeric"}
                                placeholder={"0"}
                                value={goodsInfo.outTaxRat?.toString() ?? ""}
                                onChangeText={(value) => _bloc.requestUpdateOutTaxRat(StringUtils.parseInt(value))}
                            />
                            <Text style={[TextStyles.t16NT6, { marginLeft: Sizes.dp2 }]}>%</Text>
                        </View>
                    </View>
                )}
            </View>
        );
    }
}
