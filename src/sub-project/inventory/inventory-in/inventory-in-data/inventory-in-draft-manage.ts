/**
 * create by <PERSON><PERSON>
 * desc: 入库草稿管理
 * create date 2021/4/19
 */
import { InventoryDraft, InventoryInDetailStatus } from "./inventory-in-bean";
import { JsonMapper, JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";
import { Subject } from "rxjs";
import { userCenter } from "../../../user-center";
import FileUtils from "../../../common-base-module/file/file-utils";
import _ from "lodash";
import { LogUtils } from "../../../common-base-module/log";
import { InventoryInConst } from "./inventory-in-const";
import { InventoryDraftManager } from "../../data/inventory-draft";

class AllInventoryInDraft {
    @JsonProperty({ type: Array, clazz: InventoryDraft })
    allDraft?: Array<InventoryDraft>;
}

class DraftEvent {}

export class InventoryInDraftManage {
    static inventoryInDraftManageInstance = new InventoryInDraftManage();

    _allDrafts?: InventoryDraft[];
    draftObserver = new Subject<DraftEvent>();

    constructor() {
        this._transferScanDraft();
        // //如果切换诊所，清除已读取的相关缓存数据
        userCenter.sClinicChangeObserver.subscribe((/*_*/) => {
            this._handleUserChange();
        });
    }

    _handleUserChange(): void {
        this._allDrafts = undefined;
        this.draftObserver.next(new DraftEvent());
    }

    async loadInventoryInDraft(id: string): Promise<InventoryDraft> {
        const fileName = await this._getDraftFile(id);
        if (await FileUtils.fileExists(fileName)) {
            const str = await FileUtils.readAsString(fileName);
            return JsonMapper.deserialize(InventoryDraft, JSON.parse(str));
        } else {
            const draft = new InventoryDraft();
            draft.clinicId = userCenter.clinic?.clinicId;
            draft.__draftId = InventoryInConst.inventoryInTypeScanDraftFileName;
            return draft;
        }
    }

    private async _transferScanDraft(): Promise<void> {
        const scanDraft = await InventoryDraftManager.instance.loadDraft();
        if (!scanDraft) return;
        const indexData = JsonMapper.deserialize(InventoryDraft, {
            ...scanDraft,
            __draftId: InventoryInConst.inventoryInTypeScanDraftFileName,
            status: InventoryInDetailStatus.draft,
        });

        const detailStr = JSON.stringify(indexData);
        const fileName = await this._getDraftFile(indexData.__draftId ?? "");
        await FileUtils.writeAsString(fileName, detailStr);

        const indexFile = await this._indexFile();
        const draft = JsonMapper.deserialize(AllInventoryInDraft, {
            allDraft: [indexData],
        });
        if (await FileUtils.fileExists(indexFile)) {
            const content = await FileUtils.readAsString(indexFile);

            if (!_.isNil(indexFile)) {
                const _draft = JsonMapper.deserialize(AllInventoryInDraft, JSON.parse(content));
                draft.allDraft = draft.allDraft?.concat(_draft.allDraft ?? []);
            }
        }
        await FileUtils.writeAsString(indexFile, JSON.stringify(draft));
        await InventoryDraftManager.instance.removeDraft();
    }

    async getAllDrafts(canUseCache = true): Promise<Array<InventoryDraft>> {
        if (canUseCache && this._allDrafts) return this._allDrafts;

        /**
         * 扫码入库 老的草稿信息
         * 存在的情况保存到index file中
         * 同时删除老的路径下的草稿
         */

        await this._transferScanDraft();

        const indexFile = await this._indexFile();
        if (await FileUtils.fileExists(indexFile)) {
            const content = await FileUtils.readAsString(indexFile);

            if (!_.isNil(indexFile)) {
                const draft = JsonMapper.deserialize(AllInventoryInDraft, JSON.parse(content));
                this._allDrafts = draft.allDraft;
            }
        }

        if (this._allDrafts == null) this._allDrafts = [];
        return this._allDrafts;
    }

    async saveDraft(detailData: InventoryDraft): Promise<boolean> {
        this._allDrafts = await this.getAllDrafts();
        _.remove(this._allDrafts!, (index) => index.__draftId == detailData.__draftId);

        const indexData = JsonMapper.deserialize(InventoryDraft, {
            ...detailData,
            __draftId: detailData.__draftId,
            status: InventoryInDetailStatus.draft,
        });

        this._allDrafts?.splice(0, 0, indexData!);

        this._saveIndexFile();

        const detailStr = JSON.stringify(detailData);
        const fileName = await this._getDraftFile(detailData.__draftId ?? "");
        await FileUtils.writeAsString(fileName, detailStr);

        this.draftObserver.next(new DraftEvent());

        return true;
    }

    static generateDraftId(): string {
        return new Date().getTime().toString();
    }

    async removeDraft(draftId: string): Promise<void> {
        await this.getAllDrafts();

        const indexData = this._allDrafts?.find((index) => index.__draftId == draftId);
        /**
         * @desc 无草稿 不做处理
         */
        if (!indexData) return;
        _.remove(this._allDrafts ?? [], indexData);
        try {
            const fileName = await this._getDraftFile(draftId);
            await FileUtils.deleteFile(fileName);
        } catch (e) {}

        this._saveIndexFile();

        this.draftObserver.next();
    }

    async _indexFile(): Promise<string> {
        LogUtils.d("InventoryDraftManage.root = \n" + `${await InventoryInConst.getDraftDir()}/index.json`);
        return `${await InventoryInConst.getDraftDir()}/index.json`;
    }

    async _saveIndexFile(): Promise<void> {
        await this.getAllDrafts();
        const indexFile = await this._indexFile();
        const draft = JsonMapper.deserialize(AllInventoryInDraft, {
            allDraft: this._allDrafts,
        });
        await FileUtils.writeAsString(indexFile, JSON.stringify(draft));
    }

    async _getDraftFile(draftId: string): Promise<string> {
        return await InventoryInConst.getDraftFile(draftId);
    }
}
