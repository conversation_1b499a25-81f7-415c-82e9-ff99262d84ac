// /**
//  * 成都字节星球科技公司
//  * <AUTHOR>
//  * @date 2021-03-26
//  *
//  * @description 入库单详情页
//  */
//
// import React from "react";
// import _ from "lodash";
// import { Bloc, BlocEvent } from "../../bloc";
// import { EventName } from "../../bloc/bloc";
// import { InventoryDraftManager, InventoryMedicineItem } from "../data/inventory-draft";
// import { InventoryInAgent } from "./inventory-in-data/inventory-in-agent";
// import {
//     GetInventoryInRsp,
//     InventoryInDraft,
//     InventoryInSheetPageDetails,
//     InventoryInSheetPageDetailsList,
//     InventoryInTabType,
//     SupplierListRows,
// } from "./inventory-in-data/inventory-in-bean";
// import { SearchParams } from "../data/inventory-bean";
// import { GoodsInfo } from "../../base-business/data/beans";
// import { Subject } from "rxjs";
// import { switchMap } from "rxjs/operators";
// import { ABCError } from "../../common-base-module/common-error";
// import { BaseLoadingState } from "../../bloc/bloc-helper";
// import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../base-ui/dialog/dialog-builder";
// import { ABCNavigator } from "../../base-ui/views/abc-navigator";
// import { SupplierItemStatus } from "../../outpatient/data/goods";
// import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
// import { userCenter } from "../../user-center";
// import { Clinic, ClinicEmployee } from "../../user-center/user-center";
// import { InventoryCheckAgent } from "../inventory-check/inventory-check-agent";
// import { InventoryMedicineSearchPage } from "../Inventory-medicine-search-page";
// import { Toast } from "../../base-ui/dialog/toast";
// import { InventoryUtils } from "../utils/inventory-utils";
// import { InventoryInMedicineEditPage, InventoryMedicineEditResult } from "./inventory-in-medicine-edit-page";
//
// export class State extends BaseLoadingState {
//     type: InventoryInTabType = InventoryInTabType.create;
//     // detail: InventoryInSheetPageDetails = new InventoryInSheetPageDetails(); // 获取入库详情
//
//     detail: InventoryInSheetPageDetailsList = new InventoryInSheetPageDetailsList(); // 入库单详情
//
//     hasChange = false;
//     scrollKeyIndex = 0;
//
//     clone(): State {
//         return Object.assign(new State(), this);
//     }
//
//     get isCreate(): boolean {
//         return this.type == InventoryInTabType.create;
//     }
//
//     employee?: ClinicEmployee;
//     clinic?: Clinic;
//
//     inited = false;
//
//     suppliers?: SupplierListRows[]; // 供应商
//
//     // draft?: InventoryInDraft; // 草稿
//
//     lastSearchDetail?: GetInventoryInRsp; // 上次搜索详细信息
// }
//
// export class ScrollSameViewState extends State {
//     static fromState(state: State): ScrollSameViewState {
//         const newState = new ScrollSameViewState();
//         Object.assign(newState, state);
//         return newState;
//     }
// }
//
// export class InventoryInSheetPageBloc extends Bloc<_Event, State> {
//     static Context = React.createContext<InventoryInSheetPageBloc | undefined>(undefined);
//
//     static fromContext(context: InventoryInSheetPageBloc): InventoryInSheetPageBloc {
//         return context;
//     }
//
//     private _loadDataTrigger: Subject<number> = new Subject<number>();
//
//     private _id?: number;
//     private _searchParams?: SearchParams;
//     private _goodsInfo?: GoodsInfo;
//
//     constructor(options: { id?: number; searchParams?: SearchParams; goodsInfo?: GoodsInfo }) {
//         super();
//
//         this.draftManager = InventoryDraftManager.instance;
//         this.dispatch(new _EventInit());
//
//         if (options.id) {
//             this._id = options.id;
//             this.innerState.type = InventoryInTabType.detail;
//         }
//         this._goodsInfo = options.goodsInfo;
//         this._searchParams = options.searchParams
//             ? _.assign(options.searchParams ?? {}, {
//                   withStock: "1",
//                   onlyStock: "1",
//               })
//             : undefined;
//         this.dispatch(new _EventInit());
//     }
//
//     private readonly draftManager: InventoryDraftManager;
//     private _innerState!: State;
//     get innerState(): State {
//         if (!this._innerState) this._innerState = new State();
//
//         return this._innerState;
//     }
//
//     initialState(): State {
//         return this.innerState;
//     }
//
//     createEventHandlers(): Map<EventName, Function> {
//         const map = new Map<EventName, Function>();
//         map.set(_EventInit, this._mapEventInit);
//         map.set(_EventUpdate, this._mapEventUpdate);
//         map.set(_EventOpenItemTap, this._mapEventOpenItemTap); // 点击打开对应药品
//         // map.set(_EventFinishInventoryIn, this._mapEventFinishInventoryIn); // 完成（新增入库）
//         map.set(_EventSelectSupplier, this._mapEventSelectSuppler); // 选择供应商
//         map.set(_EventChangeOrderNo, this._mapEventChangeOrderNo); // 修改随货单号
//         map.set(_EventChangeComment, this._mapEventChangeComment); // 修改备注
//         // map.set(_EventAddGood, this._mapEventAddGood); // 输入添加入库药品
//
//         // map.set(_EventScanToInventory, this._mapEventScanToInventory); // 扫码入库
//
//         // map.set(_EventContinueSubmit, this._mapEventContinueSubmit);
//
//         // map.set(_EventClear, this._mapEventClear); // 确认清空入库单
//         map.set(_EventBackPage, this._mapEventBackPage); // 未保存返回提示
//
//         return map;
//     }
//
//     // 选择供应商
//     public requestSelectSupplier(): void {
//         this.dispatch(new _EventSelectSupplier());
//     }
//
//     // 输入添加药品
//     public requestSearchMedicine(): void {
//         this.dispatch(new _EventAddGood(false));
//     }
//
//     // 扫码入库;
//     public requestScanToInventory(): void {
//         this.dispatch(new _EventScanToInventory());
//     }
//
//     // 完成入库
//     public requestFinishInventoryIn(): void {
//         this.dispatch(new _EventFinishInventoryIn());
//     }
//
//     // 修改入库单 备注
//
//     public requestChangeComment(value: string): void {
//         this.dispatch(new _EventChangeComment(value));
//     }
//
//     // 随货单号
//     public requestChangeOrderNo(value: string): void {
//         this.dispatch(new _EventChangeOrderNo(value));
//     }
//
//     // 清空
//     public requestClear(): void {
//         this.dispatch(new _EventClear());
//     }
//
//     // 点击打开一条药品
//     public requestOpenItem(item: InventoryMedicineItem): void {
//         this.dispatch(new _EventOpenItemTap(item));
//     }
//
//     private async *_mapEventInit(/*ignore: _EventInit*/): AsyncGenerator<State> {
//         this.innerState.startLoading();
//         this._loadDataTrigger
//             .pipe(
//                 switchMap(() => {
//                     return InventoryInAgent.getInventoryInSheetDetails({ id: this._id })
//                         .then((rsp) => {
//                             if (!userCenter.clinic?.isNormalClinic && userCenter.clinic?.chainId == rsp.toOrgan?.id) {
//                                 rsp.toOrgan!.shortName = "总部";
//                             }
//                             return rsp;
//                         })
//                         .catch((e) => new ABCError(e))
//                         .toObservable();
//                 })
//             )
//             .subscribe((rsp) => {
//                 this.innerState.stopLoading();
//                 if (rsp instanceof ABCError) {
//                     this.innerState.setLoadingError(rsp);
//                 } else {
//                     this.innerState.detail = rsp;
//                 }
//                 this.update();
//             })
//             .addToDisposableBag(this);
//
//         if (this.innerState.type == InventoryInTabType.detail) {
//             this.innerState.startLoading();
//             this._loadDataTrigger.next();
//             this.update();
//         } else {
//             const detail = this.innerState.detail;
//             detail.toOrgan = {
//                 id: userCenter.clinic?.clinicId,
//                 parentId: userCenter.clinic?.chainId,
//                 name: userCenter.clinic?.name,
//                 shortName:
//                     !userCenter.clinic?.isNormalClinic && userCenter.clinic?.isChainAdminClinic ? "总部" : userCenter.clinic?.shortName,
//             };
//             detail.list = [];
//             this.update();
//             if (this._goodsInfo) {
//                 this.dispatch(new _EventAddGoodToList(this._goodsInfo)).then(() => (this._goodsInfo = undefined));
//             }
//         }
//         this.addDisposable(this._loadDataTrigger);
//
//         this._loadDataTrigger.next();
//     }
//
//     private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
//         if (event.state) {
//             yield event.state;
//             return;
//         }
//
//         yield this.innerState.clone();
//     }
//
//     /**
//      * 加载供应商
//      * @private
//      */
//     private async _loadSupplier(): Promise<void> {
//         const innerState = this.innerState;
//         innerState.suppliers = await InventoryInAgent.getSupplierList();
//
//         innerState.suppliers = innerState.suppliers.filter((item) => item.status == SupplierItemStatus.enable);
//     }
//
//     /**
//      * 选择供应商
//      * @private
//      */
//
//     private async *_mapEventSelectSuppler(/*ignore: _EventSelectSupplier*/): AsyncGenerator<State> {
//         const innerState = this.innerState;
//         if (!innerState.suppliers) {
//             await this._loadSupplier();
//         }
//
//         const options = innerState.suppliers?.map((item) => item.name!) ?? [];
//
//         const selects = await showOptionsBottomSheet({
//             title: "选择供应商",
//             options: options,
//             initialSelectIndexes: new Set([options.indexOf(innerState.detail?.supplier?.name ?? "")]),
//         });
//
//         if (!_.isEmpty(selects)) {
//             innerState.draft!.supplier = innerState.suppliers![_.first(selects)!];
//             innerState.draft!.hasChanged = true;
//             yield innerState.clone();
//         }
//     }
//
//     /**
//      * 修改入库单 备注
//      * @private
//      */
//     private async *_mapEventChangeComment(event: _EventChangeComment): AsyncGenerator<State> {
//         const innerState = this.innerState;
//         innerState.draft!.comment = event.comment;
//         yield innerState.clone();
//     }
//
//     /**
//      * 添加药品
//      * @param event
//      * @private
//      */
//
//     private async *_mapEventAddGood(event: _EventAddGood): AsyncGenerator<State> {
//         let goodsInfo: GoodsInfo | undefined;
//         if (event.qrScan) {
//             goodsInfo = await InventoryUtils.BarScan2GoodsInfo(this._searchParams ?? { withStock: "1", onlyStock: "1" });
//         } else {
//             goodsInfo = await ABCNavigator.navigateToPage<GoodsInfo | undefined>(
//                 <InventoryMedicineSearchPage
//                     searchParams={this._searchParams ?? { withStock: "1", onlyStock: "1" }}
//                     callback={(goodsInfo) => ABCNavigator.pop(goodsInfo)}
//                 />
//             );
//         }
//         if (!goodsInfo || !goodsInfo.id) return;
//
//         if (goodsInfo.noStocks) {
//             await showConfirmDialog(`${goodsInfo.displayName}没有入库`, "需先将药品入库后才能执行盘点");
//             return;
//         }
//
//         const lockList = await InventoryCheckAgent.lockMedicineCheck(goodsInfo.id!);
//         if (lockList.length) {
//             await showConfirmDialog(`${goodsInfo.displayName}存在未确认的调拨单，需要调入方确认后可进行盘点`, "");
//             return;
//         }
//
//         let index = 0;
//         for (const item of this.innerState.detail?.list ?? []) {
//             // 判断药品是否相同
//             if (item.goods?.id == goodsInfo.id) {
//                 if (item.batchId == undefined) {
//                     this.innerState.scrollKeyIndex = index;
//                     Toast.show(`入库单已存在【${goodsInfo.displayName}】`, { warning: true });
//                     yield ScrollSameViewState.fromState(this.innerState);
//                     return;
//                 }
//             }
//             index++;
//         }
//
//         this.dispatch(new _EventAddGoodToList(goodsInfo));
//     }
//
//     /**
//      * 完成入库
//      * @private
//      */
//     // private async *_mapEventFinishInventoryIn(/*event: _EventFinishCheck*/): AsyncGenerator<State> {
//     //     if (!this.innerState.detail.list?.length) return;
//     //     const result = await showQueryDialog("", "完成入库将会实时更新库存信息，是否完成？");
//     //     if (result != DialogIndex.positive) return;
//     //
//     //     const req = InventoryInAgent.getInventoryInSheetDetails(this.innerState.detail);
//     //     const loadingDialog = new LoadingDialog();
//     //     loadingDialog.show(100);
//     //     InventoryInAgent.getInventoryInSheetDetails(req) // 创建入库单
//     //         .then(() => {
//     //             InventoryAgent.taskStatusPublisher.next();
//     //             InventoryAgent.medicineRefreshPublisher.next();
//     //             loadingDialog.success("提交成功").then(() => ABCNavigator.pop());
//     //         })
//     //         .catch((error) => {
//     //             loadingDialog.hide().then(() => {
//     //                 if (error instanceof ABCApiError && error?.detail?.error?.code == "12238") {
//     //                     const list: GoodsInfo[] = [];
//     //                     error.detail.error.detail.forEach((item: AnyType) => {
//     //                         list.push(JsonMapper.deserialize(GoodsInfo, { ...item, id: item.goodsId }));
//     //                     });
//     //
//     //                     this.dispatch(new _EventContinueSubmit(error?.detail?.error?.message, list));
//     //                 } else {
//     //                     showConfirmDialog("提交失败", error?.detail?.error?.message);
//     //                 }
//     //             });
//     //         });
//     // }
//
//     /**
//      * 入库未保存返回 提示
//      * @private
//      */
//     private async *_mapEventBackPage(): AsyncGenerator<State> {
//         if (this.innerState.hasChange) {
//             const result = await showQueryDialog("确认退出？", "当前内容有修改，退出后将不会保存");
//             if (result == DialogIndex.positive) ABCNavigator.pop();
//         } else {
//             ABCNavigator.pop();
//         }
//     }
//
//     /**
//      * 入库 更改随货单号
//      * @param event
//      * @private
//      */
//     private async *_mapEventChangeOrderNo(event: _EventChangeOrderNo): AsyncGenerator<State> {
//         const innerState = this.innerState;
//         innerState.draft!.outOrderNo = event.orderNo;
//         yield innerState.clone();
//     }
//
//     /**
//      * 点击打开一条药品
//      * @param event
//      * @private
//      */
//     private async *_mapEventOpenItemTap(event: _EventOpenItemTap): AsyncGenerator<State> {
//         const innerState = this.innerState;
//         const result = await InventoryInMedicineEditPage.editItem(event.item);
//         if (result.action == InventoryMedicineEditResult.actionDelete) {
//             this.draftManager.removeMedicine(event.item).then();
//             yield innerState.clone();
//         } else if (result.action == InventoryMedicineEditResult.actionChanged) {
//             this.draftManager.addOrReplaceMedicine(result.newItem!).then();
//             yield innerState.clone();
//         }
//     }
//
//     public requestBackPage(): void {
//         this.dispatch(new _EventBackPage());
//     }
//
//     /**
//      * 清空
//      * @private
//      */
//     // private async *_mapEventClear(/*ignore: _EventClear*/): AsyncGenerator<State> {
//     //     const innerState = this.innerState;
//     //     const select = await showQueryDialog("", "确认清空入库单？");
//     //     if (select != DialogIndex.positive) return;
//     //
//     //     await this.draftManager.removeDraft();
//     //     innerState.draft = await this.draftManager.loadDraft();
//     //
//     //     yield innerState.clone();
//     // }
//
//     public update(state?: State): void {
//         this.dispatch(new _EventUpdate(state));
//     }
// }
//
// class _Event extends BlocEvent {}
//
// class _EventInit extends _Event {}
//
// class _EventUpdate extends _Event {
//     state?: State;
//
//     constructor(state?: State) {
//         super();
//         this.state = state;
//     }
// }
//
// class _EventClear extends _Event {}
//
// class _EventChangeOrderNo extends _Event {
//     orderNo?: string;
//
//     constructor(orderNo?: string) {
//         super();
//         this.orderNo = orderNo;
//     }
// }
//
// class _EventChangeComment extends _Event {
//     comment?: string;
//
//     constructor(comment?: string) {
//         super();
//         this.comment = comment;
//     }
// }
//
// class _EventFinishInventoryIn extends _Event {}
//
// class _EventAddGood extends _Event {
//     qrScan: boolean;
//
//     constructor(qrScan: boolean) {
//         super();
//         this.qrScan = qrScan;
//     }
// }
//
// class _EventContinueSubmit extends _Event {
//     goodsInfoList: GoodsInfo[];
//     title: string;
//
//     constructor(title: string, goodsInfo: GoodsInfo[]) {
//         super();
//         this.goodsInfoList = goodsInfo;
//         this.title = title;
//     }
// }
//
// class _EventOpenItemTap extends _Event {
//     constructor(item: InventoryMedicineItem) {
//         super();
//         this.item = item;
//     }
//
//     item: InventoryMedicineItem;
// }
//
// class _EventScanToInventory extends _Event {}
//
// class _EventSelectSupplier extends _Event {}
//
// class _EventAddGoodToList extends _Event {
//     goodsInfo: GoodsInfo;
//
//     constructor(goodsInfo: GoodsInfo) {
//         super();
//         this.goodsInfo = goodsInfo;
//     }
// }
//
// class _EventBackPage extends _Event {}
