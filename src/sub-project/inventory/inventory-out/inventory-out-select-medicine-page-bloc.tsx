/**
 * 成都字节星球科技公司
 * <AUTHOR>
 * @date 2021-04-15
 *
 * @description
 */

import React from "react";
import { Bloc, BlocEvent } from "../../bloc";
import { EventName } from "../../bloc/bloc";
import { InventoryInSheetPageDetails, InventoryInSheetPageDetailsList } from "../inventory-in/inventory-in-data/inventory-in-bean";
import { InventoryInAgent } from "../inventory-in/inventory-in-data/inventory-in-agent";
import { AbcSet } from "../../base-ui/utils/abc-set";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { InventoryOutWriteReturnQuantitySheetPage } from "./inventory-out-write-quantity-sheet-page";
import { DialogIndex, showQueryDialog } from "../../base-ui/dialog/dialog-builder";

export class State {
    detailData: InventoryInSheetPageDetails = new InventoryInSheetPageDetails(); // 入库单详情

    selectMedicine: AbcSet<InventoryInSheetPageDetailsList> = new AbcSet();

    get canReturnListMedicine(): InventoryInSheetPageDetailsList[] {
        return this.detailData.list?.filter((item) => item.canReturn) ?? [];
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class InventoryOutSelectMedicinePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<InventoryOutSelectMedicinePageBloc | undefined>(undefined);

    private _id?: string;
    private _inInfo?: InventoryInSheetPageDetails;

    static fromContext(context: InventoryOutSelectMedicinePageBloc): InventoryOutSelectMedicinePageBloc {
        return context;
    }

    constructor(options: { inInfo: InventoryInSheetPageDetails }) {
        super();
        this._inInfo = options.inInfo;
        this._id = options.inInfo.id;

        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventModifySelectMedicines, this._mapEventModifySelectMedicines);
        map.set(_EventNavToWriteQuantityPage, this._mapEventNavToWriteQuantityPage);
        map.set(_EventModifySelectAllMedicines, this._mapEventModifySelectAllMedicines);
        map.set(_EventBackPage, this._mapEventBackPage);

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        InventoryInAgent.getInventoryInSheetDetails({ id: this._id, withReturnLeft: "1", withStockId: "1" })
            .toObservable()
            .subscribe((rsp) => {
                this.innerState.detailData = rsp;
                this.update();
            })
            .addToDisposableBag(this);
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventModifySelectMedicines(event: _EventModifySelectMedicines): AsyncGenerator<State> {
        //判断是否存在选中列表中
        if (this.innerState.selectMedicine.has(event.medicine)) {
            this.innerState.selectMedicine.delete(event.medicine);
        } else {
            this.innerState.selectMedicine.add(event.medicine);
        }
        this.update();
    }

    private async *_mapEventNavToWriteQuantityPage(/*event: _EventNavToWriteQuantityPage*/): AsyncGenerator<State> {
        const detailInfo = this.innerState.detailData;
        const selectList: InventoryInSheetPageDetailsList[] = [];
        this.innerState.selectMedicine.forEach((item) => {
            selectList.push(item);
        });

        ABCNavigator.navigateToPage(<InventoryOutWriteReturnQuantitySheetPage inInfo={detailInfo} selectList={selectList} />)
            .toObservable()
            .subscribe(() => {
                this.innerState.selectMedicine.clear(); // 返回清除已填写的退货数量
                this.update();
            })
            .addToDisposableBag(this);

        this.update();
    }

    private async *_mapEventModifySelectAllMedicines(event: _EventModifySelectAllMedicines): AsyncGenerator<State> {
        if (event.status) {
            this.innerState.canReturnListMedicine.forEach((medicine) => {
                this.innerState.selectMedicine.add(medicine);
            });
        } else {
            this.innerState.canReturnListMedicine.forEach((medicine) => {
                this.innerState.selectMedicine.delete(medicine);
            });
        }
        this.update();
    }

    private async *_mapEventBackPage(/*event: _EventBackPage*/): AsyncGenerator<State> {
        const selectList = this.innerState.selectMedicine;
        if (selectList.size) {
            const result = await showQueryDialog("", "返回上一步（选择入库单），已选择的药品将清空");
            if (result == DialogIndex.positive) {
                ABCNavigator.pop();
            }
        } else {
            ABCNavigator.pop();
        }
    }

    public requestModifySelectMedicines(medicine: InventoryInSheetPageDetailsList): void {
        if (!medicine.canReturn) return;
        this.dispatch(new _EventModifySelectMedicines(medicine));
    }

    public requestNavToWriteQuantityPage(): void {
        this.dispatch(new _EventNavToWriteQuantityPage());
    }

    public requestModifySelectAllMedicines(status: boolean): void {
        this.dispatch(new _EventModifySelectAllMedicines(status));
    }

    public requestBackPage(): void {
        this.dispatch(new _EventBackPage());
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventModifySelectMedicines extends _Event {
    medicine: InventoryInSheetPageDetailsList;

    constructor(medicine: InventoryInSheetPageDetailsList) {
        super();
        this.medicine = medicine;
    }
}

class _EventNavToWriteQuantityPage extends _Event {}

class _EventModifySelectAllMedicines extends _Event {
    status: boolean;

    constructor(status: boolean) {
        super();
        this.status = status;
    }
}

class _EventBackPage extends _Event {}
