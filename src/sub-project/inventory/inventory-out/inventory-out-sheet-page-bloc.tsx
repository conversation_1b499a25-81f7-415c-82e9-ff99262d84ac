/**
 * 成都字节星球科技公司
 * <AUTHOR>
 * @date 2021-03-26
 *
 * @description 出库单详情页
 */

import React from "react";
import { Bloc, BlocEvent } from "../../bloc";
import { EventName } from "../../bloc/bloc";
import { InventoryDraftManager } from "../data/inventory-draft";
import {
    GoodsCheckOrdersItemOrgan,
    GoodsInventoryInfo,
    InventoryApiErrorCode,
    InventoryClinicConfig,
    InventoryPharmacyInfo,
} from "../data/inventory-bean";
import { GoodsInfo } from "../../base-business/data/beans";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { BaseLoadingState } from "../../bloc/bloc-helper";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../base-ui/dialog/dialog-builder";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { userCenter } from "../../user-center";
import { Clinic, ClinicEmployee } from "../../user-center/user-center";
import { Toast } from "../../base-ui/dialog/toast";
import { InventoryOutAgent } from "./inventory-out-data/inventory-out-agent";
import {
    InventoryLossOutOrdersDraft,
    InventoryOutEnterType,
    InventoryOutGoodsInfo,
    InventoryOutOrgan,
    InventoryOutSheetComment,
    InventoryOutSheetCreatedUser,
    InventoryOutSheetDetails,
    InventoryOutSheetFromOrgan,
    InventoryOutSheetList,
    InventoryOutStatus,
    InventoryOutTabType,
    InventoryOutToOrgan,
    InventoryOutToUser,
    InventoryOutType,
    PickingDoctors,
    PostGoodsStocksOutOrdersReq,
    PostGoodsStocksOutOrdersReqList,
    PutGoodsStocksOutOrdersConfirmRep,
    PutGoodsStocksOutOrdersRejectReq,
    PutGoodsStocksOutOrdersReq,
    PutGoodsStocksOutOrdersReqItem,
    PutGoodsStocksOutOrdersReqItemCurrentStockCount,
    PutGoodsStocksOutOrdersReqItemStock,
} from "./inventory-out-data/inventory-out-bean";
import { GetInventoryInRsp } from "../inventory-in/inventory-in-data/inventory-in-bean";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";

import { InventoryMedicineSearchPage } from "../Inventory-medicine-search-page";

import { InventoryUtils } from "../utils/inventory-utils";
import { ABCError } from "../../common-base-module/common-error";
import { errorSummary, errorToStr } from "../../common-base-module/utils";
import { InStockOutConformCancelDialog, InStockOutPassRefuseDialog } from "./inventory-out-views/out-dialog";
import { ChainClinicsInfo, ClinicAgent, EmployeesMeConfig } from "../../base-business/data/clinic-agent";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { InventoryOutDraftManage } from "./inventory-out-data/inventory-out-draft-manage";
import _ from "lodash";
import { InventoryOutMedicineModifyDialog } from "./inventory-out-views/Inventory-out-medicine-modify-dialog";
import { InventoryAgent } from "../data/inventory-agent";
import { AnyType } from "../../common-base-module/common-types";
import { ABCApiError } from "../../net";
import { ScrollView, View } from "@hippy/react";
import { GoodsAgent } from "../../data/goods/goods-agent";

export class State extends BaseLoadingState {
    /**
     * 门店出库需总部审核
     * 总部审核通过后出库才生效（出库方为总部时不需要审核）
     */
    stockOutChainReview?: boolean; // 出库-总部是否审核

    createType: InventoryOutTabType = InventoryOutTabType.create; // 新增出库

    type?: InventoryOutType; // 新增出库类型

    detail: InventoryOutSheetDetails = new InventoryOutSheetDetails(); // 出库单详情（列表、药品信息）
    _detail?: InventoryOutSheetDetails;

    goodsInfo?: InventoryOutGoodsInfo;

    clinicsList: ChainClinicsInfo[] = [];
    fromOrganId?: string; // 出库门店 ID
    toUserId?: string; // 领用人 ID
    pickingInfo?: PickingDoctors; // 领料人信息

    completeInventoryOut?: PostGoodsStocksOutOrdersReq = new PostGoodsStocksOutOrdersReq(); // 完成出库
    list?: PostGoodsStocksOutOrdersReqList[];

    scrollKeyIndex = 0;
    errorFocusId?: string;
    showErrorHint = false;
    hasChange?: boolean = false;

    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;

    employeesMeConfig?: EmployeesMeConfig;

    //能否查看报损药品价格
    get canViewPrice(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.inventory?.isCanSeeDamageGoodsPrice;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }

    get isCreate(): boolean {
        return this.createType == InventoryOutTabType.create; // 新增出库
    }

    get isPicking(): boolean {
        return this.detail.type == InventoryOutType.picking; // 领料出库
    }

    get isFrmLoss(): boolean {
        return this.detail.type == InventoryOutType.frmLoss; // 报损出库
    }

    get isRevert(): boolean {
        return this.detail.type == InventoryOutType.revert; // 退货出库
    }

    get isProduce(): boolean {
        return this.detail.type == InventoryOutType.produce; // 生产出库
    }

    get isDraft(): boolean {
        return !!this.detail.__draftId; // 出库草稿单
    }

    /**
     * 是否展示金额、共计金额
     * 总部 退货出库（待审核）退货出库（待确认）（已出库）（已拒绝）（已撤回） 显示金额
     * 总部 报损（待确认） 领料、报损（待审核）或是草稿 不显示共计金额
     */
    get displayAmount(): boolean {
        let displayAmount = true;
        if (!this.detail.id) {
            displayAmount = false;
        } else if (
            userCenter.clinic?.isChainAdminClinic &&
            this.detail.status == InventoryOutStatus.waitConfirm &&
            this.detail.type == InventoryOutType.frmLoss
        ) {
            displayAmount = false;
        } else if (
            userCenter.clinic?.isChainAdminClinic &&
            this.detail.status == InventoryOutStatus.waitAgree &&
            this.detail.type == InventoryOutType.picking
        ) {
            displayAmount = false;
        } else if (
            userCenter.clinic?.isChainAdminClinic &&
            this.detail.status == InventoryOutStatus.waitAgree &&
            this.detail.type == InventoryOutType.frmLoss
        ) {
            displayAmount = false;
        }
        {
            return displayAmount;
        }
    }

    employee?: ClinicEmployee;
    clinic?: Clinic;

    isDefault?: boolean;

    lastSearchDetail?: GetInventoryInRsp; // 上次搜索详细信息

    goodsInventoryList: GoodsInventoryInfo[] = [];
}

export class ScrollToCollectionAndDeliveryPerson extends State {
    static fromState(state: State): ScrollToCollectionAndDeliveryPerson {
        const newState = new ScrollToCollectionAndDeliveryPerson();
        Object.assign(newState, state);
        return newState;
    }
}

export class ScrollSameViewState extends State {
    static fromState(state: State): ScrollSameViewState {
        const newState = new ScrollSameViewState();
        Object.assign(newState, state);
        return newState;
    }
}

export class InventoryOutSheetPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<InventoryOutSheetPageBloc | undefined>(undefined);

    static fromContext(context: InventoryOutSheetPageBloc): InventoryOutSheetPageBloc {
        return context;
    }

    private _loadDataTrigger: Subject<number> = new Subject<number>();

    private _id?: string;
    private _draftId?: string;
    private _type?: number;
    private _hasChange = false;
    private _isEditDraft = false;

    private _toUserId?: string;
    private _fromOrganId?: string;
    private _goodsInfo?: GoodsInfo;

    private readonly _draftManager: InventoryOutDraftManage;

    private _pharmacy?: InventoryPharmacyInfo;
    private _createType?: InventoryOutEnterType;

    constructor(options: {
        id?: string;
        type?: InventoryOutType;
        fromOrganId?: string; // 出库门店 ID
        toUserId?: string;
        draftId?: string;
        goodsInfo?: GoodsInfo;
        pharmacy?: InventoryPharmacyInfo;
        createType?: InventoryOutEnterType;
    }) {
        super();
        this._id = options.id;
        this._draftId = options.draftId;
        this._type = options.type;
        this._fromOrganId = options.fromOrganId; // 门店ID
        this._toUserId = options.toUserId; // 领用人 ID
        this._goodsInfo = options.goodsInfo;
        if (this._draftId) {
            this._isEditDraft = true;
        }
        this._pharmacy = options?.pharmacy;

        this._draftManager = InventoryOutDraftManage.inventoryOutDraftManageInstance;
        this._createType = options?.createType ?? InventoryOutEnterType.manual;

        this.dispatch(new _EventInit());
    }

    private draftManager!: InventoryDraftManager;
    private _innerState!: State;

    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    get isOnlineDraft(): boolean {
        return this._createType == InventoryOutEnterType.onlineDraft;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventSelectPickingUser, this._mapEventSelectPickingUser); // 选择领用人
        map.set(_EventSelectClinics, this._mapEventSelectClinics); // 如果是总店 选择出库门店
        map.set(_EventChangeComment, this._mapEventChangeComment); // 修改备注
        map.set(_EventFinishInventoryOut, this._mapEventFinishInventoryOut); // 完成出库 (新增出库)
        map.set(_EventRevokeInventoryOut, this._mapEventRevokeInventoryOut); // 撤销出库
        map.set(_EventConfirmInventoryOut, this._mapEventConfirmInventoryOut); // 子店确认出库
        map.set(_EventConfirmModifyInventoryOut, this._mapEventConfirmModifyInventoryOut); // 总部确认 （修改出库）

        map.set(_EventCancelInventoryOut, this._mapEventCancelInventoryOut); // 取消出库
        map.set(_EventPassInventoryOut, this._mapEventPassInventoryOut); // 通过出库
        map.set(_EventRefuseInventoryOut, this._mapEventRefuseInventoryOut); // 不通过出库

        map.set(_EventAddInventoryOutGoods, this._mapEventAddInventoryOutGoods); // 搜索添加出库药品 (扫码、手动添加)
        map.set(_EventInventoryOutModifyMedicinal, this._mapEventInventoryOutEditDialog); // 将选择药品带入出库编辑弹窗

        map.set(_EventBackPage, this._mapEventBackPage); // 返回提示保存草稿

        map.set(_EventOpenItemTap, this._mapEventOpenItemTap); // 点击打开对应药品
        map.set(_EventDeleteDetailListItem, this._mapEventDeleteDetailListItem); // 删除
        map.set(_EventDeleteInventoryOutDraft, this._mapEventDeleteInventoryOutDraft); // 删除草稿出库单

        return map;
    }

    private _saveDraft(): void {
        if (!this.innerState.isDraft) return;
        this.innerState.detail.lastModifiedDate = new Date();
        this._draftManager.saveDraft(JsonMapper.deserialize(InventoryLossOutOrdersDraft, { ...this.innerState.detail }));
    }

    private _countSummaryInfo(): void {
        let count = 0,
            totalPrice = 0;
        const allGoods: Map<string, InventoryOutSheetList[]> = new Map<string, InventoryOutSheetList[]>();
        this.innerState.detail.list?.forEach((item) => {
            if (!item.goods?.id) return;
            if (allGoods.has(item.goods.id)) {
                const _list = allGoods.get(item.goods.id) ?? [];
                _list.push(item);
                allGoods.set(item.goods.id, _list);
            } else {
                allGoods.set(item.goods.id, [item]);
            }
        });
        allGoods.forEach((item) => {
            let _count = 0,
                _totalPrice = 0;
            item.forEach((medicineInfo) => {
                _count += medicineInfo.outCountWithPackage;
                _totalPrice += medicineInfo.outCountWithPackage * (medicineInfo.packageCostPrice ?? 0);
            });
            count += _count;
            totalPrice += _totalPrice;
        });
        this.innerState.detail.kindCount = allGoods.size;
        this.innerState.detail.count = count;
        this.innerState.detail.amount = totalPrice;
    }

    private _createDetail(): void {
        this.innerState.detail = new InventoryOutSheetDetails();
        this.innerState.detail.fromOrganId = userCenter.clinic?.clinicId; // 当前门店 ID
        this.innerState.detail.fromOrgan = JsonMapper.deserialize(InventoryOutSheetFromOrgan, {
            id: userCenter.clinic?.clinicId,
            name: userCenter.clinic?.name,
            shortName: userCenter.clinic?.isChainAdminClinic ? "总部" : userCenter.clinic?.shortName,
        });

        this.innerState.detail.list = [];

        this.innerState.detail.type = this._type;

        this.innerState.detail.toUserId = this._toUserId; // 领用人 ID

        this.innerState.detail.toOrganId = this._fromOrganId; // 出库门店 ID

        this._draftId = InventoryOutDraftManage.generateDraftId();
        this.innerState.detail.__draftId = this._draftId;
        this.innerState.detail.createdUser = JsonMapper.deserialize(InventoryOutSheetCreatedUser, {
            id: userCenter.employee?.id,
            name: userCenter.employee?.name,
        });
        this.innerState.detail.pharmacy = this._pharmacy;
    }

    // 选择领用人
    public requestSelectPickingUser(doctors: PickingDoctors): void {
        this.dispatch(new _EventSelectPickingUser(doctors));
    }

    // 选择出库门店
    public requestSelectClinics(): void {
        this.dispatch(new _EventSelectClinics());
    }

    // 扫码入库
    public requestScanToInventory(): void {
        this.dispatch(new _EventAddInventoryOutGoods(true));
    }

    // 添加搜索出库药品
    public requestAddInventoryOutMedicine(): void {
        this.dispatch(new _EventAddInventoryOutGoods(false));
    }

    // 完成出库
    public requestFinishInventoryOut(): void {
        this.dispatch(new _EventFinishInventoryOut());
    }

    public requestDeleteInventoryOutDraft(): void {
        this.dispatch(new _EventDeleteInventoryOutDraft());
    }

    // 撤销出库
    public requestRevokeInventoryOut(): void {
        this.dispatch(new _EventRevokeInventoryOut());
    }

    // 子店 确认出库
    public requestConfirmInventoryOut(status: boolean): void {
        this.dispatch(new _EventConfirmInventoryOut(status));
    }

    // 总部 确认(修改)出库
    public requestConfirmModifyInventoryOut(status: boolean): void {
        this.dispatch(new _EventConfirmModifyInventoryOut(status));
    }

    // 取消出库
    public requestCancelInventoryOut(status: boolean): void {
        this.dispatch(new _EventCancelInventoryOut(status));
    }

    // 通过出库
    public requestPassInventoryOut(pass: boolean): void {
        this.dispatch(new _EventPassInventoryOut(pass));
    }

    // 不通过出库
    public requestRefuseInventoryOut(pass: boolean): void {
        this.dispatch(new _EventRefuseInventoryOut(pass));
    }

    // 修改出库单备注
    public requestChangeComment(value: string): void {
        this.dispatch(new _EventChangeComment(value));
    }

    // 点击打开一条药品
    public requestOpenItem(item: InventoryOutSheetList): void {
        this.dispatch(new _EventOpenItemTap(item));
    }

    // 删除一条药品
    public requestDeleteDetailListItem(item: InventoryOutSheetList): void {
        this.dispatch(new _EventDeleteDetailListItem(item));
    }

    private async _initPageConfig(): Promise<void> {
        //获取登录人员权限配置
        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig(true).catchIgnore();
        // 拉取诊所配置
        const inventoryChainConfig = await ClinicAgent.getInventoryChainConfig().catchIgnore();
        this.innerState.stockOutChainReview = !!inventoryChainConfig?.chainReview?.stockOutChainReview;
        //多药房相关配置
        this.innerState.pharmacyInfoConfig = userCenter.inventoryClinicConfig;
        this.draftManager = InventoryDraftManager.instance;
        // 拉取门店列表
        const chainClinics = await ClinicAgent.getChainClinics().catchIgnore();
        this.innerState.clinicsList =
            chainClinics?.map((clinicItem) => {
                if (clinicItem.chainAdmin === 1) {
                    clinicItem.shortName = "总部";
                    clinicItem.name = "总部";
                }
                return clinicItem;
            }) ?? [];

        this.innerState.completeInventoryOut = new PostGoodsStocksOutOrdersReq();
    }

    private _initPageTrigger(): void {
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.startLoading();
                    this.update();
                    return InventoryOutAgent.getInventoryOutSheetOrderDetails({ id: this._id })
                        .then((rsp) => {
                            if (!userCenter.clinic?.isNormalClinic && userCenter.clinic?.chainId == rsp.fromOrgan?.id) {
                                rsp.fromOrgan!.shortName = "总部";
                            }

                            return rsp;
                        })
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.stopLoading();
                if (rsp instanceof ABCError) {
                    this.innerState.setLoadingError(rsp);
                } else {
                    this.innerState.lastSearchDetail = rsp;
                    if (this.innerState.detail) {
                        this.innerState.detail = rsp;
                    } else {
                        this.innerState.detail = rsp;
                    }
                }
                this.update();
            })
            .addToDisposableBag(this);
    }

    private async _initPageData(): Promise<void> {
        // 如果是已有出库单
        if (this._id) {
            this.innerState.createType = InventoryOutTabType.detail;
            this._loadDataTrigger.next();
        } else {
            if (this._draftId && !this.isOnlineDraft) {
                const _draftDetail = await this._draftManager.loadInventoryOutDraft(this._draftId).catchIgnore();
                if (_draftDetail) this.innerState.detail = JsonMapper.deserialize(InventoryOutSheetDetails, { ..._draftDetail });
            } else {
                if (!!this._draftId && this.isOnlineDraft) {
                    const onlineDraftDetail = await InventoryOutAgent.getInventoryOutDraftDetail(this._draftId).catchIgnore();
                    if (onlineDraftDetail) {
                        this.innerState.detail = JsonMapper.deserialize(InventoryOutSheetDetails, { ...onlineDraftDetail });
                        this.innerState.detail.__draftId = this._draftId;
                    }
                    //判断当前草稿是否有本地草稿
                    const list = await InventoryOutDraftManage.inventoryOutDraftManageInstance.getAllDrafts();
                    const localDraft = list.find((it) => it.__draftId == this._draftId);
                    if (!!list.length && !!localDraft) {
                        const result = await showQueryDialog("提示", "当前存在未处理草稿，是否恢复？");
                        if (result == DialogIndex.positive) {
                            this.innerState.detail = JsonMapper.deserialize(InventoryOutSheetDetails, { ...localDraft });
                        }
                    }
                } else {
                    // 如果是新增出库单
                    this._createDetail();
                }
            }
            this.innerState._detail = _.cloneDeep(this.innerState.detail);

            this.innerState.stopLoading();
        }
        if (this._goodsInfo) {
            const outMedicine = JsonMapper.deserialize(InventoryOutSheetList, {
                goods: this._goodsInfo,
                goodsId: this._goodsInfo.id,
                pieceNum: this._goodsInfo.pieceNum,
                batchId: undefined,

                packageCount: undefined, // 包计数
                packagePrice: this._goodsInfo.packagePrice,
                pieceCount: undefined, // 账面数量
                piecePrice: this._goodsInfo.piecePrice, //
            });

            this.dispatch(new _EventInventoryOutModifyMedicinal(outMedicine));
        }
    }

    private async *_mapEventInit(/*ignore: _EventInit*/): AsyncGenerator<State> {
        await this._initPageConfig();
        this._initPageTrigger();
        await this._initPageData();
        yield this.innerState.clone();
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }

        yield this.innerState.clone();
    }

    /**
     * 新增出库-选择领用人
     * @private
     */

    private async *_mapEventSelectPickingUser(event: _EventSelectPickingUser): AsyncGenerator<State> {
        this.innerState.pickingInfo = event.doctors;
        if (!this.innerState.detail.toOrgan) this.innerState.detail.toOrgan = new InventoryOutToOrgan();
        if (!this.innerState.detail.toUser) this.innerState.detail.toUser = new InventoryOutToUser();
        this.innerState.detail.toOrgan.id = event.doctors.departmentId;
        this.innerState.detail.toOrgan.name = event.doctors.departmentName;
        this.innerState.detail.toOrganId = this.innerState.pickingInfo.departmentId;
        this.innerState.detail.toUserId = this.innerState.pickingInfo.employeeId;
        this.innerState.detail.toUser.id = this.innerState.pickingInfo.employeeId;
        this.innerState.detail.toUser.name = this.innerState.pickingInfo.employeeName;
        this.innerState.hasChange = true;
        this._saveDraft();
        yield this.innerState.clone();
    }

    /**
     * 当前是总店的报损出库/退货出库（选择出库门店）
     * @private
     */

    private async *_mapEventSelectClinics(/*event: _EventSelectClinics*/): AsyncGenerator<State> {
        let currentClinic: InventoryOutOrgan | undefined = undefined; // 当前门店
        if (this.innerState.clinicsList.length) {
            currentClinic = this.innerState.detail?.fromOrgan; // 出库门店
        }

        const index = this.innerState.clinicsList.findIndex((item) => item.id == currentClinic?.id);

        let newIndexSet = undefined;
        if (index > -1) {
            newIndexSet = new Set([index]);
        }
        const fromOrgan = await showOptionsBottomSheet({
            title: "选择出库门店",
            options: this.innerState.clinicsList.map((item) => item.shortName ?? item.name),
            initialSelectIndexes: newIndexSet,
        });
        if (fromOrgan && !!fromOrgan.length) {
            // 刷新选中clinic
            const clinic = this.innerState.clinicsList[fromOrgan[0]]; // 当前选中项
            if (clinic.id == this.innerState.detail.fromOrgan?.id) {
                return; // 当前未更改门店返回
            } else {
                if (this.innerState.detail.list?.length) {
                    const result = await showQueryDialog("", "切换出库门店，已选择的药品将清空");
                    if (result == DialogIndex.positive) {
                        this.innerState.detail.list = []; // 清空入库单
                        this.innerState.detail!.fromOrgan = JsonMapper.deserialize(GoodsCheckOrdersItemOrgan, clinic);
                        this.innerState.detail!.fromOrganId = clinic.id;
                    }
                } else {
                    this.innerState.detail!.fromOrgan = JsonMapper.deserialize(GoodsCheckOrdersItemOrgan, clinic);
                    this.innerState.detail!.fromOrganId = clinic.id;
                }

                this._saveDraft();
                this.innerState.hasChange = true;
            }
        }
        this.update();
    }

    /**
     * 修改出库单(备注)
     * @private
     */
    private async *_mapEventChangeComment(event: _EventChangeComment): AsyncGenerator<State> {
        const detail = this.innerState.detail;

        const commentItem = JsonMapper.deserialize(InventoryOutSheetComment, {
            content: event.comment,
        });
        if (detail.comment) {
            detail.comment[0] = commentItem;
        } else {
            detail.comment = [];
            detail.comment.push(commentItem);
        }
        this._saveDraft();
        this.innerState.hasChange = true;
        this.update();
    }

    /**
     * 搜索输入的出库药品
     * @param event
     * @private
     */
    private async *_mapEventAddInventoryOutGoods(event: _EventAddInventoryOutGoods): AsyncGenerator<State> {
        let goodsInfo: GoodsInfo | undefined;
        const params = {
            withStock: "1",
            onlyStock: "1",
            clinicId: this.innerState.detail.fromOrgan?.id,
            disable: 0,
            pharmacyNo: userCenter.clinic?.isChainAdminClinic ? undefined : this.innerState.detail.pharmacy?.no,
        };

        if (event.qrScan) {
            goodsInfo = await InventoryUtils.BarScan2GoodsInfo(params);
        } else {
            goodsInfo = await ABCNavigator.navigateToPage<GoodsInfo | undefined>(
                <InventoryMedicineSearchPage
                    displayStock={true}
                    placeholder={"输入药品名称/条形码搜索"}
                    searchParams={params}
                    callback={async (goodsInfo) => {
                        if (!goodsInfo?.hasInventoryStock) {
                            await Toast.show("当前药品库存为0，不可出库", { warning: true });
                            return;
                        }
                        ABCNavigator.pop(goodsInfo);
                    }}
                />
            );
        }

        if (!goodsInfo || !goodsInfo.id) return;
        goodsInfo = await GoodsAgent.getGoodsInfo(goodsInfo.id, {
            withStock: 1,
            clinicId: this.innerState.detail.fromOrgan?.id ?? userCenter.clinic?.clinicId,
            pharmacyNo: userCenter.clinic?.isChainAdminClinic ? undefined : this.innerState.detail.pharmacy?.no,
        });

        if (!goodsInfo?.hasInventoryStock) {
            Toast.show("当前药品库存为0，不可出库", { warning: true });
            return;
        }

        let index = 0;
        for (const item of this.innerState.detail?.list ?? []) {
            // 判断药品是否相同
            if (item?.goodsId == goodsInfo.id && !item.batchId) {
                this.innerState.scrollKeyIndex = index;
                yield ScrollSameViewState.fromState(this.innerState);
                this.dispatch(new _EventOpenItemTap(item));
                return;
            }
            index++;
        }

        const outMedicine = JsonMapper.deserialize(InventoryOutSheetList, {
            goodsId: goodsInfo.id,
            pieceNum: goodsInfo.pieceNum,
            batchId: undefined,

            packageCount: undefined, // 包计数
            packagePrice: goodsInfo.packagePrice,
            pieceCount: undefined, // 账面数量
            piecePrice: goodsInfo.piecePrice, //
        });
        //避免__apiOutPackageCount的值变成了自定义的outPackageCount,而不是后台返回的字段
        outMedicine.goods = goodsInfo;

        this.dispatch(new _EventInventoryOutModifyMedicinal(outMedicine));
    }

    /**
     * 修改选择的出库药品 (批次 出库数量)
     * @param event
     * @private
     */

    private async *_mapEventInventoryOutEditDialog(event: _EventInventoryOutModifyMedicinal): AsyncGenerator<State> {
        let editResult: InventoryOutSheetList | undefined = undefined,
            index = 0,
            status = false;

        do {
            editResult = await InventoryOutMedicineModifyDialog.show({
                item: event.item,
                clinicId: this.innerState.detail.fromOrgan?.id,
                pharmacyNo: this.innerState.detail.pharmacy?.no,
                isDraft: this.innerState.isDraft,
                pharmacyType: this.innerState.detail.pharmacy?.type,
            });
            if (!editResult) return;
            index = 0;
            status = false;
            for (const item of this.innerState.detail?.list ?? []) {
                //判断药品是否相同
                if (item.goods?.id == event.item.goods?.id) {
                    if (editResult.batchId == undefined || item.compareKey() == editResult.compareKey()) {
                        status = true;
                        this.innerState.scrollKeyIndex = index;
                        yield ScrollSameViewState.fromState(this.innerState);
                        if (editResult.batchId == undefined) await Toast.show("当前商品未选择批次");
                        if (item.compareKey() == editResult.compareKey()) await Toast.show("当前商品批次已存在");
                        break;
                    }
                }
                index++;
            }
        } while (status);

        if (!editResult) return;
        this.innerState.detail!.list = this.innerState.detail?.list ?? [];

        this.innerState.detail!.list.push(editResult);
        this.innerState.scrollKeyIndex = (this.innerState.detail?.list.length ?? 1) - 1;
        this._countSummaryInfo();

        this._saveDraft();
        this.innerState.hasChange = true;
        this.update();
    }

    /**
     * 新增 完成出库
     * @private
     */
    private async *_mapEventFinishInventoryOut(/*event: _EventFinishInventoryOut*/): AsyncGenerator<State> {
        const { comment, list, toOrganId, toUserId, type, fromOrgan, pharmacy } = this.innerState.detail;
        const { stockOutChainReview } = this.innerState;

        // 无领用人ID（toUserId）提示错误
        if (type == InventoryOutType.picking && !toUserId) {
            this.innerState.showErrorHint = true;
            yield ScrollToCollectionAndDeliveryPerson.fromState(this.innerState);
            return Toast.show(`领用人不能为空`, { warning: true });
        }

        // 没有填入药品信息 提示错误
        if (!this.innerState.detail.list?.length) {
            return Toast.show("未添加药品", { warning: true });
        }

        // 如果子店有配置(出库需总部审核)
        if (stockOutChainReview && userCenter.clinic?.isChainSubClinic) {
            const result = await showQueryDialog("", "确定后将发给总部审核，审核通过后将立即更新库存");
            if (result != DialogIndex.positive) return;
        }

        // 总店出库提示
        if (userCenter.clinic?.isChainAdminClinic && userCenter.clinic?.chainId == fromOrgan?.id && type == InventoryOutType.picking) {
            const result = await showQueryDialog("", "领料出库后将会立即更新系统库存"); // 总店 领料 出库门店为总店
            if (result != DialogIndex.positive) return;
        } else if (
            userCenter.clinic?.isChainAdminClinic &&
            userCenter.clinic?.chainId == fromOrgan?.id &&
            type == InventoryOutType.frmLoss
        ) {
            const result = await showQueryDialog("", "报损出库后将会立即更新系统库存"); // 总店 报损 出库门店为总店
            if (result != DialogIndex.positive) return;
        } else if (
            userCenter.clinic?.isChainAdminClinic &&
            userCenter.clinic?.chainId == fromOrgan?.id &&
            type == InventoryOutType.revert
        ) {
            const result = await showQueryDialog("", "退货出库后将会立即更新系统库存"); // 总店 退货 出库门店为总店
            if (result != DialogIndex.positive) return;
        } else if (userCenter.clinic?.isChainAdminClinic && userCenter.clinic?.chainId !== fromOrgan?.id) {
            const result = await showQueryDialog("", `出库单需要${fromOrgan?.displayName}连锁确认`); // 总店 出库门店不为总店
            if (result != DialogIndex.positive) return;
        }
        //如果指定了批次，则应该清空外层的库存数量信息
        list?.map((item) => {
            if ((item.batchs?.length ?? 0) > 0) {
                item.packageCount = 0;
                item.pieceCount = 0;
            }
        });

        // 后台post请求参数整理
        const finishOutMedicineDetail = JsonMapper.deserialize(PostGoodsStocksOutOrdersReq, {
            comment: comment?.[0]?.content,
            fromOrganId: fromOrgan?.id, // 出库门店 ID
            list: list, //（goodsId、packageCount、pieceCount）
            toOrganId: toOrganId, // 出库人 ID
            toUserId: toUserId, // 领用人 ID
            type: type, // 1.领料出库  2.报损出库 3.退货出库
            pharmacyNo: pharmacy?.no,
            outOrderDraftId: this.isOnlineDraft ? this._draftId : undefined,
        });

        InventoryOutAgent.postFinishInventoryOutSheet({
            ...finishOutMedicineDetail,
        })
            .then(() => {
                if (this._draftId) this._draftManager.removeDraft(this._draftId);
                InventoryOutAgent.inventoryOutStatusPublisher.next();
                /**
                 * 商品 直接出库，刷新商品信息
                 */
                InventoryAgent.medicineRefreshPublisher.next();
                Toast.show("创建成功", { success: true }).then(() => ABCNavigator.pop());
            })
            .catch((error) => {
                const err = error?.detail.error;
                if (err?.code == InventoryApiErrorCode.goodsDeleted) {
                    const goodsInfo = JsonMapper.deserialize(GoodsInfo, err.detail);
                    showQueryDialog("创建失败", `商品“${goodsInfo.displayName}”已经被删除`).then(() => {
                        this.innerState.detail!.list = this.innerState.detail?.list?.filter(
                            (item) => item.goods?.id !== err.detail.goodsId
                        );
                        this.update();
                    });
                } else if (err?.code == InventoryApiErrorCode.goodsDisabled) {
                    const goodsInfo = JsonMapper.deserialize(GoodsInfo, err.detail);
                    showQueryDialog("创建失败", `商品“${goodsInfo.displayName}”已经被停用`).then(() => {
                        this.innerState.detail!.list = this.innerState.detail?.list?.filter((item) => item.goods?.id !== err.detail.id);
                        this.update();
                    });
                } else if (err?.code == InventoryApiErrorCode.goodsStockNotEnough) {
                    const goodsInfos: (GoodsInfo | undefined)[] | undefined =
                        err.detail?.map((item: AnyType) => JsonMapper.deserialize(GoodsInfo, item.goods)) ?? [];
                    const displayName = goodsInfos?.map((item) => item?.displayName)?.join("、");
                    showConfirmDialog("创建失败", `商品“${displayName}”库存不足`);
                } else if (err?.code == InventoryApiErrorCode.alreadyUpdate) {
                    showQueryDialog("创建失败", `当前库存信息已被修改，请刷新`);
                } else if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode) {
                    //处理12100异常code
                    const { detail } = error?.detail?.error ?? {};
                    showConfirmDialog(
                        `创建失败：${detail?.errorTitle ?? ""}`,
                        <ScrollView>
                            <View>
                                {detail.errorList
                                    .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                    .join("\n")}
                            </View>
                        </ScrollView>
                    );
                } else if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.submitted) {
                    showQueryDialog("提示", error?.detail?.error?.message).then((index) => {
                        if (index == DialogIndex.positive) {
                            finishOutMedicineDetail.forceSubmit = 1;
                            InventoryOutAgent.postFinishInventoryOutSheet({
                                ...finishOutMedicineDetail,
                            })
                                .then(() => {
                                    if (this._draftId) this._draftManager.removeDraft(this._draftId);
                                    InventoryOutAgent.inventoryOutStatusPublisher.next();
                                    /**
                                     * 商品 直接出库，刷新商品信息
                                     */
                                    InventoryAgent.medicineRefreshPublisher.next();
                                    Toast.show("创建成功", { success: true }).then(() => ABCNavigator.pop());
                                })
                                .catch((error) => {
                                    showQueryDialog("提示", errorToStr(error));
                                });
                        } else {
                            if (this._draftId) this._draftManager.removeDraft(this._draftId);
                            InventoryOutAgent.inventoryOutStatusPublisher.next();
                            /**
                             * 商品 直接出库，刷新商品信息
                             */
                            InventoryAgent.medicineRefreshPublisher.next();
                            ABCNavigator.pop();
                        }
                    });
                } else {
                    showQueryDialog("提示", errorToStr(error));
                }
            });
        this.update();
    }

    /**
     * 撤销出库
     * @private
     */
    private async *_mapEventRevokeInventoryOut(/*event: _EventRevokeInventoryOut*/): AsyncGenerator<State> {
        if (!this.innerState.detail.list?.length) return;
        const result = await showQueryDialog("", "是否确认撤回该申请，撤回后单据将失效");
        if (result != DialogIndex.positive) return;

        InventoryOutAgent.putRevokeInventoryOutSheet(this.innerState.detail.id)
            .then(() => {
                InventoryOutAgent.inventoryOutStatusPublisher.next();
                Toast.show("撤回成功", { success: true }).then(() => ABCNavigator.pop("success"));
            })
            .catch((err) => {
                Toast.show("撤回失败:" + errorToStr(err.detail), { warning: true });
            });
        this.update();
    }

    /**
     * 子店确认出库 (确认出库后将会实时更新库存信息)
     * @private
     */
    private async *_mapEventConfirmInventoryOut(event: _EventConfirmInventoryOut): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        const result = await InStockOutConformCancelDialog.show({
            pass: event.status,
            reviewOrgan: detail.fromOrgan?.displayName,
        });

        // 确认出库
        if (result) {
            const req: PutGoodsStocksOutOrdersRejectReq = {
                comment: "",
                lastModifiedDate: detail?.lastModifiedDate,
            };
            InventoryOutAgent.putConfirmInventoryOutSheet(detail!.id!, req)
                .then(() => ABCNavigator.pop())
                .catch((error) => {
                    const err = error?.detail.error;
                    if (err?.code == InventoryApiErrorCode.goodsDeleted) {
                        const goodsInfo = JsonMapper.deserialize(GoodsInfo, err.detail);
                        showQueryDialog("提示", `商品“${goodsInfo.displayName}”已经被删除`).then(() => {
                            this.innerState.detail!.list = this.innerState.detail?.list?.filter(
                                (item) => item.goods?.id !== err.detail.goodsId
                            );
                            this.update();
                        });
                    } else if (err?.code == InventoryApiErrorCode.goodsDisabled) {
                        const goodsInfo = JsonMapper.deserialize(GoodsInfo, err.detail);
                        showQueryDialog("提示", `商品“${goodsInfo.displayName}”已经被停用`).then(() => {
                            this.innerState.detail!.list = this.innerState.detail?.list?.filter((item) => item.goods?.id !== err.detail.id);
                            this.update();
                        });
                    } else if (err?.code == InventoryApiErrorCode.goodsStockNotEnough) {
                        const goodsInfos: (GoodsInfo | undefined)[] | undefined =
                            err.detail?.map((item: AnyType) => JsonMapper.deserialize(GoodsInfo, item.goods)) ?? [];
                        const displayName = goodsInfos?.map((item) => item?.displayName)?.join("、");
                        showConfirmDialog("提示", `商品“${displayName}”库存不足`);
                    } else if (err?.code == InventoryApiErrorCode.alreadyUpdate) {
                        showQueryDialog("提示", `当前库存信息已被修改，请刷新`);
                    } else if (
                        error instanceof ABCApiError &&
                        error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode
                    ) {
                        //处理12100异常code
                        const { detail } = error?.detail?.error ?? {};
                        showConfirmDialog(
                            `提交失败：${detail?.errorTitle ?? ""}`,
                            <ScrollView>
                                <View>
                                    {detail.errorList
                                        .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                        .join("\n")}
                                </View>
                            </ScrollView>
                        );
                    } else {
                        showQueryDialog("提示", errorToStr(error));
                    }
                });
        }
        this.update();
    }

    /**
     * 总部确认（修改）出库 (确认修改出库数量 更新库存信息)
     * @private
     */
    private async *_mapEventConfirmModifyInventoryOut(/*event: _EventConfirmModifyInventoryOut*/): AsyncGenerator<State> {
        const { list } = this.innerState.detail;
        const detail = this.innerState.detail;

        // 总店修改出库单后，需要 当前诊所 确认
        const result = await showQueryDialog("", `修改出库单后，需要 ${this.innerState.detail.fromOrgan?.name} 确认`);
        if (result == DialogIndex.positive) {
            // 发送确认出库请求
            if (this._hasChange) {
                return Toast.show("未做任何修改", { warning: true }); // 如果未修改
            } else if (result) {
                const currentList = list?.map((item) => {
                    const currentStockCount = JsonMapper.deserialize(PutGoodsStocksOutOrdersReqItemCurrentStockCount, {
                        packageCount: item.packageCount,
                        pieceCount: item.pieceCount,
                    });
                    const currentStock = JsonMapper.deserialize(PutGoodsStocksOutOrdersReqItemStock, {
                        batchId: item.batchId,
                        batchNo: item.batchNo,
                        expiryDate: item.stock?.expiryDate,
                        packageCostPrice: item.packageCostPrice,
                    });
                    return JsonMapper.deserialize(PutGoodsStocksOutOrdersReqItem, {
                        batchId: item.batchId,
                        currentStockCount: currentStockCount,
                        goods: item.goods,
                        goodsId: item.goodsId,
                        id: item.id,
                        orderId: item.orderId,
                        packageCostPrice: item.packageCostPrice,
                        packageCount: item.packageCount,
                        packagePrice: item.packagePrice,
                        pieceCount: item.packageCount,
                        pieceNum: item.pieceNum,
                        piecePrice: item.piecePrice,
                        stock: currentStock,
                        stockId: item.stockId,
                        stockInId: item.stockInId,
                        totalCost: item.totalCost,
                        totalCostE: item.totalCostE,
                    });
                });
                const req: PutGoodsStocksOutOrdersReq = {
                    comment: "",
                    lastModifiedDate: detail?.lastModifiedDate,
                    list: currentList,
                };
                InventoryOutAgent.putConfirmModifyInventoryOutSheet(detail!.id!, req)
                    .then(() => {
                        Toast.show("保存成功", { success: true }).then(() => ABCNavigator.pop());
                    })
                    .catch((error) => {
                        const err = error?.detail.error;
                        if (err?.code == InventoryApiErrorCode.goodsDeleted) {
                            const goodsInfo = JsonMapper.deserialize(GoodsInfo, err.detail);
                            showQueryDialog("提示", `商品“${goodsInfo.displayName}”已经被删除`).then(() => {
                                this.innerState.detail!.list = this.innerState.detail?.list?.filter(
                                    (item) => item.goods?.id !== err.detail.goodsId
                                );
                                this.update();
                            });
                        } else if (err?.code == InventoryApiErrorCode.goodsDisabled) {
                            const goodsInfo = JsonMapper.deserialize(GoodsInfo, err.detail);
                            showQueryDialog("提示", `商品“${goodsInfo.displayName}”已经被停用`).then(() => {
                                this.innerState.detail!.list = this.innerState.detail?.list?.filter(
                                    (item) => item.goods?.id !== err.detail.id
                                );
                                this.update();
                            });
                        } else if (err?.code == InventoryApiErrorCode.goodsStockNotEnough) {
                            const goodsInfos: (GoodsInfo | undefined)[] | undefined =
                                err.detail?.map((item: AnyType) => JsonMapper.deserialize(GoodsInfo, item.goods)) ?? [];
                            const displayName = goodsInfos?.map((item) => item?.displayName)?.join("、");
                            showConfirmDialog("提示", `商品“${displayName}”库存不足`);
                        } else if (err?.code == InventoryApiErrorCode.alreadyUpdate) {
                            showQueryDialog("提示", `当前库存信息已被修改，请刷新`);
                        } else if (
                            error instanceof ABCApiError &&
                            error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode
                        ) {
                            //处理12100异常code
                            const { detail } = error?.detail?.error ?? {};
                            showConfirmDialog(
                                `提交失败：${detail?.errorTitle ?? ""}`,
                                <ScrollView>
                                    <View>
                                        {detail.errorList
                                            .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                            .join("\n")}
                                    </View>
                                </ScrollView>
                            );
                        } else {
                            showQueryDialog("提示", errorToStr(error));
                        }
                    });
            }
            this.update();
        }
    }

    /**
     * 取消出库 (确认出库后将会实时更新库存信息)
     * @private
     */

    private async *_mapEventCancelInventoryOut(event: _EventConfirmInventoryOut): AsyncGenerator<State> {
        const result = await InStockOutConformCancelDialog.show({ pass: event.status });
        if (result) {
            const detail = this.innerState.detail;
            InventoryOutAgent.putCancelInventoryOutSheet(detail!.id!, {
                comment: result.comment ?? "",
                lastModifiedDate: detail?.lastModifiedDate,
            })
                .then(() => ABCNavigator.pop())
                .catch((e) => {
                    showConfirmDialog("提示", errorToStr(e));
                });
        }
        this.update();
    }

    /**
     * 通过出库 (确认出库后将会实时更新库存信息)
     * @private
     */
    private async *_mapEventPassInventoryOut(event: _EventPassInventoryOut): AsyncGenerator<State> {
        const fromOrgan = this.innerState.detail.fromOrgan;
        const result = await InStockOutPassRefuseDialog.show({ pass: event.pass, reviewOrgan: fromOrgan?.displayName });
        if (result) {
            const detail = this.innerState.detail;
            // 通过出库
            const req: PutGoodsStocksOutOrdersConfirmRep = {
                comment: "",
                lastModifiedDate: detail?.lastModifiedDate,
                list: detail.list,
                pass: event.pass,
            };
            InventoryOutAgent.putPassInventoryOutSheet(detail!.id!, req)
                .then(() => ABCNavigator.pop())
                .catch((error) => {
                    const err = error?.detail.error;
                    if (err?.code == InventoryApiErrorCode.goodsDeleted) {
                        const goodsInfo = JsonMapper.deserialize(GoodsInfo, err.detail);
                        showQueryDialog("提示", `商品“${goodsInfo.displayName}”已经被删除`).then(() => {
                            this.innerState.detail!.list = this.innerState.detail?.list?.filter(
                                (item) => item.goods?.id !== err.detail.goodsId
                            );
                            this.update();
                        });
                    } else if (err?.code == InventoryApiErrorCode.goodsDisabled) {
                        const goodsInfo = JsonMapper.deserialize(GoodsInfo, err.detail);
                        showQueryDialog("提示", `商品“${goodsInfo.displayName}”已经被停用`).then(() => {
                            this.innerState.detail!.list = this.innerState.detail?.list?.filter((item) => item.goods?.id !== err.detail.id);
                            this.update();
                        });
                    } else if (err?.code == InventoryApiErrorCode.goodsStockNotEnough) {
                        const goodsInfos: (GoodsInfo | undefined)[] | undefined =
                            err.detail?.map((item: AnyType) => JsonMapper.deserialize(GoodsInfo, item.goods)) ?? [];
                        const displayName = goodsInfos?.map((item) => item?.displayName)?.join("、");
                        showConfirmDialog("提示", `商品“${displayName}”库存不足`);
                    } else if (err?.code == InventoryApiErrorCode.alreadyUpdate) {
                        showQueryDialog("提示", `当前库存信息已被修改，请刷新`);
                    } else if (
                        error instanceof ABCApiError &&
                        error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode
                    ) {
                        //处理12100异常code
                        const { detail } = error?.detail?.error ?? {};
                        showConfirmDialog(
                            `提交失败：${detail?.errorTitle ?? ""}`,
                            <ScrollView>
                                <View>
                                    {detail.errorList
                                        .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                        .join("\n")}
                                </View>
                            </ScrollView>
                        );
                    } else {
                        showQueryDialog("提示", errorToStr(error));
                    }
                });
        }
        this.update();
    }

    /**
     * 不通过出库 (不通过出库后 填写不通过原因)
     * @private
     */
    private async *_mapEventRefuseInventoryOut(event: _EventRefuseInventoryOut): AsyncGenerator<State> {
        const fromOrgan = this.innerState.detail.fromOrgan;
        const result = await InStockOutPassRefuseDialog.show({ pass: event.pass, reviewOrgan: fromOrgan?.displayName });
        if (result) {
            const detail = this.innerState.detail;
            InventoryOutAgent.putRefuseInventoryOutSheet(detail!.id!, {
                comment: result.comment ?? "",
                lastModifiedDate: detail?.lastModifiedDate,
                list: detail.list,
                pass: event.pass,
            })
                .then(() => ABCNavigator.pop())
                .catch((e) => {
                    showConfirmDialog("提示", errorToStr(e));
                });
        }
        this.update();
    }

    /**
     * 出库单(未保存、提交)的提示
     * @private
     */
    private async *_mapEventBackPage(/*event: _EventBackPage*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        if (!!this._draftId && innerState.hasChange) {
            if (!this._isEditDraft || !this.isOnlineDraft) {
                const result = await showQueryDialog("是否保存", "是否需要保存成草稿");
                if (result == DialogIndex.negative && !!this._draftId) {
                    this._draftManager.removeDraft(this._draftId!).then(() => {
                        ABCNavigator.pop();
                    });
                } else if (result == DialogIndex.positive) {
                    InventoryOutAgent.postInventoryOutDraft(innerState.detail.toInventoryUpdateReq())
                        .then(async (rsp) => {
                            //保存线上草稿成功,删除本地草稿
                            if (rsp?.code == 200) {
                                await this._draftManager.removeDraft(this._draftId!);
                            }
                            ABCNavigator.pop();
                        })
                        .catch((e) => {
                            showConfirmDialog("保存失败", errorSummary(e));
                        });
                }
            } else {
                if (innerState.hasChange) {
                    const result = await showQueryDialog("", "草稿信息发生变动，是否保存？");
                    if (result == DialogIndex.positive) {
                        InventoryOutAgent.putInventoryOutDraft(this._draftId!, innerState.detail.toInventoryUpdateReq())
                            .then((rsp) => {
                                //保存线上草稿成功,删除本地草稿
                                if (rsp?.code == 200) {
                                    this._draftManager.removeDraft(this._draftId!);
                                }
                                ABCNavigator.pop();
                            })
                            .catch((error) => {
                                if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.saved) {
                                    showQueryDialog("", error?.detail?.error?.message).then((operateResult) => {
                                        if (operateResult == DialogIndex.positive) {
                                            const req = innerState.detail.toInventoryUpdateReq();
                                            req.forceSubmit = 1;
                                            InventoryOutAgent.postInventoryOutDraft(req)
                                                .then((rsp) => {
                                                    //保存线上草稿成功,删除本地草稿
                                                    if (rsp?.code == 200) {
                                                        this._draftManager.removeDraft(this._draftId!);
                                                    }
                                                })
                                                .catch((e) => {
                                                    showConfirmDialog("保存失败", errorSummary(e));
                                                });
                                        } else {
                                            this._draftManager.removeDraft(this._draftId!);
                                        }
                                        ABCNavigator.pop();
                                    });
                                } else {
                                    showConfirmDialog("保存失败", errorSummary(error));
                                }
                            });
                    } else {
                        this._draftManager.removeDraft(this._draftId!);
                        ABCNavigator.pop();
                    }
                }
            }
        } else {
            ABCNavigator.pop();
        }
    }

    /**
     * 点击打开一条药品
     * @param event
     * @private
     */
    private async *_mapEventOpenItemTap(event: _EventOpenItemTap): AsyncGenerator<State> {
        let status = true;
        let editResult: InventoryOutSheetList | undefined = undefined;
        let index = 0;
        do {
            editResult = await InventoryOutMedicineModifyDialog.show({
                item: event.item,
                clinicId: this.innerState.detail.fromOrgan?.id,
                pharmacyNo: this.innerState.detail.pharmacy?.no,
                isDraft: this.innerState.isDraft,
                pharmacyType: this.innerState.detail.pharmacy?.type,
                isDetailInto: true,
            });
            if (!editResult) return;
            index = 0;
            status = false;
            for (const item of this.innerState.detail?.list ?? []) {
                //判断药品是否相同
                if (item.goods?.id == event.item.goods?.id) {
                    if (
                        (editResult.batchId == undefined || item.compareKey() == editResult.compareKey()) &&
                        item.compareKey() != event.item.compareKey()
                    ) {
                        status = true;
                        this.innerState.scrollKeyIndex = index;
                        yield ScrollSameViewState.fromState(this.innerState);
                        if (editResult.batchId == undefined) await Toast.show("当前商品未选择批次");
                        if (item.compareKey() == editResult.compareKey()) await Toast.show("当前商品批次已存在");
                        break;
                    }
                }
                index++;
            }
        } while (status);
        const currentIndex =
            this.innerState.detail.list?.findIndex(
                (item) => item.batchId == event.item?.batchId && item.goods?.id == event.item?.goods?.id
            ) ?? -1;
        if (currentIndex > -1) {
            this.innerState.detail.list![currentIndex] = editResult;
            this._countSummaryInfo();
            this.innerState.hasChange = true;
            this._saveDraft();
            this.update();
        }
    }

    private async *_mapEventDeleteDetailListItem(event: _EventDeleteDetailListItem): AsyncGenerator<State> {
        const item = event.item;
        _.remove(this.innerState.detail.list ?? [], item);
        this.innerState.hasChange = true;
        this._saveDraft();
        this._countSummaryInfo();
        this.update();
    }

    private async *_mapEventDeleteInventoryOutDraft(): AsyncGenerator<State> {
        const result = await showQueryDialog("", "是否删除草稿");
        if (result == DialogIndex.positive) {
            if (this._draftId)
                this._draftManager.removeDraft(this._draftId).then(() => {
                    ABCNavigator.pop();
                });
        }
    }

    public requestBackPage(fromEdgeGesture?: boolean): void {
        this.dispatch(new _EventBackPage(fromEdgeGesture));
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state));
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventChangeComment extends _Event {
    comment?: string;

    constructor(comment?: string) {
        super();
        this.comment = comment;
    }
}

class _EventFinishInventoryOut extends _Event {}

class _EventDeleteInventoryOutDraft extends _Event {}

class _EventRevokeInventoryOut extends _Event {}

class _EventConfirmInventoryOut extends _Event {
    status: boolean;

    constructor(status: boolean) {
        super();
        this.status = status;
    }
}

class _EventConfirmModifyInventoryOut extends _Event {
    status: boolean;

    constructor(status: boolean) {
        super();
        this.status = status;
    }
}

class _EventCancelInventoryOut extends _Event {
    status: boolean;

    constructor(status: boolean) {
        super();
        this.status = status;
    }
}

class _EventPassInventoryOut extends _Event {
    pass: boolean;

    constructor(pass: boolean) {
        super();
        this.pass = pass;
    }
}

class _EventRefuseInventoryOut extends _Event {
    pass: boolean;

    constructor(pass: boolean) {
        super();
        this.pass = pass;
    }
}

class _EventOpenItemTap extends _Event {
    item: InventoryOutSheetList;

    constructor(item: InventoryOutSheetList) {
        super();
        this.item = item;
    }
}

class _EventDeleteDetailListItem extends _Event {
    item: InventoryOutSheetList;

    constructor(item: InventoryOutSheetList) {
        super();
        this.item = item;
    }
}

class _EventSelectPickingUser extends _Event {
    doctors: PickingDoctors;

    constructor(source: PickingDoctors) {
        super();
        this.doctors = source;
    }
}

class _EventSelectClinics extends _Event {}

class _EventAddInventoryOutGoods extends _Event {
    qrScan: boolean;

    constructor(qrScan: boolean) {
        super();
        this.qrScan = qrScan;
    }
}

class _EventInventoryOutModifyMedicinal extends _Event {
    item: InventoryOutSheetList;

    constructor(item: InventoryOutSheetList) {
        super();
        this.item = item;
    }
}

class _EventBackPage extends _Event {
    fromEdgeGesture?: boolean;

    constructor(fromEdgeGesture?: boolean) {
        super();
        this.fromEdgeGesture = fromEdgeGesture;
    }
}
