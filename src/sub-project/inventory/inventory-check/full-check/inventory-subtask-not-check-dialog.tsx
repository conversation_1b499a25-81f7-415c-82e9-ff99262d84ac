/**
 * create by deng<PERSON><PERSON>
 * desc:
 * create date 2020/12/1
 */
import React from "react";
import { ListView, Text, View } from "@hippy/react";
import { BaseComponent } from "../../../base-ui/base-component";
import { <PERSON>alog<PERSON><PERSON>er, DialogButtonBuilder, DialogIndex, showQueryDialog } from "../../../base-ui/dialog/dialog-builder";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import { JsonMapper } from "../../../common-base-module/json-mapper/json-mapper";
import { CheckTaskGoodItem, SummaryTaskGoodItem } from "../../data/inventory-bean";
import { ignore } from "../../../common-base-module/global";
import { AnyType } from "../../../common-base-module/common-types";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lBarButtonStyle2 } from "../../../base-ui";
import { showBottomPanel } from "../../../base-ui/abc-app-library/panel";
import { SafeAreaBottomView } from "../../../base-ui/safe_area_view";
import { pxToDp } from "../../../base-ui/utils/ui-utils";
import { BottomSheetCloseButton } from "../../../base-ui/abc-app-library";

export enum InventorySubTaskNotCheckDialogAction {
    keep, // 保持
    changZero, // 盘0
    generatingDraft, //     生成草稿
}

export class InventorySubTaskNotCheckDialogResult {
    action?: InventorySubTaskNotCheckDialogAction;
}

interface InventorySubTaskNotCheckDialogProps {
    title?: string;
    detail?: CheckTaskGoodItem[];
    showKeep?: boolean;
    readonly?: boolean;
    canGenerateDraft?: boolean;
}

interface InventorySubtaskNotCheckDialogState {
    viewHeight: number;
}

// 库存子任务未检查对话框
export class InventorySubTaskNotCheckDialog extends BaseComponent<
    InventorySubTaskNotCheckDialogProps,
    InventorySubtaskNotCheckDialogState
> {
    static async show(params: InventorySubTaskNotCheckDialogProps): Promise<InventorySubTaskNotCheckDialogResult> {
        return await showBottomPanel(<InventorySubTaskNotCheckDialog {...params} />, { topMaskHeight: pxToDp(100) });
    }

    constructor(props: InventorySubTaskNotCheckDialogProps) {
        super(props);
        this.state = {
            viewHeight: 0,
        };
    }
    static defaultProps = {
        canGenerateDraft: false,
    };

    private async maintainBookInventory(): Promise<void> {
        const result = await showQueryDialog("提示", "确认未盘点商品保持账面数量并提交盘点吗？");
        if (result == DialogIndex.positive) {
            ABCNavigator.pop(
                JsonMapper.deserialize(InventorySubTaskNotCheckDialogResult, {
                    action: InventorySubTaskNotCheckDialogAction.keep,
                })
            );
        }
    }
    private async allCountsToZero(): Promise<void> {
        const result = await showQueryDialog("提示", "确认未盘点商品盘点为0并提交盘点单吗？");
        if (result == DialogIndex.positive) {
            ABCNavigator.pop(
                JsonMapper.deserialize(InventorySubTaskNotCheckDialogResult, {
                    action: InventorySubTaskNotCheckDialogAction.changZero,
                })
            );
        }
    }
    private async generateDraft(): Promise<void> {
        const result = await showQueryDialog("提示", "确认未盘点商品生成重盘草稿并提交盘点单吗？");
        if (result == DialogIndex.positive) {
            ABCNavigator.pop(
                JsonMapper.deserialize(InventorySubTaskNotCheckDialogResult, {
                    action: InventorySubTaskNotCheckDialogAction.generatingDraft,
                })
            );
        }
    }

    render(): JSX.Element {
        const detail = this.props.detail;
        const { showKeep, readonly, canGenerateDraft } = this.props;
        const operateBtnList = [
            {
                name: "全部保持账面数量",
                isShow: !!showKeep,
                onClick: () => this.maintainBookInventory(),
            },
            {
                name: "全部盘点为0",
                isShow: !readonly,
                onClick: () => this.allCountsToZero(),
            },
            {
                name: "生成重盘草稿",
                isShow: !!canGenerateDraft,
                onClick: () => this.generateDraft(),
            },
        ];
        const title = `${this.props.title ?? "---"}`;
        return (
            <View style={{ backgroundColor: Colors.white, flex: 1 }}>
                <View style={[Sizes.paddingLTRB(0, Sizes.dp16, 0, Sizes.dp8), ABCStyles.bottomLine]}>
                    <View style={[ABCStyles.rowAlignCenter, { justifyContent: "center" }]}>
                        <Text style={[TextStyles.t18MB.copyWith({ lineHeight: Sizes.dp28 }), { textAlign: "center" }]}>
                            {"未盘点商品处理"}
                        </Text>
                        <View style={{ position: "absolute", right: 0, top: -Sizes.dp12 }}>
                            <BottomSheetCloseButton onTap={() => ABCNavigator.pop()} />
                        </View>
                    </View>
                    <SizedBox height={Sizes.dp4} />
                    <View style={[ABCStyles.rowAlignCenter, { justifyContent: "center" }]}>
                        <Text style={TextStyles.t16MT1.copyWith({ color: Colors.t2 }).copyWith({ lineHeight: Sizes.dp24 })}>{title}</Text>
                    </View>
                </View>
                <SizedBox height={Sizes.dp8} />
                <ListView
                    style={{ flex: 1 }}
                    dataSource={detail ?? []}
                    numberOfRows={detail?.length ?? 0}
                    getRowKey={this._getRowKey.bind(this)}
                    renderRow={this._renderRow.bind(this)}
                    scrollEventThrottle={300}
                />
                <ToolBar hideWhenKeyboardShow={true} spaceWidth={Sizes.dp8}>
                    {operateBtnList
                        ?.filter((t) => t.isShow)
                        ?.map((item, index) => {
                            return (
                                <ToolBarButtonStyle2
                                    key={index}
                                    text={item.name}
                                    onClick={item.onClick}
                                    style={{
                                        fontSize: Sizes.dp14,
                                        fontWeight: "500",
                                        width: item.name.length > 6 ? Sizes.dp124 : undefined,
                                    }}
                                />
                            );
                        })}
                </ToolBar>
                <SafeAreaBottomView />
            </View>
        );
    }

    private _getRowKey(index: number): string {
        const { detail } = this.props;
        return detail![index].compareKey();
    }

    protected _renderRow(item: CheckTaskGoodItem, unknown?: AnyType, index?: number): JSX.Element {
        ignore(unknown, index);
        return (
            <View style={{ flex: 1 }} key={index}>
                <View
                    style={[
                        ABCStyles.bottomLine,
                        {
                            flex: 1,
                            paddingVertical: Sizes.dp8,
                            marginHorizontal: Sizes.dp16,
                        },
                    ]}
                >
                    <View
                        style={{
                            flex: 1,
                            ...ABCStyles.rowAlignCenterSpaceBetween,
                        }}
                    >
                        <Text
                            style={[TextStyles.t16NT1, { flexShrink: 1, textAlign: "center", lineHeight: Sizes.dp24, fontWeight: "600" }]}
                            numberOfLines={1}
                        >
                            {item.goods!.displayName ?? "--"}
                        </Text>
                        <Text
                            style={[TextStyles.t16NT2.copyWith({ color: Colors.t2 }), { flexShrink: 1, lineHeight: Sizes.dp24 }]}
                            numberOfLines={1}
                        >
                            {item.goods!.displayTypeName ?? ""}
                        </Text>
                    </View>
                    <View style={ABCStyles.rowAlignCenter}>
                        <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp22 })}>
                            {item.goods?.packageSpec ?? "--"}
                        </Text>
                        <SizedBox width={Sizes.dp8} />
                        <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp22 })} numberOfLines={1}>
                            {item.goods?.manufacturer ?? ""}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexShrink: 1,
                            ...ABCStyles.rowAlignCenterSpaceBetween,
                        }}
                    >
                        <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp22 })} numberOfLines={1}>
                            {`批次：${!!item.batchId ? item.batchId : "不指定批次"}`}
                        </Text>
                        <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp22 })} numberOfLines={1}>
                            {`账面库存:${item.beforeStockDisplay ?? "--"}`}
                        </Text>
                    </View>
                </View>
            </View>
        );
    }
}

export class InventorySubTaskSummaryWaringDialog extends InventorySubTaskNotCheckDialog {
    static async show(params: InventorySubTaskNotCheckDialogProps): Promise<InventorySubTaskNotCheckDialogResult> {
        const dialog = <InventorySubTaskSummaryWaringDialog {...params} />;
        const builder = new DialogBuilder();
        const buttons = new DialogButtonBuilder();
        buttons.appendButton({
            text: "关闭",
            textStyle: TextStyles.t16NM,
            onClick: () => {
                ABCNavigator.pop();
            },
        });
        builder.contentPadding = Sizes.paddingLTRB(0, 0);
        builder.button = buttons;
        builder.content = dialog;
        return await builder.show();
    }

    protected _renderRow(item: SummaryTaskGoodItem, unknown?: AnyType, index?: number): JSX.Element {
        ignore(unknown, index);
        return (
            <View style={{ flex: 1 }} key={index}>
                <View
                    style={[
                        ABCStyles.bottomLine,
                        {
                            flex: 1,
                            paddingVertical: Sizes.dp8,
                            paddingHorizontal: Sizes.dp16,
                        },
                    ]}
                >
                    <View
                        style={{
                            flex: 1,
                            flexDirection: "row",
                            alignItems: "center",
                        }}
                    >
                        <Text style={[TextStyles.t16NT1, { flexShrink: 1, textAlign: "center", lineHeight: Sizes.dp24 }]} numberOfLines={1}>
                            {item.goods!.displayName ?? "--"}
                        </Text>

                        <Text
                            style={[
                                TextStyles.t14NT2,
                                {
                                    textAlign: "center",
                                    marginLeft: Sizes.dp16,
                                    lineHeight: Sizes.dp20,
                                },
                            ]}
                            numberOfLines={1}
                        >
                            {item.checkBatchType ?? "--"}
                        </Text>
                    </View>

                    <Text style={[TextStyles.t14NT2, { flexShrink: 1, lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                        {`任务名：${item.taskName ?? ""}`}
                    </Text>

                    <Text style={[TextStyles.t14NT2, { flexShrink: 1, lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                        {`负责人：${item.ownerName ?? ""}`}
                    </Text>
                </View>
            </View>
        );
    }
}
