/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/29
 *
 * @description
 */
import { SizedB<PERSON>, Tab, Tabs } from "../base-ui";
import React from "react";
import { ScrollView, Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { StatisticsPageBloc } from "./statistics-page-bloc";
import { BaseComponent } from "../base-ui/base-component";
import { DropdownArrowView } from "../base-ui/iconfont/iconfont-view";
import { IncomeTrendStatType, StatSummaryData } from "./data/statistics-bean";
import { DINAlternate } from "../theme/text-styles";
import { ABCUtils } from "../base-ui/utils/utils";
import { NumberUtils, TimeUtils } from "../common-base-module/utils";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { GroupDivider } from "../base-ui/divider-line";
import { LineChartDataSet, LineChartView } from "../base-ui/views/charts/line-chart-view";
import _ from "lodash";
import { StatisticsClinicSelectPage } from "./statistics-clinic-select-page";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../base-ui/base-page";
import { StatisticsTimeFilterView } from "./views/statistics-time-filter-view";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { FilterGroup, FilterItem, Filters, TimeFilterItem } from "../base-ui/searchBar/search-bar-bean";
import { CommonFilterId } from "../base-ui/searchBar/search-bar";
import { UIUtils } from "../base-ui/utils";
import { ABCEmptyView } from "../base-ui/views/empty-view";
import { LoadingView } from "../base-ui/views/loading-view";
import { userCenter } from "../user-center";
import { ModuleIds, RolesType } from "../user-center/user-center";
import { chainSharedPreferences } from "../base-business/preferences/scoped-shared-preferences";
import { StatisticsUtils } from "./utils/statistics-utils";
import { RangePicker } from "../base-ui/picker/range-picker";
import { Range } from "../base-ui/utils/value-holder";
import abcI18Next from "../language/config";

interface StatisticsPageProps {}

export class StatisticsPage extends BaseBlocNetworkPage<StatisticsPageProps, StatisticsPageBloc> {
    bloc: StatisticsPageBloc;

    constructor(props: StatisticsPageProps) {
        super(props);

        const localStatisticsFilter: _FilterSettings | undefined = chainSharedPreferences.getObject(SAVE_STATISTICS_FILTER_STATUS);
        this.bloc = new StatisticsPageBloc(localStatisticsFilter?.isIncludeWriter, localStatisticsFilter?.isIncludeReg);

        this.addDisposable(this.bloc);
    }

    componentDidMount(): void {
        super.componentDidMount();
        this.bloc.state
            .subscribe((state) => {
                let status = ABCNetworkPageContentStatus.show_data;
                if (state.loadingStatSummary && state.loadingIncomeTrend) {
                    status = ABCNetworkPageContentStatus.loading;
                } else if (state.loadingStatSummaryError && state.loadingIncomeTrendError) {
                    status = ABCNetworkPageContentStatus.error;
                }
                this.setContentStatus(status, state.loadingStatSummaryError);
            })
            .addToDisposableBag(this);
    }

    getAppBarTitle(): string {
        return "统计";
    }

    // 页面渲染
    renderContent(): JSX.Element {
        const { loadingStatSummary, statSummaryData, statDailyRevenueList } = this.bloc.currentState;
        if (!statSummaryData || !statDailyRevenueList) return <View />;
        return (
            <ScrollView>
                <_NavigationBar />
                <View style={{ position: "relative" }}>
                    <_IncomeSummaryView />
                    <GroupDivider />
                    <_IncomePayMethodView />
                    {loadingStatSummary && (
                        <View style={[ABCStyles.absoluteFill, { backgroundColor: Colors.maskColor }]}>
                            <View style={{ alignItems: "center", marginTop: UIUtils.getScreenHeight() / 3 }}>
                                <LoadingView size={Sizes.dp24} />
                            </View>
                        </View>
                    )}
                </View>
                <GroupDivider />
                <_IncomeTrendTableView />
            </ScrollView>
        );
    }

    public reloadData(): void {
        this.bloc.requestLoadData();
    }
}

class NaviLabelItem {
    static NAVI_ITEM_TYPE_TODAY = 0;
    static NAVI_ITEM_TYPE_YESTERDAY = 1;
    static NAVI_ITEM_TYPE_WEEK = 2;
    static NAVI_ITEM_TYPE_MONTH = 3;
    static NAVI_ITEM_TYPE_YEAR = 4;
    static NAVI_ITEM_TYPE_RANGE = 5;

    constructor(params: { type: number; title: string; compareTitle: string }) {
        Object.assign(this, params);
    }

    type!: number;
    title!: string;
    compareTitle!: string;
}

const includingRegistrationID = 0;
const excludingRegistrationID = 1;
const includeWriterID = 2;
const excludeWriterID = 3;

interface _NavigationBarProps {}
const SAVE_STATISTICS_FILTER_STATUS = "saveStatisticsFilterStatus";

interface _FilterSettings {
    // timeType: number;
    isIncludeWriter: boolean;
    isIncludeReg: boolean;
}

// 顶部导航，选择不同门店，时间范围
class _NavigationBar extends BaseComponent<_NavigationBarProps> {
    static contextType = StatisticsPageBloc.Context;
    private _filters: Filters;
    private _checkedFilter?: Filters;

    constructor(props: _NavigationBarProps) {
        super(props);

        const localStatisticsFilter: _FilterSettings | undefined = chainSharedPreferences.getObject(SAVE_STATISTICS_FILTER_STATUS);
        const isDrugstoreButler = !!userCenter.clinic?.isDrugstoreButler;
        const isIncludeReg = localStatisticsFilter?.isIncludeReg ?? true;
        this._filters = JsonMapper.deserialize(Filters, {
            filters: [
                JsonMapper.deserialize(FilterGroup, {
                    filters: [
                        JsonMapper.deserialize(FilterItem, {
                            title: "今天",
                            id: CommonFilterId.timeToday,
                            exclusive: true,
                            select: true,
                            isDefault: true,
                            timeRange: new Range<Date>(TimeUtils.getTodayStart(), TimeUtils.getTodayStart()),
                        }),
                        JsonMapper.deserialize(FilterItem, {
                            title: "昨天",
                            id: CommonFilterId.timeYesterday,
                            exclusive: true,
                            timeRange: new Range<Date>(TimeUtils.getYesterdayStart(), TimeUtils.getYesterdayStart()),
                        }),
                        JsonMapper.deserialize(FilterItem, {
                            title: "本周",
                            id: CommonFilterId.timeThisWeek,
                            exclusive: true,
                            timeRange: new Range<Date>(TimeUtils.getThisWeekFirstDay(), TimeUtils.getTodayEnd()),
                        }),
                        JsonMapper.deserialize(TimeFilterItem, {
                            title: "本月",
                            id: CommonFilterId.timeThisMonth,
                            exclusive: true,
                            timeRange: new Range<Date>(TimeUtils.getThisMonthFirstDay(), TimeUtils.getTodayEnd()),
                        }),
                        JsonMapper.deserialize(FilterItem, {
                            title: "今年",
                            id: CommonFilterId.timeThisYear,
                            exclusive: true,
                            timeRange: new Range<Date>(TimeUtils.getThisYearFirstDay(), TimeUtils.getTodayEnd()),
                        }),
                        JsonMapper.deserialize(FilterItem, {
                            title: "选择时间",
                            defaultTitle: "选择时间",
                            id: CommonFilterId.asyncFilter,
                            exclusive: true,
                            asyncChange: async (item) => {
                                const timeRange = await RangePicker.show(item.timeRange);
                                if (timeRange) {
                                    item.title = `${timeRange.start?.format("yyyy-MM-dd")}\n${timeRange.end?.format("yyyy-MM-dd")}`;
                                    item.timeRange = timeRange;
                                    return item;
                                }
                            },
                        }),
                    ],
                }),
            ],
        });
        if (!isDrugstoreButler) {
            this._filters.filters.push(
                JsonMapper.deserialize(FilterGroup, {
                    filters: [
                        JsonMapper.deserialize(FilterItem, {
                            title: "含挂号费",
                            id: includingRegistrationID,
                            exclusive: true,
                            select: isIncludeReg,
                            isDefault: true,
                        }),
                        JsonMapper.deserialize(FilterItem, {
                            title: "不含挂号费",
                            id: excludingRegistrationID,
                            select: !isIncludeReg,
                            exclusive: true,
                        }),
                    ],
                })
            );
        }
        if (
            !isDrugstoreButler &&
            ((userCenter.clinic?.roles?.map((item) => item?.id).indexOf(RolesType.ROLE_DOCTOR_ASSIST_ID) ?? -1) > -1 ||
                userCenter.clinic?.isChainAdminClinic ||
                userCenter.clinic?.isManager ||
                userCenter.clinic?.moduleIds?.includes(ModuleIds.MODULE_ID_STATISTICS_ACHIEVEMENT_BILLING.toString()))
        ) {
            const isIncludeWriter = localStatisticsFilter?.isIncludeWriter ?? true;
            this._filters.filters.push(
                JsonMapper.deserialize(FilterGroup, {
                    filters: [
                        JsonMapper.deserialize(FilterItem, {
                            title: "含代录业绩",
                            id: includeWriterID,
                            select: isIncludeWriter,
                            exclusive: true,
                        }),
                        JsonMapper.deserialize(FilterItem, {
                            title: "不含代录业绩",
                            id: excludeWriterID,
                            exclusive: true,
                            select: !isIncludeWriter,
                            isDefault: true,
                        }),
                    ],
                })
            );
        }
    }

    render() {
        const { currentClinic } = StatisticsPageBloc.fromContext(this.context).currentState;
        let clinicNameRef: View | null = null;
        return (
            <View
                style={[
                    ABCStyles.bottomLine,
                    ABCStyles.rowAlignCenterSpaceBetween,
                    { paddingHorizontal: Sizes.listHorizontalMargin, backgroundColor: Colors.white, height: Sizes.listItemHeight },
                ]}
                ref={(ref) => (clinicNameRef = ref)}
                collapsable={false}
            >
                <View
                    // 统计-门店筛选
                    style={{ flex: 1, ...ABCStyles.rowAlignCenter }}
                    onClick={async () => {
                        if (!userCenter.clinic?.isChainAdminClinic) return;
                        const bloc = StatisticsPageBloc.fromContext(this.context);
                        const layout = await UIUtils.measureInWindow(clinicNameRef!);
                        const clinic = await StatisticsClinicSelectPage.show(bloc.currentState.currentClinic, layout.y + layout.height);
                        if (clinic) {
                            bloc.requestUpdateSelectClinic(clinic);
                        }
                    }}
                >
                    <Text numberOfLines={1} style={[TextStyles.t14NB, { flexShrink: 1 }]} ellipsizeMode={"tail"}>
                        {currentClinic?.isChainAdminClinic ? "全部门店" : currentClinic?.displayName ?? ""}
                    </Text>
                    {userCenter.clinic?.isChainAdminClinic && <DropdownArrowView />}
                </View>

                <StatisticsTimeFilterView
                    // 统计-时间筛选
                    filters={this._filters}
                    // 未确定情况下保留筛选状态
                    defaultFilterInfo={this._checkedFilter}
                    onChange={(filters) => {
                        if (!filters) return;
                        let timeType = NaviLabelItem.NAVI_ITEM_TYPE_TODAY;

                        let isIncludeReg = false;
                        let isIncludeWriter = false;
                        for (const group of filters.filters ?? []) {
                            for (const item of group.filters ?? []) {
                                const id = item?.id;
                                switch (id) {
                                    // 筛选今天
                                    case CommonFilterId.timeToday: {
                                        timeType = NaviLabelItem.NAVI_ITEM_TYPE_TODAY;
                                        break;
                                    }
                                    // 筛选昨天
                                    case CommonFilterId.timeYesterday: {
                                        timeType = NaviLabelItem.NAVI_ITEM_TYPE_YESTERDAY;
                                        break;
                                    }
                                    // 筛选本周
                                    case CommonFilterId.timeThisWeek: {
                                        timeType = NaviLabelItem.NAVI_ITEM_TYPE_WEEK;
                                        break;
                                    }
                                    // 筛选本月
                                    case CommonFilterId.timeThisMonth: {
                                        timeType = NaviLabelItem.NAVI_ITEM_TYPE_MONTH;
                                        break;
                                    }
                                    // 筛选今年
                                    case CommonFilterId.timeThisYear: {
                                        timeType = NaviLabelItem.NAVI_ITEM_TYPE_YEAR;
                                        break;
                                    }
                                    // 筛选选择时间
                                    case CommonFilterId.timeRange: {
                                        timeType = NaviLabelItem.NAVI_ITEM_TYPE_RANGE;
                                        break;
                                    }

                                    // 筛选含挂号费(默认)
                                    case includingRegistrationID: {
                                        isIncludeReg = true;
                                        break;
                                    }
                                    // 筛选不含挂号费
                                    case excludingRegistrationID: {
                                        isIncludeReg = false;
                                        break;
                                    }
                                    // 筛选展示待录业绩
                                    case includeWriterID: {
                                        isIncludeWriter = true;
                                        break;
                                    }
                                    // 筛选不展示待录业绩(默认)
                                    case excludeWriterID: {
                                        isIncludeWriter = false;
                                        break;
                                    }
                                }
                            }
                        }

                        // 选中状态
                        this._filters.fillSelectAttrs(filters);
                        this._checkedFilter = filters;
                        const settings: _FilterSettings = {
                            isIncludeWriter: isIncludeWriter,
                            isIncludeReg: isIncludeReg,
                        };

                        chainSharedPreferences.setObject(SAVE_STATISTICS_FILTER_STATUS, settings);
                        StatisticsPageBloc.fromContext(this.context).requestUpdateNaviIndex(
                            timeType,
                            filters,
                            isIncludeReg,
                            isIncludeWriter
                        );
                    }}
                />
            </View>
        );
    }
}

// 顶部标签选择视图
interface _LabelChooseViewProps {
    labels: string[];
    selectIndex: number;

    onChanged?(index: number): void;
}

class _LabelChooseView extends BaseComponent<_LabelChooseViewProps> {
    constructor(props: _LabelChooseViewProps) {
        super(props);
    }

    render() {
        const { labels, selectIndex, onChanged } = this.props;
        return (
            // 顶部时间标签选择
            <View style={{ flexDirection: "row" }}>
                {labels.map((item, index) => {
                    const select = selectIndex == index;
                    return (
                        <View key={index} onClick={() => onChanged?.(index)}>
                            <View
                                style={[
                                    select ? ABCStyles.halfDPActiveBorder : ABCStyles.halfDPBorder,
                                    { paddingHorizontal: Sizes.dp8, height: Sizes.dp22, justifyContent: "center", marginLeft: Sizes.dp4 },
                                ]}
                            >
                                <Text style={select ? TextStyles.t14NM : TextStyles.t14NT2}>{item}</Text>
                            </View>
                        </View>
                    );
                })}
            </View>
        );
    }
}

const DINAlternateFontFamily = { fontFamily: DINAlternate };

// 收入汇总视图
class _IncomeSummaryView extends BaseComponent {
    static contextType = StatisticsPageBloc.Context;

    render() {
        const { statSummaryData } = StatisticsPageBloc.fromContext(this.context).currentState;
        return (
            <View style={[ABCStyles.bottomLine, { paddingVertical: Sizes.dp16, backgroundColor: Colors.white }]}>
                {statSummaryData && this._renderIncomeSummaryView(statSummaryData, true)}
            </View>
        );
    }

    private _renderIncomeSummaryView(itemData: StatSummaryData, bigger: boolean /*, title: string*/) {
        const rechargeMember = itemData?.rechargeMember ?? 0; // 会员充值
        const revenue = itemData?.totalAmount ?? 0; // 营业收费
        const patientCount = itemData?.patientCount ?? 0; // 营业收费人次

        const biggerNumberTextStyle = {
            ...TextStyles.t20BB,
            ...DINAlternateFontFamily,
        };

        const smallerNumberTextStyle = {
            ...TextStyles.t14BB,
            ...DINAlternateFontFamily,
        };

        const biggerLabelTextStyle = {
            ...TextStyles.t12NT4,
        };

        const smallerLabelTextStyle = {
            ...TextStyles.t11NT4,
        };

        return (
            <View style={ABCStyles.rowAlignCenter}>
                <View
                    style={{
                        flex: 1,
                        justifyContent: "space-between",
                        flexDirection: "row",
                        paddingHorizontal: Sizes.listHorizontalMargin,
                    }}
                >
                    <View>
                        <View style={ABCStyles.rowAlignCenter}>
                            <Text
                                style={[
                                    TextStyles.t12BB,
                                    DINAlternateFontFamily,
                                    { height: Sizes.dp16, marginTop: Sizes.dp4, marginRight: Sizes.dp2 },
                                ]}
                            >
                                {abcI18Next.t("¥")}
                            </Text>
                            <Text style={bigger ? biggerNumberTextStyle : smallerNumberTextStyle}>{ABCUtils.formatPrice(revenue)}</Text>
                        </View>
                        <SizedBox height={Sizes.dp4} />
                        <Text style={bigger ? biggerLabelTextStyle : smallerLabelTextStyle}>营收收费</Text>
                    </View>

                    <View>
                        <Text style={bigger ? biggerNumberTextStyle : smallerNumberTextStyle}>{patientCount}</Text>
                        <SizedBox height={Sizes.dp4} />
                        <Text style={[bigger ? TextStyles.t12NT4 : TextStyles.t11NT4]}>收费人次</Text>
                    </View>
                </View>

                <View style={{ width: Sizes.dpHalf, height: Sizes.dp40, backgroundColor: Colors.P3 }} />
                <View style={{ width: pxToDp(177), paddingHorizontal: Sizes.listHorizontalMargin }}>
                    <View style={ABCStyles.rowAlignCenter}>
                        <Text
                            style={[
                                TextStyles.t12BB,
                                DINAlternateFontFamily,
                                { height: Sizes.dp16, marginTop: Sizes.dp4, marginRight: Sizes.dp2 },
                            ]}
                        >
                            {abcI18Next.t("¥")}
                        </Text>
                        <Text style={bigger ? biggerNumberTextStyle : smallerNumberTextStyle}>{ABCUtils.formatPrice(rechargeMember)}</Text>
                    </View>
                    <SizedBox height={Sizes.dp4} />
                    <Text style={bigger ? biggerLabelTextStyle : smallerLabelTextStyle}>会员充值</Text>
                </View>
            </View>
        );
    }
}

const kRightPercentWidth = pxToDp(132);

// 枚举收入页签顺序
enum _IncomeTabType {
    achievement, // 开单业绩
    feeType, // 费用类型
    payMethod, // 支付方式
}

interface _IncomePayMethodViewProps {}

/**
 * 开单业绩、费用类型、支付方式 组成部份
 */
class _IncomePayMethodView extends BaseComponent<_IncomePayMethodViewProps> {
    static contextType = StatisticsPageBloc.Context;
    private currentIndex: _IncomeTabType;

    constructor(props: _IncomePayMethodViewProps) {
        super(props);
        this.currentIndex = StatisticsUtils.containBillAchievement() ? _IncomeTabType.achievement : _IncomeTabType.feeType;
    }

    render() {
        const { achievement, statSummaryData } = StatisticsPageBloc.fromContext(this.context).currentState;
        if (achievement == undefined && statSummaryData == undefined) return <View />;
        const tabs = [
            { id: _IncomeTabType.achievement, name: "开单业绩" },
            { id: _IncomeTabType.feeType, name: "费用类型" },
            { id: _IncomeTabType.payMethod, name: "支付方式" },
        ];
        if (!StatisticsUtils.containBillAchievement()) _.remove(tabs, (item) => item.id === _IncomeTabType.achievement);

        return (
            <View>
                <Tabs
                    lineMargin={0}
                    initialPage={0}
                    tabStyle={{ marginRight: Sizes.dp61, ...TextStyles.t16MB.copyWith({ color: Colors.T2 }) }}
                    tabsStyle={{
                        ...ABCStyles.rowAlignCenter,
                        justifyContent: "center",
                        paddingHorizontal: Sizes.listHorizontalMargin,
                        backgroundColor: Colors.white,
                        height: Sizes.dp44,
                    }}
                    currentStyle={{ ...TextStyles.t16NM }}
                    onChange={(index) => {
                        this.currentIndex = tabs[index].id; // 当前索引等于枚举
                        this.setState({});
                    }}
                >
                    {tabs.map((item, index) => (
                        <Tab key={index} title={item.name} />
                    ))}
                </Tabs>
                {this.currentIndex == _IncomeTabType.achievement && this._renderAchievement()}
                {this.currentIndex == _IncomeTabType.feeType && this._renderFeeType()}
                {this.currentIndex == _IncomeTabType.payMethod && this._renderPayMethod()}
            </View>
        );
    }

    // 统计开单业绩 方法
    private _renderAchievement(): JSX.Element {
        const { achievement, currentClinic } = StatisticsPageBloc.fromContext(this.context).currentState;
        if (achievement?.data?.length == 0)
            return <ABCEmptyView tips={"暂无数据"} style={{ height: pxToDp(150), alignItems: "center", backgroundColor: Colors.white }} />;
        return (
            <View>
                {achievement?.data?.map((item, index) => {
                    return (
                        <_BillingPerformanceView
                            key={_IncomeTabType.achievement + item.employeeName + index}
                            isWriter={!!item.isWriter}
                            employeeName={item.employeeName!}
                            name={currentClinic?.isChainAdminClinic ? item.clinicName : ""}
                            amount={item.amount!}
                            patientCount={item.patientCount!}
                            drawTopLine={index != 0}
                        />
                    );
                })}
            </View>
        );
    }

    // 统计费用类型 方法
    private _renderFeeType(): JSX.Element {
        const { statSummaryData } = StatisticsPageBloc.fromContext(this.context).currentState;
        if (statSummaryData?.feeTypes?.length == 0)
            return <ABCEmptyView tips={"暂无数据"} style={{ height: pxToDp(150), alignItems: "center", backgroundColor: Colors.white }} />;
        let totalFee = 0;
        statSummaryData?.feeTypes?.forEach((feeItemItem) => {
            totalFee += feeItemItem.value;
        });

        return (
            <View>
                {statSummaryData?.feeTypes
                    ?.filter((item) => item.value != 0)
                    ?.sort((item1, item2) => item2.value - item1.value)
                    ?.map((item, index) => {
                        return (
                            <View key={index}>
                                <_IncomePercentItemView
                                    key={_IncomeTabType.feeType + item.name! + index}
                                    name={item.name!}
                                    amount={item.value}
                                    percent={totalFee > 0 ? item.value / totalFee : 0}
                                    drawTopLine={index != 0}
                                />
                            </View>
                        );
                    })}
            </View>
        );
    }

    // 统计支付方式 方法
    private _renderPayMethod(): JSX.Element {
        const { statSummaryData } = StatisticsPageBloc.fromContext(this.context).currentState;
        let totalFee = 0;
        if (statSummaryData?.payModes?.length == 0)
            return <ABCEmptyView tips={"暂无数据"} style={{ height: pxToDp(150), alignItems: "center", backgroundColor: Colors.white }} />;
        statSummaryData?.payModes?.forEach((item) => {
            totalFee += item.value;
        });
        return (
            <View>
                {statSummaryData?.payModes
                    ?.filter((item) => item.value != 0)
                    ?.sort((item1, item2) => item2.value - item1.value)
                    ?.map((item, index) => {
                        return (
                            <View key={index}>
                                <_IncomePercentItemView
                                    key={_IncomeTabType.payMethod + item.name! + index}
                                    name={item.name!}
                                    amount={item.value}
                                    percent={totalFee > 0 ? item.value / totalFee : 0}
                                    drawTopLine={index != 0}
                                />
                            </View>
                        );
                    })}
            </View>
        );
    }
}

// 费用类型/支付方式 百分比视图
interface _IncomePercentItemViewProps {
    name: string;
    percent: number;
    amount: number;
    drawTopLine: boolean;
}

class _IncomePercentItemView extends BaseComponent<_IncomePercentItemViewProps> {
    constructor(props: _IncomePercentItemViewProps) {
        super(props);
    }

    render() {
        const { name, percent, amount } = this.props;
        return (
            <View
                key={name}
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        borderTopWidth: 1,
                        borderTopColor: Colors.P5,
                        height: Sizes.listItemHeight,
                        paddingHorizontal: Sizes.listHorizontalMargin,
                        backgroundColor: Colors.white,
                    },
                ]}
            >
                <Text style={[TextStyles.t16NT1, { minWidth: Sizes.dp64 }]}>{name}</Text>
                <View
                    style={{
                        flex: 1,
                        backgroundColor: Colors.D2,
                        borderRadius: Sizes.dp2,
                        height: Sizes.dp4,
                        marginLeft: Sizes.dp9,
                        marginRight: Sizes.dp18,
                        flexDirection: "row",
                        alignItems: "stretch",
                    }}
                >
                    <View style={{ flex: percent * 100, backgroundColor: Colors.mainColor, borderRadius: Sizes.dp2 }} />
                    <View style={{ flex: (1 - percent) * 100 }} />
                </View>
                <View style={{ minWidth: kRightPercentWidth, justifyContent: "space-between", ...ABCStyles.rowAlignCenter }}>
                    <Text style={TextStyles.t16NT1}>{NumberUtils.formatMaxFixed(percent * 100, 2, 2)}%</Text>
                    <SizedBox width={Sizes.dp2} />
                    <Text style={[TextStyles.t16NT1, { minWidth: Sizes.dp59 }]}>{ABCUtils.formatPrice(amount)}</Text>
                </View>
            </View>
        );
    }
}

// 开单业绩格式视图 带单位(个)
interface _BillingPerformanceViewProps {
    employeeName: string;
    name: string;
    amount: number;
    patientCount: number;
    drawTopLine: boolean;
    isWriter: boolean;
}

class _BillingPerformanceView extends BaseComponent<_BillingPerformanceViewProps> {
    constructor(props: _BillingPerformanceViewProps) {
        super(props);
    }

    render() {
        const { employeeName, name, amount, patientCount, isWriter } = this.props;
        return (
            <View
                key={name}
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        borderTopWidth: 1,
                        borderTopColor: Colors.P5,
                        height: Sizes.listItemHeight,
                        paddingHorizontal: Sizes.listHorizontalMargin,
                        backgroundColor: Colors.white,
                    },
                ]}
            >
                <Text style={[isWriter ? TextStyles.t16NT4 : TextStyles.t16NT1, { minWidth: Sizes.dp64 }]}>{employeeName}</Text>
                <Text style={[TextStyles.t12NT4, { flex: 1 }]} numberOfLines={1}>
                    {name}
                </Text>

                <Text style={[isWriter ? TextStyles.t16NT4 : TextStyles.t16NT1, { textAlign: "right", minWidth: Sizes.dp71 }]}>
                    {ABCUtils.formatPrice(amount)}
                </Text>
                <Text style={[isWriter ? TextStyles.t16NT4 : TextStyles.t16NT1, { minWidth: Sizes.dp55, textAlign: "right" }]}>
                    {String(patientCount)}人
                </Text>
            </View>
        );
    }
}

class _IncomeTrendTableView extends BaseComponent {
    static contextType = StatisticsPageBloc.Context;

    render() {
        /**
         * 收入趋势图
         */
        const _incomeTrendLabelDataList = [
            { title: "近一月", type: IncomeTrendStatType.recentMonth },
            { title: "近三月", type: IncomeTrendStatType.recent3Month },
            { title: "近半年", type: IncomeTrendStatType.recentHalfYear },
            { title: "月趋势", type: IncomeTrendStatType.monthTrend },
        ];

        const { statDailyRevenueList, incomeTrendLabelType, statSummaryData } = StatisticsPageBloc.fromContext(this.context).currentState;
        if (statDailyRevenueList == undefined || statSummaryData == undefined) return <View />;

        const data: LineChartDataSet = {
            values: [],
            lineWidth: 1.5,
            lineColor: Colors.mainColor,
            circleColor: Colors.mainColor,
            drawCircles: true,
            circleRadius: 1.5,
        };

        let maxPrice = 0;
        data.values = statDailyRevenueList.map((item) => {
            maxPrice = Math.max(maxPrice, item.total);
            return {
                x: item.startDay.getTime(),
                y: item.total,
            };
        });
        return (
            <View style={{ backgroundColor: Colors.white }}>
                <View
                    style={[
                        ABCStyles.rowAlignCenterSpaceBetween,
                        ABCStyles.bottomLine,
                        { height: Sizes.listItemHeight, paddingHorizontal: Sizes.listHorizontalMargin },
                    ]}
                >
                    <Text style={TextStyles.t16MB}>收入趋势</Text>
                    <_LabelChooseView
                        labels={_incomeTrendLabelDataList.map((item) => item.title)}
                        selectIndex={incomeTrendLabelType}
                        onChanged={(index) => {
                            StatisticsPageBloc.fromContext(this.context).requestUpdateIncomeTrendType(
                                _incomeTrendLabelDataList[index].type
                            );
                        }}
                    />
                </View>

                {statDailyRevenueList.length == 0 && (
                    <View style={[ABCStyles.centerChild, { height: Sizes.dp140, marginBottom: Sizes.dp5 }]}>
                        <Text style={TextStyles.t14NT2}>无数据</Text>
                    </View>
                )}
                {statDailyRevenueList.length > 0 && (
                    <LineChartView
                        style={{ height: Sizes.dp140, marginBottom: Sizes.dp5 }}
                        xAxis={{
                            labelPosition: "bottom",
                            labelFontStyle: TextStyles.t10NT2,
                            axisMinimum: _.first(statDailyRevenueList)?.startDay.getTime() ?? 0,
                            axisMaximum: _.last(statDailyRevenueList)?.startDay.getTime() ?? 0,
                            valueFormatter: { type: "time", format: "MM.dd" },
                        }}
                        leftAxis={{ labelPosition: "outside", labelFontStyle: TextStyles.t10NT2, axisMinimum: 0, axisMaximum: maxPrice }}
                        data={[data]}
                    />
                )}
            </View>
        );
    }
}
