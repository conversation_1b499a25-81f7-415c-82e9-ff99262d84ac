/**
 * create by <PERSON><PERSON>
 * desc:门诊日报
 * create date 2021/7/9
 */
import { BaseBlocNetworkView } from "../../base-ui/base-page";
import { BlocHelper } from "../../bloc/bloc-helper";
import { PerformanceStatDoctorViewBloc } from "./performance-stat-doctor-view-bloc";
import { PercentItemView, PerformanceStatItem } from "./stat-views";
import { GridView } from "../../base-ui/views/grid-view";
import { Colors, Sizes, TextStyles } from "../../theme";
import React from "react";
import { Text, View, Style } from "@hippy/react";
import { SizedBox } from "../../base-ui";
import { AbcEmptyItemView } from "../../base-ui/views/empty-view";
import { FeeTypeId } from "../data/statistics-bean";
import { Filters } from "../../base-ui/searchBar/search-bar-bean";
import { userCenter } from "../../user-center";

interface PerformanceStatDoctorViewProps {
    date?: Date;
}

export class PerformanceStatDoctorView extends BaseBlocNetworkView<PerformanceStatDoctorViewProps, PerformanceStatDoctorViewBloc> {
    constructor(props: PerformanceStatDoctorViewProps) {
        super(props);
        this.bloc = new PerformanceStatDoctorViewBloc({ date: props.date });
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this);
    }

    public requestUpdateFilter(filters: Filters): void {
        this.bloc.requestUpdateFilter(filters);
    }

    private renderPerformanceStatItem(options: {
        key: string;
        count?: number;
        title: string;
        style?: Style | Style[];
        showRightLine?: boolean;
        showNum?: boolean;
        show?: boolean;
    }): JSX.Element {
        const { key, count, title, showRightLine, showNum, show } = options;
        if (show === false) return <View />;
        return (
            <PerformanceStatItem
                key={key}
                count={count ?? 0}
                title={title}
                style={{ paddingLeft: Sizes.dp16 }}
                showRightLine={showRightLine}
                showNum={showNum}
            />
        );
    }

    private _renderDoctorSummaryView(): JSX.Element {
        const state = this.bloc.currentState,
            { workbenchCardInformation, workbenchTotalInvoicedAmountPersonTime, dataPermission } = state;
        const views = [
            this.renderPerformanceStatItem({ key: "outpatientCount", count: workbenchCardInformation?.outpatientCount, title: "门诊人次" }),
            this.renderPerformanceStatItem({
                key: "retailPersonTime",
                count: workbenchTotalInvoicedAmountPersonTime?.retailPersonTime,
                title: "零售人次",
            }),
            this.renderPerformanceStatItem({
                key: "firstVisitOutpatientCount",
                count: workbenchCardInformation?.firstVisitOutpatientCount,
                title: "初诊人次",
            }),
            this.renderPerformanceStatItem({
                key: "reVisitOutpatientCount",
                count: workbenchCardInformation?.reVisitOutpatientCount,
                title: "复诊人次",
            }),
            this.renderPerformanceStatItem({
                key: "prescriptionCount",
                count: workbenchTotalInvoicedAmountPersonTime?.prescriptionCount,
                title: "处方量",
            }),
            this.renderPerformanceStatItem({
                show:
                    !dataPermission?.doctorNotAllowedCheckRegistration ||
                    !dataPermission?.doctorNotAllowedCheckOutpatient ||
                    userCenter.clinic?.isManager,
                key: "chargeAmount",
                count: workbenchTotalInvoicedAmountPersonTime?.chargeAmount,
                title: dataPermission?.billingCardTitleStr ?? "",
                showRightLine: false,
                showNum: false,
            }),
        ];
        if (
            !dataPermission?.doctorNotAllowedCheckRegistration ||
            !dataPermission?.doctorNotAllowedCheckOutpatient ||
            userCenter.clinic?.isManager
        ) {
            views.push(
                <PerformanceStatItem
                    key={"chargeAmount"}
                    count={workbenchTotalInvoicedAmountPersonTime?.chargeAmount ?? 0}
                    title={dataPermission?.billingCardTitleStr ?? ""}
                    style={{ paddingLeft: Sizes.dp16 }}
                    showRightLine={false}
                    showNum={false}
                />
            );
        }
        return (
            <GridView
                style={{ borderRadius: Sizes.dp6, paddingVertical: Sizes.dp16, backgroundColor: Colors.white }}
                crossAxisCount={3}
                mainAxisSpacing={Sizes.dp16}
            >
                {views}
            </GridView>
        );
    }

    private _renderFeeModesView(): JSX.Element {
        const {
                dataPermission,
                workbenchRegistrationExpense,
                workbenchDiagnosisTreatmentExpense,
                workbenchTotalInvoicedAmountPersonTime,
                isDentistry,
                clinicBasicSetting,
            } = this.bloc.currentState,
            feeTypes = workbenchDiagnosisTreatmentExpense?.displayFeeTypes[0]._feeType?.filter(
                (item) => item.prop !== FeeTypeId.kanBanTotalAmount
            ); // 排除全部费用

        const registrationFee = workbenchRegistrationExpense?.summary?.registrationAmount ?? 0; // 挂号费
        const totalAmount =
            workbenchTotalInvoicedAmountPersonTime?.chargeAmount ??
            workbenchDiagnosisTreatmentExpense?.displayFeeTypes[0]._feeType?.filter((item) => item.prop == FeeTypeId.kanBanTotalAmount)[0]
                ._data ??
            0; // 开单金额合计
        let diagnosisTreatmentFee = feeTypes?.filter((item) => item.prop !== FeeTypeId.kanBanConsultingFee); // 诊疗费(排除咨询费)
        let consultingFee = feeTypes?.filter((item) => item.prop == FeeTypeId.kanBanConsultingFee); // 咨询费
        if (isDentistry) {
            diagnosisTreatmentFee = diagnosisTreatmentFee?.filter(
                (f) => !(f.prop == FeeTypeId.medicineChineseGranuleFee || f.prop == FeeTypeId.medicineChinesePieceFee)
            ); // 牙科诊所不展示（中药颗粒、中药饮片费）
            consultingFee = []; // 牙科诊所不展示（咨询费）
        }
        if (!feeTypes?.length) {
            return <AbcEmptyItemView tips={"暂无收费数据"} style={{ paddingTop: Sizes.dp100 }} />;
        }
        if (
            dataPermission?.doctorNotAllowedCheckOutpatient &&
            dataPermission.doctorNotAllowedCheckRegistration &&
            !userCenter.clinic?.isManager
        ) {
            return <AbcEmptyItemView tips={"暂无权限"} style={{ paddingTop: Sizes.dp100 }} />;
        }

        const list = [];
        if (!dataPermission?.doctorNotAllowedCheckRegistration && !!registrationFee) {
            // 显示挂号相关费用
            list.unshift({
                name: clinicBasicSetting?.registrationFeeStr,
                percent: registrationFee ? registrationFee / totalAmount : 0,
                amount: registrationFee,
            });
        }
        if (!dataPermission?.doctorNotAllowedCheckOutpatient && !!diagnosisTreatmentFee?.length) {
            // 显示诊疗相关费用
            list.push(
                ...diagnosisTreatmentFee
                    .filter((item) => item.field != FeeTypeId.rechargeMemberFee)
                    ?.map((item) => ({
                        name: item.label ?? "-",
                        percent: item._data ? item._data / totalAmount : 0,
                        amount: item._data ?? 0,
                    }))
            );
        }
        if (!!consultingFee?.length) {
            // 显示咨询相关费用
            list.push(
                ...consultingFee
                    .filter((item) => item.field != FeeTypeId.rechargeMemberFee)
                    ?.map((item) => ({
                        name: item.label ?? "-",
                        percent: item._data ? item._data / totalAmount : 0,
                        amount: item._data ?? 0,
                    }))
            );
        }
        return (
            <View style={{ flex: 1 }}>
                {list.map((item, index) => {
                    return (
                        <PercentItemView
                            key={index}
                            name={item.name ?? ""}
                            percent={item.percent}
                            amount={item.amount}
                            style={{ paddingLeft: 0 }}
                        />
                    );
                })}
            </View>
        );
    }

    private _renderContentView(): JSX.Element {
        return (
            <View style={{ flex: 1, borderRadius: Sizes.dp6, paddingHorizontal: Sizes.dp16, backgroundColor: Colors.white }}>
                <Text style={[TextStyles.t16MB, { lineHeight: Sizes.dp62 }]}>费用类型</Text>
                {this._renderFeeModesView()}
            </View>
        );
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1 }}>
                {this._renderDoctorSummaryView()}
                <SizedBox height={Sizes.dp18} />
                {this._renderContentView()}
            </View>
        );
    }
}
