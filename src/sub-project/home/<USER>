/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/7/11
 *
 * @description
 *
 */
import { Image, ScrollView, Text, View } from "@hippy/react";
import React from "react";
import { BaseBlocComponent, BaseComponent } from "../base-ui/base-component";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";
import { Clinic, ModuleIds, RolesType, userCenter } from "../user-center/user-center";
import { GridView } from "../base-ui/views/grid-view";
import { UIUtils } from "../base-ui/utils";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import {
    HomeDataFetchedState,
    HomePageBloc,
    TodoInventoryFetchState,
    UnReadOutpatientMsgFetchState,
    UnReadRevisitMsgFetchState,
} from "./home-page-bloc";
import _ from "lodash";
import { URLProtocols } from "../url-dispatcher";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { LogUtils } from "../common-base-module/log";
import { ChargeAbcUrlUtils } from "../charge/charge-abc-url-utils";
import { DebugPage } from "../debug/debug-page";
import { IconFontView, SizedBox, Spacer } from "../base-ui";
import { ClinicChangePage } from "../clinics/clinic-change-page";
import { DoctorShareDialog } from "../share/doctor-share-dialog";
import { AbcView } from "../base-ui/views/abc-view";
import { Badge } from "../base-ui/badge/badge";
import { BehaviorSubject } from "rxjs";
import { StreamBuilder } from "../base-ui/views/stream-builder";
import { ABCEmptyView } from "../base-ui/views/empty-view";
import { InventoryInCreatePage } from "../inventory/inventory-in/inventory-in-create-page";
import { InventoryInConst } from "../inventory/inventory-in/inventory-in-data/inventory-in-const";
import { HomeScanPage } from "./home-scan-page";
import { HomeStatModuleView } from "./views/home-stat-module-view";
import { RightArrowView } from "../base-ui/iconfont/iconfont-view";
import { DeviceUtils } from "../base-ui/utils/device-utils";
import { runtimeConstants } from "../data/constants/runtime-constants-manager";
import { WxApi } from "../base-business/wxapi/wx-api";
import { environment, ServerEnvType } from "../base-business/config/environment";
import { Version } from "../base-ui/utils/version-utils";
import { AppInfo } from "../base-business/config/app-info";
import { ExpirePaymentTips } from "../views/expire-payment-dialog/expire-payment-tips";
import { WebviewPage } from "../base-ui/webview-page";
import { showConfirmDialog } from "../base-ui/dialog/dialog-builder";
import { statEvent } from "../base-business/stat/stat-manager";
import { wxLoginHelper } from "../login/wx-login-helper";
import { FeatureAuthority } from "../user-center/data/constants";
import { ApiMixService } from "../data/ApiMixService";
import { AbcText } from "../base-ui/views/abc-text";
import URL from "url";
import TimeUtils from "../common-base-module/utils/time-utils";
import { ABCApiNetwork } from "../net";

const styles = {
    quickOperationLine: {
        flex: 1,
        height: Sizes.dpHalf,
        backgroundColor: Colors.P5,
    },
};

enum _GridItemId {
    outpatient,
    register,
    charge,
    pharmacy,
    nurseStation,
    inventory,
    examination,
    statistics,
    quickOutpatient,
    quickCharge,
    quickScan, // 扫码入库
    inventoryTask, // 盘点任务
    onlineCommunication, //在线沟通
    yuyueKanban, //在线沟通
    abcZhiCaiMall, //ABC直采商城
    revisit, // 随访
    orderCloud = 16, //  订单云
    settlement = 17, // 清算
    voiceRoomCheck = 18, // 语音查房
}

class _HomeGridItem {
    id: _GridItemId;
    title: string;
    iconName: string;
    clickUrl?: string;
    ids: ModuleIds[];
    newMessageCount?: BehaviorSubject<number | string>;
    fontColor?: Color;
    chainAdmin?: boolean; //只有连锁管理员才显示
    implemented?: boolean; //功能是否实现，default true
    useImageLogo?: boolean;

    clickAction?(): void;

    constructor(params: Readonly<_HomeGridItem>) {
        const { id, ids, title, clickUrl, chainAdmin, iconName, fontColor, implemented, useImageLogo } = params;
        this.id = id;
        this.ids = ids;
        this.title = title;
        this.clickUrl = clickUrl;
        this.chainAdmin = chainAdmin;
        this.iconName = iconName;
        this.fontColor = fontColor;
        this.implemented = implemented;
        this.clickAction = params.clickAction;
        this.useImageLogo = useImageLogo;

        this.newMessageCount = new BehaviorSubject<number | string>(0);
    }
}

interface HomePageProps {}

export class HomePage extends BaseBlocComponent<HomePageProps, HomePageBloc> {
    // 药店模块
    static PHARMACY_NAV_MODULE_LIST: _HomeGridItem[] = [
        new _HomeGridItem({
            id: _GridItemId.inventory,
            ids: [ModuleIds.pharmacyInventory, ModuleIds.pharmacyGoods, ModuleIds.pharmacyStocktaking],
            title: "库存",
            iconName: "home_navi_kucun",
            chainAdmin: true,
            clickUrl: "",
            clickAction() {
                ABCNavigator.navigateToPage(URLProtocols.INVENTORY_DASHBOARD);
            },
        }),
        new _HomeGridItem({
            id: _GridItemId.statistics,
            ids: [ModuleIds.pharmacyReportForm, ModuleIds.pharmacyRevenueStatistics, ModuleIds.pharmacyPerformanceStatistics],
            title: "统计",
            iconName: "home_navi_tongji",
            chainAdmin: true,
            clickUrl: URLProtocols.STAT_CLINIC_PERFORMANCE,
        }),
        // 出现时机：1、总部：该连锁历史至少有绑定一家网店；2、门店：该门店历史至少有绑定一家网店
        new _HomeGridItem({
            id: _GridItemId.orderCloud,
            ids: [],
            title: "订单云",
            iconName: "home_navi_cloud",
            chainAdmin: true,
            clickUrl: URLProtocols.ORDER_CLOUD,
        }),
        new _HomeGridItem({
            id: _GridItemId.settlement,
            ids: [ModuleIds.pharmacyMedicalInsurance],
            title: "清算单",
            iconName: "home_nav_qingsuandan",
            chainAdmin: false,
            clickUrl: URLProtocols.SHEBAO_SETTLEMENT,
        }),
    ];
    // 药店-快捷操作模块
    static PHARMACY_QUICK_OPERATION_LIST: _HomeGridItem[] = [
        new _HomeGridItem({
            id: _GridItemId.inventoryTask,
            ids: [ModuleIds.pharmacyInventory, ModuleIds.pharmacyStocktaking],
            title: "盘点任务",
            iconName: "home_quick_inventory_task",
            chainAdmin: true,
            clickUrl: "",
            clickAction() {
                const { InventoryAddListPage } = require("../inventory/inventory-check/full-check/inventory-add-list-page");
                ABCNavigator.navigateToPage(<InventoryAddListPage />).then();
            },
        }),
    ];

    get isDrugstoreButler(): boolean {
        return !!userCenter.clinic?.isDrugstoreButler;
    }

    static isNormalHospital(): boolean {
        return !!userCenter.clinic?.isNormalHospital;
    }

    static zhiCaiMallIcon(): string {
        if (TimeUtils.isCorrespondingMonth(11)) return "home_zhicai_double_eleven";
        else if (TimeUtils.isCorrespondingMonthRange(12, 10, 31)) return "home_zhicai_double_twelfth";
        return "home_zhicai_mall";
    }

    // 统计的模块权限（医院与诊所会有所不同）
    static statisticsModuleList(): ModuleIds[] {
        if (HomePage.isNormalHospital()) {
            return [
                ModuleIds.MODULE_ID_STATISTICS,
                ModuleIds.MODULE_ID_STATISTICS_BUSINESS,
                ModuleIds.MODULE_ID_HOSPITAL_REVENUE_DOCTOR_ACHIEVEMENT,
                ModuleIds.MODULE_ID_HOSPITAL_REVENUE_CHARGE_REPORT,
                ModuleIds.MODULE_ID_HOSPITAL_MEDICAL_STATISTICS,
                ModuleIds.MODULE_ID_HOSPITAL_OPERATION_OVERVIEW,
            ];
        }
        return [
            ModuleIds.MODULE_ID_STATISTICS,
            ModuleIds.MODULE_ID_STATISTICS_BUSINESS_OVERVIEW,
            ModuleIds.MODULE_ID_STATISTICS_CLINIC_REVENUE,
            ModuleIds.MODULE_ID_STATISTICS_BUSINESS,
            ModuleIds.MODULE_ID_STATISTICS_OPERATIONAL_SITUATION,
            ModuleIds.MODULE_ID_STATISTICS_ACHIEVEMENT,
            ModuleIds.MODULE_ID_STATISTICS_ACHIEVEMENT_BILLING,
            ModuleIds.YUN_YING_RI_BAO,
            ModuleIds.YUN_YING_GAI_KUANG,
        ];
    }

    static NAVI_MODULE_LIST: _HomeGridItem[] = [
        new _HomeGridItem({
            id: _GridItemId.register,
            ids: [ModuleIds.MODULE_ID_REGISTER],
            title: "挂号预约",
            iconName: "home_navi_guahao",
            chainAdmin: false,
            clickUrl: URLProtocols.REGISTRATION_TAB,
        }),

        new _HomeGridItem({
            id: _GridItemId.yuyueKanban,
            ids: [ModuleIds.MODULE_ID_OUTPATIENT, ModuleIds.hospitalOutpatientStation],
            title: "我的预约",
            iconName: "home_navi_yuyue_kanban",
            chainAdmin: false,
            clickAction() {
                ABCNavigator.navigateToPage(`${URLProtocols.REGISTRATION_TAB}?doctorId=${userCenter.employee?.id}`);
            },
        }),

        new _HomeGridItem({
            id: _GridItemId.outpatient,
            ids: [ModuleIds.MODULE_ID_OUTPATIENT, ModuleIds.hospitalOutpatientStation],
            title: "门诊",
            iconName: "home_navi_jiezhen",
            chainAdmin: false,
            clickUrl: URLProtocols.OUTPATIENT_TAB,
        }),

        new _HomeGridItem({
            id: _GridItemId.charge,
            ids: [ModuleIds.MODULE_ID_CHARGE, ModuleIds.hospitalChargeStation],
            title: "收费",
            iconName: "home_navi_shoufei",
            chainAdmin: false,
            clickUrl: URLProtocols.CHARGE_TAB,
        }),

        new _HomeGridItem({
            id: _GridItemId.pharmacy,
            ids: [ModuleIds.MODULE_ID_PHARMACY, ModuleIds.hospitalPharmacyStation],
            title: "药房",
            iconName: "home_navi_yaofang",
            chainAdmin: false,
            clickAction() {
                ABCNavigator.navigateToPage(URLProtocols.PHARMACY_TAB);
            },
        }),
        new _HomeGridItem({
            id: _GridItemId.nurseStation,
            ids: [ModuleIds.MODULE_ID_NURSE_STATION],
            title: "执行站",
            iconName: "home_navi_hushizhan",
            chainAdmin: false,
            clickAction() {
                ABCNavigator.navigateToPage(URLProtocols.EXECUTE_TAB);
            },
        }),

        new _HomeGridItem({
            id: _GridItemId.inventory,
            ids: [
                ModuleIds.MODULE_ID_INVENTORY,
                ModuleIds.MODULE_ID_INVENTORY_MEDICINE,
                ModuleIds.MODULE_ID_INVENTORY_IN,
                ModuleIds.MODULE_ID_INVENTORY_OUT, //     库存 - 出库
                ModuleIds.MODULE_ID_INVENTORY_EXCHANGE, //库存 - 调拨
                ModuleIds.MODULE_ID_INVENTORY_STOCK, //   库存-盘点权限
                ModuleIds.MODULE_ID_INVENTORY_PURCHASE_IN, //采购入库
                ModuleIds.MODULE_ID_INVENTORY_APPLY_IN, //领用
                ModuleIds.MODULE_ID_INVENTORY_LOSS_OUT, //报损
            ],
            title: "库存",
            iconName: "home_navi_kucun",
            chainAdmin: true,
            clickUrl: "",
            clickAction() {
                ABCNavigator.navigateToPage(URLProtocols.INVENTORY_DASHBOARD);
            },
        }),

        // new _HomeGridItem({
        //     id: _GridItemId.examination,
        //     ids: [ModuleIds.MODULE_ID_EXAMINATION],
        //     title: "检查检验",
        //     iconName: "home_navi_jianyan",
        //     chainAdmin: false,
        //     clickUrl: "",
        //     implemented: false,
        //     fontColor: Colors.T4,
        //     clickAction() {
        //         ABCNavigator.navigateToPage(<UnImplementPage title={"检查检验"} />);
        //     },
        // }),
        new _HomeGridItem({
            id: _GridItemId.statistics,
            ids: HomePage.statisticsModuleList(),
            title: "统计",
            iconName: "home_navi_tongji",
            chainAdmin: true,
            clickUrl: URLProtocols.STAT_CLINIC_PERFORMANCE,
        }),
        new _HomeGridItem({
            id: _GridItemId.revisit, // 随访,
            ids: [ModuleIds.MODULE_ID_PATIENT_REVISITED, ModuleIds.MODULE_ID_PATIENT],
            title: "随访",
            iconName: "home_quick_check",
            chainAdmin: true,
            clickUrl: "",
            clickAction() {
                const { RevisitTabPage } = require("../patients/revisited/revisit-tab-page");
                ABCNavigator.navigateToPage(<RevisitTabPage />).then();
            },
        }),
        new _HomeGridItem({
            id: _GridItemId.onlineCommunication,
            ids: [ModuleIds.MODULE_ID_PATIENT, ModuleIds.ZAI_XIAN_GOU_TONG],
            title: "微信沟通",
            iconName: "home_navi_communication",
            chainAdmin: true,
            clickAction() {
                const { OnlineConversationListPage } = require("../patients/online-conversation-list-page");
                ABCNavigator.navigateToPage(<OnlineConversationListPage />);
            },
        }),
        new _HomeGridItem({
            id: _GridItemId.abcZhiCaiMall,
            ids: [ModuleIds.MODULE_ID_NONE],
            title: "直采商城",
            iconName: HomePage.zhiCaiMallIcon(),
            useImageLogo: true,
            clickAction() {
                const _userName = environment.serverEnvType == ServerEnvType.normal ? "gh_8780ede6c1fd" : "gh_a9fda1ce6263";
                // 自动带入门店信息，不用再选择一次
                const path = URL.format({
                    pathname: "pages/goods/goods-validate/index",
                    query: {
                        clinicId: userCenter.clinic?.clinicId,
                        toHome: 1,
                    },
                });

                WxApi.launchMiniProgram({
                    userName: _userName,
                    miniProgramType: environment.serverEnvType == ServerEnvType.normal ? 0 : 1,
                    path: path,
                });
            },
        }),
        new _HomeGridItem({
            id: _GridItemId.settlement,
            ids: [ModuleIds.pharmacyMedicalInsurance],
            title: "清算单",
            iconName: "home_nav_qingsuandan",
            chainAdmin: false,
            clickUrl: URLProtocols.SHEBAO_SETTLEMENT,
        }),

        // 添加语音查房
        new _HomeGridItem({
            id: _GridItemId.voiceRoomCheck,
            ids: [ModuleIds.MODULE_ID_OUTPATIENT, ModuleIds.hospitalOutpatientStation],
            title: "语音查房",
            iconName: "robot",
            chainAdmin: false,
            clickUrl: "",
            async clickAction() {
                // const { VoiceCheckPage } = require("../voice-room-check/VoiceCheckPage");
                // ABCNavigator.navigateToPage(<VoiceCheckPage />).then();
                let content = "";
                const res = await ABCApiNetwork.connectSSE("ai/analysis/text", {
                    method: "POST",
                    body: {
                        text: "[00:00] 哪里不舒服\n[00:03]头有点痛[00:06]痛了多久了[00:10]大概三天左右了",
                        data: {
                            chineseExamination: {
                                value: 0, // TODO switchSetting.chineseExamination,
                            },
                            physicalExamination: {
                                value: 0, //TODO switchSetting.physicalExamination,
                            },
                            medicalRecordType: {
                                value: 0, //TODO switchSetting.type,
                            },
                        },
                        promptName: "asr-generate-medical-record",
                    },
                    listeners: {
                        onopen: (event) => {
                            console.log("连接已打开:", event.connectionId);
                        },
                        onmessage: (event) => {
                            const data = JSON.parse(event.data);
                            if (!data || data.code !== 200) {
                                console.error("接口错误:", data);
                                return;
                            }

                            // 处理回答内容
                            if (data.data.answer) {
                                content += data.data.answer;
                            }
                            console.log("收到消息:", data);
                        },
                        onerror: (event) => {
                            console.error("连接错误:", event.error);
                        },
                    },
                });
                console.log("全部结束", content, "res", res);
            },
        }),
    ];

    //快速事件列表
    static QUICK_OPERATION_LIST: _HomeGridItem[] = [
        new _HomeGridItem({
            id: _GridItemId.quickOutpatient,
            ids: [ModuleIds.MODULE_ID_OUTPATIENT, ModuleIds.hospitalOutpatientStation],
            title: "快速接诊",
            iconName: "home_quick_outpatient",
            chainAdmin: false,
            clickAction: () => {
                //创建线上草稿单
                const { OutpatientAgent } = require("../outpatient/data/outpatient");
                const { OutpatientInvoicePage } = require("../outpatient/outpatient-invoice-page");
                const { showConfirmDialog } = require("../base-ui/dialog/dialog-builder");
                const { errorToStr } = require("../common-base-module/utils");
                OutpatientAgent.postCreateOnlineDraft({ type: 0 })
                    //@ts-ignore
                    .then(async (rsp) => {
                        const _rsp = await ABCNavigator.navigateToPage(<OutpatientInvoicePage outpatientId={rsp.id} />);
                        if (_rsp != undefined) {
                            const { OutpatientTabPage } = require("../outpatient/outpatient-tab-page");
                            ABCNavigator.navigateToPage(<OutpatientTabPage />).then();
                        }
                    })
                    //@ts-ignore
                    .catch((error) => {
                        if (userCenter.isTestLogin) {
                            ABCNavigator.navigateToPage(<OutpatientInvoicePage />).then();
                        } else showConfirmDialog("提示", errorToStr(error)).then();
                    });
            },
        }),

        new _HomeGridItem({
            id: _GridItemId.quickCharge,
            ids: [ModuleIds.MODULE_ID_CHARGE],
            title: "零售收费",
            iconName: "home_quick_direct_charge",
            chainAdmin: false,
            clickUrl: URLProtocols.CHARGE_SHEET + "?" + ChargeAbcUrlUtils.CHARGE_ABCURL_PARAMS_DIRECTCHARGE + "=true",
        }),

        new _HomeGridItem({
            id: _GridItemId.quickScan,
            ids: [ModuleIds.MODULE_ID_INVENTORY, ModuleIds.MODULE_ID_INVENTORY_PURCHASE_IN],
            title: "扫码入库",
            iconName: "home_quick_scan",
            chainAdmin: true,
            clickUrl: "",
            clickAction() {
                InventoryInCreatePage.show({
                    createType: 1,
                    draftId: InventoryInConst.inventoryInTypeScanDraftFileName,
                });
            },
        }),

        new _HomeGridItem({
            id: _GridItemId.inventoryTask,
            ids: [ModuleIds.MODULE_ID_INVENTORY, ModuleIds.MODULE_ID_INVENTORY_STOCK],
            title: "盘点任务",
            iconName: "home_quick_inventory_task",
            chainAdmin: true,
            clickUrl: "",
            clickAction() {
                const { InventoryAddListPage } = require("../inventory/inventory-check/full-check/inventory-add-list-page");
                ABCNavigator.navigateToPage(<InventoryAddListPage />).then();
            },
        }),
    ];

    _naviModuleList: _HomeGridItem[] = this.isDrugstoreButler ? HomePage.PHARMACY_NAV_MODULE_LIST : HomePage.NAVI_MODULE_LIST;

    //快捷操作列表
    _quickOperationList: _HomeGridItem[] = this.isDrugstoreButler ? HomePage.PHARMACY_QUICK_OPERATION_LIST : HomePage.QUICK_OPERATION_LIST;

    constructor(props: HomePageProps) {
        super(props);
        this.bloc = new HomePageBloc();
        this._updateModuleId(userCenter.clinic);
    }

    componentDidMount(): void {
        const _mallItem = this._naviModuleList.find((item) => item.id == _GridItemId.abcZhiCaiMall);
        _mallItem?.newMessageCount?.next("recommend");
        this.bloc.state
            .subscribe((state) => {
                if (state instanceof HomeDataFetchedState) {
                    const { clinic } = state;
                    this._updateModuleId(clinic);
                    const item = this._quickOperationList.find((item) => item.id == _GridItemId.inventoryTask);
                    item?.newMessageCount?.next(state.stockCheckTask);

                    const inventoryTodoCount = this._naviModuleList.find((item) => item.id == _GridItemId.inventory);
                    inventoryTodoCount?.newMessageCount?.next(state.inventoryTodoCount?.allCount ?? 0);

                    const regTodoCount = this._naviModuleList.find((item) => item.id == _GridItemId.register);
                    regTodoCount?.newMessageCount?.next(state.regUnAuditMsgCount ?? 0);

                    // const toBeDeliveryCount = this._naviModuleList.find((item) => item.id == _GridItemId.pharmacy);
                    // toBeDeliveryCount?.newMessageCount?.next(state.inventoryTodoCount?.toBeDeliveryCount ?? 0);
                    this.setState({});
                } else if (state instanceof UnReadOutpatientMsgFetchState) {
                    const item = this._naviModuleList.find((item) => item.id == _GridItemId.outpatient);
                    item?.newMessageCount?.next(state.unReadOutpatientMsg);
                } else if (state instanceof UnReadRevisitMsgFetchState) {
                    const item = this._naviModuleList.find((item) => item.id == _GridItemId.onlineCommunication);
                    item?.newMessageCount?.next(state.unReadRevisitMsg);
                } else if (state instanceof TodoInventoryFetchState) {
                    const item = this._naviModuleList.find((item) => item.id == _GridItemId.inventory);
                    item?.newMessageCount?.next(state.inventoryTodoCount?.allCount ?? 0);
                }
            })
            .addToDisposableBag(this);
    }

    private _updateModuleId(clinic?: Clinic) {
        const MCConfig = ApiMixService.cacheMCConfig;
        const filterModules = (modules: _HomeGridItem[]) => {
            if (!userCenter.clinic?.isDentistryClinic) {
                modules = modules.filter((item) => item.id != _GridItemId.yuyueKanban);
            } else {
                //口腔需要调整入口文案
                modules.forEach((item) => {
                    if (item.id == _GridItemId.register) {
                        item.title = "预约";
                    }
                });
            }
            if (clinic!.isChainAdminClinic) {
                modules = modules.filter((item) => item.chainAdmin);
            }

            const greaterThan270 = new Version(AppInfo.appVersion).compareTo(new Version("2.7.0.0100")) > 0;
            const weChatInstalled = wxLoginHelper._weChatInstalled;
            if (!greaterThan270 || !weChatInstalled) {
                modules = modules.filter((item) => item.id != _GridItemId.abcZhiCaiMall);
            }

            //校验微信沟通是否显示
            if (!(MCConfig?.isWeClinicOpen || this.bloc.currentState.mcConfig?.isWeClinicOpen) || !!userCenter.clinicEdition?.isExpired) {
                modules = modules.filter((item) => item.id != _GridItemId.onlineCommunication);
            }
            //检验随访是否显示
            if (!userCenter.clinicEdition?.checkFeaturePurcheased(FeatureAuthority.CRM_REVISIT)) {
                modules = modules.filter((item) => item.id != _GridItemId.revisit);
            }
            //快速接诊是否启用
            if (!this.bloc.currentState.isOpenQuickDiagnosis) {
                modules = modules.filter((item) => item.id != _GridItemId.quickOutpatient);
            }
            //零售收费是否启用
            if (!this.bloc.currentState.isOpenDirectSale) {
                modules = modules.filter((item) => item.id != _GridItemId.quickCharge);
            }
            // 订单云是否显示
            if (!userCenter.ecAuthBindList?.isShowOrderCloud) {
                modules = modules.filter((item) => item.id != _GridItemId.orderCloud);
            }
            // 清算单是否显示（目前只有成都地区的诊所+药店显示）
            if (!userCenter.shebaoConfig?._isEnableShebaoSettle) {
                modules = modules.filter((item) => item.id != _GridItemId.settlement);
            }

            const moduleIds = clinic!.moduleIds;
            const ids = moduleIds?.split(",").map((item) => parseInt(item)) ?? [];
            if (clinic!.roleId == Clinic.ROLEID_MANAGER || ids?.indexOf(ModuleIds.MODULE_ID_NONE) >= 0) {
                return modules;
            } else {
                const newModules: _HomeGridItem[] = [];
                if (moduleIds != null) {
                    modules?.forEach((module) => {
                        if (_.intersection(module.ids, ids).length > 0) {
                            newModules.push(module);
                        } else if (userCenter.clinic?.isDentistryClinic) {
                            //     口腔诊所--有患者模块，如果是医生、医助、管理员权限的话，则门诊和快速接诊可见
                            const hasPatientModule = ids?.indexOf(ModuleIds.MODULE_ID_PATIENT) > -1,
                                hasOutpatientAuthority =
                                    userCenter.clinic?.isDoctor || userCenter.clinic?.isAdministrator || userCenter.clinic?.isDoctorAssist;
                            if (
                                hasPatientModule &&
                                hasOutpatientAuthority &&
                                (module.id == _GridItemId.outpatient || module.id == _GridItemId.quickOutpatient)
                            ) {
                                newModules.push(module);
                            }
                        } else if (userCenter.shebaoConfig?._isEnableShebaoSettle) {
                            // 成都地区开启清算单，需要显示清算单,但是只有有医保权限的才可以看到
                            if (module.id == _GridItemId.settlement && ids?.indexOf(ModuleIds.pharmacyMedicalInsurance) > -1) {
                                newModules.push(module);
                            }
                        } else if (this.isDrugstoreButler) {
                            // 药店---如果有绑定的网店，则显示订单云
                            if (module.id == _GridItemId.orderCloud) {
                                newModules.push(module);
                            }
                        }
                    });
                }
                return newModules;
            }
        };

        this._naviModuleList = filterModules(this.isDrugstoreButler ? HomePage.PHARMACY_NAV_MODULE_LIST : HomePage.NAVI_MODULE_LIST);
        this._quickOperationList = filterModules(
            this.isDrugstoreButler ? HomePage.PHARMACY_QUICK_OPERATION_LIST : HomePage.QUICK_OPERATION_LIST
        );
    }

    private _jumpToRenewalPage(): void {
        const clinicId = userCenter.clinic?.clinicId;
        const chainId = userCenter.clinic?.chainId;
        const employeeId = userCenter.employee?.id ?? userCenter.employee?.employeeId;
        const url = `https://${environment.webViewPageOaName}/share/order-mobile-renewal?chainId=${chainId}&clinicId=${clinicId}&employeeId=${employeeId}&isApp=1`;
        //判断当前url是否是跳转到H5缴费的地址，再判断当前app软件的版本是否在2.8.0以上，避免低版本跳转支付不成功
        const lessThan252 = new Version(AppInfo.appVersion).compareTo(new Version("2.8.0")) < 0;
        if (url?.includes("/share/order-mobile-renewal") && lessThan252) {
            statEvent("webview", { map: { type: 1 } });
            showConfirmDialog("跳转缴费页面失败", "该软件版本过低，请先进行升级", undefined, undefined, undefined, "rgba(0,0,0,0.8)");
            return;
        }
        statEvent("webview", { map: { type: 0 } });
        ABCNavigator.navigateToPage(<WebviewPage title={"ABC数字医疗云续费"} uri={url ?? ""} />).then(async () => {
            userCenter.clinicEditionObserver.next();
        });
    }

    public renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        if (!state.clinic) return <View />;

        const itemHeight = Sizes.dp100;

        const _clinicEditionConfig = state._clinicEditionConfig;
        const isShowExpireWarn =
            _clinicEditionConfig?.isExpired || _clinicEditionConfig?.isExpireSoon || _clinicEditionConfig?.isIneffective;
        return (
            <View
                accessibilityLabel={"homePage"}
                style={{
                    flex: 1,
                    ...(DeviceUtils.isOhos() ? {} : { overflow: "hidden" }),
                    // backgroundColor: Colors.white,
                }}
            >
                <View
                    style={[
                        ABCStyles.absoluteFill,
                        {
                            backgroundColor: Colors.theme2,
                            bottom: 10,
                        },
                    ]}
                />
                {this._renderTopBar()}
                {this._naviModuleList.length == 0 && <ABCEmptyView tips={"你还没有权限，请联系管理员"} />}
                {this._naviModuleList.length != 0 && (
                    <View
                        style={{
                            flex: 1,
                            borderWidth: Sizes.dpHalf,
                            borderTopLeftRadius: Sizes.dp7,
                            borderTopRightRadius: Sizes.dp7,
                            borderColor: Colors.white,
                            backgroundColor: Colors.white,
                        }}
                    >
                        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                            {isShowExpireWarn && (
                                <ExpirePaymentTips
                                    clinicEdition={state._clinicEditionConfig}
                                    onJumpToRenewal={() => this._jumpToRenewalPage()}
                                />
                            )}
                            {!userCenter.clinic?.isDrugstoreButler &&
                                (!!userCenter.clinic?.isManager ||
                                    !!userCenter.clinic?.canOfRole(RolesType.ROLE_DOCTOR_ID) ||
                                    userCenter.clinic?.isDispensingEmployee ||
                                    userCenter.clinic?.isTherapist ||
                                    userCenter.clinic?.isChargeEmployee) && (
                                    <HomeStatModuleView topDistance={isShowExpireWarn ? Sizes.dp17 : Sizes.dp24} />
                                )}

                            <GridView
                                itemHeight={itemHeight}
                                crossAxisCount={4}
                                showNetLine={false}
                                showBottomLine={false}
                                mainAxisSpacing={Sizes.dp12} // 模块上下间距
                                style={{
                                    marginVertical: Sizes.dp10,
                                    marginHorizontal: Sizes.dp8,
                                }}
                            >
                                {this._naviModuleList.map((item) => (
                                    <_GridItemView key={item.id} item={item} />
                                ))}
                            </GridView>
                            {!userCenter.clinicEdition?.isExpired && (
                                <View style={ABCStyles.rowAlignCenter}>
                                    <View style={styles.quickOperationLine} />
                                    <Text style={[TextStyles.t12NT4, { marginHorizontal: Sizes.dp14 }]}>快捷操作</Text>
                                    <View style={styles.quickOperationLine} />
                                </View>
                            )}

                            {!userCenter.clinicEdition?.isExpired && (
                                <GridView
                                    itemHeight={itemHeight}
                                    crossAxisCount={4}
                                    showNetLine={false}
                                    showBottomLine={false}
                                    style={{
                                        marginVertical: Sizes.dp10,
                                        marginHorizontal: Sizes.dp8,
                                    }}
                                >
                                    {this._quickOperationList.map((item) => (
                                        <_GridItemView key={item.id} item={item} />
                                    ))}
                                </GridView>
                            )}
                        </ScrollView>
                    </View>
                )}
            </View>
        );
    }

    //构建顶部的用户信息部份
    _renderTopBar(): JSX.Element {
        const { clinicEmployee, clinic, canShowDoctorShare } = this.bloc.currentState;
        const chainName = clinic?.chainName ?? "";
        const clinicName = clinic?.displayName ?? "";

        return (
            <View
                style={{
                    position: "relative",
                    paddingTop: Sizes.dp25,
                    paddingHorizontal: Sizes.listHorizontalMargin,
                }}
            >
                <View style={{ height: UIUtils.safeStatusHeight() }} />
                <AssetImageView
                    name={"home_top_bar"}
                    ignoreTheme={false}
                    style={{ ...ABCStyles.absoluteFillObject }}
                    resizeMode={"cover"}
                />

                <View style={[ABCStyles.rowAlignCenter, {}]}>
                    <View
                        style={{
                            borderRadius: Sizes.dp21,
                            borderWidth: Sizes.dpHalf,
                            borderColor: Colors.white,
                            marginRight: Sizes.dp8,
                            backgroundColor: Colors.white,
                        }}
                    >
                        <AssetImageView
                            name={"avatar_doctor"}
                            style={{
                                position: "absolute",
                                flex: 1,
                                width: Sizes.dp28,
                                height: Sizes.dp28,
                                borderRadius: Sizes.dp21,
                            }}
                        />
                        <Image
                            source={{ uri: clinicEmployee?.headImgUrl ?? "" }}
                            style={{
                                width: Sizes.dp28,
                                height: Sizes.dp28,
                                borderRadius: Sizes.dp14,
                            }}
                            onLongClick={() => ABCNavigator.navigateToPage(<DebugPage />)}
                        />
                    </View>

                    <Text style={[TextStyles.t18NW, { lineHeight: Sizes.dp28 }]}>{clinicEmployee?.name ?? ""}</Text>
                    {canShowDoctorShare && (
                        <IconFontView
                            // marginBottom 解决图标未对齐
                            style={{
                                marginHorizontal: Sizes.dp4,
                                marginBottom: -Sizes.dp2,
                            }}
                            name={"qr_code"}
                            color={Colors.white}
                            size={Sizes.dp18}
                            onClick={() => {
                                this._onQRCodeTap();
                            }}
                        />
                    )}
                    <Spacer />
                    {!userCenter.clinic?.isDrugstoreButler && (
                        <AbcView
                            style={{
                                paddingHorizontal: Sizes.dp8,
                                paddingVertical: Sizes.dp4,
                            }}
                            onClick={() => {
                                this._onBarCodeTap();
                            }}
                        >
                            <IconFontView
                                // marginBottom 解决图标未对齐
                                style={{ marginBottom: -Sizes.dp2 }}
                                name={"scan"}
                                color={Colors.white}
                                size={Sizes.dp18}
                            />
                        </AbcView>
                    )}
                </View>

                {(!_.isEmpty(chainName) || !_.isEmpty(clinicName)) && (
                    <AbcView
                        accessibilityLabel={"切换门店"}
                        style={[
                            {
                                marginTop: Sizes.dp16,
                                marginBottom: Sizes.dp17,
                                flexShrink: 1,
                            },
                        ]}
                        onClick={() => this._onSwitchClinicTap()}
                    >
                        <View style={[ABCStyles.rowAlignCenter]}>
                            {!_.isEmpty(chainName) && !clinic?.isNormalClinic && (
                                <View
                                    style={[
                                        {
                                            flexDirection: "row",
                                            borderRadius: Sizes.dp2,
                                            overflow: "hidden",
                                            backgroundColor: "rgba(255,255,255,0.2)",
                                        },
                                        Sizes.paddingLTRB(Sizes.dp6, 0),
                                    ]}
                                >
                                    <Text
                                        ellipsizeMode={"tail"}
                                        numberOfLines={1}
                                        style={[
                                            TextStyles.t12NW.copyWith({
                                                color: "rgba(255,255,266,0.8)",
                                                lineHeight: Sizes.dp20,
                                            }),
                                            { flexShrink: 1 },
                                        ]}
                                    >
                                        {`${chainName ?? ""}`}
                                    </Text>
                                </View>
                            )}
                        </View>

                        {!_.isEmpty(chainName) && <SizedBox height={Sizes.dp7} />}

                        <View style={[ABCStyles.rowAlignCenter]}>
                            <AbcText numberOfLines={2} style={[TextStyles.t14NW, { flexShrink: 1 }]}>
                                {`${clinicName ?? ""}`}
                            </AbcText>
                            <Spacer />
                            <AbcView
                                style={{
                                    flexDirection: "row",
                                    alignSelf: "flex-end",
                                    paddingBottom: DeviceUtils.isIOS() ? -Sizes.dp2 : Sizes.dp1, // 解决箭头兼容问题
                                }}
                            >
                                {/*<IconFontView name={"arrow_down"} size={Sizes.dp16} color={Colors.white} />*/}
                                <RightArrowView color={Colors.white} size={Sizes.dp16} />
                            </AbcView>
                        </View>
                    </AbcView>
                )}
            </View>
        );
    }

    private _onSwitchClinicTap(): void {
        ABCNavigator.navigateToPage(<ClinicChangePage title={`切换${runtimeConstants.PRODUCT_NAME_THUMB}`} />);
    }

    /// 点击分享二维码
    private async _onQRCodeTap(): Promise<void> {
        await DoctorShareDialog.show(this.bloc.currentState.doctorDetailInfo);
    }

    private async _onBarCodeTap(): Promise<void> {
        let loopStatus = true;
        do {
            await HomeScanPage.scan({
                callback(arg1?: { action?: "user_cancel" | "click_tips" }) {
                    if (arg1?.action == "user_cancel") {
                        loopStatus = false;
                    }
                    loopStatus = false;
                },
            });
        } while (loopStatus);
    }
}

interface _GridItemViewProps {
    item: _HomeGridItem;
}

class _GridItemView extends BaseComponent<_GridItemViewProps> {
    constructor(props: _GridItemViewProps) {
        super(props);
    }

    render() {
        const {
            title,
            iconName,
            fontColor,
            clickUrl,
            clickAction,
            newMessageCount,
            implemented = true,
            useImageLogo = false,
        } = this.props.item;
        return (
            <AbcView
                style={ABCStyles.centerChild}
                onClick={() => {
                    if (userCenter.clinicEdition?.isExpired) {
                        return showConfirmDialog("提示", "您的合约已到期，续费前功能暂停使用");
                    }
                    LogUtils.d("_GridItemView, clickUrl= " + clickUrl);
                    clickAction?.();
                    !!clickUrl && ABCNavigator.navigateToPage(clickUrl);
                }}
            >
                <StreamBuilder
                    stream={newMessageCount!}
                    render={(value) => {
                        return (
                            <Badge
                                value={_.isNumber(value) ? ((value ?? 0) <= 0 ? undefined : value) : value ?? ""}
                                image={useImageLogo && _.isString(value) ? value : undefined}
                                imgPosition={{
                                    top: -Sizes.dp6,
                                    right: -Sizes.dp10,
                                }}
                            >
                                <View>
                                    <AssetImageView
                                        name={iconName}
                                        ignoreTheme={false}
                                        style={{
                                            width: Sizes.dp40,
                                            height: Sizes.dp40,
                                            marginBottom: Sizes.dp12,
                                        }}
                                    />
                                    {!implemented && <View style={[ABCStyles.absoluteFillObject, { backgroundColor: "#ffffff7F" }]} />}
                                </View>
                            </Badge>
                        );
                    }}
                />

                <Text style={[TextStyles.t14NT1, fontColor ? { color: fontColor } : {}]}>{title}</Text>
            </AbcView>
        );
    }
}
