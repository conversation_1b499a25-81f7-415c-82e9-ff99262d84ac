/**
 * create by deng<PERSON>e
 * desc: 主诉
 * create date 2020/4/7
 */

import React from "react";
import { Text, View } from "@hippy/react";

import { Colors, Sizes, TextStyles } from "../../theme";
import { DividerLine } from "../../base-ui";
import { Intellisense } from "../../../assets/medicine_usage/outpatient_intellisense_config";
import { AbcIntelligenceChip } from "../outpatient-views";
import { keyboardListener } from "../../common-base-module/utils/keyboard-listener";
import { AnyType } from "../../common-base-module/common-types";
import _ from "lodash";
import { BaseMedicalRecordPage, BaseMedicalRecordPageProps } from "./base-medical-record-page";
import { userCenter } from "../../user-center";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";

interface ChiefComplaintInputPageProps extends BaseMedicalRecordPageProps {
    chiefComplaint?: string;
}

export default class ChiefComplaintInputPage extends BaseMedicalRecordPage<ChiefComplaintInputPageProps> {
    chiefComplaintArray: Array<string>;

    private showBtn?: boolean;

    protected pageDisplayName = "主诉";

    static async show(options?: ChiefComplaintInputPageProps): Promise<string> {
        if (!!userCenter.clinic?.isDentistryClinic) {
            return DentistryChiefComplaintInputPage.show(options);
        } else {
            return super.show(options);
        }
    }

    constructor(props: ChiefComplaintInputPageProps) {
        super(props);
        this.inputValue = this.props.chiefComplaint ?? "";
        this.chiefComplaintArray = this.inputValue ? this.inputValue.split("，") : [];

        this.showBtn = true;
    }

    componentDidMount(): void {
        keyboardListener
            .subscribe((visible) => {
                if (_.isUndefined(this.showBtn)) {
                    this.showBtn = visible.visible;
                } else {
                    this.showBtn = !visible.visible;
                }

                if (this.showBtn) {
                    this._textInputRef?.blur();
                }

                this.setState({});
            })
            .addToDisposableBag(this);
    }

    _createMainComplain(): JSX.Element {
        const { symptomList1, symptomList2, symptomList3, symptomList4, symptomList5, symptomList6, symptomList7, symptomList8 } =
            Intellisense.mainComplain;
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp20, Sizes.dp16), { backgroundColor: Colors.white }]}>
                <View>
                    {this._createItem([...symptomList1, ...symptomList2], "symptom")}
                    {<DividerLine color={Colors.dividerLineColor} style={{ marginTop: Sizes.dp13, marginBottom: Sizes.dp3 }} />}
                    {this._createItem([...symptomList3, ...symptomList4], "symptom")}
                    {<DividerLine color={Colors.dividerLineColor} style={{ marginTop: Sizes.dp13, marginBottom: Sizes.dp3 }} />}
                    {this._createItem([...symptomList5, ...symptomList6], "symptom")}
                    {<DividerLine color={Colors.dividerLineColor} style={{ marginTop: Sizes.dp13, marginBottom: Sizes.dp3 }} />}
                    {this._createItem([...symptomList7, ...symptomList8], "symptom")}
                </View>
            </View>
        );
    }

    _createCommon(): JSX.Element {
        const { extentCommonList } = Intellisense.mainComplain;
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, 0), { backgroundColor: Colors.white }]}>
                <Text style={[TextStyles.t14MT1, Sizes.paddingLTRB(0, Sizes.dp12, Sizes.dp16, Sizes.dp4)]}>程度</Text>
                <View>{this._createItem(extentCommonList, "common")}</View>
            </View>
        );
    }

    _createItem(list: Array<string>, type: string): JSX.Element {
        return (
            <View
                style={{
                    flexDirection: "row",
                    flexWrap: "wrap",
                }}
            >
                {list.map((item, index) => (
                    <AbcIntelligenceChip key={index} text={item} type={type} onClick={this.handleSelectChip.bind(this)} />
                ))}
            </View>
        );
    }

    renderMedicalRecordContent(): JSX.Element {
        return (
            <View>
                {this._createMainComplain()}
                {this._createCommon()}
            </View>
        );
    }

    handleSelectChip({ type, info }: AnyType): void {
        this._textInputRef?.blur();
        if (type == "symptom") {
            this.handleSelectSymptom(info);
        } else if (type == "common") {
            this.handleSelectCommon(info);
        }
        this.forceUpdate();
    }

    handleSelectSymptom(info: string): void {
        const _chiefComplaintArray = this.chiefComplaintArray;
        const _shouldChange = _chiefComplaintArray.every((item) => {
            return item != info; // false 不可重复添加
        });
        if (!_shouldChange) return;
        _chiefComplaintArray.push(info);
        this.handleSelectChangeText(_chiefComplaintArray);
    }

    handleSelectCommon(info: string): void {
        const _chiefComplaintArray = this.chiefComplaintArray;
        const _lastItem = (_chiefComplaintArray.pop() ?? "") + info;
        _chiefComplaintArray.push(_lastItem);
        this.handleSelectChangeText(_chiefComplaintArray);
    }

    handleSelectChangeText(chiefComplaintArray: string[]): void {
        // 去重
        const setArr = new Set(chiefComplaintArray);
        const _chiefComplaintArray = new Array(...setArr);
        const _chiefComplaintValue = _chiefComplaintArray.length ? _chiefComplaintArray.join("，") : "";
        this.chiefComplaintArray = _chiefComplaintArray;
        this.inputValue = _chiefComplaintValue;
        this._textInputRef?.setValue(this.inputValue);
    }

    handleChangeInputValue(text: string): void {
        super.handleChangeInputValue(text);
        this.chiefComplaintArray = text != "" ? text.split("，") : [];
        this.forceUpdate();
    }
}

export class DentistryChiefComplaintInputPage extends ChiefComplaintInputPage {
    static async show(options?: ChiefComplaintInputPageProps): Promise<string> {
        return showBottomPanel(React.createElement(DentistryChiefComplaintInputPage, { ...options }), { topMaskHeight: Sizes.dp160 });
    }

    renderDentistryPosView(): JSX.Element {
        const {
            dentistry: { toothPos, toothLabel },
        } = Intellisense;
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp20, Sizes.dp16), { backgroundColor: Colors.white }]}>
                {this.renderMedicalRecordSubTitle("牙位")}
                <View>{this._createItem(toothPos, "toothPos")}</View>
                <View>{this._createItem(toothLabel, "toothLabel")}</View>
            </View>
        );
    }

    renderDentistryLabelView(): JSX.Element {
        const {
            dentistry: { symptoms },
        } = Intellisense;
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp20, Sizes.dp16), { backgroundColor: Colors.white }]}>
                {this.renderMedicalRecordSubTitle("症状")}
                <View>{this._createItem(symptoms, "symptoms")}</View>
            </View>
        );
    }

    renderDentistryTimeView(): JSX.Element {
        const {
            dentistry: { timeList },
        } = Intellisense;
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp20, Sizes.dp16), { backgroundColor: Colors.white }]}>
                {this.renderMedicalRecordSubTitle("时间")}
                <View>{this._createItem(timeList, "timeList")}</View>
            </View>
        );
    }

    renderDentistryReqView(): JSX.Element {
        const {
            dentistry: { requirementsList },
        } = Intellisense;
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp20, Sizes.dp16), { backgroundColor: Colors.white }]}>
                {this.renderMedicalRecordSubTitle("要求")}
                <View>{this._createItem(requirementsList, "requirementsList")}</View>
            </View>
        );
    }

    handleSelectChip({ type, info }: AnyType): void {
        this._textInputRef?.blur();

        const {
            dentistry: { toothPos, toothLabel, symptoms, timeList },
        } = Intellisense;
        switch (type) {
            case "toothPos":
            case "toothLabel":
            case "symptoms":
            case "timeList": {
                // 特殊处理 组合选择中间不加逗号
                const lastVal = this.chiefComplaintArray.slice(-1)[0];
                const labelRegExp = new RegExp(`(${toothLabel.join("|")})$`);
                const symRegExp = new RegExp(`(${symptoms.join("|")})$`);
                if (
                    (toothPos.includes(lastVal) && toothLabel.includes(info)) ||
                    (labelRegExp.test(lastVal) && symptoms.includes(info)) ||
                    (symRegExp.test(lastVal) && timeList.includes(info))
                ) {
                    this.handleSelectCommon(info);
                    break;
                }
            }
            case "requirementsList":
            default: {
                this.handleSelectSymptom(info);
            }
        }
    }
    renderMedicalRecordContent(): JSX.Element {
        return (
            <View>
                {this.renderDentistryPosView()}
                {this.renderDentistryLabelView()}
                {this.renderDentistryTimeView()}
                {this.renderDentistryReqView()}
            </View>
        );
    }
}
