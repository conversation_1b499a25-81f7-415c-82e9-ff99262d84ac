/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-28
 *
 * @description
 */
import { Bloc, BlocEvent } from "../../bloc";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { QuickReplaySettingPage } from "./quick-replay-setting-page";
import * as React from "react";
import { CommonReplyCategory, CommonReplyMsgType, ImAgent } from "../data/im-agent";
import { EventName } from "../../bloc/bloc";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../../common-base-module/common-error";

class State {
    categories?: CommonReplyCategory[]; //常用消息回复类别
    loading = false; //加载中
    loadError: any; //加载出错

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {}

class _RequestReloadData extends _Event {}

class QuickReplayBottomSheetBloc extends Bloc<_Event, State> {
    private readonly msgType: CommonReplyMsgType;
    constructor(msgType: CommonReplyMsgType) {
        super();
        this.msgType = msgType;
        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    private _loadDataTrigger = new Subject();
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventRequestSetting, this._mapEventRequestSetting);
        map.set(_RequestReloadData, this._mapRequestReloadData);

        return map;
    }

    async *_mapEventInit(/*ignored: _EventInit*/): AsyncGenerator<State> {
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.loading = true;
                    this.innerState.loadError = null;
                    this.update();
                    return ImAgent.getCommonReplayList(this.msgType)
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.loading = false;
                if (rsp instanceof ABCError) {
                    this.innerState.loadError = rsp;
                } else {
                    this.innerState.categories = rsp;
                }
                this.update();
            })
            .addToDisposableBag(this);
        this._loadDataTrigger.next();
    }

    async *_mapEventUpdate(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState.clone();
    }

    async *_mapEventRequestSetting(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        await ABCNavigator.navigateToPage(<QuickReplaySettingPage categories={this.innerState.categories!} msgType={this.msgType} />);

        yield this.innerState.clone();
    }

    async *_mapRequestReloadData(/*ignored: _RequestReloadData*/): AsyncGenerator<State> {
        this._loadDataTrigger.next();
    }

    update(): void {
        this.dispatch(new _EventUpdate());
    }

    requestSetting(): void {
        this.dispatch(new _EventRequestSetting());
    }

    //加载出错，点击重新加载数据
    requestReloadData(): void {
        this.dispatch(new _RequestReloadData());
    }
}

class _EventRequestSetting extends _Event {}

export { QuickReplayBottomSheetBloc, State };
