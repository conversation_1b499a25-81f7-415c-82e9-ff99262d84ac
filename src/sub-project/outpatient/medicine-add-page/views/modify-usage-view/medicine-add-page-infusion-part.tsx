/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2024/5/16
 * @Copyright 成都字节流科技有限公司© 2024
 */
import { PrescriptionFormItemCell } from "./base-part";
import { BaseComponent } from "../../../../base-ui/base-component";
import { BaseMedicineAddPageBloc } from "../../base-medicine-add-page-bloc";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../../theme";
import { Text, View } from "@hippy/react";
import SizedBox from "../../../../base-ui/sized-box";
import { PsychotropicNarcoticTypeView } from "../../../views/psychotropic-narcotic-type-view";
import { AbcView } from "../../../../base-ui/views/abc-view";
import { AssetImageView } from "../../../../base-ui/views/asset-image-view";
import { CustomInput } from "../../../../base-ui/input/custom-input";
import { LengthLimitingTextInputFormatter, MinMaxLimitFormat, PrecisionLimitFormatter } from "../../../../base-ui/utils/formatter";
import { IconFontView, Spacer } from "../../../../base-ui";
import { AbcBasePanel } from "../../../../base-ui/abc-app-library";
import React from "react";
import { PrescriptionInfusionForm } from "../../../data/outpatient-beans";
import { ActionBtn, StockNotEnoughTextView } from "../../../../base-ui/views/stock-not-enough-text-view";
import { AbcTextInput } from "../../../../base-ui/views/abc-text-input";
import { delayed } from "../../../../common-base-module/rxjs-ext/rxjs-ext";
import { userCenter } from "../../../../user-center";
import { StockInfo } from "../../../../base-business/data/beans";
import { ABCUtils } from "../../../../base-ui/utils/utils";
import {
    InfusionMedicineFreqKeyboard,
    MultiplePharmacyTypeKeyboard,
    WesternMedicineAstKeyboard,
    WesternMedicineFreqKeyboard,
    WesternMedicineRemarkKeyboard,
} from "../../../../base-ui/picker/medicine-usage-picker";
import { JsonMapper } from "../../../../common-base-module/json-mapper/json-mapper";
import { Freq, MedicineUsageAst, WesternMedicineConfigProvider } from "../../../data/western-medicine-config";
import { WesternMedicine } from "../../../../../assets/medicine_usage/western-medicine-config";
import { PharmacyTagView } from "../../../views/pharmacy-tag-view";
import { ChineseUsageItemInfo } from "../../../data/chinese-medicine-config";
import { SelfProvidedText } from "../../../data/outpatient-const";
import { DispensingFormItemsSourceItemType } from "../../../../charge/data/charge-beans";
import { PharmacyType } from "../../../../charge/data/charge-bean-air-pharmacy";
import { UIUtils } from "../../../../base-ui/utils";
import { PsychotropicNarcoticTagView } from "../../../../views/business-tags";

export class PrescriptionInfusionFormItemCell extends PrescriptionFormItemCell {
    private isFocusAst?: boolean = false;
    private selectPharmacy?: boolean = false;
    private isFocusRemark?: boolean = false;

    private _usageRef?: CustomInput | null;
    private _freqRef?: CustomInput | null;
    private _dosageCountRef?: CustomInput | null;
    private _daysRef?: CustomInput | null;
    private _unitCountRef?: CustomInput | null;
    private _astRef?: CustomInput | null;
    private _remarkRef?: CustomInput | null;
    createActionBtn(): ActionBtn[] {
        const { formItem } = this.props;
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        const { pharmacyInfoConfig, formDetail } = _bloc.currentState;
        const list: ActionBtn[] = [];

        list.push({
            text: !!formItem?.ast ? "取消皮试" : "皮试",
            handleClick: () => {
                this.isFocusAst = !formItem?.ast;
                _bloc.requestResetWesternAst(formItem);
                !formItem?.ast &&
                    delayed(100).subscribe(() => {
                        this._astRef?.focus();
                    });
                this.forceUpdate();
            },
        });
        if (pharmacyInfoConfig?.isOpenMultiplePharmacy && !formDetail.isCoClinicPharmacy) {
            list.push({
                text: "药品来源",
                handleClick: () => {
                    this.updateWesternRemark(true);
                },
            });
        }
        if (!!formItem?.specialRequirement) {
            list.push({
                text: "取消备注",
                handleClick: () => {
                    this.deleteWesternRemark();
                },
            });
        } else {
            list.push({
                text: "备注",
                handleClick: () => {
                    this.updateWesternRemark(false);
                },
            });
        }
        list.push({
            text: "处方类型",
            handleClick: async () => {
                await this.stockNotEnoughView?._hideTips();
                _bloc.requestSelectPrescriptionType();
            },
        });
        list.push({
            text: "删除",
            handleClick: () => {
                this.deleteItem();
            },
        });

        return list;
    }
    //修改备注(isSelPharmacy---当前点击的是药房还是备注)
    async updateWesternRemark(isSelPharmacy: boolean): Promise<void> {
        this.selectPharmacy = isSelPharmacy;
        this.isFocusRemark = true;
        this._remarkRef?.focus();
        this.forceUpdate();
        this.stockNotEnoughView?._hideTips();
    }
    computedUsageUnitList(): string[] {
        const { formItem } = this.props,
            { goodsInfo } = formItem;
        const _defaultDosageUnitList = WesternMedicineConfigProvider.getConfig().dosageFormUnit?.map((item) => item.name!);
        return !goodsInfo.usageUnits.length ? _defaultDosageUnitList ?? [] : goodsInfo.usageUnits;
    }
    /**
     * 删除备注
     */
    async deleteWesternRemark(): Promise<void> {
        this.isFocusRemark = false;
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        _bloc.requestDeleteWesternRemark(this.props.formItem);
        this.stockNotEnoughView?._hideTips();
    }
    /**
     * 删除项目
     */
    async deleteItem(): Promise<void> {
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        _bloc.requestDeleteMedicine(this.props.formItem);
        this.stockNotEnoughView?._hideTips();
    }

    async changeUsage(value: string): Promise<void> {
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        _bloc.requestUpdateWesternMedicineUsage(this.props.formItem, value);
    }

    async changeFreq(value: string): Promise<void> {
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        _bloc.requestUpdateWesternMedicineFreq(this.props.formItem, value);
    }

    async changeUsageDaysCount(value: string): Promise<void> {
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        _bloc.requestUpdateWesternMedicineDays(this.props.formItem, value);
    }

    changeIvgttUnit(value: string): void {
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        _bloc.requestUpdateWesternDosageCount(this.props.formItem, value);
    }

    changeInfusionMedicineDosageUnit(value: string): void {
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        _bloc.requestUpdateDosageUnit(this.props.formItem, value);
    }

    changeUnitCount(value: string): void {
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        _bloc.requestUpdateWesternAmountUnitCount(this.props.formItem, value);
    }

    protected renderContent(): JSX.Element {
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context),
            { infusionFormUsageGroup, showErrorHint, pharmacyInfoConfig, outpatientSheetDetail, formDetail } = _bloc.currentState;
        const { formItem } = this.props,
            { goodsInfo } = formItem,
            { departmentId } = outpatientSheetDetail ?? {};
        const isSelfProvided = !!formItem?.chargeType; // 自备药品不需提示库存不足
        const stockInfo = isSelfProvided ? new StockInfo(true) : goodsInfo.stockInfo(formItem?.unit, formItem?.unitCount, 1);
        let unChargedNotips = true;
        if (!!stockInfo) {
            unChargedNotips = userCenter.inventoryClinicConfig?.stockGoodsConfig?.disableNoStockGoods == 2;
        }
        const stockEnough = (stockInfo?.stockEnough || unChargedNotips) ?? true;
        // 显示名称的长度(兼容ipad)
        const flagLength = UIUtils.getScreenWidth() < 680 ? 10 : 18;
        const displayName =
            (goodsInfo.displayName?.length ?? 0) > flagLength
                ? goodsInfo.displayName.substring(0, flagLength) + "..."
                : goodsInfo.displayName;
        //原始单价
        const originalUnitPrice = `${ABCUtils.formatPrice(formItem?.sourceUnitPrice ?? goodsInfo?.unitPrice ?? 0)}/${formItem?.unit ?? ""}`;
        //是否是默认药房
        const defaultPharmacy = pharmacyInfoConfig?.getDefaultPharmacy({
            departmentId: departmentId,
            goodsInfo: formItem.goodsInfo,
        });
        const isShowDefaultPharmacyTag = defaultPharmacy?.no == formItem.pharmacyNo;
        const medicinePharmacyOptions =
            pharmacyInfoConfig?.filterNormalPharmacyList
                ?.map((pharmacy) => {
                    return (pharmacy.no == defaultPharmacy?.no ? pharmacy?.name + "(默认)" : pharmacy?.name) ?? "";
                })
                .filter((item) => !!item) ?? [];

        const { usage, freq, days } = infusionFormUsageGroup.get(formItem.groupId ?? 1) ?? {};
        const medicineUsageAst = WesternMedicine.astList.find((t) => t.value == formItem?.ast);
        const isOpenMultiplePharmacy = pharmacyInfoConfig?.isOpenMultiplePharmacy;
        return (
            <AbcView
                style={[ABCStyles.bottomLine, { paddingVertical: Sizes.dp12, marginHorizontal: Sizes.dp16 }]}
                onLongClick={() => this.stockNotEnoughView?.showOperateTip(() => (this.allowValidTask = !this.allowValidTask))}
            >
                <View style={ABCStyles.rowAlignCenter}>
                    <StockNotEnoughTextView
                        style={{ flexShrink: 1 }}
                        goodsStock={stockInfo}
                        text={displayName}
                        textStyle={{ ...TextStyles.t16NT1, flexShrink: 1 }}
                        ref={(ref) => {
                            this.stockNotEnoughView = ref;
                        }}
                        tipContent={this.createActionBtn()}
                        bgColor={"rgba(51, 51, 51, 0.95)"}
                        hasOtherOperate={true}
                        showNotEnoughIcon={false}
                    />
                    <PsychotropicNarcoticTagView dangerIngredient={goodsInfo.getIngredientArray} antibiotic={goodsInfo.antibiotic} />
                    {!!goodsInfo.packageSpec && <SizedBox width={Sizes.dp12} />}
                    {!!goodsInfo.packageSpec && (
                        <Text
                            numberOfLines={1}
                            style={[TextStyles.t14NT6.copyWith({ color: stockEnough ? Colors.T6 : Colors.Y2 }), { flexShrink: 1 }]}
                        >
                            {goodsInfo.packageSpec}
                        </Text>
                    )}
                    {!stockEnough && (
                        <AssetImageView
                            name={"inventory_alarm"}
                            style={{ width: Sizes.dp14, height: Sizes.dp14, marginLeft: Sizes.dp4 }}
                            onClick={() => {
                                //解决当有输入框激活时，弹出层计算错误问题
                                if (AbcTextInput.focusInput) {
                                    AbcTextInput.focusInput?.blur();
                                    delayed(200).subscribe(() => {
                                        this.showTip().then();
                                    });
                                } else {
                                    this.showTip().then();
                                }
                            }}
                        />
                    )}
                    <Spacer />
                    {userCenter.clinic?.isDentistryClinic && <Text style={TextStyles.t14NT1}>{originalUnitPrice}</Text>}
                </View>
                <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp12 }]}>
                    <CustomInput
                        ref={(ref) => {
                            this._usageRef = ref;
                            if (!!ref) {
                                this.inputRefMap.set(ref, { ref, next: () => this.validInput(this._freqRef) });
                            }
                        }}
                        style={{ flex: 60 }}
                        type={"sheet"}
                        borderType={"boxBorder"}
                        value={usage ?? "用法"}
                        onChange={this.changeUsage.bind(this)}
                        customKeyboardBuilder={new InfusionMedicineFreqKeyboard({ name: usage })}
                        notExistValue={!usage}
                        textStyle={TextStyles.t12NT2.copyWith({ color: Colors.T1 })}
                        containerStyle={{ height: Sizes.dp32 }}
                        onEndEditing={(text) => {
                            !!text && this.validInput(this._usageRef, true).then();
                        }}
                    />

                    <SizedBox width={Sizes.dp6} />

                    <CustomInput
                        ref={(ref) => {
                            this._freqRef = ref;
                            if (!!ref) {
                                this.inputRefMap.set(ref, { ref, next: () => this.validInput(this._daysRef) });
                            }
                        }}
                        style={{ flex: 40 }}
                        type={"sheet"}
                        borderType={"boxBorder"}
                        value={freq ?? "频率"}
                        onChange={this.changeFreq.bind(this)}
                        customKeyboardBuilder={
                            new WesternMedicineFreqKeyboard(
                                JsonMapper.deserialize(Freq, WesternMedicine.freq.find((item) => item.en == freq) as Object)
                            )
                        }
                        notExistValue={!freq}
                        textStyle={TextStyles.t12NT2.copyWith({ color: Colors.T1 })}
                        containerStyle={{ height: Sizes.dp32 }}
                        onEndEditing={(text) => {
                            !!text && this.validInput(this._freqRef, true).then();
                        }}
                    />

                    <SizedBox width={Sizes.dp6} />

                    <CustomInput
                        style={{ flex: 60 }}
                        textStyle={TextStyles.t12NT2.copyWith({ color: Colors.T1 })}
                        type={"input"}
                        borderType={"boxBorder"}
                        value={days ? days : undefined}
                        alwaysShowUtil={true}
                        unit={"天"}
                        formatter={LengthLimitingTextInputFormatter(4)}
                        error={showErrorHint && !days}
                        onChange={this.changeUsageDaysCount.bind(this)}
                        ref={(ref) => {
                            this._daysRef = ref;
                            if (!!ref) {
                                this.inputRefMap.set(ref, { ref, next: () => this.validInput(this._dosageCountRef) });
                            }
                        }}
                        onEndEditing={() => {
                            this.validInput(this._daysRef, true).then();
                        }}
                        containerStyle={{ height: Sizes.dp32 }}
                    />

                    <SizedBox width={Sizes.dp6} />
                    <CustomInput
                        style={{ flex: 60 }}
                        textStyle={TextStyles.t12NT2.copyWith({ color: Colors.T1 })}
                        ref={(ref) => {
                            this._dosageCountRef = ref;
                            if (!!ref) {
                                this.inputRefMap.set(ref, { ref, next: () => this.validInput(this._unitCountRef) });
                            }
                        }}
                        type={"input"}
                        borderType={"boxBorder"}
                        alwaysShowUtil={true}
                        error={showErrorHint && !formItem.dosage}
                        value={!!formItem.dosage ? formItem.dosage : "-"}
                        unit={`${formItem.dosageUnit}`}
                        unitList={this.computedUsageUnitList()}
                        formatter={PrecisionLimitFormatter(4)}
                        onChange={this.changeIvgttUnit.bind(this)}
                        onChangeUnit={this.changeInfusionMedicineDosageUnit.bind(this)}
                        onEndEditing={() => {
                            this.validInput(this._dosageCountRef, true).then();
                        }}
                        containerStyle={{ height: Sizes.dp32 }}
                    />

                    <SizedBox width={Sizes.dp6} />
                    <CustomInput
                        ref={(ref) => {
                            this._unitCountRef = ref;
                            if (!!ref) {
                                this.inputRefMap.set(ref, { ref });
                            }
                        }}
                        style={{ width: Sizes.dp60 }}
                        textStyle={TextStyles.t12NT2.copyWith({ color: Colors.T1 })}
                        borderType={"boxBorder"}
                        type={"input"}
                        alwaysShowUtil={true}
                        value={formItem?.unitCount ?? ""}
                        unit={formItem?.unit}
                        unitList={goodsInfo.sellUnits ?? WesternMedicineConfigProvider.getConfig().unitStringList}
                        formatter={PrecisionLimitFormatter(2)}
                        onChange={this.changeUnitCount.bind(this)}
                        onChangeUnit={(value) => {
                            _bloc.requestUpdateWesternAmountUnit(formItem, value);
                        }}
                        error={showErrorHint && !formItem?.unitCount}
                        onEndEditing={() => {
                            const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
                            _bloc.requestUpdateSearchbarStatus(true);
                        }}
                        containerStyle={{ height: Sizes.dp32 }}
                    />
                </View>
                {this.renderAdjustGroupView()}
                <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp8 }]}>
                    {((!this.isFocusAst && !!medicineUsageAst?.name) || this.isFocusAst) && (
                        <CustomInput
                            ref={(ref) => {
                                this._astRef = ref;
                            }}
                            disable={!!formItem.astResult}
                            style={{
                                height: !!formItem.ast || this.isFocusAst ? undefined : 0,
                                overflow: this.isFocusAst ? undefined : "hidden",
                                flex: 60,
                                marginRight: Sizes.dp6,
                            }}
                            type={"sheet"}
                            borderType={"boxBorder"}
                            value={`${
                                !!formItem.astResult?.result
                                    ? `${formItem.astResult.result}${formItem.astResult.result == "阳性" ? "+" : "-"}`
                                    : medicineUsageAst?.name ?? ""
                            }`}
                            customKeyboardBuilder={new WesternMedicineAstKeyboard(medicineUsageAst ?? new MedicineUsageAst())}
                            onChange={(value) => {
                                _bloc.requestUpdateWesternMedicineAst(
                                    formItem,
                                    WesternMedicine.astList.find((t) => t.name == value)!.value
                                );
                            }}
                            textStyle={TextStyles.t12NT2.copyWith({ color: Colors.T1 })}
                            disableColor={!!formItem?.astResult ? (formItem.astResult.result == "阳性" ? Colors.R2 : Colors.B7) : undefined}
                            containerStyle={{ height: Sizes.dp32 }}
                        />
                    )}
                    <CustomInput
                        startView={
                            isOpenMultiplePharmacy && formDetail.pharmacyType != PharmacyType.coClinic
                                ? () => (
                                      <PharmacyTagView
                                          pharmacyNo={formItem.chargeType ? -1 : formItem.pharmacyNo}
                                          text={!formItem.chargeType ? undefined : "自备"}
                                          style={{ marginRight: Sizes.dp8 }}
                                          defaultPharmacyNo={
                                              pharmacyInfoConfig?.getDefaultPharmacy({
                                                  departmentId: departmentId,
                                                  goodsInfo: { typeId: goodsInfo?.typeId },
                                              })?.no
                                          }
                                      />
                                  )
                                : undefined
                        }
                        ref={(ref) => {
                            this._remarkRef = ref;
                        }}
                        style={{
                            height:
                                !!formItem.specialRequirement ||
                                this.isFocusRemark ||
                                (isOpenMultiplePharmacy && (!isShowDefaultPharmacyTag || !!formItem?.chargeType))
                                    ? undefined
                                    : 0,
                            overflow: this.isFocusRemark ? undefined : "hidden",
                            flex: 243,
                        }}
                        onBlur={() => {
                            this.isFocusRemark = false;
                            this.setState({});
                        }}
                        type={"sheet"}
                        borderType={"boxBorder"}
                        value={`${formItem.specialRequirement ?? "备注"}`}
                        customKeyboardBuilder={
                            this.selectPharmacy
                                ? new MultiplePharmacyTypeKeyboard(
                                      JsonMapper.deserialize(ChineseUsageItemInfo, {
                                          name: !formItem.chargeType
                                              ? isShowDefaultPharmacyTag
                                                  ? formItem.pharmacyName + "(默认)"
                                                  : formItem.pharmacyName ?? ""
                                              : SelfProvidedText,
                                      }),
                                      medicinePharmacyOptions
                                  )
                                : new WesternMedicineRemarkKeyboard(
                                      { id: 0, name: formItem.specialRequirement ?? "" },
                                      !isOpenMultiplePharmacy
                                  )
                        }
                        // customKeyboardBuilder={new MultiLineKeyboard(pharmacyList)}
                        onChange={this.selectPharmacy ? this._changeMultiplePharmacy.bind(this) : this._changeWesternRemark.bind(this)}
                        sheetTypeTextAlign={"left"}
                        notExistValue={!formItem.specialRequirement}
                        textStyle={TextStyles.t12NT2.copyWith({ color: Colors.T1 })}
                        containerStyle={{ height: Sizes.dp32 }}
                    />
                </View>
            </AbcView>
        );
    }

    private async _changeMultiplePharmacy(value: string): Promise<void> {
        const { formItem } = this.props;
        //解决点击输入框时，保证一起拉起的是备注的键盘，而不是药房的
        this.selectPharmacy = false;
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        const { pharmacyInfoConfig } = _bloc.currentState;
        this._remarkRef?._textInput?.blur();
        if (value == SelfProvidedText) {
            _bloc.requestModifyMedicineSelfProvidedStatus(formItem, DispensingFormItemsSourceItemType.noCharge);
            return;
        }
        const defaultIndex = value.indexOf("(默认)");
        let selVal = value;
        if (defaultIndex > -1) {
            selVal = value.substring(-1, defaultIndex);
        }
        const pharmacyInfo = pharmacyInfoConfig?.filterNormalPharmacyList?.find((pharmacy) => pharmacy.name == selVal);
        if (!pharmacyInfo) return;
        _bloc.requestModifyMedicinePharmacy(formItem, pharmacyInfo);
    }
    private async _changeWesternRemark(value: string): Promise<void> {
        const { formItem } = this.props;
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        _bloc.requestUpdateWesternRemark(formItem, value);
    }
}

interface PrescriptionInfusionFormCellProps {
    groupId: number;
}
export class PrescriptionInfusionFormCell extends BaseComponent<PrescriptionInfusionFormCellProps> {
    static contextType = BaseMedicineAddPageBloc.Context;
    private ivgttRef?: CustomInput | null;
    constructor(props: PrescriptionInfusionFormCellProps) {
        super(props);
    }

    renderPrescriptionGroupUsageView(): JSX.Element {
        const { groupId } = this.props;
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        const { formDetail, infusionFormUsageGroup, westernMedicineConfig } = _bloc.currentState;
        const { psychotropicNarcoticType } = formDetail as PrescriptionInfusionForm;
        const medicinesGroup = _bloc.currentState.getPrescriptionItemListWithGroupId(groupId);
        const infusionUsage = infusionFormUsageGroup.get(groupId);
        if (!infusionUsage) return <View />;
        const { ivgtt, ivgttUnit } = infusionUsage;
        return (
            <View
                style={[
                    ABCStyles.bottomLine,
                    ABCStyles.rowAlignCenter,
                    {
                        backgroundColor: Colors.white,
                        marginHorizontal: Sizes.dp12,
                        height: Sizes.dp42,
                    },
                ]}
            >
                <View style={{ marginRight: Sizes.dp10, flexDirection: "row" }}>
                    <SizedBox width={Sizes.dp8} />
                    <View
                        style={{
                            width: Sizes.dp18,
                            height: Sizes.dp18,
                            borderWidth: Sizes.dp1,
                            borderColor: Colors.mainColor,
                            borderRadius: Sizes.dp36,
                        }}
                    >
                        <Text style={[TextStyles.t14NM, { flex: 1, textAlign: "center" }]}>{groupId}</Text>
                    </View>
                    <SizedBox width={Sizes.dp4} />
                    {!!psychotropicNarcoticType && <PsychotropicNarcoticTypeView type={psychotropicNarcoticType} disable={true} />}
                </View>
                <AbcView
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            flex: 1,
                        },
                    ]}
                >
                    <View style={{ flex: 1.345 }} />
                    {!!medicinesGroup.length && <AssetImageView name={"ivgtt_img"} style={{ width: Sizes.dp18, height: Sizes.dp18 }} />}
                    {!!medicinesGroup.length && (
                        <CustomInput
                            borderType={"none"}
                            style={{ flex: 1 }}
                            textStyle={TextStyles.t14NT1}
                            type={"number"}
                            ref={(ref) => {
                                this.ivgttRef = ref;
                            }}
                            alwaysShowUtil={true}
                            value={!!ivgtt ? ivgtt : ""}
                            formatter={MinMaxLimitFormat(1, 100)}
                            unit={ivgttUnit}
                            onChange={(value) => {
                                this.changeUnitCount(value, groupId);
                            }}
                            unitList={westernMedicineConfig?.ivgttUnitList}
                            onChangeUnit={(value) => {
                                this.changeIvgtt(value, groupId);
                            }}
                            disableColor={Colors.t3}
                            unitStyle={{ maxWidth: undefined }}
                        />
                    )}
                    <SizedBox width={Sizes.dp11} />
                    <AbcView
                        style={{
                            paddingVertical: Sizes.dp3,
                        }}
                        onClick={() => {
                            _bloc.requestDeleteGroup(groupId);
                        }}
                    >
                        <IconFontView name={"trash"} color={Colors.T6} size={Sizes.dp18} />
                    </AbcView>
                </AbcView>
            </View>
        );
    }

    render(): JSX.Element {
        const { groupId } = this.props;
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        const { infusionFormUsageGroup } = _bloc.currentState;
        const medicinesGroup = _bloc.currentState.getPrescriptionItemListWithGroupId(groupId);
        const infusionUsage = infusionFormUsageGroup.get(groupId);
        if (!infusionUsage) return <View />;
        return (
            <AbcBasePanel panelStyle={{ marginHorizontal: Sizes.dp8 }}>
                {this.renderPrescriptionGroupUsageView()}
                {medicinesGroup.map((formItem, index) => (
                    <PrescriptionInfusionFormItemCell key={index} formItem={formItem} sort={groupId} />
                ))}
            </AbcBasePanel>
        );
    }

    private changeUnitCount(value: string, groupId: number): void {
        const _bloc = BaseMedicineAddPageBloc.fromContext(this.context);
        _bloc.requestUpdateInfusionPrescriptionIvgtt(groupId, Number(value));
    }

    private changeIvgtt(value: string, groupId: number): void {
        const _value = value.replace(/[^0-9]/gi, "");
        this.ivgttRef?._textInput?.setValue(_value);
        this.changeUnitCount(_value, groupId);
    }
}
