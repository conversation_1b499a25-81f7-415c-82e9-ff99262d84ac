import { GoodsInfo, MedicinePharmacyInfo } from "../../base-business/data/beans";
import { <PERSON>eq, Usage, WesternMedicineConfigProvider } from "../data/western-medicine-config";
import { OutpatientConst } from "../data/outpatient-const";
import _ from "lodash";
import { JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { ChineseUsageItemInfo } from "../data/chinese-medicine-config";
import { ABCUtils } from "../../base-ui/utils/utils";
import { NumberUtils } from "../../common-base-module/utils";
import { AcuPointsItem, ExternalGoodsItem, PrescriptionFormItem, VendorInfo } from "../data/outpatient-beans";
import { AbcMap } from "../../base-ui/utils/abc-map";
import { ChinesePrescriptionUsage, InfusionPrescriptionUsage } from "../data/medicine-add-bean";
import { ChargeStatus } from "../../charge/data/charge-beans";
import { ExternalPRUsageTypeEnum, TieFuUsageSubTypeEnum } from "../../../assets/medicine_usage/outpatient-external-config";
import { WesternMedicine } from "../../../assets/medicine_usage/western-medicine-config";
import { ToothNos } from "../../data/tooth-bean";
import { StringUtils } from "../../base-ui/utils/string-utils";

export class MedicineDosageUsage {
    dosageUnit?: string;
    count?: number;
    externalUnitCount?: number; //贴敷处方-药品使用数量
}

class AstResult {
    result?: string;
    description?: string;
}
export class ProductSupportDays {
    dailyDosage?: number;
    days?: number;
    freq?: string;
}

export class MedicineUsage {
    @JsonProperty({ type: Usage })
    usage?: Usage;

    @JsonProperty({ type: Freq })
    freq?: Freq;

    @JsonProperty({ type: MedicineDosageUsage })
    dosage?: MedicineDosageUsage;
    days?: number;

    unit?: string;
    unitCount: number | undefined = 1;

    @JsonProperty({ type: ChineseUsageItemInfo })
    specialRequirement?: ChineseUsageItemInfo;

    //皮试
    ast?: number | null;
    @JsonProperty({ type: AstResult })
    astResult?: AstResult;

    acuPoints?: AcuPointsItem[];

    /**
     * 计算穴位数量
     */
    _acuPointsCount(): number {
        let count = 0;
        this.acuPoints?.forEach((it) => {
            if (it.name) {
                if (it.position === "双") {
                    count += 2;
                } else {
                    count++;
                }
            }
        });
        return count || 1;
    }

    //议价精度问题导致的总价问题-透传辅助算费-无需回传
    fractionPrice?: number;

    externalGoodsItems?: ExternalGoodsItem[];

    //【feature】诊疗项目支持医生护士添加
    nurseId?: string;
    nurseName?: string;
    doctorId?: string;
    doctorName?: string;
    departmentId?: string;
    departmentName?: string;

    /**
     * 药房信息
     * @param options
     */
    pharmacyInfo?: MedicinePharmacyInfo;

    /**
     * 自备状态
     * 默认-0 | 自备-1
     */
    selfProvidedStatus?: number;

    //牙位数据
    toothNos?: ToothNos;

    //治疗理疗支持录入天数
    @JsonProperty({ type: ProductSupportDays })
    supportInputDays?: ProductSupportDays;

    //议价模块所需字段
    //期望议价
    expectedTotalPrice?: number; //议单项总价
    expectedTotalPriceRatio?: number; //项总价比例
    expectedUnitPrice?: number; //议单价
    //页面显示议价
    totalPrice?: number; //议单项总价
    totalPriceRatio?: number; //项总价比例
    unitPrice?: number;
    sourceUnitPrice?: number; //原始单价

    async computeUnitCount(options: {
        goodsInfo: GoodsInfo;
        usage?: MedicineUsage;
        freq?: Freq;
        dosage?: MedicineDosageUsage;
        days?: number;
    }): Promise<void> {
        //处理freq只有名字没有时间的情况
        if (!this.freq?.time) {
            const config = WesternMedicineConfigProvider.getConfig();
            this.freq = config.freq?.find((it) => it.name == this.freq?.name && it.en == this.freq?.en) ?? this.freq;
        }

        let { freq, dosage, days } = options;
        const { goodsInfo } = options;
        if (_.isEmpty(goodsInfo.packageSpec)) return;
        freq = freq ?? this.freq;
        dosage = _.cloneDeep(dosage ?? this.dosage);
        days = days ?? this.days;

        if (
            freq == null ||
            dosage == null ||
            days == null ||
            dosage.count == null ||
            dosage.dosageUnit == null ||
            dosage.dosageUnit == OutpatientConst.dosageUsageProperty
        )
            return;

        //不需要计算的选项过滤
        if (freq.time == null) return;

        /**
         * @desc 计算单次剂量，分数处理
         * <AUTHOR>
         * @date 2019/01/31 11:29:42
         */
        let preUsageDosage = 1;
        const { numerator, denominator } = NumberUtils.toFraction(dosage?.count ?? "");
        const result = Number(numerator / denominator);
        if (!isNaN(result)) {
            preUsageDosage = result;
        }

        const _unitArr = ["ml", "mg", "g", "IU"];

        const { OutpatientAgent } = require("../data/outpatient");
        const {
            // 输注用量计算策略 0标准 1节约
            infusion = 0,
            // 雾化用量计算策略 0标准 1节约
            nebulizer = 1,
        } = await OutpatientAgent.getUsageConfig();
        /**
         * 根据门诊设置中 开药自动策略计算
         */
        // 默认节约型计算 计算
        let useStandardCount = false;
        // 获取输注用法 type === 2

        const { WesternMedicine } = require("../../../assets/medicine_usage/western-medicine-config");
        //@ts-ignore
        const usageConfig = WesternMedicine.usage.find((it) => {
            return this.usage?.name === it.name && it.type === 2;
        });
        if (usageConfig) {
            if (usageConfig.isNebulizer) {
                useStandardCount = nebulizer === 0;
            } else {
                useStandardCount = infusion === 0;
            }
        }

        // 统一将剂量单位开的数量换算成 制剂的单位用量J
        // 制剂单位 是否为 ml/mg/g/IU
        // - 是
        // -- 可拆零 总量 = J * 天数 * 频率
        // -- 不可拆零 需要针对包装单位换算出包装的单位用量Q 按标准型|节约型 对Q进行ceil，再算包装单位总量
        // - 不是
        // -- 需要 按标准型|节约型 对J进行ceil
        // -- 可拆零 总量 = J * 天数 * 频率
        // -- 不可拆零 总量 = J * 天数 * 频率，再换算包装单位总量

        const { componentContentNum, medicineDosageNum, medicineDosageUnit, pieceNum = 1, pieceUnit, packageUnit, dismounting } = goodsInfo;

        let { componentContentUnit } = goodsInfo;

        if (!componentContentNum) {
            componentContentUnit = "";
        }

        /**
         * @desc 统一将剂量单位开的数量换算成制剂单位的用量
         * @desc 10ml*10支/盒 => 5ml = 0.5支
         * @desc 0.3mg:2ml/支 => 0.15mg = 1ml
         * <AUTHOR>
         * @date 2021-10-20 14:31:01
         */
        if (dosage.dosageUnit === componentContentUnit) {
            if (!_.isNil(componentContentNum)) {
                preUsageDosage /= componentContentNum;
            }
        } else if (dosage.dosageUnit === medicineDosageUnit) {
            if (_unitArr.includes(medicineDosageUnit) && _unitArr.includes(pieceUnit ?? "")) {
                // case4 0.3mg:2ml/支
                preUsageDosage *= (pieceNum ?? 1) / (medicineDosageNum ?? pieceNum ?? 1);
            } else {
                // case1 10ml*10支/盒
                preUsageDosage /= medicineDosageNum ?? 1;
            }
        }

        let unit = "";
        // 制剂单位不为 ml/mg/g/IU 需要提前ceil
        if (!_unitArr.includes(pieceUnit ?? "") && useStandardCount) {
            preUsageDosage = Math.ceil(preUsageDosage);
        }

        if (dismounting) {
            unit = pieceUnit ?? "";
        } else {
            preUsageDosage /= pieceNum;
            unit = packageUnit ?? "";

            // 制剂单位是 ml/mg/g/IU 需要算出 包装单位用量后ceil
            if (_unitArr.includes(pieceUnit ?? "") && useStandardCount) {
                preUsageDosage = Math.ceil(preUsageDosage);
            }
        }

        this.unitCount = Math.ceil(preUsageDosage * (24.0 / Number(freq.time)) * days);
        if (isNaN(this.unitCount)) {
            this.unitCount = undefined;
        }
        this.unit = unit;
    }

    static fromPrescriptionFormItem(item: PrescriptionFormItem): MedicineUsage {
        const usage = JsonMapper.deserialize(MedicineUsage, {
            unit: item.unit ?? ABCUtils.last(item.goodsInfo.sellUnits),
            unitCount: (item.unitCount ?? 0) / (item.externalUnitCount ?? 1),
            usage: JsonMapper.deserialize(Usage, { name: item.usage }),
            freq: JsonMapper.deserialize(Freq, { name: item.freq }),
            days: item.days,
            ast: _.isNumber(item.ast) ? item.ast : null,
            astResult: item.astResult,
            specialRequirement: JsonMapper.deserialize(ChineseUsageItemInfo, {
                name: item.specialRequirement,
            }),
            dosage: JsonMapper.deserialize(MedicineDosageUsage, {
                count: Number(item.dosage ?? ""),
                dosageUnit: item.dosageUnit,
                externalUnitCount: item.externalUnitCount,
            }),
            acuPoints: item.acupoints,
            externalGoodsItems: item.externalGoodsItems,
            nurseId: item.nurseId,
            nurseName: item.nurseName,
            doctorId: item.doctorId,
            doctorName: item.doctorName,
            departmentId: item.departmentId,
            departmentName: item.departmentName,
            selfProvidedStatus: item.chargeType,
            toothNos: item.toothNos,
            supportInputDays: JsonMapper.deserialize(ProductSupportDays, {
                days: item?.days ?? 1,
                dailyDosage: item?.dailyDosage,
                freq: item?.freq,
            }),
            expectedTotalPrice: item?.expectedTotalPrice,
            expectedUnitPrice: item?.expectedUnitPrice,
            expectedTotalPriceRatio: item?.expectedTotalPriceRatio,
            totalPrice: item?.totalPrice,
            totalPriceRatio: item?.totalPriceRatio ?? 1,
            unitPrice: item?.unitPrice ?? item?.goodsInfo?.unitPrice,
            sourceUnitPrice: item?.sourceUnitPrice,
            fractionPrice: item?.fractionPrice,
        });

        if (_.isNaN(usage.dosage!.count)) {
            if ((item.dosage as string)?.includes("/")) {
                //@ts-ignore
                usage.dosage!.count = item.dosage;
                // usage.dosage!.count = eval(item.dosage);
            }
        }

        // const config: WesternMedicineConfig = WesternMedicineConfigProvider.getConfig();
        //上面注释的写法，会导致数据列表和本地存储的不一致，导致频率为空
        const config = WesternMedicine.freq.map((item) => JsonMapper.deserialize(Freq, item));
        let freq = config?.find((freq) => freq.en == item.freq);
        if (!freq) {
            const _freq = config?.filter((item) => item.isCustom).find((freq) => StringUtils.checkEndChar(freq.en ?? "", item.freq ?? ""));
            if (!!_freq) {
                const time = Number(StringUtils.removeHeadAndTail(item.freq ?? "") ?? 0);
                freq = { ..._freq, en: item.freq, time: time * (_freq.hMultiple ?? 0) };
            }
        }
        usage.freq = freq;
        if (!usage.freq) {
            usage.freq = JsonMapper.deserialize(Freq, { name: item.freq });
        }

        //赋值当前药房信息到用法上
        //需要业务自行添加药房的详细信息
        if (!_.isUndefined(item.pharmacyNo)) {
            usage.pharmacyInfo = {
                no: item.pharmacyNo,
                type: item.pharmacyType,
            };
        }

        return usage;
    }
}

export class MedicineAddGroup {
    groupId?: number;
    selectedMedicines: Array<GoodsInfo>; //选择的药品列表
    inputUsages: AbcMap<GoodsInfo, MedicineUsage>; //药品组中的用量信息
    infusionPrescriptionUsage?: InfusionPrescriptionUsage; //输液处方的用量
    chinesePrescriptionUsage?: ChinesePrescriptionUsage; //中药处方的用量
    vendor?: VendorInfo; //空中药房信息
    usageType?: number; //外治处方-类型
    usageSubType?: number; //外治处方-类型
    specification?: string; // 外治处方 - 现配贴药品类型

    constructor() {
        this.selectedMedicines = new Array<GoodsInfo>();
        this.inputUsages = new AbcMap<GoodsInfo, MedicineUsage>();
    }

    /**
     * 判断是否是现配贴
     * 外治处方-类别
     */
    get isSelfPrepared(): boolean {
        return this.usageType == ExternalPRUsageTypeEnum.tieFu && this.usageSubType == TieFuUsageSubTypeEnum.xianPeiTie;
    }

    static fromFormItems(formItems: Array<PrescriptionFormItem>, withGenerate?: boolean): MedicineAddGroup | undefined {
        if (ABCUtils.isEmpty(formItems)) return undefined;
        const group = new MedicineAddGroup();
        if (!!withGenerate) {
            group.inputUsages = new AbcMap<GoodsInfo, MedicineUsage>((it) => it.scrollKey);
        }
        for (const formItem of formItems) {
            const chargeStatus = formItem.chargeStatus;
            if (chargeStatus != null && chargeStatus != ChargeStatus.unCharged) continue;
            //诊疗项目备注是添加到formItem的remark中，每次从门诊列表进入，采用的是formItem中的productInfo，会导致备注没有回填
            if (!!formItem.remark) {
                let _remark = formItem.remark;

                //复制诊疗模板时，需要根据remark，更新对应的acupoints和specialRequirement
                const isExistAcuPoints = _remark.indexOf("[穴位]");
                if (isExistAcuPoints > -1) {
                    const acuPointsCont = _remark.substring(isExistAcuPoints + 4);
                    const acuPointsLastIndex = acuPointsCont.indexOf(";");
                    const acuPointsResult =
                        acuPointsLastIndex == -1 ? acuPointsCont.substring(0) : acuPointsCont.substring(0, acuPointsLastIndex);
                    const acuList: AcuPointsItem[] = [];
                    acuPointsResult
                        .replace(/，/gi, ",")
                        .replace("；", "")
                        .split(",")
                        .forEach((item) => {
                            acuList.push(
                                JsonMapper.deserialize(AcuPointsItem, {
                                    name: item,
                                })
                            );
                        });
                    formItem.acupoints = acuList;
                    if (!_.isEmpty(formItem.acupoints)) {
                        _remark = _remark.replace("[穴位]", "");
                        _remark = _remark.replace(acuPointsResult, "");
                    }
                }
                const acupunturePointsStr = formItem?.acupoints?.map((item) => item.name).join();
                formItem.specialRequirement = _remark.replace(";", "");
                formItem.productInfo!.remark = !!acupunturePointsStr
                    ? `${!!_remark ? `${_remark};` : ""}[穴位]${acupunturePointsStr};`
                    : _remark;
            }
            const medicine = formItem.productInfo;
            group.selectedMedicines.push(medicine!);
            group.inputUsages.set(medicine!, MedicineUsage.fromPrescriptionFormItem(formItem));
        }
        return group;
    }
}
