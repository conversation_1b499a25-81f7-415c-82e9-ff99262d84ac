/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-15
 *
 * @description
 */

import { Bloc, BlocEvent } from "../../bloc";
import { debounceTime, switchMap } from "rxjs/operators";
import { LogUtils } from "../../common-base-module/log";
import { of, Subject } from "rxjs";
import { GoodsAgent } from "../../data/goods/goods-agent";
import { userCenter } from "../../user-center";
import {
    ChineseGoodType,
    ChineseMedicineSpecType,
    DeliveryInfo,
    GoodsInfo,
    GoodsSubType,
    GoodsType,
    GoodsTypeId,
    Patient,
    PsychotropicNarcoticEmployee,
    PsychotropicNarcoticTypeEnum,
    UsageInfo,
} from "../../base-business/data/beans";
import { MedicineAddType, OutpatientConst, PsychotropicNarcoticTypeList } from "../data/outpatient-const";
import { chinesePrescriptionGroup, MedicineAddGroup, MedicineUsageInput, MedicineUsageParams } from "./medicine-add-page";
import { WesternMedicine } from "../../../assets/medicine_usage/western-medicine-config";
import { MedicineUsagePicker } from "../../base-ui/picker/medicine-usage-picker";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import _, { isNil } from "lodash";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { Freq, Usage, WesternMedicineConfig, WesternMedicineConfigProvider } from "../data/western-medicine-config";
import {
    ChineseMedicineConfig,
    ChineseMedicineConfigProvider,
    ChineseMedicineUsageInfo,
    ChineseUsageItemInfo,
    PrescriptionDefaultUsage,
} from "../data/chinese-medicine-config";

import { ABCUtils } from "../../base-ui/utils/utils";
import { SearchHistory, SearchItem } from "../data/search-history";
import { AirPharmacyKeLiConfig, ChinesePrescriptionUsage, InfusionPrescriptionUsage } from "../data/medicine-add-bean";
import { CDSSAgent } from "../data/cdss";
import { Const } from "../../base-ui/utils/consts";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { ChargeUtils } from "../../charge/utils/charge-utils";
import { ABCError } from "../../common-base-module/common-error";
import {
    ClinicDispensingConfig,
    OnlinePropertyConfigProvider,
    OutpatientDataPermissionGoodsPriceType,
} from "../../data/online-property-config-provder";
import { Toast } from "../../base-ui/dialog/toast";
import { Completer } from "../../common-base-module/async/completer";
import { sharedPreferences } from "../../base-business/preferences/shared-preferences";
import {
    ClinicAgent,
    ClinicAirPharmacyConfig,
    ClinicVirtualPharmacyConfig,
    EmployeesMeConfig,
} from "../../base-business/data/clinic-agent";
import { SelectAirPharmacy, SelectAirPharmacyDialog } from "../air-pharmacy/select-air-pharmacy-dialog";
import {
    ChinesePrescriptionProcessingInfoRsp,
    MedicineBatchInfoList,
    OutpatientInvoiceDetail,
    PrescriptionChineseForm,
    PrescriptionFormDelivery,
    PrescriptionFormItem,
    PrescriptionInfusionForm,
    PrescriptionProductForm,
    PrescriptionWesternForm,
    VendorInfo,
} from "../data/outpatient-beans";
import { errorToStr, UUIDGen } from "../../common-base-module/utils";
import { OutpatientUtils } from "../utils/outpatient-utils";
import { AbcMap } from "../../base-ui/utils/abc-map";
import {
    AirPharmacyCalculateForm,
    AirPharmacyCalculateReq,
    AirPharmacyOrderItem,
    AirPharmacyVendor,
    MedicineScopeId,
    PharmacyType,
    UsageScopeId,
} from "../../charge/data/charge-bean-air-pharmacy";
import { clinicSharedPreferences } from "../../base-business/preferences/scoped-shared-preferences";
import { SharedReferenceConstants } from "../data/shared-reference-constants";
import { ignore } from "../../common-base-module/global";
import { AnyType } from "../../common-base-module/common-types";
import React from "react";
import { MedicineAddPageInfusionGroupItem } from "./views/infusion-view";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../base-ui/dialog/dialog-builder";
import { Text, View } from "@hippy/react";
import { Colors, TextStyles } from "../../theme";
import { GoodsUtils } from "../../base-business/utils/utils";
import { PrescriptionEditPage } from "../views/medicine-prescription-edit-page/prescription-edit-page";
import { AbcDialog } from "../../base-ui/abc-app-library";
import ChineseMedicine from "../../../assets/medicine_usage/chinese-medicine-config";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { MedicineChinesePanelType } from "../views/medicine-prescription-edit-page/medicine-chinese-prescription-edit-page";
import { actionEvent } from "../../bloc/bloc";
import { PrescriptionTemplateCategory, PrescriptionTemplateInfo } from "../data/prescription-template-bean";
import { SaveTemplateDialog } from "../views/save-template-dialog";
import { MedicineDosageUsage, MedicineUsage } from "./medicine-add-page-bean";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { OutpatientPrescriptionTemplateSearchDialog } from "../prescription-template/outpatient-prescription-template-search-page";
import { ChargeAgent } from "../../charge/data/charge-agent";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { ChinesePrescriptionProcessView } from "./views/chinese-prescription-process-view";
import { MedicineTemplateAddType, MedicineTemplateAddTypeDialog } from "./views/medicine-template-add-type-dialog";
import { OutpatientAgent } from "../data/outpatient";
import { ChineseAirPharmacyBagsDialog } from "./views/chinese-air-pharmacy-bags-dialog";
import { InventoryClinicConfig, PharmacyListConfig } from "../../inventory/data/inventory-bean";
import {
    ChargeForm,
    ChargeFormItem,
    ChargeSourceFormType,
    ChargeStatus,
    ChineseAirPharmacyBagsParam,
    DispensingFormItemsSourceItemType,
} from "../../charge/data/charge-beans";
import { AirPharmacyDeliveryInfoEditPage } from "../../charge/air-pharmacy-delivery-info-edit-page";
import { AirPharmacyDetail } from "../air-pharmacy/select-air-pharmacy-dialog-bloc";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { DeliveryInfoEditPage } from "../../charge/delivery-info-edit-page";
import { CHINESE_PRESCRIPTION_USAGE_DEFAULT } from "../outpatient-invoice-page-bloc";
import { fromPromise } from "rxjs/internal-compatibility";
import { MedicineBatchInfoDialog } from "./views/medicine-batch-info-dialog";
import { JingMaPrescriptionRequiredPage } from "../views/jing-ma-prescription-required-page/jing-ma-prescription-required-page";
import { PrescriptionTypeDialog } from "./views/prescription-type-dialog";

const CHINESE_ARRANGEMENT = "CHINESE_ARRANGEMENT";

class State {
    loading = false;
    loadError: any;
    noData = false;

    defaultSearchView!: boolean;
    selectSearchView = false;
    selectedView!: boolean;
    searchFocus = false;

    keyword!: string;

    defaultSearchInfo: any;
    searchInfo?: Array<GoodsInfo>;

    medicineType!: MedicineAddType;

    chineseMedicineConfig!: ChineseMedicineConfig;
    westernMedicineConfig?: WesternMedicineConfig;
    localSearchHistory?: Array<SearchItem>;

    chineseMedicineSpecType: number = ChineseMedicineSpecType.chinesePiece;
    _chineseSpecType?: number; //多药房--中药处方此时要转换成的对应类型

    selectedList: any[] = [];
    selectedItemId: string | undefined;
    selectedListCount: Map<GoodsInfo, MedicineUsage> = new Map();

    airPharmacyConfig?: ClinicAirPharmacyConfig;
    virtualPharmacyConfig?: ClinicVirtualPharmacyConfig;

    //编辑态时，当前选择的组
    currentFocusGroup?: MedicineAddGroup | null = null;

    //编辑态时，当前选择的项
    currentFocusItem?: GoodsInfo | null = null;

    // searchListInStock: Set<GoodsInfo> = []; //搜索列表-在库存中的
    searchListNoStock: GoodsInfo[] = []; //搜索列表-未在库存中的结果
    // groups = [new MedicineAddGroup()];//添加一个默认组
    groups: MedicineAddGroup[] = []; //添加一个默认组

    showErrorHint = false;

    //药自动选择的标识符
    medicineAutoFocus = false;

    employeesMeConfig?: EmployeesMeConfig;

    dispensingConfig?: ClinicDispensingConfig;
    //当前是否开启代加工
    get isOpenProcess(): boolean {
        return !!this.dispensingConfig?.isDecoction;
    }

    //能否开出库存为0的药配置(空中药房、虚拟药房不受库存不足的开关控制)
    get canCheckWithoutStock(): boolean {
        return (
            !(userCenter.inventoryClinicConfig?.stockGoodsConfig?.disableNoStockGoods == 1) ||
            !!this.groups[0]?.chinesePrescriptionUsage?.pharmacyType
        );
    }
    calculating = false;
    calculateFailed: any; //算费失败错误

    originalData?: OutpatientInvoiceDetail;
    detailData?: OutpatientInvoiceDetail;

    //错误定位滚动相关
    layoutState: Map<string, number> = new Map<string, number>();

    arrangement = true;

    //在添加重复药品时，需要重新拉起对应药品的总量键盘，设置一个Completer,让对应的项进行消费
    pendingActiveFocusItem?: Completer<void>;

    /**
     * 门诊处方配置项目
     * @description 用于配置成药处方的皮试显示
     */
    showAstCheck?: boolean;

    //是否开通虚拟药房（代煎代配）
    isOpenVirtualPharmacy?: boolean;
    //是否可选择本地药房
    isEditLocalPharmacy?: boolean;

    ///重构新增
    isEditGroup = false; //是否处于删除状态

    chineseSearchFocus = false; //中药处方搜索聚焦
    process?: ChinesePrescriptionProcessingInfoRsp; //中药处方代加工信息

    airPharmacyTotalPrice = 0; //空中药房-代煎、颗粒算费价格

    ///重构新增-end

    //成药处方分组--当前聚焦
    westernCurrentFocusGroup?: MedicineAddGroup | null = null;

    //成药处方分组--当前聚焦
    westernCurrentFocusItem?: GoodsInfo | null = null;

    /**
     * 精麻类型
     */
    psychotropicNarcoticType?: PsychotropicNarcoticTypeEnum;
    chinesePrescriptionSupportMix?: number; //中药处方是否允许混开饮片颗粒

    //不允许混开中药处方饮片颗粒
    get unableChinesePrescriptionSupportMix(): boolean {
        return this.chinesePrescriptionSupportMix == 0;
    }

    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;
    //多药房当前选中药房信息(只针对本地药房，不包含空中药房、代煎代配)
    selectPharmacyInfo?: PharmacyListConfig;
    //药房状态
    // pharmacy?: number = PharmacyType.normal;
    get pharmacy(): number {
        if (this.isChinesePrescription) {
            if (_.first(this.groups)?.chinesePrescriptionUsage?.pharmacyType == PharmacyType.air) return PharmacyType.air;
            if (_.first(this.groups)?.chinesePrescriptionUsage?.pharmacyType == PharmacyType.virtual) return PharmacyType.virtual;
        }
        return PharmacyType.normal;
    }

    /**
     * 是否开启进价加成模式
     */
    get isPurchaseMarkup(): boolean {
        return (
            !!this.pharmacyInfoConfig?.subClinicPrice?.isOpenPriceMakeUpMode &&
            !!this.pharmacyInfoConfig.chainReview?.isSupportPriceMakeUpMode
        );
    }

    get chinesePrescriptionUsage(): ChinesePrescriptionUsage | undefined {
        return Array.from(this.groups)[0].chinesePrescriptionUsage;
    }

    get isChinesePrescription(): boolean {
        return this.medicineType == MedicineAddType.chinese;
    }

    get isInfusionPrescription(): boolean {
        return this.medicineType == MedicineAddType.infusion;
    }

    get isWesternPrescription(): boolean {
        return this.medicineType == MedicineAddType.western;
    }

    get isExamination(): boolean {
        return this.medicineType == MedicineAddType.examination;
    }

    get isTreatment(): boolean {
        return this.medicineType == MedicineAddType.treatment;
    }

    get isGoods(): boolean {
        return this.medicineType == MedicineAddType.goods;
    }

    get showGoodPrice(): boolean {
        return this.employeesMeConfig?.employeeDataPermission?.outpatient?.goodsPrice == OutpatientDataPermissionGoodsPriceType.allowedAll;
    }

    get showTotalPrice(): boolean {
        return this.employeesMeConfig?.employeeDataPermission?.outpatient?.goodsPrice != OutpatientDataPermissionGoodsPriceType.notAllowed;
    }

    get totalPrice(): number {
        let price = 0;
        for (const group of this.groups) {
            for (const medicine of group.selectedMedicines) {
                const usage = group.inputUsages.get(medicine);
                if (usage?.unitCount == null || usage.unit == null) {
                    continue;
                }
                const unitPrice = medicine.unitPriceWithUnit(usage.unit);

                if (usage?.specialRequirement?.name !== "【自备】") {
                    price += usage.totalPrice ?? usage.unitCount * unitPrice + (usage.fractionPrice ?? 0);
                }
            }
        }

        return price;
    }

    //获取单项初始的总价
    getSingleTotalPrice(medicine: GoodsInfo): number {
        let price = 0;
        const usage = this.groups?.[0]?.inputUsages.get(medicine);
        if (usage?.unitCount == null || usage.unit == null) {
            return price;
        }
        const unitPrice = medicine.unitPriceWithUnit(usage.unit);

        if (usage?.specialRequirement?.name !== "【自备】") {
            price = usage.unitCount * unitPrice + (usage.fractionPrice ?? 0);
        }
        return price;
    }
    //获取议价后的总价
    get getBargainTotalPrice(): number {
        let price = 0;
        for (const group of this.groups) {
            for (const medicine of group.selectedMedicines) {
                const usage = group.inputUsages.get(medicine);
                if (usage?.unitCount == null || usage.unit == null) {
                    continue;
                }
                const unitPrice = medicine.unitPriceWithUnit(usage.unit);
                price += usage.expectedTotalPrice ?? usage.totalPrice ?? usage.unitCount * unitPrice + (usage.fractionPrice ?? 0);
            }
            if (group.chinesePrescriptionUsage) {
                //外部未填写剂数时默认显示为0
                price = price * (group.chinesePrescriptionUsage.count ?? 0);
            }
        }

        return price;
    }

    get totalCount(): number {
        let _count = 0;
        for (const group of this.groups) {
            for (const medicine of group.selectedMedicines) {
                ignore(medicine);
                _count++;
            }
        }
        return _count;
    }

    isSelect(group: AnyType, medicine: AnyType): boolean {
        const _selectItem =
            this.currentFocusItem &&
            medicine &&
            (medicine.id != undefined && medicine.id != ""
                ? medicine.id == this.currentFocusItem.id
                : medicine.displayName == this.currentFocusItem.displayName);
        return this.currentFocusGroup?.groupId == group.groupId && (this.currentFocusItem == medicine || _selectItem);
    }

    boilService = false;

    get ableUseAirPharmacy(): boolean {
        return (
            (!!this.virtualPharmacyConfig?.isVirtualPharmacyOpen ||
                (this.airPharmacyConfig?.isSupportAirPharmacy && this.airPharmacyConfig.openSwitch == 1)) ??
            false
        );
    }

    ableChangePharmacyType = true;

    copyTo(state: State): void {
        Object.assign(state, this);
    }
}

class ScrollToFocusItemState extends State {
    static fromState(state: State): ScrollToFocusItemState {
        const newState = new ScrollToFocusItemState();
        state.copyTo(newState);
        return newState;
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {}

class _EventSearchGoods extends _Event {
    keyword: string;

    constructor(props: string) {
        super();
        this.keyword = props;
    }
}

class _EventAddSelectList extends _Event {
    medicine: GoodsInfo;

    constructor(medicine: GoodsInfo) {
        super();
        this.medicine = medicine;
    }
}
class _EventChangePageShow extends _Event {
    info: string;

    constructor(info: string) {
        super();
        this.info = info;
    }
}

// TODO 待删除
class _EventChangeGoodCount extends _Event {
    info: any;

    constructor(info: any) {
        super();
        this.info = info;
    }
}

class _EventChineseMedicineSpecType extends _Event {
    type: number;
    forceTrans?: boolean;
    pharmacyType?: number;

    constructor(type: number, forceTrans?: boolean, pharmacyType?: number) {
        super();
        this.type = type;
        this.forceTrans = forceTrans;
        this.pharmacyType = pharmacyType;
    }
}

class _EventUpdateTreatmentAmount extends _Event {
    medicine: GoodsInfo;
    count: number;

    constructor(medicine: GoodsInfo, count: number) {
        super();
        this.medicine = medicine;
        this.count = count;
    }
}

class _EventSelectMedicineUnit extends _Event {
    medicine: GoodsInfo;

    constructor(medicine: GoodsInfo) {
        super();
        this.medicine = medicine;
    }
}

class _EventAddMedicineGroup extends _Event {}

class _EventUpdateBoilServiceSwitch extends _Event {
    check: boolean;

    constructor(check: boolean) {
        super();
        this.check = check;
    }
}

class _EventDeleteGroup extends _Event {
    group: MedicineAddGroup;

    constructor(group: MedicineAddGroup) {
        super();
        this.group = group;
    }
}

// TODO 待删除
class _EventSelectGroup extends _Event {
    group: MedicineAddGroup;

    constructor(group: MedicineAddGroup) {
        super();
        this.group = group;
    }
}

// TODO 待删除
class _EventChangeChineseSpecType extends _Event {
    specType: number;

    constructor(specType: number) {
        super();
        this.specType = specType;
    }
}

class _EventUpdateInfusionMedicineUnit extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;
    unit: string;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo, unit: string) {
        super();
        this.group = group;
        this.medicine = medicine;
        this.unit = unit;
    }
}

class _EventUpdateInfusionMedicineUnitCount extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;
    unitCount: number | string;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo, unitCount: number | string) {
        super();
        this.group = group;
        this.medicine = medicine;
        this.unitCount = unitCount;
    }
}

class _EventUpdateInfusionMedicineDosageUnitCount extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;
    dosageUnitCount: number;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo, dosageUnitCount: number) {
        super();
        this.group = group;
        this.medicine = medicine;
        this.dosageUnitCount = dosageUnitCount;
    }
}

class _EventUpdateInfusionMedicineDosageUnit extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;
    dosageUnit: string;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo, dosageUnit: string) {
        super();
        this.group = group;
        this.medicine = medicine;
        this.dosageUnit = dosageUnit;
    }
}

class _EventUpdateInfusionMedicineSkinTest extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;
    check: boolean;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo, check: boolean) {
        super();
        this.group = group;
        this.medicine = medicine;
        this.check = check;
    }
}

class _EventUpdateInfusionPrescriptionIvgtt extends _Event {
    group: MedicineAddGroup;
    ivgtt: number;

    constructor(group: MedicineAddGroup, ivgtt: number) {
        super();
        this.group = group;
        this.ivgtt = ivgtt;
    }
}

class _EventUpdateInfusionPrescriptionDays extends _Event {
    group: MedicineAddGroup;
    days: number;

    constructor(group: MedicineAddGroup, days: number) {
        super();
        this.group = group;
        this.days = days;
    }
}

class _EventSelectInfusionPrescriptionFreq extends _Event {
    group: MedicineAddGroup;
    value?: string;

    constructor(group: MedicineAddGroup, value?: string) {
        super();
        this.group = group;
        this.value = value;
    }
}

class _EventSelectInfusionPrescriptionUsage extends _Event {
    group: MedicineAddGroup;
    value?: string;

    constructor(group: MedicineAddGroup, value?: string) {
        super();
        this.group = group;
        this.value = value;
    }
}

// TODO 待删除
class _EventPrescriptionGroupAdd extends _Event {}

// TODO 待删除
class _EventUpdateChinesePrescriptionBoilServiceMobile extends _Event {
    mobile: string;

    constructor(mobile: string) {
        super();
        this.mobile = mobile;
    }
}

class _EventUpdateChinesePrescriptionRequirement extends _Event {
    requirement: string;

    constructor(requirement: string) {
        super();
        this.requirement = requirement;
    }
}

class _EventSelectChinesePrescriptionUsage extends _Event {
    usage: string;
    constructor(usage: string) {
        super();
        this.usage = usage;
    }
}

class _EventSelectChinesePrescriptionUsageLevel extends _Event {
    usageLevel: string;
    constructor(usageLevel: string) {
        super();
        this.usageLevel = usageLevel;
    }
}

class _EventSelectChinesePrescriptionUsageUsageDays extends _Event {
    usageDays: string;
    constructor(usageDays: string) {
        super();
        this.usageDays = usageDays;
    }
}

class _EventSelectChinesePrescriptionFreq extends _Event {
    freq: string;
    constructor(freq: string) {
        super();
        this.freq = freq;
    }
}

class _EventSelectChinesePrescriptionDailyDosage extends _Event {
    dailyDosage: string;
    constructor(dailyDosage: string) {
        super();
        this.dailyDosage = dailyDosage;
    }
}

class _EventUpdateChinesePrescriptionDosageAmount extends _Event {
    dosageCount: number;

    constructor(dosageCount: number) {
        super();
        this.dosageCount = dosageCount;
    }
}

class _EventUpdateChineseMedicineAmount extends _Event {
    medicine: GoodsInfo;
    unitCount: number | string;

    constructor(medicine: GoodsInfo, unitCount: number | string) {
        super();
        this.medicine = medicine;
        this.unitCount = unitCount;
    }
}

class _EventSelectChineseMedicineBoilMethod extends _Event {
    medicine: GoodsInfo;

    constructor(medicine: GoodsInfo) {
        super();
        this.medicine = medicine;
    }
}

class _EventDeleteChineseMedicineBoilMethod extends _Event {
    medicine: GoodsInfo;

    constructor(medicine: GoodsInfo) {
        super();
        this.medicine = medicine;
    }
}

class _EventSubmit extends _Event {}

class _EventUpdateWesternDosageCount extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;
    dosageCount: number;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo, dosageCount: number) {
        super();
        this.dosageCount = dosageCount;
        this.medicine = medicine;
        this.group = group;
    }
}

class _EventDeleteMedicine extends _Event {
    group: MedicineAddGroup | null;
    medicine: GoodsInfo;
    type: string;

    constructor(group: MedicineAddGroup | null, medicine: GoodsInfo, type: string) {
        super();
        this.medicine = medicine;
        this.group = group;
        this.type = type;
    }
}

class _EventUpdateWesternAmountUnitCount extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;
    unitCount: number | string;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo, unitCount: number | string) {
        super();
        this.unitCount = unitCount;
        this.medicine = medicine;
        this.group = group;
    }
}

class _EventUpdateWesternAmountUnit extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;
    unit: string;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo, unit: string) {
        super();
        this.unit = unit;
        this.medicine = medicine;
        this.group = group;
    }
}

class _EventUpdateWesternMedicineDays extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;
    days: number;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo, days: number) {
        super();
        this.days = days;
        this.medicine = medicine;
        this.group = group;
    }
}

class _EventUpdateWesternMedicineUsage extends _Event {
    group: MedicineAddGroup;
    goodsInfo: GoodsInfo;
    usage?: string;
    constructor(group: MedicineAddGroup, goodsInfo: GoodsInfo, usage?: string) {
        super();
        this.group = group;
        this.goodsInfo = goodsInfo;
        this.usage = usage;
    }
}

class _EventUpdateWesternMedicineFreq extends _Event {
    group: MedicineAddGroup;
    goodsInfo: GoodsInfo;
    freq?: string;
    constructor(group: MedicineAddGroup, goodsInfo: GoodsInfo, freq?: string) {
        super();
        this.group = group;
        this.goodsInfo = goodsInfo;
        this.freq = freq;
    }
}

class _EventUpdateWesternRemark extends _Event {
    group: MedicineAddGroup;
    goodsInfo: GoodsInfo;
    remark?: string;
    constructor(group: MedicineAddGroup, goodsInfo: GoodsInfo, remark?: string) {
        super();
        this.group = group;
        this.goodsInfo = goodsInfo;
        this.remark = remark;
    }
}

class _EventUpdateWesternMedicineAst extends _Event {
    group: MedicineAddGroup;
    goodsInfo: GoodsInfo;
    ast?: number;
    constructor(group: MedicineAddGroup, goodsInfo: GoodsInfo, ast?: number) {
        super();
        this.group = group;
        this.goodsInfo = goodsInfo;
        this.ast = ast;
    }
}

class _EventDeleteWesternRemark extends _Event {
    group: MedicineAddGroup;
    goodsInfo: GoodsInfo;
    constructor(group: MedicineAddGroup, goodsInfo: GoodsInfo) {
        super();
        this.group = group;
        this.goodsInfo = goodsInfo;
    }
}
class _EventDeletePharmacyInfo extends _Event {
    group: MedicineAddGroup;
    goodsInfo: GoodsInfo;
    constructor(group: MedicineAddGroup, goodsInfo: GoodsInfo) {
        super();
        this.group = group;
        this.goodsInfo = goodsInfo;
    }
}

class _EventFocusItem extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo) {
        super();
        this.group = group;
        this.medicine = medicine;
    }
}

class _EventUpdateDosageUnit extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;
    unit: string;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo, unit: string) {
        super();
        this.group = group;
        this.medicine = medicine;
        this.unit = unit;
    }
}

class _EventWesternFreqTap extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo) {
        super();
        this.group = group;
        this.medicine = medicine;
    }
}

class _EventWesternUsageTap extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo) {
        super();
        this.group = group;
        this.medicine = medicine;
    }
}
class _EventAddMedicine extends _Event {
    medicine: GoodsInfo;

    constructor(medicine: GoodsInfo) {
        super();
        this.medicine = medicine;
    }
}

// TODO 待删除
class _EventToggleSearchFocus extends _Event {
    focus: boolean;

    constructor(focus: boolean) {
        super();
        this.focus = focus;
    }
}

class _EventCacheSearchInput extends _Event {
    searchInput: AbcTextInput | null;

    constructor(searchInput: AbcTextInput | null) {
        super();
        this.searchInput = searchInput;
    }
}

class _EventUpdateWesternMedicineGroupIndex extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo) {
        super();
        this.group = group;
        this.medicine = medicine;
    }
}

class _EventDeleteWesternMedicineGroupIndex extends _Event {}

class _EventContinueSearch extends _Event {
    focus: boolean;

    constructor(focus: boolean) {
        super();
        this.focus = focus;
    }
}

class _EventBackPage extends _Event {}

class _EventRecordPartLayout extends _Event {
    key: string;
    layoutY: number;

    constructor(key: string, layoutY: number) {
        super();
        this.key = key;
        this.layoutY = layoutY;
    }
}

class _EventChangeArrangement extends _Event {}

class _EventModifyPharmacyType extends _Event {}

class _EventNewModifyPharmacyType extends _Event {}

class _EventAddMedicinePrescription extends _Event {
    goodsInfo: GoodsInfo;
    group?: MedicineAddGroup;

    constructor(goodsInfo: GoodsInfo, group?: MedicineAddGroup) {
        super();
        this.goodsInfo = goodsInfo;
        this.group = group;
    }
}

class _EventAddChineseMedicine extends _Event {}
class _EventAddChineseMedicineUsage extends _Event {
    group?: MedicineAddGroup;
    tabType?: MedicineChinesePanelType;

    constructor(group?: MedicineAddGroup, tabType?: MedicineChinesePanelType) {
        super();
        this.group = group;
        this.tabType = tabType;
    }
}

class _EventModifyEditGroupState extends _Event {
    status: boolean;
    constructor(status: boolean) {
        super();
        this.status = status;
    }
}

class _EventModifyEditMedicineUsage extends _Event {
    group: MedicineAddGroup;
    medicine: GoodsInfo;

    constructor(group: MedicineAddGroup, medicine: GoodsInfo) {
        super();
        this.group = group;
        this.medicine = medicine;
    }
}

class _EventModifyInfusionGroupUsage extends _Event {
    group: MedicineAddGroup;

    constructor(group: MedicineAddGroup) {
        super();
        this.group = group;
    }
}

class _EventSaveTemplate extends _Event {}

class _EventUpdateMedicineStateScopedId extends _Event {}
class _EventCopyTemplate extends _Event {
    type?: MedicineAddType;
    constructor(type?: MedicineAddType) {
        super();
        this.type = type;
    }
}

class _EventModifyPrescriptionNarcoticType extends _Event {
    type?: number;
    constructor(type?: number) {
        super();
        this.type = type;
    }
}

class _EventModifyPrescriptionType extends _Event {}
class _EventChineseSwitchType extends _Event {}

class _EventModifyMedicinePharmacy extends _Event {
    medicine: GoodsInfo;
    pharmacy: PharmacyListConfig;
    isModifySelf: boolean;
    group?: MedicineAddGroup;
    type?: string;
    constructor(medicine: GoodsInfo, pharmacy: PharmacyListConfig, isModifySelf?: boolean, group?: MedicineAddGroup, type?: string) {
        super();
        this.medicine = medicine;
        this.pharmacy = pharmacy;
        this.isModifySelf = isModifySelf ?? false;
        this.group = group;
        this.type = type;
    }
}

class _EventModifyMedicineSelfProvidedStatus extends _Event {
    medicine: GoodsInfo;
    status: DispensingFormItemsSourceItemType;
    group?: MedicineAddGroup;
    constructor(medicine: GoodsInfo, status: DispensingFormItemsSourceItemType, group?: MedicineAddGroup) {
        super();
        this.medicine = medicine;
        this.status = status;
        this.group = group;
    }
}

class _EventSelectAirExpress extends _Event {
    pharmacyType: PharmacyType;
    constructor(pharmacyType: PharmacyType) {
        super();
        this.pharmacyType = pharmacyType;
    }
}

class _EventAdjustDiscount extends _Event {
    medicine: GoodsInfo;
    discount: number;
    type: string;
    constructor(medicine: GoodsInfo, discount: number, type: string) {
        super();
        this.medicine = medicine;
        this.discount = discount;
        this.type = type;
    }
}
class _EventAdjustTotalPrice extends _Event {
    medicine: GoodsInfo;
    totalPrice: number;
    type: string;
    constructor(medicine: GoodsInfo, totalPrice: number, type: string) {
        super();
        this.medicine = medicine;
        this.totalPrice = totalPrice;
        this.type = type;
    }
}
class _EventCheckGoodsBatchInfo extends _Event {}

class _EventUpdateCalculatePrice extends _Event {
    calculating: boolean;
    error: any;
    calculateRspData?: OutpatientInvoiceDetail | null;
    shouldNotCopyExpectedPrice?: boolean;
    type?: string;

    constructor(options: {
        calculateRspData?: OutpatientInvoiceDetail | null;
        calculating: boolean;
        error: any;
        shouldNotCopyExpectedPrice?: boolean;
        type?: string;
    }) {
        super();
        this.calculating = options.calculating;
        this.calculateRspData = options.calculateRspData;
        this.error = options.error;
        this.shouldNotCopyExpectedPrice = options.shouldNotCopyExpectedPrice ?? false;
        this.type = options?.type;
    }
}

class MedicineAddPageBloc extends Bloc<_Event, State> {
    private _hasChanged = false;
    private _searchClinicId?: string;
    private _loadVendorList = new Subject();
    private _loadVirtualVendorList = new Subject();
    _calculatePriceTrigger = new Subject<string>();
    _airPharmacyCalculateTrigger = new Subject(); //  空中药房算费（饮片-代煎或者颗粒，需要调用，其他情况不需要）
    private _detailData?: OutpatientInvoiceDetail;

    set hasChanged(newValue: boolean) {
        this._hasChanged = newValue;
    }

    get hasChanged(): boolean {
        return this._hasChanged;
    }

    medicineType: MedicineAddType;
    _loadDataTrigger = new Subject<number>();
    searchHistory!: SearchHistory;
    params?: MedicineUsageParams;

    _searchInput?: AbcTextInput | null;
    process?: ChinesePrescriptionProcessingInfoRsp; //中药处方代加工信息
    private isHideProcessInfo = false;
    otherAirPharmacyCalculateForm?: AirPharmacyCalculateForm[];
    airPharmacySort?: number;
    _departmentId?: string;
    _prescriptionFormKeyId?: string;

    constructor(params: {
        medicineUsageParams: MedicineUsageParams;
        searchClinicId?: string;
        process?: ChinesePrescriptionProcessingInfoRsp;
        isOpenVirtualPharmacy?: boolean;
        isEditLocalPharmacy?: boolean;
        isHideProcessInfo?: boolean;
        otherAirPharmacyCalculateForm?: AirPharmacyCalculateForm[];
        airPharmacySort?: number;
        departmentId?: string;
        detailData?: OutpatientInvoiceDetail;
        prescriptionFormKeyId?: string;
    }) {
        super();
        const {
            medicineUsageParams,
            searchClinicId,
            process,
            isOpenVirtualPharmacy,
            isEditLocalPharmacy,
            isHideProcessInfo,
            otherAirPharmacyCalculateForm,
            airPharmacySort,
            departmentId,
            detailData,
            prescriptionFormKeyId,
        } = params;

        this.params = medicineUsageParams;
        this.medicineType = medicineUsageParams.type;
        this.innerState.medicineType = medicineUsageParams.type;
        this.innerState.groups = medicineUsageParams.medicines ?? [];
        this.innerState.process = JsonMapper.deserialize(ChinesePrescriptionProcessingInfoRsp, {
            contactMobile: medicineUsageParams.patient?.mobile,
            ...process,
        });
        this.innerState.currentFocusItem = medicineUsageParams.selectMedicine;
        this.innerState.currentFocusGroup = medicineUsageParams.selectGroup;
        this.innerState.defaultSearchView = false;
        this.innerState.selectedView = true;
        this.innerState.ableChangePharmacyType = medicineUsageParams.ableChangePharmacyType ?? true;
        this.innerState.showAstCheck = medicineUsageParams.showAstCheck;
        this._searchClinicId = searchClinicId;
        this.params.diagnosis = medicineUsageParams.diagnosis;
        this.params.chiefComplaint = medicineUsageParams.chiefComplaint;
        this.innerState.isOpenVirtualPharmacy = isOpenVirtualPharmacy;
        this.innerState.isEditLocalPharmacy = isEditLocalPharmacy;
        this.process = JsonMapper.deserialize(ChinesePrescriptionProcessingInfoRsp, {
            contactMobile: medicineUsageParams.patient?.mobile,
            ...process,
        });

        this.innerState.psychotropicNarcoticType = medicineUsageParams.psychotropicNarcoticType;
        this.isHideProcessInfo = isHideProcessInfo ?? false;
        this.otherAirPharmacyCalculateForm = otherAirPharmacyCalculateForm;
        this.airPharmacySort = airPharmacySort;
        this._departmentId = departmentId;
        this._detailData = detailData;
        this._prescriptionFormKeyId = prescriptionFormKeyId;

        this.dispatch(new _EventInit());
    }

    static fromContext(context: MedicineAddPageBloc): MedicineAddPageBloc {
        return context;
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<string, Function> {
        const map = new Map<string, Function>();
        map.set(_EventInit.name, this._mapEventInit);
        map.set(_EventUpdate.name, this._mapEventUpdate);
        map.set(_EventToggleSearchFocus.name, this._mapEventToggleSearchFocus); // TODO 待删除
        map.set(_EventSearchGoods.name, this._EventSearchGoods);
        map.set(_EventAddSelectList.name, this._mapEventAddMedicine);
        map.set(_EventDeleteMedicine.name, this._mapEventDeleteMedicine);
        map.set(_EventChangePageShow.name, this._EventChangePageShow);
        map.set(_EventChangeGoodCount.name, this._EventChangeGoodCount); // TODO 待删除
        map.set(_EventChineseMedicineSpecType.name, this._EventChineseMedicineSpecType);

        //like flutter
        map.set(_EventUpdateTreatmentAmount.name, this._mapEventUpdateTreatmentAmount);
        map.set(_EventSelectMedicineUnit.name, this._mapEventSelectMedicineUnit);
        map.set(_EventAddMedicineGroup.name, this._EventAddMedicineGroup);
        map.set(_EventFocusItem.name, this._mapEventFocusItem);
        map.set(_EventWesternUsageTap.name, this._mapEventWesternUsageTap);
        map.set(_EventWesternFreqTap.name, this._mapEventWesternFreqTap);
        map.set(_EventUpdateDosageUnit.name, this._mapEventUpdateDosageUnit);
        map.set(_EventUpdateWesternDosageCount.name, this._mapEventUpdateWesternDosageCount);
        map.set(_EventUpdateWesternMedicineDays.name, this._mapEventUpdateWesternMedicineDays);
        map.set(_EventUpdateWesternAmountUnit.name, this._mapEventUpdateWesternAmountUnit);
        map.set(_EventUpdateWesternAmountUnitCount.name, this._mapEventUpdateWesternAmountUnitCount);

        map.set(_EventSelectChineseMedicineBoilMethod.name, this._mapEventSelectChineseMedicineBoilMethod);
        map.set(_EventUpdateChineseMedicineAmount.name, this._mapEventUpdateChineseMedicineAmount);
        map.set(_EventUpdateChinesePrescriptionDosageAmount.name, this._mapEventUpdateChinesePrescriptionDosageAmount);
        map.set(_EventSelectChinesePrescriptionUsage.name, this._mapEventSelectChinesePrescriptionUsage);
        map.set(_EventSelectChinesePrescriptionDailyDosage.name, this._mapEventSelectChinesePrescriptionDailyDosage);
        map.set(_EventSelectChinesePrescriptionFreq.name, this._mapEventSelectChinesePrescriptionFreq);
        map.set(_EventSelectChinesePrescriptionUsageLevel.name, this._mapEventSelectChinesePrescriptionUsageLevel);
        map.set(_EventUpdateChinesePrescriptionRequirement.name, this._mapEventUpdateChinesePrescriptionRequirement); // TODO 待删除
        map.set(_EventUpdateChinesePrescriptionBoilServiceMobile.name, this._mapEventUpdateChinesePrescriptionBoilServiceMobile); // TODO 待删除

        map.set(_EventSelectInfusionPrescriptionUsage.name, this._mapEventSelectInfusionPrescriptionUsage);
        map.set(_EventSelectInfusionPrescriptionFreq.name, this._mapEventSelectInfusionPrescriptionFreq);
        map.set(_EventUpdateInfusionPrescriptionDays.name, this._mapEventUpdateInfusionPrescriptionDays);
        map.set(_EventUpdateInfusionPrescriptionIvgtt.name, this._mapEventUpdateInfusionPrescriptionIvgtt);
        map.set(_EventUpdateInfusionMedicineSkinTest.name, this._mapEventUpdateInfusionMedicineSkinTest);
        map.set(_EventUpdateInfusionMedicineDosageUnit.name, this._mapEventUpdateInfusionMedicineDosageUnit);
        map.set(_EventUpdateInfusionMedicineDosageUnitCount.name, this._mapEventUpdateInfusionMedicineDosageUnitCount);
        map.set(_EventUpdateInfusionMedicineUnit.name, this._mapEventUpdateInfusionMedicineUnit);
        map.set(_EventUpdateInfusionMedicineUnitCount.name, this._mapEventUpdateInfusionMedicineUnitCount);

        map.set(_EventSelectGroup.name, this._mapEventSelectGroup); // TODO 待删除
        map.set(_EventDeleteGroup.name, this._mapEventDeleteGroup);

        map.set(_EventUpdateBoilServiceSwitch.name, this._EventUpdateBoilServiceSwitch);
        map.set(_EventSubmit.name, this._mapEventSubmit);

        map.set(_EventCacheSearchInput.name, this._mapEventCacheSearchInput);
        map.set(_EventUpdateWesternMedicineGroupIndex.name, this._mapEventUpdateWesternMedicineGroupIndex);
        map.set(_EventContinueSearch.name, this._mapEventContinueSearch);

        map.set(_EventBackPage.name, this._mapEventBackPage);

        map.set(_EventRecordPartLayout.name, this._mapEventRecordPartLayout);
        map.set(_EventChangeArrangement.name, this._mapEventChangeArrangement);
        // map.set(_EventModifyPharmacyType.name, this._mapEventModifyPharmacyType);

        map.set(_EventNewModifyPharmacyType.name, this._mapEventNewModifyPharmacyType);

        map.set(_EventAddMedicinePrescription.name, this._mapEventAddMedicinePrescription);
        map.set(_EventModifyEditGroupState.name, this._mapEventModifyEditGroupState);
        map.set(_EventModifyEditMedicineUsage.name, this._mapEventModifyEditMedicineUsage);
        map.set(_EventModifyInfusionGroupUsage.name, this._mapEventModifyInfusionGroupUsage);
        map.set(_EventAddChineseMedicine.name, this._mapEventAddChineseMedicine);
        map.set(_EventAddChineseMedicineUsage.name, this._mapEventAddChineseMedicineUsage);

        return map;
    }

    async _initHistoryAirPharmacy(): Promise<void> {
        const _pharmacyInfo = clinicSharedPreferences.getObject(SharedReferenceConstants.LAST_SELECT_VENDOR_INFO);
        if (!_pharmacyInfo) return;
        if (this.innerState.groups[0].vendor) {
            if (
                this.innerState.groups[0].vendor?.vendorId == _pharmacyInfo.vendor?.vendorId &&
                (!!_pharmacyInfo.vendor?.vendorUsageScopeId
                    ? this.innerState.groups[0].vendor?.vendorUsageScopeId == _pharmacyInfo.vendor?.vendorUsageScopeId
                    : true)
            ) {
                this.innerState.groups[0].vendor = Object.assign(this.innerState.groups[0].vendor, _pharmacyInfo.vendor);
            }
            return;
        }
        if (_pharmacyInfo) {
            if (_pharmacyInfo.pharmacyType == PharmacyType.normal) {
                this.innerState.selectPharmacyInfo = _pharmacyInfo.multiplePharmacyInfo;
            }
            this.innerState.groups[0].vendor = _pharmacyInfo.pharmacyType == PharmacyType.normal ? undefined : _pharmacyInfo.vendor;
            this.innerState.groups[0].chinesePrescriptionUsage!.pharmacyType = _pharmacyInfo.pharmacyType;
            this.innerState.groups[0].chinesePrescriptionUsage!.pharmacyNo = _pharmacyInfo.pharmacyNo;
            this.innerState.groups[0].chinesePrescriptionUsage!.usageScopeId = _pharmacyInfo.usageScopeId;
            this.innerState.groups[0].chinesePrescriptionUsage!.medicineStateScopeId = !!_pharmacyInfo.medicineStateScopeId
                ? _pharmacyInfo.medicineStateScopeId
                : _pharmacyInfo.vendor?.vendorAvailableMedicalStates?.[0]?.medicalStatId;
            const _specificationType = ChineseMedicineSpecType!.typeFromName(_pharmacyInfo.usageScopeId == "7" ? "颗粒" : "饮片");
            if ((this.innerState.groups[0].chinesePrescriptionUsage!.specificationType! = _specificationType)) {
                this.innerState.groups[0].chinesePrescriptionUsage!.specificationType = ChineseMedicineSpecType!.typeFromName(
                    _pharmacyInfo.usageScopeId == "7" ? "颗粒" : "饮片"
                );
                this.innerState.chineseMedicineSpecType = ChineseMedicineSpecType!.typeFromName(
                    _pharmacyInfo.usageScopeId == "7" ? "颗粒" : "饮片"
                );
            }
            this.innerState.groups[0].chinesePrescriptionUsage!.processBagUnitCount = _pharmacyInfo?.processBagUnitCount;
            this.update();
        }
    }
    private _initDetailData(): void {
        if (!this._detailData) return;
        this._detailData?.productForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.productFormItems?.map((item) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
            });
        });
        this._detailData?.prescriptionChineseForms?.map((form) => {
            // 防止传过来的pharmacyType为空，导致匹配对应药房的时候被过滤
            form.pharmacyType =
                form.pharmacyType ?? this.innerState.groups?.[0]?.chinesePrescriptionUsage?.pharmacyType ?? PharmacyType.normal;
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });
        this._detailData?.prescriptionInfusionForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });
        this._detailData?.prescriptionWesternForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });
        this._detailData?.prescriptionExternalForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });

        this.innerState.originalData = JsonMapper.deserialize(OutpatientInvoiceDetail, this._detailData);
        this.innerState.detailData = OutpatientUtils.tripChargedItemForOutpatientPriceCalculate(this._detailData);
    }
    _calculatePrice(): Promise<OutpatientInvoiceDetail> {
        this.innerState.calculating = true;
        this.update();
        if (_.isNumber(this.innerState.detailData!.expectedTotalPrice)) {
            this.innerState.detailData!.adjustmentFee = undefined;
        }
        return OutpatientAgent.chargeCalculate(this.innerState.detailData!);
    }

    /**
     * 清空议价信息
     * @param medicine
     * @param type
     * @param isChangePharmacy --是否切换药房，切换药房，需要将currentUnitPrice清空
     */
    _clearAdjustmentFee(medicine: GoodsInfo, type: string, isChangePharmacy?: boolean): void {
        if (!medicine) return;
        this._changeGoodsInfoToForm(type, medicine);
        if (type == "western") {
            this._filterWesternPrescriptionForms()?.forEach((form) => {
                form.expectedTotalPrice = undefined;
                const sameItem = form.prescriptionFormItems?.find((t) => t.keyId == medicine?.keyId);
                if (sameItem) {
                    sameItem.expectedTotalPriceRatio = undefined;
                    sameItem.expectedTotalPrice = undefined;
                    sameItem.expectedUnitPrice = !_.isNil(this.innerState.groups[0].inputUsages.get(medicine)?.expectedTotalPrice)
                        ? sameItem.unitPrice
                        : undefined;
                    sameItem.isTotalPriceChanged = 0;
                    sameItem.isUnitPriceChanged = 0;
                    if (!!isChangePharmacy) sameItem.currentUnitPrice = undefined;
                    sameItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
                }
            });
        } else if (type == "infusion") {
            this._filterInfusionPrescriptionForms()?.forEach((form) => {
                form.expectedTotalPrice = undefined;
                const sameItem = form.prescriptionFormItems?.find((t) => t.keyId == medicine?.keyId);
                if (sameItem) {
                    sameItem.expectedTotalPriceRatio = undefined;
                    sameItem.expectedTotalPrice = undefined;
                    sameItem.expectedUnitPrice = !_.isNil(this.innerState.groups[0].inputUsages.get(medicine)?.expectedTotalPrice)
                        ? sameItem.unitPrice
                        : undefined;
                    sameItem.isTotalPriceChanged = 0;
                    sameItem.isUnitPriceChanged = 0;
                    if (!!isChangePharmacy) sameItem.currentUnitPrice = undefined;
                    sameItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
                }
            });
        } else if (type == "chinese") {
            const pharmacyType = this.innerState.groups?.[0]?.chinesePrescriptionUsage?.pharmacyType ?? PharmacyType.normal;
            this._filterChinesePrescriptionForms(pharmacyType)?.forEach((form) => {
                form.expectedTotalPrice = undefined;
                const sameItem = form.prescriptionFormItems?.find((t) => {
                    if (!pharmacyType) return t.keyId == medicine.keyId;
                    return t?.displayName == medicine?.displayName;
                });
                if (sameItem) {
                    sameItem.expectedTotalPriceRatio = undefined;
                    sameItem.expectedTotalPrice = undefined;
                    sameItem.expectedUnitPrice = !_.isNil(this.innerState.groups[0].inputUsages.get(medicine)?.expectedTotalPrice)
                        ? sameItem.unitPrice
                        : undefined;
                    sameItem.isTotalPriceChanged = 0;
                    sameItem.isUnitPriceChanged = 0;
                    if (!!isChangePharmacy) sameItem.currentUnitPrice = undefined;
                    sameItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
                }
            });
        }
        this._calculatePriceTrigger.next(type);
    }
    private async _initPageConfig(): Promise<void> {
        this.innerState.arrangement = !sharedPreferences.getInt(CHINESE_ARRANGEMENT);

        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig().catchIgnore();
        //读取当前加工配置
        this.innerState.dispensingConfig = await OnlinePropertyConfigProvider.instance.getClinicDispensingConfig().catchIgnore();
        //多药房相关配置
        this.innerState.pharmacyInfoConfig = userCenter.inventoryClinicConfig;

        //拉取配置，防止第一次算计量失败
        const usageConfig = await OutpatientAgent.getUsageConfig(false);
        this.innerState.chinesePrescriptionSupportMix = usageConfig.chinesePrescriptionSupportMix;

        const searchFile = await OutpatientConst.getSearchHistoryFile(this.innerState.medicineType.toString());
        this.searchHistory = new SearchHistory(searchFile);
        this.addDisposable(this.searchHistory);

        this.innerState.westernMedicineConfig = WesternMedicineConfigProvider.getConfig();
        this.innerState.chineseMedicineConfig = ChineseMedicineConfigProvider.getConfig();

        if (this.innerState.groups.length == 0 && this.innerState.isChinesePrescription) {
            this.innerState.groups.push(new MedicineAddGroup());
        }

        if (this.innerState.groups.length == 0 && this.innerState.isInfusionPrescription) {
            this.innerState.groups.push(new MedicineAddGroup());
        }

        if (this.innerState.isChinesePrescription) {
            this.innerState.airPharmacyConfig = await ClinicAgent.getAirPharmacyConfig(true).catchIgnore();
            this.innerState.virtualPharmacyConfig = await ClinicAgent.getVirtualPharmacyConfig(true).catchIgnore();
        }

        for (const group of this.innerState.groups) {
            await this._initMedicineGroup(group);
            // this._refreshGroupMedicineAmount(group)
        }

        if (this.innerState.isChinesePrescription) {
            this.innerState.boilService = Boolean(this.innerState.groups[0]?.chinesePrescriptionUsage?.boilServiceMobile);
            if (this.innerState.groups[0].selectedMedicines.length == 0 && this.innerState.ableUseAirPharmacy) {
                await this._initHistoryAirPharmacy();
            }

            this.innerState.chineseMedicineSpecType =
                this.innerState.groups[0]?.chinesePrescriptionUsage?.specificationType ?? ChineseMedicineSpecType.chinesePiece;
            this.innerState._chineseSpecType = this.innerState.chineseMedicineSpecType;

            //将中药用法中没有单位的药品，黑认设置为'g'
            for (const group of this.innerState.groups) {
                if (ABCUtils.isEmpty(group?.selectedMedicines)) continue;
                for (const medicine of group.selectedMedicines!) {
                    const usage = group.inputUsages?.get(medicine);
                    if (usage != null && _.isEmpty(usage.unit)) {
                        usage.unit = ABCUtils.first(medicine.sellUnits) ?? Const.chineseMedicineDefaultUnit;
                    }
                }
            }
        }

        //多药房---药房名称应该先根据当前初始中药类型（14--中药饮片，15--中药颗粒）来获取药房信息
        const {
            pharmacyInfoConfig,
            selectPharmacyInfo,
            isChinesePrescription,
            chineseMedicineSpecType,
            isWesternPrescription,
            isInfusionPrescription,
        } = this.innerState;
        if (pharmacyInfoConfig?.isOpenMultiplePharmacy) {
            if (!selectPharmacyInfo) {
                let initGoodsTypeId = undefined;
                if (isChinesePrescription) {
                    initGoodsTypeId =
                        chineseMedicineSpecType == ChineseMedicineSpecType.chinesePiece
                            ? GoodsTypeId.medicineChinesePiece
                            : GoodsTypeId.medicineChineseGranule;
                } else if (isWesternPrescription) {
                    initGoodsTypeId = GoodsTypeId.medicineWest;
                } else if (isInfusionPrescription) {
                    initGoodsTypeId = GoodsTypeId.medicineWest;
                }
                const { pharmacyInfo } = this.params ?? {};
                const filterExistPharmacy = pharmacyInfoConfig.filterNormalPharmacyList?.filter(
                    (pharmacy) => pharmacy.no == pharmacyInfo?.no || pharmacy.name == pharmacyInfo?.name
                );
                const pharmacyList = !!filterExistPharmacy?.length
                    ? filterExistPharmacy
                    : pharmacyInfoConfig?.filterPharmacyListWithGoodsType(initGoodsTypeId);
                this.innerState.selectPharmacyInfo = !!pharmacyList?.length
                    ? pharmacyList[0]
                    : pharmacyInfoConfig?.filterNormalPharmacyList?.find((k) => k.no == PharmacyType.normal);
            }
        }
        this._initDetailData();
        this.innerState.detailData = this.innerState.detailData ?? new OutpatientInvoiceDetail();
    }
    private _initPageTrigger(): void {
        const clinic = this._searchClinicId ? { clinicId: this._searchClinicId } : userCenter.clinic;
        this.searchHistory.onChangedObserver
            .subscribe((searchHistory) => {
                this.innerState.localSearchHistory = searchHistory;
                this.update();
            })
            .addToDisposableBag(this);

        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.loadError = null;
                    if (!this.innerState.selectedView && !_.isEmpty(this.innerState.keyword)) {
                        this.innerState.loading = true;
                        this.update();
                    }

                    switch (this.medicineType) {
                        case MedicineAddType.examination: {
                            return GoodsAgent.searchExamination({
                                clinicId: clinic!.clinicId,
                                keyword: this.innerState.keyword,
                                diagnosis: "",
                            })
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        }
                        case MedicineAddType.treatment: {
                            return GoodsAgent.searchTreatments({
                                clinicId: clinic!.clinicId,
                                keyword: this.innerState.keyword,
                                diagnosis: "",
                            })
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        }
                        case MedicineAddType.goods: {
                            return GoodsAgent.searchStockGoods({
                                clinicId: clinic!.clinicId,
                                jsonType: [{ type: GoodsType.material }, { type: GoodsType.goods }],
                                spec: "",
                                keyword: this.innerState.keyword,
                                diagnosis: "",
                            })
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        }
                        case MedicineAddType.western: {
                            // 成药处方 || 注射处方
                            return GoodsAgent.smartSearchMedicine({
                                keyword: this.innerState.keyword,
                                clinicId: clinic!.clinicId,
                                chiefComplaint: this.params?.chiefComplaint ?? "",
                                diagnosis: this.params?.diagnosis ?? "",
                                jsonType: [
                                    {
                                        type: GoodsType.medicine,
                                        subType: [GoodsSubType.medicineWestern, GoodsSubType.medicineChinesePatent],
                                    },
                                    { type: GoodsType.material },
                                ],
                                sex: this.params?.patient?.sex,
                                age: this.params?.patient?.age,
                                goodsIds: [],
                                offset: 0,
                                limit: 50,
                                withDomainMedicine: 1,
                            })
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        }
                        case MedicineAddType.chinese: {
                            if (this.innerState.groups[0]?.chinesePrescriptionUsage?.pharmacyType == PharmacyType.air) {
                                if (this.innerState.keyword == "") {
                                    return of(null);
                                }
                                return GoodsAgent.searchAirPharmacyMedicines(
                                    this.innerState.keyword,
                                    this.innerState.groups![0]?.chinesePrescriptionUsage?.specificationType
                                        ? ChineseGoodType.chineseGranule
                                        : ChineseGoodType.chinesePiece,
                                    this.innerState.groups[0].vendor?.vendorId ?? "",
                                    1,
                                    this.innerState.groups[0].vendor?.chooseMedicalStateId ?? "",
                                    this.innerState.groups[0].vendor?.vendorUsageScopeId ?? ""
                                )
                                    .catch((error) => new ABCError(error))
                                    .toObservable();
                            }

                            return GoodsAgent.smartSearchMedicine({
                                keyword: this.innerState.keyword,
                                clinicId: clinic!.clinicId,
                                spec: ChineseMedicineSpecType.fullNames()[
                                    this.innerState._chineseSpecType ?? this.innerState.chineseMedicineSpecType
                                ],
                                chiefComplaint: this.params?.chiefComplaint ?? "",
                                diagnosis: this.params?.diagnosis ?? "",
                                jsonType: [{ type: GoodsType.medicine, subType: [GoodsSubType.medicineChinese] }],
                                sex: this.params?.patient?.sex,
                                age: this.params?.patient?.age,
                                goodsIds: [],
                                offset: 0,
                                limit: 50,
                                withDomainMedicine: 1,
                                pharmacyNo:
                                    (this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy &&
                                    this.innerState.pharmacy == PharmacyType.normal
                                        ? this.innerState.selectPharmacyInfo?.no?.toString()
                                        : this.innerState.groups[0]?.chinesePrescriptionUsage?.pharmacyNo?.toString()) ?? "",
                                searchByRoot: 0,
                                needAlias: 1,
                            })
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        }
                        default: {
                            return GoodsAgent.searchWesternMedicine(this.innerState.keyword, clinic!.clinicId)
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        }
                    }
                })
            )
            .subscribe((data) => {
                LogUtils.d(
                    "_EventQueryPatient subscribe=>>>>>>>>" +
                        JSON.stringify(data) +
                        "_EventQueryPatient key>>>>>>>>>>>>" +
                        this.innerState.keyword
                );
                if (data == null) {
                    this.innerState.loading = false;
                    this.update();
                    return;
                }
                if (data instanceof ABCError) {
                    this.innerState.loading = false;
                    this.innerState.loadError = data.detailError;
                    this.update();
                    return;
                }
                if (this.innerState.keyword == "" && (this.innerState.selectSearchView || this.innerState.defaultSearchView)) {
                    this.dispatch(new _EventChangePageShow("default"));
                } else if (!this.innerState.selectedView) {
                    this.dispatch(new _EventChangePageShow("search"));
                }

                this.innerState.loading = false;
                const _inStock: GoodsInfo[] = [],
                    _noStock: GoodsInfo[] = [];
                data?.forEach((item: GoodsInfo) => {
                    if (item.inStock) _inStock.push(item);
                    else _noStock.push(item);
                });
                this.innerState.searchInfo = _inStock;
                this.innerState.searchListNoStock = _noStock;
                if (this.innerState.keyword == "") {
                    this.innerState.defaultSearchInfo = _inStock;
                    this.innerState.noData = false;
                } else {
                    this.innerState.noData = !(
                        Boolean(this.innerState.searchInfo.length) || Boolean(this.innerState.searchListNoStock.length)
                    );
                }

                this.update();
            })
            .addToDisposableBag(this);

        //调用空中药房供应商列表接口
        /**
         * 加载供应商列表(空中药房)
         */
        this._loadVendorList
            .pipe(
                switchMap((/*__*/) => {
                    if (_.isEmpty(this.innerState.groups?.[0]?.selectedMedicines)) {
                        return of(undefined);
                    }
                    this.update();
                    const { count, medicineStateScopeId, usageScopeId, pharmacyNo } =
                        this.innerState.groups[0]!.chinesePrescriptionUsage ?? new ChinesePrescriptionUsage();
                    const prescriptForms = [];
                    for (const medicine of this.innerState.groups[0].selectedMedicines) {
                        let formItem = JsonMapper.deserialize(PrescriptionFormItem, {
                            keyId: UUIDGen.generate(),
                        });
                        formItem = OutpatientUtils.fillPrescriptionFromItem(
                            formItem,
                            medicine,
                            this.innerState.groups[0]?.inputUsages?.get(medicine) ?? new MedicineUsage()
                        );
                        prescriptForms.push(formItem);
                    }
                    return ChargeAgent.getVendors({
                        airPharmacyFormItems: prescriptForms,
                        doseCount: count ?? 1,
                        goodsTypeId:
                            this.innerState.chineseMedicineSpecType == ChineseMedicineSpecType.chineseGranule
                                ? ChineseGoodType.chineseGranule
                                : this.innerState.chineseMedicineSpecType == ChineseMedicineSpecType.chinesePiece
                                ? ChineseGoodType.chinesePiece
                                : ChineseGoodType.chinesePiece,
                        medicineStateScopeId: medicineStateScopeId,
                        usageScopeId: usageScopeId,
                        vendorId: this.innerState.groups?.[0]?.vendor?.vendorId ?? null,
                        pharmacyNo: pharmacyNo,
                    })
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe(
                (rsp) => {
                    if (rsp instanceof ABCError) {
                        this.innerState.loadError = rsp;
                        this.update();
                        return;
                    }
                    if (rsp) {
                        // const selectVendor = rsp?.find((item) => item.checked);//原先选中的逻辑根据后台返回的checked字段来判断，现在改为根据vendorId来判断
                        const currentVendor = this.innerState.groups?.[0]?.vendor;
                        const selectVendor = rsp?.find((item) =>
                            !!currentVendor?.vendorUsageScopeId
                                ? item?.vendorUsageScopeId == currentVendor?.vendorUsageScopeId
                                : item.vendorId == currentVendor?.vendorId && item.vendorName == currentVendor?.vendorName
                        );
                        if (selectVendor) {
                            //解决后台没有返回orderItems的问题
                            const updateOrderItems: AirPharmacyOrderItem[] = [];
                            this.innerState.groups?.[0]?.selectedMedicines?.forEach((item) => {
                                const isExist = selectVendor?.orderItems?.filter((t) => t?.displayName == item?.displayName);
                                if (!!isExist?.length) {
                                    isExist.forEach((t) => {
                                        updateOrderItems.push(t);
                                    });
                                } else {
                                    updateOrderItems.push(
                                        JsonMapper.deserialize(AirPharmacyOrderItem, { ...item, productInfo: undefined })
                                    );
                                }
                            });
                            selectVendor.orderItems = updateOrderItems?.map((item) => {
                                item.unit = item.unit ?? "g";
                                return item;
                            });
                            this.innerState.groups[0].vendor = selectVendor;
                            this.innerState.groups[0].chinesePrescriptionUsage!.vendorInfo =
                                this.innerState.groups?.[0]?.chinesePrescriptionUsage?.vendorInfo ?? new AirPharmacyKeLiConfig();
                            this.innerState.groups[0].chinesePrescriptionUsage!.vendorInfo = JsonMapper.deserialize(AirPharmacyKeLiConfig, {
                                calculateProcessBagType: selectVendor?.calculateProcessBagType,
                                processBagUnitCount: selectVendor?.processBagUnitCount,
                                processBagUnit: selectVendor?.processBagUnit,
                                doseCountLimit: selectVendor?.doseCountLimit,
                            });
                            //需要更新当前药品库存信息
                            const _prescriptForms = [];
                            for (const medicine of selectVendor.orderItems ?? []) {
                                let formItem = JsonMapper.deserialize(PrescriptionFormItem, {
                                    keyId: UUIDGen.generate(),
                                });
                                const _sameGood = this.innerState.groups[0].selectedMedicines?.find((item) =>
                                    !!medicine.productId && item.id
                                        ? item.id == medicine.productId && item.displayName == medicine?.displayName
                                        : item.displayName == medicine?.displayName
                                );

                                const usage = (_sameGood && this.innerState.groups[0].inputUsages.get(_sameGood!)) ?? new MedicineUsage();

                                //精准匹配到才直接替换
                                if (!_.isEmpty(medicine.productInfo) && medicine?.productInfo?.isFindOneToReplace) {
                                    formItem = OutpatientUtils.fillPrescriptionFromItem(
                                        formItem,
                                        medicine.productInfo!,
                                        JsonMapper.deserialize(MedicineUsage, {
                                            unit: medicine?.unit,
                                            unitCount: usage?.unitCount ?? 1,
                                        })
                                    );
                                } else {
                                    formItem.unit = medicine?.unit;
                                    formItem.unitCount = usage?.unitCount ?? 1;
                                    formItem.name = medicine.displayName;
                                    formItem.unitPrice = medicine?.unitPrice;
                                    formItem.dosageUnit = usage?.dosage?.dosageUnit;
                                    formItem.dosage = usage?.dosage?.count?.toString();
                                    formItem.ast = usage?.ast;
                                    formItem.astResult = usage?.astResult;
                                    formItem.usage = usage?.usage?.name;
                                    formItem.freq = usage?.freq?.en ?? usage?.freq?.name;
                                    formItem.days = usage?.days;
                                    formItem.inputUsages = usage;
                                }

                                _prescriptForms.push(formItem);
                            }
                            const group = this.innerState.groups[0];
                            //空中药房,需要匹配上次记忆用法用量是否存在当前供应商配置中，不存在，需要置空
                            if (
                                group.chinesePrescriptionUsage?.pharmacyType == PharmacyType.air &&
                                group.chinesePrescriptionUsage?.usageScopeId
                            ) {
                                const usageOptions = selectVendor?.businessScopeConfig?.usageOptions,
                                    freqOptions = selectVendor?.businessScopeConfig?.freqOptions,
                                    dosageOptions = selectVendor?.businessScopeConfig?.dosageOptions,
                                    usageLevelOptions = selectVendor?.businessScopeConfig?.usageLevelOptions;
                                if (!usageOptions?.find((t) => t?.name == group?.chinesePrescriptionUsage?.usage?.name)) {
                                    group.chinesePrescriptionUsage.usage!.name = "";
                                }
                                if (
                                    !!freqOptions?.length &&
                                    !freqOptions?.find((t) => t?.name == group?.chinesePrescriptionUsage?.freq?.name)
                                ) {
                                    group.chinesePrescriptionUsage.freq!.name = "";
                                }
                                if (
                                    !!dosageOptions?.length &&
                                    !dosageOptions?.find((t) => t?.name == group?.chinesePrescriptionUsage?.dailyDosage?.name)
                                ) {
                                    group.chinesePrescriptionUsage.dailyDosage!.name = "";
                                }
                                if (
                                    !!usageLevelOptions?.length &&
                                    !usageLevelOptions?.find((t) => t?.name == group?.chinesePrescriptionUsage?.usageLevel?.name)
                                ) {
                                    group.chinesePrescriptionUsage.usageLevel!.name = "";
                                }
                            }
                            const medicines: Array<GoodsInfo> = [];
                            const usages: AbcMap<GoodsInfo, MedicineUsage> = new AbcMap();
                            if (!ABCUtils.isEmpty(_prescriptForms)) {
                                _prescriptForms?.forEach((formItem: PrescriptionFormItem, index: number) => {
                                    const _sameIndex = _.findIndex(group.selectedMedicines, (i) => i.displayName == formItem.displayName);
                                    let _oldUsage;
                                    if (_.isNumber(_sameIndex)) {
                                        if (_sameIndex < 0) {
                                            //空中药房药品列表返回的数据与本地
                                            _oldUsage = group.inputUsages.get(group.selectedMedicines[index]);
                                            const newUsage = MedicineUsage.fromPrescriptionFormItem(formItem);
                                            const _oldMedicine = ABCUtils.first(medicines.splice(index, 1, formItem.goodsInfo));
                                            _oldMedicine && usages.delete(_oldMedicine);
                                            usages.set(formItem.goodsInfo, _oldUsage ?? newUsage);
                                        } else {
                                            _oldUsage = group.inputUsages.get(group.selectedMedicines[_sameIndex]);
                                            const medicine = formItem.goodsInfo;
                                            const newUsage = MedicineUsage.fromPrescriptionFormItem(formItem);
                                            medicines.push(medicine);
                                            usages.set(medicine, _oldUsage ?? newUsage);
                                        }
                                    }
                                });
                            }
                            this.innerState.groups[0].selectedMedicines = medicines;
                            this.innerState.groups[0].inputUsages = usages;
                        }
                    }
                    this.computedAirPharmacyUSagDays();
                    this.update();
                },
                (error) => {
                    this.innerState.loading = false;
                    this.innerState.loadError = error;
                    this.update();
                }
            )
            .addToDisposableBag(this);

        /**
         * 加载虚拟药房列表
         */
        this._loadVirtualVendorList
            .pipe(
                switchMap(() => {
                    if (_.isEmpty(this.innerState.groups?.[0]?.selectedMedicines)) {
                        return of(undefined);
                    }
                    this.update();

                    return ChargeAgent.getVirtualVendorList()
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe(
                async (rsp) => {
                    if (rsp instanceof ABCError) {
                        this.innerState.loadError = rsp;
                        this.update();
                        return;
                    }
                    if (rsp) {
                        const chineseUsage = this.innerState.groups?.[0]?.chinesePrescriptionUsage;

                        const virtualList =
                            this.innerState.chineseMedicineSpecType == ChineseMedicineSpecType.chineseGranule
                                ? rsp?.supportCmParticlesVendors
                                : this.innerState.chineseMedicineSpecType == ChineseMedicineSpecType.chinesePiece
                                ? rsp?.supportCmSlicesVendors
                                : [];
                        //虚拟药房--饮片和颗粒供应商的venderId有相同的，所以如果饮片和颗粒相互切换时，会选中对应venderId相同的一项
                        const groupVendorId = this.innerState.groups?.[0]?.vendor?.vendorId;
                        const selectVendor =
                            virtualList?.find((item) =>
                                chineseUsage?.medicineStateScopeId
                                    ? (!!groupVendorId
                                          ? item.vendorId == groupVendorId
                                          : !!chineseUsage?.pharmacyName
                                          ? item.vendorName == chineseUsage?.pharmacyName
                                          : false) &&
                                      !!item.vendorAvailableMedicalStates?.find(
                                          (t) => t.medicalStatId == chineseUsage?.medicineStateScopeId
                                      )
                                    : !!groupVendorId
                                    ? item.vendorId == groupVendorId
                                    : !!chineseUsage?.pharmacyName
                                    ? item.vendorName == chineseUsage?.pharmacyName
                                    : false
                            ) ?? _.first(virtualList);

                        if (selectVendor) {
                            this.innerState.groups[0].vendor = JsonMapper.deserialize(VendorInfo, { ...selectVendor });
                        }
                    }
                    this.update();
                },
                (error) => {
                    this.innerState.loading = false;
                    this.innerState.loadError = error;
                    this.update();
                }
            )
            .addToDisposableBag(this);

        this._calculatePriceTrigger
            .pipe(
                debounceTime(300),
                switchMap((type) => {
                    if (!this.innerState.detailData) return of(null);
                    return fromPromise(
                        this._calculatePrice()
                            .then((rsp) => {
                                return { calculateRspData: rsp, type: type };
                            })
                            .catch((e) => new ABCError(e))
                    );
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                if (rsp instanceof ABCError) {
                    this.dispatch(
                        new _EventUpdateCalculatePrice({
                            calculating: false,
                            calculateRspData: null,
                            error: rsp,
                            shouldNotCopyExpectedPrice: false,
                        })
                    );
                } else {
                    const { calculateRspData, type } = rsp;
                    this.dispatch(
                        new _EventUpdateCalculatePrice({
                            calculating: false,
                            calculateRspData: calculateRspData,
                            error: null,
                            shouldNotCopyExpectedPrice: false,
                            type: type,
                        })
                    );
                }
            })
            .addToDisposableBag(this);

        this._airPharmacyCalculateTrigger
            .pipe(
                switchMap(() => {
                    const isPiece = this.innerState.chineseMedicineSpecType == ChineseMedicineSpecType.chinesePiece;
                    const params = new AirPharmacyCalculateReq();
                    const chinesePrescriptionUsage =
                        this.innerState.groups?.[0]?.chinesePrescriptionUsage ?? new ChinesePrescriptionUsage();
                    const {
                        prescriptionFormDelivery,
                        count,
                        medicineStateScopeId,
                        usageScopeId,
                        vendorId,
                        usage,
                        dailyDosage,
                        freq,
                        usageLevel,
                        totalProcessCount,
                    } = chinesePrescriptionUsage;
                    if (!this.innerState.groups?.[0]?.selectedMedicines?.length) return of(null);
                    const productItems = this.innerState.groups?.[0]?.selectedMedicines?.map((item) => {
                        const usage = this.innerState.groups?.[0]?.inputUsages?.get(item);
                        return {
                            productId: item.id,
                            name: item.displayName,
                            unitPrice: item.unitPrice,
                            unit: usage?.unit,
                            unitCount: usage?.unitCount,
                            doseCount: count,
                        };
                    });
                    params.forms = [
                        {
                            deliveryInfo: prescriptionFormDelivery,
                            deliveryPrimaryFormId: "",
                            goodsTypeId: isPiece ? ChineseGoodType.chinesePiece : ChineseGoodType.chineseGranule,
                            items: productItems ?? [],
                            medicineStateScopeId: medicineStateScopeId,
                            usageInfo: {
                                dailyDosage: dailyDosage?.name,
                                doseCount: count,
                                freq: freq?.name,
                                totalProcessCount: medicineStateScopeId == MedicineScopeId.daiJian ? totalProcessCount : undefined,
                                usage: usage?.name,
                                usageLevel: usageLevel?.name,
                            },
                            usageScopeId: usageScopeId,
                            vendorId: vendorId ?? this.innerState.groups?.[0]?.vendor?.vendorId,
                        },
                    ];
                    return ChargeAgent.getAirPharmacyCalculate(params)
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!(rsp instanceof ABCError) && !!rsp) {
                    this.innerState.airPharmacyTotalPrice = rsp[0]?.goodsTotalPrice || 0;
                    this.update();
                }
            });
    }
    async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        await this._initPageConfig();
        this._initPageTrigger();
        if (this.innerState.isExamination || this.innerState.isGoods || this.innerState.isTreatment) this._loadDataTrigger.next();

        //当前为空中药房，且药品为饮片类型，药态不存在，需要查询一次供应商列表信息，避免接诊完诊后，再次点击，药态列表不存在的情况
        if (this.innerState.isChinesePrescription) {
            const pharmacyType = !_.isUndefined(this.innerState.groups?.[0]?.vendor?.pharmacyType)
                ? this.innerState.groups?.[0]?.vendor?.pharmacyType
                : this.innerState.chinesePrescriptionUsage?.pharmacyType;
            //空中药房
            if (pharmacyType == PharmacyType.air) {
                this._loadVendorList.next();
            }
            if (pharmacyType == PharmacyType.virtual) {
                this._loadVirtualVendorList.next();
            }

            //空中药房，制法为代煎
            if (
                this.innerState.groups?.[0]?.vendor?.pharmacyType == PharmacyType.air &&
                (this.innerState.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.daiJian ||
                    this.innerState.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.keLi) &&
                _.isEmpty(this.innerState.groups[0].selectedMedicines)
            ) {
                this.computedTisanesSack(false);
            }
            //空中药房，制法为代煎
            if (
                this.innerState.groups?.[0]?.vendor?.pharmacyType == PharmacyType.air &&
                (this.innerState.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.daiJian ||
                    this.innerState.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.keLi) &&
                !!this.innerState.groups[0].selectedMedicines?.length
            ) {
                this._airPharmacyCalculateTrigger.next();
            }
        }
        const type = (() => {
            if (this.medicineType == MedicineAddType.infusion) return "infusion";
            else if (this.medicineType == MedicineAddType.western) return "western";
            else if (this.medicineType == MedicineAddType.chinese) return "chinese";
        })();
        if (!!type) this._calculatePriceTrigger.next(type);
        this.update();
    }

    async _initMedicineGroup(group: MedicineAddGroup): Promise<void> {
        if (this.innerState.isChinesePrescription) {
            const _pharmacyInfo = clinicSharedPreferences.getObject(SharedReferenceConstants.LAST_SELECT_VENDOR_INFO) as AirPharmacyDetail;
            const chineseConfig = this.innerState.chineseMedicineConfig;
            //空中药房需要记忆上次用法用量
            let chinesePrescriptionUsage = !!clinicSharedPreferences.getObject(CHINESE_PRESCRIPTION_USAGE_DEFAULT)
                ? JSON.parse(clinicSharedPreferences.getObject(CHINESE_PRESCRIPTION_USAGE_DEFAULT))
                : undefined;
            chinesePrescriptionUsage = !!chinesePrescriptionUsage
                ? JsonMapper.deserialize(PrescriptionDefaultUsage, {
                      dailyDosage: chinesePrescriptionUsage?.dailyDosage?.name,
                      usage: chinesePrescriptionUsage?.usage?.name,
                      freq: chinesePrescriptionUsage?.freq?.name,
                      usageLevel: chinesePrescriptionUsage?.usageLevel?.name,
                      usageDays: chinesePrescriptionUsage?.usageDays?.name,
                  })
                : undefined;
            const isAirPharmacy = _pharmacyInfo?.usageScopeId && _pharmacyInfo.pharmacyType == PharmacyType.air;
            const chineseUsageInfo =
                !!chinesePrescriptionUsage && isAirPharmacy
                    ? chinesePrescriptionUsage
                    : _pharmacyInfo?.usageScopeId
                    ? await ChineseMedicineConfigProvider.getChinesePRWithScopeId(_pharmacyInfo.usageScopeId)
                    : ChineseMedicineConfigProvider.getChinesePRWithSpecification(ChineseMedicineSpecType.chinesePiece);
            //空中药房的用法配置--从接口读取
            let airUsage, airFreq, airUsageLevel, airDosage;
            const usageOptions =
                group?.vendor?.businessScopeConfig?.usageOptions ?? _pharmacyInfo?.vendor?.businessScopeConfig?.usageOptions;
            const freqOptions = group?.vendor?.businessScopeConfig?.freqOptions ?? _pharmacyInfo?.vendor?.businessScopeConfig?.freqOptions;
            const usageLevelOptions =
                group?.vendor?.businessScopeConfig?.usageLevelOptions ?? _pharmacyInfo?.vendor?.businessScopeConfig?.usageLevelOptions;
            const dosageOptions =
                group.vendor?.businessScopeConfig?.dosageOptions ?? _pharmacyInfo?.vendor?.businessScopeConfig?.dosageOptions;
            //空中药房用法用量--如果上次用法用量存在当前供应商列表中，则取上次用法用量，否则取默认值
            if (isAirPharmacy) {
                //用法
                airUsage = !!usageOptions?.find((t) => t?.name == chineseUsageInfo?.usage)
                    ? usageOptions?.find((t) => t?.name == chineseUsageInfo?.usage)
                    : usageOptions?.find((t) => t?.defaultValue);
                //频率
                airFreq = !!freqOptions?.find((t) => t?.name == chineseUsageInfo?.freq)
                    ? freqOptions?.find((t) => t?.name == chineseUsageInfo?.freq)
                    : freqOptions?.find((t) => t?.defaultValue);
                //用量
                airUsageLevel = !!usageLevelOptions?.find((t) => t?.name == chineseUsageInfo?.usageLevel)
                    ? usageLevelOptions?.find((t) => t?.name == chineseUsageInfo?.usageLevel)
                    : usageLevelOptions?.find((t) => t?.defaultValue);
                //剂量
                airDosage = !!dosageOptions?.find((t) => t?.name == chineseUsageInfo?.dailyDosage)
                    ? dosageOptions?.find((t) => t?.name == chineseUsageInfo?.dailyDosage)
                    : dosageOptions?.find((t) => t?.defaultValue);
                //空中药房颗粒剂配置
                if (!!group?.chinesePrescriptionUsage) {
                    group.chinesePrescriptionUsage!.vendorInfo = group.chinesePrescriptionUsage?.vendorInfo ?? new AirPharmacyKeLiConfig();
                    group.chinesePrescriptionUsage!.vendorInfo!.calculateProcessBagType = _pharmacyInfo.vendor?.calculateProcessBagType;
                    group.chinesePrescriptionUsage!.vendorInfo!.processBagUnit = _pharmacyInfo.vendor?.processBagUnit;
                    group.chinesePrescriptionUsage!.vendorInfo!.processBagUnitCount = _pharmacyInfo.vendor?.processBagUnitCount;
                    group.chinesePrescriptionUsage!.vendorInfo!.doseCountLimit = _pharmacyInfo.vendor?.doseCountLimit;
                    group.chinesePrescriptionUsage.specificationType = ChineseMedicineSpecType!.typeFromName(
                        _pharmacyInfo?.usageScopeId == "7" ? "颗粒" : "饮片"
                    );
                }
            }
            const _chinesePrescriptionUsage = {
                // count: 1,
                count: undefined,
                dailyDosage:
                    (isAirPharmacy ? airDosage : chineseConfig.dailyDosage!.find((item) => item.name == chineseUsageInfo?.dailyDosage)) ??
                    JsonMapper.deserialize(ChineseUsageItemInfo, {
                        name: isAirPharmacy && !airDosage ? "" : chineseUsageInfo?.dailyDosage,
                    }),
                usage:
                    (isAirPharmacy ? airUsage : chineseConfig.usages!.find((item) => item.name == chineseUsageInfo?.usage)) ??
                    JsonMapper.deserialize(ChineseUsageItemInfo, { name: isAirPharmacy && !airUsage ? "" : chineseUsageInfo?.usage }),
                freq:
                    (isAirPharmacy ? airFreq : chineseConfig.freq!.find((item) => item.name == chineseUsageInfo?.freq)) ??
                    JsonMapper.deserialize(ChineseUsageItemInfo, { name: isAirPharmacy && !airFreq ? "" : chineseUsageInfo?.freq }),
                specificationType:
                    this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy && _pharmacyInfo?.pharmacyType == PharmacyType.normal
                        ? _pharmacyInfo?.localPharmacyType
                        : ChineseMedicineSpecType!.typeFromName(_pharmacyInfo?.usageScopeId == "7" ? "颗粒" : "饮片") ??
                          ChineseMedicineSpecType.chinesePiece,
                usageLevel:
                    (isAirPharmacy ? airUsageLevel : chineseConfig.usageLevel!.find((item) => item.name == chineseUsageInfo?.usageLevel)) ??
                    JsonMapper.deserialize(ChineseUsageItemInfo, {
                        name: isAirPharmacy && !airUsageLevel ? "" : chineseUsageInfo?.usageLevel,
                    }),
                usageDays: JsonMapper.deserialize(ChineseUsageItemInfo, { name: chineseUsageInfo?.usageDays }),
                prescriptionFormDelivery: isAirPharmacy ? this.params?.prescriptionFormDelivery : undefined,
                vendorInfo: isAirPharmacy
                    ? JsonMapper.deserialize(AirPharmacyKeLiConfig, {
                          calculateProcessBagType: _pharmacyInfo?.vendor?.calculateProcessBagType,
                          processBagUnit: _pharmacyInfo?.vendor?.processBagUnit,
                          processBagUnitCount: _pharmacyInfo?.vendor?.processBagUnitCount,
                          doseCountLimit: _pharmacyInfo?.vendor?.doseCountLimit,
                      })
                    : undefined,
            };
            //空中药房,需要匹配上次记忆用法用量是否存在当前供应商配置中，不存在，则需要置空（如果对应用法用量不存在，则不进行下面操作，避免复制历史处方后，用法用量不回填）
            if (group.chinesePrescriptionUsage?.pharmacyType == PharmacyType.air && group.chinesePrescriptionUsage?.usageScopeId) {
                if (!!usageOptions?.length && !usageOptions?.find((t) => t?.name == group?.chinesePrescriptionUsage?.usage?.name)) {
                    group.chinesePrescriptionUsage.usage!.name = "";
                }
                if (!!freqOptions?.length && !freqOptions?.find((t) => t?.name == group?.chinesePrescriptionUsage?.freq?.name)) {
                    group.chinesePrescriptionUsage.freq!.name = "";
                }
                if (!!dosageOptions?.length && !dosageOptions?.find((t) => t?.name == group?.chinesePrescriptionUsage?.dailyDosage?.name)) {
                    group.chinesePrescriptionUsage.dailyDosage!.name = "";
                }
                if (
                    !!usageLevelOptions?.length &&
                    !usageLevelOptions?.find((t) => t?.name == group?.chinesePrescriptionUsage?.usageLevel?.name)
                ) {
                    group.chinesePrescriptionUsage.usageLevel!.name = "";
                }
            }
            group.chinesePrescriptionUsage =
                group.chinesePrescriptionUsage ?? JsonMapper.deserialize(ChinesePrescriptionUsage, _chinesePrescriptionUsage);
        }

        if (this.innerState.isInfusionPrescription && group.infusionPrescriptionUsage == null) {
            group.infusionPrescriptionUsage = new InfusionPrescriptionUsage();
            this._initInfusionPrescriptionUsage(group.infusionPrescriptionUsage);
        }

        if (ABCUtils.isEmpty(group?.selectedMedicines)) return;
        for (const medicine of group.selectedMedicines!) {
            const usage = group.inputUsages?.get(medicine) ?? JsonMapper.deserialize(MedicineUsage, {});
            //完善当前选中的药房信息
            const pharmacyInfo = this.initPrescriptionPharmacyInfo(medicine);
            if (!!pharmacyInfo) {
                usage.pharmacyInfo = {
                    name: pharmacyInfo.name,
                    no: pharmacyInfo.no,
                    type: pharmacyInfo.type,
                    typeName: pharmacyInfo.typeName,
                    pharmacyTag: pharmacyInfo.extendInfo?.tag,
                };
            }
        }
    }

    /**
     * 获取各个处方当前选中的药房信息，如果没有，则设置为处方对应的默认药房
     * @param pharmacyNo---当前选择的药房号
     * @private
     */
    private initPrescriptionPharmacyInfo(medicine?: GoodsInfo): PharmacyListConfig | undefined {
        const { pharmacyInfoConfig, isChinesePrescription, isWesternPrescription, isInfusionPrescription, chineseMedicineSpecType } =
            this.innerState;

        let initGoodsTypeId = undefined;
        if (isChinesePrescription) {
            initGoodsTypeId =
                chineseMedicineSpecType == ChineseMedicineSpecType.chinesePiece
                    ? GoodsTypeId.medicineChinesePiece
                    : GoodsTypeId.medicineChineseGranule;
        } else if (isWesternPrescription) {
            initGoodsTypeId = GoodsTypeId.medicineWest;
        } else if (isInfusionPrescription) {
            initGoodsTypeId = GoodsTypeId.medicineWest;
        }
        const pharmacyInfo = pharmacyInfoConfig?.filterNormalPharmacyList?.filter((pharmacy) => pharmacy.no == medicine?.pharmacyNo);
        return !!pharmacyInfo?.length
            ? pharmacyInfo[0]
            : pharmacyInfoConfig?.getDefaultPharmacy({
                  departmentId: this._departmentId,
                  goodsInfo: {
                      typeId: medicine?.typeId ?? initGoodsTypeId,
                  },
              });
    }

    async *_mapEventUpdate(/*event: _EventUpdate*/): AsyncGenerator<State> {
        //输注处方每次更新都去刷新下组id，保证组id递增
        if (this.innerState.isInfusionPrescription) {
            this.innerState.groups.map((group, index) => {
                group.groupId = index + 1;
            });
        }

        yield this.innerState;
    }

    // TODO 待删除
    async *_mapEventToggleSearchFocus(event: _EventToggleSearchFocus): AsyncGenerator<State> {
        this.innerState.searchFocus = event.focus;
        this.update();
    }

    async *_mapEventAddMedicine(event: _EventAddMedicine): AsyncGenerator<State> {
        LogUtils.d(`_mapEventAddMedicine event=${JSON.stringify(event.medicine)}`);
        //空中药房、代煎代配药房不受库存开出限制
        if (
            !event.medicine?.pharmacyType &&
            !this.innerState.canCheckWithoutStock &&
            (!event.medicine.inStock || (event.medicine.getStockPieceCount() ?? 0) < 0)
        )
            return;
        const _keyword = this.innerState.keyword;
        this.innerState.keyword = "";
        this.innerState.searchListNoStock = [];
        this.innerState.searchInfo = [];
        let group: MedicineAddGroup;
        const medicine = JsonMapper.deserialize(GoodsInfo, { ...event.medicine, keyId: event.medicine?.keyId ?? UUIDGen.generate() });
        if (_.isEmpty(this.innerState.groups)) {
            const group = new MedicineAddGroup();
            this._initMedicineGroup(group);
            this.innerState.groups.push(group);
        }
        const groupCount = this.innerState.groups.length;
        if (groupCount > 1 && this.innerState.isInfusionPrescription) {
            //处理添加组的逻辑
            const optionWeight: JSX.Element[] = [];
            this.innerState.groups.forEach((item, index) => {
                optionWeight.push(
                    <MedicineAddPageInfusionGroupItem index={`组${index + 1}`} selectGoodsInfo={item.selectedMedicines ?? []} />
                );
            });
            const selects = await showOptionsBottomSheet({
                title: "选择添加组",
                optionsWidgets: optionWeight,
            });
            if (ABCUtils.isEmpty(selects)) return;

            group = this.innerState.groups[selects![0]];
        } else if (this.innerState.isWesternPrescription) {
            //西药添加组逻辑
            let _group = this.innerState.groups.find((item) => item.groupId == ChargeUtils.maxGroupId);
            if (!_group) {
                _group = new MedicineAddGroup();
                _group.groupId = ChargeUtils.maxGroupId;
                this.innerState.groups.push(_group);
            }
            group = _group;
        } else {
            group = this.innerState.groups[0];
        }

        this.innerState.currentFocusGroup = group;
        this.innerState.currentFocusItem = medicine;
        this._searchInput?.clear();
        this.hasChanged = true;

        for (const medicineItem of group.selectedMedicines) {
            if (
                (!!medicine?.keyId && !!medicineItem?.keyId && medicineItem.keyId == medicine.keyId) ||
                (medicineItem.id == medicine.id && medicineItem.displayName == medicine.displayName)
            ) {
                Toast.show(`${medicine.displayName}已添加`, { warning: true, autoBlurText: false });
                // this.innerState.medicineAutoFocus = true;
                this.dispatch(new _EventChangePageShow("selected"));
                // this.innerState.currentFocusItem = medicineItem;
                // this.innerState.pendingActiveFocusItem = new Completer();
                // yield ScrollToFocusItemState.fromState(this.innerState);
                return;
            }
        }
        group.selectedMedicines?.push(medicine);
        await this.searchHistory.insert(_keyword);

        let age;
        if (this.params?.patient?.age != null) {
            age = (this.params.patient.age.year ?? 0) + (this.params.patient.age.month ?? 0) / 10;
        }

        const sellUnits = medicine.sellUnits;
        const usage = JsonMapper.deserialize(MedicineUsage, {
            unit: ABCUtils.first(sellUnits) ?? undefined,
            dosage: JsonMapper.deserialize(MedicineDosageUsage, {
                dosageUnit: ABCUtils.first(this.innerState.westernMedicineConfig?.dosageUnitStringList ?? []),
            }),
            usage: new Usage(),
            freq: new Freq(),
            specialRequirement: new ChineseUsageItemInfo(),
            pharmacyInfo: this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy
                ? this.initPrescriptionPharmacyInfo(medicine)
                : undefined,
            totalPrice: 0,
            totalPriceRatio: 1,
        });
        const units = medicine.usageUnits;
        if (!_.isEmpty(units)) usage.dosage!.dosageUnit = units[0];

        if (this.innerState.isTreatment || this.innerState.isExamination || this.innerState.isGoods) {
            usage.unitCount = 1;
        }

        if (this.innerState.isWesternPrescription || this.innerState.isInfusionPrescription) {
            usage.unit = usage.unit ?? WesternMedicine.unit[0].name;
        }

        if (this.innerState.isChinesePrescription) {
            if (!medicine.inStock) usage.unit = usage.unit ?? "g";
        }

        //完善当前选中的药房信息
        const pharmacyInfo = this.innerState.pharmacyInfoConfig?.filterNormalPharmacyList?.find(
            (pharmacy) => pharmacy.no == medicine.pharmacyNo
        );
        if (!!pharmacyInfo) {
            usage.pharmacyInfo = {
                name: pharmacyInfo.name,
                no: pharmacyInfo.no,
                type: pharmacyInfo.type,
                typeName: pharmacyInfo.typeName,
                pharmacyTag: pharmacyInfo.extendInfo?.tag,
            };
        }

        group.inputUsages.set(medicine, usage);

        if (this.innerState.isInfusionPrescription || this.innerState.isWesternPrescription) {
            this.innerState.medicineAutoFocus = false;
            try {
                CDSSAgent.getMedicineUsage({
                    medicineCadn: medicine.medicineCadn,
                    goodsId: medicine.id,
                    type: medicine.type,
                    age: age,
                    sex: this.params?.patient?.sex,
                })
                    .then(async (usage) => {
                        LogUtils.d("CDSSAgent.getMedicineUsage usage = " + JSON.stringify(usage));
                        if (usage == null) return;
                        const westernConfig = this.innerState.westernMedicineConfig;
                        const validDosageUnit =
                            _.find(medicine.usageUnits, (item) => item == usage.dosageUnit) ||
                            usage.dosageUnit == OutpatientConst.dosageUsageProperty;
                        const validUnit = _.find(medicine.sellUnits, usage.unit);

                        const medicineUsage = JsonMapper.deserialize(MedicineUsage, {
                            usage: JsonMapper.deserialize(Usage, { name: usage.usage }),
                            freq: westernConfig?.freq?.find((item: Freq) => item.en == usage.freq || item.name == usage.name) ?? new Freq(),
                            dosage: JsonMapper.deserialize(MedicineDosageUsage, {
                                count: Number(usage.dosage) ? Number(usage.dosage) : undefined,
                                dosageUnit: ABCUtils.first(medicine.usageUnits ?? westernConfig?.dosageUnitStringList ?? []),
                            }),
                        });

                        if (validDosageUnit) {
                            medicineUsage.dosage!.dosageUnit = usage.dosageUnit;
                        } else {
                            medicineUsage.dosage!.dosageUnit = ABCUtils.first(medicine.usageUnits) ?? usage.dosageUnit ?? "";
                        }
                        if (validUnit) {
                            medicineUsage.unit = usage?.unit;
                        } else {
                            medicineUsage.unit = ABCUtils.first(sellUnits) ?? usage.unit ?? "";
                        }

                        //完善当前选中的药房信息
                        const pharmacyInfo = this.innerState.pharmacyInfoConfig?.filterNormalPharmacyList?.find(
                            (pharmacy) => pharmacy.no == medicine?.pharmacyNo
                        );
                        if (!!pharmacyInfo) {
                            medicineUsage.pharmacyInfo = {
                                name: pharmacyInfo.name,
                                no: pharmacyInfo.no,
                                type: pharmacyInfo.type,
                                typeName: pharmacyInfo.typeName,
                                pharmacyTag: pharmacyInfo.extendInfo?.tag,
                            };
                        }
                        group.inputUsages.set(medicine, medicineUsage);
                        this.innerState.medicineAutoFocus = true;
                        this.update();
                    })
                    .finally(() => {
                        this.innerState.medicineAutoFocus = true;
                        this.innerState.pendingActiveFocusItem = new Completer();
                        this.update();
                    });
            } catch (e) {
                this.innerState.pendingActiveFocusItem = new Completer();
                this.innerState.medicineAutoFocus = true;
                this.update();
            }
        } else {
            this.innerState.pendingActiveFocusItem = new Completer();
            this.innerState.medicineAutoFocus = true;
        }
        if (this.innerState.isChinesePrescription) {
            if (this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy) {
                this.innerState._chineseSpecType = this.innerState.chineseMedicineSpecType;
            }
            //第一次添加空中药房触发一次拉取空中药房信息
            const pharmacyType = !_.isUndefined(this.innerState.groups?.[0]?.vendor?.pharmacyType)
                ? this.innerState.groups?.[0]?.vendor?.pharmacyType
                : this.innerState.chinesePrescriptionUsage?.pharmacyType;
            if (pharmacyType == PharmacyType.air && group.selectedMedicines.length === 1) {
                this._loadVendorList.next();
            }
            this.innerState.medicineAutoFocus = true;
            /**
             * 组件上屏后再触发焦点
             */
            yield this.innerState;
            yield ScrollToFocusItemState.fromState(this.innerState);
        } else {
            this.dispatch(new _EventChangePageShow("selected"));
            this.update();
        }
    }

    async *_EventSearchGoods(event: _EventSearchGoods): AsyncGenerator<State> {
        this.innerState.medicineAutoFocus = false;
        this.innerState.keyword = event.keyword;
        if (this.innerState.isChinesePrescription) {
            //中药搜索默认清空上次内容
            this.innerState.searchInfo = [];
            this.innerState.searchListNoStock = [];
            //如果此时为多药房，并且输入框无值，则类型切换应该恢复成当前选择类型
            if (this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy && !this.innerState.keyword) {
                this.innerState._chineseSpecType = this.innerState.chineseMedicineSpecType;
            }
        }
        if (this.innerState.isChinesePrescription && !this.innerState.keyword) return;
        this._loadDataTrigger.next(0);
    }

    async *_mapEventDeleteMedicine(event: _EventDeleteMedicine): AsyncGenerator<State> {
        const group = event.group ? event.group : this.innerState.groups[0];
        const pharmacyType = group?.chinesePrescriptionUsage?.pharmacyType ?? PharmacyType.normal;
        const westernForm = this._filterWesternPrescriptionForms(),
            infusionForm = this._filterInfusionPrescriptionForms(),
            chineseForm = this._filterChinesePrescriptionForms(pharmacyType);
        group.selectedMedicines.forEach((item, index) => {
            if (event.medicine == item) {
                (event.type == "western" ? westernForm : event.type == "chinese" ? chineseForm : infusionForm)?.forEach((form: any) => {
                    _.remove(form?.prescriptionFormItems ?? [], (formItem: PrescriptionFormItem) => () => {
                        if (!pharmacyType) return formItem.keyId == event.medicine.keyId;
                        return formItem.displayName == event.medicine.displayName;
                    });
                });
                group.inputUsages.delete(item);
                return group.selectedMedicines.splice(index, 1);
            }
        });
        //未分组的药品(ChargeUtils.maxGroupId)没有包含在同一个group中，需要合并groupId相同的项
        const maxGroupIdList = this.innerState.groups?.filter(
            (t) => t.groupId == ChargeUtils.maxGroupId && t.selectedMedicines?.length > 0
        );
        const newMaxGroup = new MedicineAddGroup();
        newMaxGroup.groupId = ChargeUtils.maxGroupId;
        if (maxGroupIdList.length > 0) {
            maxGroupIdList.forEach((item) => {
                item.selectedMedicines.forEach((medicine) => {
                    newMaxGroup.inputUsages.set(medicine, item?.inputUsages?.get(medicine) ?? new MedicineUsage());
                    newMaxGroup.selectedMedicines.push(medicine);
                });
            });
            this.innerState.groups = this.innerState.groups?.filter((t) => t.groupId != ChargeUtils.maxGroupId);
            this.innerState.groups.push(newMaxGroup);
        }
        this.innerState.currentFocusGroup = null;
        this.innerState.currentFocusItem = null;
        this.hasChanged = true;
        if (!this.innerState.isEditGroup) this._searchInput?.focus();
        //如果此时为多药房，并且输入框无值，则类型切换应该恢复成当前选择类型
        if (this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy) {
            this.innerState._chineseSpecType = this.innerState.chineseMedicineSpecType;
        }
        // 空中药房，代煎、颗粒需要调用算费
        if (
            group.chinesePrescriptionUsage?.pharmacyType == PharmacyType.air &&
            (group.chinesePrescriptionUsage.medicineStateScopeId == MedicineScopeId.keLi ||
                group.chinesePrescriptionUsage.medicineStateScopeId == MedicineScopeId.daiJian)
        ) {
            this._airPharmacyCalculateTrigger.next();
        }
        this.update();
    }

    async *_EventChangePageShow(event: _EventChangePageShow): AsyncGenerator<State> {
        if (this.innerState.medicineType == MedicineAddType.chinese) {
            if (event.info == "default") {
                this.innerState.medicineAutoFocus = false;
            } else {
                this.innerState.medicineAutoFocus = true;
            }
            this.update();
            return;
        }
        this.innerState.defaultSearchView = false;
        this.innerState.selectSearchView = false;
        this.innerState.selectedView = false;
        switch (event.info) {
            case "default": {
                this.innerState.medicineAutoFocus = false;
                this.innerState.defaultSearchView = true;
                break;
            }
            case "search": {
                this.innerState.medicineAutoFocus = false;
                this.innerState.selectSearchView = true;
                break;
            }
            case "selected": {
                this.innerState.selectedView = true;
            }
        }
        this.update();
    }

    // TODO 待删除
    async *_EventChangeGoodCount(event: _EventChangeGoodCount): AsyncGenerator<State> {
        const { count } = event.info;
        // @ts-ignore
        this.innerState.selectedListCount[this.innerState.selectedItemId] = count;
        this.hasChanged = true;
        this.update();
    }

    @actionEvent(_EventCopyTemplate)
    async *_mapEventCopyTemplate(event: _EventCopyTemplate): AsyncGenerator<State> {
        const tp = await OutpatientPrescriptionTemplateSearchDialog.show(false, {
            filterType: event.type,
            chineseUseFill: event.type != MedicineAddType.chinese,
        });
        if (!tp) return;
        const refreshResult = await OutpatientUtils.refreshGoodsInfo(
            [
                ...(tp.prInfo?.prescriptionWesternForms ?? []),
                ...(tp.prInfo?.prescriptionChineseForms?.filter((form) => form.pharmacyType != PharmacyType.air) ?? []),
                ...(tp.prInfo?.prescriptionInfusionForms ?? []),
                ...(tp.prInfo?.prescriptionExternalForms ?? []),
            ],
            this._searchClinicId ?? "",
            true,
            this.innerState.selectPharmacyInfo?.no
        );
        //中药的空中药房
        if (this.innerState.isChinesePrescription && this.innerState.pharmacy == PharmacyType.air) {
            await tp.prInfo?.prescriptionChineseForms
                ?.filter((form) => form.pharmacyType == PharmacyType.air)
                .forEach((form) => {
                    this._loadVendor(form).then((rsp) => {
                        if (rsp instanceof ABCError) return;
                        if (rsp) {
                            rsp.forEach((item) => {
                                if (item.checked) {
                                    const _orderItems = item.orderItems?.map((item) => JsonMapper.deserialize(PrescriptionFormItem, item));
                                    form.vendor = item;
                                    //最新供应商基础信息赋值到处方单上
                                    form.totalPrice = item.totalPrice;
                                    form.processPrice = item.processPrice;
                                    form.vendorName = item.vendorName;

                                    for (const formItem of _orderItems ?? []) {
                                        if (!formItem) continue;
                                        formItem.goodsId = formItem.productInfo?.id;
                                        formItem.productId = formItem.productInfo?.id;
                                        formItem.type = formItem.productInfo?.type;
                                        formItem.subType = formItem.productInfo?.subType;
                                        formItem.name = formItem.productInfo?.displayName ?? formItem.name;
                                        formItem.unit = formItem.unit ?? "g";
                                        formItem.medicineCadn = formItem.productInfo?.medicineCadn;
                                        formItem.productInfo = formItem.productInfo;
                                        const _formItem = _.findIndex(
                                            form.prescriptionFormItems,
                                            (i) => i.displayName == formItem.displayName
                                        );
                                        if (_.isNumber(_formItem)) {
                                            form.prescriptionFormItems![_formItem] = _.merge(
                                                form.prescriptionFormItems![_formItem],
                                                formItem
                                            );
                                        } else {
                                            form!.prescriptionFormItems?.push(formItem);
                                        }
                                    }
                                    this._modifyChinesePrescriptionUsage(form);
                                }
                            });
                            this.update();
                        }
                    });
                });
        }
        if (!(refreshResult instanceof Array)) {
            return await Toast.show(`添加失败 ${errorToStr(refreshResult)}`, {
                warning: true,
            });
        }

        const addGroups: MedicineAddGroup[] = [];
        if (event.type == MedicineAddType.western) {
            let _addType = MedicineTemplateAddType.reset;
            const oldGroup = !_.isEmpty(this.innerState.groups) ? this.innerState.groups[0] : new MedicineAddGroup();
            if (!!oldGroup.selectedMedicines.length) {
                _addType = await MedicineTemplateAddTypeDialog.show({
                    title: tp.name,
                    content: "确认覆盖当前处方",
                    buttonType: [
                        { name: "确定", value: MedicineTemplateAddType.reset },
                        { name: "取消", value: MedicineTemplateAddType.cancel },
                    ],
                });
            }
            if (!_addType) return;

            if (!_.isEmpty(refreshResult)) {
                for (const form of refreshResult ?? []) {
                    //将复制模板数据组装成页面需要的格式
                    this.innerState.psychotropicNarcoticType = form.psychotropicNarcoticType;

                    const groups = ChargeUtils.splitToGroupByGroupId<PrescriptionFormItem>(form.prescriptionFormItems);
                    groups?.forEach((item, key) => {
                        const selectedMedicines: GoodsInfo[] = [];
                        const inputUsages = new AbcMap<GoodsInfo, MedicineUsage>();
                        const group = JsonMapper.deserialize(MedicineAddGroup, {
                            selectedMedicines,
                            inputUsages,
                            groupId: Number(key),
                        });
                        addGroups.push(group);

                        const formItems = item;
                        formItems?.forEach((formItem) => {
                            // const isExist = selectedMedicines?.find(
                            //     (t) => t.id == formItem.goodsInfo.id && t.displayName == formItem.goodsInfo.displayName
                            // );
                            // if (!isExist) {
                            selectedMedicines.push(formItem.goodsInfo);
                            const _inputUsage = MedicineUsage.fromPrescriptionFormItem(formItem);
                            // if (formItems.length > 1) {
                            //     const _firstMedicineUsages = inputUsages.get(group.selectedMedicines[0]);
                            //     _inputUsage.freq = _firstMedicineUsages?.freq ?? _inputUsage.freq;
                            //     _inputUsage.usage = _firstMedicineUsages?.usage ?? _inputUsage.usage;
                            // }
                            _inputUsage.computeUnitCount({ goodsInfo: formItem.goodsInfo, usage: _inputUsage });
                            //同一组别相互影响，不同组别相互无影响
                            // inputUsages.set(formItem.goodsInfo, formItems.length > 1 ? _inputUsage : _.cloneDeep(_inputUsage));
                            inputUsages.set(formItem.goodsInfo, _.cloneDeep(_inputUsage));
                            // }
                        });
                    });
                }
            }
        } else if (event.type == MedicineAddType.chinese) {
            // 中药处方模板复制规则：
            // 无药，以模板为准：
            // 有药，添加以原有药房为准；覆盖以模板为准
            let _addType = MedicineTemplateAddType.reset;
            const oldGroup = !_.isEmpty(this.innerState.groups) ? this.innerState.groups[0] : new MedicineAddGroup();
            oldGroup.selectedMedicines.forEach((item) => {
                item.cMSpec = !!item.cMSpec ? item.cMSpec : ChineseMedicineSpecType.fullNames()[this.innerState.chineseMedicineSpecType];
            });
            if (!!oldGroup.selectedMedicines.length) {
                _addType = await MedicineTemplateAddTypeDialog.show({
                    title: tp.name ?? "调用模板",
                    content: "作为汤头添加到当前处方，还是覆盖当前处方",
                    buttonType: [
                        { name: "添加", value: MedicineTemplateAddType.push },
                        { name: "覆盖", value: MedicineTemplateAddType.reset },
                    ],
                });
            }
            if (!_addType) return;

            let medicines: Array<GoodsInfo> = [];
            let usages = new AbcMap<GoodsInfo, MedicineUsage>();

            //是否采取当前药房类型（true:采用当前药房类型，false:采用导入模板药房类型）
            //经典方剂和临床验方的pharmacyType没有类型，所以采用当前本地的类型
            const isUseLocalType =
                (!!oldGroup.selectedMedicines?.length && _addType == MedicineTemplateAddType.push) ||
                tp.category == PrescriptionTemplateCategory.classical ||
                tp.category == PrescriptionTemplateCategory.clinical;

            //模板处方药品类型
            const type = !!refreshResult?.[0]?.specification
                ? refreshResult?.[0]?.specification == "中药饮片"
                    ? 0
                    : 1
                : this.innerState.chineseMedicineSpecType;
            if (_addType == MedicineTemplateAddType.push) {
                //追加的时候需要与本地药品的类型比对,如果不同，先进行类型转换(以当前存在类型为准，即将模板类型转换为当前显示的类型),必须先转换本地药品类型，然后再追加模板药品
                await this._trans(
                    new _EventChineseMedicineSpecType(
                        this.innerState.chineseMedicineSpecType,
                        this.innerState.pharmacy != PharmacyType.normal || this.innerState.unableChinesePrescriptionSupportMix,
                        this.innerState.pharmacy
                    )
                );

                medicines = oldGroup.selectedMedicines ?? [];
                usages = oldGroup.inputUsages;
            } else {
                //覆盖的时候也需要对比当前类型（饮片、颗粒）,如果不同，则需要修改药品类型
                this.innerState.chineseMedicineSpecType = type;
            }

            if (!_.isEmpty(refreshResult)) {
                for (const form of refreshResult) {
                    if (!_.isEmpty(form.prescriptionFormItems)) {
                        for (const formItem of form.prescriptionFormItems!) {
                            formItem.cMSpec = !!formItem.cMSpec ? formItem.cMSpec : refreshResult[0].specification;
                            const medicine = formItem.goodsInfo;
                            medicine.keyId = medicine.keyId ?? medicine.compareKey();
                            const isExist = medicines.find((t) =>
                                !!medicine.id && t.id
                                    ? t.id == medicine.id && t.displayName == medicine.displayName
                                    : t.displayName == medicine.displayName
                            );
                            if (!isExist) {
                                //避免药品没有cMSpec,进行类型转换时误判
                                medicine.cMSpec = !!medicine.cMSpec ? medicine.cMSpec : formItem.cMSpec;
                                medicines.push(medicine);
                                usages.set(medicine, MedicineUsage.fromPrescriptionFormItem(formItem));
                            }
                        }
                    }
                }
                // 判断当前类型，如果为本地药房，则不需要添加vendor，如果不是本地药房则需要
                const isAddVendor = (isUseLocalType ? this.innerState.pharmacy : refreshResult?.[0]?.pharmacyType) == PharmacyType.air;
                const chinesePrescriptionUsage = ChinesePrescriptionUsage.fromPrescriptionForm(refreshResult?.[0], isAddVendor);
                chinesePrescriptionUsage?.fillPrescriptionChineseForm(refreshResult?.[0]);

                const group: MedicineAddGroup = JsonMapper.deserialize(MedicineAddGroup, {
                    selectedMedicines: medicines,
                    inputUsages: usages,
                    chinesePrescriptionUsage: isUseLocalType
                        ? this.innerState.groups[0].chinesePrescriptionUsage
                        : chinesePrescriptionUsage,
                    vendor: isUseLocalType ? this.innerState.groups?.[0]?.vendor : isAddVendor ? refreshResult?.[0]?.vendorInfo : undefined,
                });
                addGroups.push(group);
            }
        } else if (event.type == MedicineAddType.infusion) {
            let _addType = MedicineTemplateAddType.reset;
            const oldGroup = !_.isEmpty(this.innerState.groups) ? this.innerState.groups[0] : new MedicineAddGroup();
            if (!!oldGroup.selectedMedicines.length) {
                _addType = await MedicineTemplateAddTypeDialog.show({
                    title: tp.name,
                    content: "确认覆盖当前处方",
                    buttonType: [
                        { name: "确定", value: MedicineTemplateAddType.reset },
                        { name: "取消", value: MedicineTemplateAddType.cancel },
                    ],
                });
            }
            if (!_addType) return;

            //收集药品信息和用法信息
            if (!_.isEmpty(refreshResult)) {
                for (const form of refreshResult ?? []) {
                    this.innerState.psychotropicNarcoticType = form.psychotropicNarcoticType;

                    const groups = ChargeUtils.splitToGroupByGroupId<PrescriptionFormItem>(form.prescriptionFormItems);
                    groups.forEach((item /*, key*/) => {
                        const selectedMedicines: GoodsInfo[] = [];
                        // selectedMedicines = medicines;
                        const inputUsages = new AbcMap<GoodsInfo, MedicineUsage>();
                        const group = JsonMapper.deserialize(MedicineAddGroup, {
                            selectedMedicines,
                            inputUsages,
                            infusionPrescriptionUsage: InfusionPrescriptionUsage.fromPrescriptionFormItem(item[0]),
                        });
                        addGroups.push(group);

                        const formItems = item;
                        formItems.forEach((formItem /*, index*/) => {
                            const isExist = selectedMedicines?.find(
                                (t) => t.id == formItem.goodsInfo.id && t.displayName == formItem.goodsInfo.displayName
                            );
                            if (!isExist) {
                                selectedMedicines.push(
                                    GoodsUtils.changeGoodsUnitPriceWithUnit(formItem.goodsInfo, {
                                        unitPrice: formItem.unitPrice,
                                        unit: formItem.unit,
                                    })
                                );
                                const _inputUsage = MedicineUsage.fromPrescriptionFormItem(formItem);
                                // _inputUsage.computeUnitCount({ goodsInfo: formItem.goodsInfo });
                                inputUsages.set(formItem.goodsInfo, _inputUsage);
                            }
                        });
                    });
                }
            }
        }
        this.innerState.groups = addGroups;
        const pharmacyType =
            (!_.isUndefined(this.innerState.groups?.[0]?.vendor?.pharmacyType)
                ? this.innerState.groups?.[0]?.vendor?.pharmacyType
                : this.innerState.chinesePrescriptionUsage?.pharmacyType) ?? 0;
        if (this.innerState.isChinesePrescription) {
            //所有药品拼接进groups中后，需要将当前所有药品，转换为当前指定的类型
            await this._trans(new _EventChineseMedicineSpecType(this.innerState.chineseMedicineSpecType, true, pharmacyType));
            //空中药房
            if (pharmacyType == PharmacyType.air) {
                this._loadVendorList.next();
            }
            if (pharmacyType == PharmacyType.virtual) {
                this._loadVirtualVendorList.next();
            }
        }
        // 需要调用一次算费
        if (!pharmacyType) {
            const type = (() => {
                if (event.type == MedicineAddType.western) return "western";
                else if (event.type == MedicineAddType.chinese) return "chinese";
                else if (event.type == MedicineAddType.infusion) return "infusion";
                return "";
            })();
            if (!!type) {
                this.innerState.groups?.[0].selectedMedicines?.forEach((medicine) => {
                    this._clearAdjustmentFee(medicine, type, true);
                });
            }
        }
        debugger;
        this.update();
    }

    async *_EventChineseMedicineSpecType(event: _EventChineseMedicineSpecType): AsyncGenerator<State> {
        await this._trans(event);

        if (this.innerState.keyword && this.innerState.keyword !== "") {
            this._loadDataTrigger.next(0);
        }
        this._searchInput?.focus();
        this.update();
    }

    async *_mapEventWesternUsageTap(event: _EventWesternUsageTap): AsyncGenerator<State> {
        const group = event.group;
        const inputUsage = group.inputUsages.get(event.medicine);
        this.innerState.medicineAutoFocus = false;
        const usage = await MedicineUsagePicker.chooseWesternMedicineUsage(inputUsage?.usage);
        if (usage != null) {
            if (group.groupId == ChargeUtils.maxGroupId) {
                const inputUsage = group.inputUsages.get(event.medicine);
                inputUsage!.usage = usage;
                inputUsage?.computeUnitCount({
                    goodsInfo: event.medicine,
                });
            } else {
                group.inputUsages.forEach((item) => {
                    item.usage = usage;
                });
            }
            this.update();
        }
        this.hasChanged = true;
        this.innerState.medicineAutoFocus = true;
    }

    async *_mapEventUpdateTreatmentAmount(event: _EventUpdateTreatmentAmount): AsyncGenerator<State> {
        this.innerState.groups[0].inputUsages.get(event.medicine)!.unitCount = event.count;
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventSelectMedicineUnit(event: _EventSelectMedicineUnit): AsyncGenerator<State> {
        const usage = this.innerState.groups[0].inputUsages!.get(event.medicine);
        const units: string[] = event.medicine.sellUnits;
        const selects = await showOptionsBottomSheet({
            title: "选择单位",
            options: units,
            initialSelectIndexes: usage?.unit ? new Set([units.indexOf(usage.unit)]) : undefined,
        });
        if (!_.isEmpty(selects)) {
            // @ts-ignore
            usage.unit = units[selects[0]];
        }
        this.hasChanged = true;
        this.update();
    }

    async *_EventAddMedicineGroup(/*event: _EventAddMedicineGroup*/): AsyncGenerator<State> {
        AbcTextInput.focusInput?.blur();
        this.innerState.groups.push(new MedicineAddGroup());
        this.innerState.groups.map((group) => {
            this._initMedicineGroup(group);
        });
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventFocusItem(event: _EventFocusItem): AsyncGenerator<State> {
        this.innerState.currentFocusGroup = event.group;
        this.innerState.currentFocusItem = event.medicine;
        this.update();
    }

    async *_mapEventWesternFreqTap(event: _EventWesternFreqTap): AsyncGenerator<State> {
        const group = event.group;
        let inputUsage = group.inputUsages.get(event.medicine);
        this.innerState.medicineAutoFocus = false;
        const freq = await MedicineUsagePicker.chooseWesternMedicineFreq(inputUsage?.freq);
        if (freq != null) {
            if (group.groupId == ChargeUtils.maxGroupId) {
                if (inputUsage == null) {
                    inputUsage = new MedicineUsage();
                    group.inputUsages.set(event.medicine, inputUsage);
                }

                inputUsage.freq = freq;
                inputUsage.computeUnitCount({
                    goodsInfo: event.medicine,
                    usage: inputUsage,
                });
                group.inputUsages.set(event.medicine, inputUsage);
            } else {
                group.inputUsages.forEach((inputUsage, medicine) => {
                    inputUsage.freq = freq;
                    inputUsage.computeUnitCount({
                        goodsInfo: medicine,
                        usage: inputUsage,
                    });
                });
            }
            this.update();
        }
        this.innerState.medicineAutoFocus = true;
        this.hasChanged = true;
    }

    async *_mapEventUpdateDosageUnit(event: _EventUpdateDosageUnit): AsyncGenerator<State> {
        const group = event.group ?? this.innerState.groups[0];
        let inputUsage = group.inputUsages.get(event.medicine);
        if (inputUsage == null) {
            inputUsage = new MedicineUsage();
            group.inputUsages.set(event.medicine, inputUsage);
        }

        inputUsage.dosage = inputUsage.dosage ?? new MedicineDosageUsage();
        inputUsage.dosage.dosageUnit = event.unit;
        inputUsage.computeUnitCount({ goodsInfo: event.medicine });
        this._clearAdjustmentFee(event.medicine, "western");
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventUpdateWesternDosageCount(event: _EventUpdateWesternDosageCount): AsyncGenerator<State> {
        const group = event.group;
        let inputUsage = group.inputUsages.get(event.medicine);
        if (inputUsage == null) {
            inputUsage = new MedicineUsage();
            group.inputUsages.set(event.medicine, inputUsage);
        }
        inputUsage!.dosage = inputUsage?.dosage ?? new MedicineDosageUsage();
        inputUsage!.dosage.count = event.dosageCount ? event.dosageCount : undefined;
        const freq = this.innerState.westernMedicineConfig?.freq?.find((item) => item.en == inputUsage!.freq?.en);
        await inputUsage.computeUnitCount({
            goodsInfo: event.medicine,
            usage: inputUsage,
            freq: freq,
        });
        this.hasChanged = true;
        this._clearAdjustmentFee(event.medicine, "western");
        this.update();
    }

    async *_mapEventUpdateWesternMedicineDays(event: _EventUpdateWesternMedicineDays): AsyncGenerator<State> {
        const group = event.group;
        if (group.groupId == ChargeUtils.maxGroupId) {
            let inputUsage = group.inputUsages.get(event.medicine);
            if (inputUsage == null) {
                inputUsage = new MedicineUsage();
                group.inputUsages.set(event.medicine, inputUsage);
            }
            inputUsage.days = event.days ? event.days : undefined;
            const freq = this.innerState.westernMedicineConfig?.freq?.find((item) => item.en == inputUsage?.freq?.en);
            await inputUsage.computeUnitCount({
                goodsInfo: event.medicine,
                usage: inputUsage,
                freq: freq,
            });
            this._clearAdjustmentFee(event.medicine, "western");
        } else {
            group.inputUsages.forEach((inputUsage, medicine) => {
                inputUsage.days = event.days ? event.days : undefined;
                const freq = this.innerState.westernMedicineConfig?.freq?.find((item) => item.en == inputUsage?.freq?.en);
                inputUsage
                    .computeUnitCount({
                        goodsInfo: medicine,
                        usage: inputUsage,
                        freq: freq,
                    })
                    .then(() => {
                        this._clearAdjustmentFee(event.medicine, "western");
                    });
            });
        }
        this.hasChanged = true;
        this.update();
    }

    @actionEvent(_EventUpdateWesternMedicineUsage)
    async *_mapEventUpdateWesternMedicineUsage(event: _EventUpdateWesternMedicineUsage): AsyncGenerator<State> {
        const group = event.group;
        if (group.groupId == ChargeUtils.maxGroupId) {
            let inputUsage = group.inputUsages.get(event.goodsInfo);
            if (inputUsage == null) {
                inputUsage = new MedicineUsage();
                group.inputUsages.set(event.goodsInfo, inputUsage);
            }
            inputUsage.usage = inputUsage.usage ?? new Usage();
            inputUsage.usage!.name = event.usage;
            const freq = this.innerState.westernMedicineConfig?.freq?.find((item) => item.en == inputUsage?.freq?.en);
            await inputUsage.computeUnitCount({
                goodsInfo: event.goodsInfo,
                usage: inputUsage,
                freq: freq,
            });
            this._clearAdjustmentFee(event.goodsInfo, "western");
        } else {
            group.inputUsages.forEach((inputUsage, medicine) => {
                inputUsage.usage!.name = event.usage;
                const freq = this.innerState.westernMedicineConfig?.freq?.find((item) => item.en == inputUsage?.freq?.en);
                inputUsage
                    .computeUnitCount({
                        goodsInfo: medicine,
                        usage: inputUsage,
                        freq: freq,
                    })
                    .then(() => {
                        this._clearAdjustmentFee(event.goodsInfo, "western");
                    });
            });
        }
        this.hasChanged = true;
        this.update();
    }

    @actionEvent(_EventUpdateWesternMedicineFreq)
    async *_mapEventUpdateWesternMedicineFreq(event: _EventUpdateWesternMedicineFreq): AsyncGenerator<State> {
        debugger;
        const group = event.group;
        if (group.groupId == ChargeUtils.maxGroupId) {
            let inputUsage = group.inputUsages.get(event.goodsInfo);
            if (inputUsage == null) {
                inputUsage = new MedicineUsage();
                group.inputUsages.set(event.goodsInfo, inputUsage);
            }
            let freq = this.innerState.westernMedicineConfig?.freq?.find((item) => item.en == event.freq);
            if (!freq) {
                const _freq = this.innerState.westernMedicineConfig?.freq
                    ?.filter((item) => item.isCustom)
                    .find((item) => StringUtils.checkEndChar(item.en ?? "", event.freq ?? ""));
                if (!!_freq) {
                    const time = Number(StringUtils.removeHeadAndTail(event.freq ?? "") ?? 0);
                    freq = { ..._freq, time: time * (_freq.hMultiple ?? 0) };
                }
            }
            inputUsage.freq!.en = event.freq;
            inputUsage.freq!.name = freq?.name; //必须重新赋值，不然选中状态会有问题
            await inputUsage.computeUnitCount({
                goodsInfo: event.goodsInfo,
                usage: inputUsage,
                freq: freq,
            });
            this._clearAdjustmentFee(event.goodsInfo, "western");
        } else {
            group.inputUsages.forEach((inputUsage, medicine) => {
                let freq = this.innerState.westernMedicineConfig?.freq?.find((item) => item.en == event.freq);

                if (!freq) {
                    const _freq = this.innerState.westernMedicineConfig?.freq
                        ?.filter((item) => item.isCustom)
                        .find((item) => StringUtils.checkEndChar(item.en ?? "", event.freq ?? ""));
                    if (!!_freq) {
                        const time = Number(StringUtils.removeHeadAndTail(event.freq ?? "") ?? 0);
                        freq = { ..._freq, time: time * (_freq.hMultiple ?? 0) };
                    }
                }

                inputUsage.freq!.en = event.freq;
                inputUsage.freq!.name = freq?.name; //必须重新赋值，不然选中状态会有问题
                inputUsage
                    .computeUnitCount({
                        goodsInfo: medicine,
                        usage: inputUsage,
                        freq: freq,
                    })
                    .then(() => {
                        this._clearAdjustmentFee(event.goodsInfo, "western");
                    });
            });
        }
        this.hasChanged = true;
        this.update();
    }

    @actionEvent(_EventUpdateWesternRemark)
    async *_mapEventUpdateWesternRemark(event: _EventUpdateWesternRemark): AsyncGenerator<State> {
        const usage = event.group?.inputUsages.get(event.goodsInfo) ?? new MedicineUsage();
        if (!!event.remark) {
            usage.specialRequirement!.name = event.remark;
            this.hasChanged = true;
            this.update();
        }
    }

    @actionEvent(_EventDeleteWesternRemark)
    async *_mapEventDeleteWesternRemark(event: _EventDeleteWesternRemark): AsyncGenerator<State> {
        const usage = event.group?.inputUsages.get(event.goodsInfo) ?? new MedicineUsage();
        usage.specialRequirement = new ChineseUsageItemInfo();

        this.hasChanged = true;
        this.update();
    }

    @actionEvent(_EventDeletePharmacyInfo)
    async *_mapEventDeletePharmacyInfo(event: _EventDeletePharmacyInfo): AsyncGenerator<State> {
        const usage = event.group?.inputUsages.get(event.goodsInfo) ?? new MedicineUsage();
        // 取消自备
        usage.selfProvidedStatus = DispensingFormItemsSourceItemType.default;
        //删除药房---恢复成默认药房设置
        usage.pharmacyInfo = this.innerState.pharmacyInfoConfig?.getDefaultPharmacy({
            departmentId: this._departmentId,
            goodsInfo: { typeId: event.goodsInfo?.typeId },
        });
        //修改库存信息
        const currentPharmacyStockInfo = event.goodsInfo?.pharmacyGoodsStockList?.find((item) => item.pharmacyNo == usage.pharmacyInfo?.no);
        const { stockPackageCount = 0, stockPieceCount = 0 } = currentPharmacyStockInfo ?? {};
        event.goodsInfo.stockPackageCount = stockPackageCount;
        event.goodsInfo.stockPieceCount = stockPieceCount;

        this.hasChanged = true;
        this.update();
    }

    @actionEvent(_EventUpdateWesternMedicineAst)
    async *_mapEventUpdateWesternMedicineAst(event: _EventUpdateWesternMedicineAst): AsyncGenerator<State> {
        const usage = event.group?.inputUsages.get(event.goodsInfo) ?? new MedicineUsage();
        if (!!event.ast) {
            usage.ast = event.ast;
            this.hasChanged = true;
            this.update();
        }
    }

    async *_mapEventUpdateWesternAmountUnit(event: _EventUpdateWesternAmountUnit): AsyncGenerator<State> {
        const usage = event.group.inputUsages.get(event.medicine);
        usage!.unit = event.unit;
        this.hasChanged = true;
        this._clearAdjustmentFee(event.medicine, "western");
        this.update();
    }

    async *_mapEventUpdateWesternAmountUnitCount(event: _EventUpdateWesternAmountUnitCount): AsyncGenerator<State> {
        const usage = event.group.inputUsages.get(event.medicine) ?? new MedicineUsage();
        usage!.unitCount = _.isNumber(event.unitCount) ? event.unitCount : undefined;
        this.hasChanged = true;
        this._clearAdjustmentFee(event.medicine, "western");
        this.update();
    }

    async *_mapEventSelectChineseMedicineBoilMethod(event: _EventSelectChineseMedicineBoilMethod): AsyncGenerator<State> {
        const medicineUsage = this.innerState.groups[0].inputUsages.get(event.medicine);
        const { specialRequirement } = ChineseMedicine;
        const initIndex = specialRequirement.findIndex((i) => i.name == medicineUsage?.specialRequirement?.name);
        const selectIndex = await AbcDialog.showOptionsBottomSheet({
            title: "煎法",
            options: specialRequirement?.map((item) => item.name),
            initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
            crossAxisCount: 3,
            height: pxToDp(500),
        });
        if (!selectIndex || !selectIndex.length) return;
        medicineUsage!.specialRequirement = specialRequirement[selectIndex[0]];
        if (medicineUsage!.specialRequirement!.name == "【自备】") {
            await Toast.show(`备注为【自备】，该药品将不会纳入划价收费`, { warning: true });
            medicineUsage!.selfProvidedStatus = DispensingFormItemsSourceItemType.noCharge;
        } else {
            medicineUsage!.selfProvidedStatus = DispensingFormItemsSourceItemType.default;
        }
        this.hasChanged = true;
        this.update();
        // const selects = await MedicineUsagePicker.chooseChineseSpecialRequirement(medicineUsage?.specialRequirement);
        // if (selects && selects != null) {
        //     medicineUsage!.specialRequirement = selects;
        //     this.hasChanged = true;
        //     this.update();
        // }
    }

    @actionEvent(_EventDeleteChineseMedicineBoilMethod)
    async *_mapEventDeleteChineseMedicineBoilMethod(event: _EventDeleteChineseMedicineBoilMethod): AsyncGenerator<State> {
        const medicineUsage = this.innerState.groups[0].inputUsages.get(event.medicine) ?? new MedicineUsage();
        medicineUsage!.specialRequirement = new ChineseUsageItemInfo();
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventUpdateChineseMedicineAmount(event: _EventUpdateChineseMedicineAmount): AsyncGenerator<State> {
        this.innerState.currentFocusItem = event.medicine;
        const usage = this.innerState.groups[0].inputUsages.get(event.medicine) ?? new MedicineUsage();
        usage!.unitCount = _.isNumber(event.unitCount) ? event.unitCount : undefined;
        this.hasChanged = true;
        // 空中药房-代煎、颗粒，需要调用空中药房算费
        const pharmacyType = !_.isUndefined(this.innerState.groups?.[0]?.vendor?.pharmacyType)
            ? this.innerState.groups?.[0]?.vendor?.pharmacyType
            : this.innerState.groups[0].chinesePrescriptionUsage?.pharmacyType;
        const chinesePrescriptionUsage = this.innerState.groups?.[0]?.chinesePrescriptionUsage ?? new ChinesePrescriptionUsage();
        const { medicineStateScopeId } = chinesePrescriptionUsage;
        if (pharmacyType == PharmacyType.air) {
            if (medicineStateScopeId == MedicineScopeId.daiJian || medicineStateScopeId == MedicineScopeId.keLi) {
                this._airPharmacyCalculateTrigger.next();
            }
        }
        this._clearAdjustmentFee(event.medicine, "chinese");
        this.update();
    }

    private computedAirPharmacyUSagDays(): void {
        const chinesePrescriptionUsage = this.innerState.groups[0].chinesePrescriptionUsage! ?? new ChinesePrescriptionUsage();
        const { pharmacyType, medicineStateScopeId, usageScopeId } = chinesePrescriptionUsage;
        // 空中药房不同的 制法会对应不同的服用量
        const UsageInfoMap = ChineseMedicineUsageInfo.GetChineseUsageParam;
        const value = UsageInfoMap(medicineStateScopeId) || UsageInfoMap(usageScopeId);
        let { usageDays } = chinesePrescriptionUsage;
        usageDays = usageDays ?? JsonMapper.deserialize(ChineseUsageItemInfo, { name: "" });
        if (pharmacyType == PharmacyType.air && value) {
            const copyGroup = _.cloneDeep(this.innerState.groups);
            const changeChineseFormItems = OutpatientUtils.createPrescriptionChineseForm(
                JsonMapper.deserialize(MedicineUsageInput, {
                    medicines: copyGroup,
                })
            );
            if (changeChineseFormItems) {
                this._modifyChinesePrescriptionUsage(changeChineseFormItems);
                usageDays.name = changeChineseFormItems.usageDays ?? "";
            }
        }
    }

    async *_mapEventUpdateChinesePrescriptionDosageAmount(event: _EventUpdateChinesePrescriptionDosageAmount): AsyncGenerator<State> {
        this.innerState.chinesePrescriptionUsage!.count = event.dosageCount;
        if (
            !!this.innerState.chinesePrescriptionUsage?.pharmacyType &&
            this.innerState.chineseMedicineSpecType === ChineseMedicineSpecType.chinesePiece
        ) {
            if (this.innerState.chinesePrescriptionUsage?.pharmacyType == PharmacyType.air) {
                this._loadVendorList.next();
            } else if (this.innerState.chinesePrescriptionUsage?.pharmacyType == PharmacyType.virtual) {
                this._loadVirtualVendorList.next();
            }
        }
        this.hasChanged = true;
        this.computedTisanesSack(true).then(() => {
            this.innerState.groups?.[0]?.selectedMedicines?.forEach((medicine) => {
                this._clearAdjustmentFee(medicine, "chinese");
            });
        });
        this.update();
    }

    async *_mapEventSelectChinesePrescriptionUsage(event: _EventSelectChinesePrescriptionUsage): AsyncGenerator<State> {
        const chinesePrescriptionUsage = this.innerState.groups[0].chinesePrescriptionUsage! ?? new ChinesePrescriptionUsage();
        const { usageScopeId, medicineStateScopeId, pharmacyType, freq, usageLevel, usage, dailyDosage } = chinesePrescriptionUsage;
        let { usageDays } = chinesePrescriptionUsage;
        usageDays = usageDays ?? JsonMapper.deserialize(ChineseUsageItemInfo, { name: "" });
        usage!.name = event.usage ?? "";
        // 空中药房不同的 制法会对应不同的服用量
        const UsageInfoMap = ChineseMedicineUsageInfo.GetChineseUsageParam;
        const value = UsageInfoMap(medicineStateScopeId) || UsageInfoMap(usageScopeId);

        if (pharmacyType == PharmacyType.air && value) {
            const { defaultFreq, defaultUsageLevel } = value || {};
            freq!.name = defaultFreq ?? "";
            usageLevel!.name = defaultUsageLevel ?? "";
            const copyGroup = _.cloneDeep(this.innerState.groups);
            const changeChineseFormItems = OutpatientUtils.createPrescriptionChineseForm(
                JsonMapper.deserialize(MedicineUsageInput, {
                    medicines: copyGroup,
                })
            );
            if (changeChineseFormItems) {
                this._modifyChinesePrescriptionUsage(changeChineseFormItems);
                usageDays.name = changeChineseFormItems.usageDays ?? "";
            }
        } else {
            const chinesePRWithUsage = ChineseMedicineConfigProvider.getChinesePRWithUsage(event.usage) || {};
            dailyDosage!.name = chinesePRWithUsage.dailyDosage ?? "";
            freq!.name = chinesePRWithUsage.freq ?? "";
            usageLevel!.name = chinesePRWithUsage.usageLevel ?? "";
            usageDays!.name = chinesePRWithUsage.usageDays ?? "";
        }
        if (pharmacyType == PharmacyType.air) {
            if (!this.setAirPharmacyUsage("usage", usage!.name)) usage!.name = "";
            if (!this.setAirPharmacyUsage("usageLevel", usageLevel!.name)) usageLevel!.name = "";
            if (!this.setAirPharmacyUsage("dosage", dailyDosage!.name)) dailyDosage!.name = "";
            if (!this.setAirPharmacyUsage("freq", freq!.name)) freq!.name = "";
        }
        if (this.isSpecialUsages()) {
            this.innerState.groups[0].chinesePrescriptionUsage!.dailyDosage!.name = "";
        } else {
            this.innerState.groups[0].chinesePrescriptionUsage!.usageDays!.name = "";
        }
        this.hasChanged = true;
        this.update();
    }
    // 特殊的用法，需要增加服用天数字段，隐藏剂量字段
    isSpecialUsages(): boolean {
        const chinesePrescriptionUsage = this.innerState.groups[0].chinesePrescriptionUsage!;
        const { usage } = chinesePrescriptionUsage;
        const _arr = ["制膏", "制丸", "打粉"];
        return _arr.indexOf(usage?.name ?? "") > -1 ?? false;
    }

    /**
     * 空中药房--当前用法用量是否与配置匹配
     * @param field
     * @param fieldName
     */
    setAirPharmacyUsage(field: string, fieldName: string): boolean {
        const vendorInfo = this.innerState.groups?.[0]?.vendor,
            businessScopeConfig = vendorInfo?.businessScopeConfig;
        if (!vendorInfo || !businessScopeConfig) return false;
        switch (field) {
            case "usage":
                return !!businessScopeConfig?.usageOptions?.find((t) => t.name == fieldName);
            case "usageLevel":
                return !!businessScopeConfig?.usageLevelOptions?.find((t) => t.name == fieldName);
            case "dosage":
                return !!businessScopeConfig?.dosageOptions?.find((t) => t.name == fieldName);
            case "freq":
                return !!businessScopeConfig?.freqOptions?.find((t) => t.name == fieldName);
            default:
                return false;
        }
    }

    /**
     * 计算中药饮片加工袋数
     * 用法用量：x日y剂，1日z次
     * 每剂煎药袋数 =(z * x) / y  若计算结果为小数，保留小数，但总袋数向上取整
     * @params  canCallAirCalculate ---是否调用空中药房算费,只针对颗粒来说，因为颗粒调用的次数要少点（代煎-药品克数、剂数、每日剂量、频率、服用量,加工袋数,总袋数均会调用算费；颗粒--药品克数、剂数,加工）
     */
    async computedTisanesSack(canCallAirCalculate?: boolean): Promise<void> {
        const chinesePrescriptionUsage = this.innerState.groups[0].chinesePrescriptionUsage! ?? new ChinesePrescriptionUsage();
        const { usageScopeId, medicineStateScopeId, freq, usageLevel, dailyDosage, count } = chinesePrescriptionUsage;
        const pharmacyType = !_.isUndefined(this.innerState.groups?.[0]?.vendor?.pharmacyType)
            ? this.innerState.groups?.[0]?.vendor?.pharmacyType
            : this.innerState.groups[0].chinesePrescriptionUsage!.pharmacyType;
        const freqName = freq?.name,
            dailyName = dailyDosage?.name,
            usageLevelName = usageLevel?.name;
        let usageLevelList: ChineseUsageItemInfo[] = [];
        const z =
            !!freqName && ChineseMedicine.freq.map((t) => t.name).includes(freqName) && freqName.indexOf("1日") > -1
                ? Number(freqName.substr(2, 1))
                : 0;
        const selDailyDosage = ChineseMedicine.dailyDosage.find((t) => t.name == dailyName);
        const x = !!selDailyDosage ? selDailyDosage?.daysCount : 0;
        const y = !!selDailyDosage ? selDailyDosage?.dosageCount : 0;
        //本地药房饮片--加工通过process
        //空中药房饮片（代煎）--加工通过chinesePrescriptionUsage用法

        // 空中药房不同的 制法会对应不同的服用量
        const UsageInfoMap = ChineseMedicineUsageInfo.GetChineseUsageParam;
        const value = UsageInfoMap(medicineStateScopeId) || UsageInfoMap(usageScopeId);
        let w = 0;
        if (value && value.usageLevel) {
            usageLevelList = value.usageLevel;
            w = usageLevelList?.find((t) => t.name == usageLevelName)?.value ?? 0;
        }
        const keliSingleBags = ((z * x) / y) * w;

        const result = await ChargeAgent.processBagCountCalculate({
            dailyDosage: dailyDosage?.name,
            doseCount: count,
            freq: freq?.name,
            pharmacyType: pharmacyType,
            type: chinesePrescriptionUsage.specificationType == ChineseMedicineSpecType.chinesePiece ? 1 : 2,
            usageLevel: usageLevel?.name,
        }).catchIgnore();
        if (!result) return;
        if (!pharmacyType) {
            this.innerState.process!.processBagUnitCount = result?.bagUnitCount;
            this.innerState.process!.totalProcessCount = result?.bagTotalCount;
        } else {
            if (medicineStateScopeId == MedicineScopeId.daiJian) {
                this.innerState.groups[0].chinesePrescriptionUsage!.processBagUnitCount = result?.bagUnitCount;
                this.innerState.groups[0].chinesePrescriptionUsage!.totalProcessCount = result?.bagTotalCount;
            }
            if (medicineStateScopeId == MedicineScopeId.keLi) {
                //袋数只能为1，2，3，4，6；如果计算为5，向下取整成4，超出6，则填6
                this.innerState.groups[0].chinesePrescriptionUsage!.processBagUnitCount = !!y
                    ? Math.ceil(keliSingleBags) > 4 && Math.ceil(keliSingleBags) < 6
                        ? 4
                        : Math.ceil(keliSingleBags) > 6
                        ? 6
                        : Math.ceil(keliSingleBags)
                    : undefined;
            }
            if (
                pharmacyType == PharmacyType.air &&
                ((canCallAirCalculate && medicineStateScopeId == MedicineScopeId.keLi) || medicineStateScopeId == MedicineScopeId.daiJian)
            ) {
                this._airPharmacyCalculateTrigger.next();
            }
        }

        const processName = this.innerState.process?.processName;
        const totalProcessCount = this.innerState.process!.totalProcessCount,
            processBagUnitCount = this.innerState.process!.processBagUnitCount;
        if (processName && processName?.indexOf("煎药") > -1) {
            const usageStr = `${!!processBagUnitCount ? "（1剂煎" + processBagUnitCount + "袋" : ""}${
                !!totalProcessCount ? "，共" + totalProcessCount + "袋）" : !!processBagUnitCount ? "）" : ""
            }`;
            this.innerState.process!.processName = `煎药${usageStr}`;
        }
        this.update();
    }

    async *_mapEventSelectChinesePrescriptionDailyDosage(event: _EventSelectChinesePrescriptionDailyDosage): AsyncGenerator<State> {
        this.innerState.groups[0].chinesePrescriptionUsage!.dailyDosage!.name = event.dailyDosage ?? "";
        this.hasChanged = true;
        this.computedTisanesSack(false);
        this.update();
    }

    async *_mapEventSelectChinesePrescriptionFreq(event: _EventSelectChinesePrescriptionFreq): AsyncGenerator<State> {
        this.innerState.groups[0].chinesePrescriptionUsage!.freq!.name = event.freq ?? "";
        this.computedAirPharmacyUSagDays();
        this.hasChanged = true;
        this.computedTisanesSack(false);
        this.update();
    }

    async *_mapEventSelectChinesePrescriptionUsageLevel(event: _EventSelectChinesePrescriptionUsageLevel): AsyncGenerator<State> {
        this.innerState.groups[0].chinesePrescriptionUsage!.usageLevel!.name = event.usageLevel ?? "";
        this.computedAirPharmacyUSagDays();
        this.hasChanged = true;
        this.computedTisanesSack(false);
        this.update();
    }

    @actionEvent(_EventSelectChinesePrescriptionUsageUsageDays)
    async *_mapEventSelectChinesePrescriptionUsageUsageDays(event: _EventSelectChinesePrescriptionUsageUsageDays): AsyncGenerator<State> {
        const chineseUsage = this.innerState.groups[0].chinesePrescriptionUsage;
        chineseUsage!.usageDays = chineseUsage?.usageDays ?? new ChineseUsageItemInfo();
        this.innerState.groups[0].chinesePrescriptionUsage!.usageDays!.name = event.usageDays ?? "";
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventUpdateChinesePrescriptionRequirement(event: _EventUpdateChinesePrescriptionRequirement): AsyncGenerator<State> {
        const remark = event.requirement;
        const { requirement } = ChineseMedicine;
        const _remarkList = _.cloneDeep(requirement);
        _remarkList.push(JsonMapper.deserialize(ChineseUsageItemInfo, { name: "其他" }));
        const initIndex = _remarkList.findIndex((i) => i.name == remark);
        const selectIndex = await AbcDialog.showOptionsBottomSheet({
            title: "备注",
            options: _remarkList?.map((item) => item.name),
            initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
            crossAxisCount: 4,
            height: pxToDp(500),
        });
        if (!selectIndex || !selectIndex.length) return;
        if (typeof selectIndex == "string" && selectIndex == "其他") {
            const _value = await MedicineUsagePicker.showCustomUsageDialog({
                title: "备注",
                value: this.innerState.groups[0].chinesePrescriptionUsage!.requirement,
                enableDefaultToolBar: false,
                maxLength: 200,
            });
            if (_value != undefined) {
                this.innerState.groups[0].chinesePrescriptionUsage!.requirement = _value?.name;
            }
        } else {
            this.innerState.groups[0].chinesePrescriptionUsage!.requirement = _remarkList[selectIndex[0]].name;
        }
        this.hasChanged = true;
        this.update();
    }

    // TODO 待删除
    async *_mapEventUpdateChinesePrescriptionBoilServiceMobile(
        event: _EventUpdateChinesePrescriptionBoilServiceMobile
    ): AsyncGenerator<State> {
        this.innerState.groups[0].chinesePrescriptionUsage!.boilServiceMobile = event.mobile;
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventSelectInfusionPrescriptionUsage(event: _EventSelectInfusionPrescriptionUsage): AsyncGenerator<State> {
        const group = event.group;
        const select = event.value;
        if (!!select) {
            event.group.infusionPrescriptionUsage!.usage = select;
            for (const medicine of group.selectedMedicines) {
                const usage = group.inputUsages.get(medicine);
                usage!.usage = { name: select };
            }
            this._refreshGroupMedicineAmount(event.group);
            this.hasChanged = true;
            this.update();
        }
    }

    async *_mapEventSelectInfusionPrescriptionFreq(event: _EventSelectInfusionPrescriptionFreq): AsyncGenerator<State> {
        const selects = event.value;
        if (!!selects) {
            event.group.infusionPrescriptionUsage!.freq = selects;
            this._refreshGroupMedicineAmount(event.group);
            this.hasChanged = true;
            this.update();
        }
    }

    async *_mapEventUpdateInfusionPrescriptionDays(event: _EventUpdateInfusionPrescriptionDays): AsyncGenerator<State> {
        const group = event.group;
        event.group.infusionPrescriptionUsage!.days = event.days ? event.days : undefined;

        for (const medicine of group.selectedMedicines) {
            const usage = group.inputUsages.get(medicine);
            usage!.computeUnitCount({
                goodsInfo: medicine,
                usage: usage,
                days: group.infusionPrescriptionUsage!.days,
            });
        }

        this._refreshGroupMedicineAmount(group);
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventUpdateInfusionPrescriptionIvgtt(event: _EventUpdateInfusionPrescriptionIvgtt): AsyncGenerator<State> {
        event.group.infusionPrescriptionUsage!.ivgtt = event.ivgtt ? event.ivgtt : undefined;
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventUpdateInfusionMedicineSkinTest(event: _EventUpdateInfusionMedicineSkinTest): AsyncGenerator<State> {
        event.group.inputUsages.get(event.medicine)!.ast = event.check ? null : null;
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventUpdateInfusionMedicineDosageUnit(event: _EventUpdateInfusionMedicineDosageUnit): AsyncGenerator<State> {
        event.group.inputUsages.get(event.medicine)!.dosage =
            event.group.inputUsages.get(event.medicine)!.dosage ?? new MedicineDosageUsage();
        event.group.inputUsages.get(event.medicine)!.dosage!.dosageUnit = event.dosageUnit;
        if (event.dosageUnit) this._refreshGroupMedicineAmount(event.group);
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventUpdateInfusionMedicineDosageUnitCount(event: _EventUpdateInfusionMedicineDosageUnitCount): AsyncGenerator<State> {
        event.group.inputUsages.get(event.medicine)!.dosage =
            event.group.inputUsages.get(event.medicine)!.dosage ?? new MedicineDosageUsage();
        event.group.inputUsages.get(event.medicine)!.dosage!.count = event.dosageUnitCount ? event.dosageUnitCount : undefined;
        if (event.dosageUnitCount) this._refreshGroupMedicineAmount(event.group);
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventUpdateInfusionMedicineUnit(event: _EventUpdateInfusionMedicineUnit): AsyncGenerator<State> {
        event.group.inputUsages.get(event.medicine)!.unit = event.unit;
        this.hasChanged = true;
        this._clearAdjustmentFee(event.medicine, "infusion");
        this.update();
    }

    async *_mapEventUpdateInfusionMedicineUnitCount(event: _EventUpdateInfusionMedicineUnitCount): AsyncGenerator<State> {
        const usage = event.group.inputUsages.get(event.medicine)! ?? new MedicineUsage();
        usage!.unitCount = _.isNumber(event.unitCount) ? event.unitCount : undefined;
        this.hasChanged = true;
        this._clearAdjustmentFee(event.medicine, "infusion");
        this.update();
    }

    private _showMinimumLimitTips(): boolean {
        const { groups, chineseMedicineSpecType } = this.innerState;
        const minimum = groups?.[0]?.vendor?.minimum;
        const { count, medicineStateScopeId } = groups[0].chinesePrescriptionUsage!;
        const pharmacyType = !_.isUndefined(groups?.[0]?.vendor?.pharmacyType)
            ? groups?.[0]?.vendor?.pharmacyType
            : groups[0].chinesePrescriptionUsage!.pharmacyType;
        //颗粒剂开方剂数是否限制
        const isDoseCountLimit = groups?.[0]?.chinesePrescriptionUsage?.isDoseCountLimit;
        // 空中药房才做以下验证
        // 没有选中药品的时候不做验证
        if (pharmacyType != PharmacyType.air || !groups[0].selectedMedicines.length) {
            return true;
        }

        // 中药饮片 + 代煎 剂量至少为 3
        if (chineseMedicineSpecType === ChineseMedicineSpecType.chinesePiece && medicineStateScopeId === MedicineScopeId.daiJian) {
            if (count < 3) {
                showConfirmDialog("", `3剂起煎`).then();
                return false;
            }
        }
        //颗粒剂开方剂数限制
        if (
            isDoseCountLimit &&
            chineseMedicineSpecType === ChineseMedicineSpecType.chineseGranule &&
            medicineStateScopeId === MedicineScopeId.keLi
        ) {
            if (count && count % 2 !== 0) {
                showConfirmDialog("", `${groups[0]?.vendor?.vendorName ?? groups?.[0]?.vendor?.pharmacyName}药需剂数为双数才可调剂`).then();
                return false;
            }
        }
        let dosageValue = 0;
        groups?.[0]?.inputUsages.forEach((item) => {
            if (item.unit == "g") dosageValue += Number(item.unitCount ?? 0) ?? 0;
        });
        dosageValue = dosageValue * (count ?? 0);
        if (_.isUndefined(dosageValue) || _.isUndefined(minimum)) return true;

        if (dosageValue < minimum) {
            showConfirmDialog("", `起做量${minimum}g`).then();
            return false;
        }
        // 空中药房-代煎-服用量必填
        if (groups[0]?.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.daiJian) {
            if (!groups[0]?.chinesePrescriptionUsage.usageLevel?.name) {
                showConfirmDialog("", `服用量不能为空`).then();
                return false;
            }
            if (!groups[0]?.chinesePrescriptionUsage.dailyDosage?.name) {
                showConfirmDialog("", `每日剂量不能为空`).then();
                return false;
            }
            if (!groups[0]?.chinesePrescriptionUsage.freq?.name) {
                showConfirmDialog("", `频率不能为空`).then();
                return false;
            }
            // 加工袋数不能为空
            if (!groups[0]?.chinesePrescriptionUsage?.processBagUnitCount || !groups[0].chinesePrescriptionUsage?.totalProcessCount) {
                showConfirmDialog("", `加工袋数不能为空`).then();
                return false;
            }
        }

        return true;
    }

    async *_mapEventSubmit(/*event: _EventSubmit*/): AsyncGenerator<State> {
        if (this.innerState.isWesternPrescription) {
            const length = this.innerState.groups.length;
            for (let i = 0; i < length; i++) {
                const group = this.innerState.groups[i];
                for (const medicine of group.selectedMedicines) {
                    const _usage = group.inputUsages!.get(medicine);
                    if (
                        (_.isEmpty(_usage?.usage) && _.isNil(_usage!.usage?.name)) ||
                        _.isEmpty(_usage?.freq?.en) ||
                        (_usage?.dosage?.dosageUnit == "适量" ? false : _usage?.dosage?.count == null) ||
                        _usage?.dosage?.dosageUnit == null ||
                        _usage?.unitCount == null ||
                        _.isEmpty(_usage!.unit) ||
                        !_usage?.days
                    ) {
                        await Toast.show("请完善必填项", { warning: true });
                        this.innerState.currentFocusItem = medicine;
                        this.innerState.currentFocusGroup = group;
                        this.innerState.showErrorHint = true;
                        yield ScrollToFocusItemState.fromState(this.innerState);
                        return;
                    }
                }
            }
        } else if (this.innerState.isChinesePrescription) {
            //需要将medicineAutoFocus设置为false,避免在聚焦药品克数的时候，直接点击完成，造成提示焦点错位
            this.innerState.medicineAutoFocus = false;
            const length = this.innerState.groups.length;
            for (let i = 0; i < length; ++i) {
                const group = this.innerState.groups[i];

                for (const medicine of group.selectedMedicines) {
                    const usage = group.inputUsages!.get(medicine);
                    if (!usage!.unitCount || (!this.isHideProcessInfo && !group.chinesePrescriptionUsage?.count)) {
                        await Toast.show("请完善必填项", { warning: true });
                        this.innerState.currentFocusGroup = group;
                        this.innerState.currentFocusItem = medicine;
                        this.innerState.showErrorHint = true;
                        yield ScrollToFocusItemState.fromState(this.innerState);
                        return;
                    }
                }
            }
            if (!this._showMinimumLimitTips()) return;
        } else if (this.innerState.isInfusionPrescription) {
            const length = this.innerState.groups.length;
            for (let i = 0; i < length; ++i) {
                const group = this.innerState.groups[i];

                //只验证当前组存在药品的
                if (group.selectedMedicines.length) {
                    const prescriptionUsage = group.infusionPrescriptionUsage;
                    if (prescriptionUsage?.days == null) {
                        await Toast.show("请完善必填项", { warning: true });
                        this.innerState.currentFocusGroup = group;
                        this.innerState.currentFocusItem = null;
                        this.innerState.showErrorHint = true;
                        yield ScrollToFocusItemState.fromState(this.innerState);
                        this.innerState.medicineAutoFocus = false;
                        return;
                    }
                }
                for (const medicine of group.selectedMedicines) {
                    const usage = group.inputUsages!.get(medicine);
                    if (
                        (usage!.dosage!.count || 0) <= 0 ||
                        usage!.unitCount == null ||
                        usage!.dosage?.dosageUnit == null ||
                        usage!.unit == null
                    ) {
                        this.innerState.currentFocusItem = medicine;
                        this.innerState.currentFocusGroup = group;
                        this.innerState.showErrorHint = true;

                        await Toast.show("请完善必填项", { warning: true });
                        yield ScrollToFocusItemState.fromState(this.innerState);
                        return;
                    }
                }
            }
        } else {
            const length = this.innerState.groups.length;
            for (let i = 0; i < length; ++i) {
                const group = this.innerState.groups[i];
                for (const medicine of group.selectedMedicines) {
                    const usage = group.inputUsages!.get(medicine);
                    if (usage!.unitCount == null) {
                        this.innerState.currentFocusItem = medicine;
                        this.innerState.currentFocusGroup = group;
                        this.innerState.showErrorHint = true;

                        await Toast.show("请完善必填项", { warning: true });
                        yield ScrollToFocusItemState.fromState(this.innerState);
                        this.update();
                        return;
                    }
                }
            }
        }
        //中药处方返回之前，需要匹配当前选中药房信息（selectPharmacyInfo在使用中只针对本地多药房，而代煎代配、空中药房没有用到，此时需要回显，否则一直都是本地药房）
        //解决：诊选择处方模板后，修改为代煎代配药房，收费完成后在药房发药时提示“不同药房类型发药
        if (this.innerState.isChinesePrescription) {
            const group = this.innerState.groups?.[0];
            for (const medicine of group?.selectedMedicines ?? []) {
                const usage = group.inputUsages!.get(medicine);
                if (
                    !!usage?.pharmacyInfo &&
                    usage?.pharmacyInfo?.type != this.innerState.selectPharmacyInfo?.type &&
                    usage?.pharmacyInfo?.no != this.innerState.selectPharmacyInfo?.no
                ) {
                    this.innerState.selectPharmacyInfo = this.innerState.pharmacyInfoConfig?.pharmacyList?.find(
                        (t) => t.type == usage?.pharmacyInfo?.type && t.no == usage?.pharmacyInfo?.no
                    );
                }
            }
        }
        ABCNavigator.pop(
            JsonMapper.deserialize(MedicineUsageInput, {
                medicines: this.innerState.groups,
                process: this.innerState.process,
                psychotropicNarcoticType: this.innerState.psychotropicNarcoticType, //精麻类型
                pharmacyInfo: this.innerState.selectPharmacyInfo,
                patient: this.innerState.detailData?.patient,
                psychotropicNarcoticEmployee: this.innerState.detailData?.psychotropicNarcoticEmployee,
            })
        );
    }

    // TODO 待删除
    async *_mapEventSelectGroup(event: _EventSelectGroup): AsyncGenerator<State> {
        this.innerState.currentFocusGroup = event.group;
        this.innerState.currentFocusItem = null;
        this.update();
    }

    async *_mapEventDeleteGroup(event: _EventDeleteGroup): AsyncGenerator<State> {
        //确认删除逻辑
        this.innerState.currentFocusGroup = null;
        this.innerState.currentFocusItem = null;
        const index = this.innerState.groups.indexOf(event.group);
        if (index > -1) {
            this.innerState.groups.splice(index, 1);
        }

        //输注处方删除为空时新增一个空数组
        if (this.innerState.groups.length == 0 && this.innerState.isInfusionPrescription) {
            this.innerState.groups.push(new MedicineAddGroup());
            for (const group of this.innerState.groups) {
                await this._initMedicineGroup(group);
            }
        }

        this.hasChanged = true;
        this.update();
    }

    async *_EventUpdateBoilServiceSwitch(event: _EventUpdateBoilServiceSwitch): AsyncGenerator<State> {
        this.innerState.boilService = event.check;
        LogUtils.d("MedicineAddPage==>_EventUpdateBoilServiceSwitch===>" + JSON.stringify(this.innerState.chinesePrescriptionUsage));
        this.innerState.chinesePrescriptionUsage!.boilServiceMobile = "";
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventCacheSearchInput(event: _EventCacheSearchInput): AsyncGenerator<State> {
        this._searchInput = event.searchInput;
    }

    async *_mapEventUpdateWesternMedicineGroupIndex(event: _EventUpdateWesternMedicineGroupIndex): AsyncGenerator<State> {
        const { medicine, group } = event,
            medicineUsages = group.inputUsages.get(medicine);
        const _options: string[] = _.range(1, 10).map((item) => "组" + item.toString());
        _options.push("-"); //增加不分组的选项
        const _initIndex = group.groupId ? (group.groupId == ChargeUtils.maxGroupId ? _options.length - 1 : group.groupId - 1) : undefined;
        this.innerState.westernCurrentFocusGroup = group;
        this.innerState.westernCurrentFocusItem = medicine;
        yield this.innerState;
        const selects = await AbcDialog.showOptionsBottomSheet({
            title: "选择分组",
            options: _options,
            initialSelectIndexes: _.isUndefined(_initIndex) ? _initIndex : new Set([_initIndex]),
        });
        this.innerState.westernCurrentFocusGroup = null;
        this.innerState.westernCurrentFocusItem = null;
        //解决没有选择分组或者选择同一组，选中状态没有变化，而选择了其他不同分组，则不需要update，否则会导致页面闪烁
        if ((!_.isEmpty(selects) && _initIndex == selects![0]) || _.isEmpty(selects)) {
            this.update();
        }
        if (!_.isUndefined(selects) && !_.isEmpty(selects) && selects![0] + 1 != group.groupId) {
            let _group;
            this.innerState.groups.forEach((item /*, index*/) => {
                if (item.groupId === selects![0] + 1) {
                    _group = item;
                    return;
                }
            });

            if (_.isEmpty(_group)) {
                _group = new MedicineAddGroup();
                _group.groupId = _options[selects![0]] == "-" ? ChargeUtils.maxGroupId : selects[0] + 1;
                this.innerState.groups.push(_group);
            }
            this.innerState.groups.sort((oldV, newV) => (oldV.groupId ?? 0) - (newV.groupId ?? 0));
            //更改组别后 用法、频率、天数与组别中的第一项相同
            const _medicineUsages = medicineUsages ?? new MedicineUsage();
            if (_group?.selectedMedicines.length) {
                //判断同组是否含有相同项目
                // if (_group.inputUsages.has(medicine)) {
                //     await Toast.show("与所选组存在重复项目", { warning: true });
                //     return;
                // }
                const _firstMedicineUsages = _group?.inputUsages.get(_group?.selectedMedicines[0]);
                _medicineUsages.freq = _firstMedicineUsages?.freq ?? _medicineUsages.freq;
                _medicineUsages.days = _firstMedicineUsages?.days ?? _medicineUsages.days;
                _medicineUsages.usage = _firstMedicineUsages?.usage ?? _medicineUsages.usage;
                _medicineUsages.computeUnitCount({ goodsInfo: medicine, usage: _medicineUsages });
            }
            //同一组别相互影响，不同组别相互无影响
            const groupId = _group?.groupId;
            //判断是否存在相同组别，需要过滤掉当前正在编辑项（当前编辑组的selectedMedicines无值）/如果选择的是不分组也需要过滤
            const isExistGroup = !!this.innerState.groups?.filter((k) => k.selectedMedicines.length)?.find((t) => t.groupId == groupId);
            _group?.inputUsages.set(
                medicine,
                isExistGroup && groupId != ChargeUtils.maxGroupId ? _medicineUsages : _.cloneDeep(_medicineUsages)
            );
            _group?.selectedMedicines?.push(medicine);
            this.dispatch(new _EventDeleteMedicine(group, medicine, "western"));
            this.hasChanged = true;
            this.update();
        }
    }

    @actionEvent(_EventDeleteWesternMedicineGroupIndex)
    async *_mapEventDeleteWesternMedicineGroupIndex(): AsyncGenerator<State> {
        this.innerState.isEditGroup = false;
        //取消分组后，各个药品之间没有联动关系
        this.innerState.groups.forEach((item) => {
            item.groupId = ChargeUtils.maxGroupId;
            item.selectedMedicines.forEach((formItem) => {
                item.inputUsages.set(formItem, _.cloneDeep(item.inputUsages?.get(formItem) ?? new MedicineUsage()));
            });
        });
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventContinueSearch(event: _EventContinueSearch): AsyncGenerator<State> {
        this.innerState.chineseSearchFocus = false; //中药处方确认数量填写时，隐藏搜索框
        delayed(100).subscribe(() => {
            if (event.focus) {
                this._searchInput?.focus();
            } else {
                // this._searchInput?.blur()
            }
            this.innerState.currentFocusItem = null;
        });
        //在空中药房状态下，特定药态对应计算服用天数
        this.computedAirPharmacyUSagDays();
        this.update();
    }

    async *_mapEventBackPage(/*event: _EventBackPage*/): AsyncGenerator<State> {
        //中药处方返回之前，需要匹配当前选中药房信息（selectPharmacyInfo在使用中只针对本地多药房，而代煎代配、空中药房没有用到，此时需要回显，否则一直都是本地药房）
        //解决：诊选择处方模板后，修改为代煎代配药房，收费完成后在药房发药时提示“不同药房类型发药
        if (this.innerState.isChinesePrescription) {
            const usage = this.innerState.groups?.[0]?.chinesePrescriptionUsage;
            if (
                usage?.pharmacyType != this.innerState.selectPharmacyInfo?.type &&
                usage?.pharmacyNo != this.innerState.selectPharmacyInfo?.no
            ) {
                this.innerState.selectPharmacyInfo = this.innerState.pharmacyInfoConfig?.pharmacyList?.find(
                    (t) => t.type == usage?.pharmacyType && t.no == usage?.pharmacyNo
                );
            }
        }
        if (this.hasChanged) {
            ABCNavigator.pop(
                JsonMapper.deserialize(MedicineUsageInput, {
                    medicines: this.innerState.groups,
                    process: this.innerState.process,
                    psychotropicNarcoticType: this.innerState.psychotropicNarcoticType,
                    pharmacyInfo: this.innerState.selectPharmacyInfo,
                })
            );
        } else {
            ABCNavigator.pop();
        }
    }

    async *_mapEventRecordPartLayout(event: _EventRecordPartLayout): AsyncGenerator<State> {
        if (_.isNaN(event.layoutY)) return;
        this.innerState.layoutState.set(event.key, event.layoutY);
    }

    async *_mapEventChangeArrangement(/*event: _EventChangeArrangement*/): AsyncGenerator<State> {
        this.innerState.arrangement = !this.innerState.arrangement;
        sharedPreferences.setInt(CHINESE_ARRANGEMENT, this.innerState.arrangement ? 0 : 1);
        this.update();
    }

    async *_mapEventNewModifyPharmacyType(/*event: _EventNewModifyPharmacyType*/): AsyncGenerator<State> {
        this.innerState.searchInfo = [];
        this.innerState.searchListNoStock = [];
        // this._searchInput?.clear(); //有可能此时_searchInput不存在,会造成系统异常

        const _airPharmacy = new SelectAirPharmacy();
        let group = this.innerState.groups![0];
        _airPharmacy.prescriptForms = [];
        for (const medicine of group.selectedMedicines) {
            let formItem = JsonMapper.deserialize(PrescriptionFormItem, {
                keyId: medicine.compareKey() ?? UUIDGen.generate(),
            });
            formItem = OutpatientUtils.fillPrescriptionFromItem(
                formItem,
                medicine,
                group?.inputUsages?.get(medicine) ?? new MedicineUsage()
            );
            _airPharmacy.prescriptForms.push(formItem);
        }
        //多药房
        const isMultiplePharmacy = this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy;

        _airPharmacy.medicineStateScopeId = group.chinesePrescriptionUsage?.medicineStateScopeId;
        _airPharmacy.usageScopeId = group.chinesePrescriptionUsage?.usageScopeId;
        _airPharmacy.pharmacyType = group.chinesePrescriptionUsage?.pharmacyType;
        _airPharmacy.pharmacyNo =
            this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy && this.innerState.pharmacy == PharmacyType.normal
                ? this.innerState.selectPharmacyInfo?.no
                : group.chinesePrescriptionUsage?.pharmacyNo ?? 0;
        _airPharmacy.vendorId = group.vendor?.vendorId;
        _airPharmacy.doseCount = group.chinesePrescriptionUsage?.count;
        _airPharmacy.goodsTypeId = group.chinesePrescriptionUsage?.specificationType
            ? ChineseGoodType.chineseGranule
            : ChineseGoodType.chinesePiece;
        _airPharmacy.vendorUsageScopeId = group?.vendor?.vendorUsageScopeId;
        _airPharmacy.vendorName = group?.vendor?.vendorName;
        const _pharmacyInfo = await SelectAirPharmacyDialog.show({
            prescriptInfo: _.cloneDeep(_airPharmacy),
            chineseMedicineSpecType: this.innerState.chineseMedicineSpecType,
            enableChangePharmacy: this.innerState.ableChangePharmacyType,
            showVirtualPharmacy: this.innerState.isOpenVirtualPharmacy,
            isEditLocalPharmacy: this.innerState.isEditLocalPharmacy ?? true,
            ableUseAirPharmacy: this.innerState.ableUseAirPharmacy,
        });
        if (_pharmacyInfo) {
            //本地药房
            let tranStatus = true;
            if (_pharmacyInfo?.pharmacyType == 0) {
                //如果为多药房，切换了药房，需要查询库存信息，如果只是切换了饮片/颗粒类型,则只改变类型
                //多药房只切换了药房，类型没有切换，需要强制查询一次库存
                const forceTrans =
                    isMultiplePharmacy && group.chinesePrescriptionUsage?.specificationType == _pharmacyInfo?.prescriptFormType;
                if (isMultiplePharmacy) {
                    if (_airPharmacy.pharmacyNo != _pharmacyInfo.multiplePharmacyInfo?.no) {
                        this.innerState.selectPharmacyInfo = _pharmacyInfo?.multiplePharmacyInfo;
                    }
                }

                //空中药房/代煎代配转换为本地药房，需要强制走一遍转换(!!_airPharmacy.pharmacyType是因为没有开启空中药房或者代煎代配时,pharmacyType为undefined)
                tranStatus = await this._trans(
                    new _EventChineseMedicineSpecType(
                        _pharmacyInfo!.localPharmacyType!,
                        (!!_airPharmacy.pharmacyType &&
                            _airPharmacy.pharmacyType != PharmacyType.normal &&
                            _pharmacyInfo.pharmacyType == PharmacyType.normal) ||
                            forceTrans,
                        _pharmacyInfo.pharmacyType
                    )
                );
            }
            //代煎代配需查询一次药品信息
            if (_pharmacyInfo.pharmacyType == PharmacyType.virtual) {
                this.requestChineseMedicineSpecType(
                    _pharmacyInfo?.prescriptFormType ?? this.innerState.chineseMedicineSpecType,
                    true,
                    _pharmacyInfo.pharmacyType
                );
            }
            if (!tranStatus) return;
            group = this.innerState.groups![0];
            const medicines: Array<GoodsInfo> = [];
            const usages: AbcMap<GoodsInfo, MedicineUsage> = new AbcMap();
            if (!ABCUtils.isEmpty(_pharmacyInfo.prescriptForms)) {
                _pharmacyInfo.prescriptForms?.forEach((formItem: PrescriptionFormItem, index: number) => {
                    //如何判断是同一个药品？?
                    //本地药房转空中药房后，没办法通过keyId进行比较；
                    // 在空中药房转换为本地药房时，如果是相同药品，只通过名字比较则会一直匹配第一个药品，导致克数丢失，如果添加了一个对应克数的比较
                    const _sameIndex = _.findIndex(
                        group.selectedMedicines,
                        (i, index) =>
                            (!!i?.keyId && !!formItem?.keyId && i?.keyId == formItem?.keyId) ||
                            (i.displayName == formItem.displayName &&
                                group.inputUsages?.get(group.selectedMedicines[index])?.unitCount == formItem?.unitCount)
                    );
                    !!formItem.productInfo && (formItem.productInfo.keyId = formItem?.keyId ?? UUIDGen.generate());
                    let _oldUsage;
                    if (_.isNumber(_sameIndex)) {
                        if (_sameIndex < 0) {
                            //空中药房药品列表返回的数据与本地
                            _oldUsage = group.inputUsages.get(group.selectedMedicines[index]);
                            const newUsage = MedicineUsage.fromPrescriptionFormItem(formItem);
                            const _oldMedicine = ABCUtils.first(medicines.splice(index, 1, formItem.goodsInfo));
                            _oldMedicine && usages.delete(_oldMedicine);
                            usages.set(
                                formItem.goodsInfo,
                                JsonMapper.deserialize(MedicineUsage, {
                                    ..._oldUsage,
                                    ...newUsage,
                                })
                            );
                        } else {
                            _oldUsage = group.inputUsages.get(group.selectedMedicines[_sameIndex]);
                            let medicine = group.selectedMedicines[_sameIndex];
                            if (_pharmacyInfo.pharmacyType == PharmacyType.virtual || _pharmacyInfo.pharmacyType == PharmacyType.air) {
                                medicine = formItem.goodsInfo;
                            }
                            const newUsage = MedicineUsage.fromPrescriptionFormItem(formItem);
                            medicines.push(medicine);
                            usages.set(
                                medicine,
                                JsonMapper.deserialize(MedicineUsage, {
                                    ..._oldUsage,
                                    ...newUsage,
                                })
                            );
                        }
                    }
                });
            }
            this.innerState.groups[0].selectedMedicines = medicines;
            this.innerState.groups[0].inputUsages = usages;
            this.innerState.groups[0].vendor = _pharmacyInfo?.vendor;
            // this.innerState.groups[0].chinesePrescriptionUsage!.count = _pharmacyInfo.doseCount ?? 1;
            //@ts-ignore
            this.innerState.groups[0].chinesePrescriptionUsage!.count = _pharmacyInfo?.doseCount;
            this.innerState.groups[0].chinesePrescriptionUsage!.pharmacyType = _pharmacyInfo?.pharmacyType;
            this.innerState.groups[0].chinesePrescriptionUsage!.pharmacyNo =
                isMultiplePharmacy && _pharmacyInfo.pharmacyType == PharmacyType.normal
                    ? _pharmacyInfo.multiplePharmacyInfo?.no
                    : _pharmacyInfo?.pharmacyNo;
            this.innerState.groups[0].chinesePrescriptionUsage!.usageScopeId = _pharmacyInfo?.usageScopeId;
            this.innerState.groups[0].chinesePrescriptionUsage!.medicineStateScopeId = !!_pharmacyInfo.medicineStateScopeId
                ? _pharmacyInfo.medicineStateScopeId
                : _pharmacyInfo.vendor?.vendorAvailableMedicalStates?.[0]?.medicalStatId;
            const _specificationType = _pharmacyInfo?.prescriptFormType;
            if (
                !_.isUndefined(_specificationType) &&
                this.innerState.groups[0].chinesePrescriptionUsage!.specificationType != _specificationType
            ) {
                this.innerState.groups[0].chinesePrescriptionUsage!.specificationType = _specificationType;
                this.innerState.chineseMedicineSpecType = _specificationType;
                this.innerState._chineseSpecType = this.innerState.chineseMedicineSpecType;
            }
            //空中药房需要记忆上次用法用量(如果当前供应商没有该用法用量，应该清空)

            const usageOptions = _pharmacyInfo.vendor?.businessScopeConfig?.usageOptions;
            const freqOptions = _pharmacyInfo.vendor?.businessScopeConfig?.freqOptions;
            const usageLevelOptions = _pharmacyInfo.vendor?.businessScopeConfig?.usageLevelOptions;
            const dosageOptions = _pharmacyInfo.vendor?.businessScopeConfig?.dosageOptions;
            let chinesePrescriptionUsage = !!clinicSharedPreferences.getObject(CHINESE_PRESCRIPTION_USAGE_DEFAULT)
                ? JSON.parse(clinicSharedPreferences.getObject(CHINESE_PRESCRIPTION_USAGE_DEFAULT))
                : undefined;
            chinesePrescriptionUsage = !!chinesePrescriptionUsage
                ? JsonMapper.deserialize(PrescriptionDefaultUsage, {
                      dailyDosage: !!dosageOptions?.find((t) => t?.name == chinesePrescriptionUsage?.dailyDosage?.name)
                          ? chinesePrescriptionUsage?.dailyDosage?.name
                          : undefined,
                      usage: !!usageOptions?.find((t) => t?.name == chinesePrescriptionUsage?.usage?.name)
                          ? chinesePrescriptionUsage?.usage?.name
                          : undefined,
                      freq: !!freqOptions?.find((t) => t?.name == chinesePrescriptionUsage?.freq?.name)
                          ? chinesePrescriptionUsage?.freq?.name
                          : undefined,
                      usageLevel: !!usageLevelOptions?.find((t) => t?.name == chinesePrescriptionUsage?.usageLevel?.name)
                          ? chinesePrescriptionUsage?.usageLevel?.name
                          : undefined,
                  })
                : undefined;
            const chineseUsageInfo =
                (_pharmacyInfo?.pharmacyType == PharmacyType.air && !!chinesePrescriptionUsage
                    ? chinesePrescriptionUsage
                    : ChineseMedicineConfigProvider.getChinesePRWithUsage(_pharmacyInfo?.usageScopeName ?? "")) ??
                ChineseMedicineConfigProvider.getChinesePRWithSpecification(ChineseMedicineSpecType.chinesePiece);
            chineseUsageInfo.usage = !!this.innerState.groups[0].chinesePrescriptionUsage?.usage?.name
                ? this.innerState.groups[0].chinesePrescriptionUsage?.usage?.name
                : chineseUsageInfo.usage;
            //本地药房服用量会根据用法以及饮片、颗粒而有所不同
            if (!_pharmacyInfo?.pharmacyType) {
                let usageLevelList: ChineseUsageItemInfo[] = [];
                const specificationType = this.innerState.groups[0].chinesePrescriptionUsage!.specificationType;
                const { usageLevel, zhiGaoUsageLevel, zhiWanUsageLevel, daFenUsageLevel, keLiChongFuUsageLevel } = ChineseMedicine;
                switch (chineseUsageInfo.usage) {
                    case "制膏":
                        usageLevelList = zhiGaoUsageLevel;
                        break;
                    case "制丸":
                        usageLevelList = zhiWanUsageLevel;
                        break;
                    case "打粉":
                        usageLevelList = daFenUsageLevel;
                        break;
                    default:
                        usageLevelList = usageLevel;
                }
                if (specificationType == ChineseMedicineSpecType.chineseGranule && chineseUsageInfo?.usage == "冲服") {
                    usageLevelList = keLiChongFuUsageLevel;
                }
                chineseUsageInfo.usageLevel =
                    usageLevelList.find((t) => t.name == chineseUsageInfo.usageLevel)?.name ??
                    ChineseMedicineConfigProvider.getChinesePRWithUsage(chineseUsageInfo?.usage)?.usageLevel;
            }
            this._fillChineseUsage(chineseUsageInfo);
            this.computedAirPharmacyUSagDays();
            this.computedTisanesSack(true).then(() => {
                const pharmacyType = !_.isUndefined(this.innerState.groups?.[0]?.vendor?.pharmacyType)
                    ? this.innerState.groups?.[0]?.vendor?.pharmacyType
                    : this.innerState.groups[0].chinesePrescriptionUsage!.pharmacyType;
                if (!pharmacyType) {
                    this.innerState.groups?.[0].selectedMedicines?.forEach((medicine) => {
                        this._clearAdjustmentFee(medicine, "chinese", true);
                    });
                }
            });
            //切换饮片、颗粒时，对应的药态制法应该置空
            if (!!this.innerState.groups[0].chinesePrescriptionUsage?.processRemark) {
                this.innerState.groups[0].chinesePrescriptionUsage!.processRemark = undefined;
            }
            //空中药房供应商配置--颗粒剂
            if (_pharmacyInfo?.vendor) {
                this.innerState.groups[0].chinesePrescriptionUsage!.vendorInfo =
                    this.innerState.groups[0].chinesePrescriptionUsage!.vendorInfo ?? new AirPharmacyKeLiConfig();
                this.innerState.groups[0].chinesePrescriptionUsage!.vendorInfo!.calculateProcessBagType =
                    _pharmacyInfo.vendor?.calculateProcessBagType;
                this.innerState.groups[0].chinesePrescriptionUsage!.vendorInfo!.processBagUnitCount =
                    _pharmacyInfo.vendor?.processBagUnitCount;
                this.innerState.groups[0].chinesePrescriptionUsage!.vendorInfo!.processBagUnit = _pharmacyInfo.vendor?.processBagUnit;
                this.innerState.groups[0].chinesePrescriptionUsage!.vendorInfo!.doseCountLimit = _pharmacyInfo.vendor?.doseCountLimit;
                this.innerState.groups[0].chinesePrescriptionUsage!.pharmacyName = _pharmacyInfo.vendor?.pharmacyName;
                this.innerState.groups[0].chinesePrescriptionUsage!.vendorId = _pharmacyInfo.vendor?.vendorId;
                this.innerState.groups[0].chinesePrescriptionUsage!.vendorUsageScopeId = _pharmacyInfo.vendor?.vendorUsageScopeId;
            }
            //  如果切换了供应商，对应处方的供应商信息也需要更新
            if (
                (_airPharmacy.vendorId != _pharmacyInfo.vendor?.vendorId ||
                    _airPharmacy.vendorUsageScopeId != _pharmacyInfo.vendor?.vendorUsageScopeId ||
                    _airPharmacy.vendorName != _pharmacyInfo.vendor?.vendorName) &&
                !!_pharmacyInfo.pharmacyType &&
                !!this._filterChinesePrescriptionForms(_pharmacyInfo.pharmacyType)?.length
            ) {
                // 找到处方中是否含有group中selectedMedicines中的药品，则为当前处方
                let currentEditFormIndex = -1; //当前编辑的处方索引
                for (let i = 0; i < this._filterChinesePrescriptionForms(_pharmacyInfo.pharmacyType!).length; i++) {
                    const formItem = this._filterChinesePrescriptionForms(_pharmacyInfo.pharmacyType!)[i];
                    const isExist = formItem.prescriptionFormItems?.every((item) => {
                        return this.innerState.groups?.[0]?.selectedMedicines.some((medicine) => item.displayName == medicine.displayName);
                    });
                    if (isExist) currentEditFormIndex = i;
                    break;
                }
                if (currentEditFormIndex > -1) {
                    const chineseFormItem = this._filterChinesePrescriptionForms(_pharmacyInfo.pharmacyType!)[currentEditFormIndex];
                    chineseFormItem.vendorId = _pharmacyInfo.vendor?.vendorId;
                    chineseFormItem.vendorName = _pharmacyInfo.vendor?.vendorName;
                    chineseFormItem.vendorUsageScopeId = _pharmacyInfo.vendor?.vendorUsageScopeId;
                }
            }
            this.update();
        }
        this._searchInput?.focus();
    }

    async *_mapEventAddMedicinePrescription(event: _EventAddMedicinePrescription): AsyncGenerator<State> {
        //弹出搜索框，获取当前选中的商品
        // const goodsInfo = await WesternMedicineSearchPage.show<GoodsInfo>({
        //     params: this.params,
        //     _searchClinicId: this._searchClinicId,
        //     showPrice: this.innerState.showGoodPrice,
        // });
        const goodsInfo = event.goodsInfo;
        goodsInfo.keyId = goodsInfo.keyId ?? UUIDGen.generate();
        if (!goodsInfo) return;
        //将当前选中的商品添加到group中
        if (!this.innerState.canCheckWithoutStock && (!goodsInfo.inStock || (goodsInfo.getStockPieceCount() ?? 0) < 0)) return;
        const _keyword = this.innerState.keyword;
        this.innerState.keyword = "";
        this.innerState.searchListNoStock = [];
        this.innerState.searchInfo = [];
        const medicine = JsonMapper.deserialize(GoodsInfo, goodsInfo);
        //西药添加组逻辑
        let _group = this.innerState.groups.find((item) => item.groupId == ChargeUtils.maxGroupId);
        if (!event.group && !_group) {
            _group = new MedicineAddGroup();
            this._initMedicineGroup(_group);
            _group.groupId = ChargeUtils.maxGroupId;
            this.innerState.groups.push(_group);
        }
        const group = event.group ?? _group!;

        this.innerState.currentFocusGroup = group;
        this.innerState.currentFocusItem = medicine;
        this._searchInput?.clear();
        this.hasChanged = true;
        for (const medicineItem of group.selectedMedicines) {
            if (medicineItem.id == medicine.id && medicineItem.displayName == medicine.displayName) {
                Toast.show(`${medicine.displayName}已添加`, { warning: true, autoBlurText: false });
                // this.innerState.medicineAutoFocus = true;
                this.dispatch(new _EventChangePageShow("selected"));
                this._searchInput?.focus();
                // this.innerState.currentFocusItem = medicineItem;
                // this.innerState.pendingActiveFocusItem = new Completer();
                // yield ScrollToFocusItemState.fromState(this.innerState);
                return;
            }
        }
        group.selectedMedicines?.push(medicine);
        await this.searchHistory.insert(_keyword);

        let age;
        if (this.params?.patient?.age != null) {
            age = (this.params.patient.age.year ?? 0) + (this.params.patient.age.month ?? 0) / 10;
        }

        const sellUnits = medicine.sellUnits;
        const usage = JsonMapper.deserialize(MedicineUsage, {
            unit: ABCUtils.first(sellUnits) ?? undefined,
            dosage: JsonMapper.deserialize(MedicineDosageUsage, {
                dosageUnit: ABCUtils.first(this.innerState.westernMedicineConfig?.dosageUnitStringList ?? []),
            }),
            usage: new Usage(),
            freq: new Freq(),
            specialRequirement: new ChineseUsageItemInfo(),
            pharmacyInfo: this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy
                ? this.initPrescriptionPharmacyInfo(medicine)
                : undefined,
            totalPrice: this.innerState.getSingleTotalPrice(medicine),
            totalPriceRatio: 1,
        });
        const units = medicine.usageUnits;
        if (!_.isEmpty(units)) usage.dosage!.dosageUnit = units[0];

        if (this.innerState.isWesternPrescription || this.innerState.isInfusionPrescription) {
            usage.unit = usage.unit ?? WesternMedicine.unit[0].name;
            usage.unitPrice = medicine.unitPriceWithUnit(usage.unit);
        }

        //注射类使用处方组中的用法用量
        if (this.innerState.isInfusionPrescription) {
            const infusionPrescriptionUsage = group.infusionPrescriptionUsage;
            usage.usage!.name = infusionPrescriptionUsage?.usage ?? usage.usage?.name;
        }

        //更新药品库存
        const currentSelStock = medicine?.pharmacyGoodsStockList?.find((item) => item?.pharmacyNo == (usage?.pharmacyInfo?.no ?? 0));
        const { stockPackageCount = 0, stockPieceCount = 0 } = currentSelStock || {};
        medicine.stockPackageCount = stockPackageCount;
        medicine.stockPieceCount = stockPieceCount;

        group.inputUsages.set(medicine, usage);

        if (this.innerState.isInfusionPrescription || this.innerState.isWesternPrescription) {
            this.innerState.medicineAutoFocus = false;
            try {
                await CDSSAgent.getMedicineUsage({
                    medicineCadn: medicine.medicineCadn,
                    goodsId: medicine.id,
                    type: medicine.type,
                    age: age,
                    sex: this.params?.patient?.sex,
                })
                    .then(async (usage) => {
                        LogUtils.d("CDSSAgent.getMedicineUsage usage = " + JSON.stringify(usage));
                        if (usage == null) return;
                        const westernConfig = this.innerState.westernMedicineConfig;
                        const validDosageUnit =
                            _.find(medicine.usageUnits, (item) => item == usage.dosageUnit) ||
                            usage.dosageUnit == OutpatientConst.dosageUsageProperty;
                        const validUnit = _.find(medicine.sellUnits, usage.unit);

                        const medicineUsage = JsonMapper.deserialize(MedicineUsage, {
                            usage: JsonMapper.deserialize(Usage, { name: usage.usage }),
                            freq: JsonMapper.deserialize(
                                Freq,
                                westernConfig?.freq?.find((item: Freq) => item.en == usage.freq || item.name == usage.name) ?? {
                                    en: "",
                                    name: "",
                                }
                            ),
                            dosage: JsonMapper.deserialize(MedicineDosageUsage, {
                                count: Number(usage.dosage) ? Number(usage.dosage) : undefined,
                                dosageUnit: ABCUtils.first(
                                    !_.isEmpty(medicine.usageUnits) ? medicine.usageUnits : westernConfig?.dosageUnitStringList ?? []
                                ),
                            }),
                            specialRequirement: JsonMapper.deserialize(ChineseUsageItemInfo, {
                                name: /*usage.specialRequirement*/ "", //取消推荐备注
                            }),
                            pharmacyInfo: this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy
                                ? this.initPrescriptionPharmacyInfo(medicine)
                                : undefined,
                            totalPrice: this.innerState.getSingleTotalPrice(medicine),
                            totalPriceRatio: 1,
                        });

                        if (validDosageUnit) medicineUsage.dosage!.dosageUnit = usage.dosageUnit;
                        if (validUnit) {
                            medicineUsage.unit = usage.unit;
                        } else {
                            medicineUsage.unit = ABCUtils.first(sellUnits) ?? "";
                        }
                        if (!!medicineUsage?.unit) medicineUsage.unitPrice = medicine.unitPriceWithUnit(medicineUsage.unit);
                        //更新药品库存
                        const currentSelStock = medicine?.pharmacyGoodsStockList?.find(
                            (item) => item?.pharmacyNo == (medicineUsage?.pharmacyInfo?.no ?? 0)
                        );
                        const { stockPackageCount = 0, stockPieceCount = 0 } = currentSelStock || {};
                        medicine.stockPackageCount = stockPackageCount;
                        medicine.stockPieceCount = stockPieceCount;

                        group.inputUsages.set(medicine, medicineUsage);
                        this.innerState.medicineAutoFocus = true;
                        this.update();
                    })
                    .catchIgnore()
                    .finally(() => {
                        this.innerState.medicineAutoFocus = true;
                        this.innerState.pendingActiveFocusItem = new Completer();
                        this.update();
                    });
            } catch (e) {
                this.innerState.pendingActiveFocusItem = new Completer();
                this.innerState.medicineAutoFocus = true;
                this.update();
            }
        } else {
            this.innerState.pendingActiveFocusItem = new Completer();
            this.innerState.medicineAutoFocus = true;
        }
        group?.inputUsages?.get(medicine)?.computeUnitCount({ goodsInfo: medicine });

        if (this.innerState.isInfusionPrescription) {
            //设置当前组的用法到当前药品
            this._refreshGroupMedicineAmount(group);
        }
        this.update();
    }

    async *_mapEventAddChineseMedicineUsage(event: _EventAddChineseMedicineUsage): AsyncGenerator<State> {
        this.innerState.chineseSearchFocus = false;
        const { group } = event;
        const result = await showBottomPanel<chinesePrescriptionGroup>(
            <ChinesePrescriptionProcessView
                group={group!}
                chinesePrescriptionUsage={group?.chinesePrescriptionUsage ?? new ChinesePrescriptionUsage()}
                process={this.innerState.process}
            />,
            {
                topMaskHeight: pxToDp(200),
            }
        );
        if (!result) return;
        // const result = await PrescriptionEditPage.show<chinesePrescriptionGroup>({
        //     type: this.innerState.medicineType,
        //     group: group,
        //     chinesePrescriptionUsage: group?.chinesePrescriptionUsage ?? new ChinesePrescriptionUsage(),
        //     tabType: tabType,
        //     process: this.process,
        // });
        if (result?.medicine?.chinesePrescriptionUsage) {
            this.innerState.groups[0].chinesePrescriptionUsage = result?.medicine?.chinesePrescriptionUsage;
        }
        if (!!result?.process) {
            //用于接诊详情页面中药处方的数据显示
            this.process = JsonMapper.deserialize(ChinesePrescriptionProcessingInfoRsp, result?.process);
            //用于中药处方详情卡片数据显示
            if (result.process.processName && result.process.processName?.indexOf("煎药") > -1) {
                const usageStr = `${!!result.process.processBagUnitCount ? "（1剂煎" + result.process.processBagUnitCount + "袋" : ""}${
                    !!result.process.totalProcessCount
                        ? "，共" + result.process.totalProcessCount + "袋）"
                        : !!result.process.processBagUnitCount
                        ? "）"
                        : ""
                }`;
                result.process.processName = `煎药${usageStr}`;
            } else if (!!result.process?.processRemark) {
                result.process.processName = result.process.processRemark;
            }
            this.innerState.process = JsonMapper.deserialize(ChinesePrescriptionProcessingInfoRsp, result?.process);
            //删除药房---恢复成默认药房设置
            this.innerState.groups[0].selectedMedicines.forEach((item) => {
                const usage = this.innerState.groups[0].inputUsages.get(item);
                if (usage) {
                    const transPharmacy = this.innerState.pharmacyInfoConfig?.getDefaultPharmacy({
                        departmentId: this._departmentId,
                        goodsInfo: { typeId: item?.typeId },
                        processInfo: this.innerState.process,
                    });
                    this.innerState.selectPharmacyInfo = transPharmacy;
                    if (
                        !!transPharmacy &&
                        (usage.pharmacyInfo?.no != transPharmacy?.no || usage.pharmacyInfo?.type != transPharmacy?.type)
                    ) {
                        usage.pharmacyInfo = transPharmacy;
                        //刷新药品库存
                        GoodsAgent.searchChineseMedicineGoodsBySpec({
                            cMSpec: ChineseMedicineSpecType.fullNames()[this.innerState.chineseMedicineSpecType!],
                            list: this.innerState.groups[0].selectedMedicines?.map((item) => ({
                                // medicineCadn: item._medicineCadn ?? item.displayName ?? item.medicineCadn,
                                medicineCadn: item.displayName,
                                goodsId: item.id ?? "",
                                manufacturer: item.manufacturer,
                                keyId: item.keyId ?? UUIDGen.generate(),
                                cMSpec: item.__cMSpec ?? ChineseMedicineSpecType.fullNames()[this.innerState.chineseMedicineSpecType!],
                                pieceUnit: item.pieceUnit ?? "",
                                extendSpec: item?.extendSpec ?? "",
                            })),
                            pharmacyNo: transPharmacy.no,
                        }).then((rsp) => {
                            this.innerState.groups[0].selectedMedicines = this.innerState.groups[0].selectedMedicines.map((item) => {
                                const isExistGoods = rsp?.find((t) => t.keyId == item.keyId);
                                const _newGoodInfo = !!isExistGoods?.goods?.id ? isExistGoods?.goods : undefined;
                                const sellUnits = _newGoodInfo?.sellUnits;
                                const usage = JsonMapper.deserialize(MedicineUsage, {
                                    ...this.innerState.groups[0].inputUsages.get(item),
                                    unit: !!sellUnits?.length ? ABCUtils.first(sellUnits) : undefined,
                                    pharmacyInfo: this.innerState.selectPharmacyInfo,
                                });
                                this.innerState.groups[0].inputUsages.delete(item);
                                //返回的药品可能是同一个，id相同，AbcMap则会覆盖
                                if (_newGoodInfo) {
                                    const _newMedicine = _newGoodInfo;
                                    _newMedicine.cMSpec = ChineseMedicineSpecType.fullNames()[this.innerState.chineseMedicineSpecType!];
                                    _newMedicine._medicineCadn = isExistGoods?.medicineCadn;
                                    _newMedicine.keyId = isExistGoods?.keyId ?? UUIDGen.generate();
                                    this.innerState.groups[0].inputUsages.set(_newMedicine, usage!);
                                    return _newMedicine;
                                } else {
                                    const _newMedicineGoods = GoodsUtils.clearGoodsStockInfo(item);
                                    _newMedicineGoods.cMSpec =
                                        ChineseMedicineSpecType.fullNames()[this.innerState.chineseMedicineSpecType!];
                                    _newMedicineGoods._medicineCadn = item?._medicineCadn;
                                    _newMedicineGoods.keyId = item.keyId ?? UUIDGen.generate();
                                    this.innerState.groups[0].inputUsages.set(_newMedicineGoods, usage!);
                                    return _newMedicineGoods;
                                }
                            });
                            this.update();
                        });
                    }
                }
            });
        }
        this.hasChanged = true;
        this.update();
    }

    async *_mapEventModifyEditGroupState(event: _EventModifyEditGroupState): AsyncGenerator<State> {
        this.innerState.isEditGroup = event.status;
        this.update();
    }

    async *_mapEventModifyEditMedicineUsage(event: _EventModifyEditMedicineUsage): AsyncGenerator<State> {
        const { medicine, group } = event;
        const result = await PrescriptionEditPage.show<MedicineUsage>({
            type: this.innerState.medicineType,
            medicine: medicine,
            medicineUsage: group?.inputUsages?.get(medicine) ?? new MedicineUsage(),
        });
        if (!!result) {
            group?.inputUsages.set(medicine, result);
        }
        this.update();
    }

    async *_mapEventModifyInfusionGroupUsage(event: _EventModifyInfusionGroupUsage): AsyncGenerator<State> {
        let { group } = event;
        const groupIndex = this.innerState.groups.findIndex((g) => _.eq(g, group));
        const result = await PrescriptionEditPage.show<MedicineAddGroup>({
            type: this.innerState.medicineType,
            group: group,
            groupIndex,
            infusionPrescriptionUsage: group.infusionPrescriptionUsage ?? new InfusionPrescriptionUsage(),
        });
        if (!!result && result instanceof MedicineAddGroup) {
            group = result;
            this._refreshGroupMedicineAmount(group);
        }
        this.update();
    }

    async *_mapEventAddChineseMedicine(): AsyncGenerator<State> {
        this.innerState.chineseSearchFocus = true;
        this.update();
    }

    @actionEvent(_EventSaveTemplate)
    async *_mapEventSaveTemplate(): AsyncGenerator<State> {
        if (!this.innerState.groups?.[0]?.selectedMedicines?.length) {
            return Toast.show("未添加任何项目", { warning: true });
        }
        const input = JsonMapper.deserialize(MedicineUsageInput, {
            medicines: this.innerState.groups,
            process: this.process,
            psychotropicNarcoticType: this.innerState.psychotropicNarcoticType,
        });
        let form;
        const temp = JsonMapper.deserialize(PrescriptionTemplateInfo, {});
        switch (this.innerState.medicineType) {
            case MedicineAddType.chinese:
                form = OutpatientUtils.createPrescriptionChineseForm(input);
                if (!form) return;
                temp.prescriptionChineseForms = [form];
                break;
            case MedicineAddType.infusion:
                form = OutpatientUtils.createPrescriptionInfusionForm(input);
                if (!form) return;
                temp.prescriptionInfusionForms = [form];
                break;
            case MedicineAddType.western:
                form = OutpatientUtils.createPrescriptionWesternForm(input);
                if (!form) return;
                temp.prescriptionWesternForms = [form];
                break;
        }
        await SaveTemplateDialog.show(temp);
    }

    @actionEvent(_EventUpdateMedicineStateScopedId)
    async *_mapEventUpdateMedicineStateScopedId(): AsyncGenerator<State> {
        const usageList = this.innerState.groups[0]?.vendor?.vendorAvailableMedicalStates;
        const options = usageList?.map((item) => item.medicalStatName);
        const initIndex =
            usageList?.findIndex((i) => i.medicalStatId == this.innerState.groups[0]?.chinesePrescriptionUsage?.medicineStateScopeId) ?? -1;
        const isAirPharmacy = this.innerState.groups[0]?.chinesePrescriptionUsage?.pharmacyType == PharmacyType.air;
        if (
            this.innerState.groups[0]?.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.keLi ||
            this.innerState.groups[0]?.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.daiJian ||
            this.innerState.groups[0]?.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.zhiJian
        ) {
            const info = await showBottomPanel<ChineseAirPharmacyBagsParam>(
                <ChineseAirPharmacyBagsDialog
                    options={options}
                    initIndex={initIndex}
                    chinesePrescriptionUsage={this.innerState.groups[0]?.chinesePrescriptionUsage ?? new ChinesePrescriptionUsage()}
                    medicalStateList={usageList}
                />,
                {
                    topMaskHeight: pxToDp(160),
                }
            );
            if (_.isUndefined(info)) return;
            this.innerState.groups[0].chinesePrescriptionUsage!.medicineStateScopeId = usageList![info.selectIndex!].medicalStatId;
            //空中药房显示加工信息、代煎代配药房为饮片处方-代煎，也需要显示加工信息
            const isShowProcess =
                isAirPharmacy ||
                (this.innerState.groups[0]?.chinesePrescriptionUsage?.pharmacyType == PharmacyType.virtual &&
                    this.innerState.groups[0]?.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.daiJian);
            this.innerState.groups[0].chinesePrescriptionUsage!.processBagUnitCount = isShowProcess ? info.processBagUnitCount : undefined;
            this.innerState.groups[0].chinesePrescriptionUsage!.totalProcessCount = isShowProcess ? info.totalProcessCount : undefined;
            this.innerState.groups[0].chinesePrescriptionUsage!.processRemark = isShowProcess ? info.processRemark : undefined;
        } else {
            const result = await AbcDialog.showOptionsBottomSheet({
                title: "药态",
                options: options,
                initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
            });
            if (!result || !result.length) return;
            this.innerState.groups[0].chinesePrescriptionUsage!.medicineStateScopeId = usageList![result[0]].medicalStatId;
        }

        if (this.innerState.chineseMedicineSpecType === ChineseMedicineSpecType.chinesePiece) {
            if (this.innerState.chinesePrescriptionUsage?.pharmacyType == PharmacyType.air) {
                this._loadVendorList.next();
            } else if (
                this.innerState.chinesePrescriptionUsage?.pharmacyType == PharmacyType.virtual &&
                this.innerState.groups[0]?.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.daiJian
            ) {
                this._loadVirtualVendorList.next();
            }
        }
        if (
            isAirPharmacy &&
            (this.innerState.groups[0]?.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.keLi ||
                this.innerState.groups[0]?.chinesePrescriptionUsage?.medicineStateScopeId == MedicineScopeId.daiJian)
        ) {
            this._airPharmacyCalculateTrigger.next();
        }
        this.hasChanged = true;
        this.update();
    }

    _loadVendor(form: PrescriptionChineseForm): Promise<AirPharmacyVendor[] | ABCError> {
        return ChargeAgent.getVendors({
            airPharmacyFormItems: form.prescriptionFormItems,
            doseCount: form.doseCount!,
            goodsTypeId: ChineseMedicineSpecType.typeFromName(form.specification!)
                ? ChineseGoodType.chineseGranule
                : ChineseGoodType.chinesePiece,
            medicineStateScopeId: form.medicineStateScopeId!,
            usageScopeId: form.usageScopeId!,
            vendorId: form.vendorId!,
            pharmacyNo: form.pharmacyNo,
        }).catch((error) => new ABCError(error));
    }

    private _modifyChinesePrescriptionUsage(prescription: PrescriptionChineseForm): void {
        const usageInfo: UsageInfo = {
            freq: prescription.freq,
            usageLevel: prescription.usageLevel,
        };
        prescription.usageDays = ChargeUtils.calcUsageDays({
            totalDoseWeight: prescription.computePrescriptionDosageWeight() * (prescription.doseCount ?? 1),
            finishedRate: prescription.vendor?.finishedRate,
            isAirPharmacy: prescription.isAirPharmacy,
            usageScopeId: prescription.usageScopeId,
            medicineStateScopeId: prescription.medicineStateScopeId,
            usageInfo: usageInfo,
        });
    }

    private async _trans(event: _EventChineseMedicineSpecType): Promise<boolean> {
        /**
         * 更改类型后判断药品是否转换类型
         */
        const toSpec = ChineseMedicineSpecType.fullNames()[event.type];
        const toSpecShort = ChineseMedicineSpecType.displayNames()[event.type];
        // const fromSpecShort = ChineseMedicineSpecType.displayNames().find((item) => toSpecShort != item);
        const isOpenMultiplePharmacy = this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy;

        //中药饮片颗粒不可转换的情况下，不管当前药品是何类型，均需要查询一次药品信息
        //
        // const transformList =
        //     this.innerState.unableChinesePrescriptionSupportMix || isOpenMultiplePharmacy
        //         ? this.innerState.groups[0].selectedMedicines
        //         : this.innerState.groups[0].selectedMedicines.filter((item) => {
        //               return _.isEmpty(item.cMSpec) || item.cMSpec != toSpec;
        //           });

        const transformList = this.innerState.groups[0].selectedMedicines;

        let queryInfo = DialogIndex.positive;
        if (transformList.length) {
            if (!event.forceTrans) {
                //多药房
                // if (isOpenMultiplePharmacy) {
                queryInfo = await showQueryDialog(
                    `处方药态将转换为${toSpecShort}`,
                    <View>
                        <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>
                            {`是否将处方中的所有药品转化为${toSpecShort}？`}
                        </Text>
                    </View>,
                    `确认转化`,
                    `取消`
                );
                // 以下注释---本地药房饮片、颗粒在弹窗中选择后切换模式
                // } else if (this.innerState.unableChinesePrescriptionSupportMix) {
                //     //不允许中药处方混开饮片颗粒
                //     queryInfo = await showQueryDialog(
                //         `${fromSpecShort}→${toSpecShort}`,
                //         <View>
                //             <Text>{`处方内所有${fromSpecShort}将转换为${toSpecShort}，是否确定？`}</Text>
                //         </View>,
                //         `确定`,
                //         `取消`
                //     );
                // } else {
                //     queryInfo = await showQueryDialog(
                //         `${fromSpecShort}→${toSpecShort}`,
                //         <View>
                //             <Text>{`处方内含 ${transformList.length} 味${fromSpecShort}:`}</Text>
                //             <Text style={[TextStyles.t14NT2, { lineHeight: Sizes.dp24 }]}>
                //                 {transformList.map((item) => item.displayName).join("、")}
                //             </Text>
                //             <SizedBox height={Sizes.dp16} />
                //             <Text>{`是否需要转换为相应的${toSpecShort}药味？`}</Text>
                //         </View>,
                //         `转换成${toSpecShort}`,
                //         `不转换`
                //     );
                // }
            }
            if (_.isNil(queryInfo)) {
                return false;
            } else if (queryInfo == DialogIndex.positive) {
                this.innerState.groups[0].selectedMedicines.forEach((item) => {
                    item.keyId = item.keyId ?? UUIDGen.generate();
                    if (item.cMSpec == toSpec) return;
                    item.__cMSpec = item.cMSpec;
                    item.cMSpec = toSpec;
                });
                //注释掉，会导致混开有问题，后续再梳理 在event.forceTrans为true情况下，比较当前药品中__cMSpec是否一致，如果一致的话，调用下面方法需要传递toSpec，如果不一致的话，则不需要传递
                // const uniqueCMSpecs = new Set(
                //     this.innerState.groups[0].selectedMedicines?.map((item) => (!!item.__cMSpec ? item.__cMSpec : item.cMSpec))
                // );
                // const isExistDiffCMspec = !event.forceTrans ? uniqueCMSpecs.size > 1 : true;
                //只有当转换类型cMSpec与内部cMSpec不一致时，才走trim匹配规则
                await GoodsAgent.searchChineseMedicineGoodsBySpec({
                    cMSpec: toSpec,
                    list: this.innerState.groups[0].selectedMedicines?.map((item) => ({
                        // medicineCadn: item._medicineCadn ?? item.displayName ?? item.medicineCadn,
                        medicineCadn: item.displayName,
                        goodsId: item.id ?? "",
                        manufacturer: item.manufacturer,
                        keyId: item.keyId ?? UUIDGen.generate(),
                        cMSpec: item.__cMSpec ?? ChineseMedicineSpecType.fullNames()[this.innerState.chineseMedicineSpecType!],
                        pieceUnit: item.pieceUnit ?? "",
                        extendSpec: item?.extendSpec ?? "",
                    })),
                    pharmacyNo:
                        isOpenMultiplePharmacy && event?.pharmacyType == PharmacyType.normal
                            ? this.innerState.selectPharmacyInfo?.no
                            : this.innerState.groups[0].chinesePrescriptionUsage?.pharmacyNo,
                }).then((rsp) => {
                    this.innerState.groups[0].selectedMedicines = this.innerState.groups[0].selectedMedicines.map((item) => {
                        const isExistGoods = rsp?.find((t) => t.keyId == item.keyId);
                        const _newGoodInfo = !!isExistGoods?.goods?.id ? isExistGoods?.goods : undefined;
                        const sellUnits = _newGoodInfo?.sellUnits;
                        const usage = JsonMapper.deserialize(MedicineUsage, {
                            ...this.innerState.groups[0].inputUsages.get(item),
                            unit: !!sellUnits?.length ? ABCUtils.first(sellUnits) : undefined,
                            pharmacyInfo: this.innerState.selectPharmacyInfo,
                        });
                        this.innerState.groups[0].inputUsages.delete(item);
                        //返回的药品可能是同一个，id相同，AbcMap则会覆盖
                        if (_newGoodInfo) {
                            const _newMedicine = _newGoodInfo;
                            _newMedicine.cMSpec = toSpec;
                            _newMedicine._medicineCadn = isExistGoods?.medicineCadn;
                            _newMedicine.keyId = isExistGoods?.keyId ?? UUIDGen.generate();
                            this.innerState.groups[0].inputUsages.set(_newMedicine, usage!);
                            return _newMedicine;
                        } else {
                            const _newMedicineGoods = GoodsUtils.clearGoodsStockInfo(item);
                            _newMedicineGoods.cMSpec = toSpec;
                            _newMedicineGoods._medicineCadn = item?._medicineCadn;
                            _newMedicineGoods.keyId = item.keyId ?? UUIDGen.generate();
                            this.innerState.groups[0].inputUsages.set(_newMedicineGoods, usage!);
                            return _newMedicineGoods;
                        }
                    });
                    this.update();
                });
            }
        }
        //中药饮片颗粒不可混开情况下，点击"取消",不应该转换类型
        const isNotNeedChangeType = this.innerState.unableChinesePrescriptionSupportMix && queryInfo == DialogIndex.negative;
        if (!isNotNeedChangeType) {
            this.innerState.chineseMedicineSpecType = event.type;
            this.innerState.groups[0].chinesePrescriptionUsage!.specificationType = event.type;
            this.innerState._chineseSpecType = this.innerState.chineseMedicineSpecType;
        }
        this.hasChanged = true;
        return !isNotNeedChangeType;
    }
    private async _updatePsychotropicNarcoticEmployee(): Promise<void> {
        const result: { patientInfo?: Patient; agentInfo?: Patient } = await ABCNavigator.navigateToPage(
            <JingMaPrescriptionRequiredPage
                patientInfo={this.innerState.detailData?.patient}
                agentInfo={this.innerState.detailData?.psychotropicNarcoticEmployee}
            />
        );
        if (!result) return;
        this.innerState.detailData!.patient = JsonMapper.deserialize(Patient, result?.patientInfo);
        this.innerState.detailData!.psychotropicNarcoticEmployee = JsonMapper.deserialize(PsychotropicNarcoticEmployee, {
            ...result?.agentInfo,
            patient: result?.patientInfo,
        });
    }
    @actionEvent(_EventModifyPrescriptionNarcoticType)
    async *_mapEventModifyPrescriptionNarcoticType(event: _EventModifyPrescriptionNarcoticType): AsyncGenerator<State> {
        this.innerState.psychotropicNarcoticType = event.type;
        const needCheckType = [
            PsychotropicNarcoticTypeEnum.JING1,
            PsychotropicNarcoticTypeEnum.JING2,
            PsychotropicNarcoticTypeEnum.MAZUI,
            PsychotropicNarcoticTypeEnum.DU,
        ];
        // 处方类型为：麻醉、精一、精二、毒中任何一种，并且当前处方没有填写过代办人信息
        if (event?.type && needCheckType.includes(event.type)) {
            await this._updatePsychotropicNarcoticEmployee();
        }
        this.hasChanged = true;
        this.update();
    }

    @actionEvent(_EventModifyPrescriptionType)
    async *_mapEventModifyPrescriptionType(): AsyncGenerator<State> {
        let psychotropicTypeList = PsychotropicNarcoticTypeList;
        if (this.innerState.isChinesePrescription || this.innerState.isExamination) {
            psychotropicTypeList = psychotropicTypeList.filter((item) => item.value == 0 || item.value > 4);
        }
        const result: { type?: number; isModify?: boolean } = await showBottomPanel(
            <PrescriptionTypeDialog
                list={psychotropicTypeList}
                type={this.innerState.psychotropicNarcoticType}
                agentName={this.innerState.detailData?.psychotropicNarcoticEmployee?.name}
                isShowAgent={
                    !!this.innerState.psychotropicNarcoticType &&
                    this.innerState.psychotropicNarcoticType <= PsychotropicNarcoticTypeEnum.DU
                }
            />,
            {
                topMaskHeight: pxToDp(312),
            }
        );
        if (!result) return;
        if (!isNil(result.type)) {
            this.requestModifyPrescriptionNarcoticType(result.type ?? 0);
        }
        if (!!result?.isModify) {
            await this._updatePsychotropicNarcoticEmployee();
        }
    }

    //多药房--饮片/颗粒相互切换
    @actionEvent(_EventChineseSwitchType)
    async *_mapEventChineseSwitchType(): AsyncGenerator<State> {
        this.innerState._chineseSpecType = this.innerState._chineseSpecType == 0 ? 1 : 0;
        this.medicineType = MedicineAddType.chinese;
        this._loadDataTrigger.next();
    }

    @actionEvent(_EventModifyMedicinePharmacy)
    async *_mapEventModifyMedicinePharmacy(event: _EventModifyMedicinePharmacy): AsyncGenerator<State> {
        const { medicine, pharmacy, isModifySelf } = event;

        //还原自备状态
        if (!isModifySelf) {
            this.requestModifyMedicineSelfProvidedStatus(medicine, DispensingFormItemsSourceItemType.default, event?.group);
        }

        //修改库存信息
        const currentPharmacyStockInfo = medicine.pharmacyGoodsStockList?.find((item) => item.pharmacyNo == pharmacy.no);
        const { stockPackageCount = 0, stockPieceCount = 0 } = currentPharmacyStockInfo ?? {};
        medicine.stockPackageCount = stockPackageCount;
        medicine.stockPieceCount = stockPieceCount;
        //修改药房信息
        medicine.pharmacyNo = pharmacy.no;
        medicine.pharmacyType = pharmacy.type;
        const usage = (!!event?.group ? event.group : this.innerState.groups[0]).inputUsages.get(medicine) ?? new MedicineUsage();
        usage.pharmacyInfo = {
            name: pharmacy.name,
            no: pharmacy.no,
            type: pharmacy.type,
            typeName: pharmacy.typeName,
            pharmacyTag: pharmacy.extendInfo?.tag,
        };
        (!!event?.group ? event.group : this.innerState.groups[0]).inputUsages.set(medicine, usage);
        if ((userCenter.clinic?.isDentistryClinic || userCenter.clinic?.isNormalHospital) && !!event?.type)
            this._clearAdjustmentFee(medicine, event?.type, true);
        this.update();
    }

    @actionEvent(_EventModifyMedicineSelfProvidedStatus)
    async *_mapEventModifyMedicineSelfProvidedStatus(event: _EventModifyMedicineSelfProvidedStatus): AsyncGenerator<State> {
        const { medicine, status, group } = event;

        const usage = (!!event?.group ? event.group : this.innerState.groups[0]).inputUsages.get(medicine) ?? new MedicineUsage();
        usage.selfProvidedStatus = status;

        //清空药房信息
        if (status == DispensingFormItemsSourceItemType.noCharge) {
            if (this.innerState.selectPharmacyInfo) {
                this.dispatch(new _EventModifyMedicinePharmacy(medicine, this.innerState.selectPharmacyInfo, true, group));
            }
        }

        (!!event?.group ? event.group : this.innerState.groups[0]).inputUsages.set(medicine, usage);

        this.update();
    }

    @actionEvent(_EventSelectAirExpress)
    async *_mapEventSelectAirExpress(event: _EventSelectAirExpress): AsyncGenerator<State> {
        let result;
        const chargeForm = new ChargeForm();
        chargeForm.keyId = UUIDGen.generate();
        chargeForm.sourceFormType = ChargeSourceFormType.airPharmacy;
        chargeForm.status = ChargeStatus.unCharged;

        this.innerState.groups?.map((it) => {
            const usage: UsageInfo = (chargeForm.usageInfo = chargeForm.usageInfo ?? {});
            usage.freq = it.chinesePrescriptionUsage?.freq?.name;
            usage.usageLevel = it.chinesePrescriptionUsage?.usageLevel?.name;
            usage.specification = it.chinesePrescriptionUsage?.specificationType.toString();
            usage.dailyDosage = it.chinesePrescriptionUsage?.dailyDosage?.name;
            usage.usage = it.chinesePrescriptionUsage?.usage?.name;
            usage.usageDays = it.chinesePrescriptionUsage?.usageDays?.name;
            usage.doseCount = it.chinesePrescriptionUsage?.count;

            chargeForm.vendorName = it.vendor?.vendorName;
            chargeForm.vendorId = it.vendor?.vendorId;
            chargeForm.medicineStateScopeId = it.chinesePrescriptionUsage?.medicineStateScopeId;
            chargeForm.medicineStateScopeName = it.vendor?.vendorAvailableMedicalStates?.find(
                (item) => item.medicalStatId == it.chinesePrescriptionUsage?.medicineStateScopeId
            )?.medicalStatName;
            chargeForm.pharmacyType = it.vendor?.pharmacyType ?? it.chinesePrescriptionUsage?.pharmacyType;
            chargeForm.pharmacyName = it.vendor?.pharmacyName;
            chargeForm.pharmacyNo = it.vendor?.pharmacyNo;
            chargeForm.usageScopeId = it.chinesePrescriptionUsage?.usageScopeId as UsageScopeId;
            chargeForm.deliveryInfo = JsonMapper.deserialize(DeliveryInfo, it.chinesePrescriptionUsage?.prescriptionFormDelivery);
            chargeForm.chargeFormItems = !_.isEmpty(chargeForm.chargeFormItems) ? chargeForm.chargeFormItems : [];
            it.selectedMedicines?.map((sub) => {
                chargeForm.chargeFormItems?.push(
                    JsonMapper.deserialize(ChargeFormItem, {
                        productInfo: sub,
                        productId: sub?.id,
                        name: sub?.displayName,
                        unit: it.inputUsages.get(sub)?.unit,
                        unitCount: it.inputUsages.get(sub)?.unitCount,
                        unitPrice: sub?.piecePrice,
                        doseCount: it.chinesePrescriptionUsage?.count,
                    })
                );
            });
        });
        chargeForm.fixAirPharmacyPrescriptionInfo();
        if (event.pharmacyType == PharmacyType.virtual) {
            result = await DeliveryInfoEditPage.show({
                patient: this.params?.patient ?? new Patient(),
                deliveryInfo: chargeForm.deliveryInfo ?? new DeliveryInfo(),
                payTypeMutable: true,
                addressMutable: true,
                deliverFeeEnable: false,
                chargeForm: chargeForm,
            });
        } else {
            result = await AirPharmacyDeliveryInfoEditPage.show({
                patient: this.params?.patient ?? new Patient(),
                chargeForm: chargeForm,
                payTypeMutable: true,
                addressMutable: true,
                deliverFeeEnable: true,
                otherAirPharmacyCalculateForm: this.otherAirPharmacyCalculateForm,
                airPharmacySort: this.airPharmacySort,
            });
        }

        if (result && result.deliverInfo) {
            this.innerState.groups[0].chinesePrescriptionUsage!.prescriptionFormDelivery =
                this.innerState.groups[0].chinesePrescriptionUsage?.prescriptionFormDelivery ?? new PrescriptionFormDelivery();
            this.innerState.groups[0].chinesePrescriptionUsage!.prescriptionFormDelivery = JsonMapper.deserialize(
                PrescriptionFormDelivery,
                {
                    ...result?.deliverInfo,
                    deliveryCompanyId: result.deliverInfo?.deliveryCompany?.id,
                    deliveryFee: result.deliverInfo?.expectDeliverFee__,
                }
            );
            const { medicineStateScopeId } = this.innerState.groups?.[0]?.chinesePrescriptionUsage ?? new ChinesePrescriptionUsage();
            if (
                event.pharmacyType == PharmacyType.air &&
                (medicineStateScopeId == MedicineScopeId.keLi || medicineStateScopeId == MedicineScopeId.daiJian)
            ) {
                this._airPharmacyCalculateTrigger.next();
            }
        }
        this.update();
    }

    /**
     *  筛选出对应药房的中药处方
     */
    _filterChinesePrescriptionForms(pharmacyType: number): PrescriptionChineseForm[] {
        return (
            this.innerState.detailData?.prescriptionChineseForms?.filter(
                (t) => t.pharmacyType == pharmacyType && (!!this._prescriptionFormKeyId ? t.keyId == this._prescriptionFormKeyId : true)
            ) ?? []
        );
    }

    /**
     * 筛选出对应药房的成药处方
     */
    _filterWesternPrescriptionForms(): PrescriptionWesternForm[] {
        return (
            this.innerState.detailData?.prescriptionWesternForms?.filter((t) =>
                !!this._prescriptionFormKeyId ? t.keyId == this._prescriptionFormKeyId : true
            ) ?? []
        );
    }
    /**
     * 筛选出对应药房的输液处方
     */
    _filterInfusionPrescriptionForms(): PrescriptionInfusionForm[] {
        return (
            this.innerState.detailData?.prescriptionInfusionForms?.filter((t) =>
                !!this._prescriptionFormKeyId ? t.keyId == this._prescriptionFormKeyId : true
            ) ?? []
        );
    }

    /**
     * 将group转换成各个处方form结构
     * @param type
     * @param medicine   主要用于标识当前药房信息
     */
    _changeGoodsInfoToForm(type: string, medicine?: GoodsInfo): void {
        const info = JsonMapper.deserialize(MedicineUsageInput, {
            medicines: this.innerState.groups,
            process: this.innerState.process,
            psychotropicNarcoticType: this.innerState.psychotropicNarcoticType, //精麻类型
            pharmacyInfo: !!medicine
                ? {
                      name: medicine.pharmacyName,
                      type: medicine.pharmacyType,
                      no: medicine.pharmacyNo,
                  }
                : this.innerState.selectPharmacyInfo,
        });
        const group = this.innerState.groups?.[0];
        if (type == "western") {
            // 新增
            if (!this.params?.medicines?.length && !this._filterWesternPrescriptionForms()?.length) {
                const westernPrescriptionForm = OutpatientUtils.createPrescriptionWesternForm(info);
                this.innerState.detailData!.prescriptionWesternForms = [];

                if (westernPrescriptionForm && !!westernPrescriptionForm?.prescriptionFormItems?.length)
                    this.innerState.detailData?.prescriptionWesternForms?.push(westernPrescriptionForm);
            } else {
                //  原来处方基础上修改
                group?.selectedMedicines?.forEach((medicine) => {
                    this._filterWesternPrescriptionForms()?.map((form) => {
                        let sameItem = form.prescriptionFormItems?.find((t) => t.keyId == medicine.keyId);
                        if (sameItem) {
                            sameItem = OutpatientUtils.fillPrescriptionFromItem(sameItem, medicine, group?.inputUsages.get(medicine));
                            return;
                        } else {
                            const formItem = OutpatientUtils.fillPrescriptionFromItem(
                                new PrescriptionFormItem(),
                                medicine,
                                group?.inputUsages.get(medicine)
                            );
                            formItem.keyId = medicine.keyId;
                            form.prescriptionFormItems?.push(formItem);
                        }
                        return form;
                    });
                });
            }
            this._filterWesternPrescriptionForms()?.map((form) => {
                form.keyId = form.keyId ?? UUIDGen.generate();
                form.prescriptionFormItems?.map((item, index) => {
                    item.keyId = item.keyId ?? UUIDGen.generate();
                    item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
                });
            });
        } else if (type == "infusion") {
            // 新增
            if (!this.params?.medicines?.length && !this._filterInfusionPrescriptionForms()?.length) {
                const InfusionPrescriptionForm = OutpatientUtils.createPrescriptionInfusionForm(info);
                this.innerState.detailData!.prescriptionInfusionForms = [];
                if (InfusionPrescriptionForm && !!InfusionPrescriptionForm?.prescriptionFormItems?.length)
                    this.innerState.detailData?.prescriptionInfusionForms?.push(InfusionPrescriptionForm);
            } else {
                //  原来处方基础上修改
                this.innerState.groups?.forEach((item) => {
                    item?.selectedMedicines?.forEach((medicine) => {
                        const usage = item.inputUsages.get(medicine);
                        if (usage) usage.days = item.infusionPrescriptionUsage?.days;
                        this._filterInfusionPrescriptionForms()?.map((form) => {
                            let sameItem = form.prescriptionFormItems?.find((t) => t.keyId == medicine.keyId);
                            if (sameItem) {
                                sameItem = OutpatientUtils.fillPrescriptionFromItem(sameItem, medicine, usage);
                                return;
                            } else {
                                const formItem = OutpatientUtils.fillPrescriptionFromItem(new PrescriptionFormItem(), medicine, usage);
                                formItem.keyId = medicine.keyId;
                                formItem.groupId = item.groupId;
                                form.prescriptionFormItems?.push(formItem);
                            }
                            return form;
                        });
                    });
                });
            }
            this._filterInfusionPrescriptionForms()?.map((form) => {
                form.keyId = form.keyId ?? UUIDGen.generate();
                form.prescriptionFormItems?.map((item, index) => {
                    item.keyId = item.keyId ?? UUIDGen.generate();
                    item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
                });
            });
        } else if (type == "chinese") {
            // 中药需要区分pharmacyType
            const pharmacyType = group?.chinesePrescriptionUsage?.pharmacyType ?? PharmacyType.normal;
            // 新增
            if (!this.params?.medicines?.length && !this._filterChinesePrescriptionForms(pharmacyType)?.length) {
                const chinesePrescriptionForm = OutpatientUtils.createPrescriptionChineseForm(info);
                if (!!chinesePrescriptionForm) chinesePrescriptionForm.pharmacyType = pharmacyType;
                this.innerState.detailData!.prescriptionChineseForms = [];
                if (chinesePrescriptionForm && !!chinesePrescriptionForm?.prescriptionFormItems?.length)
                    this.innerState.detailData?.prescriptionChineseForms?.push(chinesePrescriptionForm);
            } else {
                //  原来处方基础上修改
                group?.selectedMedicines?.forEach((medicine) => {
                    for (const form of this._filterChinesePrescriptionForms(pharmacyType) ?? []) {
                        let sameItem = form.prescriptionFormItems?.find((t) => {
                            if (!pharmacyType) return t.keyId == medicine?.keyId;
                            return t?.displayName == medicine?.displayName;
                        });
                        const inputUsage = JsonMapper.deserialize(MedicineUsage, {
                            ...group?.inputUsages.get(medicine),
                            dosage: JsonMapper.deserialize(MedicineDosageUsage, {
                                count: group?.chinesePrescriptionUsage?.count,
                            }),
                        });
                        if (sameItem) {
                            sameItem = OutpatientUtils.fillPrescriptionFromItem(sameItem, medicine, inputUsage);
                            continue;
                        } else {
                            const formItem = OutpatientUtils.fillPrescriptionFromItem(new PrescriptionFormItem(), medicine, inputUsage);
                            formItem.keyId = medicine.keyId;
                            form.prescriptionFormItems?.push(formItem);
                        }
                        return form;
                    }
                });
            }
            this._filterChinesePrescriptionForms(pharmacyType)?.map((form) => {
                form.doseCount = group?.chinesePrescriptionUsage?.count;
                form.keyId = form.keyId ?? UUIDGen.generate();
                form.prescriptionFormItems?.map((item, index) => {
                    item.keyId = item.keyId ?? UUIDGen.generate();
                    item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
                });
            });
        }
    }

    @actionEvent(_EventAdjustDiscount)
    async *_mapEventAdjustDiscount(event: _EventAdjustDiscount): AsyncGenerator<State> {
        if (!event?.medicine) return;
        this._changeGoodsInfoToForm(event.type);
        this.innerState.groups[0].inputUsages.get(event.medicine)!.expectedTotalPriceRatio = event.discount / 100;
        this.hasChanged = true;
        if (event.type == "western") {
            this.innerState.detailData?.prescriptionWesternForms?.forEach((form) => {
                const sameItem = form.prescriptionFormItems?.find((t) => t.goodsInfo.id == event.medicine.id);
                if (sameItem) {
                    sameItem.expectedTotalPriceRatio = event.discount / 100;
                    sameItem.expectedTotalPrice = undefined;
                    sameItem.expectedUnitPrice = undefined;
                    sameItem.totalPrice = undefined;
                    sameItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
                }
            });
        } else if (event.type == "infusion") {
            this.innerState.detailData?.prescriptionInfusionForms?.forEach((form) => {
                const sameItem = form.prescriptionFormItems?.find((t) => t.goodsInfo.id == event.medicine.id);
                if (sameItem) {
                    sameItem.expectedTotalPriceRatio = event.discount / 100;
                    sameItem.expectedTotalPrice = undefined;
                    sameItem.expectedUnitPrice = undefined;
                    sameItem.totalPrice = undefined;
                    sameItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
                }
            });
        }
        this._calculatePriceTrigger.next(event.type);
        this.update();
    }

    @actionEvent(_EventAdjustTotalPrice)
    async *_mapEventAdjustTotalPrice(event: _EventAdjustTotalPrice): AsyncGenerator<State> {
        if (!event?.medicine) return;
        this._changeGoodsInfoToForm(event.type);
        this.innerState.groups[0].inputUsages.get(event.medicine)!.expectedTotalPrice = event.totalPrice;
        this.hasChanged = true;
        if (event.type == "western") {
            this.innerState.detailData?.prescriptionWesternForms?.forEach((form) => {
                const sameItem = form.prescriptionFormItems?.find((t) => t.goodsInfo.id == event.medicine.id);
                if (sameItem) {
                    sameItem.expectedTotalPrice = event.totalPrice;
                    sameItem.expectedTotalPriceRatio = undefined;
                    sameItem.expectedUnitPrice = undefined;
                    sameItem.totalPriceRatio = undefined;
                    sameItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
                }
            });
        } else if (event.type == "infusion") {
            this.innerState.detailData?.prescriptionInfusionForms?.forEach((form) => {
                const sameItem = form.prescriptionFormItems?.find((t) => t.goodsInfo.id == event.medicine.id);
                if (sameItem) {
                    sameItem.expectedTotalPrice = event.totalPrice;
                    sameItem.expectedTotalPriceRatio = undefined;
                    sameItem.expectedUnitPrice = undefined;
                    sameItem.totalPriceRatio = undefined;
                    sameItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
                }
            });
        }
        this._calculatePriceTrigger.next(event.type);
        this.update();
    }
    _copyPriceAttrTo(fromForms: Array<any>, toForms: Array<any>, shouldNotCopyExpectedPrice?: boolean): void {
        if (ABCUtils.isEmpty(fromForms) || ABCUtils.isEmpty(toForms)) return;

        for (const form of fromForms) {
            // for (let item of form) {
            const originalForm = toForms.find((item) => item.keyId == form.keyId);
            originalForm.totalPrice = form.totalPrice;
            originalForm.isTotalPriceChanged = form.isTotalPriceChanged;
            originalForm.sheetFlatPrice = form.sheetFlatPrice;
            originalForm.sourceTotalPrice = form.sourceTotalPrice;
            originalForm.expectedTotalPrice = form.expectedTotalPrice;
            originalForm.totalPriceRatio = form.totalPriceRatio;

            let fromFormItems: Array<PrescriptionFormItem>;
            let toFormItems: Array<PrescriptionFormItem>;

            if (form instanceof PrescriptionProductForm) {
                fromFormItems = form.productFormItems!;
                toFormItems = originalForm.productFormItems;
            } else {
                if (form.pharmacyType == PharmacyType.air) {
                    originalForm.expectedTotalPrice = form.expectedTotalPrice;
                }
                fromFormItems = form.prescriptionFormItems;
                toFormItems = originalForm.prescriptionFormItems;
            }

            for (const formItem of fromFormItems) {
                const originalFormItem = toFormItems.find((item) => item.keyId == formItem.keyId);
                originalFormItem!.totalPrice = formItem.totalPrice;
                originalFormItem!.sourceUnitPrice = formItem.sourceUnitPrice;
                originalFormItem!.sourceTotalPrice = formItem.sourceTotalPrice;
                originalFormItem!.unitPrice = formItem.unitPrice;
                originalFormItem!.formFlatPrice = formItem.formFlatPrice;
                originalFormItem!.sheetFlatPrice = formItem.sheetFlatPrice;
                originalFormItem!.fractionPrice = formItem.fractionPrice;
                originalFormItem!.isUnitPriceChanged = formItem.isUnitPriceChanged;
                originalFormItem!.isTotalPriceChanged = formItem.isTotalPriceChanged;
                originalFormItem!.currentUnitPrice = formItem.currentUnitPrice;
                originalFormItem!.totalPriceRatio = formItem.totalPriceRatio;
                originalFormItem!.productInfo = formItem?.productInfo;
                originalFormItem!.expectedTotalPrice = formItem.expectedTotalPrice;
                originalFormItem!.expectedUnitPrice = formItem.expectedUnitPrice;
                originalFormItem!.batchInfos = formItem.batchInfos;
                if (!shouldNotCopyExpectedPrice) {
                    if (!_.isNil(formItem!.expectedTotalPrice)) {
                        originalFormItem!.expectedTotalPrice = formItem.expectedTotalPrice;
                    }
                    originalFormItem!.expectedUnitPrice = formItem.expectedUnitPrice;
                    originalFormItem!.expectedTotalPriceRatio = formItem.expectedTotalPriceRatio;
                }
            }
            // }
        }
    }
    _doUpdateCalculatePrice(event: _EventUpdateCalculatePrice): void {
        this.innerState.calculating = event.calculating;
        this.innerState.calculateFailed = event.error;
        this.update();

        if (event.calculateRspData == null) return;

        this.innerState.detailData!.totalPrice = event.calculateRspData.totalPrice;
        this.innerState.detailData!.adjustmentFee = event.calculateRspData.adjustmentFee;
        // this.innerState.detailData!.expectedTotalPrice = event.calculateRspData.expectedTotalPrice;
        this.innerState.detailData!.sourceTotalPrice = event.calculateRspData.sourceTotalPrice;
        this.innerState.detailData!.isTotalPriceChanged = event.calculateRspData.isTotalPriceChanged;
        this.innerState.detailData!.registrationFee = event.calculateRspData.registrationFee; //挂号费也进行摊费
        this.innerState.detailData!.chargeRoundingTips = _.isNil(event.calculateRspData.chargeRoundingTips)
            ? undefined
            : event.calculateRspData.chargeRoundingTips;
        this._copyPriceAttrTo(
            event.calculateRspData.productForms ?? [],
            this.innerState.detailData?.productForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionChineseForms ?? [],
            this.innerState.detailData?.prescriptionChineseForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionWesternForms ?? [],
            this.innerState.detailData?.prescriptionWesternForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionInfusionForms ?? [],
            this.innerState.detailData?.prescriptionInfusionForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionExternalForms ?? [],
            this.innerState.detailData?.prescriptionExternalForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
    }

    /**
     * 各个处方算费结果赋值到页面上
     * @param group
     * @param form
     * @private
     */
    private _calculateResultToPage(group: MedicineAddGroup, form: any): void {
        const pharmacyType = group?.chinesePrescriptionUsage?.pharmacyType ?? PharmacyType.normal;
        group?.selectedMedicines?.map((medicine) => {
            const formItem = form?.prescriptionFormItems?.find((item: any) => {
                if (!pharmacyType) return item.keyId == medicine.keyId;
                return item?.displayName == medicine?.displayName;
            });
            const inputUsage = group.inputUsages.get(medicine);
            if (!!formItem && !!inputUsage) {
                inputUsage.expectedTotalPrice = formItem?.expectedTotalPrice;
                inputUsage.expectedUnitPrice = formItem?.expectedUnitPrice;
                inputUsage.expectedTotalPriceRatio = formItem?.expectedTotalPriceRatio;
                inputUsage.totalPrice = formItem?.totalPrice;
                inputUsage.totalPriceRatio = formItem?.totalPriceRatio;
                inputUsage.unitPrice = formItem?.unitPrice;
                inputUsage.fractionPrice = formItem?.fractionPrice;
            }
        });
    }
    @actionEvent(_EventUpdateCalculatePrice)
    async *_mapEventUpdateCalculatePrice(event: _EventUpdateCalculatePrice): AsyncGenerator<State> {
        this._doUpdateCalculatePrice(event);
        const group = this.innerState.groups?.[0];
        const westernForm = this._filterWesternPrescriptionForms();
        const infusionForm = this._filterInfusionPrescriptionForms();
        const chineseForm = this._filterChinesePrescriptionForms(group?.chinesePrescriptionUsage?.pharmacyType ?? PharmacyType.normal);
        if (event.type == "western" || event.type == "chinese") {
            (event.type == "western" ? westernForm : chineseForm)?.forEach((form: any) => {
                this._calculateResultToPage(group, form);
            });
        } else {
            this.innerState.groups?.forEach((item) => {
                infusionForm?.forEach((form) => {
                    this._calculateResultToPage(item, form);
                });
            });
        }

        this.update();
    }

    @actionEvent(_EventCheckGoodsBatchInfo)
    async *_mapEventCheckGoodsBatchInfo(): AsyncGenerator<State> {
        const goodsBatchList: MedicineBatchInfoList[] = [];
        this.innerState.groups?.forEach((item) => {
            item.selectedMedicines?.forEach((medicine) => {
                const usage = item.inputUsages.get(medicine);
                let singlePrice = 0;
                if (!!usage?.unitCount && !!usage?.unit) {
                    const unitPrice = medicine.unitPriceWithUnit(usage.unit);

                    if (usage?.specialRequirement?.name !== "【自备】") {
                        singlePrice = userCenter.clinic?.isDentistryClinic
                            ? usage.expectedTotalPrice ?? usage.totalPrice ?? usage.unitCount * unitPrice + (usage.fractionPrice ?? 0)
                            : usage.unitCount * unitPrice + (usage.fractionPrice ?? 0);
                    }
                }

                if (!!medicine._batchInfos?.length) {
                    goodsBatchList.push({
                        displayName: medicine.displayName,
                        manufacturer: medicine.manufacturer,
                        unit: medicine.sellUnits[0],
                        count: usage?.unitCount,
                        totalPrice: singlePrice,
                        specifications: medicine.displaySpec,
                        goodsBatchInfoList: medicine._batchInfos,
                    });
                }
            });
        });
        if (!goodsBatchList?.length) return;
        await showBottomPanel(<MedicineBatchInfoDialog batchList={goodsBatchList} />, {
            topMaskHeight: pxToDp(160),
        });
    }

    _fillChineseUsage(usage: PrescriptionDefaultUsage | undefined): void {
        if (!usage) return;
        this.innerState.groups[0].chinesePrescriptionUsage!.freq!.name = usage.freq ?? "";
        this.innerState.groups[0].chinesePrescriptionUsage!.dailyDosage!.name = usage.dailyDosage ?? "";
        this.innerState.groups[0].chinesePrescriptionUsage!.usage!.name = usage.usage ?? "";
        this.innerState.groups[0].chinesePrescriptionUsage!.usageLevel!.name = usage.usageLevel ?? "";
    }

    _initInfusionPrescriptionUsage(infusionUsage: InfusionPrescriptionUsage): void {
        const defaultValues = this.innerState.westernMedicineConfig!.prescriptionInfusionDefaultValues;
        infusionUsage.usage = defaultValues?.usage;
        infusionUsage.freq = defaultValues?.freq;
        infusionUsage.ivgtt = defaultValues?.ivgtt;
        infusionUsage.ivgttUnit = defaultValues?.ivgttUnit;
    }

    update(): void {
        this.dispatch(new _EventUpdate());
    }

    //搜索商品
    requestGoodsList(keyword: string): void {
        this.dispatch(new _EventSearchGoods(keyword));
    }

    //添加选中的Item
    requestAddSelectList(item: GoodsInfo): void {
        this.dispatch(new _EventAddSelectList(item));
    }

    //改变页面状态
    requestChangePageShow(status: string): void {
        this.dispatch(new _EventChangePageShow(status));
    }

    // TODO 待删除
    //改变商品数量
    requestChangeGoodCount(info: { count: number }): void {
        this.dispatch(new _EventChangeGoodCount(info));
    }

    //改变中药的搜索类型
    requestChineseMedicineSpecType(type: number, forceTrans?: boolean, pharmacyType?: number): void {
        if (_.isUndefined(type)) return;
        this.dispatch(new _EventChineseMedicineSpecType(type, forceTrans, pharmacyType));
    }

    // 更新治疗项数量
    requestUpdateTreatmentAmount(medicine: GoodsInfo, count: number): void {
        this.dispatch(new _EventUpdateTreatmentAmount(medicine, count));
    }

    // 添加组
    requestAddMedicineGroup(): void {
        this.dispatch(new _EventAddMedicineGroup());
    }

    // TODO 待删除
    requestToggleSearchFocus(focus: boolean): void {
        this.dispatch(new _EventToggleSearchFocus(focus));
    }

    // TODO 待删除
    requestAddMedicine(medicine: GoodsInfo): void {
        this.dispatch(new _EventAddMedicine(medicine));
    }

    // TODO 待删除
    //修改西药用法
    requestChangeWesternMedicineUsage(group: MedicineAddGroup, medicine: GoodsInfo): Promise<void> {
        return this.dispatch(new _EventWesternUsageTap(group, medicine));
    }

    requestChangeWesternFreq(group: MedicineAddGroup, medicine: GoodsInfo): Promise<void> {
        return this.dispatch(new _EventWesternFreqTap(group, medicine));
    }

    /// 更新西药处方频率剂量单位
    requestUpdateDosageUnit(group: MedicineAddGroup, medicine: GoodsInfo, unit: string): void {
        this.dispatch(new _EventUpdateDosageUnit(group, medicine, unit));
    }

    //更改西药处方的频率剂量
    requestUpdateWesternDosageCount(group: MedicineAddGroup, medicine: GoodsInfo, dosageCount: number): void {
        this.dispatch(new _EventUpdateWesternDosageCount(group, medicine, dosageCount));
    }

    /// 选中某项
    requestFocusItem(group: MedicineAddGroup, medicine: GoodsInfo): void {
        LogUtils.d("requestFocusItem medicine = " + JSON.stringify(medicine));
        // if (!_innerState.isSelect(group, medicine)) {
        //     FocusScope.of(this.context).requestFocus(FocusNode());
        // }

        this.dispatch(new _EventFocusItem(group, medicine));
    }

    //更新西药处方的用法
    requestUpdateWesternMedicineUsage(group: MedicineAddGroup, goodsInfo: GoodsInfo, usage?: string): void {
        this.dispatch(new _EventUpdateWesternMedicineUsage(group, goodsInfo, usage));
    }

    //更新西药处方的频率
    requestUpdateWesternMedicineFreq(group: MedicineAddGroup, goodsInfo: GoodsInfo, freq?: string): void {
        this.dispatch(new _EventUpdateWesternMedicineFreq(group, goodsInfo, freq));
    }

    // 修改西药处方的天数
    requestUpdateWesternMedicineDays(group: MedicineAddGroup, medicine: GoodsInfo, days: number): void {
        this.dispatch(new _EventUpdateWesternMedicineDays(group, medicine, days));
    }

    // 更新西药的总用量的单位
    requestUpdateWesternAmountUnit(group: MedicineAddGroup, medicine: GoodsInfo, unit: string): void {
        this.dispatch(new _EventUpdateWesternAmountUnit(group, medicine, unit));
    }

    //更新西药的总量
    requestUpdateWesternAmountUnitCount(group: MedicineAddGroup, medicine: GoodsInfo, unitCount: number | string): void {
        this.dispatch(new _EventUpdateWesternAmountUnitCount(group, medicine, unitCount));
    }

    //更新西药的备注
    requestUpdateWesternRemark(group: MedicineAddGroup, medicine: GoodsInfo, remark: string): void {
        this.dispatch(new _EventUpdateWesternRemark(group, medicine, remark));
    }

    //删除备注
    requestDeleteWesternRemark(group: MedicineAddGroup, medicine: GoodsInfo): void {
        this.dispatch(new _EventDeleteWesternRemark(group, medicine));
    }

    //取消药房
    requestDeletePharmacyInfo(group: MedicineAddGroup, medicine: GoodsInfo): void {
        this.dispatch(new _EventDeletePharmacyInfo(group, medicine));
    }

    //删除
    requestDeleteMedicine(group: MedicineAddGroup | null, medicine: GoodsInfo, type: string): void {
        this.dispatch(new _EventDeleteMedicine(group, medicine, type));
    }

    //修改西药处方皮试、续用、免试等注射方法
    requestUpdateWesternMedicineAst(group: MedicineAddGroup, medicine: GoodsInfo, ast: number): void {
        this.dispatch(new _EventUpdateWesternMedicineAst(group, medicine, ast));
    }

    //点击确认处方
    requestSubmit(): void {
        this.dispatch(new _EventSubmit());
    }

    requestSelectChineseMedicineBoilMethod(medicine: GoodsInfo): void {
        this.dispatch(new _EventSelectChineseMedicineBoilMethod(medicine));
    }

    //删除中药煎法
    requestDeleteChineseMedicineBoilMethod(medicine: GoodsInfo): void {
        this.dispatch(new _EventDeleteChineseMedicineBoilMethod(medicine));
    }

    //更新单个中药的数量
    requestUpdateChineseMedicineAmount(medicine: GoodsInfo, unitCount: number | string): void {
        this.dispatch(new _EventUpdateChineseMedicineAmount(medicine, unitCount));
    }

    //更新中药处方剂数
    requestUpdateChinesePrescriptionDosageAmount(dosageCount: number): void {
        this.dispatch(new _EventUpdateChinesePrescriptionDosageAmount(dosageCount));
    }

    //更改中药处方每日剂量
    requestSelectChinesePrescriptionDailyDosage(dailyDosage: string): void {
        this.dispatch(new _EventSelectChinesePrescriptionDailyDosage(dailyDosage));
    }

    //更新中药处方频率
    requestSelectChinesePrescriptionFreq(freq: string): void {
        this.dispatch(new _EventSelectChinesePrescriptionFreq(freq));
    }

    //更新中药处方的使用级别"每次200ml"
    requestSelectChinesePrescriptionUsageLevel(usageLevel: string): void {
        this.dispatch(new _EventSelectChinesePrescriptionUsageLevel(usageLevel));
    }

    requestSelectChinesePrescriptionUsageUsageDays(usageDays: string): void {
        this.dispatch(new _EventSelectChinesePrescriptionUsageUsageDays(usageDays));
    }

    //更新中药处方的用法（煎服）
    requestSelectChinesePrescriptionUsage(usage: string): void {
        this.dispatch(new _EventSelectChinesePrescriptionUsage(usage));
    }

    //更新中药处方备注信息
    requestUpdateChinesePrescriptionRequirement(value: string): void {
        this.dispatch(new _EventUpdateChinesePrescriptionRequirement(value));
    }

    // TODO 待删除
    //中药处方代煎电话
    requestUpdateChinesePrescriptionBoilServiceMobile(value: string): void {
        this.dispatch(new _EventUpdateChinesePrescriptionBoilServiceMobile(value));
    }

    // TODO 待删除
    /// 添加一组
    requestPrescriptionGroupAdd(): void {
        this.dispatch(new _EventPrescriptionGroupAdd());
    }

    /// 选择输液处方的用法
    requestSelectInfusionPrescriptionUsage(group: MedicineAddGroup, value?: string): void {
        this.dispatch(new _EventSelectInfusionPrescriptionUsage(group, value));
    }

    ///输液处方选择频率
    requestSelectInfusionPrescriptionFeq(group: MedicineAddGroup, value?: string): void {
        this.dispatch(new _EventSelectInfusionPrescriptionFreq(group, value));
    }

    /// 更新输液处方天数
    requestUpdateInfusionPrescriptionDays(group: MedicineAddGroup, days: number): void {
        this.dispatch(new _EventUpdateInfusionPrescriptionDays(group, days));
    }

    requestUpdateInfusionPrescriptionIvgtt(group: MedicineAddGroup, ivgtt: number): void {
        this.dispatch(new _EventUpdateInfusionPrescriptionIvgtt(group, ivgtt));
    }

    requestUpdateInfusionMedicineSkinTest(group: MedicineAddGroup, medicine: GoodsInfo, value: boolean): void {
        this.dispatch(new _EventUpdateInfusionMedicineSkinTest(group, medicine, value));
    }

    requestUpdateInfusionMedicineDosageUnit(group: MedicineAddGroup, medicine: GoodsInfo, unit: string): void {
        this.dispatch(new _EventUpdateInfusionMedicineDosageUnit(group, medicine, unit));
    }

    requestUpdateInfusionMedicineDosageUnitCount(group: MedicineAddGroup, medicine: GoodsInfo, unitCount: number): void {
        this.dispatch(new _EventUpdateInfusionMedicineDosageUnitCount(group, medicine, unitCount));
    }

    requestUpdateInfusionMedicineUnitCount(group: MedicineAddGroup, medicine: GoodsInfo, unitCount: number | string): void {
        this.dispatch(new _EventUpdateInfusionMedicineUnitCount(group, medicine, unitCount));
    }

    requestUpdateInfusionMedicineUnit(group: MedicineAddGroup, medicine: GoodsInfo, unit: string): void {
        this.dispatch(new _EventUpdateInfusionMedicineUnit(group, medicine, unit));
    }

    //刷新指定组下药品的数量
    _refreshGroupMedicineAmount(group: MedicineAddGroup): void {
        let freq = this.innerState.westernMedicineConfig?.freq?.find((item) => group.infusionPrescriptionUsage?.freq == item.en);
        if (!freq) {
            const _freq = this.innerState.westernMedicineConfig?.freq
                ?.filter((item) => item.isCustom)
                .find((item) => StringUtils.checkEndChar(item.en ?? "", group.infusionPrescriptionUsage?.freq ?? ""));
            if (!!_freq) {
                const time = Number(StringUtils.removeHeadAndTail(group.infusionPrescriptionUsage?.freq ?? "") ?? 0);
                freq = { ..._freq, time: time * (_freq.hMultiple ?? 0) };
            }
        }
        for (const medicine of group.selectedMedicines) {
            const usage = group.inputUsages.get(medicine) ?? new MedicineUsage();
            usage!
                .computeUnitCount({
                    goodsInfo: medicine,
                    usage,
                    freq: freq,
                    days: group.infusionPrescriptionUsage?.days,
                })
                .then(() => {
                    this._clearAdjustmentFee(medicine, "infusion");
                });
        }
    }

    /// 更新药品的总量单位
    requestSelectMedicineUnit(medicine: GoodsInfo): void {
        this.dispatch(new _EventSelectMedicineUnit(medicine));
    }

    // TODO 待删除
    // 更新中药类型（饮片，颗粒)
    requestChangeChineseSpecType(specType: number): void {
        this.dispatch(new _EventChangeChineseSpecType(specType));
    }

    // TODO 待删除
    //选中组
    requestSelectGroup(group: MedicineAddGroup): void {
        this.dispatch(new _EventSelectGroup(group));
    }

    //删除组
    requestDeleteGroup(group: MedicineAddGroup): void {
        this.dispatch(new _EventDeleteGroup(group));
    }

    // TODO 待删除
    //中药药方代煎
    requestUpdateBoilServiceSwitch(check: boolean): void {
        this.dispatch(new _EventUpdateBoilServiceSwitch(check));
    }

    //保存页面中的输入框组件
    requestCacheSearchInput(element?: AbcTextInput | null): void {
        if (_.isUndefined(element)) return;
        this.dispatch(new _EventCacheSearchInput(element));
    }

    requestUpdateWesternMedicineGroupIndex(group: MedicineAddGroup, medicine: GoodsInfo): void {
        this.dispatch(new _EventUpdateWesternMedicineGroupIndex(group, medicine));
    }

    //取消分组（删除存在的分组，恢复成默认状态）
    requestDeleteWesternMedicineGroupIndex(): void {
        this.dispatch(new _EventDeleteWesternMedicineGroupIndex());
    }

    requestUpdateSearchbarStatus(focus: boolean): void {
        this.dispatch(new _EventContinueSearch(focus));
    }

    requestBackPage(): void {
        this.dispatch(new _EventBackPage());
    }

    requestRecordPartLayout(key: string, layoutY: number): void {
        this.dispatch(new _EventRecordPartLayout(key, layoutY));
    }

    requestChangeArrangement(): void {
        this.dispatch(new _EventChangeArrangement());
    }

    requestModifyPharmacyType(): void {
        this.dispatch(new _EventModifyPharmacyType());
    }

    // 修改药房类型
    requestNewModifyPharmacyType(): void {
        this.dispatch(new _EventNewModifyPharmacyType());
    }

    requestUpdateWesternAst(group: MedicineAddGroup, medicine: GoodsInfo, status: boolean): void {
        this.dispatch(new _EventUpdateInfusionMedicineSkinTest(group, medicine, status));
    }

    //添加药品
    requestAddMedicinePrescription(goodsInfo: GoodsInfo, group?: MedicineAddGroup): void {
        this.dispatch(new _EventAddMedicinePrescription(goodsInfo, group));
    }

    //中药处方
    requestChineseMedicine(): void {
        this.dispatch(new _EventAddChineseMedicine());
    }

    //中药处方--用法
    requestAddChineseMedicineUsage(group?: MedicineAddGroup, tabType?: MedicineChinesePanelType): void {
        this.dispatch(new _EventAddChineseMedicineUsage(group, tabType));
    }

    requestModifyEditGroupState(status: boolean): void {
        this.dispatch(new _EventModifyEditGroupState(status));
    }

    requestModifyEditMedicineUsage(group: MedicineAddGroup, medicine: GoodsInfo): void {
        this.dispatch(new _EventModifyEditMedicineUsage(group, medicine));
    }

    requestModifyInfusionGroupUsage(group: MedicineAddGroup): void {
        this.dispatch(new _EventModifyInfusionGroupUsage(group));
    }

    requestSaveTemplate(): void {
        this.dispatch(new _EventSaveTemplate());
    }

    //更新空中药房--药态选中信息
    requestUpdateMedicineStateScopeId(): void {
        this.dispatch(new _EventUpdateMedicineStateScopedId());
    }

    // 复制模板
    requestCopyTemplate(type?: MedicineAddType): void {
        this.dispatch(new _EventCopyTemplate(type));
    }

    //修改精麻类型
    requestModifyPrescriptionNarcoticType(type?: number): void {
        this.dispatch(new _EventModifyPrescriptionNarcoticType(type));
    }

    //修改处方类型
    requestSelectPrescriptionType(): void {
        this.dispatch(new _EventModifyPrescriptionType());
    }

    //多药房--切换饮片/颗粒类型
    requestChineseSwitchType(): void {
        this.dispatch(new _EventChineseSwitchType());
    }

    /**
     * 修改药品来源药房
     * @param medicine
     * @param pharmacy
     */
    requestModifyMedicinePharmacy(medicine: GoodsInfo, pharmacy: PharmacyListConfig, group?: MedicineAddGroup, type?: string): void {
        this.dispatch(new _EventModifyMedicinePharmacy(medicine, pharmacy, undefined, group, type));
    }

    /**
     * 修改药品自备状态
     * @param medicine
     * @param status
     */
    requestModifyMedicineSelfProvidedStatus(
        medicine: GoodsInfo,
        status: DispensingFormItemsSourceItemType,
        group?: MedicineAddGroup
    ): void {
        this.dispatch(new _EventModifyMedicineSelfProvidedStatus(medicine, status, group));
    }

    //空中药房--快递选择
    requestSelectAirExpress(pharmacyType: PharmacyType): void {
        this.dispatch(new _EventSelectAirExpress(pharmacyType));
    }

    //单项议折扣
    requestAdjustDiscount(medicine: GoodsInfo, discount: number, type: string): void {
        this.dispatch(new _EventAdjustDiscount(medicine, discount, type));
    }
    //单项议总价
    requestAdjustTotalPrice(medicine: GoodsInfo, totalPrice: number, type: string): void {
        this.dispatch(new _EventAdjustTotalPrice(medicine, totalPrice, type));
    }
    // 查看药品批次信息
    requestCheckGoodsBatchInfo(): void {
        this.dispatch(new _EventCheckGoodsBatchInfo());
    }
}

export { MedicineAddPageBloc, State, ScrollToFocusItemState };
