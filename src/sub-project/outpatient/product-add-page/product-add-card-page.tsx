import React from "react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { Text, View } from "@hippy/react";
import { CustomInput } from "../../base-ui/input/custom-input";
import { PrecisionLimitFormatter, ZeroForbidden } from "../../base-ui/utils/formatter";
import { IconFontView, SizedBox } from "../../base-ui";
import { GoodsInfo } from "../../base-business/data/beans";
import { ABCUtils } from "../../base-ui/utils/utils";
import { ProductAddPageBloc, ScrollToFocusItemState } from "./product-add-page-bloc";
import { BaseComponent } from "../../base-ui/base-component";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { MedicineUsage } from "../medicine-add-page/medicine-add-page-bean";
import { ActionBtn, StockNotEnoughTextView } from "../../base-ui/views/stock-not-enough-text-view";
import _ from "lodash";
import { AbcView } from "../../base-ui/views/abc-view";
import { userCenter } from "../../user-center";
import { PharmacyTagView } from "../views/pharmacy-tag-view";
import { AbcToothView } from "../../base-ui/abc-app-library/tooth-panel/abc-tooth-view";
import abcI18Next from "../../language/config";
import { PsychotropicNarcoticTagView } from "../../views/business-tags";
import { ExternalMedicineFreqKeyboard } from "../../base-ui/picker/medicine-usage-picker";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { ChargeFormStatus } from "../../charge/data/charge-beans";
import { DiagnosisTreatmentConfig, TreatmentUsageMethod } from "../../data/online-property-config-provder";
import { MedicineAddType } from "../data/outpatient-const";

interface ProductAddCardPageProps {
    medicine: GoodsInfo;
    usage: MedicineUsage;
    showErrorHint?: boolean;
    departmentId?: string;
    isOpenSupportInputDays?: boolean;
    disableAddPrescription?: boolean;
    diagnosisTreatment?: DiagnosisTreatmentConfig;
}
export class ProductAddCardPage extends BaseComponent<ProductAddCardPageProps, any> {
    private _productAddInput?: AbcTextInput | null;
    private _productSupportFreqInput?: AbcTextInput | null;
    private _productSupportDailyDosageInput?: AbcTextInput | null;
    private _productSupportDaysInput?: AbcTextInput | null;
    private _stockNotEnoughView?: StockNotEnoughTextView | null;
    private _isShowRemark?: boolean;
    private _treatmentRef?: CustomInput | null;
    private _isRemarkFocus?: boolean = false; //备注是否处于聚焦状态
    constructor(props: ProductAddCardPageProps) {
        super(props);
    }

    static contextType = ProductAddPageBloc.Context;

    componentDidMount(): void {
        ProductAddPageBloc.fromContext(this.context)
            .state.subscribe((state) => {
                const select = state.isSelect(ABCUtils.first(state.groups), this.props.medicine);
                if (state instanceof ScrollToFocusItemState) {
                    if (select) {
                        this._productAddInput?.focus();
                    }
                }
            })
            .addToDisposableBag(this);
    }

    //删除备注
    deleteRemark(): void {
        this._isShowRemark = false;
        this._stockNotEnoughView?._hideTips();
        const { medicine, usage } = this.props;
        const _bloc = ProductAddPageBloc.fromContext(this.context);
        _bloc.requestUpdateTreatmentRemark(medicine, usage.specialRequirement?.name ?? "", true);
    }

    //选择穴位
    selectAcupuncturePoints(): void {
        const { medicine } = this.props;
        const _bloc = ProductAddPageBloc.fromContext(this.context);
        _bloc.requestSelectAcupuncturePoints(medicine);
    }

    render(): JSX.Element {
        const { medicine, usage, showErrorHint, isOpenSupportInputDays, departmentId, disableAddPrescription, diagnosisTreatment } =
            this.props;
        const _bloc = ProductAddPageBloc.fromContext(this.context);
        const _count = usage?.unitCount;
        const _unit = usage?.unit;
        let _remark = usage?.specialRequirement?.name;
        const acupunturePointsStr = usage?.acuPoints?.map((item) => item.name).join();
        _remark = !!acupunturePointsStr ? `${!!_remark ? `${_remark};` : ""}[穴位]${acupunturePointsStr};` : _remark;
        const stockInfo = medicine.stockInfo(usage?.unit, usage?.unitCount, 1);

        const pharmacyInfo = usage.pharmacyInfo,
            isOpenMultiplePharmacy = _bloc.currentState.pharmacyInfoConfig?.isOpenMultiplePharmacy,
            defaultPharmacyNo = _bloc.currentState.pharmacyInfoConfig?.getDefaultPharmacy({
                departmentId: departmentId,
                goodsInfo: { typeId: medicine?.typeId },
            })?.no;
        const isShowPharmacyTag = isOpenMultiplePharmacy && defaultPharmacyNo != pharmacyInfo?.no && medicine.isCanSpecifyPharmacy;

        const remarkContent = !!_remark || isShowPharmacyTag;
        //治疗理疗支持录入天数
        const _supportDays = usage.supportInputDays?.days,
            _supportDailyDosage = usage.supportInputDays?.dailyDosage,
            _supportFreq = usage.supportInputDays?.freq;
        let isShowFreq = true;
        let isShowDailyDosage = true;
        let isShowDays = true;
        let dailyDosagePlaceholder = "";
        //是否显示治疗理疗天数（开启了项目录入天数+项目为治疗理疗）
        let isShowProductDays = (medicine.isTreatment || medicine.isPhysiotherapy) && isOpenSupportInputDays;
        //单项折扣议价
        const discountRation = Number(((usage?.expectedTotalPriceRatio ?? usage?.totalPriceRatio ?? 0) * 100).toFixed(0));
        //单项总价
        const totalPrice = ABCUtils.formatPrice(
            usage?.expectedTotalPrice ?? usage?.totalPrice ?? (usage.unitCount ?? 0) * (usage?.unitPrice ?? 0)
        );
        //原始单价
        const originalUnitPrice = `${ABCUtils.formatPrice(usage?.sourceUnitPrice ?? usage?.unitPrice ?? 0)}/${usage?.unit ?? ""}`;
        // 检查项目已完成，则不可编辑
        const disabledItem = medicine.__hasResultExaminations;
        // 商品(支持拆零并且是开小单位的商品)允许小数开出，最多允许输入两位小数，耗材不用管是否拆零都支持两位小数,其他只能输入整数
        const isAllowDecimalLength =
            (medicine?.canDismounting && medicine.isGoods && usage?.unit == medicine.pieceUnit) || medicine.isMaterial ? 2 : 0;
        // 只有未收费的项目才能编辑，已收费的项目不能编辑
        const isCanEdit = !disableAddPrescription || medicine._chargeStatus == ChargeFormStatus.unCharged;

        // 如果是检查检验项目 MedicineAddType.examination 不展示 freq dailyDosage
        if (medicine.type === MedicineAddType.examination) {
            isShowFreq = false;
            isShowProductDays = false;
            isShowDailyDosage = false;
            isShowDays = false;
        }
        switch (diagnosisTreatment?.supportInputDays) {
            case TreatmentUsageMethod.NoUsage:
                // 保持原样
                isShowFreq = false;
                isShowProductDays = false;
                isShowDailyDosage = false;
                isShowDays = false;
                break;
            case TreatmentUsageMethod.DailyTreatment:
                // 每天治疗数量、治疗天数
                isShowFreq = false; // 频率
                isShowProductDays = true; // 治疗天数
                dailyDosagePlaceholder = "天";
                break;
            case TreatmentUsageMethod.FullUsage:
                // 完整用法（治疗频率、单次治疗数量、治疗天数）
                isShowProductDays = true; // 治疗天数
                dailyDosagePlaceholder = "次";
                break;
        }

        return (
            <AbcView
                style={[
                    ABCStyles.bottomLine,
                    {
                        marginHorizontal: Sizes.dp8,
                        backgroundColor: Colors.white,
                        paddingVertical: Sizes.dp16,
                        paddingHorizontal: Sizes.dp8,
                        flexGrow: 1,
                        borderBottomWidth: 0.5,
                    },
                ]}
                onLongClick={() => isCanEdit && this._stockNotEnoughView?.showOperateTip()}
            >
                <View style={ABCStyles.rowAlignCenterSpaceBetween}>
                    <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                        {!!userCenter.clinic?.isDentistryClinic && (
                            <View style={{ alignSelf: "stretch", marginRight: Sizes.dp8 }}>
                                <AbcToothView
                                    width={_bloc.currentState.toothMaxWidth}
                                    toothNos={usage.toothNos}
                                    copyToothNos={_bloc.currentState.canCopyToothList}
                                    onChange={(tooth) => {
                                        isCanEdit && _bloc.requestModifyGoodsToothNos(medicine, tooth);
                                    }}
                                />
                            </View>
                        )}
                        <Text style={[TextStyles.t16NT3.copyWith({ color: Colors.t3, lineHeight: Sizes.dp22 })]}>
                            {`${medicine.isGoods ? "商品" : medicine.displayTypeShortName}`}
                        </Text>
                        <StockNotEnoughTextView
                            style={{ flexShrink: 1 }}
                            goodsStock={stockInfo}
                            text={medicine.name}
                            textStyle={{
                                ...TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22 }),
                                marginHorizontal: Sizes.dp4,
                            }}
                            ref={(ref) => {
                                this._stockNotEnoughView = ref;
                            }}
                            tipContent={this._createActionBtn()}
                            bgColor={"rgba(51, 51, 51, 0.95)"}
                            hasOtherOperate={!disabledItem && isCanEdit}
                        />
                        <PsychotropicNarcoticTagView dangerIngredient={medicine.getIngredientArray} antibiotic={medicine.getAntibiotic} />
                    </View>
                    {!this._isShowRemark && !remarkContent && !isShowProductDays && !userCenter.clinic?.isDentistryClinic && (
                        <View style={[ABCStyles.rowAlignCenter]} onClick={() => ({})}>
                            <CustomInput
                                style={{ width: Sizes.dp60 }}
                                disable={!!disabledItem || !isCanEdit}
                                borderType={"boxBorder"}
                                type={"input"}
                                value={_count}
                                ref={(ref) => {
                                    this._productAddInput = ref?._textInput;
                                }}
                                error={showErrorHint && !_.isNumber(_count)}
                                formatter={PrecisionLimitFormatter(isAllowDecimalLength)}
                                textStyle={TextStyles.t13NT2.copyWith({ color: Colors.T1 })}
                                alwaysShowUtil={true}
                                unit={_unit}
                                unitInPageLength={1}
                                unitList={medicine.sellUnits}
                                onChangeUnit={(unit) => {
                                    isCanEdit && _bloc.requestModifyGoodsUnit(medicine, unit);
                                }}
                                onChange={(value) =>
                                    isCanEdit && _bloc.requestUpdateTreatmentAmount(medicine, !!value.length ? Number(value) : "")
                                }
                                onEndEditing={() => {
                                    isCanEdit && _bloc.requestUpdateSearchbarStatus(true);
                                }}
                                selectTextOnFocus={false}
                                containerStyle={{ height: Sizes.dp32 }}
                            />
                        </View>
                    )}
                    {userCenter.clinic?.isDentistryClinic && (
                        <Text style={[TextStyles.t14NT1]}>{`${abcI18Next.t("￥")}${originalUnitPrice}`}</Text>
                    )}
                </View>
                {isShowProductDays && (
                    <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp16 }]}>
                        {isShowFreq ? (
                            <CustomInput
                                ref={(ref) => (this._productSupportFreqInput = ref?._textInput)}
                                style={{ flex: 78 }}
                                borderType={"boxBorder"}
                                type={"sheet"}
                                value={_supportFreq}
                                placeholder={"频率"}
                                textStyle={TextStyles.t13NT2.copyWith({ color: Colors.T1 })}
                                customKeyboardBuilder={new ExternalMedicineFreqKeyboard({ name: _supportFreq })}
                                onChange={(value) => isCanEdit && _bloc.requestUpdateProductFreq(medicine, !!value.length ? value : "")}
                                selectTextOnFocus={false}
                                containerStyle={{ height: Sizes.dp32 }}
                                notExistValue={!_supportFreq}
                                onEndEditing={(text) => {
                                    isCanEdit &&
                                        !!text &&
                                        delayed(100).subscribe(() => {
                                            this._productSupportDailyDosageInput?.focus();
                                        });
                                }}
                            />
                        ) : (
                            <View style={{ flex: 78 }} />
                        )}
                        <SizedBox width={Sizes.dp6} />
                        {isShowDailyDosage ? (
                            <CustomInput
                                ref={(ref) => (this._productSupportDailyDosageInput = ref?._textInput)}
                                style={{ flex: 75 }}
                                disable={false}
                                borderType={"boxBorder"}
                                type={"input"}
                                value={_supportDailyDosage}
                                placeholder={`每${dailyDosagePlaceholder}`}
                                placeholderColor={Colors.T4}
                                formatter={PrecisionLimitFormatter(0)}
                                textStyle={TextStyles.t13NT2.copyWith({ color: Colors.T1 })}
                                alwaysShowUtil={true}
                                unit={(_unit?.length ?? 0) > 2 ? _unit?.slice(0, 2) : _unit}
                                unitList={medicine.sellUnits}
                                onChangeUnit={(unit) => {
                                    isCanEdit && _bloc.requestModifyGoodsUnit(medicine, unit);
                                }}
                                onChange={(value) =>
                                    isCanEdit && _bloc.requestUpdateProductDailyDosage(medicine, !!value.length ? Number(value) : "")
                                }
                                onEndEditing={(text) => {
                                    isCanEdit &&
                                        !!text &&
                                        delayed(100).subscribe(() => {
                                            this._productSupportDaysInput?.focus();
                                        });
                                }}
                                selectTextOnFocus={false}
                                containerStyle={{ height: Sizes.dp32 }}
                            />
                        ) : (
                            <View style={{ flex: 75 }} />
                        )}

                        <SizedBox width={Sizes.dp6} />
                        {isShowDays ? (
                            <CustomInput
                                ref={(ref) => (this._productSupportDaysInput = ref?._textInput)}
                                style={{ flex: 66 }}
                                disable={false}
                                borderType={"boxBorder"}
                                type={"input"}
                                value={_supportDays}
                                error={showErrorHint && !_.isNumber(_supportDays)}
                                formatter={[PrecisionLimitFormatter(0), ZeroForbidden()]}
                                textStyle={TextStyles.t13NT2.copyWith({ color: Colors.T1 })}
                                alwaysShowUtil={true}
                                unit={"天"}
                                onChange={(value) =>
                                    isCanEdit && _bloc.requestUpdateProductDays(medicine, !!value.length ? Number(value) : "")
                                }
                                onEndEditing={(text) => {
                                    isCanEdit &&
                                        !!text &&
                                        delayed(100).subscribe(() => {
                                            this._productAddInput?.focus();
                                        });
                                }}
                                selectTextOnFocus={false}
                                containerStyle={{ height: Sizes.dp32 }}
                            />
                        ) : (
                            <View style={{ flex: 66 }} />
                        )}
                        <SizedBox width={Sizes.dp6} />
                        {!userCenter.clinic?.isDentistryClinic && (
                            <CustomInput
                                style={{ width: Sizes.dp102 }}
                                disable={false}
                                borderType={"boxBorder"}
                                type={"input"}
                                value={_count}
                                ref={(ref) => {
                                    this._productAddInput = ref?._textInput;
                                }}
                                error={showErrorHint && !_.isNumber(_count)}
                                formatter={PrecisionLimitFormatter(isAllowDecimalLength)}
                                textStyle={TextStyles.t13NT2.copyWith({ color: Colors.T1 })}
                                alwaysShowUtil={true}
                                unit={_unit}
                                unitList={medicine.sellUnits}
                                onChangeUnit={(unit) => {
                                    isCanEdit && _bloc.requestModifyGoodsUnit(medicine, unit);
                                }}
                                onChange={(value) =>
                                    isCanEdit && _bloc.requestUpdateTreatmentAmount(medicine, !!value.length ? Number(value) : "")
                                }
                                onEndEditing={() => {
                                    isCanEdit && _bloc.requestUpdateSearchbarStatus(true);
                                }}
                                selectTextOnFocus={false}
                                containerStyle={{ height: Sizes.dp32 }}
                                unitStyle={{ maxWidth: Sizes.dp54 }}
                            />
                        )}
                    </View>
                )}
                {userCenter.clinic?.isDentistryClinic && (
                    <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp16 }]}>
                        <CustomInput
                            style={{ width: Sizes.dp60 }}
                            disable={false}
                            borderType={"boxBorder"}
                            type={"input"}
                            value={_count}
                            ref={(ref) => {
                                this._productAddInput = ref?._textInput;
                            }}
                            error={showErrorHint && !Boolean(_count)}
                            formatter={PrecisionLimitFormatter(isAllowDecimalLength)}
                            onChange={(value) => isCanEdit && _bloc.requestUpdateTreatmentAmount(medicine, Number(value))}
                            onEndEditing={() => {
                                isCanEdit && _bloc.requestUpdateSearchbarStatus(true);
                            }}
                            alwaysShowUtil={true}
                            textStyle={TextStyles.t13NT2.copyWith({ color: Colors.T1 })}
                            unit={_unit}
                            unitInPageLength={1}
                            unitList={medicine.sellUnits}
                            onChangeUnit={(unit) => {
                                isCanEdit && _bloc.requestModifyGoodsUnit(medicine, unit);
                            }}
                            selectTextOnFocus={false}
                            containerStyle={{ height: Sizes.dp32 }}
                        />
                        <SizedBox width={Sizes.dp6} />
                        <CustomInput
                            style={{ width: Sizes.dp127 }}
                            disable={false}
                            borderType={"boxBorder"}
                            type={"input"}
                            value={discountRation > 100 ? "-" : discountRation}
                            formatter={(oldValue, newValue) => {
                                const value = Number(newValue);
                                if (isNaN(value)) return "";
                                if (value < 0 || value > 100) return oldValue;
                                return newValue;
                            }}
                            onChange={(value) => {
                                if (!value) return;
                                isCanEdit && _bloc.requestAdjustDiscount(medicine, Number(value));
                            }}
                            alwaysShowUtil={true}
                            textStyle={TextStyles.t13NT2.copyWith({ color: Colors.T1 })}
                            unit={"%"}
                            selectTextOnFocus={false}
                            containerStyle={{ height: Sizes.dp32 }}
                        />
                        <SizedBox width={Sizes.dp6} />
                        <CustomInput
                            style={{ width: Sizes.dp128 }}
                            startView={() => <Text style={TextStyles.t13NT1}>{abcI18Next.t("¥")}</Text>}
                            disable={false}
                            borderType={"boxBorder"}
                            type={"input"}
                            value={totalPrice}
                            formatter={PrecisionLimitFormatter(2)}
                            onChange={(value) => {
                                if (!value) return;
                                isCanEdit && _bloc.requestAdjustTotalPrice(medicine, Number(value));
                            }}
                            alwaysShowUtil={true}
                            textStyle={TextStyles.t13NT2.copyWith({ color: Colors.T1 })}
                            selectTextOnFocus={false}
                            containerStyle={{ height: Sizes.dp32 }}
                        />
                    </View>
                )}
                {(!!this._isShowRemark || !!remarkContent) && (
                    <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp16 }]}>
                        <View
                            style={[
                                ABCStyles.rowAlignCenter,
                                {
                                    backgroundColor: this._isRemarkFocus ? Colors.theme2Mask8 : Colors.whiteSmoke,
                                    paddingRight: Sizes.dp8,
                                    borderRadius: Sizes.dp4,
                                    borderWidth: this._isRemarkFocus ? Sizes.dpHalf : undefined,
                                    borderColor: Colors.mainColor,
                                    flex: 1,
                                },
                            ]}
                        >
                            {isShowPharmacyTag && (
                                <PharmacyTagView
                                    pharmacyNo={pharmacyInfo?.no}
                                    defaultPharmacyNo={defaultPharmacyNo}
                                    style={{ marginLeft: Sizes.dp8 }}
                                />
                            )}
                            <CustomInput
                                ref={(ref) => {
                                    this._treatmentRef = ref;
                                }}
                                style={{
                                    flex: 1,
                                    textAlign: "left",
                                    paddingLeft: Sizes.dp8,
                                }}
                                type={"input-text"}
                                borderType={"none"}
                                value={`${_remark ?? ""}`}
                                onChange={(value) => isCanEdit && _bloc.requestUpdateTreatmentRemark(medicine, value)}
                                autoFocus={this._isRemarkFocus}
                                onFocus={() => {
                                    this._isRemarkFocus = true;
                                    this.setState({});
                                }}
                                onBlur={() => {
                                    this._isRemarkFocus = false;
                                    this.setState({});
                                }}
                                containerStyle={{ height: this._isRemarkFocus ? Sizes.dp30 : Sizes.dp32 }}
                            />
                            {/*<SizedBox width={Sizes.dp13} />*/}
                            {this._isRemarkFocus && (
                                <IconFontView
                                    style={{ paddingLeft: Sizes.dp13 }}
                                    name={"meridian"}
                                    size={Sizes.dp18}
                                    color={Colors.mainColor}
                                    onClick={() => isCanEdit && this.selectAcupuncturePoints()}
                                />
                            )}
                        </View>
                        {!userCenter.clinic?.isDentistryClinic && <SizedBox width={Sizes.dp6} />}
                        {!isShowProductDays && !userCenter.clinic?.isDentistryClinic ? (
                            <CustomInput
                                style={{ width: Sizes.dp60 }}
                                disable={!!disabledItem}
                                borderType={"boxBorder"}
                                type={"input"}
                                value={_count}
                                ref={(ref) => {
                                    this._productAddInput = ref?._textInput;
                                }}
                                error={showErrorHint && !Boolean(_count)}
                                formatter={PrecisionLimitFormatter(isAllowDecimalLength)}
                                onChange={(value) => isCanEdit && _bloc.requestUpdateTreatmentAmount(medicine, Number(value))}
                                onEndEditing={() => {
                                    isCanEdit && _bloc.requestUpdateSearchbarStatus(true);
                                }}
                                alwaysShowUtil={true}
                                textStyle={TextStyles.t13NT2.copyWith({ color: Colors.T1 })}
                                unit={_unit}
                                unitList={medicine.sellUnits}
                                onChangeUnit={(unit) => {
                                    isCanEdit && _bloc.requestModifyGoodsUnit(medicine, unit);
                                }}
                                selectTextOnFocus={false}
                                containerStyle={{ height: Sizes.dp32 }}
                            />
                        ) : (
                            <View style={{ width: !userCenter.clinic?.isDentistryClinic ? Sizes.dp60 : 0 }} />
                        )}
                    </View>
                )}
            </AbcView>
        );
    }

    private _createActionBtn(): ActionBtn[] {
        const { usage, medicine } = this.props;
        const inventoryClinicConfig = userCenter.inventoryClinicConfig;
        let _remark = usage?.specialRequirement?.name;
        const acupunturePointsStr = usage?.acuPoints?.map((item) => item.name).join();
        _remark = !!acupunturePointsStr ? `${_remark}${acupunturePointsStr}` : _remark;
        const _bloc = ProductAddPageBloc.fromContext(this.context),
            group = _bloc.currentState.groups[0];
        const list: ActionBtn[] = [];
        if (!_.isEmpty(_remark) || this._isShowRemark) {
            list.push({
                text: "取消备注",
                handleClick: () => {
                    this.deleteRemark();
                },
            });
        } else {
            list.push({
                text: "备注",
                handleClick: () => {
                    this._isShowRemark = true;
                    this._treatmentRef?.focus();
                    this._isRemarkFocus = true;
                    this._stockNotEnoughView?._hideTips();
                    this.forceUpdate();
                },
            });
        }

        //切换药房处理
        if (inventoryClinicConfig?.isOpenMultiplePharmacy && medicine.isCanSpecifyPharmacy) {
            list.push({
                text: "药品来源",
                handleClick: () => {
                    _bloc.requestModifyGoodsPharmacy(medicine);
                },
            });
        }
        list.push({
            text: "删除",
            handleClick: () => {
                _bloc.requestDeleteMedicine(group, medicine);
            },
        });
        return list;
    }
}
