/**
 * create by dengjie
 * desc:
 * create date 2020/5/13
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import {
    OutpatientInvoiceDetail,
    PrescriptionChineseForm,
    PrescriptionExternalForm,
    PrescriptionFormItem,
    PrescriptionInfusionForm,
    PrescriptionProductForm,
    PrescriptionWesternForm,
} from "./data/outpatient-beans";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { OutpatientUtils } from "./utils/outpatient-utils";
import { Subject } from "rxjs";
import { debounceTime, switchMap } from "rxjs/operators";
import { fromPromise } from "rxjs/internal-compatibility";
import { OutpatientAgent } from "./data/outpatient";
import { ABCUtils } from "../base-ui/utils/utils";
import { Toast } from "../base-ui/dialog/toast";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { BargainingBusinessKey, ChargeBargainingRule, ChargeConfig, MatchType } from "../charge/data/charge-beans";
import { errorToStr, UUIDGen } from "../common-base-module/utils";
import {
    GetClinicBasicSetting,
    OnlinePropertyConfigProvider,
    OutpatientDataPermissionGoodsPriceType,
} from "../data/online-property-config-provder";
import _ from "lodash";
import { PharmacyType } from "../charge/data/charge-bean-air-pharmacy";
import { actionEvent } from "../bloc/bloc";
import { ClinicAgent, EmployeesMeConfig } from "../base-business/data/clinic-agent";
import { ABCError } from "../common-base-module/common-error";
import { userCenter } from "../user-center";
import { ChargeAgent } from "../charge/data/charge-agent";

class State {
    originalData?: OutpatientInvoiceDetail;

    detailData?: OutpatientInvoiceDetail;

    calculating = false;
    calculateFailed: any; //算费失败错误

    //算费配置
    chargeConfig?: ChargeConfig;

    employeesMeConfig?: EmployeesMeConfig;

    clinicBasicSetting?: GetClinicBasicSetting;
    bargainingRules?: ChargeBargainingRule[];
    // 当前登录者是否有门诊单项议价权限
    get hasSingleBargainPermission(): boolean {
        // 首先需要开启了门诊单项议价
        if (!this.doctorShouldSingleBargain) return false;
        // 管理员不受控制
        if (userCenter.clinic?.isManager) return true;
        // 有门诊议价权限的可议价
        if (!this.chargeConfig?.doctorAssignEmployeeBargain) return true;
        // 指定人员议价
        const rule = this.bargainingRules?.find((rule) => rule.businessKey == BargainingBusinessKey.outpatientAdjustEmployeeMatch);
        if (!rule) return false;
        return (
            rule.productTypeMatches?.some(
                (match) =>
                    match.type == MatchType.EMPLOYEE && match.typeRefId == (userCenter.employee?.id ?? userCenter.employee?.employeeId)
            ) ?? false
        );
    }

    get doctorShouldBargain(): boolean {
        if (this.chargeConfig?.doctorBargainSwitch == 0) return false;
        // 管理员不受控制
        if (userCenter.clinic?.isManager) return true;
        if (!this.chargeConfig?.doctorAssignEmployeeBargain) return true;
        // 指定人员议价
        const rule = this.bargainingRules?.find((rule) => rule.businessKey == BargainingBusinessKey.outpatientAdjustEmployeeMatch);
        if (!rule) return false;
        return (
            rule.productTypeMatches?.some(
                (match) =>
                    match.type == MatchType.EMPLOYEE && match.typeRefId == (userCenter.employee?.id ?? userCenter.employee?.employeeId)
            ) ?? false
        );
    }
    // 是否有挂号费议价权限
    get hasRegistrationFeeBargainPermission(): boolean {
        if (this.chargeConfig?.doctorRegisteredBargainSwitch == 0) return false;
        // 管理员不受控制
        if (userCenter.clinic?.isManager) return true;
        if (!this.chargeConfig?.doctorAssignEmployeeBargain) return true;
        // 指定人员议价
        const rule = this.bargainingRules?.find((rule) => rule.businessKey == BargainingBusinessKey.outpatientAdjustEmployeeMatch);
        if (!rule) return false;
        return (
            rule.productTypeMatches?.some(
                (match) =>
                    match.type == MatchType.EMPLOYEE && match.typeRefId == (userCenter.employee?.id ?? userCenter.employee?.employeeId)
            ) ?? false
        );
    }

    get doctorShouldSingleBargain(): boolean {
        return this.chargeConfig?.doctorSingleBargainSwitch != 0;
    }

    get doctorShouldSingleGoodsBargain(): boolean {
        return this.employeesMeConfig?.employeeDataPermission?.outpatient?.goodsPrice == OutpatientDataPermissionGoodsPriceType.allowedAll;
        // return this.dataPermissionConfig
        //     ? this.dataPermissionConfig?.outpatient?.goodsPrice == OutpatientDataPermissionGoodsPriceType.allowedAll
        //     : false;
    }

    get doctorShouldViewPrescriptionFromTotal(): boolean {
        return this.employeesMeConfig?.employeeDataPermission?.outpatient?.goodsPrice != OutpatientDataPermissionGoodsPriceType.notAllowed;
        // return this.dataPermissionConfig
        //     ? this.dataPermissionConfig?.outpatient?.goodsPrice != OutpatientDataPermissionGoodsPriceType.notAllowed
        //     : false;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {
    detailData: OutpatientInvoiceDetail;

    constructor(detailData: OutpatientInvoiceDetail) {
        super();
        this.detailData = detailData;
    }
}

class _EventUpdate extends _Event {}

class _EventChangeItemTotalPrice extends _Event {
    formItem: PrescriptionFormItem;
    price: number;

    constructor(formItem: PrescriptionFormItem, price: number) {
        super();
        this.formItem = formItem;
        this.price = price;
    }
}

class _EventChangeItemUnitPrice extends _Event {
    formItem: PrescriptionFormItem;
    price: number;

    constructor(formItem: PrescriptionFormItem, price: number) {
        super();
        this.formItem = formItem;
        this.price = price;
    }
}

class _EventChangeRegistrationFee extends _Event {
    fee: number;

    constructor(fee: number) {
        super();
        this.fee = fee;
    }
}

class _EventSave extends _Event {}

class _EventChangeTotalPrice extends _Event {
    price: number;

    constructor(price: number) {
        super();
        this.price = price;
    }
}

class _EventUpdateCalculatePrice extends _Event {
    calculating: boolean;
    error: any;
    calculateRspData?: OutpatientInvoiceDetail | null;
    shouldNotCopyExpectedPrice?: boolean;

    constructor(options: {
        calculateRspData?: OutpatientInvoiceDetail | null;
        calculating: boolean;
        error: any;
        shouldNotCopyExpectedPrice?: boolean;
    }) {
        super();
        this.calculating = options.calculating;
        this.calculateRspData = options.calculateRspData;
        this.error = options.error;
        this.shouldNotCopyExpectedPrice = options.shouldNotCopyExpectedPrice ?? false;
    }
}

class _EventChangeAirPharmacyFormPrice extends _Event {
    form: PrescriptionChineseForm;
    value: number;

    constructor(form: PrescriptionChineseForm, value: number) {
        super();
        this.form = form;
        this.value = value;
    }
}

class _EventChangeFormPrice extends _Event {
    form: PrescriptionChineseForm | PrescriptionWesternForm | PrescriptionInfusionForm | PrescriptionExternalForm;
    value: number;

    constructor(
        form: PrescriptionChineseForm | PrescriptionWesternForm | PrescriptionInfusionForm | PrescriptionExternalForm,
        value: number
    ) {
        super();
        this.form = form;
        this.value = value;
    }
}

class OutpatientSingleBargainDialogBloc extends Bloc<_Event, State> {
    static Context = React.createContext<OutpatientSingleBargainDialogBloc | undefined>(undefined);

    _calculatePriceTrigger = new Subject<number>();

    constructor(detailData: OutpatientInvoiceDetail) {
        super();

        this.dispatch(new _EventInit(detailData));
    }

    static fromContext(context: OutpatientSingleBargainDialogBloc): OutpatientSingleBargainDialogBloc {
        return context;
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<string, Function> {
        const map = new Map<string, Function>();
        map.set(_EventInit.name, this._mapEventInit);
        map.set(_EventUpdate.name, this._mapEventUpdate);

        map.set(_EventUpdateCalculatePrice.name, this._mapEventUpdateCalculatePrice);
        map.set(_EventChangeItemTotalPrice.name, this._mapEventChangeItemTotalPrice);
        map.set(_EventChangeItemUnitPrice.name, this._mapEventChangeItemUnitPrice);
        map.set(_EventChangeRegistrationFee.name, this._mapEventChangeRegistrationFee);
        map.set(_EventSave.name, this._mapEventSave);
        map.set(_EventChangeTotalPrice.name, this._mapChangeTotalPrice);
        map.set(_EventChangeAirPharmacyFormPrice.name, this._mapEventChangeAirPharmacyFormPrice);

        return map;
    }

    async *_mapEventInit(event: _EventInit): AsyncGenerator<State> {
        event.detailData.productForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.productFormItems?.map((item) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
            });
        });
        event.detailData.prescriptionChineseForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });
        event.detailData.prescriptionInfusionForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });
        event.detailData.prescriptionWesternForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });

        event.detailData.prescriptionExternalForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });

        this.innerState.originalData = JsonMapper.deserialize(OutpatientInvoiceDetail, event.detailData);

        this.innerState.detailData = OutpatientUtils.tripChargedItemForOutpatientPriceCalculate(event.detailData);

        const disposable = this._calculatePriceTrigger
            .pipe(
                debounceTime(300),
                switchMap((/*_*/) => {
                    return fromPromise(this._calculatePrice().catch((e) => new ABCError(e)));
                })
            )
            .subscribe(
                (calculateRspData) => {
                    if (calculateRspData instanceof ABCError) {
                        this.dispatch(
                            new _EventUpdateCalculatePrice({
                                calculating: false,
                                calculateRspData: null,
                                error: calculateRspData,
                                shouldNotCopyExpectedPrice: true,
                            })
                        );
                    } else {
                        this.dispatch(
                            new _EventUpdateCalculatePrice({
                                calculating: false,
                                calculateRspData: calculateRspData,
                                error: null,
                                shouldNotCopyExpectedPrice: true,
                            })
                        );
                    }
                },
                (error) => {
                    this.dispatch(
                        new _EventUpdateCalculatePrice({
                            calculating: false,
                            calculateRspData: null,
                            error: error,
                            shouldNotCopyExpectedPrice: true,
                        })
                    );
                }
            );
        this.addDisposable(disposable);

        this.innerState.chargeConfig = OnlinePropertyConfigProvider.instance.getChargeConfigSync();

        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig(true).catchIgnore();

        this.update();

        OutpatientUtils.clearOutpatientDetailAllExpectedPrice(this.innerState.detailData);

        this.innerState.clinicBasicSetting = await OnlinePropertyConfigProvider.instance.getClinicBasicSetting();
        this.innerState.bargainingRules = await ChargeAgent.queryChargeBargainingRules({
            businessKeys: [BargainingBusinessKey.outpatientAdjustEmployeeMatch],
        }).catchIgnore();

        this._calculatePriceTrigger.next(0);
    }

    _calculatePrice(): Promise<OutpatientInvoiceDetail> {
        this.innerState.calculating = true;
        this.update();
        if (_.isNumber(this.innerState.detailData!.expectedTotalPrice)) {
            this.innerState.detailData!.adjustmentFee = undefined;
        }
        return OutpatientAgent.chargeCalculate(this.innerState.detailData!);
    }

    _copyPriceAttrTo(fromForms: Array<any>, toForms: Array<any>, shouldNotCopyExpectedPrice?: boolean): void {
        if (ABCUtils.isEmpty(fromForms) || ABCUtils.isEmpty(toForms)) return;

        for (const form of fromForms) {
            // for (let item of form) {
            const originalForm = toForms.find((item) => item.keyId == form.keyId);
            originalForm.totalPrice = form.totalPrice;
            originalForm.isTotalPriceChanged = form.isTotalPriceChanged;
            originalForm.sheetFlatPrice = form.sheetFlatPrice;
            originalForm.sourceTotalPrice = form.sourceTotalPrice;
            originalForm.expectedTotalPrice = form.expectedTotalPrice;
            originalForm.totalPriceRatio = form.totalPriceRatio;
            originalForm.canAdjustment = form.canAdjustment;

            let fromFormItems: Array<PrescriptionFormItem>;
            let toFormItems: Array<PrescriptionFormItem>;

            if (form instanceof PrescriptionProductForm) {
                fromFormItems = form.productFormItems!;
                toFormItems = originalForm.productFormItems;
            } else {
                //PrescriptionChineseForm, PrescriptionWesternForm, PrescriptionInfusionForm
                if (form.pharmacyType == PharmacyType.air) {
                    originalForm.expectedTotalPrice = form.expectedTotalPrice;
                }
                fromFormItems = form.prescriptionFormItems;
                toFormItems = originalForm.prescriptionFormItems;
            }

            for (const formItem of fromFormItems) {
                const originalFormItem = toFormItems.find((item) => item.keyId == formItem.keyId);
                originalFormItem!.totalPrice = formItem.totalPrice;
                originalFormItem!.sourceUnitPrice = formItem.sourceUnitPrice;
                originalFormItem!.sourceTotalPrice = formItem.sourceTotalPrice;
                originalFormItem!.unitPrice = formItem.unitPrice;
                originalFormItem!.formFlatPrice = formItem.formFlatPrice;
                originalFormItem!.sheetFlatPrice = formItem.sheetFlatPrice;
                originalFormItem!.fractionPrice = formItem.fractionPrice;
                originalFormItem!.isUnitPriceChanged = formItem.isUnitPriceChanged;
                originalFormItem!.isTotalPriceChanged = formItem.isTotalPriceChanged;
                originalFormItem!.currentUnitPrice = formItem.currentUnitPrice;
                originalFormItem!.expectedTotalPrice = formItem.expectedTotalPrice;
                originalFormItem!.expectedUnitPrice = formItem.expectedUnitPrice;
                originalFormItem!.totalPriceRatio = formItem.totalPriceRatio;
                originalFormItem!.canAdjustment = formItem.canAdjustment;
                if (!shouldNotCopyExpectedPrice) {
                    if (!_.isNil(formItem!.expectedTotalPrice)) {
                        originalFormItem!.expectedTotalPrice = formItem.expectedTotalPrice;
                    }
                    originalFormItem!.expectedUnitPrice = formItem.expectedUnitPrice;
                }
            }
            // }
        }
    }

    _doUpdateCalculatePrice(event: _EventUpdateCalculatePrice): void {
        this.innerState.calculating = event.calculating;
        this.innerState.calculateFailed = event.error;
        this.update();

        if (event.calculateRspData == null) return;

        this.innerState.detailData!.totalPrice = event.calculateRspData.totalPrice;
        this.innerState.detailData!.adjustmentFee = event.calculateRspData.adjustmentFee;
        // this.innerState.detailData!.expectedTotalPrice = event.calculateRspData.expectedTotalPrice;
        this.innerState.detailData!.sourceTotalPrice = event.calculateRspData.sourceTotalPrice;
        this.innerState.detailData!.isTotalPriceChanged = event.calculateRspData.isTotalPriceChanged;
        this.innerState.detailData!.registrationFee = event.calculateRspData.registrationFee; //挂号费也进行摊费
        this.innerState.detailData!.chargeRoundingTips = _.isNil(event.calculateRspData.chargeRoundingTips)
            ? undefined
            : event.calculateRspData.chargeRoundingTips;
        this.innerState.detailData!.canAdjustmentFee = event.calculateRspData.canAdjustmentFee;
        this.innerState.detailData!.canAdjustment = event.calculateRspData.canAdjustment;
        this.innerState.detailData!.expectedTotalPrice = event.calculateRspData.expectedTotalPrice;
        this._copyPriceAttrTo(
            event.calculateRspData.productForms!,
            this.innerState.detailData?.productForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionChineseForms!,
            this.innerState.detailData?.prescriptionChineseForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionWesternForms!,
            this.innerState.detailData?.prescriptionWesternForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionInfusionForms!,
            this.innerState.detailData?.prescriptionInfusionForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionExternalForms!,
            this.innerState.detailData?.prescriptionExternalForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
    }

    async *_mapEventUpdate(/*ignore: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    update(): void {
        this.dispatch(new _EventUpdate());
    }

    async *_mapEventUpdateCalculatePrice(event: _EventUpdateCalculatePrice): AsyncGenerator<State> {
        this._doUpdateCalculatePrice(event);
        this.update();
    }

    async *_mapEventChangeItemTotalPrice(event: _EventChangeItemTotalPrice): AsyncGenerator<State> {
        if (event.formItem.expectedTotalPrice == event.price) return;
        OutpatientUtils.clearOutpatientDetailAllExpectedPrice(this.innerState.detailData);
        event.formItem.expectedTotalPrice = event.price;
        event.formItem.isUnitPriceChanged = 0;
        event.formItem.isTotalPriceChanged = 1;
        event.formItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
        this.innerState.calculating = true;
        yield this.innerState;
        this._calculatePriceTrigger.next(0);
    }

    async *_mapEventChangeItemUnitPrice(event: _EventChangeItemUnitPrice): AsyncGenerator<State> {
        if (event.formItem.expectedUnitPrice == event.price) return;
        OutpatientUtils.clearOutpatientDetailAllExpectedPrice(this.innerState.detailData);
        event.formItem.expectedUnitPrice = event.price;
        event.formItem.isUnitPriceChanged = 1;
        event.formItem.isTotalPriceChanged = 0;
        event.formItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
        this.innerState.calculating = true;
        yield this.innerState;
        this._calculatePriceTrigger.next(0);
    }

    async *_mapEventChangeRegistrationFee(event: _EventChangeRegistrationFee): AsyncGenerator<State> {
        OutpatientUtils.clearOutpatientDetailAllExpectedPrice(this.innerState.detailData);
        this.innerState.detailData!.registrationFee = event.fee;
        this.innerState.calculating = true;
        yield this.innerState;
        this._calculatePriceTrigger.next(0);
    }

    async *_mapEventSave(/*ignore: _EventSave*/): AsyncGenerator<State> {
        if (this.innerState.calculateFailed != null) {
            try {
                this._doUpdateCalculatePrice(new _EventUpdateCalculatePrice({ calculating: true, error: null }));
                this.update();
                const rsp = await this._calculatePrice();
                this._doUpdateCalculatePrice(
                    new _EventUpdateCalculatePrice({
                        calculating: false,
                        error: null,
                        calculateRspData: rsp,
                    })
                );
                this.update();
            } catch (e) {
                this._doUpdateCalculatePrice(
                    new _EventUpdateCalculatePrice({
                        calculating: false,
                        error: e,
                        calculateRspData: null,
                    })
                );
                await Toast.show(`算费失败：${errorToStr(e)}`, { warning: true });
                this.update();
                return;
            }
        }

        if (OutpatientUtils.canEditWithChargeStatus(this.innerState.originalData?.registrationFeeStatus)) {
            this.innerState.originalData!.registrationFee = this.innerState.detailData?.registrationFee;
        }
        this.innerState.originalData!.adjustmentFee = this.innerState.detailData?.adjustmentFee;
        this.innerState.originalData!.totalPrice = this.innerState.detailData?.totalPrice;
        this.innerState.originalData!.expectedTotalPrice = this.innerState.detailData?.expectedTotalPrice;
        this.innerState.originalData!.isTotalPriceChanged = this.innerState.detailData?.isTotalPriceChanged;
        this.innerState.originalData!.registrationFee = this.innerState.detailData?.registrationFee;
        this._copyPriceAttrTo(this.innerState.detailData?.productForms ?? [], this.innerState.originalData?.productForms ?? []);
        this._copyPriceAttrTo(
            this.innerState.detailData!.prescriptionWesternForms ?? [],
            this.innerState.originalData?.prescriptionWesternForms ?? []
        );
        this._copyPriceAttrTo(
            this.innerState.detailData!.prescriptionChineseForms ?? [],
            this.innerState.originalData?.prescriptionChineseForms ?? []
        );
        this._copyPriceAttrTo(
            this.innerState.detailData!.prescriptionInfusionForms ?? [],
            this.innerState.originalData?.prescriptionInfusionForms ?? []
        );
        this._copyPriceAttrTo(
            this.innerState.detailData!.prescriptionExternalForms ?? [],
            this.innerState.originalData?.prescriptionExternalForms ?? []
        );

        //空中药房的单子需要把运费手动加入到总价中
        this.innerState.originalData?.prescriptionChineseForms?.forEach((item) => {
            item.totalPrice = item.totalPrice ?? 0;
        });

        //调用算费保存接口。议价信息后台落地
        if (!!this.innerState.originalData) {
            await OutpatientAgent.putOutpatientAdjustmentPrice(this.innerState.originalData);
        }
        ABCNavigator.pop(this.innerState.originalData);
    }

    async *_mapChangeTotalPrice(event: _EventChangeTotalPrice): AsyncGenerator<State> {
        if (this.innerState.detailData?.expectedTotalPrice == event.price) return;
        OutpatientUtils.clearOutpatientDetailAllExpectedPrice(this.innerState.detailData, true);
        this.innerState.detailData!.expectedTotalPrice = event.price;
        this.dispatch(
            new _EventUpdateCalculatePrice({
                calculating: true,
                calculateRspData: null,
                error: null,
            })
        );
        this.innerState.calculating = true;
        yield this.innerState;
        this._calculatePriceTrigger.next(0);
    }

    async *_mapEventChangeAirPharmacyFormPrice(event: _EventChangeAirPharmacyFormPrice): AsyncGenerator<State> {
        OutpatientUtils.clearOutpatientDetailAllExpectedPrice(this.innerState.detailData);
        event.form.totalPrice = event.value;
        event.form.expectedTotalPrice = event.value;
        event.form.prescriptionFormItems?.map((formItem) => {
            formItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
            return formItem;
        });
        this.innerState.calculating = true;
        yield this.innerState;
        this._calculatePriceTrigger.next(0);
    }

    @actionEvent(_EventChangeFormPrice)
    async *_mapEventChangeFormPrice(event: _EventChangeFormPrice): AsyncGenerator<State> {
        OutpatientUtils.clearOutpatientDetailAllExpectedPrice(this.innerState.detailData);
        event.form.expectedTotalPrice = event.value;
        event.form.prescriptionFormItems?.map((formItem) => {
            formItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
            return formItem;
        });
        this.innerState.calculating = true;
        yield this.innerState;
        this._calculatePriceTrigger.next(0);
    }

    requestChangeItemTotalPrice(formItem: PrescriptionFormItem, price: number): void {
        this.dispatch(new _EventChangeItemTotalPrice(formItem, price));
    }

    //保存修改
    requestSave(): void {
        this.dispatch(new _EventSave());
    }

    requestChangeItemUnitPrice(formItem: PrescriptionFormItem, price: number): void {
        this.dispatch(new _EventChangeItemUnitPrice(formItem, price));
    }

    ///修改挂号费
    requestChangeRegistrationFee(fee: number): void {
        this.dispatch(new _EventChangeRegistrationFee(fee));
    }

    /// 设置总价
    requestChangeTotalPrice(price: number): void {
        this.dispatch(new _EventChangeTotalPrice(price));
    }

    ///空中药房的整单议价
    requestChangeAirPharmacyFormPrice(form: PrescriptionChineseForm, value: number): void {
        this.dispatch(new _EventChangeAirPharmacyFormPrice(form, value));
    }

    requestChangeFormPrice(
        form: PrescriptionChineseForm | PrescriptionWesternForm | PrescriptionInfusionForm | PrescriptionExternalForm,
        value: number
    ): void {
        this.dispatch(new _EventChangeFormPrice(form, value));
    }
}

export { OutpatientSingleBargainDialogBloc, State };
