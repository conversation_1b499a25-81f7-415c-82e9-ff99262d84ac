/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/10/22
 */

import { Bloc, BlocEvent } from "../../bloc";
import React from "react";
import { actionEvent } from "../../bloc/bloc";
import { BaseLoadingState } from "../../bloc/bloc-helper";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import _, { cloneDeep, isUndefined } from "lodash";
import {
    AntibioticEnum,
    GoodsInfo,
    GoodsSubType,
    GoodsType,
    PsychotropicNarcoticTypeEnum,
    SubClinicPricePriceMode,
} from "../../base-business/data/beans";
import { of, Subject } from "rxjs";
import { debounce, debounceTime, switchMap } from "rxjs/operators";
import { GoodsAgent } from "../../data/goods/goods-agent";
import { ABCError } from "../../common-base-module/common-error";
import { MedicineUsageParams } from "../medicine-add-page/medicine-add-page";
import { SuggestSearchItem, SuggestService } from "../../data/suggest-service";
import {
    AcuPointsItem,
    ExternalGoodsItem,
    OutpatientInvoiceDetail,
    PrescriptionExternalForm,
    PrescriptionFormItem,
    PrescriptionProductForm,
} from "../data/outpatient-beans";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { ABCUtils } from "../../base-ui/utils/utils";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { Toast } from "../../base-ui/dialog/toast";
import { AcupunctureDialog } from "../acupuncture/acupuncture-page";
import { AcupunctureItem } from "../acupuncture/data/acupuncture-bean";
import { AbcSet } from "../../base-ui/utils/abc-set";
import { AbcDialog } from "../../base-ui/abc-app-library";
import { ExternalPrescriptionTypeDialog } from "./views/external-view";
import { ExternalPRUsageTypeEnum, UsageTypeOptions } from "../../../assets/medicine_usage/outpatient-external-config";
import { PrescriptionTemplateInfo } from "../data/prescription-template-bean";
import { OutpatientUtils } from "../utils/outpatient-utils";
import { SaveTemplateDialog } from "../views/save-template-dialog";
import { OutpatientPrescriptionTemplateSearchDialog } from "../prescription-template/outpatient-prescription-template-search-page";
import { MedicineAddType } from "../data/outpatient-const";
import { errorToStr, UUIDGen } from "../../common-base-module/utils";
import { GoodsUtils } from "../../base-business/utils/utils";
import { MedicineTemplateAddType, MedicineTemplateAddTypeDialog } from "../medicine-add-page/views/medicine-template-add-type-dialog";
import { ClinicAgent, EmployeesMeConfig } from "../../base-business/data/clinic-agent";
import { OutpatientDataPermissionGoodsPriceType } from "../../data/online-property-config-provder";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { InventoryClinicConfig } from "../../inventory/data/inventory-bean";
import { userCenter } from "../../user-center";
import { fromPromise } from "rxjs/internal-compatibility";
import { OutpatientAgent } from "../data/outpatient";
import { PharmacyType } from "../../charge/data/charge-bean-air-pharmacy";
import { MedicineAddPageUtils } from "../medicine-add-page/medicine-add-page-utils";
import { AcuPointsId } from "./constants/external-constants";
import { showConfirmDialog } from "../../base-ui/dialog/dialog-builder";

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}
class _EventCacheSearchInput extends _Event {
    searchInput: AbcTextInput | null;

    constructor(searchInput: AbcTextInput | null) {
        super();
        this.searchInput = searchInput;
    }
}

class _EventSearchGoodsList extends _Event {
    keyword: string;

    constructor(props: string) {
        super();
        this.keyword = props;
    }
}

class _EventAddSelectAcuPoint extends _Event {
    acuPoint: AcuPointsItem;
    constructor(acuPoint: AcuPointsItem) {
        super();
        this.acuPoint = acuPoint;
    }
}

class _EventAddSelectMedicine extends _Event {
    medicine: GoodsInfo;
    constructor(medicine: GoodsInfo) {
        super();
        this.medicine = medicine;
    }
}

class _EventAddSelectGoods extends _EventAddSelectMedicine {}

class _EventDeleteGoods extends _Event {
    formItem: PrescriptionFormItem;
    constructor(formItem: PrescriptionFormItem) {
        super();
        this.formItem = formItem;
    }
}

class _EventDeleteAcuPoint extends _Event {
    acuPoint: AcuPointsItem;
    formItem: PrescriptionFormItem;
    constructor(formItem: PrescriptionFormItem, acuPoint: AcuPointsItem) {
        super();
        this.formItem = formItem;
        this.acuPoint = acuPoint;
    }
}

class _EventDeleteMedicine extends _Event {
    medicine: ExternalGoodsItem;
    formItem: PrescriptionFormItem;
    constructor(formItem: PrescriptionFormItem, medicine: ExternalGoodsItem) {
        super();
        this.formItem = formItem;
        this.medicine = medicine;
    }
}

class _EventChangeGoodsInfoUnitCount extends _Event {
    formItem: PrescriptionFormItem;
    count?: number;
    calculation?: boolean;
    constructor(formItem: PrescriptionFormItem, count?: number, calculation?: boolean) {
        super();
        this.formItem = formItem;
        this.count = count;
        this.calculation = calculation;
    }
}

class _EventChangeGoodsExternalUnitCount extends _Event {
    formItem: PrescriptionFormItem;
    count?: number;
    constructor(formItem: PrescriptionFormItem, count?: number) {
        super();
        this.formItem = formItem;
        this.count = count;
    }
}

class _EventChangeGoodsExternalUnit extends _Event {
    formItem: PrescriptionFormItem;
    unit?: string;
    constructor(formItem: PrescriptionFormItem, unit?: string) {
        super();
        this.formItem = formItem;
        this.unit = unit;
    }
}

class _EventChangeRequirementText extends _Event {
    formItem: PrescriptionFormItem;
    text: string;
    constructor(formItem: PrescriptionFormItem, text: string) {
        super();
        this.formItem = formItem;
        this.text = text;
    }
}

class _EventChangeToAddAcuPoint extends _Event {
    formItem: PrescriptionFormItem;
    searchMode: SearchMode;
    constructor(formItem: PrescriptionFormItem, searchMode: SearchMode) {
        super();
        this.formItem = formItem;
        this.searchMode = searchMode;
    }
}

class _EventModifyAcuPointInfo extends _Event {
    point: AcuPointsItem;
    formItem: PrescriptionFormItem;
    constructor(formItem: PrescriptionFormItem, pointInfo: AcuPointsItem) {
        super();
        this.point = pointInfo;
        this.formItem = formItem;
    }
}

class _EventChangeMedicineUsageBizExtension extends _Event {
    formItem: PrescriptionFormItem;
    billingType: number;
    constructor(formItem: PrescriptionFormItem, billingType: number) {
        super();
        this.formItem = formItem;
        this.billingType = billingType;
    }
}

class _EventSubmit extends _Event {}

class _EventChangeMedicineUsageFreq extends _Event {
    formItem: PrescriptionFormItem;
    freq?: string;
    constructor(formItem: PrescriptionFormItem, freq?: string) {
        super();
        this.formItem = formItem;
        this.freq = freq;
    }
}
class _EventModifyAcuPointList extends _Event {
    formItem: PrescriptionFormItem;
    constructor(formItem: PrescriptionFormItem) {
        super();
        this.formItem = formItem;
    }
}

class _EventBackPage extends _Event {}

class _EventChangeExternalType extends _Event {}

class _EventSelectPrescriptionLabel extends _Event {
    type?: number;
    constructor(type?: number) {
        super();
        this.type = type;
    }
}

class _EventSaveTemplate extends _Event {}
class _EventUseTemplate extends _Event {}

class _EventModifyMedicineUnitCount extends _Event {
    formItem: PrescriptionFormItem;
    externalGoodsItem: ExternalGoodsItem;
    count?: string;
    constructor(formItem: PrescriptionFormItem, externalGoodsItem: ExternalGoodsItem, count?: string) {
        super();
        this.formItem = formItem;
        this.externalGoodsItem = externalGoodsItem;
        this.count = count;
    }
}
class _EventUpdateCalculatePrice extends _Event {
    calculating: boolean;
    error: any;
    calculateRspData?: OutpatientInvoiceDetail | null;
    shouldNotCopyExpectedPrice?: boolean;

    constructor(options: {
        calculateRspData?: OutpatientInvoiceDetail | null;
        calculating: boolean;
        error: any;
        shouldNotCopyExpectedPrice?: boolean;
    }) {
        super();
        this.calculating = options.calculating;
        this.calculateRspData = options.calculateRspData;
        this.error = options.error;
        this.shouldNotCopyExpectedPrice = options.shouldNotCopyExpectedPrice ?? false;
    }
}

class _EventUpdateFormItemSort extends _Event {
    formItem: PrescriptionFormItem;
    constructor(formItem: PrescriptionFormItem) {
        super();
        this.formItem = formItem;
    }
}

const AcuPointPosition = ["左", "右", "双"];
const AShiXuePointsNumber = Array.from({ length: 20 }, (_, i) => (i + 1).toString()); // 阿是穴（穴位计数1~20）
const JiaJiPointsNumber = Array.from({ length: 35 }, (_, i) => (i + 1).toString()); // 夹脊（穴位计数1~35）

export enum SearchMode {
    goods,
    acuPoints,
    chineseMedicine,
}

export class State extends BaseLoadingState {
    keyword?: string;
    searchGoodsList?: GoodsInfo[];
    searchAcuPointsList?: SuggestSearchItem[];
    searchListNoStock?: [];
    searchMode: SearchMode = SearchMode.goods;
    arrangement?: boolean;

    formDetail?: PrescriptionExternalForm;
    //编辑态时，当前选择的项
    currentFocusItem?: PrescriptionFormItem;
    currentSelectItemKeyId?: string; // 用于处方分组点击状态判断

    focusViewKey?: string; // 选中的项目keyId
    focusMedicineView?: string; //选中的药品名字

    showErrorHint?: boolean;
    employeesMeConfig?: EmployeesMeConfig;

    detailData?: OutpatientInvoiceDetail;
    pharmacyInfoConfig?: InventoryClinicConfig;
    calculating = false;
    calculateFailed: any; //算费失败错误

    allowAntibioticList?: AntibioticEnum[]; //允许的抗菌药物
    //开药时是否校验药物类型
    get allowAntibiotic(): boolean {
        return this.allowAntibioticList != undefined;
    }

    /**
     * 是否开启进价加成模式
     */
    get isPurchaseMarkup(): boolean {
        return this.pharmacyInfoConfig?.subClinicPrice?.priceMode === SubClinicPricePriceMode.purchaseMarkup;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }

    get usageTypeDisplay(): string {
        return UsageTypeOptions.find((it) => it.value == this.formDetail?.usageType)?.label ?? "选择类别";
    }

    get usageSubTypeDisplay(): string {
        const usageType = UsageTypeOptions.find((it) => it.value == this.formDetail?.usageType);
        return usageType?.children?.find((it) => it.value == this.formDetail?.usageSubType)?.label ?? "选择类别";
    }
    get totalPrice(): number {
        let singleFormPrice = 0;
        singleFormPrice =
            this.formDetail?.prescriptionFormItems?.reduce((pre, formItem) => {
                const { unitCount, unit, goodsInfo } = formItem;
                if (unitCount == null || unit == null) {
                    return pre;
                }
                const unitPrice = goodsInfo.unitPriceWithUnit(unit);
                return pre + unitCount * unitPrice;
            }, 0) ?? 0;
        return singleFormPrice;
    }

    /**
     * 显示总价
     */
    get showTotalPrice(): boolean {
        return this.employeesMeConfig?.employeeDataPermission?.outpatient?.goodsPrice != OutpatientDataPermissionGoodsPriceType.notAllowed;
    }
    /**
     * 显示单项价格
     */
    get showItemPrice(): boolean {
        return this.employeesMeConfig?.employeeDataPermission?.outpatient?.goodsPrice == OutpatientDataPermissionGoodsPriceType.allowedAll;
    }
}

export class ScrollToViewState extends State {
    static fromState(state: State): ScrollToViewState {
        const newState = new ScrollToViewState();
        Object.assign(newState, state);
        return newState;
    }
}

export class MedicineExternalAddPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<MedicineExternalAddPageBloc | undefined>(undefined);
    private readonly pageProps: {
        formDetail?: PrescriptionExternalForm;
        outpatientDetail?: OutpatientInvoiceDetail;
        psychotropicNarcoticType?: PsychotropicNarcoticTypeEnum;
    };

    static fromContext(context: MedicineExternalAddPageBloc): MedicineExternalAddPageBloc {
        return context;
    }

    _searchInput?: AbcTextInput | null;
    private _loadDataTrigger = new Subject<number>();
    private _loadAcuPointTrigger = new Subject<number>();
    _calculatePriceTrigger = new Subject<string>();
    params?: MedicineUsageParams;
    private _prescriptionFormKeyId?: string;

    constructor(props: {
        formDetail?: PrescriptionExternalForm;
        outpatientDetail?: OutpatientInvoiceDetail;
        psychotropicNarcoticType?: PsychotropicNarcoticTypeEnum;
        allowAntibiotic?: AntibioticEnum[];
    }) {
        super();
        this.pageProps = props;
        const { formDetail, outpatientDetail } = props;
        this.innerState.formDetail = formDetail;
        this.innerState.detailData = cloneDeep(outpatientDetail);
        this.innerState.allowAntibioticList = props?.allowAntibiotic;
        this._prescriptionFormKeyId = formDetail?.keyId;

        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    @actionEvent(_EventAddSelectMedicine)
    async *_mapEventAddSelectMedicine(event: _EventAddSelectMedicine): AsyncGenerator<State> {
        if (!this.innerState.currentFocusItem) return;
        const currentFocusItem = this.innerState.currentFocusItem;
        if (!currentFocusItem.externalGoodsItems?.length) {
            currentFocusItem.externalGoodsItems = [];
        }
        const isExistRepeatGoods = currentFocusItem?.externalGoodsItems?.find((item) => item.goodsId == event.medicine.id);
        const medicine = event.medicine;
        const _medicine = JsonMapper.deserialize(ExternalGoodsItem, {
            ...medicine,
            medicineCadn: medicine.medicineCadn,
            name: medicine.displayName,
            manufacturer: medicine.manufacturer,
            type: medicine.type,
            subType: medicine.subType,
            unit: medicine.unit,
            useDismounting: medicine.dismounting,
            goodsId: event.medicine.id,
            unitCount: undefined,
            cMSpec: medicine.cMSpec,
        });
        if (!isExistRepeatGoods) {
            this.innerState.formDetail?.prescriptionFormItems?.forEach((formItem) => {
                if (formItem.keyId == currentFocusItem.keyId) {
                    formItem.externalGoodsItems = formItem.externalGoodsItems ?? [];
                    formItem.externalGoodsItems?.push(_medicine);
                }
            });
            this.innerState.focusMedicineView = _medicine.displayName;
        }
        this.innerState.keyword = "";
        this._searchInput?.clear();
        //解决"如果，点了一次，重复的，提示重复,再搜索，就还是会有，先出上次一的搜索结果，再刷新成刚输入的"问题
        this.innerState.searchGoodsList = undefined;
        this.innerState.searchListNoStock = undefined;
        this.innerState.searchAcuPointsList = undefined;
        yield ScrollToViewState.fromState(this.innerState);
        this._calculatePriceTrigger.next();
        if (isExistRepeatGoods) {
            Toast.show(`${_medicine.displayName}已添加`, { warning: true, autoBlurText: false });
        }
    }

    @actionEvent(_EventSearchGoodsList)
    async *_mapEventSearchGoodsList(event: _EventSearchGoodsList): AsyncGenerator<State> {
        this.innerState.keyword = event.keyword;
        this.innerState.searchGoodsList = undefined;
        this.innerState.searchListNoStock = undefined;
        this.innerState.searchAcuPointsList = undefined;
        if (this.innerState.searchMode == SearchMode.acuPoints) {
            this._loadAcuPointTrigger.next();
        } else if (this.innerState.searchMode == SearchMode.chineseMedicine) {
            this._loadDataTrigger.next();
        }
    }

    @actionEvent(_EventCacheSearchInput)
    async *_mapEventCacheSearchInput(event: _EventCacheSearchInput): AsyncGenerator<State> {
        this._searchInput = event.searchInput;
    }

    @actionEvent(_EventAddSelectAcuPoint)
    async *_mapEventAddSelectAcuPoint(event: _EventAddSelectAcuPoint): AsyncGenerator<State> {
        if (!this.innerState.currentFocusItem) return;
        const currentFocusItem = this.innerState.formDetail?.prescriptionFormItems?.find(
            (formItem) => formItem.keyId == this.innerState.currentFocusItem?.keyId
        );
        if (!currentFocusItem) return;
        const isExistRepeatGoods = currentFocusItem.acupoints?.find((item) => item.id == event.acuPoint.id);
        if (!isExistRepeatGoods) {
            currentFocusItem.acupoints = currentFocusItem.acupoints ?? [];
            currentFocusItem.acupoints.push(event.acuPoint);
        }
        this.calculationUnitCount(this.innerState.currentFocusItem);
        this.innerState.keyword = "";
        //解决"如果，点了一次，重复的，提示重复,再搜索，就还是会有，先出上次一的搜索结果，再刷新成刚输入的"问题
        this.innerState.searchGoodsList = undefined;
        this.innerState.searchListNoStock = undefined;
        this.innerState.searchAcuPointsList = undefined;
        yield this.innerState;
        if (isExistRepeatGoods) {
            Toast.show(`${event.acuPoint.name}穴位已添加`, { warning: true, autoBlurText: false });
        }
    }

    @actionEvent(_EventAddSelectGoods)
    async *_mapEventAddSelectGoods(event: _EventAddSelectGoods): AsyncGenerator<State> {
        this.innerState.searchMode = SearchMode.goods;
        yield this.innerState.clone();
        const goodsInfo = JsonMapper.deserialize(GoodsInfo, { ...event.medicine, keyId: event.medicine?.keyId ?? UUIDGen.generate() });
        if (!goodsInfo) return;
        if (!isUndefined(goodsInfo.antibiotic) && this.innerState.allowAntibiotic) {
            if (!this.innerState.allowAntibioticList?.includes(goodsInfo.antibiotic ?? 0)) {
                return showConfirmDialog(
                    "提示",
                    `【${goodsInfo.displayName}】为${goodsInfo._antibioticDisplay}药物，当前医生不具备使用权限`
                );
            }
        }
        // 加一组，当前没有formItem，则以1开始排序，如果存在子项，需要在上一个formItem排序基础上加1
        let sort = 0;
        if (!this.innerState.formDetail?.prescriptionFormItems?.length) {
            sort = sort + 1;
        } else {
            const lastSort =
                this.innerState.formDetail?.prescriptionFormItems?.[this.innerState.formDetail?.prescriptionFormItems?.length - 1]?.sort ??
                0;
            sort = lastSort + 1;
        }
        const _addFormItem = JsonMapper.deserialize(PrescriptionFormItem, {
            keyId: goodsInfo.keyId,
            goodsId: goodsInfo.id,
            productInfo: goodsInfo,
            ...goodsInfo,
            name: goodsInfo.name ?? goodsInfo.displayName, // 中药药房切换需要用到name进行筛选
            sort: sort,
        });

        const unit = goodsInfo.unitPreferPackage;
        let externalUnitCount,
            billingType = 2;
        if (goodsInfo.isGoods || goodsInfo.isChineseWesternMedicine) {
            externalUnitCount = 1;
        }
        if (/[\d一二三四五六七八九十零贰叁肆伍陆柒捌玖拾]/.test(unit)) {
            billingType = 1;
        }
        Object.assign(_addFormItem, {
            acuPoints: [],
            externalGoodsItems: [],
            freq: "1日1次",
            specialRequirement: "",
            dosage: 1,
            dosageUnit: "次",
            externalUnitCount,
            unit: unit,
            unitCount: undefined,
            billingType,
        });
        this.calculationUnitCount(_addFormItem);
        this.innerState.formDetail?.prescriptionFormItems?.push(_addFormItem);
        this.innerState.currentFocusItem = _addFormItem;
        this.innerState.focusViewKey = _addFormItem.keyId;
        this.innerState.searchMode = SearchMode.acuPoints;
        this.innerState.keyword = "";
        this._sortFormItems();
        this.update(ScrollToViewState.fromState(this.innerState));
    }

    @actionEvent(_EventDeleteGoods)
    async *_mapEventDeleteGoods(event: _EventDeleteGoods): AsyncGenerator<State> {
        const formDetail = this.innerState.formDetail;
        formDetail?.prescriptionFormItems?.forEach((item, index) => {
            if (item.keyId == event.formItem.keyId) {
                this.innerState.formDetail?.prescriptionFormItems?.splice(index, 1);
            }
        });
        this.innerState.searchMode = SearchMode.goods;
        this._searchInput?.focus();
        this.update();
    }

    @actionEvent(_EventDeleteMedicine)
    async *_mapEventDeleteMedicine(event: _EventDeleteMedicine): AsyncGenerator<State> {
        event.formItem.externalGoodsItems = event.formItem.externalGoodsItems ?? [];
        _.remove(event.formItem.externalGoodsItems ?? [], event.medicine);
        this.update();
    }

    @actionEvent(_EventDeleteAcuPoint)
    async *_mapEventDeleteAcuPoint(event: _EventDeleteAcuPoint): AsyncGenerator<State> {
        event.formItem.acupoints = event.formItem.acupoints ?? [];
        _.remove(event.formItem.acupoints ?? [], event.acuPoint);
        this.calculationUnitCount(event.formItem);
        this.update();
    }

    @actionEvent(_EventChangeToAddAcuPoint)
    async *_mapEventChangeToAddAcuPoint(event: _EventChangeToAddAcuPoint): AsyncGenerator<State> {
        this.innerState.focusMedicineView = undefined;
        //聚焦时清除上一次搜索结果，避免连续搜索时，闪现出上一次结果
        this.innerState.searchGoodsList = undefined;
        this.innerState.searchAcuPointsList = undefined;
        this.innerState.searchMode = event.searchMode;
        this.innerState.currentFocusItem = event.formItem;
        const formItem = event.formItem;
        if (event.searchMode == SearchMode.chineseMedicine) {
            this.innerState.focusViewKey = `${event.formItem.keyId}${ABCUtils.last(formItem.externalGoodsItems ?? [])?.displayName ?? ""}`;
        } else if (event.searchMode == SearchMode.acuPoints) {
            this.innerState.focusViewKey = `${event.formItem.keyId}${ABCUtils.last(formItem?.acupoints ?? [])?.name}`;
        }
        this.update(ScrollToViewState.fromState(this.innerState));
    }

    @actionEvent(_EventChangeGoodsInfoUnitCount)
    async *_mapEventChangeGoodsInfoUnitCount(event: _EventChangeGoodsInfoUnitCount): AsyncGenerator<State> {
        // 自行修改贴数后，不重新计算数量
        if (event.calculation) {
            event.formItem.dosage = event.count;
            this.calculationUnitCount(event.formItem);
        } else {
            event.formItem.unitCount = event.count;
        }
        this._clearAdjustmentFee(event.formItem);
        this.update();
    }

    @actionEvent(_EventChangeGoodsExternalUnitCount)
    async *_mapEventChangeGoodsExternalUnitCount(event: _EventChangeGoodsExternalUnitCount): AsyncGenerator<State> {
        event.formItem.externalUnitCount = event.count;
        this.calculationUnitCount(event.formItem);
        this._clearAdjustmentFee(event.formItem);
        this.update();
    }

    @actionEvent(_EventChangeGoodsExternalUnit)
    async *_mapEventChangeGoodsExternalUnit(event: _EventChangeGoodsExternalUnit): AsyncGenerator<State> {
        event.formItem.unit = event.unit;
        this.calculationUnitCount(event.formItem);
        this._clearAdjustmentFee(event.formItem);
        this.update();
    }

    @actionEvent(_EventChangeRequirementText)
    async *_mapEventChangeRequirementText(event: _EventChangeRequirementText): AsyncGenerator<State> {
        event.formItem.specialRequirement = event.text;
        this.update();
    }

    @actionEvent(_EventModifyAcuPointInfo)
    async *_mapEventModifyAcuPointInfo(event: _EventModifyAcuPointInfo): AsyncGenerator<State> {
        const isAcuPointNumber = event.point.position == "1"; // 按穴位数量计数
        if (event.point.type == 1 || isAcuPointNumber) {
            let _positionIndex = AcuPointPosition.findIndex((item) => item == event.point.position);
            let _positionOptions = AcuPointPosition;
            if (isAcuPointNumber) {
                switch (event.point.id?.toString()) {
                    case AcuPointsId.A_SHI_XUE:
                        _positionIndex = AShiXuePointsNumber.findIndex((item) => item == event.point.position);
                        _positionOptions = AShiXuePointsNumber;
                        break;
                    case AcuPointsId.JIA_JI:
                        _positionIndex = JiaJiPointsNumber.findIndex((item) => item == event.point.position);
                        _positionOptions = JiaJiPointsNumber;
                        break;
                }
            }
            const positionSet = new Set([_positionIndex]);
            const select = await AbcDialog.showOptionsBottomSheet({
                title: "选择穴位位置",
                options: _positionOptions,
                initialSelectIndexes: positionSet,
                crossAxisCount: 3,
                showTitleBottomLine: false,
            });
            if (!select || _.isEmpty(select)) return;
            event.point.position = _positionOptions[select[0]];
            this.calculationUnitCount(event.formItem);
            this.update();
        } else {
            await Toast.show("当前穴位不可修改位置");
        }
    }
    @actionEvent(_EventChangeMedicineUsageBizExtension)
    async *_mapEventChangeMedicineUsageBizExtension(event: _EventChangeMedicineUsageBizExtension): AsyncGenerator<State> {
        const formItem = event.formItem;
        formItem.billingType = event.billingType;
        formItem.unitCount = undefined;
        this.calculationUnitCount(formItem);
        this._clearAdjustmentFee(event.formItem);
        this.update();
    }

    @actionEvent(_EventSubmit)
    async *_mapEventSubmit(): AsyncGenerator<State> {
        const { formDetail } = this.innerState;
        for (const formItem of formDetail?.prescriptionFormItems ?? []) {
            const { dosage, dosageUnit, externalGoodsItems, unitCount } = formItem;
            if (!dosage || !dosageUnit || unitCount == undefined) {
                await Toast.show("请完善必填项", { warning: true });
                this.innerState.focusViewKey = formItem.keyId;
                this.innerState.showErrorHint = true;
                yield ScrollToViewState.fromState(this.innerState);
                this.innerState.focusViewKey = undefined;
                return;
            }
            if (!!externalGoodsItems?.length) {
                for (const medicine of externalGoodsItems) {
                    // 模板复制出来的，可能带有空项，不需要校验
                    if (!medicine?.goodsId || !medicine?.displayName || !medicine?.keyId) continue;
                    if (!medicine.unitCount) {
                        await Toast.show("请完善必填项", { warning: true });
                        this.innerState.focusViewKey = formItem.keyId;
                        this.innerState.showErrorHint = true;
                        yield ScrollToViewState.fromState(this.innerState);
                        this.innerState.focusViewKey = undefined;
                        return;
                    }
                }
            }
        }
        ABCNavigator.pop(formDetail);
    }

    @actionEvent(_EventModifyMedicineUnitCount)
    async *_mapEventModifyMedicineUnitCount(event: _EventModifyMedicineUnitCount): AsyncGenerator<State> {
        event.externalGoodsItem.unitCount = _.isNil(event.count) ? undefined : Number(event.count);
        this.update();
    }

    @actionEvent(_EventChangeMedicineUsageFreq)
    async *_mapEventChangeMedicineUsageFreq(event: _EventChangeMedicineUsageFreq): AsyncGenerator<State> {
        if (!!event.freq) {
            event.formItem.freq = event.freq;
            this.update();
        }
    }

    @actionEvent(_EventModifyAcuPointList)
    async *_mapEventModifyAcuPointList(event: _EventModifyAcuPointList): AsyncGenerator<State> {
        const formItem = event.formItem;
        const acuList: AcupunctureItem[] = [];
        const acuNameList = new Map<String, AcuPointsItem>();
        formItem.acupoints?.forEach((item) => {
            acuNameList.set(item.name ?? "", item);
            acuList.push(item as AcupunctureItem);
        });

        const pointInfo: AbcSet<AcupunctureItem> = await AcupunctureDialog.show(acuList);
        if (pointInfo) {
            const acuList: AcuPointsItem[] = [];
            pointInfo.forEach((item) => {
                if (acuNameList.has(item.name) && acuNameList.get(item.name)) {
                    acuList.push(acuNameList.get(item.name)!);
                } else {
                    const _item = item as AcuPointsItem;
                    if (_item.type == 1) {
                        _item.position = "双";
                    } else {
                        _item.position = "单";
                    }
                    acuList.push(_item);
                }
            });
            event.formItem.acupoints = acuList;
            this.calculationUnitCount(event.formItem);
            this.update();
        }
    }

    @actionEvent(_EventBackPage)
    async *_mapEventBackPage(): AsyncGenerator<State> {
        const formDetail = this.innerState.formDetail;
        ABCNavigator.pop(formDetail);
    }

    @actionEvent(_EventChangeExternalType)
    async *_mapEventChangeExternalType(): AsyncGenerator<State> {
        const state = this.innerState,
            { formDetail } = state,
            { usageType, usageSubType, specification } = formDetail ?? {};
        const prescriptionType = {
            usageType: usageType,
            usageSubType: usageSubType,
            specification: specification,
        };
        const result = await ExternalPrescriptionTypeDialog.show({ externalPrescriptionType: prescriptionType });
        if (!!result) {
            formDetail!.usageType = result.usageType;
            formDetail!.usageSubType = result.usageSubType;
            formDetail!.specification = result.specification;
            //切换类型时，如果当前类型不是现配贴，则应该清空药品，避免在接诊处展示与现在所选不同
            if (!formDetail?.isSelfPrepared) {
                formDetail?.prescriptionFormItems?.forEach((formItem) => {
                    formItem.externalGoodsItems = [];
                });
            }
            // 如果是贴敷切换其他类型，清空药品、商品、物资
            if (prescriptionType.usageType == ExternalPRUsageTypeEnum.tieFu) {
                if (prescriptionType.usageType !== result.usageType || prescriptionType.usageSubType !== result.usageSubType) {
                    _.remove(
                        formDetail?.prescriptionFormItems ?? [],
                        (formItem) =>
                            formItem.goodsInfo.isGoods ||
                            formItem.goodsInfo.type == GoodsType.medicine ||
                            formItem.goodsInfo.type == GoodsType.material ||
                            formItem.goodsInfo.type == GoodsType.goods
                    );
                }
            }
            formDetail?.prescriptionFormItems?.map((item) => {
                if (["穴", "贴"].includes(item.unit ?? "")) {
                    item.unit = "穴";
                    // let unit = "穴";
                    // if (formDetail?.usageType === ExternalPRUsageTypeEnum.tieFu) {
                    //     unit = "贴";
                    // }
                    // item.unit = unit;
                }
            });
            this.update();
        }
    }

    @actionEvent(_EventSaveTemplate)
    async *_mapEventSaveTemplate(): AsyncGenerator<State> {
        if (!this.innerState.formDetail?.prescriptionFormItems?.length) {
            return Toast.show("未添加任何项目", { warning: true });
        }
        const temp = JsonMapper.deserialize(PrescriptionTemplateInfo, { prescriptionExternalForms: [this.innerState.formDetail] });
        await SaveTemplateDialog.show(temp);
    }

    /**
     * 添加选中药品
     * @param medicine
     */
    requestAddSelectMedicine(medicine: GoodsInfo): void {
        this.dispatch(new _EventAddSelectMedicine(medicine));
    }

    @actionEvent(_EventUseTemplate)
    async *_mapEventUseTemplate(): AsyncGenerator<State> {
        const tp = await OutpatientPrescriptionTemplateSearchDialog.show(false, { filterType: MedicineAddType.external });
        if (!tp) return;
        const refreshResult = await OutpatientUtils.refreshGoodsInfo([...(tp.prInfo?.prescriptionExternalForms ?? [])], "");

        if (!!refreshResult) {
            return await Toast.show(`添加失败 ${errorToStr(refreshResult)}`, {
                warning: true,
            });
        }

        const prescription = ABCUtils.first(tp.tempDetail?.prescriptionExternalForms ?? []);
        if (!prescription) return;
        //收集信息和用法
        prescription.prescriptionFormItems?.forEach((formItem) => {
            //解决重复药品之间相互联动的问题 TODO ??
            formItem.keyId = formItem.keyId ?? UUIDGen.generate();
            formItem.productInfo = GoodsUtils.changeGoodsUnitPriceWithUnit(formItem.goodsInfo, {
                unitPrice: formItem.unitPrice,
                unit: formItem.unit,
            });
            formItem.dosageUnit = _.isEmpty(formItem.dosageUnit) ? "次" : formItem.dosageUnit;
            MedicineAddPageUtils.computeMedicineUnitCount(formItem);
        });

        let _addType = MedicineTemplateAddType.reset;
        if (!!this.innerState.formDetail?.prescriptionFormItems?.length) {
            _addType = await MedicineTemplateAddTypeDialog.show({
                title: tp.name,
                content: "确认覆盖当前处方",
                buttonType: [
                    { name: "确定", value: MedicineTemplateAddType.reset },
                    { name: "取消", value: MedicineTemplateAddType.cancel },
                ],
            });
        }
        if (_addType == MedicineTemplateAddType.reset) {
            this.innerState.formDetail = prescription;
        } else if (_addType == MedicineTemplateAddType.push) {
            const prescriptionFormItems = this.innerState.formDetail?.prescriptionFormItems ?? [];
            Object.assign(this.innerState.formDetail, {
                prescriptionFormItems: prescriptionFormItems.concat(prescription.prescriptionFormItems ?? []),
            });
        }
        this.update();
    }

    @actionEvent(_EventSelectPrescriptionLabel)
    async *_mapEventSelectPrescriptionLabel(/*event: _EventSelectPrescriptionLabel*/): AsyncGenerator<State> {
        const psychotropicTypeList = [
            { index: 0, label: "无", value: PsychotropicNarcoticTypeEnum.NONE },
            { index: 1, label: "普通", value: PsychotropicNarcoticTypeEnum.PUTONG },
            { index: 2, label: "儿科", value: PsychotropicNarcoticTypeEnum.ERKE },
            { index: 3, label: "慢病", value: PsychotropicNarcoticTypeEnum.MANBING },
            { index: 4, label: "老年病", value: PsychotropicNarcoticTypeEnum.LAONIANBING },
            { index: 5, label: "急诊", value: PsychotropicNarcoticTypeEnum.JIZHEN },
            { index: 6, label: "长期", value: PsychotropicNarcoticTypeEnum.CHANGQI },
        ];
        const _initialSelectIndexes =
            psychotropicTypeList.find((i) => i.value == this.innerState.formDetail?.psychotropicNarcoticType)?.index ?? 0;
        const selectIndex = await AbcDialog.showOptionsBottomSheet({
            title: "处方类型",
            options: psychotropicTypeList?.map((item) => item.label),
            initialSelectIndexes: new Set<number>([_initialSelectIndexes]),
            crossAxisCount: 3,
            height: pxToDp(500),
        });
        let _selectIndex = selectIndex;
        if (selectIndex?.[0] == 1) {
            _selectIndex = [PsychotropicNarcoticTypeEnum.PUTONG];
        } else if (selectIndex?.[0] == 2) {
            _selectIndex = [PsychotropicNarcoticTypeEnum.ERKE];
        } else if (selectIndex?.[0] == 3) {
            _selectIndex = [PsychotropicNarcoticTypeEnum.MANBING];
        } else if (selectIndex?.[0] == 4) {
            _selectIndex = [PsychotropicNarcoticTypeEnum.LAONIANBING];
        } else if (selectIndex?.[0] == 5) {
            _selectIndex = [PsychotropicNarcoticTypeEnum.JIZHEN];
        }
        if (!_selectIndex || !_selectIndex.length) return;
        this.innerState.formDetail!.psychotropicNarcoticType = _selectIndex?.[0] ?? 0;
        this.update();
    }

    /**
     * 删除选中的现配药品信息
     * @param formItem
     * @param medicine
     */
    requestDeleteMedicine(formItem: PrescriptionFormItem, medicine: ExternalGoodsItem): void {
        this.dispatch(new _EventDeleteMedicine(formItem, medicine));
    }

    /**
     * 保存页面中的输入框组件
     * @param element
     */
    requestCacheSearchInput(element?: AbcTextInput | null): void {
        if (!element) return;
        this.dispatch(new _EventCacheSearchInput(element));
    }

    @actionEvent(_EventUpdate)
    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    /**
     * 搜索项目或者穴位
     * @param keyword
     */
    requestSearchGoodsList(keyword: string): void {
        this.dispatch(new _EventSearchGoodsList(keyword));
    }

    /**
     * 添加选中项目
     * @param goodsInfo
     */
    requestAddSelectGoods(goodsInfo: GoodsInfo): void {
        this.dispatch(new _EventAddSelectGoods(goodsInfo));
    }

    /**
     * 添加选中穴位
     * @param acuPoint
     */
    requestAddSelectAcuPoint(acuPoint: SuggestSearchItem): void {
        const type = Number(acuPoint.type);
        const id = acuPoint.id;
        const jiaJi = AcuPointsId.JIA_JI; // 夹脊
        const aShiXue = AcuPointsId.A_SHI_XUE; // 阿是穴
        let position: string;
        // 阿是穴、夹脊是选择穴位数量 需单独处理（默认为1）
        if (id == aShiXue || id == jiaJi) {
            position = "1";
        } else if (type == 1) {
            position = "双";
        } else {
            position = "单";
        }
        this.dispatch(
            new _EventAddSelectAcuPoint(
                JsonMapper.deserialize(AcuPointsItem, {
                    id: acuPoint.id,
                    name: acuPoint.name,
                    position: position,
                    type: type,
                })
            )
        );
    }

    /**
     * 删除添加的项目
     * @param formItem
     */
    requestDeleteGoods(formItem: PrescriptionFormItem): void {
        this.dispatch(new _EventDeleteGoods(formItem));
    }

    /**
     * 删除选中的穴位信息
     * @param formItem
     * @param acuPoint
     */
    requestDeleteAcuPoint(formItem: PrescriptionFormItem, acuPoint: AcuPointsItem): void {
        this.dispatch(new _EventDeleteAcuPoint(formItem, acuPoint));
    }

    /**
     * 激活添加穴位输入框
     * @param formItem
     * @param searchMode
     */
    requestChangeToAddAcuPoint(formItem: PrescriptionFormItem, searchMode = SearchMode.acuPoints): void {
        this.dispatch(new _EventChangeToAddAcuPoint(formItem, searchMode));
    }

    /**
     * 更改项目的用量
     * @param formItem
     * @param acuPoint
     * @param calculation 是否需要重新计算单位数量
     */
    requestChangeGoodsInfoUnitCount(formItem: PrescriptionFormItem, acuPoint?: number, calculation?: boolean): void {
        this.dispatch(new _EventChangeGoodsInfoUnitCount(formItem, acuPoint, calculation));
    }

    /**
     * 直接改变备注
     * @param formItem
     * @param text
     */
    requestChangeRequirementText(formItem: PrescriptionFormItem, text: string): void {
        this.dispatch(new _EventChangeRequirementText(formItem, text));
    }

    /**
     * 修改穴位位置
     * @param formItem
     * @param pointInfo
     */
    requestModifyAcuPointInfo(formItem: PrescriptionFormItem, pointInfo: AcuPointsItem): void {
        this.dispatch(new _EventModifyAcuPointInfo(formItem, pointInfo));
    }

    /**
     * 修改项目技术计数方式
     * @param formItem
     * @param billingType
     */
    requestChangeMedicineUsageBizExtension(formItem: PrescriptionFormItem, billingType: number): void {
        this.dispatch(new _EventChangeMedicineUsageBizExtension(formItem, billingType));
    }

    /**
     * 保存为模板
     */
    requestSaveTemplate(): void {
        this.dispatch(new _EventSaveTemplate());
    }

    requestUseTemplate(): void {
        this.dispatch(new _EventUseTemplate());
    }

    /**
     * 修改频率
     * @param formItem
     * @param freq
     */
    requestChangeMedicineUsageFreq(formItem: PrescriptionFormItem, freq?: string): void {
        this.dispatch(new _EventChangeMedicineUsageFreq(formItem, freq));
    }

    /**
     * 点击完成时
     */
    requestSubmit(): void {
        this.dispatch(new _EventSubmit());
    }

    /**
     * 修改穴位位置信息
     * @param formItem
     */
    requestModifyAcuPointList(formItem: PrescriptionFormItem): void {
        this.dispatch(new _EventModifyAcuPointList(formItem));
    }

    /**
     * 修改现配方药品使用量
     */
    requestModifyMedicineUnitCount(formItem: PrescriptionFormItem, externalGoodsItem: ExternalGoodsItem, count?: string): void {
        this.dispatch(new _EventModifyMedicineUnitCount(formItem, externalGoodsItem, count));
    }

    /**
     * 退出页面带返回值
     */
    requestBackPage(): void {
        this.dispatch(new _EventBackPage());
    }

    /**
     * 修改外治处方类型
     */
    requestChangeExternalType(): void {
        this.dispatch(new _EventChangeExternalType());
    }

    /**
     * 选择处方标签
     */
    requestSelectPrescriptionLabel(): void {
        this.dispatch(new _EventSelectPrescriptionLabel());
    }

    /**
     * 修改商品的使用数量
     */
    requestChangeGoodsExternalUnitCount(formItem: PrescriptionFormItem, unitCount?: number): void {
        this.dispatch(new _EventChangeGoodsExternalUnitCount(formItem, unitCount));
    }

    /**
     * 修改商品的使用单位
     */
    requestChangeGoodsExternalUnit(formItem: PrescriptionFormItem, unit?: string): void {
        this.dispatch(new _EventChangeGoodsExternalUnit(formItem, unit));
    }

    /**
     * 处方排序
     * @param formItem
     */
    requestUpdateFormItemSort(formItem: PrescriptionFormItem): void {
        this.dispatch(new _EventUpdateFormItemSort(formItem));
    }

    _calculatePrice(): Promise<OutpatientInvoiceDetail> {
        this.innerState.calculating = true;
        this.update();
        if (_.isNumber(this.innerState.detailData!.expectedTotalPrice)) {
            this.innerState.detailData!.adjustmentFee = undefined;
        }
        //给相应处方赋值
        this.innerState.detailData!.prescriptionExternalForms = [this.innerState.formDetail!];
        return OutpatientAgent.chargeCalculate(this.innerState.detailData!);
    }

    private async _initPageConfig(): Promise<void> {
        this.innerState.pharmacyInfoConfig = userCenter.inventoryClinicConfig;
        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig(true).catchIgnore();
    }

    @actionEvent(_EventUpdateCalculatePrice)
    async *_mapEventUpdateCalculatePrice(event: _EventUpdateCalculatePrice): AsyncGenerator<State> {
        this._doUpdateCalculatePrice(event);
        const formDetail = this.innerState.formDetail;
        this._filterExternalPrescriptionForms()?.forEach((form) => {
            formDetail?.prescriptionFormItems?.map((formItem) => {
                form?.prescriptionFormItems?.map((item) => {
                    let isSameItem = false;
                    if (item.keyId && formItem.keyId) {
                        isSameItem = item.keyId == formItem.keyId;
                    } else {
                        isSameItem = item.displayName == formItem.displayName;
                    }
                    if (isSameItem) {
                        Object.assign(formItem, {
                            expectedTotalPrice: item.expectedTotalPrice,
                            expectedUnitPrice: item.expectedUnitPrice,
                            expectedTotalPriceRatio: item.expectedTotalPriceRatio,
                            totalPrice: item.totalPrice,
                            totalPriceRatio: item.totalPriceRatio,
                            unitPrice: item.unitPrice,
                            fractionPrice: item.fractionPrice,
                        });
                    }
                });
            });
        });
        this.update();
    }

    /**
     * 将group转换成各个处方form结构
     */
    _changeGoodsInfoToForm(): void {
        const formDetail = this.innerState.formDetail;
        if (!formDetail) return;
        formDetail.keyId = formDetail.keyId ?? UUIDGen.generate();
        formDetail.prescriptionFormItems?.map((item, index) => {
            item.keyId = item.keyId ?? UUIDGen.generate();
            item.sort = item.sort ?? (formDetail.sort ?? 9) * 1000 + index;
        });
    }

    /**
     * 清空议价信息
     * @param formDetail
     */
    _clearAdjustmentFee(formDetail: PrescriptionFormItem): void {
        if (!formDetail) return;
        this._changeGoodsInfoToForm();
        this._filterExternalPrescriptionForms()?.forEach((form) => {
            form.expectedTotalPrice = undefined;
            const sameItem = form.prescriptionFormItems?.find((t) => t.keyId == formDetail.keyId);
            if (sameItem) {
                sameItem.expectedTotalPriceRatio = undefined;
                sameItem.expectedTotalPrice = undefined;
                sameItem.expectedUnitPrice = sameItem.unitPrice;
                sameItem.isTotalPriceChanged = 0;
                sameItem.isUnitPriceChanged = 0;
                sameItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
            }
        });
        this._calculatePriceTrigger.next();
    }

    private calculationUnitCount(formItem: PrescriptionFormItem): void {
        if (formItem.billingType == 1) return;
        let count = 0;
        formItem.acupoints?.forEach((item) => {
            if (item.position == "双") {
                count += 2;
            } else {
                count += 1;
            }
        });
        count = count ? count : 1;
        formItem.unitCount = count * ((formItem.dosage as number) ?? 0) * (formItem.externalUnitCount ?? 1);
        formItem.useDismounting = formItem.unit ? (formItem.goodsInfo.useDismounting(formItem.unit) ? 1 : 0) : undefined;
    }
    _copyPriceAttrTo(fromForms: Array<any>, toForms: Array<any>, shouldNotCopyExpectedPrice?: boolean): void {
        if (ABCUtils.isEmpty(fromForms) || ABCUtils.isEmpty(toForms)) return;

        for (const form of fromForms) {
            const originalForm = toForms.find((item) => item.keyId == form.keyId);
            originalForm.totalPrice = form.totalPrice;
            originalForm.isTotalPriceChanged = form.isTotalPriceChanged;
            originalForm.sheetFlatPrice = form.sheetFlatPrice;
            originalForm.sourceTotalPrice = form.sourceTotalPrice;
            originalForm.expectedTotalPrice = form.expectedTotalPrice;
            originalForm.totalPriceRatio = form.totalPriceRatio;

            let fromFormItems: Array<PrescriptionFormItem>;
            let toFormItems: Array<PrescriptionFormItem>;

            if (form instanceof PrescriptionProductForm) {
                fromFormItems = form.productFormItems!;
                toFormItems = originalForm.productFormItems;
            } else {
                if (form.pharmacyType == PharmacyType.air) {
                    originalForm.expectedTotalPrice = form.expectedTotalPrice;
                }
                fromFormItems = form.prescriptionFormItems;
                toFormItems = originalForm.prescriptionFormItems;
            }

            for (const formItem of fromFormItems) {
                const originalFormItem = toFormItems.find((item) => item.keyId == formItem.keyId);
                originalFormItem!.totalPrice = formItem.totalPrice;
                originalFormItem!.sourceUnitPrice = formItem.sourceUnitPrice;
                originalFormItem!.sourceTotalPrice = formItem.sourceTotalPrice;
                originalFormItem!.unitPrice = formItem.unitPrice;
                originalFormItem!.formFlatPrice = formItem.formFlatPrice;
                originalFormItem!.sheetFlatPrice = formItem.sheetFlatPrice;
                originalFormItem!.fractionPrice = formItem.fractionPrice;
                originalFormItem!.isUnitPriceChanged = formItem.isUnitPriceChanged;
                originalFormItem!.isTotalPriceChanged = formItem.isTotalPriceChanged;
                originalFormItem!.currentUnitPrice = formItem.currentUnitPrice;
                originalFormItem!.totalPriceRatio = formItem.totalPriceRatio;
                originalFormItem!.productInfo = formItem?.productInfo;
                originalFormItem!.expectedTotalPrice = formItem.expectedTotalPrice;
                originalFormItem!.expectedUnitPrice = formItem.expectedUnitPrice;
                originalFormItem!.batchInfos = formItem.batchInfos;
                if (!shouldNotCopyExpectedPrice) {
                    if (!_.isNil(formItem!.expectedTotalPrice)) {
                        originalFormItem!.expectedTotalPrice = formItem.expectedTotalPrice;
                    }
                    originalFormItem!.expectedUnitPrice = formItem.expectedUnitPrice;
                    originalFormItem!.expectedTotalPriceRatio = formItem.expectedTotalPriceRatio;
                }
            }
        }
    }
    _doUpdateCalculatePrice(event: _EventUpdateCalculatePrice): void {
        this.innerState.calculating = event.calculating;
        this.innerState.calculateFailed = event.error;
        this.update();

        if (event.calculateRspData == null) return;

        this.innerState.detailData!.totalPrice = event.calculateRspData.totalPrice;
        this.innerState.detailData!.adjustmentFee = event.calculateRspData.adjustmentFee;
        // this.innerState.detailData!.expectedTotalPrice = event.calculateRspData.expectedTotalPrice;
        this.innerState.detailData!.sourceTotalPrice = event.calculateRspData.sourceTotalPrice;
        this.innerState.detailData!.isTotalPriceChanged = event.calculateRspData.isTotalPriceChanged;
        this.innerState.detailData!.registrationFee = event.calculateRspData.registrationFee; //挂号费也进行摊费
        this.innerState.detailData!.chargeRoundingTips = _.isNil(event.calculateRspData.chargeRoundingTips)
            ? undefined
            : event.calculateRspData.chargeRoundingTips;
        this._copyPriceAttrTo(
            event.calculateRspData.productForms ?? [],
            this.innerState.detailData?.productForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionChineseForms ?? [],
            this.innerState.detailData?.prescriptionChineseForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionWesternForms ?? [],
            this.innerState.detailData?.prescriptionWesternForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionInfusionForms ?? [],
            this.innerState.detailData?.prescriptionInfusionForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
        this._copyPriceAttrTo(
            event.calculateRspData.prescriptionExternalForms ?? [],
            this.innerState.detailData?.prescriptionExternalForms ?? [],
            event.shouldNotCopyExpectedPrice
        );
    }

    private _initPageTrigger(): void {
        this._loadDataTrigger
            .pipe(
                debounce((/*ignored*/) => {
                    this.innerState.loading = true;
                    this.innerState.loadError = undefined;
                    this.update();
                    if (_.isEmpty(this.innerState.keyword)) return of(0);
                    return delayed(200);
                }),
                switchMap(() => {
                    if (!this.innerState.keyword) {
                        this.innerState.searchGoodsList = undefined;
                        return of(null);
                    }
                    const state = this.innerState,
                        { specification } = state.formDetail ?? {};
                    return GoodsAgent.smartSearchMedicine({
                        keyword: this.innerState.keyword ?? "",
                        jsonType: [
                            {
                                type: GoodsType.medicine,
                                subType: [GoodsSubType.medicineChinese],
                            },
                        ],
                        spec: specification,
                    })
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) {
                    this.update();
                    return;
                }
                if (rsp instanceof ABCError) {
                    this.innerState.loading = false;
                    this.innerState.loadError = rsp;
                } else {
                    this.innerState.loading = false;
                    this.innerState.searchGoodsList = rsp.map((item) => {
                        item.type = GoodsType.medicine;
                        return item;
                    });
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._loadAcuPointTrigger
            .pipe(
                debounce((/*ignored*/) => {
                    if (_.isEmpty(this.innerState.keyword)) return of(0);
                    return delayed(200);
                }),
                switchMap(() => {
                    if (!this.innerState.keyword) {
                        this.innerState.searchAcuPointsList = undefined;
                        return of(null);
                    }
                    this.innerState.loading = true;
                    this.innerState.loadError = undefined;
                    this.update();
                    return SuggestService.suggestSearch({ client: "acupuncture-point", keyword: this.innerState.keyword })
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) {
                    this.update();
                    return;
                }
                if (rsp instanceof ABCError) {
                    this.innerState.loadError = rsp;
                    this.innerState.loading = false;
                } else {
                    this.innerState.loading = false;
                    this.innerState.searchAcuPointsList = rsp.hits;
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._calculatePriceTrigger
            .pipe(
                debounceTime(300),
                switchMap(() => {
                    if (!this.innerState.detailData) return of(null);
                    return fromPromise(
                        this._calculatePrice()
                            .then((rsp) => {
                                return { calculateRspData: rsp };
                            })
                            .catch((e) => new ABCError(e))
                    );
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                if (rsp instanceof ABCError) {
                    this.dispatch(
                        new _EventUpdateCalculatePrice({
                            calculating: false,
                            calculateRspData: null,
                            error: rsp,
                            shouldNotCopyExpectedPrice: false,
                        })
                    );
                } else {
                    const { calculateRspData } = rsp;
                    this.dispatch(
                        new _EventUpdateCalculatePrice({
                            calculating: false,
                            calculateRspData: calculateRspData,
                            error: null,
                            shouldNotCopyExpectedPrice: false,
                        })
                    );
                }
            })
            .addToDisposableBag(this);
    }
    /**
     * 筛选出对应药房的外治处方
     */
    _filterExternalPrescriptionForms(): PrescriptionExternalForm[] {
        return (
            this.innerState.detailData?.prescriptionExternalForms?.filter((t) =>
                !!this._prescriptionFormKeyId ? t.keyId == this._prescriptionFormKeyId : true
            ) ?? []
        );
    }

    private _initDetailData(): void {
        //初始化form结构
        if (!this.innerState.formDetail) {
            this.innerState.formDetail = new PrescriptionExternalForm();
            Object.assign(this.innerState.formDetail, {
                keyId: UUIDGen.generate(),
                prescriptionFormItems: [],
                usageType: 0, //外治处方-类型
                usageSubType: 0, //外治处方-类型
                specification: "中药饮片", //现配贴药品类型
                psychotropicNarcoticType: this.pageProps.psychotropicNarcoticType, //外治处方-麻醉药品类型
            });
        }

        if (!this.innerState.detailData) return;
        this.innerState.detailData?.productForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.productFormItems?.map((item) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
            });
        });
        this.innerState.detailData?.prescriptionChineseForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });
        this.innerState.detailData?.prescriptionInfusionForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });
        this.innerState.detailData?.prescriptionWesternForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });
        this.innerState.detailData?.prescriptionExternalForms?.map((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.prescriptionFormItems?.map((item, index) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.sort = item.sort ?? (form.sort ?? 9) * 1000 + index;
            });
        });

        this.innerState.detailData = OutpatientUtils.tripChargedItemForOutpatientPriceCalculate(this.innerState.detailData);
    }

    @actionEvent(_EventInit)
    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        await this._initPageConfig();
        this._initPageTrigger();
        this._initDetailData();
        if (this.innerState.currentFocusItem) {
            const isNowStick = this.innerState.usageSubTypeDisplay == "现配贴";
            if (isNowStick) {
                this._searchInput?.focus();
            }
            this.innerState.searchMode = isNowStick ? SearchMode.chineseMedicine : SearchMode.acuPoints;
            this.innerState.focusViewKey = this.innerState.currentFocusItem.keyId;
            this.update(ScrollToViewState.fromState(this.innerState));
        } else {
            this.update();
        }
        this._calculatePriceTrigger.next();
    }

    // 外治处方中的formItems根据sort进行升序
    private _sortFormItems(): void {
        this.innerState.formDetail?.prescriptionFormItems?.sort((a, b) => {
            if (a.sort && b.sort) {
                return a.sort - b.sort;
            }
            return 0;
        });
    }

    @actionEvent(_EventUpdateFormItemSort)
    private async *_mapEventUpdateFormItemSort(event: _EventUpdateFormItemSort): AsyncGenerator<State> {
        if (!event.formItem) return;
        this.innerState.currentSelectItemKeyId = event.formItem.keyId;
        yield this.innerState;
        const _initIndex = (event.formItem.sort ?? 0) - 1 ?? -1;
        const _options: string[] = _.range(1, 10).map((item) => "组" + item.toString());
        const selects = await AbcDialog.showOptionsBottomSheet({
            title: "选择分组",
            options: _options,
            initialSelectIndexes: _.isUndefined(_initIndex) ? _initIndex : new Set([_initIndex]),
        });
        this.innerState.currentSelectItemKeyId = undefined;
        yield this.innerState;
        if (!selects?.length || _initIndex == selects![0]) {
            return;
        }
        event.formItem.sort = selects[0] + 1;
        this._sortFormItems();
        this.update();
    }
}
