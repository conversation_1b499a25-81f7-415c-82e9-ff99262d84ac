/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import {
    ChineseMedicineSpecType,
    DeliveryCompany,
    DeliveryPayType,
    DentistryMedicalRecordItem,
    ExtendDiagnosisInfosItem,
    GoodsInfo,
    GoodsSubType,
    GoodsType,
    GoodsTypeId,
    MedicalRecord,
    Patient,
    PrescriptionFormLogisticsTraceRsp,
    PsychotropicNarcoticTypeEnum,
    ExaminationResult,
    FormItemBatchInfos,
    PsychotropicNarcoticEmployee,
    SurgeryRequest,
} from "../../base-business/data/beans";
import { NumberUtils, UUIDGen } from "../../common-base-module/utils";
import _, { isNil } from "lodash";
import "reflect-metadata";
import { fromJsonToDate, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { MedicineUsage } from "../medicine-add-page/medicine-add-page-bean";
import { FormDataDetail, FormDetail } from "../../base-business/data/form-beans";
import { DataBase } from "../../views/list-data-holder";
import { ChargeFormStatus, ChargeSourceFormType, DispensingFormItemsSourceItemType, ProcessRule } from "../../charge/data/charge-beans";
import {
    AirPharmacyVendor,
    BusinessScopeConfig,
    MedicalState,
    MedicineScopeId,
    PharmacyType,
} from "../../charge/data/charge-bean-air-pharmacy";
import { PrescriptionDefaultUsage } from "./chinese-medicine-config";
import { RegistrationDoctorScheduleStatusList, RegistrationRevisitStatus } from "../../registration/data/bean";
import { PharmacyListConfig } from "../../inventory/data/inventory-bean";
import { userCenter } from "../../user-center";
import { ToothNos } from "../../data/tooth-bean";
import { OutpatientUtils } from "../utils/outpatient-utils";
import moment from "moment";
import { ExternalPRUsageTypeEnum, TieFuUsageSubTypeEnum } from "../../../assets/medicine_usage/outpatient-external-config";
import { PatientGuardianDetail } from "../../data/patient-bean";
import { ConsultantList } from "../../base-business/data/clinic-data";

export class OutpatientTabId {
    static normal = 1; //线下
    static online = 2; //线上
}

// 门诊单状态
export class OutpatientInvoiceStatus {
    static waitVisit = 0; //待诊

    static visited = 1; //已诊
    static diagnosing = 2; //就诊中
    static waitingForTriage = 10; //待分诊
    static expired = 20; //过期
    static draft = 99; //草稿
}

export class OutpatientInvoiceSubStatus {
    static continueDiagnose = 1; // 回诊 -- status:1
}

export class AstResult {
    result?: string;
    description?: string;
}

export class AcuPointsItem {
    id?: string;
    name?: string;
    position?: string;
    type?: number;
}

//外治处方现配贴项
export class ExternalGoodsItem {
    medicineCadn?: string;
    name?: string;
    manufacturer?: string;
    type?: number;
    subType?: number;
    unit?: string;
    useDismounting?: number;
    keyId?: string;
    goodsId?: string;
    unitCount?: number;
    sort?: number;
    cMSpec?: string;

    get isChineseMedicine(): boolean {
        return this.type == GoodsType.medicine && GoodsSubType.medicineChinese == this.subType;
    }

    get displayName(): string {
        if (this.isChineseMedicine) {
            if (this.medicineCadn) return this.medicineCadn;

            if (this.name) return this.name;
            return "";
        }

        let fullName = "";
        if (this.medicineCadn) {
            fullName = this.medicineCadn;
        }

        if (this.name) {
            if (fullName) {
                fullName += `(${this.name})`;
            } else fullName = this.name;
        }

        return fullName;
    }
}

export class VerifySignaturesItem {
    keyId?: string;
    goodsId?: string;
    cadn?: string;
    status?: number; // 1 确认签字
}

export class PrescriptionFormItem {
    id?: string;
    patientOrderId?: string;
    clinicId?: string;
    outpatientSheetId?: string;
    prescriptionFormId?: string;
    goodsId?: string;
    domainMedicineId?: string;
    type?: number;
    subType?: number;
    medicineCadn?: string;
    name?: string;
    specification?: string;
    manufacturer?: string;

    /**
     * @description null -> 处方未开启皮试
     * @description number -> 未勾选
     */
    ast?: number | null;
    usage?: string;

    //  //特殊处理，用于兼容处方模板调用里返回的字符串类型
    ivgtt?: number;

    ivgttUnit?: string;
    freq?: string;
    dosage?: string | number;
    dosageUnit?: string;
    days?: number;
    specialRequirement?: string;
    useDismounting?: number;

    unitCount?: number;
    unit?: string;
    externalUnitCount?: number; // 外治处方-商品项数量

    unitPrice?: number;
    costUnitPrice?: number;
    sort?: number;
    groupId?: number;
    stockPieceCount?: number;
    stockPackageCount?: number;

    @JsonProperty({ type: AstResult })
    astResult?: AstResult;
    chargeStatus?: number;

    @JsonProperty({ type: GoodsInfo })
    productInfo?: GoodsInfo;

    // @JsonProperty({ type: MedicineUsage })
    inputUsages?: MedicineUsage;

    keyId?: string;
    productId?: string;

    totalPrice?: number;
    sourceUnitPrice?: number;
    sourceTotalPrice?: number;

    //在单项议价时用于后台算费
    expectedUnitPrice?: number; //[议价三总模式互斥][议单价]议单价
    expectedTotalPrice?: number; //[议价三总模式互斥][议单项总价]
    expectedTotalPriceRatio?: number; //[议价三总模式互斥][议单项总价比例]
    totalPriceRatio?: number;
    unitAdjustmentFeeLastModifiedBy?: string; //议价修改人id(当前登录人id)
    fractionPrice?: number;
    /***
     * form上平摊下来的金额
     */
    formFlatPrice?: number;
    /**
     * sheet上平摊下来的金额
     */
    sheetFlatPrice?: number;

    isUnitPriceChanged?: number;
    isTotalPriceChanged?: number;

    currentUnitPrice?: number;

    ///商品信息
    dismounting?: number;

    packagePrice?: number;

    packageCostPrice?: number;

    piecePrice?: number;

    canAdjustment?: number; // 是否可以议价
    canAdjustmentFee?: number; // 能够议价的最低金额
    get isCanAdjustment(): boolean {
        return this.canAdjustment == 1;
    }

    pieceNum?: number;
    pieceUnit?: string;
    packageUnit?: string;
    medicineDosageNum?: number;
    medicineDosageUnit?: string;
    medicineNmpn?: string;
    materialSpec?: string;
    cMSpec?: string;
    __cMSpec?: string; //终端使用字段，保存药品转换前的类型
    grade?: string;

    //外治处方-穴位相关
    @JsonProperty({ type: Array, clazz: AcuPointsItem })
    acupoints?: AcuPointsItem[];
    @JsonProperty({ type: Array, clazz: ExternalGoodsItem })
    externalGoodsItems?: ExternalGoodsItem[];
    billingType?: number; //计算方式
    /**
     * 计算穴位数量
     */
    get acuPointsCount(): number {
        let count = 0;
        this.acupoints?.forEach((it) => {
            if (it.name) {
                if (it.position === "双") {
                    count += 2;
                } else {
                    count++;
                }
            }
        });
        return count || 1;
    }

    remark?: string; //治疗项目备注

    chargeType?: number;

    sourceItemType?: DispensingFormItemsSourceItemType; // 1自备 0普通

    //【feature】诊疗项目支持医生护士添加
    nurseId?: string;
    nurseName?: string;
    doctorId?: string;
    doctorName?: string;
    departmentId?: string;
    departmentName?: string;

    pharmacyNo?: number;
    pharmacyName?: string;
    pharmacyType?: number;

    toothNos?: ToothNos;
    index?: number;
    needExecutive?: number;
    productFormId?: string;
    executeStatus?: number;
    executeStatusName?: string;
    @JsonProperty({ type: Array, clazz: ExaminationResult })
    examinationResult?: ExaminationResult[];
    @JsonProperty({ type: Array, clazz: FormItemBatchInfos })
    batchInfos?: FormItemBatchInfos[];
    lockId?: string; //进价加成-批次锁库ID

    @JsonProperty({ type: Array, clazz: GoodsInfo })
    children?: GoodsInfo[]; // 目前用于诊疗项目中手术类别
    @JsonProperty({ type: SurgeryRequest })
    surgeryDetail?: SurgeryRequest; // 手术相关的申请单
    @JsonProperty({ type: Array, clazz: GoodsInfo })
    composeChildren?: GoodsInfo[]; // 套餐

    hasResultExaminations(): ExaminationResult[] {
        if (Array.isArray(this.examinationResult)) {
            return this.examinationResult.filter((item) => {
                return item.status === 1;
            });
        }
        return [];
    }

    // 药品签字
    @JsonProperty({ type: Array, clazz: VerifySignaturesItem })
    verifySignatures?: VerifySignaturesItem[];

    //项目录入字段（治疗理疗支持录入天数）
    dailyDosage?: number; //每天多少次

    get isWesternMedicine(): boolean {
        return this.type == GoodsType.medicine && GoodsSubType.medicineWestern == this.subType;
    }

    get isChineseWesternMedicine(): boolean {
        return this.type == GoodsType.medicine && GoodsSubType.medicineChinesePatent == this.subType;
    }

    get isChineseMedicine(): boolean {
        return this.type == GoodsType.medicine && GoodsSubType.medicineChinese == this.subType;
    }

    get isNotTreatment(): boolean {
        return this.type != GoodsType.treatment;
    }

    _computeGoodsInfo?: GoodsInfo;

    get goodsInfo(): GoodsInfo {
        if (this.productInfo != null && this.productInfo.displayName) {
            this.productInfo.keyId = this.productInfo.keyId ?? this.keyId;
            return this.productInfo;
        }

        if (this._computeGoodsInfo && this._computeGoodsInfo instanceof GoodsInfo) return this._computeGoodsInfo;

        this._computeGoodsInfo = JsonMapper.deserialize(GoodsInfo, {});
        this._computeGoodsInfo!.id = this.goodsId;
        this._computeGoodsInfo!.name = this.name;
        this._computeGoodsInfo!.medicineCadn = this.medicineCadn ?? this.productInfo?._medicineCadn;
        this._computeGoodsInfo!.type = this.type;
        this._computeGoodsInfo!.subType = this.subType;
        this._computeGoodsInfo!.manufacturer = this.manufacturer;
        this._computeGoodsInfo!.dismounting = this.dismounting;
        this._computeGoodsInfo!.packagePrice = this.packagePrice;
        this._computeGoodsInfo!.packageCostPrice = this.packageCostPrice;
        this._computeGoodsInfo!.pieceNum = this.pieceNum;
        this._computeGoodsInfo!.pieceUnit = this.pieceUnit;
        this._computeGoodsInfo!.packageUnit = this.packageUnit;
        this._computeGoodsInfo!.medicineDosageNum = this.medicineDosageNum;
        this._computeGoodsInfo!.medicineDosageUnit = this.medicineDosageUnit;
        this._computeGoodsInfo!.medicineNmpn = this.medicineNmpn;
        this._computeGoodsInfo!.materialSpec = this.materialSpec;
        this._computeGoodsInfo!.cMSpec = this.cMSpec;
        this._computeGoodsInfo!.grade = this.grade;
        this._computeGoodsInfo!.disableSell = this?.productInfo?.disableSell ?? 0;
        this._computeGoodsInfo!.keyId = this.productInfo?.keyId ?? this.keyId;

        return this._computeGoodsInfo!;
    }

    /**
     * 根据药房号重置库存
     */
    resetStockByPharmacyNo(): void {
        if (!this.goodsInfo) return;
        const res = this.goodsInfo?.pharmacyGoodsStockList?.find((item) => item?.pharmacyNo == this.pharmacyNo);
        const { stockPackageCount = 0, stockPieceCount = 0, pharmacyName = "" } = res || {};
        this.stockPackageCount = stockPackageCount;
        this.stockPieceCount = stockPieceCount;
        this.goodsInfo.stockPackageCount = stockPackageCount;
        this.goodsInfo.stockPieceCount = stockPieceCount;
        this.goodsInfo.pharmacyName = pharmacyName;
        this.goodsInfo.pharmacyNo = this.pharmacyNo;
    }

    //终端使用的数据，非后台协议字段

    get displayName(): string {
        if (this.medicineCadn) return this.medicineCadn;
        if (this.name) return this.name;

        return "";
    }
    // 目前只应用在模板显示的位置
    get displayMedicineCadnName(): string {
        if (this.name) return this.name;
        if (this.medicineCadn) return this.medicineCadn;
        return "";
    }

    displayUsageInfo(noIvgtt = false, separator = "，"): string {
        const usageItems = new Array<string>();
        if (this.usage != null) usageItems.push(this.usage);
        if (this.freq != null) usageItems.push(this.freq);
        if (this.dosage != null && this.dosageUnit != null) usageItems.push(`${this.dosage}${this.dosageUnit}`);
        if (this.days != null) usageItems.push(`${this.days}天`);
        if (!noIvgtt && this.ivgtt != null && this.ivgttUnit) usageItems.push(`${NumberUtils.formatMaxFixed(this.ivgtt)}${this.ivgttUnit}`);

        return usageItems.join(separator);
    }

    get externalUsageDisplay(): string | undefined {
        const usageSuffix = new Array<string>();
        if (this.dosage) usageSuffix.push(`共${this.dosage}次`);
        if (this.freq) usageSuffix.push(this.freq);
        if (this.specialRequirement) usageSuffix.push(`${this.specialRequirement}`);
        if (!usageSuffix.length) return undefined;
        return usageSuffix.join("，");
    }

    get localTotalPrice(): number {
        return (this.unitCount ?? 0) * (this.unitPrice ?? 0);
    }

    //是否修改了单价
    get UnitPriceChanged(): boolean {
        return this.isUnitPriceChanged == 1 ?? false;
    }

    //是否修改了总价
    get TotalPriceChanged(): boolean {
        return this.isTotalPriceChanged == 1 ?? false;
    }

    // 是自备药品吗
    get isSelfProvided(): boolean {
        return (
            this.sourceItemType == DispensingFormItemsSourceItemType.noCharge || this.specialRequirement == "【自备】" || !!this.chargeType
        );
    }
}

export class PrescriptionProductForm {
    id?: string;
    keyId?: string;
    patientOrderId?: string;
    outpatientSheetId?: string;
    patientId?: string;
    clinicId?: string;
    departmentId?: string;
    doctorId?: string;
    sort?: number;
    chargeStatus?: number;
    chargeStatusName?: string;
    sourceFormType?: number;

    @JsonProperty({ type: Array, clazz: PrescriptionFormItem })
    productFormItems?: Array<PrescriptionFormItem>;
    created?: string;
    lastModified?: string;
    totalPrice?: number;
    totalPriceRatio?: number;
    displayTotalPrice?: number;
    isTotalPriceChanged?: number;

    //收费信息
    receivableFee?: number; //应收金额
    receivedPrice?: number; //实收金额
    needPayFee?: number; //待收金额
    refundedFee?: number; //已退金额
    expectedTotalPrice?: number;

    get isTreatment(): boolean {
        const { ChargeSourceFormType } = require("../../charge/data/charge-beans");
        return this.sourceFormType == ChargeSourceFormType.treatment;
    }

    get isExamination(): boolean {
        const { ChargeSourceFormType } = require("../../charge/data/charge-beans");
        return this.sourceFormType == ChargeSourceFormType.examination;
    }

    get isGoods(): boolean {
        const { ChargeSourceFormType } = require("../../charge/data/charge-beans");
        return this.sourceFormType == ChargeSourceFormType.goods;
    }

    ///医疗器械
    get isMaterial(): boolean {
        const { ChargeSourceFormType } = require("../../charge/data/charge-beans");
        return this.sourceFormType == ChargeSourceFormType.material;
    }

    ///套餐
    get isPackage(): boolean {
        const { ChargeSourceFormType } = require("../../charge/data/charge-beans");
        return this.sourceFormType == ChargeSourceFormType.package;
    }

    //未收费状态
    get isCanEdit(): boolean {
        return !this.chargeStatus;
    }

    get isOtherFee(): boolean {
        return this.sourceFormType == ChargeSourceFormType.otherFee;
    }

    get isNurseFee(): boolean {
        return this.sourceFormType == ChargeSourceFormType.nurseProductFee;
    }

    get isSurgeryFee(): boolean {
        return this.sourceFormType == ChargeSourceFormType.surgeryFee;
    }

    //(应收 === 实收 + 已退)就是全收了
    get isPartChargeFee(): boolean {
        return (
            this.chargeStatus == ChargeFormStatus.charged &&
            this.receivableFee != (this.receivedPrice ?? 0) + Math.abs(this.refundedFee ?? 0)
        );
    }
}

export class VendorInfo {
    pharmacyType?: number;
    pharmacyNo?: number;
    pharmacyName?: string;
    vendorId?: string;
    vendorName?: string;
    vendorUsageScopeId?: string;
    processPrice?: number;
    processUsage?: string;
    usageScopeId?: string;
    medicineStateScopeId?: string;
    vendorAvailableMedicalStates?: MedicalState[];
    finishedRate?: number; // 成品率范围上限
    finishedRateMin?: number; // 成品率范围下限
    minimum?: number;
    ingredientPrice?: number; //辅料费
    @JsonProperty({ type: BusinessScopeConfig })
    businessScopeConfig?: BusinessScopeConfig;
}

export class PrescriptionFormDelivery {
    id?: string;
    primaryPrescriptionFormId?: string;
    addressProvinceId?: string;
    addressProvinceName?: string;
    addressCityId?: string;
    addressCityName?: string;
    addressDistrictId?: string;
    addressDistrictName?: string;
    addressDetail?: string;
    deliveryName?: string;
    deliveryMobile?: string;
    @JsonProperty({ type: DeliveryCompany })
    deliveryCompany?: DeliveryCompany;
    deliveryCompanyId?: string;
    deliveryOrderNo?: string;
    deliveryPayType?: number;
    deliveryFee?: number;
    @JsonProperty({ clazz: PrescriptionFormLogisticsTraceRsp })
    __logisticTraceList?: PrescriptionFormLogisticsTraceRsp; //终端自定义物流信息

    get getAddressInfo(): string {
        return (
            (!!this.addressProvinceName ? this.addressProvinceName : "") +
            (!!this.addressCityName ? "/" + this.addressCityName : "") +
            (!!this.addressDistrictName ? "/" + this.addressDistrictName : "") +
            (!!this.addressDetail ? "/" + this.addressDetail : "")
        );
    }

    get combineAddress(): string {
        return (
            (this.deliveryPayType == DeliveryPayType.freightCollect
                ? "到付"
                : this.deliveryPayType == DeliveryPayType.cashNow
                ? "寄付"
                : "") +
            (!!this.deliveryCompany?.name ? " · " + this.deliveryCompany?.name : "") +
            (!!this.deliveryOrderNo ? " · " + this.deliveryOrderNo : "") +
            (!!this.getAddressInfo ? " · " + this.getAddressInfo : "")
        );
    }

    get expressInfo(): string {
        return (
            (this.deliveryPayType == DeliveryPayType.freightCollect
                ? "到付"
                : this.deliveryPayType == DeliveryPayType.cashNow
                ? "寄付"
                : "") +
            (!!this.deliveryCompany?.name ? " · " + this.deliveryCompany?.name : "") +
            (!!this.deliveryOrderNo ? " · " + this.deliveryOrderNo : "")
        );
    }

    // 当前处方最新物流信息
    get getLatestLogisticTrace(): string {
        const logisticTraceList = this.__logisticTraceList?.traceList;
        if (!logisticTraceList) return "";
        return logisticTraceList?.[0]?.context ?? "";
    }

    get getCompanyNameAndDeliveryNo(): string {
        if (!!this.deliveryCompany?.name && !!this.deliveryOrderNo) {
            return `${this.deliveryCompany?.name}  ${this.deliveryOrderNo}`;
        }
        return "";
    }
}

export class BasePrescriptionForm {
    keyId?: string;
    id?: string;
    qrid?: string;
    @JsonProperty({ type: Date, fromJson: fromJsonToDate })
    created?: Date;
    @JsonProperty({ type: Date, fromJson: fromJsonToDate })
    lastModified?: Date;
    patientOrderId?: string;
    outpatientSheetId?: string;
    patientId?: string;
    clinicId?: string;
    departmentId?: string;
    doctorId?: string;

    chargeStatus?: number;
    chargeStatusName?: string;
    totalPrice?: number;
    expectedTotalPrice?: number; //期待总价
    sourceTotalPrice?: number; //原总价
    sheetFlatPrice?: number;
    isTotalPriceChanged?: number;
    //收费信息
    receivableFee?: number; //应收金额
    receivedPrice?: number; //实收金额
    needPayFee?: number; //待收金额
    refundedFee?: number; //已退金额

    contactMobile?: string;
    type?: number;
    specification?: string;
    doseCount?: number;
    dailyDosage?: string;
    usage?: string;
    freq?: string;
    requirement?: string;
    usageLevel?: string;
    usageDays?: string; // 空中药房调整   添加字段   服用时间
    sort?: number;
    isDecoction?: boolean;
    usageType?: number;
    usageSubType?: number;
    @JsonProperty({ type: Array, clazz: PrescriptionFormItem })
    prescriptionFormItems?: PrescriptionFormItem[];

    psychotropicNarcoticType?: PsychotropicNarcoticTypeEnum; //精麻类型
    pharmacyName?: string;
    pharmacyNo?: number;
    pharmacyType?: number;
    canAdjustment?: number; // 是否可议价
    canAdjustmentFee?: number; // 能够议价的最低金额
    get isCanAdjustment(): boolean {
        return this.canAdjustment == 1;
    }

    // 是否满足需要代办人信息的条件
    get isNeedPsychotropicNarcoticEmployee(): boolean {
        const needAgentInfo = [
            PsychotropicNarcoticTypeEnum.JING1,
            PsychotropicNarcoticTypeEnum.JING2,
            PsychotropicNarcoticTypeEnum.MAZUI,
            PsychotropicNarcoticTypeEnum.DU,
        ];
        return needAgentInfo.includes(this.psychotropicNarcoticType ?? 0);
    }

    //滚动使用
    get scrollKey(): string {
        if (!this.keyId) {
            this.keyId = UUIDGen.generate();
        }
        return this.keyId;
    }
    //编辑状态下form总价
    get localTotalPrice(): number {
        return _localTotalPrice(this.prescriptionFormItems);
    }
    //当前form总价显示
    get displayTotalPrice(): number {
        if ((this.chargeStatus ?? 0) >= ChargeFormStatus.charged && !this.isPartChargeFee) {
            return this?.receivedPrice ?? this.localTotalPrice;
        }
        return this.totalPrice ?? this.localTotalPrice;
    }
    //当前form添加箱数数量
    get localTotalCount(): number {
        return _localTotalCount(this.prescriptionFormItems);
    }
    //是否修改了总价
    get TotalPriceChanged(): boolean {
        return this.isTotalPriceChanged == 1 ?? false;
    }
    //(应收 === 实收 + 已退)就是全收了
    get isPartChargeFee(): boolean {
        return this.chargeStatus == ChargeFormStatus.charged && this.receivableFee != (this.receivedPrice ?? 0) + (this.refundedFee ?? 0);
    }
    get isAirPharmacy(): boolean {
        return this.pharmacyType == PharmacyType.air;
    }
    get isCoClinicPharmacy(): boolean {
        return this.pharmacyType == PharmacyType.coClinic;
    }
}
export class PrescriptionChineseForm extends BasePrescriptionForm {
    processBagUnitCount?: number;
    processBagUnit?: string; //加工单位"袋/格"
    contactMobile?: string;

    //空中药房相关
    vendorId?: string;
    vendorName?: string;
    processPrice?: number;
    processUsage?: string;
    ingredientPrice?: number; //辅料费
    usageScopeId?: string;
    medicineStateScopeId?: MedicineScopeId;
    vendor?: AirPharmacyVendor;
    pharmacyInfo?: PharmacyListConfig;

    processRemark?: string; //备注
    totalProcessCount?: number; //总共煎药袋数

    @JsonProperty({ type: PrescriptionFormDelivery })
    deliveryInfo?: PrescriptionFormDelivery;

    medicinePriceInfo?: {
        totalPrice: number;
        displayTotalPrice: number;
        promotionPrice: number;
        adjustmentPrice: number;
        refundTotalPrice: number;
        refundDiscountedPrice: number;
        refundedFee: number;
        unselectedFee: number;
        receivableFee: number;
        needPayFee: number;
        owedFee: number;
        receivedPrice: number;
        chargeStatus: number;
    };

    //用于空中药房供应商唯一值判断
    vendorUsageScopeId?: string;

    //(应收 === 实收 + 已退)就是全收了
    get isPartChargeFee(): boolean {
        return (
            this.chargeStatus == ChargeFormStatus.charged &&
            this.medicinePriceInfo?.receivableFee !=
                (this.medicinePriceInfo?.receivedPrice ?? 0) + (this.medicinePriceInfo?.refundedFee ?? 0)
        );
    }

    get vendorInfo(): VendorInfo {
        return JsonMapper.deserialize(VendorInfo, {
            pharmacyType: this.pharmacyType,
            pharmacyName: this.pharmacyName,
            pharmacyNo: this.pharmacyNo,
            vendorId: this.vendorId,
            vendorName: this.vendorName,
            processPrice: this.processPrice,
            processUsage: this.processUsage,
            usageScopeId: this.usageScopeId,
            medicineStateScopeId: this.medicineStateScopeId,
            vendorAvailableMedicalStates: this.vendor?.vendorAvailableMedicalStates ?? undefined,
            vendorUsageScopeId: this.vendorUsageScopeId,
            businessScopeConfig: this.vendor?.businessScopeConfig,
        });
    }

    // 本地计算总价 和后台保持一致：包含加工费、辅料费
    get localTotalPrice(): number {
        if (!!this.medicinePriceInfo && !this.prescriptionFormItems?.find((f) => f.isSelfProvided)) {
            if ((this.chargeStatus ?? 0) >= ChargeFormStatus.charged && !this.isPartChargeFee) {
                return this.medicinePriceInfo.receivedPrice;
            }
            return this.medicinePriceInfo.totalPrice;
        }
        if (!isNil(this.totalPrice)) {
            return this.totalPrice;
        } else {
            let _price = _localTotalPrice(this.prescriptionFormItems);
            if (this.prescriptionFormItems?.every((it) => isNil(it.totalPrice))) {
                _price = _price * (this.doseCount ?? 1);
            }
            return _price;
        }
    }

    processName?: string;
    //整合加工信息
    get processInfo(): ChinesePrescriptionProcessingInfoRsp {
        return JsonMapper.deserialize(ChinesePrescriptionProcessingInfoRsp, {
            isDecoction: this.isDecoction ?? !!this.usageType,
            usageType: this.usageType,
            usageSubType: this.usageSubType,
            processBagUnitCount: !this.pharmacyType ? this.processBagUnitCount : undefined,
            contactMobile: this.contactMobile,
            processName: this.processName ?? this.displayProcess,
            processRemark: this.processRemark,
            totalProcessCount: this.totalProcessCount,
        });
    }
    // 门诊UI重构
    get displayProcess(): string | undefined {
        const { ChargeAgent } = require("../../charge/data/charge-agent");
        const _processRules: ProcessRule[] = ChargeAgent.getProcessRulesSync() ?? [];
        const _displayProcess: string[] = [];

        if (this.usageType || this.usageSubType) {
            const _usageType = _processRules.find((item) => item.type == this.usageType);
            if (_usageType?.children) {
                const _usageSubType = _usageType.children.find((item) => item.subType == this.usageSubType);
                if (_usageSubType) {
                    _displayProcess.push(_usageSubType?.name ?? "");
                }
            } else {
                _displayProcess.push(_usageType?.name ?? "");
            }
        }

        //FIXME: 这里本来不应该直接判断this.usageType == 1条件-------拦截pc切换代加工信息未重置processBagUnitCount
        // if (this.usageType == 1 && this.processBagUnitCount) {
        //     _displayProcess.push(`（1剂${this.processBagUnitCount}袋）`);
        // }
        // if (this.contactMobile) {
        //     _displayProcess.push(this.contactMobile);
        // }
        if (this.usageType == 1 && this.processBagUnitCount) {
            _displayProcess.push(
                `（1剂煎${this.processBagUnitCount}袋${!!this.totalProcessCount ? "，共" + this.totalProcessCount + "袋" : ""}）`
            );
        }
        if (!!this.processRemark) {
            _displayProcess.push(!!_displayProcess.length ? "，" + this.processRemark : this.processRemark);
        }
        if (_displayProcess.length) {
            return _displayProcess.join(" ");
        }
        return undefined;
    }

    get usageInfoDisplay(): string | undefined {
        const usageSuffix = [];
        if (this.doseCount) {
            usageSuffix.push(`共${this.doseCount}剂`);
        }
        if (this.usage) {
            usageSuffix.push(`${this.usage}`);
        }
        if (this.dailyDosage) {
            usageSuffix.push(`${this.dailyDosage}`);
        }
        if (this.freq) {
            usageSuffix.push(`${this.freq}`);
        }
        if (this.usageLevel) {
            usageSuffix.push(`${this.usageLevel}`);
        }
        if (this.usageDays) {
            usageSuffix.push(`${this.usageDays}`);
        }
        if (!usageSuffix.length) return undefined;
        return usageSuffix.join("，");
    }

    get prescriptionInputUsageInfo(): PrescriptionDefaultUsage {
        return JsonMapper.deserialize(PrescriptionDefaultUsage, {
            usage: this.usage,
            dailyDosage: this.dailyDosage,
            freq: this.freq,
            usageLevel: this.usageLevel,
        });
    }

    //计算当前重量处方单
    computePrescriptionDosageWeight(): number {
        let count = 0;
        this.prescriptionFormItems?.forEach((item) => {
            if (item.unit == "g") {
                const unitCount = _.isNaN(Number(item.unitCount)) ? 0 : Number(item.unitCount);
                count += unitCount ?? 0;
            }
        });
        return count;
    }
}

function _localTotalPrice(prescriptionFormItems?: PrescriptionFormItem[]): number {
    let price = 0.0;
    if (_.isEmpty(prescriptionFormItems)) {
        return price;
    }

    prescriptionFormItems!.forEach((formItem) => {
        if (_.isArray(formItem)) {
            price += _localTotalPrice(formItem);
        } else {
            if (formItem.unitCount != null && formItem.unitPrice != null && !formItem.isSelfProvided)
                price += (function () {
                    if (!isNil(formItem.totalPrice)) {
                        return formItem.totalPrice;
                    }
                    return formItem.unitCount * formItem.unitPrice + (formItem.fractionPrice ?? 0);
                })();
        }
    });

    return price;
}

function _localTotalCount(prescriptionFormItems?: PrescriptionFormItem[]): number {
    let _count = 0;
    if (_.isEmpty(prescriptionFormItems)) {
        return _count;
    }
    prescriptionFormItems!.forEach((formItem) => {
        if (_.isArray(formItem)) {
            _count += _localTotalCount(formItem);
        } else {
            if (formItem != null) _count++;
        }
    });

    return _count;
}

export class PrescriptionWesternForm extends BasePrescriptionForm {}

export class PrescriptionInfusionForm extends BasePrescriptionForm {}

export class PrescriptionExternalForm extends PrescriptionWesternForm {
    get isTieFu(): boolean {
        return this.usageType === ExternalPRUsageTypeEnum.tieFu;
    }
    /**
     * 判断是否是现配贴
     * 外治处方-类别
     */
    get isSelfPrepared(): boolean {
        return this.usageType == ExternalPRUsageTypeEnum.tieFu && this.usageSubType == TieFuUsageSubTypeEnum.xianPeiTie;
    }
}

export class Attachment {
    id?: string;
    url?: string;
    filename?: number;
    sort?: number;
    imageWidth?: number;
    imageHeight?: number;

    display(): string | undefined {
        return this.url;
    }
}

export class ConsultationRule {
    refundTime?: number;
    serviceTime?: number;
    treatOnlineSwitch?: number;
}

export interface QuestionSheet {
    id: string;
    status: number;
}

export class HistoryConsultationSheet {
    id!: string;
    conversationId!: string;
    status!: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    startTime!: Date;
    finishTime!: Date;
}

export class OutpatientConsultationInvoiceData {
    doctorId?: string;
    doctor?: DoctorInfo;
    chiefComplaint?: string;
    fee?: number;
    id?: string;
    startTime?: string; //Date
    clinicId?: string;

    replyTime?: Date;
    chainId?: string;
    status?: number; //OutpatientConsultationStatus
    patientOrderId?: string;
    conversationId?: string;
    patientId?: string;
    patient?: Patient;

    @JsonProperty({ type: Array, clazz: Attachment })
    attachments?: Array<Attachment>;

    planVisible?: number; //医生端 治疗方案详情可见性（0: 支付后可见；1: 不支付也可见

    isChargedOnce?: number; //是否支付过
    unchargedChargeSheet?: UnchargedChargeSheet;

    @JsonProperty({ type: ConsultationRule })
    consultationRule?: ConsultationRule;
    questionSheet?: QuestionSheet;

    //历史咨询单1.6添加
    @JsonProperty({ type: Array, clazz: HistoryConsultationSheet })
    historyConsultationSheets?: HistoryConsultationSheet[];

    //服务时长,单位 小时
    serviceTime?: number;
    //赠送时间,单位分钟
    promotionServiceTime?: number;

    isFinish(): boolean {
        return this.status != undefined && this.status >= OutpatientConsultationStatus.finish;
    }
}

export interface PrescriptionAttachment {
    id?: string;
    sort?: number;
    imageWidth?: number;
    imageHeight?: number;
    clinicId?: string;
    chainId?: string;
    outpatientSheetId?: string;
    url: string;
    filename: string;
}

export interface QuestionSheetItem {
    id: string;
    name: string;
}

export class OutpatientInvoiceDetail {
    id?: string;
    patientOrderId?: string;
    clinicId?: string;
    departmentId?: string;
    departmentName?: string;
    clinicName?: string;
    doctorId?: string;
    doctorName?: string;
    doctorSignImgUrl?: string;
    patientOrderNo?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    diagnosedDate?: Date;
    orderByDate?: string;
    status?: number;
    subStatus?: OutpatientInvoiceSubStatus;
    chargeStatus?: number;
    executeStatus?: number;
    statusName?: string;
    source?: number;
    outpatientSource?: number; //OutpatientSourceType
    dataSignature?: string;
    registrationFee?: number;
    totalFee?: number;
    canAdjustment?: number; // 整单议价能够议价的标识
    canAdjustmentFee?: number; // 能够议价的最低金额

    @JsonProperty({ fromJson: fromJsonToDate })
    currentTime?: Date;

    @JsonProperty({ name: "registrationFee" })
    __registrationFee?: number; //存储初始挂号费，用于判断挂号费是否议价，修改颜色

    @JsonProperty({ fromJson: fromJsonToDate })
    reserveDate?: Date;

    revisitStatus?: RegistrationRevisitStatus;
    get getRevisitStatusName(): string {
        switch (this.revisitStatus) {
            case RegistrationRevisitStatus.again:
                return "复诊";
            case RegistrationRevisitStatus.first:
                return "初诊";
            default:
                return "";
        }
    }
    registrationCategory?: number;
    get registrationCategoryDisplay(): string {
        if (this.registrationCategory == 2) {
            return "便民";
        } else if (this.registrationCategory == 1) {
            return "专家";
        } else {
            return "普通";
        }
    }

    totalPrice?: number; //当前总价
    registrationFeeStatus?: number;
    chargeRoundingTips?: string;

    @JsonProperty({ type: Patient })
    patient?: Patient;
    @JsonProperty({ type: PsychotropicNarcoticEmployee })
    psychotropicNarcoticEmployee?: PsychotropicNarcoticEmployee; //代办人信息

    @JsonProperty({ type: PatientGuardianDetail })
    patientGuardian?: PatientGuardianDetail;

    @JsonProperty({ type: MedicalRecord })
    medicalRecord?: MedicalRecord;

    @JsonProperty({ type: Array, clazz: PrescriptionProductForm })
    productForms?: PrescriptionProductForm[];

    @JsonProperty({ type: Array, clazz: PrescriptionChineseForm })
    prescriptionChineseForms?: Array<PrescriptionChineseForm>;

    @JsonProperty({ type: Array, clazz: PrescriptionWesternForm })
    prescriptionWesternForms?: Array<PrescriptionWesternForm>;

    @JsonProperty({ type: Array, clazz: PrescriptionInfusionForm })
    prescriptionInfusionForms?: Array<PrescriptionInfusionForm>;

    //外治处方
    @JsonProperty({ type: Array, clazz: PrescriptionExternalForm })
    prescriptionExternalForms?: Array<PrescriptionExternalForm>;

    @JsonProperty({ type: Array, clazz: GoodsInfo })
    productInfos?: Array<GoodsInfo>;

    adjustmentFee?: number;
    expectedTotalPrice?: number | null;
    sourceTotalPrice?: number; //原总价
    isTotalPriceChanged?: number;

    /// v1.3添加
    isOnline?: number; //网诊

    @JsonProperty({ type: OutpatientConsultationInvoiceData })
    consultationSheet?: OutpatientConsultationInvoiceData;

    prescriptionAttachments?: PrescriptionAttachment[];

    isPhotoPrescriptionCopied?: number; //0--没有抄方,1--已抄方,不允许修改

    //住院相关接口
    hospitalPatientOrderId?: string; // 住院id
    //判断是否是住院的收费单
    get isHospitalizationSheet(): boolean {
        return !!this.hospitalPatientOrderId;
    }

    //以下值为终端使用，服务端不使用此值
    draftId?: string;

    saveASTemplate = false;
    templateName?: string; //接诊时，同时保存为模板，模板名称

    questionSheets?: QuestionSheetItem[];

    //回传
    isDraft?: number;

    type?: number; //不知道含义??

    action?: number; //预诊时提交为2

    diagnoseCount?: number; // 历史就诊次数

    //空中药房同步订单号
    airPharmacyOrderId?: string;

    get isWaitVisit(): boolean {
        return this.status == OutpatientInvoiceStatus.waitVisit || this.status == OutpatientInvoiceStatus.expired;
    }

    get isVisited(): boolean {
        return this.status == OutpatientInvoiceStatus.visited;
    }

    /**
     * 回诊单据
     */
    get isContinueDiagnose(): boolean {
        return this.isVisited && this.subStatus == OutpatientInvoiceSubStatus.continueDiagnose;
    }

    get isReceived(): boolean {
        return this.statusName == "已收";
    }

    // 判断是否是儿童
    get isChildren(): boolean {
        return (
            (!!this.patient?.age?.year && (this.patient?.age?.year ?? 0) <= 14) ||
            (!this.patient?.age?.year && (this.patient?.age?.month ?? 0) > 0) ||
            (!this.patient?.age?.year && (this.patient?.age?.day ?? 0) > 0)
        );
    }

    expired(): boolean {
        return this.status == OutpatientInvoiceStatus.expired;
    }

    get isLocalDraft(): boolean {
        return !Boolean(this.id);
    }

    fillKeyIds(): void {
        if (!_.isEmpty(this.productForms)) {
            this.productForms!.forEach((form) => {
                if (_.isEmpty(form.productFormItems)) {
                    if (!form.keyId) {
                        form.keyId = UUIDGen.generate();
                    }
                    form.productFormItems!.forEach((formItem) => {
                        if (!formItem.keyId) {
                            formItem.keyId = UUIDGen.generate();
                        }
                    });
                }
            });
        }

        if (!_.isEmpty(this.prescriptionWesternForms)) {
            this.prescriptionWesternForms!.forEach((form) => {
                if (!form.keyId) {
                    form.keyId = UUIDGen.generate();
                }

                form.prescriptionFormItems!.forEach((formItem) => {
                    if (!formItem.keyId) formItem.keyId = UUIDGen.generate();
                });
            });
        }

        if (!_.isEmpty(this.prescriptionChineseForms)) {
            this.prescriptionChineseForms!.forEach((form) => {
                if (!form.keyId) {
                    form.keyId = UUIDGen.generate();
                }

                form.prescriptionFormItems!.forEach((formItem) => {
                    if (!formItem.keyId) formItem.keyId = UUIDGen.generate();
                });
            });
        }

        if (!_.isEmpty(this.prescriptionInfusionForms)) {
            this.prescriptionInfusionForms!.forEach((form) => {
                if (!form.keyId) form.keyId = UUIDGen.generate();

                form.prescriptionFormItems!.forEach((formItem) => {
                    if (!formItem.keyId) formItem.keyId = UUIDGen.generate();
                });
            });
        }
    }

    /**
     * 获取各个处方当前选中的药房信息，如果没有，则设置为处方对应的默认药房
     * @param initGoodsTypeId ---各个类型的goodsTypeId
     * @param pharmacyNo---存在的药房号
     * @private
     */
    private initPrescriptionPharmacyInfo(params: {
        initGoodsTypeId?: number;
        pharmacyNo?: number;
        departmentId?: string;
    }): PharmacyListConfig | undefined {
        const { initGoodsTypeId, pharmacyNo, departmentId } = params;
        const pharmacyInfoConfig = userCenter.inventoryClinicConfig;
        const pharmacyInfo = pharmacyInfoConfig?.pharmacyList?.filter((pharmacy) => pharmacy.no == pharmacyNo);
        const pharmacyList = !!pharmacyInfo?.length
            ? pharmacyInfo
            : pharmacyInfoConfig?.filterPharmacyListWithGoodsType(initGoodsTypeId, undefined, departmentId)?.filter((t) => !isNil(t?.no));
        const selectPharmacyInfo = !!pharmacyList?.length
            ? pharmacyList[0]
            : pharmacyInfoConfig?.pharmacyList?.find((k) => k.no == PharmacyType.normal);
        return selectPharmacyInfo;
    }

    /**
     * 复制历史处方和使用模板，都不记录原来的处方，都从默认下达处方取
     * 重置诊疗项目、成药、中药、输注的默认药房
     * 更新库存
     */
    clearFormItemPharmacyInfo(): void {
        this.productForms?.map((form) => {
            form.productFormItems?.map((formItem) => {
                const initGoodsTypeId = GoodsType.GoodsTypeAndSubType2TypeId({
                    ...formItem?.goodsInfo,
                });
                formItem.pharmacyNo = this.initPrescriptionPharmacyInfo({ initGoodsTypeId, departmentId: this.departmentId })?.no;
                formItem.pharmacyType = this.initPrescriptionPharmacyInfo({ initGoodsTypeId, departmentId: this.departmentId })?.type;
                formItem.resetStockByPharmacyNo();
            });
        });
        this.prescriptionWesternForms?.map((form) => {
            form.pharmacyType = this.initPrescriptionPharmacyInfo({ initGoodsTypeId: GoodsTypeId.medicineWest })?.type;
            form.pharmacyNo = this.initPrescriptionPharmacyInfo({ initGoodsTypeId: GoodsTypeId.medicineWest })?.no;
            form.pharmacyName = this.initPrescriptionPharmacyInfo({ initGoodsTypeId: GoodsTypeId.medicineWest })?.name;
            form.prescriptionFormItems?.map((formItem) => {
                formItem.pharmacyNo = this.initPrescriptionPharmacyInfo({ initGoodsTypeId: GoodsTypeId.medicineWest })?.no;
                formItem.pharmacyType = this.initPrescriptionPharmacyInfo({ initGoodsTypeId: GoodsTypeId.medicineWest })?.type;
                formItem.resetStockByPharmacyNo();
            });
        });
        //多药房只针对本地药房，空中药房和代煎代配药房没有多药房
        this.prescriptionChineseForms
            ?.filter((t) => t.pharmacyType == PharmacyType.normal)
            ?.map((form) => {
                const initGoodsTypeId =
                    form?.specification == ChineseMedicineSpecType.fullNames()[1]
                        ? GoodsTypeId.medicineChineseGranule
                        : GoodsTypeId.medicineChinesePiece;
                form.pharmacyType = this.initPrescriptionPharmacyInfo({ initGoodsTypeId })?.type;
                form.pharmacyNo = this.initPrescriptionPharmacyInfo({ initGoodsTypeId })?.no;
                form.pharmacyName = this.initPrescriptionPharmacyInfo({ initGoodsTypeId })?.name;
                form.prescriptionFormItems?.map((formItem) => {
                    formItem.pharmacyNo = this.initPrescriptionPharmacyInfo({ initGoodsTypeId })?.no;
                    formItem.pharmacyType = this.initPrescriptionPharmacyInfo({ initGoodsTypeId })?.type;
                    formItem.resetStockByPharmacyNo();
                });
            });
        this.prescriptionInfusionForms?.map((form) => {
            form.pharmacyType = this.initPrescriptionPharmacyInfo({ initGoodsTypeId: GoodsTypeId.medicineWest })?.type;
            form.pharmacyNo = this.initPrescriptionPharmacyInfo({ initGoodsTypeId: GoodsTypeId.medicineWest })?.no;
            form.pharmacyName = this.initPrescriptionPharmacyInfo({ initGoodsTypeId: GoodsTypeId.medicineWest })?.name;
            form.prescriptionFormItems?.map((formItem) => {
                formItem.pharmacyNo = this.initPrescriptionPharmacyInfo({ initGoodsTypeId: GoodsTypeId.medicineWest })?.no;
                formItem.pharmacyType = this.initPrescriptionPharmacyInfo({ initGoodsTypeId: GoodsTypeId.medicineWest })?.type;
                formItem.resetStockByPharmacyNo();
            });
        });
    }

    get canCopyToothList(): ToothNos[] {
        let list: ToothNos[] = [];

        OutpatientUtils.DentistryMedicalRecordKey.forEach((key) => {
            const medicalRecord = this.medicalRecord ?? new MedicalRecord();
            if (!medicalRecord[key]) medicalRecord[key] = [];

            medicalRecord[key]?.forEach((item: DentistryMedicalRecordItem | ExtendDiagnosisInfosItem) => {
                if (!!item.toothNos?.length) {
                    list.push(item.toothNos);
                }
            });
        });

        this.productForms?.map((form) => {
            form.productFormItems?.map((formItem) => {
                if (!!formItem.toothNos?.length) {
                    list.push(formItem.toothNos);
                }
            });
        });

        //去重
        list = [...new Set(list.map((tooth) => tooth.sort().join(","))).values()].map((item) =>
            item.split(",").map((item) => Number(item))
        );

        return list;
    }
}

export class DoctorInfo {
    id?: string;
    name?: string;
    headImgUrl?: string;

    chainInfo?: ChainInfo;
}

export class PracticeInfo {
    type?: number; //05|放射技术人员
    title?: string;
}

export class ChainInfo {
    practiceImgUrl?: string; //执业照

    @JsonProperty({ type: Array, clazz: PracticeInfo })
    practiceInfo?: Array<PracticeInfo>;
    sex?: string;
}

export enum OutpatientConsultationStatus {
    waiting = 20, //待回复
    consulting = 30, //咨询中
    visited = 40, //已诊
    finish = 50, //结束
    refunded = 60, //已退费
}

export class UnchargedChargeSheet {
    id?: string;
    receivableFee?: number;
    totalFee?: number;
    sendToPatientStatus?: number;
    checkStatus?: number;
}

export class OutpatientHistory {
    patient!: Patient;
    patientOrderId!: string;
    outpatientSheetId!: string;
    diagnosis!: string;
    created!: string;
    diagnosedDate!: string;
    doctorId!: string;
    doctorName!: string;
    departmentId!: string;
    departmentName!: string;
    clinicId!: string;
    clinicName!: string;
    isCopyWrite!: number;
    source!: number;
    sourceName!: string;
    chargeStatus!: number;
    status!: number;
    statusName!: string;
    revisitStatus!: number;

    @JsonProperty({ type: Array, clazz: ExtendDiagnosisInfosItem })
    extendDiagnosisInfos?: ExtendDiagnosisInfosItem[];

    // 处置
    @JsonProperty({ type: Array, clazz: DentistryMedicalRecordItem })
    disposals?: DentistryMedicalRecordItem[];

    get showDentistryDiagnosisInfosText(): string {
        return (
            this.extendDiagnosisInfos
                ?.map((diagnosisInfoItem) => {
                    const toothNoDisplay = "";
                    // if (!!diagnosisInfoItem.toothNos?.length) {
                    //     toothNoDisplay = `${diagnosisInfoItem.toothNos.join("、")} `;
                    // }

                    return `${toothNoDisplay}${diagnosisInfoItem.value
                        ?.map((d_i) => d_i.name)
                        .filter((d_i) => !!d_i)
                        .join("、")}`;
                })
                .join(";") ?? ""
        );
    }
}

export class Registration {
    id?: string;
    patientOrderId?: string;
    registrationSheetId?: string;
    registrationFormId?: string;
    clinicId?: string;
    patientId?: string;
    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    consultantId?: string;
    consultingRoomId?: string;
    consultingRoomName?: string;
    orderNo?: number;
    orderNoStr?: string; // 后台返回完整字段 上午+2
    isAdditional?: number; // 0 1  （1表示+号）
    registrationCategory?: number;
    reserveDate?: string;
    reserveStart?: string;
    reserveEnd?: string;
    reserveShift?: number;
    isReserved?: number;
    status?: number;
    type?: number;
    payStatus?: number;
    fee?: number;
    costUnitPrice?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    signInTime?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;

    //预计就诊时间
    get __reserveTimeD(): Date | undefined {
        let __time;
        if (!!this.reserveDate && !!this.reserveStart) {
            __time = this.created ?? moment(`${this.reserveDate} ${this.reserveStart}`).toDate();
        }
        return (this.isReserved ? this.signInTime : __time) ?? __time;
    }

    // 是额外+号
    get extraNumPool(): boolean {
        return !_.isNil(this.isAdditional) && (this.isAdditional ?? 0) > 0;
    }
    // 是否便民挂号
    get isConvenient(): boolean {
        return this.registrationCategory == 2;
    }
}

export class OutpatientConsultationItem {
    patientHeadImgUrl?: string;
    status?: number; //OutpatientConsultationStatus
    created?: Date;
    startTime?: string;
    replyTime?: Date;
    id?: string;
    conversationId?: string;
    patientId?: string;
    statusName?: string;
    patientName?: string;
    unReadCount?: number;

    get isFinish(): boolean {
        return (this.status ?? 0) >= OutpatientConsultationStatus.finish;
    }

    get dataTime(): Date | undefined {
        return this.created;
    }
}

export class OutpatientOrderItem extends DataBase {
    id?: string;
    patientOrderId?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    statusLastModified?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    currentTime?: Date;
    chainId?: string;
    clinicId?: string;
    departmentId?: string;
    doctorId?: string;
    doctorName?: string;
    @JsonProperty({ type: Registration })
    registration?: Registration;
    status?: number; //OutpatientInvoiceStatus
    subStatus?: OutpatientInvoiceSubStatus;
    chargeStatus?: number;
    executeStatus?: number;
    source?: number;
    outpatientSource?: number; //OutpatientSourceType
    statusName?: string;
    @JsonProperty({ type: Patient })
    patient?: Patient;
    @JsonProperty({ type: PsychotropicNarcoticEmployee })
    psychotropicNarcoticEmployee?: PsychotropicNarcoticEmployee;

    //以下字段为终端添加，服务端不使用此值
    draftId?: string;

    isDraft?: number;
    isOnline?: number;
    @JsonProperty({ type: OutpatientConsultationItem })
    consultation?: OutpatientConsultationItem;

    //是否填写病历
    isMedicalRecordFilled?: number;
    // 是否是空中药房同步的订单
    isAirPharmacyOrder?: number;
    sourceFromOpenApi?: number; // 是否为开放平台单据

    //@override
    dataTime(): Date | undefined {
        return this.created;
    }

    referralFlag?: number;

    get isReferral(): boolean {
        return (this.referralFlag ?? 0) > 0;
    }

    //状态持续时间
    get statusDuration(): number {
        const diff =
            (this.currentTime ?? new Date()).getTime() -
            (this.statusLastModified ?? this.registration?.__reserveTimeD ?? new Date()).getTime();
        return Math.floor(diff / 1000 / 60);
    }

    /**
     * 回诊单据
     */
    get isContinueDiagnose(): boolean {
        return this.isVisited && this.subStatus == OutpatientInvoiceSubStatus.continueDiagnose;
    }

    get isVisited(): boolean {
        return this.status == OutpatientInvoiceStatus.visited;
    }
}

export class ConsultationQuestion {
    id?: string;
    status?: number;
    doctor?: DoctorInfo;
    type?: number;
    chiefComplaint?: string;
    attachments?: Attachment[];

    @JsonProperty({ type: FormDetail })
    formDetail!: FormDetail;

    @JsonProperty({ type: FormDataDetail })
    formDataDetail?: FormDataDetail;
}

export interface ConsultationFormItem {
    formId: string;
    name: string;
}

export class ChinesePrescriptionProcessingInfoRsp {
    isDecoction?: boolean;
    usageType?: number;
    usageSubType?: number;
    processBagUnitCount?: number; //中药饮片1剂袋数，可为小数
    contactMobile?: string;
    processName?: string; //自定义字段，存储代加工名字
    totalProcessCount?: number; //共煎袋数
    processRemark?: string; //备注
}
export enum DoctorOrConsultantType {
    doctor = 1,
    consultant = 2,
}

export class DoctorAndConsultantInfo {
    doctorInfo?: RegistrationDoctorScheduleStatusList;
    consultantInfo?: ConsultantList;
    pay?: {
        memberId?: string;
        fee?: number;
        receivable?: number;
        useMemberFlag?: number;
    };
    reserveTime?: {
        start?: string;
        end?: string;
    };
    revisitStatus?: number;
    reserveDate?: string;
}
export class MedicineBatchInfoList {
    displayName?: string;
    manufacturer?: string;
    unit?: string;
    count?: number;
    totalPrice?: number;
    specifications?: string;
    @JsonProperty({ type: Array, clazz: FormItemBatchInfos })
    goodsBatchInfoList?: FormItemBatchInfos[];
}
