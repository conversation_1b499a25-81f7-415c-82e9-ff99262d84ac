/**
 * 成都字节星球科技公司
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020-04-03
 *
 * @description
 * 患者数据模块请求
 */

import { Bloc, BlocEvent } from "../bloc";
import { Dimensions, Text, View } from "@hippy/react";

import React from "react";
import { LogUtils } from "../common-base-module/log";
import { Colors, Sizes, TextStyles } from "../theme";
import { BehaviorSubject, interval, of, Subject, Subscription } from "rxjs";
import { switchMap } from "rxjs/operators";
import {
    OutpatientInvoiceDetail,
    OutpatientInvoiceStatus,
    PrescriptionChineseForm,
    PrescriptionExternalForm,
    PrescriptionFormDelivery,
    PrescriptionFormItem,
    PrescriptionInfusionForm,
    PrescriptionProductForm,
    PrescriptionWesternForm,
    VerifySignaturesItem,
} from "./data/outpatient-beans";
import {
    AntibioticEnum,
    AttachmentItem,
    ChineseGoodType,
    ChineseMedicineSpecType,
    ClinicDoctorInfo,
    DentistryMedicalRecordItem,
    DepartmentTypeStatus,
    ExtendDiagnosisInfosItem,
    GoodsInfo,
    GoodsType,
    GoodsTypeId,
    MedicalRecord,
    ObstetricalHistory,
    OralExaminationItem,
    OssUpdateModules,
    Patient,
    PsychotropicNarcoticEmployee,
    PsychotropicNarcoticTypeEnum,
    ReportType,
    UsageInfo,
    WxBindStatus,
} from "../base-business/data/beans";
import { ABCNavigator, TransitionType } from "../base-ui/views/abc-navigator";
import { ABCUtils } from "../base-ui/utils/utils";
import { OutpatientPreference } from "./data/outpatient-peferences";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import _, { cloneDeep, isEmpty, isNumber } from "lodash";
import { File } from "../common-base-module/file/file";
import { userCenter } from "../user-center";
import { ClinicEmployee } from "../user-center/user-center";
import { CDSSAgent, GetAiDiagnosisRspDate, OutpatientInvoiceVerifyRsp, VerifyItemDetail } from "./data/cdss";
import { MedicineAddGroup, MedicineUsageInput } from "./medicine-add-page/medicine-add-page";
import { MedicineAddPage as NewMedicineAddPage } from "./medicine-add-page/base-medicine-add-page";
import { MedicineAddType, OutpatientContentRefKey } from "./data/outpatient-const";
import {
    ChargeConfig,
    ChargeFormItemStatus,
    ChargeFormStatus,
    ChargeInvoiceSource,
    ChargeSourceFormType,
    DispensingFormItemsSourceItemType,
} from "../charge/data/charge-beans";
import { errorSummary, errorToStr, TimeUtils, UUIDGen } from "../common-base-module/utils";
import { OutpatientUtils } from "./utils/outpatient-utils";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../base-ui/dialog/dialog-builder";
import { ChargeUtils } from "../charge/utils/charge-utils";
import { ChinesePrescriptionUsage } from "./data/medicine-add-bean";
import { createOutpatientSingleBargainDialog } from "./outpatient-single-bargain-dialog";
import { OutpatientPrescriptionTemplateSearchDialog } from "./prescription-template/outpatient-prescription-template-search-page";
import { OutpatientTemplateType, PrescriptionTemplate } from "./data/prescription-template-bean";
import { Toast } from "../base-ui/dialog/toast";
import { Pair, ValueHolder } from "../base-ui/utils/value-holder";
import { IconFontView } from "../base-ui";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import { AntimicrobialDrugConfig, ClinicEmployeesInfo, OutpatientAgent, OutpatientLimitType } from "./data/outpatient";
import { OutpatientHistoryDialogAction, OutpatientHistoryDialogResult } from "./outpatient-history-dialog";
import { OutpatientDraftManager } from "./data/outpatient-drafts";
import {
    ChainBasicOutpatient,
    GetClinicBasicSetting,
    MedicalRecordConfig,
    OnlinePropertyConfigProvider,
    OutpatientConfig,
    OutpatientDataPermissionGoodsPriceType,
} from "../data/online-property-config-provder";
import { OutpatientAiVerifyDialog } from "./outpatient-ai-verfiy-dialog";
import { ABCError } from "../common-base-module/common-error";
import { ClinicAgent, ClinicAirPharmacyConfig, ClinicVirtualPharmacyConfig, EmployeesMeConfig } from "../base-business/data/clinic-agent";
import { NewProductAddPage } from "./product-add-page/product-add-page";
import { ChargeAgent, UsageScopesItem } from "../charge/data/charge-agent";
import { ChineseMedicineConfigProvider } from "./data/chinese-medicine-config";
import FileUtils from "../common-base-module/file/file-utils";
import { AirPharmacyCalculateForm, AirPharmacyVendor, MedicineScopeId, PharmacyType } from "../charge/data/charge-bean-air-pharmacy";
import { MedicineExternalAddPage } from "./external-add-page/medicine-external-add-page";
import { clinicSharedPreferences } from "../base-business/preferences/scoped-shared-preferences";
import { ShebaoAgent } from "../base-business/mix-agent/shebao-agent";
import { ClinicShebaoConfig, DiseasesCode } from "../base-business/mix-agent/data/shebao-bean";
import { StringUtils } from "../base-ui/utils/string-utils";
import { CrmAgent } from "../patients/data/crm-agent";
import { GoodsUtils } from "../base-business/utils/utils";
import { QuestionSheetAgent } from "./question-sheet/question-sheet-data/consultation-sheet-agent";
import { SelectConsultationSheetList, SelectQuestionType } from "./question-sheet/question-sheet-data/consultation-sheet-bean";
import { OutpatientQuestionSheetDetailPage } from "./question-sheet/outpatient-question-sheet-detail-page";
import { FeatureAuthority } from "../user-center/data/constants";
import UiUtils, { pxToDp } from "../base-ui/utils/ui-utils";
import {
    DEFAULT_DOCTOR_ID,
    RegistrationDetail,
    RegistrationDiffForRevisitedType,
    RegistrationDoctorEnableCategories,
    RegistrationFee,
    RegistrationPageSourceType,
    RegistrationRevisitStatus,
    RegistrationsDoctorFeeItemsRsp,
} from "../registration/data/bean";
import { GetOutpatientRevisitStatusRsp, RegistrationAgent } from "../registration/data/registration-agent";
import { AbcDialog } from "../base-ui/abc-app-library";
import { OutpatientDiagnosisInputPage } from "./medical-record-page/outpatient-diagnosis-input-page";
import OutpatientAuxiliaryExaminationInputPage from "./medical-record-page/outpatient-auxiliary-examination-input-page";
import { SyndromeInputPage } from "./medical-record-page/syndrome-input-page";
import { TherapyInputPage } from "./medical-record-page/therapy-input-page";
import { OutpatientDoctorAdviseInputPage } from "./medical-record-page/outpatient-doctor-advise-input-page";
import ChiefComplaintInputPage from "./medical-record-page/chief-complaint-input-page";
import OutpatientPresentHistoryInputPage from "./medical-record-page/outpatient-present-history-input-page";
import OutpatientPastHistoryInputPage from "./medical-record-page/outpatient-past-history-input-page";
import { FamilyHistoryInputPage } from "./medical-record-page/family-history-input-page";
import { EpidemiologicalHistoryInputPage } from "./medical-record-page/epidemiological-history-input-page";
import OutpatientPhysicalExaminationInputPage from "./medical-record-page/outpatient-physical-examination-input-page";
import { AbcTextInput } from "../base-ui/views/abc-text-input";
import { AbcImagePicker } from "../base-business/image-picker/abc-image-picker";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { SearchDoctorDepartmentSelectPage } from "../base-ui/doctor-department-select-view";
import OutpatientObstetricalHistoryInputPage from "./medical-record-page/outpatient-obstetrical-history-input-page";
import { actionEvent } from "../bloc/bloc";
import { DentistryDiagnosisInputPage } from "./medical-record-page/dentistry-diagnosis-input-page";
import { HospitalRegisterDetail } from "../base-business/data/hospital-register/bean";
import { HospitalAgent } from "../views/patient-hospital-info/hospital-agent";
import { PersonalHistoryInputPage } from "./medical-record-page/personal-history-input-page";
import { GoodsAgent, PharmacyListItem } from "../data/goods/goods-agent";
import { showOptionsBottomSheet } from "../base-ui/dialog/bottom_sheet";
import { OphthalmicReport } from "./views/ophthalmic-report";
import { OutpatientTreatmentPlansInputPage } from "./medical-record-page/dentistry-treatment-plans-input-page";
import { PatientOrderAgent } from "../base-business/data/patient-order/patient-order-agent";
import { PatientOrderLockDetail, PatientOrderLockType } from "../base-business/data/patient-order/patient-order-bean";
import { onlineMessageManager } from "../base-business/msg/online-message-manager";
import { DentistryInvoicePage } from "../registration/dentistry/dentistry-invoice-page";
import { MedicalRecordCheckDialog } from "./medical-record-page/views/medical-record-check-dialog";
import { MedicalRecordUtils } from "./medical-record-page/utils/medical-record-utils";
import { ProductMedicineUsageParams } from "./product-add-page/product-add-page-bean";
import { InventoryClinicConfig, PharmacyListConfig } from "../inventory/data/inventory-bean";
import { OutpatientDisposalInputPage } from "./medical-record-page/outpatient-disposal-input-page";
import { OralExaminationInputPage } from "./medical-record-page/dentistry/oral-examination-input-page";
import { ToothNos, ToothNosDetailInfo } from "../data/tooth-bean";
import { PrescriptionInputPage } from "./medical-record-page/prescription-input-page";
import { DentistryAgent } from "../registration/dentistry/data/dentistry-agent";
import { DentistryConfig, RegistrationType } from "../registration/dentistry/data/bean";
import { AirPharmacyStepsView } from "../charge/view/air-pharmacy-steps-view";
import { StepStatusListItem } from "../base-ui/abc-steps/abc-steps";
import { InfectiousDiseasesDetailDialog } from "./medical-record-page/views/infectious-diseases-detail-dialog";
import { OutpatientConfirmModifyDialog } from "./views/outpatient-confirm-modify-dialog";
import { AllergicHistoryInputPage } from "./medical-record-page/allergic-history-input-page";
import { DefaultOralExaminationInputPage } from "./medical-record-page/dentistry/default-oral-examination-input-page";
import { TimePicker } from "../base-ui/picker/time-picker";
import { PatientGuardianDetail } from "../data/patient-bean";
import { GuardianEditDialog } from "../views/guardian-edit-dialog/guardian-edit-page";
import { RestrictedDrugView } from "./views/RestrictedDrugView";
import { MedicineTemplateAddType, MedicineTemplateAddTypeDialog } from "./medicine-add-page/views/medicine-template-add-type-dialog";
import { PatientAgent } from "../base-business/data/patient-agent";
import { MedicineAddPageUtils } from "./medicine-add-page/medicine-add-page-utils";
import { SyndromeTreatmentInputInputPage } from "./medical-record-page/dialectical-treatment-input-page";
import { showStateLogoQueryDialog } from "../charge/view/state-logo-dialog-builder";
import { AbcWithStateLogTitle } from "../base-ui/abc-with-state-log-title/abc-with-state-log-title";
import { JingMaPrescriptionRequiredPage } from "./views/jing-ma-prescription-required-page/jing-ma-prescription-required-page";
import OutpatientPulseInputPage from "./medical-record-page/outpatient-pulse-input-page";
import { AIMedicalRecordsManager } from "../AI";
import { Asr, AsrResult } from "../AI/asr";

export const CHINESE_PRESCRIPTION_USAGE_DEFAULT = "chinesePrescriptionUsageDefault";

class State {
    loading = true;
    loadError: any;

    aiDiagnosis: GetAiDiagnosisRspDate = new GetAiDiagnosisRspDate(); //ai智能诊断推荐

    get showErrorHint(): boolean {
        return this._showErrorHint;
    }

    set showErrorHint(status: boolean) {
        if (!!userCenter.clinic?.isDentistryClinic) {
            this._showMedicalRecordErrorHint = false;
        }
        this._showErrorHint = status;
    }

    private _showErrorHint = false;

    get showMedicalRecordErrorHint(): boolean {
        return this.showErrorHint && this._showMedicalRecordErrorHint;
    }

    get isDentistryClinic(): boolean {
        return !!userCenter.clinic?.isDentistryClinic;
    }

    detailData?: OutpatientInvoiceDetail;
    registrationDetail?: RegistrationDetail;
    registrationConfig?: DentistryConfig;
    clinicShebaoConfig?: ClinicShebaoConfig;
    clinicRegistrationCategories?: RegistrationDoctorEnableCategories;
    doctorRegistrationCategories?: RegistrationDoctorEnableCategories;

    lockDetails: Map<PatientOrderLockType, PatientOrderLockDetail> = new Map<PatientOrderLockType, PatientOrderLockDetail>();
    private _showMedicalRecordErrorHint = true;

    get isEditing(): boolean {
        return !this.orderIsLocking && this._isEditing;
    }

    set isEditing(status: boolean) {
        this._isEditing = status;
    }

    // 门诊锁单
    get outpatientLocking(): boolean {
        return !!this.lockDetail?.status;
    }

    // 不包含锁单情况下的是否可编辑
    get notLockingIsEditing(): boolean {
        return this._isEditing;
    }

    // 如果是收费锁，病历可以继续编辑
    get canEditMedical(): boolean {
        return !!this.chargeLockDetail?.status && !!this.chargeLockDetail?.value?.chargeInProgress;
    }

    // 病历是否更改
    isMedicalChanged?: boolean;
    // 病历修改后，需要回到编辑前状态
    needBackToEdit?: boolean;

    doctorsList?: Array<ClinicDoctorInfo>;

    canUseDoctorList?: ClinicDoctorInfo[]; // 仅处方签名处使用

    chargeConfig?: ChargeConfig;

    /**
     * 是否允许挂号费调整
     */
    get shouldRegisteredBargainSwitch(): boolean {
        return !!this.chargeConfig?.doctorRegisteredBargainSwitch || !!userCenter.clinic?.isManager;
    }

    airPharmacyConfig?: ClinicAirPharmacyConfig;
    virtualPharmacyConfig?: ClinicVirtualPharmacyConfig;
    private _isEditing = false;

    aiVerify?: OutpatientInvoiceVerifyRsp; //智能审方

    outpatientConfig?: OutpatientConfig;
    outpatientRequiredConfig?: ChainBasicOutpatient;
    antimicrobialDrugConfig?: AntimicrobialDrugConfig; // 抗菌配置
    //当前处方医生的基础信息
    currentDoctorDetailInfo?: ClinicEmployeesInfo;

    /**
     * 是否开启医生职称限制
     */
    get enableDoctorPractice(): boolean {
        return !!this.antimicrobialDrugConfig?.isOpenAntimicrobialDrug;
    }

    usageList: UsageScopesItem[] = [];

    focusItemKey?: string;

    employeesMeConfig?: EmployeesMeConfig;

    hospitalDetail?: HospitalRegisterDetail; //长护详情
    canSendToPatient?: boolean; //能否推送给患者
    isMeetpushPayment?: boolean; //自助支付推送的前提条件（必须开通微诊所和ABC支付）

    //获取药房列表
    pharmacyList?: PharmacyListItem[];

    // 语音记录列表
    voiceRecordResult?: AsrResult[];

    //是否开通虚拟药房（代煎代配）
    get isOpenVirtualPharmacy(): boolean {
        return !!this.pharmacyList?.find((t) => t.type == PharmacyType.virtual) ?? false;
    }

    /**
     * 开通药诊互通功能
     */
    get isOpenCoClinic(): boolean {
        return !!this.pharmacyInfoConfig?.isOpenCoClinicPharmacy && this.detailData?.isOnline != 1;
    }

    /**
     * 是否可用问诊单
     * 通过 edition 判断
     */
    canAddQuestionForm?: boolean;

    /**
     * 显示总价
     */
    get showTotalPrice(): boolean {
        return this.employeesMeConfig?.employeeDataPermission?.outpatient?.goodsPrice != OutpatientDataPermissionGoodsPriceType.notAllowed;
    }

    /**
     * 显示单项价格
     */
    get showItemPrice(): boolean {
        return this.employeesMeConfig?.employeeDataPermission?.outpatient?.goodsPrice == OutpatientDataPermissionGoodsPriceType.allowedAll;
    }

    /**
     *  能够查看患者手机号
     */
    get canSeePatientPhone(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.outpatient?.isCanSeePatientMobile;
    }

    isItemCanEdit(chargeStatus?: number): boolean {
        return OutpatientUtils.canEditWithChargeStatus(chargeStatus);
    }

    get ableUseAirPharmacy(): boolean {
        return (
            (!!this.virtualPharmacyConfig?.isVirtualPharmacyOpen ||
                (this.airPharmacyConfig?.isSupportAirPharmacy && this.airPharmacyConfig.openSwitch == 1)) ??
            true
        );
    }

    doctorFee?: RegistrationsDoctorFeeItemsRsp; // 医生诊费

    //门诊单锁单情况
    get lockDetail(): PatientOrderLockDetail | undefined {
        return this.lockDetails.get(PatientOrderLockType.outpatientSheet);
    }

    // 收费中锁单
    get chargeLockDetail(): PatientOrderLockDetail | undefined {
        return this.lockDetails.get(PatientOrderLockType.chargeSheet);
    }

    // 收费员锁单
    get tollCollectorCharges(): boolean {
        return ((this.chargeLockDetail?.value?.businessDetail?.addedLockStatus ?? 0) & 0x0008) > 0;
    }

    // 微诊所患者支付锁单(0x0002)或者自助服务机支付锁单(0x0004)
    get microclinicsOrSelfServiceMachines(): boolean {
        return (
            ((this.chargeLockDetail?.value?.businessDetail?.addedLockStatus ?? 0) & 0x0002) > 0 ||
            ((this.chargeLockDetail?.value?.businessDetail?.addedLockStatus ?? 0) & 0x0004) > 0
        );
    }

    get orderIsLocking(): boolean {
        /**
         * 医生编辑中、收费中两者满足其一都是锁单(收费只锁收费单和门诊)
         * @param lockType ---- 锁单类型
         * @param isNeedCompareOperate -- 是否需要比较锁单人（门诊需要，收费不需要----门诊如果是同一个账号不锁，收费不区分是否是同一个账号都要锁）
         */
        const checkLock = (lockType: PatientOrderLockType, isNeedCompareOperate = true): boolean => {
            const lockDetail = this.lockDetails.get(lockType);
            const hasIdentity = !!lockDetail?.identity;
            const sameEmployee = isNeedCompareOperate ? userCenter.employee?.id === lockDetail?.employeeId : false;
            const isChargeInProgress = lockType == PatientOrderLockType.chargeSheet ? !!lockDetail?.value?.chargeInProgress : true;
            return hasIdentity && !sameEmployee && !!lockDetail?.status && !!isChargeInProgress;
        };
        const isDoctorEditing = checkLock(PatientOrderLockType.outpatientSheet);
        const chargeInProgress = checkLock(PatientOrderLockType.chargeSheet, false);

        return isDoctorEditing || chargeInProgress;
    }

    clinicBasicSetting?: GetClinicBasicSetting;

    /**
     * 新冠症状提示开关
     */
    covid19DetectionStatus = false;

    /**
     * 传染病症状提示开关
     */
    infectiousDiseasesStatus = false;

    /**
     * 修改初复诊状态开关
     */
    canChangeRevisitedStatus = true;

    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;

    //能否开出库存为0的药配置
    get canCheckWithoutStock(): boolean {
        //disableNoStockGoods只有在为1的时候，才不允许开出
        return this.pharmacyInfoConfig?.stockGoodsConfig?.disableNoStockGoods != 1;
    }

    /**
     * 保存同步牙位数据
     */
    asyncToothNosMap: Map<number, ToothNos> = new Map<number, ToothNos>();

    /**
     * 最长牙位显示长度
     */
    get toothNosMaxWidth(): number {
        let _maxWidth = 0;
        const _toothNosDetailInfo = new ToothNosDetailInfo();
        OutpatientUtils.DentistryMedicalRecordKey.forEach((key) => {
            this.detailData?.medicalRecord?.[key]?.forEach((item: DentistryMedicalRecordItem | ExtendDiagnosisInfosItem) => {
                _toothNosDetailInfo.originArr = item?.toothNos ?? [];
                _maxWidth = Math.max(_toothNosDetailInfo.maxQuadrantWidth, _maxWidth);
            });
        });
        return _maxWidth;
    }

    /**
     * 诊疗项目最长牙位显示长度
     */
    get toothNosMaxWidthWithProduct(): number {
        let _maxWidth = 0;
        const _toothNosDetailInfo = new ToothNosDetailInfo();
        this.detailData?.productForms?.forEach((form) => {
            form.productFormItems?.forEach((item) => {
                _toothNosDetailInfo.originArr = item?.toothNos ?? [];
                _maxWidth = Math.max(_toothNosDetailInfo.maxQuadrantWidth, _maxWidth);
            });
        });
        return _maxWidth;
    }

    // 有未签名药品
    needDoubleSign?: boolean; // 处方中存在"风险"或"提醒"需展示确认签字按钮

    airPharmacyRisk?: boolean; // 空中药房中存在"风险"或"提醒"

    pediatricsDoctorsList?: ClinicDoctorInfo[]; // 儿科医生列表

    /**
     * 当前患者是儿童且医生为儿科医生 开具处方时默认显示儿科标记
     */
    get showDefaultPediatricTag(): boolean {
        return !!(this.isPediatricDoctor() && this.detailData?.isChildren);
    }

    /**
     * 是儿科医生
     */
    isPediatricDoctor(): boolean {
        return !!this.pediatricsDoctorsList?.find(
            (f) => f.doctorId == this.detailData?.doctorId && f.departmentId == this.detailData?.departmentId
        );
    }

    prescriptionUseCa?: boolean; //电子签名状态（本地使用）  1 启用 0 关闭
    isEnableCA?: boolean; // 是否启用CA签名
    doctorSignatureStatus?: number; // 电子签名状态 0 已绑定 1 已失效 undefined 未绑定
    isDoctorSignatureExpired?: boolean; // 医生签名已过期
    needShowAgreementPage?: boolean; // 是否需要在勾选时弹出《数字证书服务协议》页面

    isDangerPrescriptionMustSign?: boolean; // 风险处方强制签名  0 不打开 1 打开

    /**
     * 是否允许编辑病历
     */
    get allowEditMedicalRecord(): boolean {
        if (!this.detailData?.isVisited) return true;
        const medicalRecordUpdateLimitTime = this.clinicBasicSetting?.outpatient?.settings?.medicalRecordUpdateLimitTime;
        if (!medicalRecordUpdateLimitTime) return true;
        return this.checkOutpatientCreateDate(medicalRecordUpdateLimitTime);
    }

    /**
     * 判断是否是当天处方
     */
    checkOutpatientCreateDate(days: number): boolean {
        const created = this.detailData?.created;
        const differenceTime = TimeUtils.difference(created, new Date());
        return differenceTime.inDays < days;
    }

    /**
     * 判断是否是当天处方
     */
    checkOutpatientCreateDateIsToday(): boolean {
        const created = this.detailData?.created;
        return TimeUtils.isToday(created ?? new Date());
    }

    /**
     * 是否允许新增处方
     */
    get allowAddPrescription(): boolean {
        if (!this.detailData?.isVisited) return true;
        const updateLimitTime = this.clinicBasicSetting?.outpatient?.settings?.prescriptionUpdateLimitTime;
        if (!updateLimitTime) return true;
        const created = this.detailData?.created;
        const differenceTime = TimeUtils.difference(created, new Date());
        return differenceTime.inDays < updateLimitTime;
    }

    get isBeijingSupervise(): boolean {
        return !!this.clinicShebaoConfig?.isBeijing && !!this.detailData?.isOnline;
    }

    copyTo(state: State): void {
        Object.assign(state, this);
    }

    checkGoodsIsControlledSubstances(goodsId?: string): boolean {
        const list = this.aiVerify?.hasControlledSubstancesRule;
        return !!list?.find((item) => item.detail?.goodsId == goodsId);
    }

    /**
     * 是否开启多号种
     */
    get enableRegistrationCategories(): boolean {
        return !!this.clinicRegistrationCategories?.enableRegistrationCategories;
    }

    /**
     * 可使用号种列表
     */
    get doctorRegistrationCategoriesList(): RegistrationFee[] {
        return (this.doctorRegistrationCategories ?? this.clinicRegistrationCategories)?.registrationFees ?? [];
    }

    outpatientRevisitStatusRsp?: GetOutpatientRevisitStatusRsp;

    /**
     * 是短期内再次就诊挂号费不同模式
     */
    get isShortRevisitsDifferent(): boolean {
        return !!this.doctorRegistrationCategories?.registrationFees?.some(
            (fee) => fee?.isDiffForRevisited == RegistrationDiffForRevisitedType.shortRevisitsDifferent
        );
    }

    /**
     * 是否X天内首诊(反之再诊)
     */
    isFirstRevisitWithinDays(options: { departmentId: string; doctorId: string }): boolean {
        const { departmentId, doctorId } = options;
        let effectiveDays = 0; // X天内在同一医生首次就诊/再次就诊的患者天数
        this.doctorRegistrationCategories?.registrationFees?.find((fee) => {
            if (fee.departmentId == departmentId && fee.doctorId == doctorId) {
                effectiveDays = fee.revisitedFeeCustomUseRule?.effectiveDays ?? 0;
            }
        });
        return !!this.outpatientRevisitStatusRsp?.isFirstRevisitWithinDays({
            reserveDate: this.detailData?.created,
            effectiveDays: effectiveDays,
        });
    }

    /**
     * 效验是否收缩压低于舒张压
     */
    isSystolicLowerThanDiastolic(): boolean {
        const regex = /血压(\d+)\/(\d+)mmHg/g;
        const physicalExamination = this.detailData?.medicalRecord?.physicalExamination;

        if (!!physicalExamination) {
            let match;
            while ((match = regex.exec(physicalExamination)) !== null) {
                // match[0]：完整匹配的血压值，例如 "血压80/120mmHg"；match[1]：捕获收缩压的值，例如 80；match[2]：捕获舒张压的值，例如 120
                const systolic = Number(match[1]);
                const diastolic = Number(match[2]);
                if (systolic < diastolic) {
                    return true;
                }
            }
        }
        return false;
    }

    medicalRecordUpdateLimit?: number; // 医生修改病历允许的时间
    prescriptionUpdateLimit?: number; // 医生继续新增处方允许的时间
    isOpenEditMedicalRecordDiagnosed?: number; // 完诊后修改病历
    isOpenEditPrescriptionDiagnosed?: number; // 完诊后新增医嘱
    // 是否开启完诊后修改病历开关
    get isOpenEditMedicalRecord(): boolean {
        return this.isOpenEditMedicalRecordDiagnosed == 1;
    }

    // 是否开启完诊后新增医嘱开关
    get isOpenEditPrescription(): boolean {
        return this.isOpenEditPrescriptionDiagnosed == 1;
    }

    // 病历修改时间限制
    get disableUpdateMedicalRecord(): boolean {
        const { diagnosedDate, currentTime } = this.detailData || {};
        if (!this.isOpenEditMedicalRecord && !(!diagnosedDate || !currentTime)) return true;
        return this.disableUpdateMedicalRecordOrPrescription(this.medicalRecordUpdateLimit ?? 0);
    }

    // 处方、诊疗项目新增时间限制
    get disableAddPrescription(): boolean {
        const { diagnosedDate, currentTime } = this.detailData || {};
        if (!this.isOpenEditPrescription && !(!diagnosedDate || !currentTime)) return true;
        return this.disableUpdateMedicalRecordOrPrescription(this.prescriptionUpdateLimit ?? 0);
    }

    disableUpdateMedicalRecordOrPrescription(limitType: number): boolean {
        // 待诊的不限制
        const { diagnosedDate, currentTime } = this.detailData || {};
        if (!diagnosedDate || !currentTime) return false;
        switch (limitType) {
            case OutpatientLimitType.withTheDay:
                return TimeUtils.currentDateOutLimit(diagnosedDate, currentTime, 1);
            case OutpatientLimitType.withTheLastThreeDays:
                return TimeUtils.currentDateOutLimit(diagnosedDate, currentTime, 3);
            case OutpatientLimitType.withTheLastSevenDays:
                return TimeUtils.currentDateOutLimit(diagnosedDate, currentTime, 7);
            case OutpatientLimitType.withTheLastFourteenDays:
                return TimeUtils.currentDateOutLimit(diagnosedDate, currentTime, 14);
            case OutpatientLimitType.withTheLastOneMonth:
                return TimeUtils.currentDateOutLimit(diagnosedDate, currentTime, 30);
            case OutpatientLimitType.withTheLastTwoDays:
                return TimeUtils.currentDateOutLimit(diagnosedDate, currentTime, 2);
            case OutpatientLimitType.withTheLastThreeWeeks:
                return TimeUtils.currentDateOutLimit(diagnosedDate, currentTime, 21);
            case OutpatientLimitType.withTheLastTwoMonths:
                return TimeUtils.currentDateOutLimit(diagnosedDate, currentTime, 60);
            case OutpatientLimitType.withTheLastThreeMonths:
                return TimeUtils.currentDateOutLimit(diagnosedDate, currentTime, 90);
            default:
                return false;
        }
    }
}

class ScrollToFocusItemState extends State {
    static fromState(state: State): ScrollToFocusItemState {
        const newState = new ScrollToFocusItemState();
        state.copyTo(newState);
        return newState;
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {
    draftId?: string;
    createNew?: boolean;
    outpatientId?: string;
    registrationSheetId?: string;

    constructor(options: { draftId?: string; outpatientId?: string; createNew?: boolean; registrationSheetId?: string }) {
        super();
        this.draftId = options.draftId;
        this.outpatientId = options.outpatientId;
        this.createNew = options.createNew;
        this.registrationSheetId = options.registrationSheetId;
    }
}

class _EventUpdate extends _Event {}

class _EventModifyTap extends _Event {}

class _EventNavToHistoryDialog extends _Event {
    result: OutpatientHistoryDialogResult;

    constructor(result: OutpatientHistoryDialogResult) {
        super();
        this.result = result;
    }
}

class _EventAddImage extends _Event {}

class _EventRemoveImage extends _Event {
    index: number;

    constructor(index: number) {
        super();
        this.index = index;
    }
}

class _EventDeleteOutpatient extends _Event {}

class _EventClearPatient extends _Event {}

class _EventSelectDoctor extends _Event {}

class _EventUpdateRegistrationFee extends _Event {
    fee: number;

    constructor(fee: number) {
        super();
        this.fee = fee;
    }
}

class _EventUpdateDoctorAdvices extends _Event {}

class _EventAddPrescriptionWestern extends _Event {}

class _EventChargePreview extends _Event {}

class _EventViewAiVerifyResult extends _Event {}

class _EventViewVerifyDoubleSignResult extends _Event {
    confirmSign?: boolean;

    constructor(confirmSign?: boolean) {
        super();
        this.confirmSign = confirmSign;
    }
}

class _EventChangePrescriptionType extends _Event {
    detail: VerifyItemDetail;

    constructor(detail: VerifyItemDetail) {
        super();
        this.detail = detail;
    }
}

class _EventFinishTap extends _Event {}

class _EventSaveMedical extends _Event {}

class _EventModifyRevisitStatus extends _Event {}

class _EventModifyRevisitStatusAndCategories extends _Event {}

// 西药，点击进行用法编辑
class _EventModifyWesternMedicine extends _Event {
    prescriptionWesternForm: PrescriptionWesternForm;
    formItem?: PrescriptionFormItem;
    showAstCheck?: boolean;

    constructor(prescriptionWesternForm: PrescriptionWesternForm, formItem?: PrescriptionFormItem, showAstCheck?: boolean) {
        super();
        this.prescriptionWesternForm = prescriptionWesternForm;
        this.formItem = formItem;
        this.showAstCheck = showAstCheck;
    }
}

class _EventModifyChineseMedicine extends _Event {
    prescriptionChineseForm: PrescriptionChineseForm;
    formItem?: PrescriptionFormItem;

    constructor(prescriptionChineseForm: PrescriptionChineseForm, formItem?: PrescriptionFormItem) {
        super();
        this.prescriptionChineseForm = prescriptionChineseForm;
        this.formItem = formItem;
    }
}

//点击输液处方，进行用法修改
class _EventModifyInfusionMedicine extends _Event {
    prescriptionInfusionForm: PrescriptionInfusionForm;
    formItem?: PrescriptionFormItem;

    constructor(prescriptionInfusionForm: PrescriptionInfusionForm, formItem?: PrescriptionFormItem) {
        super();
        this.prescriptionInfusionForm = prescriptionInfusionForm;
        this.formItem = formItem;
    }
}

//点击帖敷处方，进行用法修改
class _EventModifyExternalMedicine extends _Event {
    prescriptionExternalForm: PrescriptionExternalForm;
    formItem?: PrescriptionFormItem;

    constructor(prescriptionExternalForm: PrescriptionExternalForm, formItem?: PrescriptionFormItem) {
        super();
        this.prescriptionExternalForm = prescriptionExternalForm;
        this.formItem = formItem;
    }
}

class _EventAddPrescriptionChinese extends _Event {}

class _EventAddPrescriptionInfusion extends _Event {}

class _EventAddPrescriptionExternal extends _Event {}

class _EventInvoiceTemplate extends _Event {
    prescriptionTemplate?: PrescriptionTemplate;

    constructor(prescriptionTemplate?: PrescriptionTemplate) {
        super();
        this.prescriptionTemplate = prescriptionTemplate;
    }
}

class _EventDeletePrescription extends _Event {
    prescriptionForm: PrescriptionChineseForm | PrescriptionInfusionForm | PrescriptionWesternForm | PrescriptionExternalForm;

    constructor(prescriptionForm: PrescriptionChineseForm | PrescriptionInfusionForm | PrescriptionWesternForm | PrescriptionExternalForm) {
        super();
        this.prescriptionForm = prescriptionForm;
    }
}

class _EventEditPatientInfo extends _Event {
    patient: Patient;
    oldPatient?: Patient;

    constructor(patient: Patient, oldPatient?: Patient) {
        super();
        this.patient = patient;
        this.oldPatient = oldPatient;
    }
}

class _EventSaveChiefComplaintInfo extends _Event {}

class _EventChangeOnsetTimeInfo extends _Event {}

class _EventSavePresentHistoryInfo extends _Event {}

class _EventSavePastHistoryInfo extends _Event {}

class _EventSaveAllergicHistoryInfo extends _Event {}

class _EventPersonalHistoryInfo extends _Event {}

class _EventSaveDiagnosisInfo extends _Event {}

class _EventSaveDentistryDiagnosisInfo extends _Event {
    item?: ExtendDiagnosisInfosItem;
    index?: number;

    constructor(item?: ExtendDiagnosisInfosItem, index?: number) {
        super();
        this.item = item;
        this.index = index;
    }
}

class _EventSaveObstetricalHistoryInfo extends _Event {}

class _EventSaveSyndromeInfo extends _Event {}

class _EventSaveTherapyInfo extends _Event {}

class _EventSaveChinesePrescriptionInfo extends _Event {}

class _EventSavePhysicalExaminationInfo extends _Event {}

class _EventSaveChineseExaminationInfo extends _Event {}

class _EventSaveTongueInfo extends _Event {}

class _EventSavePulseInfo extends _Event {}

class _EventSaveDefaultOralExaminationInfo extends _Event {}

class _EventSaveSyndromeTreatmentInfo extends _Event {}

class _EventSaveAuxiliaryExaminationInfo extends _Event {
    item?: DentistryMedicalRecordItem;
    index?: number;

    constructor(item?: DentistryMedicalRecordItem, index?: number) {
        super();
        this.item = item;
        this.index = index;
    }
}

class _EventSaveFamilyHistoryInfo extends _Event {}

class _EventSaveEpidemiologicalHistoryInfo extends _Event {}

class _EventSaveTreatmentPlansInfo extends _Event {
    item?: DentistryMedicalRecordItem;
    index?: number;

    constructor(item?: DentistryMedicalRecordItem, index?: number) {
        super();
        this.item = item;
        this.index = index;
    }
}

class _EventSaveDisposalsInfo extends _Event {
    item?: DentistryMedicalRecordItem;
    index?: number;

    constructor(item?: DentistryMedicalRecordItem, index?: number) {
        super();
        this.item = item;
        this.index = index;
    }
}

class _EventSaveOralExaminationInfo extends _Event {
    item?: DentistryMedicalRecordItem;
    index?: number;

    constructor(item?: DentistryMedicalRecordItem, index?: number) {
        super();
        this.item = item;
        this.index = index;
    }
}

class _EventAddProductInfo extends _Event {
    formItem?: PrescriptionFormItem;

    constructor(formItem?: PrescriptionFormItem) {
        super();
        this.formItem = formItem;
    }
}

class _EventBack extends _Event {}

class _EventChangeSetting extends _Event {
    config: OutpatientConfig;

    constructor(config: OutpatientConfig) {
        super();
        this.config = config;
    }
}

class _EventAddQuestionSheet extends _Event {}

class _EventAddAttachment extends _Event {}

class _EventAddVoiceRecord extends _Event {}

class _EventAddMedicalRecordTemplate extends _Event {}

class _EventModifyQuestionSheet extends _Event {
    id: string;

    constructor(id: string) {
        super();
        this.id = id;
    }
}

class _EventUpdateClinicType extends _Event {
    info?: HospitalRegisterDetail;

    constructor(info?: HospitalRegisterDetail) {
        super();
        this.info = info;
    }
}

class _EventModifyPatientGuardianDetail extends _Event {
    value?: PatientGuardianDetail;

    constructor(info?: PatientGuardianDetail) {
        super();
        this.value = info;
    }
}

class _EventPushOrderToPatient extends _Event {
    chargeSheetId: string;

    constructor(chargeSheetId: string) {
        super();
        this.chargeSheetId = chargeSheetId;
    }
}

class _EventPreOutpatient extends _Event {}

class _EventFreshInit extends _Event {
    needConform: boolean;

    constructor(needConform = false) {
        super();
        this.needConform = needConform;
    }
}

class _EventViewReportDetail extends _Event {
    examinationSheetId?: string;
    type?: ReportType;

    constructor(examinationSheetId?: string, type?: ReportType) {
        super();
        this.examinationSheetId = examinationSheetId;
        this.type = type;
    }
}

class _EventRepeatAppointment extends _Event {
    patient?: Patient;
    doctorId?: string;
    departmentId?: string;
    revisitStatus?: RegistrationRevisitStatus; // 初复诊状态

    constructor(options: { patient?: Patient; doctorId?: string; departmentId?: string; revisitStatus?: RegistrationRevisitStatus }) {
        super();
        this.patient = options.patient;
        this.doctorId = options.doctorId;
        this.departmentId = options.departmentId;
        this.revisitStatus = options.revisitStatus;
    }
}

class _EventCheckExpressInfo extends _Event {
    formId: string;
    companyInfo?: string;

    constructor(formId: string, companyInfo?: string) {
        super();
        this.formId = formId;
        this.companyInfo = companyInfo;
    }
}

class _EventShowInfectiousDiseasesDetail extends _Event {}

class _EventDeleteDraft extends _Event {}

class OutPatientInvoicePageBloc extends Bloc<_Event, State> {
    private _hasChanged = false;
    private allowDraft = true;
    outpatientId?: string;
    draftId?: string;
    draftClinicScope?: boolean;
    detailData?: OutpatientInvoiceDetail;
    registrationSheetId?: string;
    doctorName?: string;
    doctorId?: string;

    _loadVendorTrigger = new Subject<PrescriptionChineseForm>();
    _aiVerify = new Subject();
    _getAIDiagnosisTrigger = new Subject<number>();
    _updateOutpatientConfig = new Subject();
    treatOnlineClinicId: string | undefined;
    _getUnfinishOutpatientTrigger = new Subject<string>();

    private _lockTimeIntervals: Map<PatientOrderLockType, Subscription | undefined> = new Map<PatientOrderLockType, Subscription>();

    private _patientRevisitStatusTrigger: Subject<string> = new Subject<string>();

    private _getHistoryListTrigger = new Subject<number>(); // 就诊历史
    _calculateChineseProcessingFeeTrigger = new Subject();
    private _getAirPharmacyLogisticsTrace = new BehaviorSubject<string>(""); //空中药房处方对应物流信息

    _textInput?: AbcTextInput | null; // 医生

    private _supervisionSccaDoctorCaInfoTrigger: Subject<number> = new Subject<number>(); // 医生证书查询

    private _isUseCaSignatureTrigger: Subject<number> = new Subject<number>(); // 获取医生是否勾选CA电子签名

    get hasChanged(): boolean {
        return this._hasChanged;
    }

    set hasChanged(newValue: boolean) {
        this._hasChanged = newValue;
        this._saveDraft(false);
    }

    get isEditing(): boolean {
        return this.innerState.isEditing ?? false;
    }

    constructor(options: {
        outpatientId?: string;
        draftClinicScope?: boolean;
        draftId?: string;
        detailData?: OutpatientInvoiceDetail;
        registrationSheetId?: string;
    }) {
        super();
        this.outpatientId = options.outpatientId ?? options.detailData?.id;
        this.draftClinicScope = options.draftClinicScope ?? true;
        this.draftId = options.draftId ?? "";
        this.detailData = options.detailData;
        this.registrationSheetId = options.registrationSheetId ?? "";
        this.dispatch(new _EventInit(options));
    }

    static fromContext(context: OutPatientInvoicePageBloc): OutPatientInvoicePageBloc {
        return context;
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    _draftManager(): OutpatientDraftManager {
        return OutpatientDraftManager.getInstance(this.draftClinicScope);
    }

    dispose(): void {
        super.dispose();
        this._saveDraft(false);
        this.unLockPatientOrder();
    }

    private _saveDraft(refreshList?: boolean): void {
        //如果有变更，就保存
        if (this.hasChanged && this.innerState.detailData?.isWaitVisit && this.allowDraft && this.innerState.detailData.isOnline != 1) {
            if (!this.innerState.detailData.draftId) {
                this.innerState.detailData!.draftId = OutpatientDraftManager.generateDraftId();
            }

            this._draftManager().saveDraft(this.innerState.detailData, refreshList);
        }
    }

    @actionEvent(_EventNavToHistoryDialog)
    async *_mapEventNavToHistoryDialog(event: _EventNavToHistoryDialog): AsyncGenerator<State> {
        const result = event.result;
        let needCreateAnotherDetail = false;
        if (result.action == OutpatientHistoryDialogAction.copyHistory) {
            if (!this.innerState.allowEditMedicalRecord) {
                //如果不允许处方操作需要提示-通过后生成一个新的草稿单，将数据传入新的单据中
                const confirmResult = await showQueryDialog("提示", "当前病历不可编辑，无法复制。是否新接诊该患者，并复制病历");
                if (confirmResult == DialogIndex.positive) {
                    needCreateAnotherDetail = true;
                } else {
                    return;
                }
            }

            //复制病历时：
            //需要校验：主诉、现病史、体格检查、诊断、过敏史是否已填
            //弹窗提示插入or覆盖
            let _addType = MedicineTemplateAddType.reset;
            const labelStr = MedicalRecord.keyList
                .map((item) => {
                    if (!!this.innerState.detailData?.medicalRecord?.[item.key]) return `"${item.label}"`;
                })
                .filter((item) => !!item)
                .join(" ");
            if (!!labelStr) {
                _addType = await MedicineTemplateAddTypeDialog.show({
                    title: `已填写${labelStr}`,
                    content: "插入到已填写内容中，还是覆盖已填写内容？",
                    buttonType: [
                        { name: "覆盖", value: MedicineTemplateAddType.reset },
                        { name: "插入", value: MedicineTemplateAddType.push },
                    ],
                });
            }

            //复制病历模板时：
            // 若病历模板中填写了既往史，复制模板时覆当前病历全部内容；
            // 若病历模板中未填写既往史，复制模板时，不覆盖既往史（其余内容还是覆盖）
            const {
                pastHistory: _pastHistory,
                allergicHistory: _allergicHistory,
                familyHistory: _familyHistory,
                personalHistory: _personalHistory,
                epidemiologicalHistory: _epidemiologicalHistory,
            } = this.innerState.detailData?.medicalRecord ?? {};
            const {
                pastHistory: _newPastHistory,
                allergicHistory: _newAllergicHistory,
                familyHistory: _newFamilyHistory,
                personalHistory: _newPersonalHistory,
                epidemiologicalHistory: _newEpidemiologicalHistory,
            } = result.outpatientData?.medicalRecord ?? {};
            this.innerState.detailData!.medicalRecord = this.innerState.detailData!.medicalRecord ?? new MedicalRecord();
            // 复制病历史
            const _attachments = this.innerState.detailData?.medicalRecord?.attachments;
            if (!_addType) {
                return;
            } else if (_addType == MedicineTemplateAddType.push) {
                result.outpatientData?.medicalRecord?.assignObj(this.innerState.detailData?.medicalRecord);
            }
            this.innerState.detailData!.medicalRecord = result.outpatientData?.medicalRecord ?? new MedicalRecord();
            this.innerState.detailData!.medicalRecord.attachments = _attachments; //fix :【ID1016149】【APP门诊改版】先添加图片，复制病历，图片清除了（应该保留）

            this.innerState.detailData!.medicalRecord.pastHistory = (!_pastHistory ? _newPastHistory : _pastHistory) ?? "";
            this.innerState.detailData!.medicalRecord.allergicHistory = (!_allergicHistory ? _newAllergicHistory : _allergicHistory) ?? "";
            this.innerState.detailData!.medicalRecord.familyHistory = (!_familyHistory ? _newFamilyHistory : _familyHistory) ?? "";
            this.innerState.detailData!.medicalRecord.personalHistory = (!_personalHistory ? _newPersonalHistory : _personalHistory) ?? "";
            this.innerState.detailData!.medicalRecord.epidemiologicalHistory =
                (!_epidemiologicalHistory ? _newEpidemiologicalHistory : _epidemiologicalHistory) ?? "";

            this.hasChanged = true;
        } else if (result.action == OutpatientHistoryDialogAction.copyPrescription) {
            if (!this.innerState.allowAddPrescription) {
                //如果不允许处方操作需要提示-通过后生成一个新的草稿单，将数据传入新的单据中
                const confirmResult = await showQueryDialog("提示", "当前病历不可编辑，无法复制。是否新接诊该患者，并复制病历");
                if (confirmResult == DialogIndex.positive) {
                    needCreateAnotherDetail = true;
                } else {
                    return;
                }
            }

            //删除未收费项
            _.remove(this.innerState.detailData!.productForms ?? [], (form) => OutpatientUtils.canEditWithChargeStatus(form.chargeStatus));
            _.remove(this.innerState.detailData!.prescriptionWesternForms ?? [], (form) =>
                OutpatientUtils.canEditWithChargeStatus(form.chargeStatus)
            );
            _.remove(this.innerState.detailData!.prescriptionChineseForms ?? [], (form) =>
                OutpatientUtils.canEditWithChargeStatus(form.chargeStatus)
            );
            _.remove(this.innerState.detailData!.prescriptionInfusionForms ?? [], (form) =>
                OutpatientUtils.canEditWithChargeStatus(form.chargeStatus)
            );
            _.remove(this.innerState.detailData!.prescriptionExternalForms ?? [], (form) =>
                OutpatientUtils.canEditWithChargeStatus(form.chargeStatus)
            );
            if (ABCUtils.isNotEmpty(result.outpatientData?.productForms ?? [])) {
                OutpatientUtils.clearPrescriptionFlags(result.outpatientData?.productForms ?? []);
                this.innerState.detailData!.productForms = this.innerState.detailData!.productForms ?? [];

                for (const form of result.outpatientData?.productForms ?? []) {
                    if (ABCUtils.isEmpty(form.productFormItems)) continue;

                    const oldForm = this.innerState.detailData?.productForms.find(
                        (innerForm) =>
                            innerForm.sourceFormType == form.sourceFormType &&
                            OutpatientUtils.canEditWithChargeStatus(innerForm.chargeStatus)
                    );
                    if (oldForm == null) {
                        this.innerState.detailData?.productForms.push(form);
                    } else {
                        oldForm.productFormItems = oldForm.productFormItems ?? [];
                        oldForm.productFormItems = oldForm.productFormItems.concat(form.productFormItems ?? []);
                    }
                }
            }

            if (ABCUtils.isNotEmpty(result.outpatientData?.prescriptionWesternForms ?? [])) {
                OutpatientUtils.clearPrescriptionFlags(result.outpatientData?.prescriptionWesternForms ?? []);
                this.innerState.detailData!.prescriptionWesternForms = this.innerState.detailData?.prescriptionWesternForms ?? [];
                this.innerState.detailData!.prescriptionWesternForms = this.innerState.detailData?.prescriptionWesternForms.concat(
                    result.outpatientData?.prescriptionWesternForms ?? []
                );
            }

            if (ABCUtils.isNotEmpty(result.outpatientData?.prescriptionChineseForms ?? [])) {
                OutpatientUtils.clearPrescriptionFlags(result.outpatientData?.prescriptionChineseForms ?? []);
                this.innerState.detailData!.prescriptionChineseForms = this.innerState.detailData!.prescriptionChineseForms ?? [];
                this.innerState.detailData!.prescriptionChineseForms = this.innerState.detailData!.prescriptionChineseForms.concat(
                    result.outpatientData?.prescriptionChineseForms ?? []
                );
            }

            if (ABCUtils.isNotEmpty(result.outpatientData?.prescriptionInfusionForms ?? [])) {
                OutpatientUtils.clearPrescriptionFlags(result.outpatientData?.prescriptionInfusionForms ?? []);
                this.innerState.detailData!.prescriptionInfusionForms = this.innerState.detailData!.prescriptionInfusionForms ?? [];
                this.innerState.detailData!.prescriptionInfusionForms = this.innerState.detailData!.prescriptionInfusionForms.concat(
                    result.outpatientData?.prescriptionInfusionForms ?? []
                );
            }

            if (ABCUtils.isNotEmpty(result.outpatientData?.prescriptionExternalForms ?? [])) {
                OutpatientUtils.clearPrescriptionFlags(result.outpatientData?.prescriptionExternalForms ?? []);
                this.innerState.detailData!.prescriptionExternalForms = this.innerState.detailData!.prescriptionExternalForms ?? [];
                this.innerState.detailData!.prescriptionExternalForms = this.innerState.detailData!.prescriptionExternalForms.concat(
                    result.outpatientData?.prescriptionExternalForms ?? []
                );
            }

            this.hasChanged = true;
            this._aiVerify.next(); // 解决复制处方后未提示有风险的药品项
            if (this.innerState.detailData) OutpatientUtils.prescriptionInitKeyId(this.innerState.detailData);
            const calculateRsp = await OutpatientAgent.chargeCalculate(
                OutpatientUtils.tripChargedItemForOutpatientPriceCalculate(this.innerState.detailData!)
            );
            if (!!calculateRsp.prescriptionChineseForms?.length) {
                OutpatientUtils.copyPriceAttrToPrescription(
                    calculateRsp.prescriptionChineseForms,
                    this.innerState.detailData?.prescriptionChineseForms ?? []
                );
            }
            if (!!calculateRsp.prescriptionExternalForms?.length) {
                OutpatientUtils.copyPriceAttrToPrescription(
                    calculateRsp.prescriptionExternalForms,
                    this.innerState.detailData?.prescriptionExternalForms ?? []
                );
            }
            if (!!calculateRsp.prescriptionInfusionForms?.length) {
                OutpatientUtils.copyPriceAttrToPrescription(
                    calculateRsp.prescriptionInfusionForms,
                    this.innerState.detailData?.prescriptionInfusionForms ?? []
                );
            }
            if (!!calculateRsp.prescriptionWesternForms?.length) {
                OutpatientUtils.copyPriceAttrToPrescription(
                    calculateRsp.prescriptionWesternForms,
                    this.innerState.detailData?.prescriptionWesternForms ?? []
                );
            }
            if (!!calculateRsp.productForms?.length) {
                OutpatientUtils.copyPriceAttrToPrescription(calculateRsp.productForms, this.innerState.detailData?.productForms ?? []);
            }
        }
        if (needCreateAnotherDetail) {
            await this._createAnotherDetail();
        }
        this.update();
    }

    @actionEvent(_EventSaveAuxiliaryExaminationInfo)
    async *_mapEventSaveAuxiliaryExaminationInfo(event: _EventSaveAuxiliaryExaminationInfo): AsyncGenerator<State> {
        let value = event.item,
            index = event.index;
        const auxiliaryExaminations = this.innerState.detailData?.medicalRecord?.auxiliaryExaminations ?? [];
        if (!value || index == undefined) {
            value = JsonMapper.deserialize(DentistryMedicalRecordItem, {
                toothNos: [],
                value: "",
            });
            auxiliaryExaminations.push(value);
            index = auxiliaryExaminations.length - 1;
            this.update();
        }
        const result = (await OutpatientAuxiliaryExaminationInputPage.show({
            auxiliaryExaminationInfo: value,
            index,
            canCopyTooth: this.innerState.detailData?.canCopyToothList,
        })) as unknown as {
            info?: DentistryMedicalRecordItem;
            isDelete: boolean;
        };
        if (!result) return;
        const { info, isDelete } = result;
        if (!!info) {
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            auxiliaryExaminations[index] = info;
            this.innerState.detailData!.medicalRecord!.auxiliaryExaminations = auxiliaryExaminations;

            //同步所有牙位
            if (!!OutpatientUtils.asyncOutpatientMedicineRecordToothStatus.get(index)) {
                OutpatientUtils.asyncOutpatientMedicineRecordTooth.next({
                    tooth: info.toothNos ?? [],
                    index: index ?? 0,
                    status: true,
                });
            }
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        } else if (isDelete) {
            _.remove(auxiliaryExaminations, value);
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    @actionEvent(_EventInit)
    async *_mapEventInit(event: _EventInit): AsyncGenerator<State> {
        //初始化相关依赖配置
        await this._initPageConfig();
        this._initPageTrigger();

        /////////////////////////////// 初始化业务数据 //////////////////////////
        if (event.draftId) {
            let orderDetail;
            do {
                try {
                    orderDetail = await this._draftManager().loadOutpatientDraft(event.draftId!);

                    //如果没有找到相应的草稿，但存在门诊id或详情数据，则使用相应的数据
                    if (orderDetail == null && (this.detailData != null || this.outpatientId)) break;

                    ///此单子已经通过其它设备进行了修改，放弃本地草稿
                    if (this.detailData?.dataSignature && this.detailData?.dataSignature != orderDetail.dataSignature) {
                        await this._draftManager().removeDraft(event.draftId);
                        break;
                    }

                    this.innerState.detailData = JsonMapper.deserialize(OutpatientInvoiceDetail, {
                        ...orderDetail,
                        registrationFee: orderDetail?.registrationFee ?? 0.0,
                    });
                    this.innerState.detailData.medicalRecord = this.innerState.detailData.medicalRecord ?? new MedicalRecord();
                    if (orderDetail.isWaitVisit) this.innerState.isEditing = true;

                    this.innerState.loading = false;
                    this.outpatientId = orderDetail.id;
                    yield this.innerState;
                } catch (error) {
                    this.innerState.loading = false;
                    this.innerState.detailData =
                        this.detailData ??
                        JsonMapper.deserialize(OutpatientInvoiceDetail, {
                            draftId: event.draftId,
                            patient: new Patient(),
                            medicalRecord: new MedicalRecord(),
                            registrationFee: 0.0,
                            productForms: [],
                            prescriptionWesternForms: [],
                            prescriptionChineseForms: [],
                            prescriptionInfusionForms: [],
                            clinicId: userCenter.clinic?.clinicId,
                            status: OutpatientInvoiceStatus.waitVisit,
                            doctorName: this.doctorName,
                            doctorId: this.doctorId,
                        });

                    if (this.innerState.detailData?.isWaitVisit) this.innerState.isEditing = true;
                }
            } while (false);
        }

        //获取门诊转诊详情
        if (this.registrationSheetId) {
            this.innerState.registrationDetail = await RegistrationAgent.getRegistrationDetail(this.registrationSheetId).catchIgnore();
        }

        if (this.innerState.detailData != null) {
            //从草稿中恢复出了，需与线上数据进行同步：问诊单+挂号状态
            if (this.outpatientId) {
                const orderDetail = await OutpatientAgent.getOutpatientInvoiceDetail(this.outpatientId).catchIgnore();
                this.innerState.detailData.questionSheets = orderDetail?.questionSheets ?? this.innerState.detailData.questionSheets;
                //判断挂号费状态进行，判断是否进行覆盖
                if (!OutpatientUtils.canEditWithChargeStatus(orderDetail?.registrationFeeStatus)) {
                    this.innerState.detailData.registrationFeeStatus = orderDetail?.registrationFeeStatus;
                    this.innerState.detailData.registrationFee = orderDetail?.registrationFee;
                }
            }
        } else if (this.detailData != null) {
            //草稿中无数据，外部有传值进入(网诊开单)
            this.innerState.detailData = this.detailData;
            if (this.innerState.detailData.isWaitVisit) this.innerState.isEditing = true;
            //判断挂号费状态进行，判断是否进行覆盖
            if (this.outpatientId) {
                const orderDetail = await OutpatientAgent.getOutpatientInvoiceDetail(this.outpatientId).catchIgnore();
                this.innerState.detailData.questionSheets = orderDetail?.questionSheets ?? this.innerState.detailData.questionSheets;
            }
        } else if (event.outpatientId) {
            await this._loadDataFromNetwork();
        } else {
            this.innerState.detailData = JsonMapper.deserialize(OutpatientInvoiceDetail, {
                patient: new Patient(),
                medicalRecord: new MedicalRecord(),
                registrationFee: 0,
                productForms: [],
                prescriptionWesternForms: [],
                prescriptionChineseForms: [],
                prescriptionInfusionForms: [],
                clinicId: userCenter.clinic?.clinicId,
                status: OutpatientInvoiceStatus.waitVisit,
                doctorName: this.doctorName,
                doctorId: this.doctorId,
            });
            this.innerState.isEditing = true;
            this.innerState.loading = false;
            yield this.innerState;
        }

        this.treatOnlineClinicId = this.innerState.detailData?.consultationSheet?.clinicId;
        //未完成接诊，才需要调用
        if (this.innerState.detailData?.isWaitVisit) {
            this._getUnfinishOutpatientTrigger.next(this.innerState.detailData?.patient?.id ?? "");
            if (OutpatientUtils.canEditWithChargeStatus(this.innerState.detailData?.registrationFeeStatus)) {
                //未填写过初复诊的才需要初始化拉取一次
                if (!this.innerState.detailData.revisitStatus) {
                    this._patientRevisitStatusTrigger.next();
                }
            }
        }

        //加锁前查询当前锁单详情
        if (!!this.innerState.detailData?.patientOrderId) {
            const lockDetail = await PatientOrderAgent.getPatientOrdersListLocks(this.innerState.detailData.patientOrderId, [
                PatientOrderLockType.outpatientSheet,
                PatientOrderLockType.chargeSheet,
            ]).catchIgnore();
            if (!!lockDetail?.length) {
                // 锁单展示顺序：医生编辑中 > 收费中 （一次只展示一个）
                const doctorEditing = lockDetail?.find((t) => t.businessKey == PatientOrderLockType.outpatientSheet),
                    chargeOrderInfo = lockDetail?.find((t) => t.businessKey == PatientOrderLockType.chargeSheet);
                if (!!doctorEditing) {
                    this.innerState.lockDetails.set(PatientOrderLockType.outpatientSheet, doctorEditing);
                }
                if (!!chargeOrderInfo && chargeOrderInfo?.value?.chargeInProgress) {
                    this.innerState.lockDetails.set(PatientOrderLockType.chargeSheet, chargeOrderInfo);
                }
            }
        }
        this._initDepartmentAndDoctor();
        //更新当前门诊单中患者详情
        if (!!this.innerState.detailData?.patient?.id) {
            this._initPatientWxBindInfo().then();
            CrmAgent.getPatientById(this.innerState.detailData.patient.id)
                .toObservable()
                .subscribe((patient) => {
                    this.innerState.detailData!.patient = _.merge(this.innerState.detailData!.patient, patient);
                })
                .addToDisposableBag(this);
            this._getHistoryListTrigger.next(0); //获取历史就诊次数
        }

        this._aiVerify.next(0);

        if (this.innerState.detailData?.isOnline == 1) {
            this.draftClinicScope = false;
            this._supervisionSccaDoctorCaInfoTrigger.next(0); // 获取医生证书
            this._isUseCaSignatureTrigger.next(0); // 获取网诊医生是否勾选CA电子签名
        }

        if (this.innerState.isEditing) {
            this._getAIDiagnosisTrigger.next(0);
        }

        // 加载语音记录数据
        if (this.outpatientId) {
            this.innerState.voiceRecordResult = await Asr.getAsrResult(this.outpatientId, "");
        }

        //初始时同步中药处方空中药房供应商信息
        this.innerState.detailData?.prescriptionChineseForms?.forEach((form) => {
            if (form.pharmacyType == PharmacyType.air) {
                this._loadVendor(form).then((rsp) => {
                    if (!rsp) return;
                    rsp.forEach((item) => {
                        if (item.checked) {
                            const _orderItems = item.orderItems?.map((item) => JsonMapper.deserialize(PrescriptionFormItem, item));
                            form.vendor = item;
                            form.vendorId = item.vendorId;
                            form.vendorName = item.vendorName;
                            form.vendorUsageScopeId = item.vendorUsageScopeId;
                            for (const formItem of _orderItems ?? []) {
                                if (!formItem) continue;
                                formItem.goodsId = formItem.productInfo?.id;
                                formItem.productId = formItem.productInfo?.id;
                                formItem.type = formItem.productInfo?.type;
                                formItem.subType = formItem.productInfo?.subType;
                                formItem.name = formItem.productInfo?.displayName ?? formItem.displayName;
                                formItem.unit = formItem.unit ?? "g";
                                formItem.medicineCadn = formItem.productInfo?.medicineCadn;
                                const _formItem = _.findIndex(form.prescriptionFormItems, (i) => i.displayName == formItem.displayName);
                                if (_.isNumber(_formItem)) {
                                    form.prescriptionFormItems![_formItem] = _.merge(form.prescriptionFormItems![_formItem], formItem);
                                } else {
                                    form!.prescriptionFormItems?.push(formItem);
                                }
                            }
                        }
                    });
                    this.update();
                });
                //获取对应处方物流信息
                if (!!form.deliveryInfo?.deliveryOrderNo && !!form?.id) {
                    this._getAirPharmacyLogisticsTrace.next(form?.id ?? "");
                }
            }
        });

        this.innerState.loading = false;

        this.update();
    }

    @actionEvent(_EventEditPatientInfo)
    async *_mapEventEditPatientInfo(event: _EventEditPatientInfo): AsyncGenerator<State> {
        const needGetPastHistory =
            !_.isNil(event.patient.id) && event.patient.id != (event.oldPatient?.id ?? this.innerState.detailData?.patient?.id);
        this.innerState.detailData!.patient = event.patient;
        // 处理口腔诊所，新建患者没有id的情况
        if (userCenter.clinic?.isDentistryClinic && !event.patient?.id) {
            const patientInfo = await PatientAgent.createPatient(event.patient).catchIgnore();
            if (!!patientInfo?.id) event.patient.id = patientInfo?.id;
        }

        if (event.patient.id) {
            // 口腔诊所-此时同步患者信息草稿
            if (this.innerState.isDentistryClinic && !this.innerState.detailData?.id && !userCenter.isTestLogin) {
                const rsp = await OutpatientAgent.postCreateOnlineDraft({
                    type: 0,
                    patientId: event.patient.id,
                }).catch((e) => {
                    showConfirmDialog("提示", errorToStr(e)).then();
                });
                if (rsp && !!rsp?.id) {
                    this.innerState.detailData = this.innerState.detailData ?? new OutpatientInvoiceDetail();
                    this.innerState.detailData.id = rsp.id;
                    this.outpatientId = rsp.id;
                }
            }
            CrmAgent.getPatientById(event.patient.id, { wx: 1, childCareRecords: 1 })
                .toObservable()
                .subscribe((patient: Patient) => {
                    //更换患者时才进行既往史替换
                    if (patient.id != this.innerState.detailData!.patient?.id) {
                        this.innerState.detailData!.medicalRecord!.pastHistory = patient.pastHistory ?? "";
                    }
                    this.innerState.detailData!.medicalRecord!.allergicHistory = patient.allergicHistory ?? "";
                    this._initPatientWxBindInfo();
                    this.innerState.detailData!.patient = _.merge(this.innerState.detailData!.patient, patient);
                    if (patient.sex == "男") {
                        this.clearMedicalRecordObstetricalHistory();
                    }
                    this.update();
                })
                .addToDisposableBag(this);

            OutpatientAgent.getOutpatientHistoryList(event.patient.id)
                .toObservable()
                .subscribe((history) => {
                    this.innerState.detailData!.diagnoseCount = history?.totalCount ?? 0;
                    this.update();
                })
                .addToDisposableBag(this);
            this._getUnfinishOutpatientTrigger.next(event.patient.id);
        }

        if (needGetPastHistory) {
            const history = await OutpatientAgent.getPastHistory(event.patient.id!);
            this.innerState.detailData!.medicalRecord!.pastHistory = history;
            this.innerState.detailData!.medicalRecord!.patientId = event.patient.id;
            this._patientRevisitStatusTrigger.next();
        }

        if (needGetPastHistory || this.innerState.isEditing) this.hasChanged = true;

        if (event.patient?.sex == "男") {
            this.clearMedicalRecordObstetricalHistory();
        }

        this._saveDraft(true);
        this.update();
    }

    //判断当前患者是否可以推送
    async _initPatientWxBindInfo(): Promise<void> {
        CrmAgent.postPatientFamilyWxStatus(this.innerState.detailData?.patient?.chainId ?? userCenter.clinic?.chainId ?? "", [
            this.innerState.detailData?.patient?.id ?? "",
        ])
            .then((result) => {
                // isPush，这个为1就能推，不会管微信绑定和关注状态；要是这个isPush不为1，就再去判断这个患者是否绑定且关注微信，达到条件就可以推送
                if (result && !_.isEmpty(result.list)) {
                    this.innerState.canSendToPatient =
                        (result.list![0].isPush == 1 ||
                            (result.list![0].isPush != 1 && result.list![0].wxStatus == WxBindStatus.SUBSCRIBE_AND_BIND)) ??
                        false;
                }
            })
            .catchIgnore()
            .then();
    }

    /**
     * 清空当前的月经婚育史信息
     * 触发场景：患者由男性转变为女性
     */
    clearMedicalRecordObstetricalHistory(): void {
        if (!!this.innerState.detailData?.medicalRecord?.obstetricalHistory) {
            this.innerState.detailData.medicalRecord.obstetricalHistory = [];
        }
    }

    _loadVendor(form: PrescriptionChineseForm): Promise<AirPharmacyVendor[] | undefined> {
        return ChargeAgent.getVendors({
            airPharmacyFormItems: form.prescriptionFormItems,
            doseCount: form.doseCount!,
            goodsTypeId: ChineseMedicineSpecType.typeFromName(form.specification!)
                ? ChineseGoodType.chineseGranule
                : ChineseGoodType.chinesePiece,
            medicineStateScopeId: form.medicineStateScopeId!,
            usageScopeId: form.usageScopeId!,
            vendorId: form.vendorId!,
            pharmacyNo: form.pharmacyNo,
        }).catchIgnore();
    }

    _initDepartmentAndDoctor(): void {
        Promise.all([
            RegistrationAgent.getClinicAllDoctorRegFeeList(),
            userCenter.employee,
            OutpatientPreference.loadRecentSelectDoctorV2(),
            ClinicAgent.getClinicAllDoctorsRegfee(userCenter.clinic?.isManager ? 0 : 1, 0, 0, 0), // 管理员需展示所有医生
            ClinicAgent.getClinicDoctorOwnRegfee(), //医生个人
        ])
            .toObservable()
            .subscribe(
                async (allRsp) => {
                    const DoctorsRegFeeList = allRsp[0];
                    const employee: ClinicEmployee = JsonMapper.deserialize(ClinicEmployee, allRsp[1]!);
                    const recentSelect: ClinicDoctorInfo = JsonMapper.deserialize(ClinicDoctorInfo, allRsp[2]!);
                    const canUseDoctor = allRsp[3]?.filter((item) => item.departmentType !== DepartmentTypeStatus.childHealthType) ?? [];
                    const doctorHimself = allRsp[4]?.filter((item) => item.departmentType !== DepartmentTypeStatus.childHealthType);

                    //可用医生中无当前医生，手动填一个
                    doctorHimself?.forEach((s_item) => {
                        if (
                            !canUseDoctor?.find(
                                (c_item) => s_item.doctorId == c_item.doctorId && s_item.departmentId == c_item.departmentId
                            )
                        ) {
                            canUseDoctor.push(s_item);
                        }
                    });

                    this.innerState.canUseDoctorList = canUseDoctor;

                    this.innerState.pediatricsDoctorsList = allRsp[3]?.filter(
                        (item) =>
                            item?.departmentType == DepartmentTypeStatus.childHealthType ||
                            (item?.departmentType == DepartmentTypeStatus.outPatientType && item?.mainMedicalName == "儿科") ||
                            (item?.departmentType == DepartmentTypeStatus.outPatientType && item?.mainMedicalName == "小儿外科") ||
                            (item?.departmentType == DepartmentTypeStatus.outPatientType && item?.mainMedicalName == "儿童保健科")
                    );

                    /**
                     * 拉取所有的医生列表
                     * 医助角色：拉取医助列表
                     * 非医助不需要拉取，只需要对比当前id
                     */
                    //当前可用医生使用canUseDoctorList数据
                    //从employeeRegistrationFees中获取挂号费，没有的取诊所默认挂号费
                    this.innerState.doctorsList = canUseDoctor
                        ?.map((doctorItem) => {
                            const sameRegItem = DoctorsRegFeeList?.employeeRegistrationFees?.find(
                                (item) => item.doctorId == doctorItem.doctorId && item.departmentId == doctorItem.departmentId
                            );
                            return sameRegItem ?? JsonMapper.deserialize(ClinicDoctorInfo, { ...DoctorsRegFeeList, ...doctorItem });
                        })
                        .filter(
                            (it) =>
                                !!(
                                    ((userCenter.clinic?.isDoctorAssist || userCenter.clinic?.isManager) &&
                                        !!canUseDoctor?.find(
                                            (itt) => itt.doctorId == it.doctorId && itt.departmentId == it.departmentId
                                        )) ||
                                    (userCenter.clinic?.isDoctor &&
                                        !!doctorHimself?.find((itt) => itt.doctorId == it.doctorId && itt.departmentId == it.departmentId))
                                )
                        );
                    // this.innerState.doctorsList = DoctorsRegFeeList?.employeeRegistrationFees?.filter(
                    //     (it) =>
                    //         !!(
                    //             ((userCenter.clinic?.isDoctorAssist || userCenter.clinic?.isManager) &&
                    //                 !!canUseDoctor?.find((itt) => itt.doctorId == it.doctorId && itt.departmentId == it.departmentId)) ||
                    //             (userCenter.clinic?.isDoctor &&
                    //                 !!doctorHimself?.find((itt) => itt.doctorId == it.doctorId && itt.departmentId == it.departmentId))
                    //         )
                    // );
                    const detailData = this.innerState.detailData;
                    this._getCurrentDoctorDetailInfo();
                    if (this.innerState.detailData?.isOnline == 1) {
                        // 如果是网诊单，只能选择患者当前咨询医生的列表
                        this.innerState.doctorsList = this.innerState.doctorsList?.filter(
                            (item) => item.doctorId == this.innerState.detailData?.doctorId
                        );
                        // 网诊过来的数据，没有departmentId，需要将医生列表数据中的对应医生项的departmentId赋值给detailData
                        const currentSelectDoctor = this.innerState.doctorsList?.find(
                            (item) => item.doctorId == this.innerState.detailData?.doctorId
                        );
                        if (currentSelectDoctor) {
                            this.innerState.detailData!.departmentId = currentSelectDoctor.departmentId;
                            this.innerState.detailData!.departmentName = currentSelectDoctor.departmentName;
                        }
                    }
                    if (!detailData || !!detailData?.doctorId) return;
                    let selectDoctor: ClinicDoctorInfo | undefined = undefined;
                    //如果最近选择的医生有效，直接使用最近的选择
                    selectDoctor = this.innerState.doctorsList?.find(
                        (item) =>
                            item.doctorId == recentSelect.doctorId &&
                            item.doctorId == employee.id &&
                            item.departmentId == recentSelect.departmentId
                    );
                    if (!selectDoctor) {
                        selectDoctor = this.innerState.doctorsList?.find((item) => item.doctorId == employee.id);
                    }
                    selectDoctor =
                        selectDoctor ??
                        JsonMapper.deserialize(ClinicDoctorInfo, {
                            regUnitPrice: DoctorsRegFeeList?.regUnitPrice,
                        });
                    this.innerState.detailData!.departmentId = selectDoctor.departmentId;
                    this.innerState.detailData!.departmentName = selectDoctor.departmentName;
                    this.innerState.detailData!.doctorName = selectDoctor.doctorName;
                    this.innerState.detailData!.doctorId = selectDoctor.doctorId;
                    //重新匹配号种
                    if (this.innerState.enableRegistrationCategories) {
                        const _result = await RegistrationAgent.getRegistrationDoctorEnableCategories({
                            doctorId: selectDoctor.doctorId,
                            departmentId: selectDoctor.departmentId,
                        }).catchIgnore();
                        if (!!_result) {
                            this.innerState.doctorRegistrationCategories = _result;
                        }
                    }
                    if (OutpatientUtils.canEditWithChargeStatus(this.innerState.detailData?.registrationFeeStatus)) {
                        this.innerState.detailData!.registrationFee = selectDoctor.regUnitPrice ?? 0;
                    }
                    //未完成接诊，需要根据当前医生拉取一次初复诊（针对挂号时候不指定医生情况）
                    if (this.innerState.detailData?.isWaitVisit) {
                        this._patientRevisitStatusTrigger.next();
                    }
                    this.update();
                },
                (error) => {
                    if (error.code === 401) {
                        return;
                    }
                }
            )
            .addToDisposableBag(this);
    }

    @actionEvent(_EventUpdate)
    async *_mapEventUpdate(/*event: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    @actionEvent(_EventInvoiceTemplate)
    async *_mapEventInvokeTemplateTap(event: _EventInvoiceTemplate): AsyncGenerator<State> {
        const tp: PrescriptionTemplate =
            event.prescriptionTemplate ?? (await OutpatientPrescriptionTemplateSearchDialog.show(this.innerState.ableUseAirPharmacy));

        if (!tp) return;
        if (this.innerState.enableDoctorPractice) {
            const _removeGoodsList: PrescriptionFormItem[] = [];
            tp.prInfo?.prescriptionWesternForms?.forEach((form) => {
                _removeGoodsList.concat(
                    _removeGoodsList,
                    OutpatientUtils.removePrescriptionByPracticeInfo(
                        form,
                        this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                            this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                        )
                    ) ?? []
                );
            });
            (tp.prInfo?.prescriptionChineseForms?.filter((form) => form.pharmacyType != PharmacyType.air) ?? [])?.forEach((form) => {
                _removeGoodsList.concat(
                    _removeGoodsList,
                    OutpatientUtils.removePrescriptionByPracticeInfo(
                        form,
                        this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                            this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                        )
                    ) ?? []
                );
            });
            (tp.prInfo?.prescriptionInfusionForms?.filter((form) => form.pharmacyType != PharmacyType.air) ?? [])?.forEach((form) => {
                _removeGoodsList.concat(
                    _removeGoodsList,
                    OutpatientUtils.removePrescriptionByPracticeInfo(
                        form,
                        this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                            this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                        )
                    ) ?? []
                );
            });
            (tp.prInfo?.prescriptionExternalForms?.filter((form) => form.pharmacyType != PharmacyType.air) ?? [])?.forEach((form) => {
                _removeGoodsList.concat(
                    _removeGoodsList,
                    OutpatientUtils.removePrescriptionByPracticeInfo(
                        form,
                        this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                            this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                        )
                    ) ?? []
                );
            });
            if (_removeGoodsList.length > 0) {
                const groupedByType = _removeGoodsList.reduce((accumulator, currentItem) => {
                    const type = currentItem.goodsInfo.antibiotic ?? 0;
                    if (!accumulator[type]) {
                        accumulator[type] = [];
                    }
                    accumulator[type].push(currentItem);
                    return accumulator;
                }, {} as { [key: string]: PrescriptionFormItem[] });
                let str = "";
                Object.keys(groupedByType).forEach((key) => {
                    groupedByType[key].map((item) => (str += `【${item.goodsInfo.displayName}】`));
                    if (Number(key) == AntibioticEnum.yes) {
                        str += `为限制级抗菌药物\n`;
                    } else if (Number(key) == AntibioticEnum.special) {
                        str += `为特殊抗菌药物\n`;
                    }
                });
                await showConfirmDialog("提示", `${str}当前医生不具备使用权限`);
            }
        }

        let needCreateAnotherDetail = false;

        //如果不允许处方操作需要提示-通过后生成一个新的草稿单，将数据传入新的单据中
        if (!this.innerState.allowAddPrescription) {
            const confirmResult = await showQueryDialog("提示", "当前病历不可编辑，无法复制。是否新接诊该患者，并复制病历");
            if (confirmResult == DialogIndex.positive) {
                needCreateAnotherDetail = true;
            } else {
                return;
            }
        }

        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        const refreshResult = await OutpatientUtils.refreshGoodsInfo(
            [
                ...(tp.prInfo?.prescriptionWesternForms ?? []),
                ...(tp.prInfo?.prescriptionChineseForms?.filter((form) => form.pharmacyType != PharmacyType.air) ?? []),
                ...(tp.prInfo?.prescriptionInfusionForms ?? []),
                ...(tp.prInfo?.prescriptionExternalForms ?? []),
            ],
            this.treatOnlineClinicId ?? ""
        );
        await tp.prInfo?.prescriptionChineseForms
            ?.filter((form) => form.pharmacyType == PharmacyType.air)
            .forEach((form) => {
                this._loadVendor(form).then((rsp) => {
                    if (!rsp) return;
                    rsp.forEach((item) => {
                        if (item.checked) {
                            const _orderItems = item.orderItems?.map((item) => JsonMapper.deserialize(PrescriptionFormItem, item));
                            form.vendor = item;
                            //最新供应商基础信息赋值到处方单上
                            form.totalPrice = item.totalPrice;
                            form.processPrice = item.processPrice;
                            form.vendorName = item.vendorName;
                            form.vendorId = item.vendorId;
                            form.vendorUsageScopeId = item.vendorUsageScopeId;

                            for (const formItem of _orderItems ?? []) {
                                if (!formItem) continue;
                                formItem.goodsId = formItem.productInfo?.id;
                                formItem.productId = formItem.productInfo?.id;
                                formItem.type = formItem.productInfo?.type;
                                formItem.subType = formItem.productInfo?.subType;
                                formItem.name = formItem.productInfo?.displayName ?? formItem.name;
                                formItem.unit = formItem.unit ?? "g";
                                formItem.medicineCadn = formItem.productInfo?.medicineCadn;

                                if (!!event.prescriptionTemplate) {
                                    for (const list of this.innerState.aiDiagnosis?.list ?? []) {
                                        for (const prescriptionExam of list.prescriptionExam ?? []) {
                                            if (ABCUtils.isNotEmpty(prescriptionExam.prescriptionFormItems ?? [])) {
                                                for (const prescriptionFormItem of prescriptionExam.prescriptionFormItems ?? []) {
                                                    if (!prescriptionFormItem) continue;
                                                    formItem.name = prescriptionFormItem.name;
                                                    formItem.unit = prescriptionFormItem.unit ?? "g";
                                                    formItem.medicineCadn = prescriptionFormItem.medicineCadn;
                                                    formItem.unitCount = Number(prescriptionFormItem.unitCount);
                                                }
                                            }
                                        }
                                    }
                                }

                                const _formItem = _.findIndex(form.prescriptionFormItems, (i) => i.displayName == formItem.displayName);
                                if (_.isNumber(_formItem)) {
                                    form.prescriptionFormItems![_formItem] = _.merge(form.prescriptionFormItems![_formItem], formItem);
                                } else {
                                    form!.prescriptionFormItems?.push(formItem);
                                }
                            }
                            this._modifyChinesePrescriptionUsage(form);
                        }
                    });
                    this.update();
                });
            });
        if (refreshResult != null) {
            return await Toast.show(`添加失败 ${errorToStr(refreshResult)}`, {
                warning: true,
            });
        }

        if (ABCUtils.isNotEmpty(tp.prInfo?.prescriptionInfusionForms ?? [])) {
            this.innerState.detailData!.prescriptionInfusionForms = (this.innerState.detailData?.prescriptionInfusionForms ?? []).concat(
                tp.prInfo?.prescriptionInfusionForms ?? []
            );

            //如果开启多药房，复制历史处方和使用模板，都不记录原来的处方，都从默认下达处方取
            if (this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy) {
                const prescriptionInfusionForms = this.innerState.detailData!.prescriptionInfusionForms;
                prescriptionInfusionForms?.map((item) => {
                    item.pharmacyNo = this.initPrescriptionPharmacyInfo(GoodsTypeId.medicineWest)?.no;
                    item.pharmacyType = this.initPrescriptionPharmacyInfo(GoodsTypeId.medicineWest)?.type;
                    item.pharmacyName = this.initPrescriptionPharmacyInfo(GoodsTypeId.medicineWest)?.name;
                    item.prescriptionFormItems?.map((formItem) => {
                        formItem.pharmacyNo = this.initPrescriptionPharmacyInfo(GoodsTypeId.medicineWest)?.no;
                        formItem.pharmacyType = this.initPrescriptionPharmacyInfo(GoodsTypeId.medicineWest)?.type;
                        //更新库存信息
                        formItem.resetStockByPharmacyNo();
                    });
                });
            }

            const firstGroup = ABCUtils.first(tp.prInfo?.prescriptionInfusionForms ?? []);
            this.innerState.focusItemKey = firstGroup?.scrollKey;
            yield ScrollToFocusItemState.fromState(this.innerState);
        }

        // 添加中药模板特殊处理，支持添加到汤头功能
        if (ABCUtils.isNotEmpty(tp.prInfo?.prescriptionChineseForms ?? [])) {
            //收集当前未收费的中药处方
            const unChargedChineseForms: Array<Pair<PrescriptionChineseForm, number>> = [];
            let index = 0;
            this.innerState.detailData?.prescriptionChineseForms?.forEach((form) => {
                if (OutpatientUtils.canEditWithChargeStatus(form.chargeStatus!)) {
                    unChargedChineseForms.push(new Pair(form, index));
                }
                index++;
            });

            if (ABCUtils.isNotEmpty(unChargedChineseForms)) {
                const formOptions = unChargedChineseForms.map((item) => `中药处方${ABCUtils.toChineseNum(item.second + 1)}`);
                const options: Array<string> = ["新处方", ...formOptions];

                const selects: Array<number> | undefined = await AbcDialog.showOptionsBottomSheet({
                    title: "该汤头添加至",
                    options: options,
                    initialSelectIndexes: new Set(),
                });
                if (ABCUtils.isEmpty(selects)) {
                    return;
                }

                if (selects![0] == 0) {
                    //新加处方
                    this.innerState.detailData!.prescriptionChineseForms = (
                        this.innerState.detailData?.prescriptionChineseForms ?? []
                    ).concat(tp.prInfo?.prescriptionChineseForms ?? []);
                    this.innerState.focusItemKey = ABCUtils.first(tp.prInfo?.prescriptionChineseForms ?? [])?.scrollKey;
                    yield ScrollToFocusItemState.fromState(this.innerState);
                } else {
                    const prescriptionForm = unChargedChineseForms[selects![0] - 1].first; //第一项为"新处方"，所以-1
                    let finalFormItems: Array<PrescriptionFormItem> = [];
                    let doseCount = 0;
                    //收集模板中的所有中药项
                    tp.prInfo?.prescriptionChineseForms?.forEach((form) => {
                        doseCount += Number(form.doseCount ?? 0);
                        if (ABCUtils.isNotEmpty(form.prescriptionFormItems ?? []))
                            finalFormItems = finalFormItems.concat(form.prescriptionFormItems ?? []);
                    });

                    prescriptionForm.prescriptionFormItems = prescriptionForm.prescriptionFormItems ?? [];
                    //将所有模板中药项，添加到选择的中药处方中(去重)
                    for (const item of finalFormItems) {
                        const _sameGood = prescriptionForm.prescriptionFormItems.find((t) =>
                            !!t.id && item.id ? t.id == item.id && t.displayName == item.displayName : t.displayName == item.displayName
                        );
                        if (!_sameGood) {
                            prescriptionForm.prescriptionFormItems.push(item);
                        }
                    }

                    //去重，并收集到finalFormItems
                    finalFormItems = [];
                    for (const formItem of prescriptionForm.prescriptionFormItems) {
                        const exist = finalFormItems.find((existItem) => {
                            return (
                                existItem.goodsInfo.id == formItem.goodsInfo.id &&
                                existItem.goodsInfo.medicineCadn == formItem.goodsInfo.medicineCadn
                            );
                        });
                        if (exist == null) {
                            finalFormItems.push(formItem);
                        }
                    }

                    prescriptionForm.prescriptionFormItems = finalFormItems;
                    prescriptionForm.doseCount = _.max([prescriptionForm.doseCount ?? 0, Number(doseCount)]); //产品需要，将模板中所有中药的剂数，累计添加

                    //如果是空中药房---需要再调一次供应商(刷新库存)
                    if (prescriptionForm.pharmacyType == PharmacyType.air) {
                        await this._loadVendor(prescriptionForm).then((rsp) => {
                            if (!rsp) return;
                            rsp.forEach((item) => {
                                if (item.checked) {
                                    const _orderItems = item.orderItems?.map((item) => JsonMapper.deserialize(PrescriptionFormItem, item));
                                    prescriptionForm.vendor = item;
                                    //最新供应商基础信息赋值到处方单上
                                    prescriptionForm.totalPrice = item.totalPrice;
                                    prescriptionForm.processPrice = item.processPrice;
                                    prescriptionForm.vendorName = item.vendorName;
                                    prescriptionForm.vendorId = item.vendorId;
                                    prescriptionForm.vendorUsageScopeId = item.vendorUsageScopeId;

                                    for (const formItem of _orderItems ?? []) {
                                        if (!formItem) continue;
                                        formItem.goodsId = formItem.productInfo?.id;
                                        formItem.productId = formItem.productInfo?.id;
                                        formItem.type = formItem.productInfo?.type;
                                        formItem.subType = formItem.productInfo?.subType;
                                        formItem.name = formItem.productInfo?.displayName ?? formItem.name;
                                        formItem.unit = formItem.unit ?? "g";
                                        formItem.medicineCadn = formItem.productInfo?.medicineCadn;
                                        const _formItem = _.findIndex(
                                            prescriptionForm.prescriptionFormItems,
                                            (i) => i.displayName == formItem.displayName
                                        );
                                        if (_.isNumber(_formItem)) {
                                            prescriptionForm.prescriptionFormItems![_formItem] = _.merge(
                                                prescriptionForm.prescriptionFormItems![_formItem],
                                                formItem
                                            );
                                        } else {
                                            prescriptionForm!.prescriptionFormItems?.push(formItem);
                                        }
                                    }
                                    this._modifyChinesePrescriptionUsage(prescriptionForm);
                                }
                            });
                            this.update();
                        });
                    }

                    //滚动到新添加项
                    this.innerState.focusItemKey = prescriptionForm.scrollKey;
                    yield ScrollToFocusItemState.fromState(this.innerState);
                }
            } else {
                this.innerState.detailData!.prescriptionChineseForms = (this.innerState.detailData?.prescriptionChineseForms ?? []).concat(
                    tp.prInfo?.prescriptionChineseForms ?? []
                );

                const focusGroup = ABCUtils.first(tp.prInfo?.prescriptionChineseForms ?? []);
                this.innerState.focusItemKey = focusGroup?.scrollKey;
                yield ScrollToFocusItemState.fromState(this.innerState);
            }

            //如果开启多药房，复制历史处方和使用模板，都不记录原来的处方，都从默认下达处方取
            if (this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy) {
                const prescriptionChineseForms = this.innerState.detailData!.prescriptionChineseForms;
                const initGoodsTypeId =
                    prescriptionChineseForms?.[0]?.specification == ChineseMedicineSpecType.fullNames()[1]
                        ? GoodsTypeId.medicineChineseGranule
                        : GoodsTypeId.medicineChinesePiece;
                prescriptionChineseForms?.map((item) => {
                    item.pharmacyNo = this.initPrescriptionPharmacyInfo(initGoodsTypeId)?.no;
                    item.pharmacyType = this.initPrescriptionPharmacyInfo(initGoodsTypeId)?.type;
                    item.pharmacyName = this.initPrescriptionPharmacyInfo(initGoodsTypeId)?.name;
                    item.prescriptionFormItems?.map((formItem) => {
                        formItem.pharmacyNo = this.initPrescriptionPharmacyInfo(initGoodsTypeId)?.no;
                        formItem.pharmacyType = this.initPrescriptionPharmacyInfo(initGoodsTypeId)?.type;
                        //更新库存信息
                        formItem.resetStockByPharmacyNo();
                    });
                });
            }
        }

        if (ABCUtils.isNotEmpty(tp.prInfo?.prescriptionWesternForms ?? [])) {
            this.innerState.detailData!.prescriptionWesternForms = (this.innerState.detailData?.prescriptionWesternForms ?? []).concat(
                tp.prInfo?.prescriptionWesternForms ?? []
            );

            //如果开启多药房，复制历史处方和使用模板，都不记录原来的处方，都从默认下达处方取
            if (this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy) {
                const prescriptionWesternForms = this.innerState.detailData!.prescriptionWesternForms;
                prescriptionWesternForms?.map((item) => {
                    item.pharmacyNo = this.initPrescriptionPharmacyInfo(GoodsTypeId.medicineWest)?.no;
                    item.pharmacyType = this.initPrescriptionPharmacyInfo(GoodsTypeId.medicineWest)?.type;
                    item.pharmacyName = this.initPrescriptionPharmacyInfo(GoodsTypeId.medicineWest)?.name;
                    item.prescriptionFormItems?.map((formItem) => {
                        formItem.pharmacyNo = this.initPrescriptionPharmacyInfo(GoodsTypeId.medicineWest)?.no;
                        formItem.pharmacyType = this.initPrescriptionPharmacyInfo(GoodsTypeId.medicineWest)?.type;
                        //更新库存信息
                        formItem.resetStockByPharmacyNo();
                    });
                });
            }

            const firstGroup = ABCUtils.first(tp.prInfo?.prescriptionWesternForms ?? []);
            this.innerState.focusItemKey = firstGroup?.scrollKey;
            yield ScrollToFocusItemState.fromState(this.innerState);
        }

        if (ABCUtils.isNotEmpty(tp.prInfo?.prescriptionExternalForms ?? [])) {
            this.innerState.detailData!.prescriptionExternalForms = (this.innerState.detailData?.prescriptionExternalForms ?? []).concat(
                tp.prInfo?.prescriptionExternalForms ?? []
            );

            const firstGroup = ABCUtils.first(tp.prInfo?.prescriptionExternalForms ?? []);
            this.innerState.focusItemKey = firstGroup?.scrollKey;
            yield ScrollToFocusItemState.fromState(this.innerState);
        }

        this.hasChanged = true;
        this._aiVerify.next();
        this.innerState.detailData!.expectedTotalPrice = null;
        if (needCreateAnotherDetail) {
            await this._createAnotherDetail();
        }
        if (this.innerState.detailData) OutpatientUtils.prescriptionInitKeyId(this.innerState.detailData);
        const calculateRsp = await OutpatientAgent.chargeCalculate(
            OutpatientUtils.tripChargedItemForOutpatientPriceCalculate(this.innerState.detailData!)
        );
        if (!!calculateRsp.prescriptionChineseForms?.length) {
            OutpatientUtils.copyPriceAttrToPrescription(
                calculateRsp.prescriptionChineseForms,
                this.innerState.detailData?.prescriptionChineseForms ?? []
            );
        }
        if (!!calculateRsp.prescriptionExternalForms?.length) {
            OutpatientUtils.copyPriceAttrToPrescription(
                calculateRsp.prescriptionExternalForms,
                this.innerState.detailData?.prescriptionExternalForms ?? []
            );
        }
        if (!!calculateRsp.prescriptionInfusionForms?.length) {
            OutpatientUtils.copyPriceAttrToPrescription(
                calculateRsp.prescriptionInfusionForms,
                this.innerState.detailData?.prescriptionInfusionForms ?? []
            );
        }
        if (!!calculateRsp.prescriptionWesternForms?.length) {
            OutpatientUtils.copyPriceAttrToPrescription(
                calculateRsp.prescriptionWesternForms,
                this.innerState.detailData?.prescriptionWesternForms ?? []
            );
        }
        if (!!calculateRsp.productForms?.length) {
            OutpatientUtils.copyPriceAttrToPrescription(calculateRsp.productForms, this.innerState.detailData?.productForms ?? []);
        }
        this.update();
    }

    /**
     * 初复诊挂号费用
     * @private
     */
    private registerFeeCalculation(): void {
        const {
            doctorId = DEFAULT_DOCTOR_ID,
            departmentId = "",
            registrationCategory = 0,
            revisitStatus = RegistrationRevisitStatus.first,
        } = this.innerState.detailData || {};
        RegistrationAgent.getRegistrationDoctorEnableCategories({ departmentId, doctorId })
            .catchIgnore()
            .toObservable()
            .subscribe((result) => {
                if (!result) return;
                this.innerState.doctorRegistrationCategories = result;
                const currentCategory =
                    result.registrationFees?.find((item) => item.registrationCategory == registrationCategory) ||
                    result.registrationFees?.[0];
                if (!currentCategory) return;
                const { regUnitPrice, revisitedRegUnitPrice, referralRegUnitPrice } = currentCategory || {};
                let _regUnitPrice;
                // 如果是短期内再次就诊且挂号费不同模式
                if (this.innerState.isShortRevisitsDifferent) {
                    // 是转诊且开启了多号种(目前就医院有多号种)
                    const isReferralAndShowRegistrationCategory =
                        this.innerState.registrationDetail?.registrationFormItem?.isReferral &&
                        this.innerState.enableRegistrationCategories;
                    if (!this.innerState.outpatientRevisitStatusRsp?.lastDiagnosedTime) {
                        _regUnitPrice = isReferralAndShowRegistrationCategory ? referralRegUnitPrice : regUnitPrice || 0;
                    } else {
                        _regUnitPrice = this.innerState.isFirstRevisitWithinDays({
                            departmentId: departmentId,
                            doctorId: doctorId,
                        })
                            ? regUnitPrice
                            : revisitedRegUnitPrice;
                    }
                } else {
                    // 其它模式，直接根据复诊状态设置挂号费
                    _regUnitPrice = revisitStatus === RegistrationRevisitStatus.first ? regUnitPrice : revisitedRegUnitPrice;
                }
                this.innerState.detailData!.registrationFee = _regUnitPrice;
                this.update();
            })
            .addToDisposableBag(this);
    }

    @actionEvent(_EventAddImage)
    async *_mapEventAddImage(/*event: _EventAddImage*/): AsyncGenerator<State> {
        await AbcImagePicker.pickImageAndUpload(
            null,
            `${userCenter.clinic?.clinicId ?? ""}/${OssUpdateModules.MEDICAL_RECORD}`,
            OssUpdateModules.MEDICAL_RECORD
        ).then((result) => {
            if (result == null || _.isEmpty(result.url)) return;

            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            this.innerState.detailData!.medicalRecord.attachments = this.innerState.detailData?.medicalRecord.attachments ?? [];

            const item = JsonMapper.deserialize(AttachmentItem, {
                fileName: FileUtils.getFileNameAndExt(new File(result.filePath!)),
                url: result.url,
                // 要使用签名图片，只需要转换下面两个值即可
                signUrl: result.signUrl,
                useImageSignUrl: result.useImageSignUrl,
            });

            this.innerState.detailData?.medicalRecord.attachments.push(item);

            this.hasChanged = true;
        });

        //滚动
        this.innerState.focusItemKey = OutpatientContentRefKey.attachment;
        yield ScrollToFocusItemState.fromState(this.innerState);
    }

    @actionEvent(_EventSelectDoctor)
    async *_mapEventSelectDoctor(/*event: _EventSelectDoctor*/): AsyncGenerator<State> {
        if (ABCUtils.isEmpty(this.innerState.doctorsList)) {
            await Toast.show("医生列表空");
            return;
        }

        const allDoctorsInfo = _.cloneDeep(this.innerState.doctorsList);

        const selectDoctorInfo = allDoctorsInfo?.find((item) => {
            return item.doctorId == this.innerState.detailData?.doctorId && item.departmentId == this.innerState.detailData?.departmentId;
        });

        const select: ClinicDoctorInfo | undefined = await showBottomPanel(
            <SearchDoctorDepartmentSelectPage selectDoctorInfo={selectDoctorInfo} allDoctorsInfo={allDoctorsInfo ?? []} />,
            { topMaskHeight: Sizes.dp160 }
        );

        if (select != null) {
            const { departmentId, departmentName, doctorId, doctorName } = select;
            this.innerState.detailData!.departmentId = departmentId;
            this.innerState.detailData!.departmentName = departmentName;
            this.innerState.detailData!.doctorName = doctorName;
            this.innerState.detailData!.doctorId = doctorId;

            if (this.innerState.enableDoctorPractice) {
                await this._getCurrentDoctorDetailInfo();
            }
            const detailData = this.innerState.detailData;
            //开单如果是【空中药房】，调整科室、患者重新命中新的本地药房的话，不切换药房哦
            const localPharmacyForms = detailData?.prescriptionChineseForms?.filter((t) => !t?.pharmacyType);
            if (!!localPharmacyForms?.length) this.resetGoodsPharmacyAndStock(localPharmacyForms);
            if (!!detailData?.prescriptionWesternForms?.length) this.resetGoodsPharmacyAndStock(detailData?.prescriptionWesternForms);
            if (!!detailData?.prescriptionInfusionForms?.length) this.resetGoodsPharmacyAndStock(detailData?.prescriptionInfusionForms);
            if (!!detailData?.productForms?.length) this.resetGoodsPharmacyAndStock(detailData?.productForms);
            // 如果是网诊单，切换医生科室，只改变医生科室，不改变挂号费，也不调用算费（挂号费已收的情况下也是同样的，不能再改变挂号费了）
            if (
                !this.innerState.detailData?.isOnline &&
                OutpatientUtils.canEditWithChargeStatus(this.innerState.detailData?.registrationFeeStatus)
            ) {
                //重新匹配号种
                if (this.innerState.enableRegistrationCategories) {
                    const _result = await RegistrationAgent.getRegistrationDoctorEnableCategories({
                        doctorId,
                        departmentId,
                    }).catchIgnore();
                    if (!!_result) {
                        this.innerState.doctorRegistrationCategories = _result;
                        if (!_result.enableCategories?.includes(this.innerState.detailData?.registrationCategory ?? 0)) {
                            this.innerState.detailData!.registrationCategory = 0;
                        }
                    }
                }

                if (OutpatientUtils.canEditWithChargeStatus(this.innerState.detailData?.registrationFeeStatus)) {
                    this.innerState.detailData!.registrationFee = select.regUnitPrice;
                }
                this.innerState.detailData!.expectedTotalPrice = null;
                await OutpatientPreference.saveRecentSelectDoctor(select);
                this._patientRevisitStatusTrigger.next();
            }
            this.hasChanged = true;

            this.update();
        }
    }

    @actionEvent(_EventModifyTap)
    async *_mapEventModifyTap(/*event: _EventModifyTap*/): AsyncGenerator<State> {
        this.innerState.needBackToEdit = false;
        // 只有门诊锁时才禁止修改，收费锁不禁用，因为还可以编辑病历
        if (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) return;
        // 同日就诊调整
        if (
            !!this.innerState.clinicShebaoConfig?.basicInfo?.isOpenSameDayMedicalSettleLimit &&
            !this.innerState.checkOutpatientCreateDateIsToday()
        ) {
            const result = await showQueryDialog(
                "医保同日就诊结算限制",
                "修改时添加项目、处方将生成新收费单，此单无法医保支付。是否继续修改？",
                "重新接诊",
                "继续修改"
            );
            if (result == undefined) return;
            if (result == DialogIndex.positive) {
                await this._createAnotherDetail({ needPrescription: false, needMedicalRecord: false });
                this.innerState.isEditing = true;
                this.update();
                return;
            }
        }
        this.lockPatientOrder(PatientOrderLockType.outpatientSheet);
        this.innerState.isEditing = true;
        this.update();
    }

    /**
     * 修改主诉
     */
    @actionEvent(_EventSaveChiefComplaintInfo)
    async *_EventSaveChiefComplaintInfo(/*event: _EventSaveChiefComplaintInfo*/): AsyncGenerator<State> {
        let info = await ChiefComplaintInputPage.show({
            chiefComplaint: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.chiefComplaint),
        });
        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info);
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.chiefComplaint) {
                return;
            }
            this.innerState.detailData!.medicalRecord.chiefComplaint = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    /**
     * 修改现病史
     */
    @actionEvent(_EventSavePresentHistoryInfo)
    async *_EventSavePresentHistoryInfo(/*event: _EventSavePresentHistoryInfo*/): AsyncGenerator<State> {
        let info = await OutpatientPresentHistoryInputPage.show({
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.presentHistory),
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info);
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.presentHistory) {
                return;
            }
            this.innerState.detailData!.medicalRecord.presentHistory = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
        }
        this.update();
    }

    /**
     * 重置多药房药品对应的药房信息及库存
     * @private
     * @param formList
     */
    private resetGoodsPharmacyAndStock(
        formList: (PrescriptionChineseForm | PrescriptionWesternForm | PrescriptionInfusionForm | PrescriptionProductForm)[]
    ): void {
        formList?.map((form) => {
            if (form instanceof PrescriptionWesternForm || form instanceof PrescriptionInfusionForm) {
                const westernPharmacy = this.innerState.pharmacyInfoConfig?.getDefaultPharmacy({
                    departmentId: this.innerState.detailData?.departmentId,
                    goodsInfo: { typeId: GoodsTypeId.medicineWest },
                });
                form.prescriptionFormItems?.map((medicine) => {
                    //修改库存信息
                    const currentPharmacyStockInfo = medicine?.goodsInfo?.pharmacyGoodsStockList?.find(
                        (t) => t.pharmacyNo == westernPharmacy?.no
                    );
                    medicine.pharmacyNo = currentPharmacyStockInfo?.pharmacyNo;
                    medicine.resetStockByPharmacyNo();
                    return medicine;
                });
            } else if (form instanceof PrescriptionProductForm) {
                form.productFormItems?.map((medicine) => {
                    let initGoodsTypeId;
                    if (medicine?.goodsInfo?.isGoods) {
                        initGoodsTypeId = GoodsTypeId.goods;
                    } else if (medicine?.goodsInfo?.isMedicalMaterial) {
                        initGoodsTypeId = GoodsTypeId.material;
                    }
                    const goodsPharmacy = this.innerState.pharmacyInfoConfig?.getDefaultPharmacy({
                        departmentId: this.innerState.detailData?.departmentId,
                        goodsInfo: { typeId: medicine?.goodsInfo?.typeId ?? initGoodsTypeId },
                    });
                    //修改库存信息
                    const currentPharmacyStockInfo = medicine?.goodsInfo?.pharmacyGoodsStockList?.find(
                        (t) => t.pharmacyNo == goodsPharmacy?.no
                    );
                    medicine.pharmacyNo = currentPharmacyStockInfo?.pharmacyNo;
                    medicine.resetStockByPharmacyNo();
                    return medicine;
                });
            } else {
                const typeId =
                    form.specification == ChineseMedicineSpecType.fullNames()[0]
                        ? GoodsTypeId.medicineChinesePiece
                        : GoodsTypeId.medicineChineseGranule;
                const chinesePharmacy = this.innerState.pharmacyInfoConfig?.getDefaultPharmacy({
                    departmentId: this.innerState.detailData?.departmentId,
                    goodsInfo: { typeId: typeId },
                });
                form.pharmacyName = chinesePharmacy?.name;
                form.pharmacyNo = chinesePharmacy?.no;
                form.pharmacyType = chinesePharmacy?.type;
                form.prescriptionFormItems?.map((medicine) => {
                    //修改库存信息
                    const currentPharmacyStockInfo = medicine?.goodsInfo?.pharmacyGoodsStockList?.find(
                        (t) => t.pharmacyNo == chinesePharmacy?.no
                    );
                    medicine.pharmacyNo = currentPharmacyStockInfo?.pharmacyNo;
                    medicine.resetStockByPharmacyNo();
                    return medicine;
                });
            }
            return form;
        });
    }

    /**
     * 修改既往史
     */
    @actionEvent(_EventSavePastHistoryInfo)
    async *_EventSavePastHistoryInfo(/*event: _EventSavePastHistoryInfo*/): AsyncGenerator<State> {
        let info = await OutpatientPastHistoryInputPage.show({
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.pastHistory),
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info.trim());
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.pastHistory) {
                return;
            }
            this.innerState.detailData!.medicalRecord.pastHistory = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
        }
        this.update();
    }

    //治疗计划
    @actionEvent(_EventSaveTreatmentPlansInfo)
    async *_mapEventSaveTreatmentPlansInfo(event: _EventSaveTreatmentPlansInfo): AsyncGenerator<State> {
        let value = event.item,
            index = event.index;
        const treatmentPlansInfos = this.innerState.detailData?.medicalRecord?.treatmentPlans ?? [];
        if (!value || index == undefined) {
            value = JsonMapper.deserialize(DentistryMedicalRecordItem, {
                toothNos: [],
                value: "",
            });
            treatmentPlansInfos.push(value);
            index = treatmentPlansInfos.length - 1;
            this.innerState.detailData!.medicalRecord!.treatmentPlans = treatmentPlansInfos;
            this.update();
            return;
        }
        const result = (await OutpatientTreatmentPlansInputPage.show({
            value,
            index,
            canCopyTooth: this.innerState.detailData?.canCopyToothList,
        })) as unknown as {
            info?: DentistryMedicalRecordItem;
            isDelete: boolean;
        };
        if (!result) return;
        const { info, isDelete } = result;
        if (!!info) {
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            treatmentPlansInfos[index] = info;
            this.innerState.detailData!.medicalRecord!.treatmentPlans = treatmentPlansInfos;

            //同步所有牙位
            if (!!OutpatientUtils.asyncOutpatientMedicineRecordToothStatus.get(index)) {
                OutpatientUtils.asyncOutpatientMedicineRecordTooth.next({
                    tooth: info.toothNos ?? [],
                    index: index ?? 0,
                    status: true,
                });
            }

            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        } else if (isDelete) {
            _.remove(treatmentPlansInfos, (item, _index) => _index == index);
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    //处置
    @actionEvent(_EventSaveDisposalsInfo)
    async *_mapEventSaveDisposalsInfo(event: _EventSaveDisposalsInfo): AsyncGenerator<State> {
        let value = event.item,
            index = event.index;
        const disposalsInfos = this.innerState.detailData?.medicalRecord?.disposals ?? [];
        if (!value || index == undefined) {
            value = JsonMapper.deserialize(DentistryMedicalRecordItem, {
                toothNos: [],
                value: "",
            });
            disposalsInfos.push(value);
            index = disposalsInfos.length - 1;
            this.innerState.detailData!.medicalRecord!.disposals = disposalsInfos;
            this.update();
            return;
        }
        const result = (await OutpatientDisposalInputPage.show({
            value,
            index,
            canCopyTooth: this.innerState.detailData?.canCopyToothList,
        })) as unknown as {
            info?: DentistryMedicalRecordItem;
            isDelete: boolean;
        };
        if (!result) return;
        const { info, isDelete } = result;
        if (!!info) {
            disposalsInfos[index] = info;
            this.innerState.detailData!.medicalRecord!.disposals = disposalsInfos;

            //同步所有牙位
            if (!!OutpatientUtils.asyncOutpatientMedicineRecordToothStatus.get(index)) {
                OutpatientUtils.asyncOutpatientMedicineRecordTooth.next({
                    tooth: info.toothNos ?? [],
                    index: index ?? 0,
                    status: true,
                });
            }

            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        } else if (isDelete) {
            _.remove(disposalsInfos, (item, _index) => _index == index);
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    //口腔检查
    @actionEvent(_EventSaveOralExaminationInfo)
    async *_mapEventSaveOralExaminationInfo(event: _EventSaveOralExaminationInfo): AsyncGenerator<State> {
        let value = event.item,
            index = event.index;
        const disposalsInfos = this.innerState.detailData?.medicalRecord?.dentistryExaminations ?? [];
        if (!value || index == undefined) {
            value = JsonMapper.deserialize(DentistryMedicalRecordItem, {
                toothNos: [],
                value: "",
            });
            disposalsInfos.push(value);
            index = disposalsInfos.length - 1;
            this.innerState.detailData!.medicalRecord!.dentistryExaminations = disposalsInfos;
            this.update();
            return;
        }
        const result = (await OralExaminationInputPage.show({ value, index })) as unknown as {
            info?: DentistryMedicalRecordItem;
            isDelete: boolean;
        };
        if (!result) return;
        const { info, isDelete } = result;
        if (!!info) {
            disposalsInfos[index] = info;
            this.innerState.detailData!.medicalRecord!.dentistryExaminations = disposalsInfos;

            //同步所有牙位
            if (!!OutpatientUtils.asyncOutpatientMedicineRecordToothStatus.get(index)) {
                OutpatientUtils.asyncOutpatientMedicineRecordTooth.next({
                    tooth: info.toothNos ?? [],
                    index: index ?? 0,
                    status: true,
                });
            }

            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        } else if (isDelete) {
            _.remove(disposalsInfos, (item, _index) => _index == index);
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    @actionEvent(_EventUpdateRegistrationFee)
    async *_mapEventUpdateRegistrationFee(event: _EventUpdateRegistrationFee): AsyncGenerator<State> {
        this.innerState.detailData!.registrationFee = event.fee ?? 0;
        this.hasChanged = true;
        this.innerState.detailData!.expectedTotalPrice = null;

        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        this.update();
    }

    /**
     * 修改体格检查
     */
    @actionEvent(_EventSavePhysicalExaminationInfo)
    async *_EventSavePhysicalExaminationInfo(/*event: _EventSavePhysicalExaminationInfo*/): AsyncGenerator<State> {
        let info = await OutpatientPhysicalExaminationInputPage.show({
            type: "physical",
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.physicalExamination),
            aiDiagnosis: this.innerState.aiDiagnosis,
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info.trim());
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.physicalExamination) {
                return;
            }
            this.innerState.detailData!.medicalRecord.physicalExamination = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
        }

        this.update();
    }

    @actionEvent(_EventChangeOnsetTimeInfo)
    async *_mapEventChangeOnsetTimeInfo(): AsyncGenerator<State> {
        const currentDate = this.innerState.detailData?.medicalRecord?.symptomTime;
        const onsetTime = await TimePicker.show(currentDate);
        if (!onsetTime) return;
        this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
        this.innerState.detailData!.medicalRecord.symptomTime = onsetTime;
        this.hasChanged = true;
        this.innerState.isMedicalChanged = true;
        this.update();
    }

    /**
     * 修改中医体格检查-望闻切诊
     */
    @actionEvent(_EventSaveChineseExaminationInfo)
    async *_mapEventSaveChineseExaminationInfo(/*event: _EventSaveChineseExaminationInfo*/): AsyncGenerator<State> {
        let info = await OutpatientPhysicalExaminationInputPage.show({
            type: "chineseExamination",
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.chineseExamination),
            aiDiagnosis: this.innerState.aiDiagnosis,
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info);
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.chineseExamination) {
                return;
            }
            this.innerState.detailData!.medicalRecord.chineseExamination = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
        }

        this.update();
    }

    /**
     * 修改中医体格检查-舌象
     */
    @actionEvent(_EventSaveTongueInfo)
    async *_mapEventSaveTongueInfo(/*event: _EventSaveTongueInfo*/): AsyncGenerator<State> {
        let info = await OutpatientPhysicalExaminationInputPage.show({
            type: "tongue",
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.tongue),
            aiDiagnosis: this.innerState.aiDiagnosis,
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info);
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.tongue) {
                return;
            }
            this.innerState.detailData!.medicalRecord.tongue = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
        }

        this.update();
    }

    @actionEvent(_EventSavePulseInfo)
    async *_mapEventSavePulseInfo(/*event: _EventSavePulseInfo*/): AsyncGenerator<State> {
        let info = await OutpatientPulseInputPage.show({
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.pulse),
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info);
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.pulse) {
                return;
            }
            this.innerState.detailData!.medicalRecord.pulse = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
        }
        this.update();
    }

    /**
     * 修改月经婚育史
     */
    @actionEvent(_EventSaveObstetricalHistoryInfo)
    async *_mapEventSaveObstetricalHistoryInfo(/*event: _EventSaveObstetricalHistoryInfo*/): AsyncGenerator<State> {
        const info = await showBottomPanel<(ObstetricalHistory | string)[] | undefined>(
            <OutpatientObstetricalHistoryInputPage
                obstetricalHistoryOrigin={this.innerState.detailData?.medicalRecord?.obstetricalHistory}
            />
        );

        if (!_.isNil(info)) {
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            this.innerState.detailData!.medicalRecord.obstetricalHistory = info;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
        }

        this.update();
    }

    @actionEvent(_EventSaveAllergicHistoryInfo)
    async *_EventSaveAllergicHistoryInfo(/*event: _EventSaveAllergicHistoryInfo*/): AsyncGenerator<State> {
        let info = await AllergicHistoryInputPage.show({
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.allergicHistory),
        });
        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info.trim());
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.allergicHistory) {
                return;
            }
            this.innerState.detailData!.medicalRecord.allergicHistory = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
        }
        this.update();
    }

    @actionEvent(_EventPersonalHistoryInfo)
    async *_EventSavePersonalHistoryInfo(/*event: _EventPersonalHistoryInfo*/): AsyncGenerator<State> {
        let info = await PersonalHistoryInputPage.show({
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.personalHistory),
        });
        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info.trim());
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.personalHistory) {
                return;
            }
            this.innerState.detailData!.medicalRecord.personalHistory = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
        }
        this.update();
    }

    /**
     * 口腔门店的口腔检查
     */
    @actionEvent(_EventSaveDefaultOralExaminationInfo)
    async *_mapEventSaveDefaultOralExaminationInfo(/*event: _EventSaveDefaultOralExaminationInfo*/): AsyncGenerator<State> {
        let oralExamination: (OralExaminationItem | string)[] = [];
        try {
            oralExamination = JSON.parse(this.innerState.detailData?.medicalRecord?.oralExamination ?? "[]") as (
                | OralExaminationItem
                | string
            )[];
        } catch (e) {
            oralExamination = [];
        }

        const result = await DefaultOralExaminationInputPage.show({ oralExamination: oralExamination });

        if (!result) return;
        this.innerState.detailData!.medicalRecord!.oralExamination = result;
        this.hasChanged = true;
        this.innerState.isMedicalChanged = true;
        this.update();
    }

    /**
     * 修改诊断
     */
    @actionEvent(_EventSaveDiagnosisInfo)
    async *_mapEventSaveDiagnosisInfo(/*event: _EventSaveDiagnosisInfo*/): AsyncGenerator<State> {
        const diagnosis =
            this.innerState.detailData?.medicalRecord?.diagnosisInfos?.map((item) => item.name)?.join(StringUtils.specialComma) ??
            StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.diagnosis) ??
            "";

        let info = await OutpatientDiagnosisInputPage.show({
            diagnosis: diagnosis,
            aiDiagnosis: this.innerState.aiDiagnosis,
            diagnosisType: this.innerState.outpatientConfig?.medicalRecord?.type,
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info.trim());
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.diagnosis) {
                return;
            }
            this.innerState.focusItemKey = undefined;

            //删除当前已存在但是无匹配的code项目
            const diagnosisList = info.split(StringUtils.specialComma);
            const oldDiagnosisList = this.innerState.detailData?.medicalRecord?.diagnosisInfos;
            const list: DiseasesCode[] = [];
            oldDiagnosisList?.map((item) => {
                if ((diagnosisList?.findIndex((_item) => _item == item.name) ?? -1) > -1) {
                    list.push(item);
                }
            });

            //添加诊断修改新增的内容
            diagnosisList.map((str) => {
                if ((oldDiagnosisList?.findIndex((item) => item.name == str) ?? -1) < 0) {
                    list.push({ code: "", name: str, type: 0, hint: "" });
                }
            });

            this.innerState.detailData!.medicalRecord!.diagnosisInfos = list;
            this.innerState.detailData!.medicalRecord!.diagnosis = list?.map((it) => it.name)?.join(StringUtils.specialComma);

            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    /**
     * 保存治法信息
     */
    @actionEvent(_EventSaveTherapyInfo)
    async *_mapEventSaveTherapyInfo(/*event: _EventSaveTherapyInfo*/): AsyncGenerator<State> {
        let info = await TherapyInputPage.show({
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.therapy),
            therapyType: this.innerState.outpatientConfig?.medicalRecord?.type,
            diagnosis: this.innerState.detailData?.medicalRecord?.diagnosis,
            aiDiagnosis: this.innerState.aiDiagnosis,
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info);
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.therapy) {
                return;
            }
            this.innerState.detailData!.medicalRecord.therapy = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    /**
     * 方药信息
     */
    @actionEvent(_EventSaveChinesePrescriptionInfo)
    async *_mapEventSaveChinesePrescriptionInfo(/*event: _EventSaveChinesePrescriptionInfo*/): AsyncGenerator<State> {
        let info = await PrescriptionInputPage.show({
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.chinesePrescription),
            therapyType: this.innerState.outpatientConfig?.medicalRecord?.type,
            diagnosis: this.innerState.detailData?.medicalRecord?.diagnosis,
            aiDiagnosis: this.innerState.aiDiagnosis,
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info);
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.chinesePrescription) {
                return;
            }
            this.innerState.detailData!.medicalRecord.chinesePrescription = info as string;
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    /**
     * 保存辨证信息
     */
    @actionEvent(_EventSaveSyndromeInfo)
    async *_mapEventSaveSyndromeInfo(): AsyncGenerator<State> {
        let info = await SyndromeInputPage.show({
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.syndrome),
            diagnosis: this.innerState.detailData?.medicalRecord?.diagnosis,
            aiDiagnosis: this.innerState.aiDiagnosis,
            syndromeType: this.innerState.outpatientConfig?.medicalRecord?.type,
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info.trim());
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.syndrome) {
                return;
            }
            this.innerState.detailData!.medicalRecord.syndrome = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    @actionEvent(_EventSaveDentistryDiagnosisInfo)
    async *_mapEventSaveDentistryDiagnosisInfo(event: _EventSaveDentistryDiagnosisInfo): AsyncGenerator<State> {
        let value = event.item,
            index = event.index;
        const dentistryDiagnosisInfos = this.innerState.detailData?.medicalRecord?.extendDiagnosisInfos ?? [];
        if (!value || index == undefined) {
            value = JsonMapper.deserialize(ExtendDiagnosisInfosItem, {
                toothNos: [],
                value: [],
            });
            dentistryDiagnosisInfos.push(value);
            index = dentistryDiagnosisInfos.length - 1;
            this.innerState.detailData!.medicalRecord!.extendDiagnosisInfos = dentistryDiagnosisInfos;
            this.update();
            return;
        }
        const result = (await DentistryDiagnosisInputPage.show({
            diagnosisInfos: value,
            index,
            canCopyTooth: this.innerState.detailData?.canCopyToothList,
        })) as unknown as {
            info?: ExtendDiagnosisInfosItem;
            isDelete: boolean;
        };
        if (!result) return;
        const { info, isDelete } = result;
        if (!!info) {
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            dentistryDiagnosisInfos[index] = info;
            this.innerState.detailData!.medicalRecord!.extendDiagnosisInfos = dentistryDiagnosisInfos;

            //同步所有牙位
            if (!!OutpatientUtils.asyncOutpatientMedicineRecordToothStatus.get(index)) {
                OutpatientUtils.asyncOutpatientMedicineRecordTooth.next({
                    tooth: info.toothNos ?? [],
                    index: index ?? 0,
                    status: true,
                });
            }
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        } else if (isDelete) {
            _.remove(dentistryDiagnosisInfos, (item, _index) => _index == index);
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    @actionEvent(_EventSaveFamilyHistoryInfo)
    async *_mapEventSaveFamilyHistoryInfo(): AsyncGenerator<State> {
        let info = await FamilyHistoryInputPage.show({
            familyHistory: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.familyHistory),
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info);
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.familyHistory) {
                return;
            }
            this.innerState.detailData!.medicalRecord.familyHistory = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    @actionEvent(_EventSaveEpidemiologicalHistoryInfo)
    async *_mapEventSaveEpidemiologicalHistoryInfo(): AsyncGenerator<State> {
        const info = await EpidemiologicalHistoryInputPage.show({
            content: this.innerState.detailData?.medicalRecord?.__EpidemiologicalHistoryObj,
        });

        if (!_.isNil(info)) {
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.epidemiologicalHistory) {
                return;
            }
            this.innerState.detailData!.medicalRecord.epidemiologicalHistory = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    @actionEvent(_EventAddProductInfo)
    async *_mapEventAddProductInfo(event: _EventAddProductInfo): AsyncGenerator<State> {
        let examinationForm = this.innerState.detailData!.productForms?.find((item) => item.isCanEdit && item.isExamination);
        let treatmentForm = this.innerState.detailData!.productForms?.find((item) => item.isCanEdit && item.isTreatment);
        let goodsForm = this.innerState.detailData!.productForms?.find((item) => item.isCanEdit && item.isGoods);
        let materialForm = this.innerState.detailData!.productForms?.find((item) => item.isCanEdit && item.isMaterial);
        let packageForm = this.innerState.detailData!.productForms?.find((item) => item.isCanEdit && item.isPackage);
        let otherFeeForm = this.innerState.detailData!.productForms?.find((item) => item.isCanEdit && item.isOtherFee);
        let nurseFeeForm = this.innerState.detailData!.productForms?.find((item) => item.isCanEdit && item.isNurseFee);
        let surgeryFeeForm = this.innerState.detailData!.productForms?.find((item) => item.isCanEdit && item.isSurgeryFee);
        const formItems = [];
        if (examinationForm?.productFormItems != null) {
            for (const value of examinationForm?.productFormItems) formItems.push(value);
        }
        if (treatmentForm?.productFormItems != null) {
            for (const value of treatmentForm?.productFormItems) formItems.push(value);
        }
        if (goodsForm?.productFormItems != null) {
            for (const value of goodsForm?.productFormItems) formItems.push(value);
        }
        if (materialForm?.productFormItems != null) {
            for (const value of materialForm?.productFormItems) formItems.push(value);
        }
        if (packageForm?.productFormItems != null) {
            for (const value of packageForm?.productFormItems) formItems.push(value);
        }
        if (otherFeeForm?.productFormItems != null) {
            for (const value of otherFeeForm?.productFormItems) formItems.push(value);
        }
        if (nurseFeeForm?.productFormItems != null) {
            for (const value of nurseFeeForm?.productFormItems) formItems.push(value);
        }
        if (surgeryFeeForm?.productFormItems != null) {
            for (const value of surgeryFeeForm?.productFormItems) formItems.push(value);
        }
        let group = MedicineAddGroup.fromFormItems(formItems, true);
        const input: MedicineUsageInput = await ABCNavigator.navigateToPage(
            <NewProductAddPage
                medicineUsageParams={JsonMapper.deserialize(ProductMedicineUsageParams, {
                    title: "诊疗项目",
                    type: MedicineAddType.examination,
                    patient: this.innerState.detailData?.patient,
                    medicines: group != null ? [group] : undefined,
                    selectMedicine: event?.formItem?.goodsInfo,
                    selectGroup: group,
                    outpatientSheetDoctorInfo: {
                        id: this.innerState.detailData?.doctorId,
                        name: this.innerState.detailData?.doctorName,
                        departmentId: this.innerState.detailData?.departmentId,
                    },
                    canCopyToothList: this.innerState.detailData?.canCopyToothList,
                    isOpenSupportInputDays: !!this.innerState.outpatientConfig?.supportInputDays,
                })}
                searchClinicId={this.treatOnlineClinicId}
                detailData={this.innerState.detailData}
                allowAntibiotic={this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                    this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                )}
                disableAddPrescription={this.innerState.disableAddPrescription}
                diagnosisTreatment={this.innerState.outpatientConfig?.diagnosisTreatment}
            />,
            { transitionType: TransitionType.inFromBottom }
        );
        if (input == null) return;

        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        //删除可编辑项（未收费项）
        _.remove(examinationForm?.productFormItems ?? [], (item) => OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!));
        _.remove(treatmentForm?.productFormItems ?? [], (item) => OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!));
        _.remove(goodsForm?.productFormItems ?? [], (item) => OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!));
        _.remove(materialForm?.productFormItems ?? [], (item) => OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!));
        _.remove(packageForm?.productFormItems ?? [], (item) => OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!));
        _.remove(otherFeeForm?.productFormItems ?? [], (item) => OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!));
        _.remove(nurseFeeForm?.productFormItems ?? [], (item) => OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!));
        _.remove(surgeryFeeForm?.productFormItems ?? [], (item) => OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!));
        group = input.medicines![0];
        const examination: GoodsInfo[] = [];
        const treatment: GoodsInfo[] = [];
        const goodsMedicines: GoodsInfo[] = [];
        const materialMedicines: GoodsInfo[] = [];
        const packages: GoodsInfo[] = [];
        const otherFees: GoodsInfo[] = [];
        const nurseFees: GoodsInfo[] = [];
        const surgeryFees: GoodsInfo[] = [];
        group?.selectedMedicines?.forEach((item: GoodsInfo) => {
            if (item.type == GoodsType.examination) examination.push(item);
            if (item.type == GoodsType.treatment) treatment.push(item);
            if (item.type == GoodsType.package) packages.push(item);
            if (item.isGoods) goodsMedicines.push(item);
            if (item.isMedicalMaterial) materialMedicines.push(item);
            if (item.isOtherFee) otherFees.push(item);
            if (item.isNurseFee) nurseFees.push(item);
            if (item.isSurgery) surgeryFees.push(item);
        });

        if (ABCUtils.isNotEmpty(examination)) {
            examinationForm =
                examinationForm ??
                JsonMapper.deserialize(PrescriptionProductForm, {
                    sourceFormType: ChargeSourceFormType.examination,
                    keyId: UUIDGen.generate(),
                });

            const newProductFormItems = examination.map((item: GoodsInfo) => {
                const usage = group!.inputUsages.get(item);
                let formItem = JsonMapper.deserialize(PrescriptionFormItem, {
                    keyId: UUIDGen.generate(),
                    id: item.__id,
                });
                formItem = OutpatientUtils.fillPrescriptionFromItem(formItem, JsonMapper.deserialize(GoodsInfo, item), usage);
                return formItem;
            });

            //重新添加未收费项
            examinationForm!.productFormItems = examinationForm?.productFormItems ?? [];
            examinationForm!.productFormItems = examinationForm?.productFormItems.concat(newProductFormItems);
        }
        this.innerState.detailData!.productForms = this.innerState.detailData?.productForms ?? [];
        _.remove(this.innerState.detailData!.productForms!, (item) => item.isCanEdit && item.isExamination);
        if (examinationForm != null) this.innerState.detailData?.productForms.push(examinationForm);

        if (ABCUtils.isNotEmpty(treatment)) {
            treatmentForm =
                treatmentForm ??
                JsonMapper.deserialize(PrescriptionProductForm, {
                    sourceFormType: ChargeSourceFormType.treatment,
                    keyId: UUIDGen.generate(),
                });

            const newProductFormItems = treatment.map((item: GoodsInfo) => {
                const usage = group?.inputUsages.get(item);
                let formItem = JsonMapper.deserialize(PrescriptionFormItem, {
                    keyId: UUIDGen.generate(),
                    id: item.__id,
                });
                formItem = OutpatientUtils.fillPrescriptionFromItem(formItem, JsonMapper.deserialize(GoodsInfo, item), usage);
                return formItem;
            });

            //重新添加未收费项
            treatmentForm!.productFormItems = treatmentForm?.productFormItems ?? [];
            treatmentForm!.productFormItems = treatmentForm?.productFormItems?.concat(newProductFormItems);
        }
        this.innerState.detailData!.productForms = this.innerState.detailData?.productForms ?? [];
        _.remove(this.innerState.detailData!.productForms!, (item) => item.isCanEdit && item.isTreatment);
        if (treatmentForm != null) this.innerState.detailData?.productForms.push(treatmentForm);

        function createForm(
            oldForm: PrescriptionProductForm,
            sourceFormType: number,
            medicines: Array<GoodsInfo>
        ): PrescriptionProductForm {
            oldForm =
                oldForm ??
                JsonMapper.deserialize(PrescriptionProductForm, {
                    sourceFormType: sourceFormType,
                    keyId: UUIDGen.generate(),
                });
            oldForm.productFormItems = oldForm.productFormItems ?? [];

            const newProductFormItems = medicines.map((goods) => {
                let formItem = JsonMapper.deserialize(PrescriptionFormItem, {
                    keyId: UUIDGen.generate(),
                });
                formItem = OutpatientUtils.fillPrescriptionFromItem(formItem, goods, group!.inputUsages.get(goods));
                return formItem;
            });

            //重新添加未收费项
            oldForm.productFormItems = oldForm.productFormItems.concat(newProductFormItems);

            return oldForm;
        }

        if (ABCUtils.isNotEmpty(goodsMedicines)) {
            goodsForm = createForm(goodsForm!, ChargeSourceFormType.goods, goodsMedicines);
        }

        if (ABCUtils.isNotEmpty(materialMedicines)) {
            materialForm = createForm(materialForm!, ChargeSourceFormType.material, materialMedicines);
        }

        if (ABCUtils.isNotEmpty(packages)) {
            packageForm = createForm(packageForm!, ChargeSourceFormType.package, packages);
        }

        if (ABCUtils.isNotEmpty(otherFees)) {
            otherFeeForm = createForm(otherFeeForm!, ChargeSourceFormType.otherFee, otherFees);
        }

        if (ABCUtils.isNotEmpty(nurseFees)) {
            nurseFeeForm = createForm(nurseFeeForm!, ChargeSourceFormType.nurseProductFee, nurseFees);
        }

        if (ABCUtils.isNotEmpty(surgeryFees)) {
            surgeryFeeForm = createForm(surgeryFeeForm!, ChargeSourceFormType.surgeryFee, surgeryFees);
        }

        this.innerState.detailData!.productForms = this.innerState.detailData!.productForms ?? [];
        _.remove(
            this.innerState.detailData?.productForms ?? [],
            (item) =>
                item.isCanEdit &&
                (item == goodsForm ||
                    item == materialForm ||
                    item == packageForm ||
                    item == otherFeeForm ||
                    item == nurseFeeForm ||
                    item == surgeryFeeForm)
        );

        if (goodsForm != null) this.innerState.detailData?.productForms.push(goodsForm);
        if (materialForm != null) this.innerState.detailData?.productForms.push(materialForm);
        if (packageForm != null) this.innerState.detailData?.productForms.push(packageForm);
        if (otherFeeForm != null) this.innerState.detailData?.productForms.push(otherFeeForm);
        if (nurseFeeForm != null) this.innerState.detailData?.productForms.push(nurseFeeForm);
        if (surgeryFeeForm != null) this.innerState.detailData?.productForms.push(surgeryFeeForm);

        //清空空列表项
        _.remove(this.innerState.detailData!.productForms!, (item) => _.isEmpty(item.productFormItems));

        this.hasChanged = true;
        this.innerState.detailData!.expectedTotalPrice = null;
        this._aiVerify.next();
        this.update();
    }

    @actionEvent(_EventUpdateDoctorAdvices)
    async *_mapEventUpdateDoctorAdvices(/*event: _EventUpdateDoctorAdvices*/): AsyncGenerator<State> {
        const advices = (await OutpatientDoctorAdviseInputPage.show({
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.doctorAdvice),
        })) as unknown as string[];

        if (advices || advices == "") {
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            this.innerState.detailData!.medicalRecord.doctorAdvice = StringUtils.stringN2Br(advices.join("\n"), "<br>");
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    @actionEvent(_EventAddPrescriptionWestern)
    async *_mapEventAddPrescriptionWestern(/*event: _EventAddPrescriptionWestern*/): AsyncGenerator<State> {
        const result: {
            formData: PrescriptionWesternForm;
            extraParameter?: { patient?: Patient; psychotropicNarcoticEmployee?: Patient };
        } = await ABCNavigator.navigateToPage(
            <NewMedicineAddPage
                type={MedicineAddType.western}
                title={"成药处方"}
                outpatientDetail={this.innerState.detailData}
                showAstCheck={this.innerState.outpatientConfig?.astPrescription?.showWesternSwitch}
                allowAntibiotic={this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                    this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                )}
            />,
            { transitionType: TransitionType.inFromBottom }
        );
        const info = result?.formData;
        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        if (!info || !info.prescriptionFormItems?.length) return;
        if (!this.innerState.detailData?.prescriptionWesternForms) this.innerState.detailData!.prescriptionWesternForms = [];

        this.innerState.detailData?.prescriptionWesternForms?.push(info);
        this._updatePatientAndAgentInfo(result?.extraParameter);
        this.hasChanged = true;
        this._aiVerify.next();
        this.innerState.detailData!.expectedTotalPrice = null;
        yield this.innerState;

        //滚动
        this.innerState.focusItemKey = info.scrollKey;
        yield ScrollToFocusItemState.fromState(this.innerState);
    }

    @actionEvent(_EventSaveSyndromeTreatmentInfo)
    async *_mapEventSaveSyndromeTreatmentInfo(): AsyncGenerator<State> {
        let info = await SyndromeTreatmentInputInputPage.show({
            text: StringUtils.stringBr2N(this.innerState.detailData?.medicalRecord?.syndromeTreatment),
            therapyType: this.innerState.outpatientConfig?.medicalRecord?.type,
            diagnosis: this.innerState.detailData?.medicalRecord?.diagnosis,
            aiDiagnosis: this.innerState.aiDiagnosis,
        });

        if (!_.isNil(info)) {
            info = StringUtils.stringN2Br(info);
            this.innerState.detailData!.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();
            if (info == this.innerState.detailData!.medicalRecord.syndromeTreatment) {
                return;
            }
            this.innerState.detailData!.medicalRecord.syndromeTreatment = info as string;
            this._getAIDiagnosisTrigger.next();
            this.hasChanged = true;
            this.innerState.isMedicalChanged = true;
            this.update();
        }
    }

    /**
     * 辅助检查
     */
    requestSaveAuxiliaryExaminationInfo(item?: DentistryMedicalRecordItem, index?: number): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveAuxiliaryExaminationInfo(item, index));
    }

    @actionEvent(_EventAddPrescriptionChinese)
    async *_mapEventAddPrescriptionChinese(/*event: _EventAddPrescriptionChinese*/): AsyncGenerator<State> {
        const result: {
            formData: PrescriptionChineseForm;
            extraParameter?: { patient?: Patient; psychotropicNarcoticEmployee?: Patient };
        } = await ABCNavigator.navigateToPage(
            <NewMedicineAddPage
                type={MedicineAddType.chinese}
                title={"中药处方"}
                outpatientDetail={this.innerState.detailData}
                psychotropicNarcoticType={this.innerState.showDefaultPediatricTag ? PsychotropicNarcoticTypeEnum.ERKE : undefined} // 年龄小于14岁的患者默认标签勾选儿科}
                isOpenVirtualPharmacy={this.innerState.isOpenVirtualPharmacy}
                allowAntibiotic={this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                    this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                )}
            />,
            { transitionType: TransitionType.inFromBottom }
        );
        const info = result?.formData;

        if (!info) return;

        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        if (!info || !info.prescriptionFormItems?.length) return;
        if (!this.innerState.detailData?.prescriptionChineseForms) this.innerState.detailData!.prescriptionChineseForms = [];
        this.innerState.detailData?.prescriptionChineseForms?.push(info);
        this._removeEmptyForm();
        this._updatePatientAndAgentInfo(result?.extraParameter);
        this.hasChanged = true;
        this._aiVerify.next();
        this._calculateChineseProcessingFeeTrigger.next();
        this.innerState.detailData!.expectedTotalPrice = null;
        yield this.innerState;

        //滚动
        this.innerState.focusItemKey = info.scrollKey;
        yield ScrollToFocusItemState.fromState(this.innerState);
    }

    @actionEvent(_EventAddPrescriptionInfusion)
    async *_mapEventAddPrescriptionInfusion(/*event: _EventAddPrescriptionInfusion*/): AsyncGenerator<State> {
        const result: {
            formData: PrescriptionInfusionForm;
            extraParameter?: { patient?: Patient; psychotropicNarcoticEmployee?: Patient };
        } = await ABCNavigator.navigateToPage(
            <NewMedicineAddPage
                type={MedicineAddType.infusion}
                title={"输/注处方"}
                outpatientDetail={this.innerState.detailData}
                showAstCheck={this.innerState.outpatientConfig?.astPrescription?.showWesternSwitch}
                psychotropicNarcoticType={this.innerState.showDefaultPediatricTag ? PsychotropicNarcoticTypeEnum.ERKE : undefined} // 年龄小于14岁的患者默认标签勾选儿科}
                allowAntibiotic={this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                    this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                )}
            />,
            { transitionType: TransitionType.inFromBottom }
        );
        const info = result?.formData;
        if (!info) return;

        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        if (!info || !info.prescriptionFormItems?.length) return;
        if (!this.innerState.detailData?.prescriptionInfusionForms) this.innerState.detailData!.prescriptionInfusionForms = [];
        this.innerState.detailData?.prescriptionInfusionForms?.push(info);

        this._removeEmptyForm();
        this._updatePatientAndAgentInfo(result?.extraParameter);
        this.hasChanged = true;
        this._aiVerify.next();
        this.innerState.detailData!.expectedTotalPrice = null;
        yield this.innerState;

        //滚动
        this.innerState.focusItemKey = info.scrollKey;
        yield ScrollToFocusItemState.fromState(this.innerState);
    }

    @actionEvent(_EventAddPrescriptionExternal)
    async *_mapEventAddPrescriptionExternal(): AsyncGenerator<State> {
        const form: PrescriptionExternalForm = await ABCNavigator.navigateToPage(
            <MedicineExternalAddPage
                outpatientDetail={this.innerState.detailData}
                psychotropicNarcoticType={this.innerState.showDefaultPediatricTag ? PsychotropicNarcoticTypeEnum.ERKE : undefined} // 年龄小于14岁的患者默认标签勾选儿科
                allowAntibiotic={this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                    this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                )}
            />,
            { transitionType: TransitionType.inFromBottom }
        );
        if (!form || !form.prescriptionFormItems?.length) return;
        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();
        if (!this.innerState.detailData?.prescriptionExternalForms) this.innerState.detailData!.prescriptionExternalForms = [];
        this.innerState.detailData?.prescriptionExternalForms?.push(form);
        this._removeEmptyForm();
        this.hasChanged = true;
        this._aiVerify.next();
        this.innerState.detailData!.expectedTotalPrice = null;
        yield this.innerState;

        //滚动
        this.innerState.focusItemKey = form.scrollKey;
        yield ScrollToFocusItemState.fromState(this.innerState);
    }

    @actionEvent(_EventClearPatient)
    async *_mapEventClearPatient(/*event:_EventClearPatient*/): AsyncGenerator<State> {
        if (this.innerState.detailData) {
            this.innerState.detailData.patient = new Patient();
            this.innerState.detailData.diagnoseCount = 0;
            this.hasChanged = true;
            this.update();
        }
    }

    @actionEvent(_EventDeletePrescription)
    async *_mapEventDeletePrescription(event: _EventDeletePrescription): AsyncGenerator<State> {
        const select = await showQueryDialog("确认删除该处方？", "");
        if (select != DialogIndex.positive) return;

        if (event.prescriptionForm instanceof PrescriptionChineseForm) {
            this.innerState.detailData!.prescriptionChineseForms?.map((item, index, self) => {
                if (item == event.prescriptionForm) {
                    self.splice(index, 1);
                }
            });
        } else if (event.prescriptionForm instanceof PrescriptionInfusionForm) {
            this.innerState.detailData!.prescriptionInfusionForms?.map((item, index, self) => {
                if (item == event.prescriptionForm) {
                    self.splice(index, 1);
                }
            });
        } else if (event.prescriptionForm instanceof PrescriptionExternalForm) {
            this.innerState.detailData!.prescriptionExternalForms?.map((item, index, self) => {
                if (item == event.prescriptionForm) {
                    self.splice(index, 1);
                }
            });
        } else if (event.prescriptionForm instanceof PrescriptionWesternForm) {
            this.innerState.detailData!.prescriptionWesternForms?.map((item, index, self) => {
                if (item == event.prescriptionForm) {
                    self.splice(index, 1);
                }
            });
        }
        // 判断除了当前处方，还有其他精麻处方吗，如果有，则不删除代办人信息，如果没有，则需要删除代办人信息
        let hasOtherPsychotropicNarcotic = false;
        hasOtherPsychotropicNarcotic = !!this.innerState.detailData?.prescriptionInfusionForms?.some(
            (t) => t.isNeedPsychotropicNarcoticEmployee
        );
        hasOtherPsychotropicNarcotic = !!this.innerState.detailData?.prescriptionWesternForms?.some(
            (t) => t.isNeedPsychotropicNarcoticEmployee
        );
        if (!hasOtherPsychotropicNarcotic && !!this.innerState.detailData?.psychotropicNarcoticEmployee) {
            this.innerState.detailData!.psychotropicNarcoticEmployee = undefined;
        }

        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        this.hasChanged = true;
        this._aiVerify.next();
        this.innerState.detailData!.expectedTotalPrice = null;
        this.update();
    }

    private _updatePatientAndAgentInfo(info?: { patient?: Patient; psychotropicNarcoticEmployee?: Patient }): void {
        if (info?.patient) {
            this.innerState.detailData!.patient = JsonMapper.deserialize(Patient, info.patient);
        }
        if (info?.psychotropicNarcoticEmployee) {
            this.innerState.detailData!.psychotropicNarcoticEmployee = JsonMapper.deserialize(PsychotropicNarcoticEmployee, {
                ...info?.psychotropicNarcoticEmployee,
                patient: info?.patient,
            });
        }
    }

    @actionEvent(_EventModifyWesternMedicine)
    async *_mapEventModifyWesternMedicine(event: _EventModifyWesternMedicine): AsyncGenerator<State> {
        if (!this.innerState.isEditing) return;
        if (!event.prescriptionWesternForm.prescriptionFormItems?.length) return;

        const { prescriptionWesternForm } = event;

        const oldPrescriptionWesternForm = cloneDeep(prescriptionWesternForm);

        oldPrescriptionWesternForm.prescriptionFormItems?.forEach((item) => {
            if (!_.isNumber(item.unitCount)) {
                MedicineAddPageUtils.computeMedicineSingleTotalPrice(item);
            }
        });
        const result: {
            formData: PrescriptionWesternForm;
            extraParameter?: { patient?: Patient; psychotropicNarcoticEmployee?: Patient };
        } = await ABCNavigator.navigateToPage(
            <NewMedicineAddPage
                type={MedicineAddType.western}
                title={"成药处方"}
                formDetail={oldPrescriptionWesternForm}
                outpatientDetail={this.innerState.detailData}
                showAstCheck={this.innerState.outpatientConfig?.astPrescription?.showWesternSwitch}
                allowAntibiotic={this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                    this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                )}
                disableAddPrescription={this.innerState.disableAddPrescription}
            />,
            { transitionType: TransitionType.inFromBottom }
        );
        const input = result?.formData;
        if (!input) return;

        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        //有药品信息更新时，去掉totalPrice，防止遍历计算总价失效
        input.totalPrice = undefined;
        //替换相同处方
        const index = this.innerState.detailData?.prescriptionWesternForms?.findIndex((form) => form.keyId == input.keyId) ?? -1;
        if (index >= 0) {
            this.innerState.detailData!.prescriptionWesternForms![index] = input;
        } else {
            this.innerState.detailData?.prescriptionWesternForms?.push(input);
        }
        this._updatePatientAndAgentInfo(result?.extraParameter);
        this._removeEmptyForm();

        this._aiVerify.next();
        this.hasChanged = true;
        this.innerState.detailData!.expectedTotalPrice = null;

        this.update();
    }

    /**
     * 删除门诊单
     */
    @actionEvent(_EventDeleteOutpatient)
    async *_mapEventDeleteOutpatient(/*event: _EventDeleteOutpatient*/): AsyncGenerator<State> {
        const select = await showQueryDialog("确认删除本次接诊？", "删除后不能恢复");
        if (select != DialogIndex.positive) return;

        const draftId = this.innerState.detailData?.draftId;
        const orderId = this.innerState.detailData?.id;

        //删除本地草稿
        if (draftId) await this._draftManager().removeDraft(draftId);
        if (orderId) await OutpatientAgent.deleteOnlineDraft(orderId, 0);

        this.hasChanged = false;
        ABCNavigator.pop();
    }

    @actionEvent(_EventModifyInfusionMedicine)
    async *_mapEventModifyInfusionMedicine(event: _EventModifyInfusionMedicine): AsyncGenerator<State> {
        if (!this.innerState.isEditing) return;

        const { prescriptionInfusionForm } = event;
        if (!prescriptionInfusionForm.prescriptionFormItems?.length) return;
        const oldPrescriptionInfusionForm = cloneDeep(prescriptionInfusionForm);
        oldPrescriptionInfusionForm.prescriptionFormItems?.forEach((item) => {
            if (!_.isNumber(item.unitCount)) {
                MedicineAddPageUtils.computeMedicineSingleTotalPrice(item);
            }
        });
        const result: {
            formData: PrescriptionInfusionForm;
            extraParameter?: { patient?: Patient; psychotropicNarcoticEmployee?: Patient };
        } = await ABCNavigator.navigateToPage(
            <NewMedicineAddPage
                type={MedicineAddType.infusion}
                title={"输/注处方"}
                formDetail={oldPrescriptionInfusionForm}
                outpatientDetail={this.innerState.detailData}
                showAstCheck={this.innerState.outpatientConfig?.astPrescription?.showWesternSwitch}
                psychotropicNarcoticType={this.innerState.showDefaultPediatricTag ? PsychotropicNarcoticTypeEnum.ERKE : undefined} // 年龄小于14岁的患者默认标签勾选儿科}
                allowAntibiotic={this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                    this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                )}
                disableAddPrescription={this.innerState.disableAddPrescription}
            />,
            { transitionType: TransitionType.inFromBottom }
        );
        const input = result?.formData;
        if (!input) return;

        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        //有药品信息更新时，去掉totalPrice，防止遍历计算总价失效
        input.totalPrice = undefined;
        //替换相同处方
        const index = this.innerState.detailData?.prescriptionInfusionForms?.findIndex((form) => form.keyId == input.keyId) ?? -1;
        if (index >= 0) {
            this.innerState.detailData!.prescriptionInfusionForms![index] = input;
        } else {
            this.innerState.detailData?.prescriptionInfusionForms?.push(input);
        }
        this._updatePatientAndAgentInfo(result?.extraParameter);

        this._removeEmptyForm();
        this.hasChanged = true;
        this._aiVerify.next();
        this.innerState.detailData!.expectedTotalPrice = null;
        this.update();
    }

    private async _getCurrentDoctorDetailInfo(): Promise<void> {
        const { enableDoctorPractice, detailData: { doctorId } = {} } = this.innerState;
        // 重新获取医生职称
        if (enableDoctorPractice && !!doctorId) {
            const result = await OutpatientAgent.getClinicEmployeesList(doctorId).catchIgnore();
            if (result) {
                this.innerState.currentDoctorDetailInfo = result;
            }
        }
    }

    //清空空处方单
    _removeEmptyForm(): void {
        _.remove(this.innerState.detailData?.productForms ?? [], (item) => ABCUtils.isEmpty(item.productFormItems));
        _.remove(this.innerState.detailData?.prescriptionInfusionForms ?? [], (item) => ABCUtils.isEmpty(item.prescriptionFormItems));
        _.remove(this.innerState.detailData?.prescriptionChineseForms ?? [], (item) => ABCUtils.isEmpty(item.prescriptionFormItems));
        _.remove(this.innerState.detailData?.prescriptionWesternForms ?? [], (item) => ABCUtils.isEmpty(item.prescriptionFormItems));
        _.remove(this.innerState.detailData?.prescriptionExternalForms ?? [], (item) => ABCUtils.isEmpty(item.prescriptionFormItems));
    }

    async _loadDataFromNetwork(): Promise<void> {
        this.innerState.loadError = null;
        this.innerState.loading = true;
        this.update();

        let orderDetail: OutpatientInvoiceDetail; // OutpatientInvoiceDetail;

        try {
            orderDetail = await OutpatientAgent.getOutpatientInvoiceDetail(this.outpatientId ?? "");
            if (!!this.innerState.detailData?.isOnline) {
                this.innerState.detailData = Object.assign(this.innerState.detailData, orderDetail);
            } else {
                this.innerState.detailData = orderDetail;
            }
            this.innerState.detailData.medicalRecord = this.innerState.detailData.medicalRecord ?? new MedicalRecord();
            if (orderDetail.isWaitVisit) this.innerState.isEditing = true;
            this.innerState.loading = false;
            this.update();
            this.hasChanged = false;

            // this._initDepartmentAndDoctor();
        } catch (error) {
            if (error && error.code === 401) {
                return; // AbcNetDelegate中已做处理 这里选择忽略
            }
            LogUtils.d("loadOutpatientDraft error = " + errorToStr(error));
            this.innerState.loading = false;
            this.innerState.loadError = error;
            this.update();
        }
    }

    @actionEvent(_EventModifyChineseMedicine)
    async *_mapEventModifyChineseMedicine(event: _EventModifyChineseMedicine): AsyncGenerator<State> {
        if (!ABCUtils.isEmpty(event.prescriptionChineseForm.prescriptionFormItems)) {
            for (const formItem of event.prescriptionChineseForm.prescriptionFormItems!) {
                formItem.keyId = formItem?.keyId || UUIDGen.generate();
                const medicine = formItem.goodsInfo;
                medicine.keyId = formItem.keyId;
            }
        }

        const result: {
            formData: PrescriptionChineseForm;
            extraParameter?: { patient?: Patient; psychotropicNarcoticEmployee?: Patient };
        } = await ABCNavigator.navigateToPage(
            <NewMedicineAddPage
                type={MedicineAddType.chinese}
                title={"中药处方"}
                formDetail={event.prescriptionChineseForm}
                outpatientDetail={this.innerState.detailData}
                psychotropicNarcoticType={event.prescriptionChineseForm.psychotropicNarcoticType}
                isOpenVirtualPharmacy={this.innerState.isOpenVirtualPharmacy}
                allowAntibiotic={this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                    this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                )}
                disableAddPrescription={this.innerState.disableAddPrescription}
            />,
            { transitionType: TransitionType.inFromBottom }
        );
        const info = result?.formData;

        if (!info) return;

        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        //有药品信息更新时，去掉totalPrice，防止遍历计算总价失效
        info.totalPrice = undefined;

        //药房切换时清空议价信息
        if (OutpatientUtils.checkPrescriptionFormHasExpected(event.prescriptionChineseForm)) {
            OutpatientUtils.clearPrescriptionFormExpected(event.prescriptionChineseForm);
            await showConfirmDialog("提示", "处方变动导致议价清空，请重新确认价格");
        }

        event.prescriptionChineseForm = info;
        // 解决没有赋值到detailData对应处方问题，替换相同处方
        if (this.innerState.detailData?.prescriptionChineseForms?.length) {
            this.innerState.detailData.prescriptionChineseForms.forEach((item, index) => {
                if (item.keyId === info.keyId) {
                    this.innerState.detailData!.prescriptionChineseForms![index] = info;
                }
            });
        }

        this._removeEmptyForm();
        this._updatePatientAndAgentInfo(result?.extraParameter);

        if (event.prescriptionChineseForm.pharmacyType == PharmacyType.air) {
            //空中药房信息在修改回来后拉取下供应商信息（获取加工费）
            this._loadVendorTrigger.next(event.prescriptionChineseForm);
        }

        this.hasChanged = true;
        this._aiVerify.next();
        this._calculateChineseProcessingFeeTrigger.next();
        this.innerState.detailData!.expectedTotalPrice = null;
        this.update();
    }

    @actionEvent(_EventModifyExternalMedicine)
    async *_mapEventModifyExternalMedicine(event: _EventModifyExternalMedicine): AsyncGenerator<State> {
        if (!this.innerState.isEditing) return;
        event.prescriptionExternalForm.keyId = event.prescriptionExternalForm?.keyId || UUIDGen.generate();
        event.prescriptionExternalForm?.prescriptionFormItems?.forEach((formItem) => {
            formItem.keyId = formItem.keyId || UUIDGen.generate();
            formItem.goodsInfo.keyId = formItem.goodsInfo.keyId ?? formItem.keyId;
            formItem.dosageUnit = isEmpty(formItem.dosageUnit) ? "次" : formItem.dosageUnit;
            if (!isNumber(formItem.unitCount)) {
                MedicineAddPageUtils.computeMedicineUnitCount(formItem);
            }
        });
        const form: PrescriptionExternalForm = await ABCNavigator.navigateToPage(
            <MedicineExternalAddPage
                formDetail={event.prescriptionExternalForm}
                outpatientDetail={this.innerState.detailData}
                allowAntibiotic={this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
                    this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
                )}
                disableAddPrescription={this.innerState.disableAddPrescription}
            />,
            { transitionType: TransitionType.inFromBottom }
        );
        if (!form) return;
        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        //有药品信息更新时，去掉totalPrice，防止遍历计算总价失效
        event.prescriptionExternalForm.totalPrice = undefined;
        event.prescriptionExternalForm.prescriptionFormItems = form?.prescriptionFormItems;
        event.prescriptionExternalForm.usageType = form?.usageType;
        event.prescriptionExternalForm.usageSubType = form?.usageSubType;
        event.prescriptionExternalForm.psychotropicNarcoticType = form.psychotropicNarcoticType; // 处方标签

        this._removeEmptyForm();
        this.hasChanged = true;
        this._aiVerify.next();
        this.innerState.detailData!.expectedTotalPrice = null;
        this.update();
    }

    /**
     * 病例附件-添加图片
     */
    requestAddAttachment(): void {
        if (!this.innerState.allowEditMedicalRecord) return;
        this.dispatch(new _EventAddAttachment());
    }

    // 语音病历
    requestAddVoiceRecord(): void {
        if (!this.innerState.allowEditMedicalRecord) return;
        this.dispatch(new _EventAddVoiceRecord());
    }

    private combinedAirPharmacyPrescription(airPharmacyList?: PrescriptionChineseForm[]): AirPharmacyCalculateForm[] | undefined {
        if (!airPharmacyList?.length) {
            return undefined;
        }

        return airPharmacyList.map((item) => {
            const isPiece = ChineseMedicineSpecType.chinesePiece === ChineseMedicineSpecType.typeFromName(item.specification ?? "");
            const { deliveryInfo, keyId, usageScopeId, vendorId } = item;
            const { dailyDosage, freq, usage, usageLevel } = item;
            const goodsTypeId = isPiece ? ChineseGoodType.chinesePiece : ChineseGoodType.chineseGranule;
            const items = item.prescriptionFormItems?.map((sub) => {
                const { displayName: name, productId, unit, unitCount, unitPrice } = sub;
                const doseCount = item.doseCount;
                return { name, productId, unit, unitCount, unitPrice, doseCount };
            });
            const sort = item.sort;

            return {
                deliveryInfo,
                keyId,
                usageScopeId,
                vendorId,
                usageInfo: { dailyDosage, freq, usage, usageLevel },
                goodsTypeId,
                items,
                sort,
            };
        });
    }

    // 费用预览
    @actionEvent(_EventChargePreview)
    async *_mapEventChargePreview(/*event: _EventChargePreview*/): AsyncGenerator<State> {
        this.innerState.detailData?.fillKeyIds();

        const checkResult = new ValueHolder<boolean>();
        const resultState = await this._validateForm({
            checkResult: checkResult,
            onlyCheckMedicine: true,
        });
        if (resultState) yield resultState;

        if (!checkResult.value) {
            return;
        }

        //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
        this.lockPatientOrder();

        const result = await createOutpatientSingleBargainDialog(
            this.innerState.detailData!,
            this.innerState.hospitalDetail?.hospitalSheetId
        );
        if (result) {
            // //判断挂号费状态进行，判断是否进行覆盖
            if (!OutpatientUtils.canEditWithChargeStatus(this.innerState.detailData?.registrationFeeStatus)) {
                result.registrationFeeStatus = this.innerState.detailData?.registrationFeeStatus;
                result.registrationFee = this.innerState.detailData?.registrationFee;
            }

            this.innerState.detailData = result;
            this.hasChanged = true;
            this.update();
        }
    }

    async _validateForm(options: {
        checkResult: ValueHolder<boolean>;
        onlyCheckMedicine?: boolean;
    }): Promise<ScrollToFocusItemState | undefined> {
        const outpatientConfig = this.innerState.outpatientConfig;
        const { checkResult } = options;
        const { onlyCheckMedicine = false } = options;
        checkResult.value = true;
        const requiredConfig = this.innerState.outpatientRequiredConfig?.outpatient;
        const setErrorInfo = (group: any, item: any, dialogText?: string, toastTest?: string) => {
            this.innerState.showErrorHint = true;
            checkResult.value = false;
            if (!userCenter.clinic?.notNeedVerifyMedicalClinic) {
                if (dialogText) {
                    showConfirmDialog(dialogText, "");
                } else {
                    Toast.show(`${toastTest ?? "请完善必填项"}`, { warning: true });
                }
            }
            if (!item || item instanceof Patient) {
                this.innerState.focusItemKey = OutpatientContentRefKey.patient;
            } else if (item instanceof MedicalRecord && !userCenter.clinic?.notNeedVerifyMedicalClinic) {
                if (!item.chiefComplaint) {
                    this.innerState.focusItemKey = OutpatientContentRefKey.chiefComplaint;
                } else if (requiredConfig?.requiredPastHistory && !item.pastHistory?.length) {
                    this.innerState.focusItemKey = OutpatientContentRefKey.pastHistory;
                } else if (requiredConfig?.requiredPresentHistory && !item.presentHistory?.length) {
                    this.innerState.focusItemKey = OutpatientContentRefKey.presentHistory;
                } else if (!item.diagnosis || !item.diagnosisInfos?.length) {
                    this.innerState.focusItemKey = OutpatientContentRefKey.diagnosis;
                } else if (requiredConfig?.requiredEpidemiologicalHistory && !item.epidemiologicalHistory) {
                    this.innerState.focusItemKey = OutpatientContentRefKey.epidemiologicalHistory;
                }
            } else if (group instanceof PrescriptionProductForm) {
                this.innerState.focusItemKey = item.goodsInfo.scrollKey;
            } else if (group instanceof PrescriptionExternalForm) {
                this.innerState.focusItemKey = item.goodsInfo.scrollKey;
            } else if (group instanceof PrescriptionChineseForm) {
                // this.innerState.focusItemKey = item.goodsInfo.scrollKey;
                this.innerState.focusItemKey = group.scrollKey;
            } else if (group instanceof PrescriptionInfusionForm) {
                this.innerState.focusItemKey = item.goodsInfo.scrollKey;
            } else if (group instanceof PrescriptionWesternForm) {
                this.innerState.focusItemKey = item.goodsInfo.scrollKey;
            }
            return ScrollToFocusItemState.fromState(this.innerState);
        };

        if (!onlyCheckMedicine) {
            if (
                _.isNil(this.innerState.detailData!.patient?.name) ||
                !this.innerState.detailData!.patient?.age?.displayAge ||
                _.isNil(this.innerState.detailData!.doctorName)
            ) {
                return setErrorInfo(null, this.innerState.detailData!.patient);
            }
            // 完诊后修改病历设置,如果是禁止修改病历，则不校验病历的必填项
            if (!userCenter.clinic?.notNeedVerifyMedicalClinic && !this.innerState.disableUpdateMedicalRecord) {
                if (!this.innerState.detailData!.medicalRecord?.chiefComplaint || !this.innerState.detailData!.medicalRecord?.diagnosis) {
                    return setErrorInfo(null, this.innerState.detailData!.medicalRecord);
                }

                for (const _key in this.innerState.detailData!.medicalRecord) {
                    if (
                        outpatientConfig?.getCurrentOnlineMedicalRecordConfig(_key)?.required &&
                        // @ts-ignore
                        !this.innerState.detailData!.medicalRecord[_key]
                    ) {
                        if (_key === "obstetricalHistory" && this.detailData?.patient?.sex != "女") {
                            continue;
                        }
                        // // 如果【望闻切诊】是必填项，不再需要校验该项，因为将【望闻切诊】调整为【舌象】【脉象】，只兼容以前【望闻切诊】的数据，不再争对这个字段进行校验
                        // if (_key === "chineseExamination") {
                        //     continue;
                        // }
                        // 如果【舌象】【脉象】是必填项，不再需要校验该项，因为将【舌象】【脉象】调整为【望闻切诊】，只兼容以前【舌象】【脉象】的数据，不再争对这个字段进行校验
                        if (_key === "tongue" || _key === "pulse") {
                            continue;
                        }
                        return setErrorInfo(null, this.innerState.detailData!.medicalRecord);
                    }
                }

                //社保相关
                // 病历诊断需要调整一下，读取extendDiagnosisInfos中的字段，然后采用本地用的特殊符号进行拼接，最后复制到medicalRecord.diagnosis
                // 解决：APP复制病历，需要再编辑选择一次诊断，直接开出会提示诊断不对（因为diagnosis中的分隔符与本地的分隔符不一致，导致识别不了）
                let diagnosisList: string[] = [];
                if (!!this.innerState.detailData!.medicalRecord?.extendDiagnosisInfos?.length) {
                    diagnosisList =
                        this.innerState.detailData?.medicalRecord.extendDiagnosisInfos
                            .flatMap((item) => item.value ?? [])
                            .filter((v) => !!v?.name)
                            .map((v) => v.name) ?? [];
                }
                this.innerState.detailData!.medicalRecord!.diagnosisInfos = await ShebaoAgent.getDiseasesQueryList(
                    diagnosisList.map((item) => {
                        return { name: item, code: "", hint: "", type: 0 };
                    }) ?? []
                )
                    .then((rsp) =>
                        rsp.diagnosisInfos?.length
                            ? rsp.diagnosisInfos
                            : diagnosisList.map((item) => {
                                  return { name: item, code: "", hint: "", type: 0 };
                              })
                    )
                    .catchIgnore();
            }
        }

        if (this.innerState.detailData?.productForms != null) {
            for (const form of this.innerState.detailData.productForms) {
                if (ABCUtils.isEmpty(form.productFormItems) || (form.chargeStatus && form.chargeStatus != ChargeFormItemStatus.unCharged))
                    continue;
                const disableComposeMedicineName: string[] = [];
                for (const formItem of form.productFormItems ?? []) {
                    const medicine = formItem.goodsInfo;
                    if (!_.isNumber(formItem.unitCount) || !formItem?.unit) {
                        return setErrorInfo(form, formItem);
                    }
                    if (medicine.isPackage) {
                        const noStockMedicineName: string[] = [];
                        for (const composeItem of medicine.children ?? []) {
                            if (!this.innerState.canCheckWithoutStock) {
                                const composeUseDismounting = composeItem.composeUseDismounting == 1;
                                const itemUnitCount = formItem?.unitCount;
                                const stockInfo = composeItem.stockInfo(
                                    composeUseDismounting ? composeItem.pieceUnit : composeItem.packageUnit,
                                    composeUseDismounting ? composeItem.composePieceCount : composeItem.composePackageCount,
                                    itemUnitCount,
                                    undefined,
                                    undefined,
                                    ""
                                );

                                if (!stockInfo.stockEnough) {
                                    noStockMedicineName.push(composeItem.displayName);
                                }
                            }

                            if (!!composeItem.disableSell) {
                                disableComposeMedicineName.push(composeItem.displayName);
                            }
                        }
                        if (!_.isEmpty(noStockMedicineName)) {
                            return setErrorInfo(
                                form,
                                formItem,
                                `${medicine.displayName} 套餐内“${noStockMedicineName.join("，")}”库存不足，不可开出，请换用其他替代套餐。`
                            );
                        }

                        if (!_.isEmpty(disableComposeMedicineName)) {
                            return setErrorInfo(
                                form,
                                formItem,
                                `${medicine.displayName} 套餐内“${disableComposeMedicineName.join(
                                    "，"
                                )}” 已停用不可销售，请换用其他替代套餐。`
                            );
                        }
                    } else {
                        if (!!medicine.disableSell) {
                            disableComposeMedicineName.push(medicine.displayName);
                        }
                        if (!this.innerState.canCheckWithoutStock) {
                            if (!medicine.stockInfo(formItem.unit, formItem.unitCount, 1).stockEnough) {
                                return setErrorInfo(form, formItem, `${medicine.displayName} 库存不足，不可开出，请换用其他替代项目。`);
                            }
                        }
                    }
                }
                if (!_.isEmpty(disableComposeMedicineName)) {
                    return setErrorInfo(
                        form,
                        ABCUtils.first(form.productFormItems ?? []),
                        `${disableComposeMedicineName.join("，")} 已停用不可销售，请换用其他替代套餐。`
                    );
                }
            }
        }

        if (this.innerState.detailData!.prescriptionWesternForms != null) {
            for (const form of this.innerState.detailData!.prescriptionWesternForms) {
                if (
                    ABCUtils.isEmpty(form.prescriptionFormItems) ||
                    (form.chargeStatus && form.chargeStatus != ChargeFormItemStatus.unCharged)
                )
                    continue;
                const noStockMedicineName: string[] = [];
                const disableMedicineName: string[] = [];
                for (const formItem of form.prescriptionFormItems ?? []) {
                    if (
                        !Boolean(formItem.usage) ||
                        !Boolean(formItem.freq) ||
                        (formItem.dosageUnit == "适量" ? false : !Boolean(formItem.dosage)) ||
                        !Boolean(formItem.dosageUnit) ||
                        !_.isNumber(formItem.unitCount) ||
                        !Boolean(formItem.unit)
                    ) {
                        return setErrorInfo(form, formItem);
                    }
                    if (formItem.specialRequirement == "【自备】") {
                        formItem.sourceItemType = DispensingFormItemsSourceItemType.noCharge;
                        formItem.chargeType = DispensingFormItemsSourceItemType.noCharge;
                    }
                    if (!this.innerState.canCheckWithoutStock) {
                        const medicine = formItem.goodsInfo;
                        //当前处方选了自备后，都可以开出（包括没有库存不足的时候）
                        if (
                            !medicine.stockInfo(formItem.unit, formItem.unitCount, 1).stockEnough &&
                            formItem.chargeType != DispensingFormItemsSourceItemType.noCharge
                        ) {
                            noStockMedicineName.push(medicine.displayName);
                        }
                    }
                    if (!!formItem.goodsInfo.disableSell) {
                        disableMedicineName.push(formItem.displayName);
                    }
                }
                if (!_.isEmpty(disableMedicineName)) {
                    return setErrorInfo(
                        form,
                        ABCUtils.first(form.prescriptionFormItems ?? []),
                        `${disableMedicineName.join("，")} 已停用不可销售，请换用其他替代药品。`
                    );
                }
                if (!_.isEmpty(noStockMedicineName)) {
                    return setErrorInfo(
                        form,
                        ABCUtils.first(form.prescriptionFormItems ?? []),
                        `${noStockMedicineName.join("，")} 库存不足，不可开出，请换用其他替代药品。`
                    );
                }
            }
        }

        if (this.innerState.detailData!.prescriptionChineseForms != null) {
            for (const form of this.innerState.detailData!.prescriptionChineseForms) {
                if (
                    ABCUtils.isEmpty(form.prescriptionFormItems) ||
                    (form.chargeStatus && form.chargeStatus != ChargeFormItemStatus.unCharged)
                )
                    continue;
                const noStockMedicineName: string[] = [];
                const disableMedicineName: string[] = [];
                for (const formItem of form.prescriptionFormItems ?? []) {
                    if (!formItem.unitCount) {
                        return setErrorInfo(form, formItem);
                    }
                    if (!this.innerState.canCheckWithoutStock) {
                        const medicine = formItem.goodsInfo;
                        if (
                            !medicine.stockInfo(
                                formItem.unit,
                                formItem.unitCount,
                                form.doseCount,
                                undefined,
                                undefined,
                                undefined,
                                form.pharmacyType
                            ).stockEnough
                        ) {
                            noStockMedicineName.push(medicine.displayName);
                        }
                    }

                    if (!!formItem.goodsInfo.disableSell) {
                        disableMedicineName.push(formItem.displayName);
                    }
                    if (formItem.specialRequirement == "【自备】") {
                        formItem.sourceItemType = DispensingFormItemsSourceItemType.noCharge;
                        formItem.chargeType = DispensingFormItemsSourceItemType.noCharge;
                    }
                }

                if (!Boolean(form.doseCount) || !form.usage) {
                    return setErrorInfo(form, ABCUtils.first(form.prescriptionFormItems ?? []));
                }

                //空中药房-饮片-自煎-剂数小于3
                const { pharmacyType, medicineStateScopeId, doseCount, specification, vendor } = form;
                const sortIndex = this.innerState.detailData!.prescriptionChineseForms?.findIndex((t) => t == form);
                if (
                    pharmacyType == PharmacyType.air &&
                    ChineseMedicineSpecType.typeFromName(specification ?? "") == ChineseMedicineSpecType.chinesePiece &&
                    medicineStateScopeId == MedicineScopeId.daiJian.toString() &&
                    (doseCount ?? 0) < 3
                ) {
                    return setErrorInfo(
                        form,
                        ABCUtils.first(form.prescriptionFormItems ?? []),
                        undefined,
                        `${sortIndex > -1 ? `中药处方${ABCUtils.toChineseNum(sortIndex + 1)}:` : ""}3剂起煎`
                    );
                }
                //如果有最小值起煎量
                if (
                    pharmacyType == PharmacyType.air &&
                    vendor?.checked &&
                    vendor.minimum &&
                    form.computePrescriptionDosageWeight() * (doseCount ?? 0) < vendor.minimum
                ) {
                    return setErrorInfo(
                        form,
                        ABCUtils.first(form.prescriptionFormItems ?? []),
                        undefined,
                        `${sortIndex > -1 ? `中药处方${ABCUtils.toChineseNum(sortIndex + 1)}:` : ""}起做量${vendor.minimum}g`
                    );
                }
                //空中药房-颗粒剂-固定袋数
                //颗粒剂开方剂数限制
                if (
                    pharmacyType == PharmacyType.air &&
                    vendor?.doseCountLimit == 1 &&
                    ChineseMedicineSpecType.typeFromName(specification ?? "") == ChineseMedicineSpecType.chineseGranule &&
                    medicineStateScopeId === MedicineScopeId.keLi
                ) {
                    if ((doseCount ?? 0) % 2 !== 0) {
                        return setErrorInfo(
                            form,
                            ABCUtils.first(form.prescriptionFormItems ?? []),
                            undefined,
                            `${sortIndex > -1 ? `中药处方${ABCUtils.toChineseNum(sortIndex + 1)}:` : ""}${
                                vendor?.pharmacyName ?? vendor?.vendorName ?? ""
                            }需剂数为双数才可调剂`
                        );
                    }
                }
                // 空中药房-代煎-每日剂量、频率、服用量必填
                if (pharmacyType == PharmacyType.air && medicineStateScopeId === MedicineScopeId.daiJian) {
                    const isSpecialUsages = ["制膏", "制丸", "打粉"].indexOf(form?.usage ?? "") > -1;
                    if (!isSpecialUsages && !form?.dailyDosage) {
                        return setErrorInfo(
                            form,
                            ABCUtils.first(form.prescriptionFormItems ?? []),
                            `${sortIndex > -1 ? `中药处方${ABCUtils.toChineseNum(sortIndex + 1)}:` : ""}${
                                vendor?.pharmacyName ?? vendor?.vendorName ?? ""
                            }用法用量中每日剂量必填`
                        );
                    }
                    if (!form?.freq) {
                        return setErrorInfo(
                            form,
                            ABCUtils.first(form.prescriptionFormItems ?? []),
                            `${sortIndex > -1 ? `中药处方${ABCUtils.toChineseNum(sortIndex + 1)}:` : ""}${
                                vendor?.pharmacyName ?? vendor?.vendorName ?? ""
                            }用法用量中频率必填`
                        );
                    }
                    if (!form?.usageLevel) {
                        return setErrorInfo(
                            form,
                            ABCUtils.first(form.prescriptionFormItems ?? []),
                            `${sortIndex > -1 ? `中药处方${ABCUtils.toChineseNum(sortIndex + 1)}:` : ""}${
                                vendor?.pharmacyName ?? vendor?.vendorName ?? ""
                            }用法用量中服用量必填`
                        );
                    }
                    if (!form?.processBagUnitCount || !form?.totalProcessCount) {
                        return setErrorInfo(
                            form,
                            ABCUtils.first(form.prescriptionFormItems ?? []),
                            `${sortIndex > -1 ? `中药处方${ABCUtils.toChineseNum(sortIndex + 1)}:` : ""}${
                                vendor?.pharmacyName ?? vendor?.vendorName ?? ""
                            }加工袋数必填`
                        );
                    }
                }

                /**
                 * 虚拟药房校验 ？？？
                 *
                 */

                if (!_.isEmpty(disableMedicineName)) {
                    return setErrorInfo(
                        form,
                        ABCUtils.first(form.prescriptionFormItems ?? []),
                        `${disableMedicineName.join("，")} 已停用不可销售，请换用其他替代药品。`
                    );
                }

                if (!_.isEmpty(noStockMedicineName)) {
                    return setErrorInfo(
                        form,
                        ABCUtils.first(form.prescriptionFormItems ?? []),
                        `${noStockMedicineName.join("，")} 库存不足，不可开出，请换用其他替代药品。`
                    );
                }
                if (!_.isEmpty(disableMedicineName)) {
                    return setErrorInfo(
                        form,
                        ABCUtils.first(form.prescriptionFormItems ?? []),
                        `${disableMedicineName.join("，")} 已停用不可销售，请换用其他替代药品。`
                    );
                }
            }
        }

        if (this.innerState.detailData!.prescriptionInfusionForms != null) {
            for (const form of this.innerState.detailData!.prescriptionInfusionForms) {
                if (
                    ABCUtils.isEmpty(form.prescriptionFormItems) ||
                    (form.chargeStatus && form.chargeStatus != ChargeFormItemStatus.unCharged)
                )
                    continue;
                const noStockMedicineName: string[] = [];
                const disableMedicineName: string[] = [];
                for (const formItem of form.prescriptionFormItems ?? []) {
                    if (!formItem.dosage || !formItem.dosageUnit || !_.isNumber(formItem.unitCount) || !formItem.unit || !formItem.days) {
                        return setErrorInfo(form, formItem);
                    }
                    for (const goodsItem of formItem.externalGoodsItems ?? []) {
                        if (!goodsItem.unitCount && goodsItem.unitCount != 0) {
                            return setErrorInfo(formItem, goodsItem);
                        }
                    }
                    if (formItem.specialRequirement == "【自备】") {
                        formItem.sourceItemType = DispensingFormItemsSourceItemType.noCharge;
                        formItem.chargeType = DispensingFormItemsSourceItemType.noCharge;
                    }

                    if (!this.innerState.canCheckWithoutStock) {
                        const medicine = formItem.goodsInfo;
                        if (
                            !medicine.stockInfo(formItem.unit, formItem.unitCount, 1).stockEnough &&
                            formItem.chargeType != DispensingFormItemsSourceItemType.noCharge
                        ) {
                            noStockMedicineName.push(medicine.displayName);
                        }
                    }
                    if (!!formItem.goodsInfo.disableSell) {
                        disableMedicineName.push(formItem.displayName);
                    }
                }
                if (!_.isEmpty(noStockMedicineName)) {
                    return setErrorInfo(
                        form,
                        ABCUtils.first(form.prescriptionFormItems ?? []),
                        `${noStockMedicineName.join("，")} 库存不足，不可开出，请换用其他替代药品。`
                    );
                }
            }
        }

        if (this.innerState.detailData!.prescriptionExternalForms != null) {
            for (const form of this.innerState.detailData!.prescriptionExternalForms) {
                if (
                    ABCUtils.isEmpty(form.prescriptionFormItems) ||
                    (form.chargeStatus && form.chargeStatus != ChargeFormItemStatus.unCharged)
                )
                    continue;
                for (const formItem of form.prescriptionFormItems ?? []) {
                    if (!Boolean(formItem.dosage)) {
                        return setErrorInfo(form, formItem);
                    }
                }
            }
        }

        if (!onlyCheckMedicine) {
            const saveASTemplate = this.innerState.detailData!.saveASTemplate ?? false;
            if (saveASTemplate && !this.innerState.detailData!.templateName) {
                return setErrorInfo(null, "saveASTemplateKey");
            }
        }
    }

    async _onlyValidateMedicalRecord(options: {
        checkResult: ValueHolder<boolean>;
        onlyCheckMedicine?: boolean;
    }): Promise<ScrollToFocusItemState | undefined> {
        const outpatientConfig = this.innerState.outpatientConfig;
        const { checkResult } = options;
        const { onlyCheckMedicine = false } = options;
        checkResult.value = true;
        const requiredConfig = this.innerState.outpatientRequiredConfig?.outpatient;
        const setErrorInfo = (group: any, item: any, dialogText?: string, toastTest?: string) => {
            this.innerState.showErrorHint = true;
            checkResult.value = false;
            if (!userCenter.clinic?.notNeedVerifyMedicalClinic) {
                if (dialogText) {
                    showConfirmDialog(dialogText, "");
                } else {
                    Toast.show(`${toastTest ?? "请完善必填项"}`, { warning: true });
                }
            }
            if (item instanceof MedicalRecord && !userCenter.clinic?.notNeedVerifyMedicalClinic) {
                if (!item.chiefComplaint) {
                    this.innerState.focusItemKey = OutpatientContentRefKey.chiefComplaint;
                } else if (requiredConfig?.requiredPastHistory && !item.pastHistory?.length) {
                    this.innerState.focusItemKey = OutpatientContentRefKey.pastHistory;
                } else if (requiredConfig?.requiredPresentHistory && !item.presentHistory?.length) {
                    this.innerState.focusItemKey = OutpatientContentRefKey.presentHistory;
                } else if (!item.diagnosis || !item.diagnosisInfos?.length) {
                    this.innerState.focusItemKey = OutpatientContentRefKey.diagnosis;
                } else if (requiredConfig?.requiredEpidemiologicalHistory && !item.epidemiologicalHistory) {
                    this.innerState.focusItemKey = OutpatientContentRefKey.epidemiologicalHistory;
                }
            }
            return ScrollToFocusItemState.fromState(this.innerState);
        };
        if (!onlyCheckMedicine) {
            if (!userCenter.clinic?.notNeedVerifyMedicalClinic) {
                if (!this.innerState.detailData!.medicalRecord?.chiefComplaint || !this.innerState.detailData!.medicalRecord?.diagnosis) {
                    return setErrorInfo(null, this.innerState.detailData!.medicalRecord);
                }

                for (const _key in this.innerState.detailData!.medicalRecord) {
                    if (
                        outpatientConfig?.getCurrentOnlineMedicalRecordConfig(_key)?.required &&
                        // @ts-ignore
                        !this.innerState.detailData!.medicalRecord[_key]
                    ) {
                        if (_key === "obstetricalHistory" && this.detailData?.patient?.sex != "女") {
                            continue;
                        }
                        return setErrorInfo(null, this.innerState.detailData!.medicalRecord);
                    }
                }

                //社保相关
                const diagnosisList = this.innerState.detailData!.medicalRecord?.diagnosis.split(StringUtils.specialComma);
                this.innerState.detailData!.medicalRecord!.diagnosisInfos = await ShebaoAgent.getDiseasesQueryList(
                    diagnosisList.map((item) => {
                        return { name: item, code: "", hint: "", type: 0 };
                    }) ?? []
                )
                    .then((rsp) =>
                        rsp.diagnosisInfos?.length
                            ? rsp.diagnosisInfos
                            : diagnosisList.map((item) => {
                                  return { name: item, code: "", hint: "", type: 0 };
                              })
                    )
                    .catchIgnore();
            }
        }
    }

    /**
     * 获取各个处方当前选中的药房信息，如果没有，则设置为处方对应的默认药房
     * @param initGoodsTypeId ---各个类型的goodsTypeId
     * @param pharmacyNo---存在的药房号
     * @private
     */
    private initPrescriptionPharmacyInfo(initGoodsTypeId?: number, pharmacyNo?: number): PharmacyListConfig | undefined {
        const { pharmacyInfoConfig } = this.innerState;
        const pharmacyInfo = pharmacyInfoConfig?.pharmacyList?.filter((pharmacy) => pharmacy.no == pharmacyNo);
        const pharmacyList = !!pharmacyInfo?.length ? pharmacyInfo : pharmacyInfoConfig?.filterPharmacyListWithGoodsType(initGoodsTypeId);
        const selectPharmacyInfo = !!pharmacyList?.length
            ? pharmacyList[0]
            : pharmacyInfoConfig?.pharmacyList?.find((k) => k.no == PharmacyType.normal);
        return selectPharmacyInfo;
    }

    // 如果有精麻处方，但是代办人没有信息，则需要提示
    private async jingMaPrescriptionRequiredItemsCheck(): Promise<boolean> {
        const detailData = this.innerState.detailData;
        const isWesternExistPsyType = detailData?.prescriptionWesternForms?.some(
            (form) => !!form.psychotropicNarcoticType && form.psychotropicNarcoticType <= PsychotropicNarcoticTypeEnum.DU
        );
        const isInfusionExistPsyType = detailData?.prescriptionInfusionForms?.some(
            (form) => !!form.psychotropicNarcoticType && form.psychotropicNarcoticType <= PsychotropicNarcoticTypeEnum.DU
        );
        const { name, age, sex, mobile, idCard, address } = detailData?.patient || {};
        const isMeetCondition = !name || !age || !sex || !idCard || !mobile || !address?.addressDisplayWithGeo;
        if (isWesternExistPsyType || isInfusionExistPsyType) {
            if (isMeetCondition) {
                const index = await showStateLogoQueryDialog(
                    "",
                    <AbcWithStateLogTitle
                        logo={"image_dlg_fail"}
                        title={"风险提示"}
                        content={"根据处方规范要求，开具精麻处方必须完善患者信息"}
                    />
                );
                if (!index) return false;
                if (!!index) {
                    const result: { patientInfo?: Patient; agentInfo?: Patient } = await ABCNavigator.navigateToPage(
                        <JingMaPrescriptionRequiredPage
                            patientInfo={this.innerState.detailData?.patient}
                            agentInfo={this.innerState.detailData?.psychotropicNarcoticEmployee}
                        />
                    );
                    if (!result) return false;
                    this.innerState.detailData!.patient = JsonMapper.deserialize(Patient, result?.patientInfo);
                    this.innerState.detailData!.psychotropicNarcoticEmployee = JsonMapper.deserialize(PsychotropicNarcoticEmployee, {
                        ...result?.agentInfo,
                        patient: result.patientInfo,
                    });
                }
            }
        }
        return true;
    }

    //清除限制处方内容
    private _removePrescriptionFormItemByPracticeInfo() {
        const _practiceInfo = this.innerState.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(
            this.innerState.currentDoctorDetailInfo?.chainInfo?.practiceInfo
        );
        if (!this.innerState.enableDoctorPractice) return;
        OutpatientUtils.removePrescriptionByPracticeInfoWithForms(this.innerState.detailData?.productForms, _practiceInfo);
        OutpatientUtils.removePrescriptionByPracticeInfoWithForms(this.innerState.detailData?.prescriptionWesternForms, _practiceInfo);
        OutpatientUtils.removePrescriptionByPracticeInfoWithForms(this.innerState.detailData?.prescriptionChineseForms, _practiceInfo);
        OutpatientUtils.removePrescriptionByPracticeInfoWithForms(this.innerState.detailData?.prescriptionInfusionForms, _practiceInfo);
        OutpatientUtils.removePrescriptionByPracticeInfoWithForms(this.innerState.detailData?.prescriptionExternalForms, _practiceInfo);
        this.update();
    }

    @actionEvent(_EventFinishTap)
    async *_mapEventFinishTap(/*event: _EventFinishTap*/): AsyncGenerator<State> {
        const innerState = this.innerState,
            outpatientConfig = innerState.outpatientConfig;
        // clinicShebaoConfig = innerState.clinicShebaoConfig;
        this._removePrescriptionFormItemByPracticeInfo();
        this._removeEmptyForm();
        //判断是否未成年
        if (
            innerState.isBeijingSupervise &&
            innerState.detailData?.patient?.age?.validLimitYear() &&
            !innerState.detailData?.patientGuardian
        ) {
            const result = await GuardianEditDialog.show(innerState.detailData?.patientGuardian);
            if (!result) return;
            innerState.detailData!.patientGuardian = result;
        }
        // 判断精麻毒相关处方-北京限制
        if (innerState.isBeijingSupervise && !!this.innerState.aiVerify?.hasControlledSubstancesRule?.length) {
            return RestrictedDrugView.show(this.innerState.aiVerify?.hasControlledSubstancesRule);
        }

        const checkResult = new ValueHolder<boolean>();
        //判断是否含有流病史
        if (this.innerState.covid19DetectionStatus) {
            const result = await MedicalRecordCheckDialog.show({ medicalRecord: this.innerState.detailData?.medicalRecord });
            if (result == DialogIndex.positive) {
                return;
            }
        }
        const resultState = await this._validateForm({ checkResult: checkResult });
        if (resultState) {
            yield resultState;
            return;
        }
        const isNotPassCheck = await this.jingMaPrescriptionRequiredItemsCheck();
        if (!isNotPassCheck) return;
        if (!checkResult.value) return this.update();
        let formSortIndex = 0;
        let itemSortIndex = 0;
        let itemIndex = 1;
        _.remove(this.innerState.detailData!.productForms ?? [], (item) => ABCUtils.isEmpty(item.productFormItems));
        for (const form of this.innerState.detailData?.productForms ?? []) {
            form.sort = formSortIndex++;
            for (const item of form.productFormItems ?? []) {
                item.sort = itemSortIndex++;
                item.index = itemIndex++;
                const _remark = item?.inputUsages?.specialRequirement?.name;
                const acupText = item?.inputUsages?.acuPoints?.map((item) => item.name).join();
                item.remark =
                    (!!acupText ? `${!!_remark ? `${_remark?.replace(/;/g, "")};` : ""}[穴位]${acupText};` : _remark) ?? item.remark;
            }
        }
        formSortIndex = 0;
        itemSortIndex = 0;
        _.remove(this.innerState.detailData!.prescriptionInfusionForms ?? [], (item) => ABCUtils.isEmpty(item.prescriptionFormItems));
        for (const form of this.innerState.detailData?.prescriptionInfusionForms ?? []) {
            form.sort = formSortIndex++;

            for (const item of form.prescriptionFormItems ?? []) {
                item.sort = itemSortIndex++;
                item.sourceItemType = !item.isSelfProvided ? 0 : 1;
                //解决当前ivgtt为空时，打印出来的单据上依然显示了ivgttUnit的问题
                if (!item?.ivgtt) {
                    item.ivgttUnit = undefined;
                }
            }
        }

        formSortIndex = 0;
        itemSortIndex = 0;
        _.remove(this.innerState.detailData!.prescriptionWesternForms ?? [], (item) => ABCUtils.isEmpty(item.prescriptionFormItems));
        for (const form of this.innerState.detailData?.prescriptionWesternForms ?? []) {
            form.sort = formSortIndex++;

            for (const item of form.prescriptionFormItems ?? []) {
                /**
                 * @description 只有单子未被执行并且ast处方单关闭 才对ast默认赋值 null
                 */
                item.ast = !item.astResult && !outpatientConfig?.astPrescription?.showWesternSwitch ? null : item.ast;
                item.sort = itemSortIndex++;
                item.sourceItemType = !item.isSelfProvided ? 0 : 1;
                //重新计算当前处方开单的拆零标识
                item.useDismounting = item.unit ? (item.goodsInfo.useDismounting(item.unit) ? 1 : 0) : item.useDismounting;
            }
        }

        formSortIndex = 0;
        itemSortIndex = 0;
        _.remove(this.innerState.detailData?.prescriptionChineseForms ?? [], (item) => ABCUtils.isEmpty(item.prescriptionFormItems));
        for (const form of this.innerState.detailData?.prescriptionChineseForms ?? []) {
            form.sort = formSortIndex++;
            //如果当前不是本地药房，并且制法不是代煎、颗粒，则需要清除煎药的袋数
            //空中药房(代煎、颗粒)、虚拟药房（代煎）不需要清除煎药袋数
            if (
                !!form.pharmacyType &&
                form.medicineStateScopeId != MedicineScopeId.daiJian &&
                form.medicineStateScopeId != MedicineScopeId.keLi
            ) {
                form.processBagUnitCount = undefined;
            }
            //空中药房代煎、颗粒/代煎代配药房代煎则代表加工，需要将isDecocting置为true
            if (
                (form.pharmacyType == PharmacyType.air &&
                    (form.medicineStateScopeId == MedicineScopeId.daiJian || form.medicineStateScopeId == MedicineScopeId.keLi)) ||
                (form.pharmacyType == PharmacyType.virtual && form.medicineStateScopeId == MedicineScopeId.daiJian)
            ) {
                form.isDecoction = true;
            }

            for (const item of form.prescriptionFormItems ?? []) {
                item.sort = itemSortIndex++;
                item.sourceItemType = !item.isSelfProvided ? 0 : 1;
                item.unit = item.goodsInfo?.pieceUnit ?? item.unit;
            }
        }

        formSortIndex = 0;
        _.remove(this.innerState.detailData?.prescriptionExternalForms ?? [], (item) => ABCUtils.isEmpty(item.prescriptionFormItems));
        for (const form of this.innerState.detailData?.prescriptionExternalForms ?? []) {
            form.sort = formSortIndex++;
        }

        const attachments = this.innerState.detailData?.medicalRecord?.attachments;
        if (attachments != null) {
            const len = attachments.length;
            for (let i = 0; i < len; ++i) {
                attachments[i].sort = i;
            }
        }

        //病历中眼部检查转义的html需要还原
        const eyeExaminationItem = this.innerState.detailData?.medicalRecord?.eyeExamination?.items;
        eyeExaminationItem?.map((item) => {
            item.leftEyeValue = StringUtils.htmlEncode(item.leftEyeValue);
            item.rightEyeValue = StringUtils.htmlEncode(item.rightEyeValue);
        });

        const actionText = !this.innerState.detailData?.id ? "保存" : "接诊";

        ////计算门诊单费用，显示价格预览
        let dialog = new LoadingDialog("正在计算费用...");
        dialog.show(300);

        let calculateRsp: OutpatientInvoiceDetail;
        try {
            calculateRsp = await OutpatientAgent.chargeCalculate(
                OutpatientUtils.tripChargedItemForOutpatientPriceCalculate(this.innerState.detailData!)
            );
        } catch (e) {
            await dialog.hide();
            await showQueryDialog("", `${actionText}失败,计算费用失败:${errorToStr(e)}`);
            return;
        } finally {
            await dialog.hide();
        }

        const _showPriceConfirmDialog = async () => {
            const detail = this.innerState.detailData;
            const isOnlineOutpatient = detail?.source == ChargeInvoiceSource.onlineOutpatient || detail?.isOnline == 1;
            const isEnableCA = this.innerState.isEnableCA;

            return await OutpatientConfirmModifyDialog.show({
                title: `${!!this.draftId?.length || detail?.status == 0 ? "确认完成就诊" : "确认修改门诊单"}`,
                detailData: detail,
                calculateRsp: calculateRsp,
                showPrice: this.innerState.showTotalPrice,
                isEnableCA: isEnableCA,
                isOnlineOutpatient: isOnlineOutpatient,

                canSigned: this.innerState.doctorSignatureStatus == 0,
                prescriptionUseCa: !!this.innerState.prescriptionUseCa, // 是否勾选签名
                isExpired: this.innerState.isDoctorSignatureExpired, // 签名已过期
                isUnavailable: this.innerState.doctorSignatureStatus == 1,
                unbound: this.innerState.doctorSignatureStatus == undefined,
                needShowAgreementPage: this.innerState.needShowAgreementPage && !this.innerState.prescriptionUseCa, // 需要首次弹出签名协议
                modifyCaStatus: (status) => {
                    this.innerState.prescriptionUseCa = status;
                },
            });
        };

        if (this.innerState.detailData?.isOnline == 1) {
            const autoSendOrderInfoSwitch = false; // this.innerState.chargeConfig?.autoSendOrderInfoSwitch == 1
            let select;
            if (autoSendOrderInfoSwitch) {
                //网诊
                select = await showQueryDialog(
                    "是否确认处方并发送给患者?",
                    this.innerState.detailData?.patient?.isWxBind ? "完成就诊后患者可在微诊所内支付该门诊单" : ""
                );
                // select = await OutpatientDialog.showSendToPatientConfirmDialog(calculateRsp.totalPrice!, this.innerState.outpatientDetailData?.medicalRecord?.diagnosis!, this.innerState.outpatientDetailData?.patient?.name,);
            } else {
                select = await _showPriceConfirmDialog();
            }
            if (select != DialogIndex.positive) return;
            await ClinicAgent.updateOnlineDoctorElectronicSignature(
                this.innerState.detailData.consultationSheet?.doctorId ?? "",
                !!this.innerState.prescriptionUseCa
            ).catchIgnore();
            // 点击确定后，如果get接口返回值没有created字段(needShowAgreementPage == true)，则调用post接口通知这次弹过窗了
            if (this.innerState.needShowAgreementPage) {
                ClinicAgent.postCaSignatureRead(this.innerState.detailData?.consultationSheet?.doctorId ?? "").catchIgnore();
            }
        } else {
            const select = await _showPriceConfirmDialog();
            if (select != DialogIndex.positive) return;
        }
        dialog = new LoadingDialog("正在提交...");
        dialog.show();

        let error;

        const draftId = this.innerState.detailData?.draftId;
        this.innerState.detailData!.hospitalPatientOrderId = !!this.innerState.hospitalDetail?.id
            ? this.innerState.hospitalDetail?.id
            : undefined;
        try {
            if (!this.innerState.detailData?.id) {
                this.innerState.detailData = await OutpatientAgent.createOutpatientInvoice(this.innerState.detailData!);
            } else {
                const { dentistryExaminations } = this.innerState.detailData.medicalRecord ?? {};
                const req = JsonMapper.deserialize(OutpatientInvoiceDetail, {
                    ...this.innerState.detailData,
                });
                !!req.medicalRecord &&
                    (req.medicalRecord.dentistryExaminations = userCenter.clinic?.isDentistryClinic ? dentistryExaminations : undefined);
                this.innerState.detailData = await OutpatientAgent.updateOutpatientInvoice(req);
                LogUtils.d("detailData ====" + JSON.stringify(this.innerState.detailData));
            }
        } catch (e) {
            error = e;
        } finally {
        }

        if (error != null) {
            await dialog.hide();
            await Toast.show(`${actionText}失败：${errorToStr(error)}`, {
                warning: true,
            });

            //判断挂号费状态进行，判断是否进行覆盖
            if (
                errorToStr(error).includes(`只有未收费才可修改${this.innerState.clinicBasicSetting?.registrationFeeStr}`) &&
                !!this.innerState.detailData?.id
            ) {
                const orderDetail = await OutpatientAgent.getOutpatientInvoiceDetail(this.innerState.detailData.id).catchIgnore();

                // //判断挂号费状态进行，判断是否进行覆盖
                if (!OutpatientUtils.canEditWithChargeStatus(orderDetail?.registrationFeeStatus)) {
                    this.innerState.detailData!.registrationFeeStatus = orderDetail?.registrationFeeStatus;
                    this.innerState.detailData!.registrationFee = orderDetail?.registrationFee;
                    this.update();
                }
            }
            return;
        }

        //【ID1015454】收费时同时修改门诊单优化-提交成功后清空本地定时器
        this.unLockPatientOrder();

        this.innerState.isEditing = false;
        this.hasChanged = false;

        //保存中药处方上次使用方案
        if (this.getLastChinesePrescriptionUsage()) {
            clinicSharedPreferences.setObject(CHINESE_PRESCRIPTION_USAGE_DEFAULT, JSON.stringify(this.getLastChinesePrescriptionUsage()));
        }
        //修改中药处方 用法本地用法合并
        ChineseMedicineConfigProvider.setChinesePRWithUsage(
            this.innerState.detailData?.prescriptionChineseForms?.map((item) => item.prescriptionInputUsageInfo) ?? []
        );
        //记录外治处方药品的计算方式
        const bizRelevantIds = new Map<
            string,
            {
                goodsId: string;
                bizExtension: {
                    billingType: number;
                };
            }
        >();
        this.innerState.detailData?.prescriptionExternalForms?.forEach((externalForm) => {
            externalForm.prescriptionFormItems?.forEach((formItem) => {
                if (!formItem.goodsInfo.bizExtension && !!formItem.goodsInfo.id) {
                    bizRelevantIds.set(formItem.goodsInfo.id, {
                        goodsId: formItem.goodsInfo.id,
                        bizExtension: {
                            billingType: formItem.billingType ?? 2,
                        },
                    });
                }
            });
        });
        GoodsAgent.postGoodsBizExtensionsInfo({ list: Array.from(bizRelevantIds.values()) }).catchIgnore();

        //删除本地草稿
        if (draftId && this.allowDraft) {
            await this._draftManager().removeDraft(draftId);
        }
        await dialog.success(`${actionText}完成`);
        await dialog.hide();

        if (this.innerState.detailData?.isOnline == 1) {
            //网诊完成接诊，需回到上一个页面
            ABCNavigator.pop(this);
        } else {
            //完成接诊后，不回到列表页面，就在当前详情页，需要刷新一下信息
            await this._loadDataFromNetwork();
            this.innerState.focusItemKey = OutpatientContentRefKey.patient;
            yield ScrollToFocusItemState.fromState(this.innerState);
        }
    }

    @actionEvent(_EventSaveMedical)
    async *_mapEventSaveMedical(/*event: _EventSaveMedical*/): AsyncGenerator<State> {
        const checkResult = new ValueHolder<boolean>();
        this.innerState.detailData = this.innerState.detailData ?? new OutpatientInvoiceDetail();
        //判断是否含有流病史
        if (this.innerState.covid19DetectionStatus) {
            const result = await MedicalRecordCheckDialog.show({ medicalRecord: this.innerState.detailData?.medicalRecord });
            if (result == DialogIndex.positive) {
                return;
            }
        }
        const resultState = await this._onlyValidateMedicalRecord({ checkResult: checkResult });
        if (resultState) {
            yield resultState;
            return;
        }
        if (!this.innerState.isMedicalChanged) return;
        this.innerState.detailData.medicalRecord = this.innerState.detailData?.medicalRecord ?? new MedicalRecord();

        const attachments = this.innerState.detailData?.medicalRecord?.attachments;
        if (attachments != null) {
            const len = attachments.length;
            for (let i = 0; i < len; ++i) {
                attachments[i].sort = i;
            }
        }

        //病历中眼部检查转义的html需要还原
        const eyeExaminationItem = this.innerState.detailData?.medicalRecord?.eyeExamination?.items;
        eyeExaminationItem?.map((item) => {
            item.leftEyeValue = StringUtils.htmlEncode(item.leftEyeValue);
            item.rightEyeValue = StringUtils.htmlEncode(item.rightEyeValue);
        });
        const select = await showQueryDialog("修改门诊单", "未添加医嘱，无新增费用");
        if (select != DialogIndex.positive) return;
        if (!!this.innerState.detailData?.medicalRecord?.id) {
            const dialog = new LoadingDialog("正在保存病历");
            dialog.show();
            try {
                const rsp = await OutpatientAgent.saveMedicalRecord({
                    medicalRecordId: this.innerState.detailData?.medicalRecord?.id,
                    medicalRecord: this.innerState.detailData?.medicalRecord,
                });
                this.innerState.detailData = this.innerState.detailData ?? new OutpatientInvoiceDetail();
                this.innerState.detailData.medicalRecord = rsp;
                this.innerState.needBackToEdit = true;
                this.innerState.isEditing = false;
                this.hasChanged = false;
                await dialog.success("修改成功", 1000);
            } catch (e) {
                await dialog.fail(`保存失败：${errorSummary(e)}`);
            }
            this.update();
        }
    }

    @actionEvent(_EventBack)
    async *_mapEventBack(/*event: _EventBack*/): AsyncGenerator<State> {
        // 不用isEditing原因：门诊收费锁时可以继续修改病历，此时用isEditing锁单中会直接返回，不会提示病历修改的内容
        if (!this.innerState.notLockingIsEditing) {
            ABCNavigator.pop();
            return;
        }

        // 如果没有门诊单id，则需要删除当前这条草稿
        if (!this.innerState.detailData?.id) {
            const select = await showQueryDialog("确认退出编辑吗？", "退出将不会保存已发生的修改");
            if (select != DialogIndex.positive) return;
            if (!!this.innerState.detailData?.draftId) await this._draftManager().removeDraft(this.innerState.detailData.draftId);
            this.hasChanged = false;
            ABCNavigator.pop();
            return;
        }
        if (this.hasChanged && this.innerState.detailData?.isWaitVisit && this.allowDraft && this.innerState.detailData.isOnline != 1) {
            if (!this.innerState.detailData.draftId) {
                this.innerState.detailData!.draftId = OutpatientDraftManager.generateDraftId();
            }

            if (!(await this._draftManager().saveDraft(this.innerState.detailData, false))) return;
        }
        if (this.innerState.detailData == null || !this.innerState.detailData.isVisited || !this.hasChanged) {
            // 此处调用解锁：因为在点击修改按钮时就会进行加锁，不管是否有修改，在返回是应该解锁
            this.unLockPatientOrder();
            ABCNavigator.pop();
            return;
        }
        const select = await showQueryDialog("确认退出编辑吗？", "退出将不会保存已发生的修改");
        if (select != DialogIndex.positive) return;

        //【ID1015454】收费时同时修改门诊单优化-调用解锁单接口
        this.unLockPatientOrder();

        ABCNavigator.pop();
    }

    @actionEvent(_EventChangeSetting)
    async *_mapEventChangeSetting(event: _EventChangeSetting): AsyncGenerator<State> {
        if (this.innerState.outpatientConfig?.medicalRecord?.type != event.config.medicalRecord?.type) {
            this.innerState.outpatientConfig = event.config;
            this._getAIDiagnosisTrigger.next();
        } else {
            this.innerState.outpatientConfig = event.config;
        }
        this.update();
        this._updateOutpatientConfig.next();
    }

    // 智能审方
    @actionEvent(_EventViewAiVerifyResult)
    async *_mapEventViewAiVerifyResult(/*event: _EventViewAiVerifyResult*/): AsyncGenerator<State> {
        await OutpatientAiVerifyDialog.show(this.innerState.aiVerify);
    }

    // 确认签字
    @actionEvent(_EventViewVerifyDoubleSignResult)
    async *_mapEventViewVerifyDoubleSignResult(event: _EventViewVerifyDoubleSignResult): AsyncGenerator<State> {
        this.innerState.needDoubleSign = !event.confirmSign;
        const needCheckSignList = this.innerState.aiVerify?.checkItems?.map((item) => item.__originDetail);
        const needCheckSignFitnessList = this.innerState.aiVerify?.verifyItems?.FITNESS?.map((item) => item?.__originDetail);
        const prescriptionChineseForms = this.innerState.detailData?.prescriptionChineseForms;
        const prescriptionWesternForms = this.innerState.detailData?.prescriptionWesternForms;
        const prescriptionInfusionForms = this.innerState.detailData?.prescriptionInfusionForms;
        needCheckSignList?.map((k) => {
            for (const form of prescriptionChineseForms ?? []) {
                for (const formItem of form.prescriptionFormItems ?? []) {
                    //判断当前药品是否有存在禁忌警告
                    const needCheckSign =
                        (!!formItem.goodsId && k?.includes(formItem.goodsId ?? "")) ||
                        (!!formItem.keyId && k?.includes(formItem.keyId ?? ""));
                    if (needCheckSign) {
                        if (event.confirmSign) {
                            formItem.verifySignatures = [
                                JsonMapper.deserialize(VerifySignaturesItem, {
                                    keyId: formItem.keyId,
                                    goodsId: formItem.goodsId,
                                    cadn: formItem.medicineCadn,
                                    status: 1,
                                }),
                            ];
                        } else {
                            formItem.verifySignatures = [];
                        }
                    }
                }
            }
        });

        needCheckSignFitnessList?.map((k) => {
            for (const form of prescriptionWesternForms ?? []) {
                for (const formItem of form.prescriptionFormItems ?? []) {
                    //判断当前药品是否有存在禁忌警告
                    const needCheckSign =
                        (!!formItem.goodsId && k?.includes(formItem.goodsId ?? "")) ||
                        (!!formItem.keyId && k?.includes(formItem.keyId ?? ""));
                    if (needCheckSign) {
                        if (event.confirmSign) {
                            formItem.verifySignatures = [
                                JsonMapper.deserialize(VerifySignaturesItem, {
                                    keyId: formItem.keyId,
                                    goodsId: formItem.goodsId,
                                    cadn: formItem.medicineCadn,
                                    status: 1,
                                }),
                            ];
                        } else {
                            formItem.verifySignatures = [];
                        }
                    }
                }
            }
        });

        needCheckSignFitnessList?.map((k) => {
            for (const form of prescriptionInfusionForms ?? []) {
                for (const formItem of form.prescriptionFormItems ?? []) {
                    //判断当前药品是否有存在禁忌警告
                    const needCheckSign =
                        (!!formItem.goodsId && k?.includes(formItem.goodsId ?? "")) ||
                        (!!formItem.keyId && k?.includes(formItem.keyId ?? ""));
                    if (needCheckSign) {
                        if (event.confirmSign) {
                            formItem.verifySignatures = [
                                JsonMapper.deserialize(VerifySignaturesItem, {
                                    keyId: formItem.keyId,
                                    goodsId: formItem.goodsId,
                                    cadn: formItem.medicineCadn,
                                    status: 1,
                                }),
                            ];
                        } else {
                            formItem.verifySignatures = [];
                        }
                    }
                }
            }
        });

        this.hasChanged = true;
        this.update();
    }

    @actionEvent(_EventChangePrescriptionType)
    async *_mapEventChangePrescriptionType(event: _EventChangePrescriptionType): AsyncGenerator<State> {
        const { prescriptionName, index = 0, psychotropicNarcoticType } = event.detail;
        switch (prescriptionName) {
            case "中西成药处方": {
                this.innerState.detailData!.prescriptionWesternForms![index].psychotropicNarcoticType = psychotropicNarcoticType;
                break;
            }
            case "外治处方": {
                this.innerState.detailData!.prescriptionExternalForms![index].psychotropicNarcoticType = psychotropicNarcoticType;
                break;
            }
            case "中药处方": {
                this.innerState.detailData!.prescriptionChineseForms![index].psychotropicNarcoticType = psychotropicNarcoticType;
                break;
            }
            case "输注处方": {
                this.innerState.detailData!.prescriptionInfusionForms![index].psychotropicNarcoticType = psychotropicNarcoticType;
                break;
            }
        }
        this.update();
    }

    private getLastChinesePrescriptionUsage(): ChinesePrescriptionUsage | undefined {
        const last = ABCUtils.last(this.innerState.detailData?.prescriptionChineseForms ?? []);
        if (last) {
            const chinesePrescriptionUsage = ChinesePrescriptionUsage.fromPrescriptionForm(last);
            chinesePrescriptionUsage.count = 1;
            chinesePrescriptionUsage.boilServiceMobile = undefined;
            chinesePrescriptionUsage.requirement = undefined;
            return chinesePrescriptionUsage;
        } else {
            return undefined;
        }
    }

    // 添加问诊单
    @actionEvent(_EventAddQuestionSheet)
    async *_mapEventAddQuestionSheet(): AsyncGenerator<State> {
        const selectConsultation = await QuestionSheetAgent.getQuestionSheetList({ type: SelectQuestionType.outpatient });
        const isExistList = !!selectConsultation?.rows?.length; // 是否存在问诊单
        const maxHeight = Dimensions.get("window").height - UiUtils.safeStatusHeight() - 44; //问诊单弹窗最大的高度
        const contentHeight = isExistList ? (selectConsultation?.rows!.length / 2) * (46 + 8) : 0; //问诊单单行列表的高度
        let outpatientListView: JSX.Element[] = [];
        outpatientListView = (selectConsultation?.rows ?? []).map((item, index) => (
            <View
                key={index}
                style={{
                    flex: 1,
                    backgroundColor: Colors.bg1,
                    paddingHorizontal: Sizes.dp12,
                    paddingVertical: Sizes.dp13,
                    borderRadius: Sizes.dp4,
                    borderWidth: Sizes.dpHalf,
                    borderColor: Colors.bg1,
                    flexDirection: "row",
                    width: pxToDp(168),
                }}
                onClick={() => {
                    ABCNavigator.pop(item);
                }}
            >
                <IconFontView
                    name={"inquiry_file"}
                    size={Sizes.dp16}
                    style={{
                        marginRight: Sizes.dp8,
                        marginTop: Sizes.dp2,
                    }}
                    color={Colors.T6}
                />
                <Text
                    style={[
                        TextStyles.t14NT1,
                        {
                            flex: 1,
                            flexShrink: 1,
                            lineHeight: Sizes.dp20,
                        },
                    ]}
                    numberOfLines={1}
                >
                    {item.name ?? ""}
                </Text>
            </View>
        ));
        if (isExistList && contentHeight < maxHeight) {
            if (outpatientListView.length % 2 == 0) {
                outpatientListView.push(<View style={{ height: Sizes.dp46 }} />);
            } else {
                outpatientListView.push(<View style={{ height: Sizes.dp46 }} />);
                outpatientListView.push(<View style={{ height: Sizes.dp46 }} />);
            }
        }
        const realHeight = (outpatientListView!.length / 2) * (46 + 8);
        const selects = await AbcDialog.showOptionsBottomSheet({
            title: "问诊单",
            showConfirmBtn: false,
            itemHeight: Sizes.dp46,
            height: contentHeight >= maxHeight ? maxHeight : realHeight + 57 + 32 + 32,
            optionsWidgets: outpatientListView,
        });

        if (!selects) return;

        const result = await ABCNavigator.navigateToPage<OutpatientInvoiceDetail>(
            <OutpatientQuestionSheetDetailPage
                id={(selects as SelectConsultationSheetList).id}
                outpatientSheetDetail={this.innerState.detailData}
            />,
            {
                ohosWillPop: true,
            }
        );

        if (result) {
            //直接将 detail 处理好扔回来
            this.innerState.detailData = result;
            this.innerState.focusItemKey = OutpatientContentRefKey.attachment;
            yield ScrollToFocusItemState.fromState(this.innerState);
        }
    }

    // 修改问诊单
    @actionEvent(_EventModifyQuestionSheet)
    async *_mapEventModifyQuestionSheet(event: _EventModifyQuestionSheet): AsyncGenerator<State> {
        /**
         * 修改问诊单流程
         * @return {id:string; name:string}
         */
        const result = await ABCNavigator.navigateToPage<OutpatientInvoiceDetail>(
            <OutpatientQuestionSheetDetailPage sheetId={event.id} outpatientSheetDetail={this.innerState.detailData} />,
            {
                ohosWillPop: true,
            }
        );

        if (result) {
            //直接将 detail 处理好扔回来
            this.innerState.detailData = result;
            this.innerState.focusItemKey = OutpatientContentRefKey.attachment;
            yield ScrollToFocusItemState.fromState(this.innerState);
        }
    }

    // 添加附件
    @actionEvent(_EventAddAttachment)
    async *_mapEventAddAttachment(): AsyncGenerator<State> {
        const canAddQuestionForm = !!this.innerState.canAddQuestionForm; // 是否可用问诊单
        let result: number[] | undefined = [1];
        if (canAddQuestionForm) {
            result = await AbcDialog.showOptionsBottomSheet({
                title: "病例附件",
                options: ["问诊单", "图片"],
            });
        }

        if (result && result.length) {
            if (result[0] == 0) {
                if (!this.innerState.detailData?.patient) {
                    this.innerState.focusItemKey = OutpatientContentRefKey.patient;
                    Toast.show("请添加患者信息", { warning: true });
                    return ScrollToFocusItemState.fromState(this.innerState);
                }
                this.requestAddQuestionSheet();
            }
            if (result[0] == 1) {
                this.requestAddImage();
            }
            this.update();
        }
    }

    @actionEvent(_EventAddVoiceRecord)
    async *_mapEventAddVoiceRecord(): AsyncGenerator<State> {
        // 加载语音记录数据
        if (this.outpatientId) {
            this.innerState.voiceRecordResult = await Asr.getAsrResult(this.outpatientId, "");
        }
        const outpatientConfig = this.innerState?.outpatientConfig?.currentMedicalRecordConfig || {};
        const chineseExamination = this.innerState.detailData?.medicalRecord?.chineseExamination ? 1 : 0;
        const physicalExamination = this.innerState.detailData?.medicalRecord?.physicalExamination ? 1 : 0;
        const medicalRecordType = outpatientConfig instanceof MedicalRecordConfig ? outpatientConfig.type : 0;
        const businessId = this.outpatientId;
        const patient = this.innerState.detailData?.patient;
        const voiceRecordResult = this.innerState.voiceRecordResult?.[this.innerState.voiceRecordResult.length - 1] || ({} as AsrResult);
        const draft = this.innerState.detailData;
        const _this = this;
        await AIMedicalRecordsManager.showMedicalRecordTranscriptionOverlay(
            patient,
            businessId,
            voiceRecordResult,
            chineseExamination,
            physicalExamination,
            medicalRecordType,
            draft,
            (res: any) => {
                const { medicalRecordData } = res || {};
                const outpatientASRResult = new OutpatientHistoryDialogResult();
                outpatientASRResult.action = OutpatientHistoryDialogAction.copyHistory;
                outpatientASRResult.outpatientData = new OutpatientInvoiceDetail();
                if (medicalRecordData) {
                    outpatientASRResult.outpatientData.medicalRecord = medicalRecordData as MedicalRecord;
                    _this.requestNavToHistoryDialog(outpatientASRResult);
                }
                _this.update();
            }
        );
    }

    private _modifyChinesePrescriptionUsage(prescription: PrescriptionChineseForm): void {
        const usageInfo: UsageInfo = {
            freq: prescription.freq,
            usageLevel: prescription.usageLevel,
        };
        prescription.usageDays =
            ChargeUtils.calcUsageDays({
                totalDoseWeight: prescription.computePrescriptionDosageWeight() * (prescription.doseCount ?? 1),
                finishedRate: prescription.vendor?.finishedRate,
                isAirPharmacy: prescription.isAirPharmacy,
                usageScopeId: prescription.usageScopeId,
                medicineStateScopeId: prescription.medicineStateScopeId,
                usageInfo: usageInfo,
            }) ?? prescription.usageDays;
    }

    //【ID1015454】收费时同时修改门诊单优化-调用锁单接口
    async lockPatientOrder(type: PatientOrderLockType = PatientOrderLockType.chargeSheet): Promise<void> {
        const orderId = this.innerState.detailData?.patientOrderId;
        if (!orderId) return;
        switch (type) {
            case PatientOrderLockType.outpatientSheet: {
                let _lockTimeInterval = this._lockTimeIntervals.get(type);
                if (!!_lockTimeInterval) return;
                //加锁前查询当前锁单详情
                let lockDetail = this.innerState.lockDetails.get(type);
                if (!lockDetail) {
                    lockDetail = await PatientOrderAgent.getPatientOrderLockDetail(orderId, {
                        businessKey: PatientOrderLockType.outpatientSheet,
                    });
                }

                //无已加锁信息需进行加锁
                if (!lockDetail.identity) {
                    lockDetail = await PatientOrderAgent.postPatientOrderLock(orderId, {
                        businessKey: PatientOrderLockType.outpatientSheet,
                    }); // 要赋值，否则解锁时没有identity则不用调用解锁的方法
                }

                _lockTimeInterval = interval(50 * 1000).subscribe(async () => {
                    await PatientOrderAgent.putPatientOrderLockRenew(orderId, {
                        businessKey: PatientOrderLockType.outpatientSheet,
                        identity: lockDetail?.identity ?? "",
                        businessLockValue: lockDetail?.value,
                    });
                });
                this.addDisposable(_lockTimeInterval);
                this._lockTimeIntervals.set(type, _lockTimeInterval);
                this.innerState.lockDetails.set(type, lockDetail);
                break;
            }
            case PatientOrderLockType.chargeSheet: {
                let _lockTimeInterval = this._lockTimeIntervals.get(type);
                if (!!_lockTimeInterval) return;

                let chargeLockDetail = this.innerState.lockDetails.get(type);
                if (!chargeLockDetail?.identity) {
                    chargeLockDetail = await PatientOrderAgent.postPatientOrderLock(orderId, {
                        businessKey: type,
                    });
                    this.innerState.lockDetails.set(type, chargeLockDetail);
                }

                _lockTimeInterval = interval(50 * 1000).subscribe(async () => {
                    await PatientOrderAgent.putPatientOrderLockRenew(orderId, {
                        businessKey: type,
                        identity: chargeLockDetail?.identity ?? "",
                        businessLockValue: chargeLockDetail?.value,
                    });
                });
                this.addDisposable(_lockTimeInterval);
                this._lockTimeIntervals.set(type, _lockTimeInterval);
                break;
            }
        }
    }

    //【ID1015454】收费时同时修改门诊单优化-调用解锁单接口
    async unLockPatientOrder(): Promise<void> {
        const orderId = this.innerState.detailData?.patientOrderId;
        if (!orderId || this.innerState.orderIsLocking) return;
        this.innerState.lockDetails.forEach(async (detail, type) => {
            if (!detail.identity) return;
            await PatientOrderAgent.postPatientOrderLockUnlock(orderId, {
                businessKey: type,
                identity: this.innerState.lockDetail?.identity ?? "",
                businessLockValue: detail?.value,
            });
            const _subject = this._lockTimeIntervals.get(type);
            if (!!_subject) {
                _subject.unsubscribe();
            }
            this._lockTimeIntervals.set(type, undefined);
        });
    }

    @actionEvent(_EventRemoveImage)
    async *_mapEventRemoveImage(event: _EventRemoveImage): AsyncGenerator<State> {
        this.innerState.detailData?.medicalRecord?.attachments?.splice(event.index, 1);
        this.hasChanged = true;
        this.update();
    }

    @actionEvent(_EventModifyPatientGuardianDetail)
    async *_mapEventModifyPatientGuardianDetail(event: _EventModifyPatientGuardianDetail): AsyncGenerator<State> {
        this.innerState.detailData!.patientGuardian = event.value;
        this.hasChanged = true;
        this.update();
    }

    private _initPageTrigger(): void {
        /////////////////////////////// 事件监听 ///////////////////////////////
        this._updateOutpatientConfig
            .pipe(
                switchMap((/*_*/) => {
                    return OnlinePropertyConfigProvider.instance
                        .updateOutpatientConfig({
                            scope: "employee",
                            config: this.innerState.outpatientConfig!,
                        })
                        .toObservable();
                })
            )
            .subscribe((config) => {
                this.innerState.outpatientConfig = config;
                if (!config?.medicalRecord?.uploadImage && this.innerState.detailData?.medicalRecord?.attachments?.length) {
                    this.innerState.detailData.medicalRecord.attachments = [];
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._getAIDiagnosisTrigger
            .pipe(
                switchMap((/*_*/) => {
                    this.innerState.aiDiagnosis.list = [];
                    const { medicalRecord, patient } = this.innerState.detailData ?? {};
                    const chiefComplaint = medicalRecord?.chiefComplaint;
                    if (this.innerState.outpatientConfig?.medicalRecord?.type == 1) {
                        return CDSSAgent.getChineseDiagnosis({
                            q: chiefComplaint ?? "",
                            sex: patient?.sex ?? "男",
                            age: patient?.age?.AgeToPoint ?? 0,
                            dn: medicalRecord?.diagnosis ?? "",
                            hpi: medicalRecord?.pastHistory ?? "",
                            ob: medicalRecord?.chineseExamination ?? "",
                            sn: medicalRecord?.syndrome ?? "",
                            tn: medicalRecord?.therapy ?? "",
                            dictDiseaseType: this.innerState.clinicShebaoConfig?.dictDiseaseType,
                        })
                            .catchIgnore()
                            .toObservable();
                    } else {
                        return CDSSAgent.getDiagnosis({
                            q: chiefComplaint ?? "",
                            sex: patient?.sex ?? "男",
                            age: patient?.age?.AgeToPoint ?? 0,
                        })
                            .catchIgnore()
                            .toObservable();
                    }
                })
            )
            .subscribe((diagnosis) => {
                if (!diagnosis) return;
                this.innerState.aiDiagnosis = diagnosis;
                this.update();
            })
            .addToDisposableBag(this);

        //门诊单未完成接诊时，获取长护详情
        this._getUnfinishOutpatientTrigger
            .pipe(
                switchMap((id) => {
                    if (!id) return of(null);
                    return HospitalAgent.getOutpatientHospitalRegDetail(id).catchIgnore().toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                this.innerState.hospitalDetail = rsp;
                this.update();
            })
            .addToDisposableBag(this);

        /**
         * 监听方药同步状态
         */
        OutpatientUtils.asyncOutpatientMedicineAddPrescription
            .subscribe((options: { prescriptionTemplate: PrescriptionTemplate }) => {
                if (options.prescriptionTemplate) {
                    this.dispatch(new _EventInvoiceTemplate(options.prescriptionTemplate));
                }
            })
            .addToDisposableBag(this);

        /**
         * 获取患者初复诊状态
         */
        this._patientRevisitStatusTrigger
            .pipe(
                switchMap(() => {
                    const detail = this.innerState.detailData,
                        doctorId = detail?.doctorId,
                        patientId = detail?.patient?.id;
                    if (!doctorId || !patientId) {
                        return of(RegistrationRevisitStatus.first);
                    }
                    return RegistrationAgent.getOutpatientRevisitStatus({
                        doctorId,
                        patientId,
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof GetOutpatientRevisitStatusRsp && !!rsp) {
                    this.innerState.detailData!.revisitStatus = rsp?.revisitStatus ?? RegistrationRevisitStatus.first;
                    this.innerState.outpatientRevisitStatusRsp = rsp;
                } else {
                    this.innerState.detailData!.revisitStatus = rsp;
                }
                //修改初复诊状态时 保存配置文件
                if (
                    this.innerState.isDentistryClinic &&
                    this.innerState.outpatientConfig!.revisit != this.innerState.detailData!.revisitStatus
                ) {
                    this.innerState.outpatientConfig!.revisit = Number(this.innerState.detailData!.revisitStatus);
                    this._updateOutpatientConfig.next();
                }
                this.registerFeeCalculation();
            })
            .addToDisposableBag(this);

        /**
         * 医生证书查询
         */
        this._supervisionSccaDoctorCaInfoTrigger
            .pipe(
                switchMap(() => {
                    const detail = this.innerState.detailData,
                        doctorId = detail?.doctorId;
                    return OutpatientAgent.getSupervisionSccaDoctorCaInfo(doctorId!, true).catchIgnore().toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                this.innerState.doctorSignatureStatus = rsp?.body?.status;
                this.innerState.isDoctorSignatureExpired = (rsp?.body?.validDays ?? 0) <= 0;
            })
            .addToDisposableBag(this);

        /**
         * 获取当前网诊单的签名配置
         */
        this._isUseCaSignatureTrigger
            .pipe(
                switchMap(() => {
                    return Promise.all([
                        ClinicAgent.getOnlineDoctorStatus(this.innerState.detailData?.consultationSheet?.doctorId ?? "").catchIgnore(),
                        ClinicAgent.getIsCaSignatureRead(this.innerState.detailData?.consultationSheet?.doctorId ?? "").catchIgnore(),
                    ]).toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                const [prescriptionUseCaRsp, needShowAgreementPageRsp] = rsp;
                // 获取医生是否勾选CA电子签名
                this.innerState.prescriptionUseCa = prescriptionUseCaRsp?.prescriptionUseCa == 1;
                // 获取医生是否是第一次网诊完诊 (查看是否有created字段，如果有，则不需要强制弹窗，如果没有，则需要强制弹窗)
                this.innerState.needShowAgreementPage = !needShowAgreementPageRsp?.created;
            })
            .addToDisposableBag(this);

        /**
         * 监听同步状态
         */
        OutpatientUtils.asyncOutpatientMedicineRecordToothStatus = new Map<number, boolean>();
        OutpatientUtils.asyncOutpatientMedicineRecordTooth
            .subscribe((options: { tooth: ToothNos; index: number; status: boolean }) => {
                if (options.status) {
                    this.innerState.asyncToothNosMap.set(options.index, options.tooth);
                    this._asyncMedicalRecordToothNos();
                } else {
                    this.innerState.asyncToothNosMap.delete(options.index);
                }
                this.update();
            })
            .addToDisposableBag(this);

        /**
         * 智能审方
         */
        this._aiVerify
            .pipe(
                switchMap((/*_*/) => {
                    if (
                        ABCUtils.isEmpty(this.innerState.detailData?.prescriptionWesternForms) &&
                        ABCUtils.isEmpty(this.innerState.detailData?.prescriptionInfusionForms) &&
                        ABCUtils.isEmpty(this.innerState.detailData?.prescriptionChineseForms)
                    )
                        return of(null);

                    // 发起智能审方请求时不传specialRequirement可能会使后台报错，导致处方单智能提醒无法正常显示 在不影响原参数的情况下这里使用_.cloneDeep处理
                    const _detailData = _.cloneDeep(this.innerState.detailData);
                    _detailData?.prescriptionChineseForms?.forEach((form) => {
                        form.prescriptionFormItems?.map((item) => {
                            item.specialRequirement = item.productInfo?.remark ?? "";
                        });
                        form.pharmacyType = form.pharmacyType ?? 0;
                    });

                    //如果开启了限制条件
                    if (this.innerState.enableDoctorPractice) {
                        //增加两个参数，isPrescriptionTip：处方精麻标记，0：未标记，1：已标记，药品增加了成分字段ingredient：药品成分位/多选 0x01 精1 0x02 精2 0x04麻 0x08 毒 0x10放 0x20 麻黄碱
                        _detailData?.prescriptionWesternForms?.map((form) => {
                            form.prescriptionFormItems?.forEach((formItem) => {
                                Object.assign(formItem, {
                                    ingredient: formItem.goodsInfo.dangerIngredient,
                                });
                            });
                            Object.assign(form, {
                                isPrescriptionTip: !!form.psychotropicNarcoticType ? 1 : 0,
                            });
                        });
                    }

                    return CDSSAgent.outpatientVerify(_detailData!, this.innerState.isBeijingSupervise).catchIgnore().toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) {
                    this.innerState.aiVerify = undefined;
                    return;
                }
                this.innerState.aiVerify = rsp;
                this.innerState.airPharmacyRisk = false;
                const needCheckSignList = this.innerState.aiVerify?.checkItems?.map((item) => item);
                const needCheckSignFitnessList = this.innerState.aiVerify?.verifyItems?.FITNESS?.map((ai) => ai);
                const compareSignStr = needCheckSignList?.map((t) => t.__originDetail).join("");
                const compareSignFitnessStr = needCheckSignFitnessList?.map((t) => t.__originDetail).join("");

                this.innerState.needDoubleSign = false; // 没有禁忌警告时，不需要强制签名

                needCheckSignList?.map((item) => {
                    for (const form of this.innerState.detailData?.prescriptionChineseForms ?? []) {
                        for (const formItem of form.prescriptionFormItems ?? []) {
                            //判断当前药品是否有存在禁忌警告
                            const needCheckSign =
                                (!!formItem.goodsId && compareSignStr?.includes(formItem.goodsId ?? "")) ||
                                (!!formItem.keyId && compareSignStr?.includes(formItem.keyId ?? ""));
                            //判断当前药品已经签名
                            if (needCheckSign) {
                                if (!formItem.verifySignatures?.length) this.innerState.needDoubleSign = true;
                                if (form.isAirPharmacy && item.pharmacyType) this.innerState.airPharmacyRisk = true;
                            }
                            if (this.innerState.isEditing && !needCheckSign) {
                                if (!!formItem.verifySignatures?.length) {
                                    formItem.verifySignatures = [];
                                }
                            }
                        }
                    }
                });

                needCheckSignFitnessList?.map((item) => {
                    for (const form of this.innerState.detailData?.prescriptionWesternForms ?? []) {
                        for (const formItem of form.prescriptionFormItems ?? []) {
                            //判断当前药品是否有存在禁忌警告
                            const needCheckSign =
                                (!!formItem.goodsId && compareSignFitnessStr?.includes(formItem.goodsId ?? "")) ||
                                (!!formItem.keyId && compareSignFitnessStr?.includes(formItem.keyId ?? ""));

                            //判断当前药品已经签名
                            if (needCheckSign) {
                                if (!formItem.verifySignatures?.length) this.innerState.needDoubleSign = true;
                                if (form.isAirPharmacy && item.pharmacyType) this.innerState.airPharmacyRisk = true;
                            }
                            if (this.innerState.isEditing && !needCheckSign) {
                                if (!!formItem.verifySignatures?.length) {
                                    formItem.verifySignatures = [];
                                }
                            }
                        }
                    }
                    for (const form of this.innerState.detailData?.prescriptionInfusionForms ?? []) {
                        for (const formItem of form.prescriptionFormItems ?? []) {
                            //判断当前药品是否有存在禁忌警告
                            const needCheckSign =
                                (!!formItem.goodsId && compareSignFitnessStr?.includes(formItem.goodsId ?? "")) ||
                                (!!formItem.keyId && compareSignFitnessStr?.includes(formItem.keyId ?? ""));
                            //判断当前药品已经签名
                            if (needCheckSign) {
                                if (!formItem.verifySignatures?.length) this.innerState.needDoubleSign = true;
                                if (form.isAirPharmacy && item.pharmacyType) this.innerState.airPharmacyRisk = true;
                            }
                            if (this.innerState.isEditing && !needCheckSign) {
                                if (!!formItem.verifySignatures?.length) {
                                    formItem.verifySignatures = [];
                                }
                            }
                        }
                    }
                });
                this.update();
            })
            .addToDisposableBag(this);

        /**
         * 计算中药处方加工费
         */
        this._calculateChineseProcessingFeeTrigger
            .pipe(
                switchMap(() => {
                    const chineseForms = this.innerState.detailData?.prescriptionChineseForms?.filter((t) => !t?.pharmacyType);
                    if (!chineseForms?.length) return of(null);
                    return ChargeAgent.outpatientCalculateProcess(chineseForms!).catchIgnore().toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                rsp.forms?.forEach((form) => {
                    this.innerState.detailData?.prescriptionChineseForms?.forEach((item) => {
                        if (form?.keyId == item?.keyId) {
                            item.processPrice = form.processPrice;
                        }
                    });
                });
                this.update();
            })
            .addToDisposableBag(this);

        /**
         * 更新供应商
         */
        this._loadVendorTrigger
            .pipe(
                switchMap((form) => {
                    return ChargeAgent.getVendors({
                        airPharmacyFormItems: form.prescriptionFormItems,
                        doseCount: form.doseCount!,
                        goodsTypeId: ChineseMedicineSpecType.typeFromName(form.specification!)
                            ? ChineseGoodType.chineseGranule
                            : ChineseGoodType.chinesePiece,
                        medicineStateScopeId: form.medicineStateScopeId!,
                        usageScopeId: form.usageScopeId!,
                        vendorId: form.vendorId!,
                        pharmacyNo: form.pharmacyNo,
                    })
                        .then((rsp) => {
                            return {
                                newForm: rsp,
                                oldForm: form,
                            };
                        })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((assign) => {
                if (!assign) return;
                const rsp = assign.newForm;
                let prescriptionForm = assign.oldForm;
                if (rsp) {
                    rsp.forEach((item) => {
                        const currentVendor =
                            !!item?.vendorUsageScopeId && !!prescriptionForm?.vendorUsageScopeId
                                ? item?.vendorUsageScopeId == prescriptionForm?.vendorUsageScopeId
                                : item.vendorId == prescriptionForm?.vendorId && item.vendorName == prescriptionForm?.vendorName;
                        if (currentVendor) {
                            const _orderItems = item.orderItems?.map((item) => JsonMapper.deserialize(PrescriptionFormItem, item));
                            prescriptionForm.vendor = item;
                            //vendor中的processBagUnitCount只针对颗粒剂，其他类型不能覆盖，否则导致其他类型药态显示有问题
                            prescriptionForm = Object.assign(
                                prescriptionForm,
                                !item.calculateProcessBagType
                                    ? {
                                          ...item,
                                          processBagUnitCount: prescriptionForm.processBagUnitCount,
                                          dailyDosage: prescriptionForm?.dailyDosage,
                                          freq: prescriptionForm?.freq,
                                          totalProcessCount: prescriptionForm?.totalProcessCount,
                                          usage: prescriptionForm?.usage,
                                          usageLevel: prescriptionForm?.usageLevel,
                                          keyId: prescriptionForm?.keyId,
                                          doseCount: prescriptionForm?.doseCount,
                                          specification: prescriptionForm?.specification,
                                      }
                                    : item
                            );
                            prescriptionForm.vendorUsageScopeId = item.vendorUsageScopeId;
                            prescriptionForm.vendorId = item.vendorId;
                            prescriptionForm.vendorName = item.vendorName;
                            //在接诊处总价totalPrice只代表药品总价，不算加工费和辅料费(此处必须克隆，不然完诊算费会有问题)
                            prescriptionForm.totalPrice = _.clone(item.goodsTotalPrice);
                            for (const formItem of _orderItems ?? []) {
                                if (!formItem) continue;
                                formItem.goodsId = formItem.productInfo?.id;
                                formItem.productId = formItem.productInfo?.id;
                                formItem.type = formItem.productInfo?.type;
                                formItem.subType = formItem.productInfo?.subType;
                                formItem.name = formItem.productInfo?.displayName ?? formItem.name;
                                formItem.unit = formItem.unit ?? "g";
                                formItem.medicineCadn = formItem.productInfo?.medicineCadn;
                                const _formItem = _.findIndex(
                                    prescriptionForm.prescriptionFormItems,
                                    (i) => i.displayName == formItem.displayName
                                );
                                if (_.isNumber(_formItem)) {
                                    prescriptionForm.prescriptionFormItems![_formItem] = _.merge(
                                        prescriptionForm.prescriptionFormItems![_formItem],
                                        formItem
                                    );
                                } else {
                                    prescriptionForm.prescriptionFormItems?.push(formItem);
                                }
                            }
                            //在服用天数不存在的情况下，再去根据用法计算服用天数,避免服用天数没办法自定义
                            if (!prescriptionForm.usageDays) {
                                this._modifyChinesePrescriptionUsage(prescriptionForm);
                            }
                        }
                    });
                    this.update();
                }
            })
            .addToDisposableBag(this);

        this._getAirPharmacyLogisticsTrace
            .pipe(
                switchMap((formId) => {
                    return OutpatientAgent.getPrescriptionFormLogisticsTrace(formId)
                        .then((rsp) => ({
                            data: rsp,
                            formId,
                        }))
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                const { data, formId } = rsp;
                this.innerState.detailData?.prescriptionChineseForms?.forEach((item) => {
                    if (!item.isAirPharmacy) return;
                    if (item?.id == formId) {
                        item.deliveryInfo = item?.deliveryInfo ?? new PrescriptionFormDelivery();
                        item.deliveryInfo!.__logisticTraceList = data;
                    }
                });
                this.update();
            })
            .addToDisposableBag(this);

        /**
         * 获取历史就诊次数
         */
        this._getHistoryListTrigger
            .pipe(
                switchMap((/*data*/) => {
                    const patientId = this.innerState.detailData?.patient?.id;
                    if (!patientId) return of(null);

                    return OutpatientAgent.getOutpatientHistoryList(patientId);
                })
            )
            .subscribe(
                (patientSummaryList) => {
                    if (!patientSummaryList) return;
                    this.innerState.detailData!.diagnoseCount = patientSummaryList.totalCount;
                    this.update();
                },
                (error) => {
                    this.innerState.loading = false;
                    this.innerState.loadError = error;
                    this.update();
                }
            )
            .addToDisposableBag(this);

        /**
         * 锁单socket信息（门诊目前只显示门诊医嘱编辑处方以及收费支付锁单信息）
         */
        onlineMessageManager.patientOrderSheetLockMsgObserver.subscribe(async (data) => {
            if (data.key != this.innerState.detailData?.patientOrderId) return;
            if (data.businessKey != PatientOrderLockType.outpatientSheet && data.businessKey != PatientOrderLockType.chargeSheet) return;
            const businessKey = data.businessKey;
            if (!businessKey) return;
            if (data.businessKey == PatientOrderLockType.chargeSheet && data.status && !data.value?.chargeInProgress) return;
            let detail = this.innerState.lockDetails.get(businessKey);
            detail = Object.assign(detail ?? {}, data);
            this.innerState.lockDetails.set(businessKey, detail!);
            // 如果是收费解锁时，需要拉取一次详情接口，确保是最新数据
            if (data.businessKey == PatientOrderLockType.chargeSheet && !data.status) {
                await this._loadDataFromNetwork();
            }
            this.update();
        });
        OutpatientUtils.voiceRecordingAsrResult.subscribe((hasAsr) => {
            if (hasAsr) {
                if (this.outpatientId) {
                    Asr.getAsrResult(this.outpatientId, "").then((res) => {
                        this.innerState.voiceRecordResult = res;
                        this.update();
                    });
                }
            }
        });
        /////////////////////////////// 事件监听 ///////////////////////////////
    }

    @actionEvent(_EventAddMedicalRecordTemplate)
    async *_mapEventAddMedicalRecordTemplate(): AsyncGenerator<State> {
        const tp: PrescriptionTemplate = await OutpatientPrescriptionTemplateSearchDialog.show(false, {
            templateType: OutpatientTemplateType.medicalrecord,
        });

        if (!tp) return;
        //诊疗相关的是打平到tp那一层
        const medicalRecord = JsonMapper.deserialize(MedicalRecord, tp);

        //复制病历时：
        //需要校验：主诉、现病史、体格检查、诊断、过敏史是否已填
        //弹窗提示插入or覆盖
        let _addType = MedicineTemplateAddType.reset;
        const labelStr = MedicalRecord.keyList
            .map((item) => {
                if (!!this.innerState.detailData?.medicalRecord?.[item.key]) return `"${item.label}"`;
            })
            .filter((item) => !!item)
            .join(" ");
        if (!!labelStr) {
            _addType = await MedicineTemplateAddTypeDialog.show({
                title: `已填写${labelStr}`,
                content: "插入到已填写内容中，还是覆盖已填写内容？",
                buttonType: [
                    { name: "覆盖", value: MedicineTemplateAddType.reset },
                    { name: "插入", value: MedicineTemplateAddType.push },
                ],
            });
        }
        if (!_addType) {
            return;
        } else if (_addType == MedicineTemplateAddType.push) {
            medicalRecord?.assignObj(this.innerState.detailData?.medicalRecord);
        }

        //复制病历模板时：
        // 若病历模板中填写了既往史，复制模板时覆当前病历全部内容；
        // 若病历模板中未填写既往史，复制模板时，不覆盖既往史（其余内容还是覆盖）
        const { pastHistory, allergicHistory, familyHistory, personalHistory, epidemiologicalHistory } =
            this.innerState.detailData?.medicalRecord ?? {};
        medicalRecord.pastHistory = !!pastHistory ? pastHistory : medicalRecord.pastHistory;
        medicalRecord.allergicHistory = !!allergicHistory ? allergicHistory : medicalRecord.allergicHistory;
        medicalRecord.familyHistory = !!familyHistory ? familyHistory : medicalRecord.familyHistory;
        medicalRecord.personalHistory = !!personalHistory ? personalHistory : medicalRecord.personalHistory;
        medicalRecord.epidemiologicalHistory = !!epidemiologicalHistory ? epidemiologicalHistory : medicalRecord.epidemiologicalHistory;

        this.innerState.detailData!.medicalRecord = medicalRecord;
        this.update();
    }

    @actionEvent(_EventUpdateClinicType)
    async *_mapEventUpdateClinicType(event: _EventUpdateClinicType): AsyncGenerator<State> {
        this.innerState.hospitalDetail = event?.info;
        this.update();
    }

    /**
     * 病历模板添加
     */
    requestAddMedicalRecordTemplate(): void {
        if (!this.innerState.allowEditMedicalRecord) return;
        this.dispatch(new _EventAddMedicalRecordTemplate());
    }

    @actionEvent(_EventPushOrderToPatient)
    async *_mapEventPushOrderToPatient(event: _EventPushOrderToPatient): AsyncGenerator<State> {
        if (!event.chargeSheetId) return;
        const result = await showQueryDialog("", "将推送至患者微信，是否确认推送？", undefined, undefined, undefined, "rgba(0,0,0,0.9)");
        if (result == DialogIndex.positive) {
            //推送
            ChargeAgent.pushOrdertoPatient(event.chargeSheetId)
                .then(() => {
                    Toast.show("推送成功", { success: true });
                })
                .catch((e) => {
                    showConfirmDialog("", errorSummary(e));
                });
        }
        this.update();
    }

    @actionEvent(_EventPreOutpatient)
    async *_mapEventPreOutpatient(): AsyncGenerator<State> {
        if (!this.innerState.detailData) return;
        if (_.isNil(this.innerState.detailData!.patient?.name) || !this.innerState.detailData!.patient?.age?.displayAge) {
            return Toast.show("请完善患者信息");
        } else if (!this.innerState.detailData!.doctorName) {
            return Toast.show("请完善医生信息");
        }

        const result = await showQueryDialog(
            `提交预诊后主诊医生 ${this.innerState.detailData?.doctorName ?? "未知"} 可查看到预诊内容`,
            "主诊医生可在预诊内容基础上继续完善病历和医嘱，高效完成接诊"
        );

        if (result == DialogIndex.positive) {
            const req = JsonMapper.deserialize(OutpatientInvoiceDetail, {
                ...this.innerState.detailData,
                action: 2,
            });
            OutpatientAgent.updateOutpatientInvoice(req, async () => {
                //删除本地草稿--避免预诊单无法更新
                const draftId = this.innerState.detailData?.draftId ?? this.draftId;
                !!draftId && (await this._draftManager().removeDraft(draftId));
            }).then(() => {
                this._loadDataFromNetwork();
            });
        }
    }

    @actionEvent(_EventFreshInit)
    async *_mapEventFreshInit(event: _EventFreshInit): AsyncGenerator<State> {
        if (event.needConform && this.hasChanged) {
            const result = await showQueryDialog("提示", "患者病历已发生修改，是否取消修改？");
            if (result != DialogIndex.positive) {
                return;
            }
        }
        this._loadDataFromNetwork();
    }

    @actionEvent(_EventViewReportDetail)
    async *_mapEventViewReportDetail(event: _EventViewReportDetail): AsyncGenerator<State> {
        if (!event.examinationSheetId) return;
        await ABCNavigator.navigateToPage(<OphthalmicReport examinationSheetId={event.examinationSheetId} type={event.type} />, {
            transitionType: TransitionType.inFromBottom,
        });
        this.update();
    }

    @actionEvent(_EventRepeatAppointment)
    async *_mapEventRepeatAppointment(event: _EventRepeatAppointment): AsyncGenerator<State> {
        await ABCNavigator.navigateToPage(
            <DentistryInvoicePage
                patient={event.patient}
                doctorId={event.doctorId}
                departmentId={event.departmentId}
                source={RegistrationPageSourceType.again}
                assistantList={this.innerState.doctorsList?.map((it) => it.doctorId ?? "")}
            />
        );
    }

    @actionEvent(_EventCheckExpressInfo)
    async *_mapEventCheckExpressInfo(event: _EventCheckExpressInfo): AsyncGenerator<State> {
        if (!event?.formId) return;
        const result = await OutpatientAgent.getPrescriptionFormLogisticsTrace(event.formId).catchIgnore();
        if ((result?.traceList?.length ?? 0) == 0) return;
        //更新门诊单页面的物流信息，以防数据不同步
        const airPharmacyForm = this.innerState.detailData?.prescriptionChineseForms?.filter((t) => t?.isAirPharmacy);
        airPharmacyForm?.map((item) => {
            if (item?.id == event.formId) item.deliveryInfo!.__logisticTraceList = result;
            return item;
        });
        yield this.innerState;
        const logisticTranceData: StepStatusListItem[] = [];
        result?.traceList?.map((item, index) => {
            logisticTranceData.push({
                id: index,
                title: item?.context ?? "",
                complete: index == 0,
                date: item?.ftime ?? "",
            });
        });
        await AirPharmacyStepsView.show({
            data: [
                {
                    stepList: logisticTranceData,
                },
            ],
            subTitle: event?.companyInfo ?? "",
        });
        this.update();
    }

    @actionEvent(_EventShowInfectiousDiseasesDetail)
    async *_mapEventShowInfectiousDiseasesDetail(/*event: _EventShowInfectiousDiseasesDetail*/): AsyncGenerator<State> {
        const infectiousDiseasesDetail = this.innerState.detailData?.medicalRecord?.includeInfectiousDiseasesList;
        if (!infectiousDiseasesDetail?.hasInfectiousDiseases) return;
        await InfectiousDiseasesDetailDialog.show(infectiousDiseasesDetail);
    }

    @actionEvent(_EventDeleteDraft)
    async *_mapEventDeleteDraft(): AsyncGenerator<State> {
        if (!!this.innerState.detailData?.draftId) await this._draftManager().removeDraft(this.innerState.detailData.draftId);
        this.hasChanged = false;
        ABCNavigator.pop();
        return;
    }

    update(): void {
        this.dispatch(new _EventUpdate());
    }

    requestModify(): void {
        this.dispatch(new _EventModifyTap());
    }

    // 复制病历或处方
    requestNavToHistoryDialog(result: OutpatientHistoryDialogResult): void {
        this.dispatch(new _EventNavToHistoryDialog(result));
    }

    // 添加图片
    requestAddImage(): void {
        if (!this.innerState.isEditing) return;
        this.dispatch(new _EventAddImage());
    }

    // 删除草稿并返回
    requestDeleteDraft(): void {
        this.dispatch(new _EventDeleteDraft());
    }

    @actionEvent(_EventModifyRevisitStatus)
    private async *_mapEventModifyRevisitStatus(/*event: _EventModifyRevisitStatus*/): AsyncGenerator<State> {
        const initIndex = this.innerState?.detailData?.revisitStatus ?? RegistrationRevisitStatus.first;
        const result = await showOptionsBottomSheet({
            title: "初/复诊",
            options: ["初诊", "复诊"],
            initialSelectIndexes: initIndex ? new Set<number>([initIndex - 1]) : undefined,
            height: pxToDp(240),
            showConfirmBtn: false,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (result && result.length) {
            this.innerState.detailData!.revisitStatus = result[0] + 1;
            if (OutpatientUtils.canEditWithChargeStatus(this.innerState.detailData?.registrationFeeStatus)) {
                this.registerFeeCalculation();
            }

            //修改初复诊状态时 保存配置文件
            if (
                this.innerState.isDentistryClinic &&
                this.innerState.outpatientConfig!.revisit != this.innerState.detailData!.revisitStatus
            ) {
                this.innerState.outpatientConfig!.revisit = this.innerState.detailData!.revisitStatus;
                this._updateOutpatientConfig.next();
            }

            this.hasChanged = true;
            this.update();
        }
    }

    /**
     * 切换初诊/复诊
     */
    public requestModifyRevisitStatusAndCategories(): void {
        this.dispatch(new _EventModifyRevisitStatusAndCategories());
    }

    /**
     * 删除附件(图片)
     */
    requestRemoveImage(index: number): void {
        this.dispatch(new _EventRemoveImage(index));
    }

    /**
     * 删除门诊单
     */
    requestDeleteOutpatient(): void {
        if (!this.isEditing) return;
        this.dispatch(new _EventDeleteOutpatient());
    }

    /**
     * 清空患者信息
     */
    requestClearPatient(): void {
        if (!this.isEditing) return;
        this.dispatch(new _EventClearPatient());
    }

    /**
     * 修改患者信息
     */
    requestEditPatientInfo(info: Patient): void {
        this.dispatch(new _EventEditPatientInfo(info));
    }

    /**
     * 医生选择
     */
    requestSelectDoctor(): void {
        if (!this.isEditing) return;
        this.dispatch(new _EventSelectDoctor());
    }

    @actionEvent(_EventModifyRevisitStatusAndCategories)
    private async *_mapEventModifyRevisitStatusAndCategories(): AsyncGenerator<State> {
        const { canChangeRevisitedStatus, isEditing, detailData, enableRegistrationCategories, doctorRegistrationCategoriesList } =
                this.innerState,
            { revisitStatus = RegistrationRevisitStatus.first, registrationCategory = 0, chargeStatus } = detailData ?? {};
        const doctorMutable = isEditing && OutpatientUtils.canEditWithChargeStatus(chargeStatus);
        const _options = [
            {
                title: "初/复诊",
                disable: !canChangeRevisitedStatus || !doctorMutable,
                crossAxisCount: 2,
                options: [
                    {
                        label: "初诊",
                        value: 1,
                    },
                    {
                        label: "复诊",
                        value: 2,
                    },
                ],
            },
        ];
        if (enableRegistrationCategories) {
            _options.push({
                title: "号种",
                disable: false,
                crossAxisCount: 3,
                options: doctorRegistrationCategoriesList.map((item) => ({
                    label: item.registrationCategoryDisplay,
                    value: item.registrationCategory ?? 0,
                })),
            });
        }
        const result = await AbcDialog.sheetMenuGroupDialog({
            options: _options,
            selectedOptions: [{ value: revisitStatus }, { value: registrationCategory }],
        });
        if (!result) return;
        const [rS, rC] = result;
        Object.assign(this.innerState.detailData, {
            revisitStatus: rS.value,
            registrationCategory: rC.value ?? registrationCategory,
        });
        if (OutpatientUtils.canEditWithChargeStatus(this.innerState.detailData?.registrationFeeStatus)) {
            this.registerFeeCalculation();
        }
        if (rS.value != revisitStatus) {
            //修改初复诊状态时 保存配置文件
            if (this.innerState.isDentistryClinic && this.innerState.outpatientConfig!.revisit != rS.value) {
                this.innerState.outpatientConfig!.revisit = rS.value;
                this._updateOutpatientConfig.next();
            }

            this.hasChanged = true;
        }
        this.update();
    }

    /**
     * 切换初诊/复诊
     */
    public requestModifyRevisitStatus(): void {
        this.dispatch(new _EventModifyRevisitStatus());
    }

    requestUpdateRegistrationFee(fee: number): void {
        if (!this.isEditing) return;
        if (fee == this.innerState.detailData?.registrationFee) return;
        this.dispatch(new _EventUpdateRegistrationFee(fee));
    }

    /**
     * 主诉
     */
    saveChiefComplaintInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveChiefComplaintInfo());
    }

    /**
     * 发病时间
     */
    requestChangeOnsetTime(): void {
        if (!this.isEditing || this.innerState.disableUpdateMedicalRecord) return;
        this.dispatch(new _EventChangeOnsetTimeInfo());
    }

    /**
     * 现病史
     */
    savePresentHistoryInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSavePresentHistoryInfo());
    }

    /**
     * 既往史
     */
    savePastHistoryInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSavePastHistoryInfo());
    }

    /**
     * 过敏史
     */
    saveAllergicHistoryInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveAllergicHistoryInfo());
    }

    /**
     * 个人史
     */
    savePersonalHistoryInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventPersonalHistoryInfo());
    }

    /**
     * 诊断
     */
    saveDiagnosisInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveDiagnosisInfo());
    }

    /**
     * 牙科
     */
    requestSaveDentistryDiagnosisInfo(item?: ExtendDiagnosisInfosItem, index?: number): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveDentistryDiagnosisInfo(item, index));
    }

    /**
     * 月经婚育史
     */
    saveObstetricalHistoryInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveObstetricalHistoryInfo());
    }

    /**
     * 辨证
     */
    saveSyndromeInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveSyndromeInfo());
    }

    /**
     * 治法
     */
    saveTherapyInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveTherapyInfo());
    }

    /**
     * 方药
     */
    saveChinesePrescriptionInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveChinesePrescriptionInfo());
    }

    /**
     * 体格检查
     */
    savePhysicalExaminationInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSavePhysicalExaminationInfo());
    }

    /**
     * 望闻问切
     */
    saveChineseExaminationInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveChineseExaminationInfo());
    }

    /**
     * 舌象
     */
    saveTongueInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveTongueInfo());
    }

    /**
     * 脉象
     */
    savePulseInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSavePulseInfo());
    }

    /**
     * 普通门店-口腔检查
     */
    saveOralExaminationInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveDefaultOralExaminationInfo());
    }

    /**
     * 辨证论治
     */
    saveSyndromeTreatmentInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveSyndromeTreatmentInfo());
    }

    private _asyncMedicalRecordToothNos(): void {
        const { asyncToothNosMap, detailData } = this.innerState;
        const medicalRecord = detailData?.medicalRecord ?? new MedicalRecord();
        OutpatientUtils.DentistryMedicalRecordKey.forEach((key) => {
            asyncToothNosMap.forEach((toothNos, index) => {
                if (!medicalRecord[key]) medicalRecord[key] = [];
                if (!medicalRecord[key]![index]) medicalRecord[key]![index] = new DentistryMedicalRecordItem();
                medicalRecord[key]![index].toothNos = toothNos;
            });
        });
        detailData!.medicalRecord = medicalRecord;
    }

    /**
     * 家族史
     */
    requestSaveFamilyHistoryInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveFamilyHistoryInfo());
    }

    /**
     * 流行病史
     */
    requestSaveEpidemiologicalHistoryInfo(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveEpidemiologicalHistoryInfo());
    }

    /**
     * 治疗计划
     */
    requestSaveTreatmentPlansInfo(item?: DentistryMedicalRecordItem, index?: number): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveTreatmentPlansInfo(item, index));
    }

    /**
     * 处置
     */
    requestSaveDisposalsInfo(item?: DentistryMedicalRecordItem, index?: number): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveDisposalsInfo(item, index));
    }

    /**
     * 口腔检查
     */
    requestSaveOralExaminationInfo(item?: DentistryMedicalRecordItem, index?: number): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            this.innerState.disableUpdateMedicalRecord ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit
        )
            return;
        this.dispatch(new _EventSaveOralExaminationInfo(item, index));
    }

    /**
     * 诊疗项目
     * @param params
     * @param fromAddBtn   -- 是否从新增按钮点击进入，用于门诊设置完诊后新增医嘱判断
     * @returns
     */
    requestAddProduct(params?: { item?: PrescriptionFormItem; fromAddBtn?: boolean }): void {
        const { item, fromAddBtn } = params || {};
        // 完诊后新增医嘱--超出可编辑范围时，未支付的处方允许修改、删除内容，已支付的不可编辑；超出限制时间范围后，新增都不允许
        // 每项无未收费
        const allCharged = !this.innerState.detailData?.productForms?.every(
            (t) => t.chargeStatus != ChargeFormStatus.charged || t.isPartChargeFee
        );
        if (!this.innerState.isEditing || (this.innerState.disableAddPrescription && (fromAddBtn || allCharged))) return;
        this.dispatch(new _EventAddProductInfo(item));
    }

    /**
     * 医嘱备注
     */
    requestUpdateDoctorAdvices(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            !this.innerState.notLockingIsEditing ||
            this.innerState.needBackToEdit ||
            this.innerState.disableUpdateMedicalRecord
        )
            return;
        this.dispatch(new _EventUpdateDoctorAdvices());
    }

    /**
     * 成药处方
     */
    requestAddPrescriptionWestern(): void {
        this.dispatch(new _EventAddPrescriptionWestern());
    }

    /**
     * 中药处方
     */
    requestAddPrescriptionChinese(): void {
        this.dispatch(new _EventAddPrescriptionChinese());
    }

    /**
     * 输/注处方
     */
    requestAddPrescriptionInfusion(): void {
        this.dispatch(new _EventAddPrescriptionInfusion());
    }

    /**
     * 外治处方
     */
    requestAddPrescriptionExternal(): void {
        this.dispatch(new _EventAddPrescriptionExternal());
    }

    /**
     * 处方模板
     */
    requestInvokeTemplate(): void {
        this.dispatch(new _EventInvoiceTemplate());
    }

    /**
     * 删除处方
     */
    requestDeletePrescription(
        prescriptionForm: PrescriptionChineseForm | PrescriptionInfusionForm | PrescriptionWesternForm | PrescriptionExternalForm
    ): void {
        this.dispatch(new _EventDeletePrescription(prescriptionForm));
    }

    requestModifyWesternMedicine(
        prescriptionWesternForm: PrescriptionWesternForm,
        item?: PrescriptionFormItem,
        showAstCheck?: boolean
    ): void {
        // 在允许修改处方时间范围内，已收费的不能修改；在允许修改时间范围外，已收费的不能修改
        if (!this.isEditing || !this.innerState.isItemCanEdit(prescriptionWesternForm.chargeStatus!)) return;

        this.dispatch(new _EventModifyWesternMedicine(prescriptionWesternForm, item, showAstCheck));
    }

    requestModifyChineseMedicine(prescriptionChineseForm: PrescriptionChineseForm, item?: PrescriptionFormItem): void {
        if (!this.isEditing || !this.innerState.isItemCanEdit(prescriptionChineseForm.chargeStatus!)) return;
        this.dispatch(new _EventModifyChineseMedicine(prescriptionChineseForm, item));
    }

    requestModifyInfusionMedicine(prescriptionInfusionForm: PrescriptionInfusionForm, item?: PrescriptionFormItem): void {
        if (!this.isEditing || !this.innerState.isItemCanEdit(prescriptionInfusionForm.chargeStatus!)) return;
        this.dispatch(new _EventModifyInfusionMedicine(prescriptionInfusionForm, item));
    }

    /**
     * 编辑外治处方
     * @param prescriptionExternalFormForm
     * @param item
     */
    requestModifyExternalMedicine(prescriptionExternalFormForm: PrescriptionExternalForm, item?: PrescriptionFormItem): void {
        if (!this.isEditing || !this.innerState.isItemCanEdit(prescriptionExternalFormForm.chargeStatus!)) return;
        this.dispatch(new _EventModifyExternalMedicine(prescriptionExternalFormForm, item));
    }

    /**
     * 费用预览
     */
    requestChargePreview(): void {
        if (!this.isEditing) return;
        this.dispatch(new _EventChargePreview());
    }

    /**
     * 智能审方
     */
    requestViewAiVerifyResult(): void {
        this.dispatch(new _EventViewAiVerifyResult());
    }

    /**
     * 智能审方双签
     */
    requestViewAiVerifyDoubleSignResult(confirmSign?: boolean): void {
        this.dispatch(new _EventViewVerifyDoubleSignResult(confirmSign));
    }

    submitChanges(): void {
        if (
            (this.innerState.outpatientLocking && this.innerState.lockDetail?.employeeId != userCenter.employee?.id) ||
            !this.innerState.notLockingIsEditing
        )
            return;
        if (this.innerState.orderIsLocking && this.innerState.canEditMedical) {
            // 只对病历进行保存
            this.dispatch(new _EventSaveMedical());
        } else {
            this.dispatch(new _EventFinishTap());
        }
    }

    ///---保存草稿(使用事件调用方式)---///

    requestBack(): void {
        this.dispatch(new _EventBack());
    }

    /**
     * 门诊设置
     * @param config
     */
    requestChangeSetting(config: OutpatientConfig): void {
        this.dispatch(new _EventChangeSetting(config));
    }

    /**
     * 病例附件-添加问诊单
     */
    requestAddQuestionSheet(): void {
        this.dispatch(new _EventAddQuestionSheet());
    }

    private async _createAnotherDetail(
        options: {
            needMedicalRecord: boolean;
            needPrescription: boolean;
        } = { needMedicalRecord: true, needPrescription: true }
    ) {
        const { needMedicalRecord, needPrescription } = options;
        // 创建一个新草稿
        const detailData = await OutpatientAgent.postCreateOnlineDraft({
            type: 0,
            patientId: this.innerState.detailData?.patient?.id,
        });
        // 将本地数据放入
        this.outpatientId = detailData.id;
        Object.assign(this.innerState.detailData ?? {}, {
            id: this.outpatientId,
            status: OutpatientInvoiceStatus.waitVisit,
            draftId: OutpatientDraftManager.generateDraftId(),
        });
        if (!needMedicalRecord) {
            Object.assign(this.innerState.detailData ?? {}, {
                medicalRecord: JsonMapper.deserialize(MedicalRecord, {}),
            });
        }
        if (!needPrescription) {
            Object.assign(this.innerState.detailData ?? {}, {
                productForms: [],
                prescriptionChineseForms: [],
                prescriptionWesternForms: [],
                prescriptionInfusionForms: [],
                prescriptionExternalForms: [],
            });
        }
        this._saveDraft();
    }

    /**
     * 初始化页面相关依赖配置
     * @private
     */
    private async _initPageConfig(): Promise<void> {
        ////////////////////////////初始化配置数据//////////////////////////////////////
        //--////////////////////////无需等待接口//////////////////////////////////
        GoodsUtils.initGoodsCustomUnitIfNeed().catch((/*error*/) => false); //初始化拉取自goods的自定义单位，在这里不使用，在界面上显示自定义理疗单位时要使用
        OnlinePropertyConfigProvider.instance.getClinicBasicSetting().then((rsp) => {
            this.innerState.clinicBasicSetting = rsp;
            this.innerState.covid19DetectionStatus = !!rsp.outpatient?.settings?.covid19Detection;
            MedicalRecordUtils.setDefaultWarnSymptoms(rsp.outpatient?.settings?.covid19Keyword ?? []);
            this.innerState.infectiousDiseasesStatus = !!rsp.outpatient?.settings?.infectiousDiseases;
            this.innerState.canChangeRevisitedStatus = !!rsp.outpatient?.settings?.canChangeRevisited;
            this.innerState.isEnableCA = rsp.isEnableCA == 1; // 门店是否开启CA电子签名
            this.innerState.isDangerPrescriptionMustSign = rsp.outpatient?.settings?.dangerPrescriptionMustSign == 1; // 门店是否开启风险处方强制签名
            // 门诊设置---读取病历修改时间、继续新增处方时间配置
            this.innerState.medicalRecordUpdateLimit = rsp.outpatient?.settings?.medicalRecordUpdateLimit;
            this.innerState.prescriptionUpdateLimit = rsp.outpatient?.settings?.prescriptionUpdateLimit;
            this.innerState.isOpenEditMedicalRecordDiagnosed = rsp?.outpatient?.settings?.isOpenEditMedicalRecordDiagnosed;
            this.innerState.isOpenEditPrescriptionDiagnosed = rsp?.outpatient?.settings?.isOpenEditPrescriptionDiagnosed;
        });
        Promise.all([
            OnlinePropertyConfigProvider.instance.getChargeConfig(false).catch((error) => new ABCError(error)),
            ChargeAgent.getUsageScopes().catch((error) => new ABCError(error)),
            userCenter.getInventoryChainConfig(false).catchIgnore(),
            ChargeAgent.canShowChargePush().catch((error) => new ABCError(error)),
            ClinicAgent.getAirPharmacyConfig(),
            ClinicAgent.getVirtualPharmacyConfig(),
            GoodsAgent.getPharmacyList().catchIgnore(),
            userCenter.isAllowedRegUpgrade
                ? DentistryAgent.queryDentistryRegistrationConfig(RegistrationType.outpatientRegistration, false)
                : undefined,
            ShebaoAgent.getClinicShebaoConfig(),
            RegistrationAgent.getRegistrationDoctorEnableCategories(),
        ])
            .toObservable()
            .subscribe((rsp) => {
                const [
                    chargeConfig,
                    usageList,
                    pharmacyInfoConfig,
                    isMeetpushPayment,
                    airPharmacyConfig,
                    virtualPharmacyConfig,
                    pharmacyConfig,
                    registrationConfig, // 获取预约配置
                    shebaoConfig, // 诊所shebao相关配置
                    registrationCategories, // 诊所号种相关配置
                ] = rsp;
                if (!(chargeConfig instanceof ABCError)) this.innerState.chargeConfig = chargeConfig;
                if (!(usageList instanceof ABCError)) this.innerState.usageList = usageList!;
                this.innerState.pharmacyInfoConfig = pharmacyInfoConfig;
                if (!(isMeetpushPayment instanceof ABCError)) this.innerState.isMeetpushPayment = isMeetpushPayment;
                this.innerState.airPharmacyConfig = airPharmacyConfig;
                this.innerState.virtualPharmacyConfig = virtualPharmacyConfig;
                this.innerState.pharmacyList = pharmacyConfig?.rows;
                this.innerState.registrationConfig = registrationConfig;
                this.innerState.clinicShebaoConfig = shebaoConfig;
                this.innerState.clinicRegistrationCategories = registrationCategories;
                this.update();
            })
            .addToDisposableBag(this);
        this.innerState.antimicrobialDrugConfig = await OutpatientAgent.getAntimicrobialDrugConfig().catchIgnore();
        this.innerState.outpatientRequiredConfig = await OnlinePropertyConfigProvider.instance
            .getChainBasicOutpatientSetting()
            .catchIgnore();
        // 必须在上一句代码之后，否则新用户初始进入与再次进入有些配置不一致
        this.innerState.outpatientConfig = await OnlinePropertyConfigProvider.instance.getOutpatientConfig();
        //--////////////////////////必须依赖接口//////////////////////////////////
        //员工配置-必须优先获取，解决显示价格显示问题
        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig(true).catchIgnore(); // 员工个人
        this.innerState.canAddQuestionForm = userCenter.clinicEdition?.checkFeaturePurcheased(FeatureAuthority.QUESTION_FORM);
        ////////////////////////////初始化数据//////////////////////////////////////
    }

    /**
     *  修改问诊单
     */
    requestModifyQuestionSheet(id: string): void {
        this.dispatch(new _EventModifyQuestionSheet(id));
    }

    /**
     *  完成预诊
     */
    requestPreOutpatient(): void {
        if (!this.isEditing) return;
        this.dispatch(new _EventPreOutpatient());
    }

    /**
     * 更新就诊类型
     * @param info
     */
    requestUpdateClinicType(info?: HospitalRegisterDetail): void {
        this.dispatch(new _EventUpdateClinicType(info));
    }

    requestModifyPatientGuardianDetail(value?: PatientGuardianDetail): void {
        this.dispatch(new _EventModifyPatientGuardianDetail(value));
    }

    /**
     * 推送支付（推送给患者）
     * @param chargeSheetId
     */
    requestPushOrderToPatient(chargeSheetId: string): void {
        this.dispatch(new _EventPushOrderToPatient(chargeSheetId));
    }

    /**
     * 重新加载门诊单
     */
    requestFreshInit(needConform = false): void {
        this.dispatch(new _EventFreshInit(needConform));
    }

    /**
     * 查看眼科检查报告
     */
    requestViewReportDetail(examinationSheetId?: string, type?: ReportType): void {
        this.dispatch(new _EventViewReportDetail(examinationSheetId, type));
    }

    /**
     * 复诊预约
     */
    requestRepeatAppointment(options: {
        patient?: Patient;
        doctorId?: string;
        departmentId?: string;
        revisitStatus?: RegistrationRevisitStatus;
    }): void {
        this.dispatch(new _EventRepeatAppointment({ ...options }));
    }

    /**
     * 空中药房物流信息
     */
    requestCheckExpressInfo(formId: string, companyInfo?: string): void {
        this.dispatch(new _EventCheckExpressInfo(formId, companyInfo));
    }

    /**
     * 查看传染病详情
     */
    requestShowInfectiousDiseasesDetail(): void {
        this.dispatch(new _EventShowInfectiousDiseasesDetail());
    }

    /**
     * 快速设置处方类型
     */
    requestChangePrescriptionType(detail: VerifyItemDetail): void {
        this.dispatch(new _EventChangePrescriptionType(detail));
    }
}

export { OutPatientInvoicePageBloc, State, ScrollToFocusItemState };
