import { HistoryPermissionModuleType, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ient, PatientTag } from "../../base-business/data/beans";
import React from "react";
import { Style, Text, View } from "@hippy/react";
import { ABCStyles, Color, Colors, flattenStyles, Sizes, TextStyles } from "../../theme";
import { AbcView } from "../../base-ui/views/abc-view";
import { OutpatientHistoryDialogResult } from "../outpatient-history-dialog";
import { BaseComponent } from "../../base-ui/base-component";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { AbcBasePanel, BottomSheetHelper } from "../../base-ui/abc-app-library";
import { CrmAgent } from "../../patients/data/crm-agent";
import OutpatientHistoryPage, { OutpatientHistoryPagType } from "../outpatient-history-page";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import _ from "lodash";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import PatientDetailEditPage from "../patient-edit-page/patient-edit-page";
import { AbcText } from "../../base-ui/views/abc-text";
import { PatientSearchPage } from "../patient-edit-page/patient-search-page";
import { AssetImageView } from "../../base-ui/views/asset-image-view";
import { AbcEmptyItemView } from "../../base-ui/views/empty-view";
import { userCenter } from "../../user-center";
import { AbcSvgTag, AbcTag } from "../../base-ui/tag/abc-tag";
import { Spacer } from "../../base-ui";
import { urlLauncher } from "../../base-business/url-launcher/url-launcher";
import { Toast } from "../../base-ui/dialog/toast";
import { PatientInfoMethod } from "../../base-business/data/patient-beans";
import { PatientTags, PatientTagsTypeList } from "../../patients/data/crm-bean";
import { Clinic, ModuleIds } from "../../user-center/user-center";

/**
 * 门诊可编辑患者卡片
 */
interface AbcPatientCardInfoViewProps {
    isEditing?: boolean;
    showErrorHint?: boolean;

    patient?: Patient;

    medicalRecord?: MedicalRecord;

    treatOnlineClinicId?: string;

    patientSwitchable?: boolean;

    requirePatientSource?: boolean; //患者来源是否必填项

    requireMobile?: boolean; //手机号是否必填项

    isOutpatient?: boolean; //是否门诊进入

    onChange?: (patient: Patient) => void;
    onCancel?(): void; //取消

    onClearPatient?(text: string): void; // 清空患者信息

    onChangePatientGender?(text: string): void; // 修改性别
    onChangePatientAge?(text: string): void; // 修改年龄
    onChangePatientMobile?(text: string): void; // 修改电话

    diagnoseCount?: number; // 历史就诊次数

    revisitStatusName?: string;

    onChangePatientHistory?: <T>(result: T) => void;

    /**
     * @description 底部面板拓展内容
     */
    extendView?(): JSX.Element | JSX.Element[];
    panelBgColor?: Color; //背景色
    textColor?: Color; //文字颜色
    lineColor?: Color; //线条颜色
    hideCopyHistory?: boolean;
    isCanSeePatientHistoryInCashier?: boolean; //收费-能否查看查看患者就诊历史
    isCanSeePatientHistoryInRegister?: boolean; //挂号预约-能否查看患者就诊历史
    isCanSeePatientHistoryInExecuteStation?: boolean; //执行站-能否查看患者就诊历史
    isCanSeePatientHistoryInPharmacy?: boolean; //药房-能否查看患者就诊历史
    type?: HistoryPermissionModuleType;
    canSeePatientMobileInRegister?: boolean; //挂号预约-能否查看患者手机号
    canSeePatientMobileInOutpatient?: boolean; //门诊-能否查看患者手机号
    canSeePatientMobileInCashier?: boolean; //收费-能否查看患者手机号、
    canSeePatientMobileInPharmacy?: boolean; //药房-能否查看患者手机号
    canSeePatientMobileInExecution?: boolean; // 执行站-能否查看患者手机号
    disableAddPrescription?: boolean; // 禁止复制病历（门诊配置--允许修改病历时间限制）
    disableUpdateMedicalRecord?: boolean; // 禁止复制处方（门诊配置--允许新增处方时间限制）

    canEditPatientInfo?: boolean; // 能否更改个人信息(默认是可以查看编辑个人信息,这个字段主要是为了收费锁单不可编辑添加的)
}

export default class AbcPatientCardInfoView extends React.Component<AbcPatientCardInfoViewProps> {
    _textInput?: AbcTextInput | null;
    protected _patientTagsTypeList?: PatientTagsTypeList[];

    static defaultProps = {
        isEditing: true,
        isShowLine: false,
    };

    constructor(props: AbcPatientCardInfoViewProps) {
        super(props);
    }

    componentDidMount(): void {
        const { patient, type } = this.props;
        if (userCenter.clinic?.isDentistryClinic && !patient?.id && type == HistoryPermissionModuleType.outpatient)
            this._renderSearchDialogView().then();
        //     获取患者标签列表
        CrmAgent.getPatientTagsTypeList().then((rsp) => {
            if (!!rsp && !!rsp?.length) this._patientTagsTypeList = rsp;
            this.setState({});
        });
    }

    async _renderSearchDialogView(): Promise<void> {
        const {
            requirePatientSource,
            requireMobile,
            type,
            canSeePatientMobileInRegister,
            canSeePatientMobileInOutpatient,
            canSeePatientMobileInCashier,
            canSeePatientMobileInPharmacy,
            canSeePatientMobileInExecution,
        } = this.props;
        const patient = await PatientSearchPage.show({
            showAll: true,
            requirePatientSource,
            requireMobile,
            type,
            canSeePatientMobileInRegister,
            canSeePatientMobileInOutpatient,
            canSeePatientMobileInCashier,
            canSeePatientMobileInPharmacy,
            canSeePatientMobileInExecution,
        });
        if (!patient) {
            if (userCenter.clinic?.isDentistryClinic) this.props?.onCancel?.();
            return;
        }
        if (patient.id) {
            const _patient = await CrmAgent.getPatientById(patient?.id).then();
            this.props.onChange?.(_patient);
        } else {
            this.props.onChange?.(patient);
        }
    }

    async _onClickModifyPatientInfo(): Promise<void> {
        const { patient, onChange } = this.props;
        let _patient: Patient | undefined = patient;
        if (patient?.id)
            await CrmAgent.getPatientById(patient?.id).then((patientRsp: Patient) => {
                _patient = _.merge(patient, patientRsp);
            });
        const _editInfo: Patient = await ABCNavigator.navigateToPage(
            <PatientDetailEditPage {...this.props} showAll={true} patient={_patient} />
        );
        if (_editInfo) {
            onChange?.(_editInfo);
        }
    }

    async _onClickCallPatient(): Promise<void> {
        if (urlLauncher.supportTel()) {
            await urlLauncher.tel(this.props.patient!.mobile!);
        } else {
            Toast.show("当前版本不支持拨打电话", { warning: true }).then();
        }
    }

    render(): JSX.Element {
        return (
            <AbcBasePanel panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}>
                <View style={{ flex: 1 }}>{this.renderPatientCard()}</View>
            </AbcBasePanel>
        );
    }

    protected renderPatientCard(): JSX.Element {
        const { extendView } = this.props;
        return (
            <View>
                {this.renderPatientView()}
                {extendView && extendView()}
            </View>
        );
    }

    protected renderPatientPhoneIconView(): JSX.Element {
        return <View />;
    }

    renderPatientStatusTagView(): JSX.Element {
        return <View style={ABCStyles.rowAlignCenter}></View>;
    }

    // 患者标签展示，首先处理标签数据方便查找，再兼容id、tagId这种后端问题，得到有效的患者标签
    showTags(): PatientTag[] {
        const { patient } = this.props;
        let tags: PatientTags[] = [];
        const newPatientTag: PatientTag[] = [];
        this._patientTagsTypeList?.forEach((item) => {
            if (item.tags) {
                tags = [...tags, ...item.tags];
            }
        });
        if (!!patient?.tags?.length) {
            patient?.tags.map((item) => {
                const target = tags?.find((one) => one.id === item.tagId);
                if (!!target) {
                    newPatientTag.push({ ...item, tagName: target.name });
                }
            });
        }
        return newPatientTag;
    }

    /**
     * tag 组
     */
    renderPatientTagInfoView(): JSX.Element {
        const { textColor } = this.props;
        // let _svgCount = 0,
        //     _tagCount = 0;
        return (
            <View style={[ABCStyles.rowAlignCenter, { flexWrap: "wrap" }]}>
                {this.showTags()?.map((it, index, self) => {
                    const { tagName, style, viewMode } = it;
                    const isLast = index == self.length - 1;
                    let views;
                    if (viewMode == 1 && !!style) {
                        // _svgCount++;
                        views = <AbcSvgTag key={index} theme={style?.shape} customTagConfig={{ ...style, size: 18 }} />;
                    } else {
                        // _tagCount++;
                        views = (
                            <AbcTag
                                key={index}
                                text={tagName}
                                style={[
                                    Sizes.paddingLTRB(Sizes.dp4, 0),
                                    Sizes.marginLTRB(0, 0),
                                    { borderColor: textColor ?? Colors.mainColor, borderRadius: Sizes.dp24 },
                                ]}
                                textStyle={[TextStyles.t10NT2.copyWith({ lineHeight: Sizes.dp16, color: textColor ?? Colors.mainColor })]}
                            />
                        );
                    }
                    // if (_svgCount + _tagCount > 4) return <View />;

                    return (
                        <View key={index} style={[!isLast ? { marginRight: Sizes.dp4 } : {}, { marginVertical: Sizes.dp2 }]}>
                            {views}
                        </View>
                    );
                })}
            </View>
        );
    }

    /**
     * 当前登录人是否有收费模块权限
     */
    hasChargePermission(): boolean {
        const moduleIds = userCenter.clinic!.moduleIds;
        const ids = moduleIds?.split(",").map((item) => parseInt(item)) ?? [];
        if (userCenter.clinic!.roleId == Clinic.ROLEID_MANAGER || ids?.indexOf(ModuleIds.MODULE_ID_NONE) >= 0) {
            return true;
        } else {
            return !!ids?.find((t) => t == ModuleIds.MODULE_ID_CHARGE || t == ModuleIds.hospitalChargeStation);
        }
    }

    /**
     * 口腔门诊重构显示内容
     * @protected
     */
    protected renderDentistryPatientBaseInfo(): JSX.Element {
        const { patient, showErrorHint } = this.props;
        const _showErrorHint = showErrorHint && !patient?.name;

        return (
            <AbcView onClick={!patient?.name ? () => this._renderSearchDialogView() : undefined}>
                <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                    <View
                        style={[
                            !patient?.name && _showErrorHint ? ABCStyles.bottomLine : {},
                            {
                                paddingBottom: _showErrorHint ? Sizes.dp5 : undefined,
                                backgroundColor: _showErrorHint ? Colors.errorBorderBg : undefined,
                                borderBottomColor: _showErrorHint ? Colors.errorBorder : Colors.dividerLineColor,
                                flexShrink: 1,
                            },
                        ]}
                    >
                        <AbcText
                            style={[
                                !!patient?.name ? TextStyles.t18MT1 : TextStyles.t18NT4.copyWith({ color: Colors.t4 }),
                                { lineHeight: Sizes.dp25, flexShrink: 1 },
                            ]}
                            numberOfLines={1}
                        >
                            {!!patient?.name ? patient?.name : "患者姓名"}
                        </AbcText>
                    </View>
                    {!!patient?.isMember && (
                        <AssetImageView
                            name={"charge_invoice_patient_member"}
                            style={{ marginLeft: Sizes.dp4, width: Sizes.dp20, height: Sizes.dp20 }}
                        />
                    )}
                    {this.hasChargePermission() && !!patient?.hasArrearsOfFees && (
                        <AssetImageView name={"charge_owe"} style={{ marginLeft: Sizes.dp4, width: Sizes.dp20, height: Sizes.dp20 }} />
                    )}
                    {!!patient?.name && (
                        <View style={ABCStyles.rowAlignCenter}>
                            <AbcText
                                style={[
                                    !!patient?.name ? TextStyles.t16NT1 : TextStyles.t16NT1.copyWith({ color: Colors.t4 }),
                                    { lineHeight: Sizes.dp22, marginLeft: Sizes.dp8 },
                                ]}
                            >
                                {!!patient?.sex ? patient?.sex : "--"}
                            </AbcText>
                            <AbcText
                                style={[
                                    !!patient?.name ? TextStyles.t16NT1 : TextStyles.t16NT1.copyWith({ color: Colors.t4 }),
                                    { lineHeight: Sizes.dp22, marginLeft: Sizes.dp8 },
                                ]}
                            >
                                {!!patient?.age?.displayAgeIncludeMonth ? patient?.age?.displayAgeIncludeMonth : "--"}
                            </AbcText>
                        </View>
                    )}
                    {this.renderPatientStatusTagView()}
                </View>
                {this.renderPatientTagInfoView()}
            </AbcView>
        );
    }

    //渲染患者手机号
    renderPatientMobileView(): JSX.Element {
        const { patient, textColor } = this.props;
        return (
            <AbcText
                style={[TextStyles.t16NT1, !!textColor ? { color: textColor } : {}, { lineHeight: Sizes.dp25, marginLeft: Sizes.dp8 }]}
            >
                {PatientInfoMethod.canSeePatientMobile({ ...this.props })
                    ? patient?.mobile ?? ""
                    : PatientInfoMethod.encryptThePhoneNumber(patient?.mobile)}
            </AbcText>
        );
    }

    protected renderPatientBaseInfo(showMobile = false): JSX.Element {
        if (!showMobile && userCenter.clinic?.isDentistryClinic) {
            return this.renderDentistryPatientBaseInfo();
        }

        const { patient, showErrorHint } = this.props;

        const _showErrorHint = showErrorHint && !patient?.name;
        return (
            <AbcView style={ABCStyles.rowAlignCenter} onClick={!patient?.name ? () => this._renderSearchDialogView() : undefined}>
                <View
                    style={[
                        !patient?.name && _showErrorHint ? ABCStyles.bottomLine : {},
                        {
                            flexShrink: 1,
                            paddingBottom: _showErrorHint ? Sizes.dp5 : undefined,
                            backgroundColor: _showErrorHint ? Colors.errorBorderBg : undefined,
                            borderBottomColor: _showErrorHint ? Colors.errorBorder : Colors.dividerLineColor,
                        },
                    ]}
                >
                    <AbcText
                        style={[
                            !!patient?.name ? TextStyles.t18MT1 : TextStyles.t18NT4.copyWith({ color: Colors.t4 }),
                            { lineHeight: Sizes.dp25 },
                        ]}
                        numberOfLines={1}
                    >
                        {!!patient?.name ? patient?.name : "患者姓名"}
                    </AbcText>
                </View>
                {!!patient?.isMember && (
                    <AssetImageView
                        name={"charge_invoice_patient_member"}
                        style={{ marginLeft: Sizes.dp4, width: Sizes.dp20, height: Sizes.dp20 }}
                    />
                )}
                {!!patient?.name && (
                    <View style={ABCStyles.rowAlignCenter}>
                        <AbcText
                            style={[
                                !!patient?.name ? TextStyles.t18NT1 : TextStyles.t18NT4.copyWith({ color: Colors.t4 }),
                                { lineHeight: Sizes.dp25, marginLeft: Sizes.dp8 },
                            ]}
                        >
                            {!!patient?.sex ? patient?.sex : "--"}
                        </AbcText>
                        <AbcText
                            style={[
                                !!patient?.name ? TextStyles.t18NT1 : TextStyles.t18NT4.copyWith({ color: Colors.t4 }),
                                { lineHeight: Sizes.dp25, marginLeft: Sizes.dp8 },
                            ]}
                        >
                            {!!patient?.age?.displayAgeIncludeMonth ? patient?.age?.displayAgeIncludeMonth : "--"}
                        </AbcText>
                        {this.renderPatientMobileView()}
                        <Spacer />
                        {this.renderPatientPhoneIconView()}
                    </View>
                )}
            </AbcView>
        );
    }

    private renderPatientView(): JSX.Element {
        const {
            patient,
            isEditing,
            patientSwitchable = !!patient?.isHasPatient && !!isEditing,
            revisitStatusName,
            treatOnlineClinicId,
            onChangePatientHistory,
            diagnoseCount,
            panelBgColor,
            textColor,
            lineColor,
            isOutpatient = false,
            hideCopyHistory = !isEditing,
            isCanSeePatientHistoryInCashier = true,
            isCanSeePatientHistoryInRegister = true,
            isCanSeePatientHistoryInExecuteStation = true,
            isCanSeePatientHistoryInPharmacy = true,
            type,
            disableUpdateMedicalRecord,
            disableAddPrescription,
            canEditPatientInfo = true,
        } = this.props;
        //各个模块权限控制就诊历史的显示与否
        let _isCanSeePatientHistory = true;
        switch (type) {
            case HistoryPermissionModuleType.registration:
                _isCanSeePatientHistory = isCanSeePatientHistoryInRegister;
                break;
            case HistoryPermissionModuleType.execution:
                _isCanSeePatientHistory = isCanSeePatientHistoryInExecuteStation;
                break;
            case HistoryPermissionModuleType.cashier:
                _isCanSeePatientHistory = isCanSeePatientHistoryInCashier;
                break;
            case HistoryPermissionModuleType.pharmacy:
                _isCanSeePatientHistory = isCanSeePatientHistoryInPharmacy;
            default:
                break;
        }
        return (
            <View>
                <View style={Sizes.paddingLTRB(Sizes.dp16)}>{this.renderPatientBaseInfo()}</View>

                {!!patient?.name && (
                    <View style={[ABCStyles.rowAlignCenter, { height: Sizes.dp39, backgroundColor: panelBgColor ?? Colors.mainColor_05 }]}>
                        {!!patient?.isHasPatient && _isCanSeePatientHistory && (
                            <AbcView style={{ flex: 1, paddingVertical: Sizes.dp11 }}>
                                <ConsultationHistoryButton
                                    patient={patient}
                                    revisitStatusName={revisitStatusName}
                                    enableEdit={isEditing}
                                    treatOnlineClinicId={treatOnlineClinicId}
                                    hideCopyHistory={hideCopyHistory}
                                    diagnoseCount={patient.id ? diagnoseCount : undefined}
                                    onChange={(result) => {
                                        onChangePatientHistory?.(result);
                                    }}
                                    textStyles={{ lineHeight: Sizes.dp39, color: textColor ?? Colors.mainColor }}
                                    isCanCheckPrescription={isOutpatient}
                                    disableUpdateMedicalRecord={disableUpdateMedicalRecord}
                                    disableAddPrescription={disableAddPrescription}
                                />
                            </AbcView>
                        )}
                        {!!patient?.isHasPatient && isCanSeePatientHistoryInCashier && (
                            <View style={{ width: Sizes.dp1, height: Sizes.dp20, backgroundColor: lineColor ?? Colors.mainColor_20 }} />
                        )}
                        <AbcView style={{ flex: 1 }} onClick={() => canEditPatientInfo && this._onClickModifyPatientInfo()}>
                            <Text
                                style={[
                                    TextStyles.t14NM.copyWith({ color: textColor ?? Colors.mainColor }),
                                    { textAlign: "center", lineHeight: Sizes.dp39 },
                                ]}
                            >
                                {"信息详情"}
                            </Text>
                        </AbcView>
                        {patientSwitchable && (
                            <View style={{ width: Sizes.dp1, height: Sizes.dp20, backgroundColor: lineColor ?? Colors.mainColor_20 }} />
                        )}
                        {patientSwitchable && (
                            <AbcView
                                style={{ flex: 1 }}
                                onClick={() => {
                                    this._renderSearchDialogView();
                                }}
                            >
                                <Text
                                    style={[
                                        TextStyles.t14NM.copyWith({ color: textColor ?? Colors.mainColor }),
                                        { textAlign: "center", lineHeight: Sizes.dp39 },
                                    ]}
                                >
                                    {"更换"}
                                </Text>
                            </AbcView>
                        )}
                    </View>
                )}
            </View>
        );
    }
}

/**
 * 患者就诊历史
 */
interface ConsultationHistoryButtonProps {
    patient?: Patient;
    revisitStatusName?: string; // 初复诊状态
    enableEdit?: boolean;

    hideCopyHistory?: boolean; //是否隐藏复制历史病历按钮，在enableEdit为true时才生效

    treatOnlineClinicId?: string; //网诊单中复制使用医生所在诊所

    onChange?: (result: OutpatientHistoryDialogResult) => void;

    diagnoseCount?: number; // 历史就诊次数

    textStyles?: Style | Style[];

    isCanCheckPrescription?: boolean;
    disableAddPrescription?: boolean; // 禁止复制病历（门诊配置--允许修改病历时间限制）
    disableUpdateMedicalRecord?: boolean; // 禁止复制处方（门诊配置--允许新增处方时间限制）
}

/**
 * 就诊历史面板
 */
class ConsultationHistoryButton extends BaseComponent<ConsultationHistoryButtonProps> {
    async _navToHistoryDialog(): Promise<void> {
        const {
            patient,
            revisitStatusName,
            enableEdit,
            hideCopyHistory,
            onChange,
            treatOnlineClinicId,
            isCanCheckPrescription,
            disableAddPrescription,
            disableUpdateMedicalRecord,
        } = this.props;

        if (!patient?.id) {
            return showBottomPanel(
                <View style={{ flex: 1 }}>
                    {BottomSheetHelper.createTitleBar(`就诊历史(0)`)}
                    <AbcEmptyItemView tips={"暂无就诊历史"} />
                </View>,
                { topMaskHeight: Sizes.dp160 }
            );
        }

        const result: OutpatientHistoryDialogResult = await showBottomPanel(
            <OutpatientHistoryPage
                type={OutpatientHistoryPagType.visitHistory}
                patient={patient}
                revisitStatusName={revisitStatusName}
                viewOnly={!enableEdit}
                hideCopyHistory={hideCopyHistory}
                treatOnlineClinicId={treatOnlineClinicId}
                isCanCheckPrescription={isCanCheckPrescription}
                disableAddPrescription={disableAddPrescription}
                disableUpdateMedicalRecord={disableUpdateMedicalRecord}
            />,
            { topMaskHeight: Sizes.dp160 }
        );
        if (result) {
            onChange?.(result);
        }
    }

    render(): JSX.Element {
        const { diagnoseCount, textStyles } = this.props;

        return (
            <AbcView
                style={ABCStyles.rowAlignCenter}
                onClick={() => {
                    this._navToHistoryDialog().then();
                }}
            >
                <Text style={[TextStyles.t14NM, { flex: 1, textAlign: "center" }, flattenStyles(textStyles)]} numberOfLines={1}>
                    {`就诊历史(${diagnoseCount ?? 0})`}
                </Text>
            </AbcView>
        );
    }
}
