/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */
import { ConversationChatViewBloc, FetchDataState } from "./conversation-chat-view-bloc";
import { ChatView } from "../chat-view/chat-view";
import React from "react";
import { BaseBlocComponent } from "../../../../base-ui/base-component";
import { Text, View } from "@hippy/react";
import { AssetImageView } from "../../../../base-ui/views/asset-image-view";
import { TextStyles } from "../../../../theme";
import { LoadingView } from "../../../../base-ui/views/loading-view";
import { AbcTextInput } from "../../../../base-ui/views/abc-text-input";
import { LogUtils } from "../../../../common-base-module/log";
import { ChatMessage } from "../chat-view/model/chat-message";
import { delayed } from "../../../../common-base-module/rxjs-ext/rxjs-ext";
import { ConversationChatToolbar } from "./conversation-chat-toolbar";
import { ImagePicker } from "../../../../base-business/image-picker/image-picker";
import { userCenter } from "../../../../user-center";
import { OssUpdateModules } from "../../../../base-business/data/beans";

interface ConversationChatViewProps {
    bloc: ConversationChatViewBloc;

    isFinished?: boolean;

    absolutPanelBuilder?: () => JSX.Element;

    expandPanelBuilder(): JSX.Element;

    onAddImageToMedicalRecord?(url: string): void;

    onRevokeMessage?(message: ChatMessage): void;
}
export class ConversationChatView extends BaseBlocComponent<ConversationChatViewProps, ConversationChatViewBloc> {
    private _chatView: ChatView | null = null;
    private _toolbar: ConversationChatToolbar | null = null;
    constructor(props: ConversationChatViewProps) {
        super(props);
        this.bloc = this.props.bloc;
    }

    componentDidMount(): void {
        super.componentDidMount();

        this.bloc.state.subscribe((state) => {
            if (state.loadMsgError) {
                this._chatView?.refreshCompleted();
            }

            if (state instanceof FetchDataState) {
                this._chatView?.refreshCompleted();
                if (state.previousMessageCount) {
                    delayed(100)
                        .subscribe(() => {
                            this._chatView?.scrollToIndex(state.previousMessageCount!, true);
                        })
                        .addToDisposableBag(this);
                }
            }
        });
    }
    protected takeBlocOwnership(): boolean {
        return false;
    }

    /**
     */
    public async requestSendImage(): Promise<boolean> {
        const info = await ImagePicker.pickImageAndUpload(null, `${userCenter.clinic?.clinicId ?? ""}/${OssUpdateModules.IM}`);
        if (!info) return false;

        const { currentChatUser } = this.bloc.currentState;
        const msg = new ChatMessage({
            image: info.url,
            imageSize: info.size,
            user: currentChatUser,
            text: "",
            createdAt: new Date(),
        });

        await this.onSend(msg);
        return true;
    }

    public async requestSendText(text: string): Promise<void> {
        const { currentChatUser } = this.bloc.currentState;
        const msg = new ChatMessage({
            text: text,
            user: currentChatUser,
            createdAt: new Date(),
        });

        await this.onSend(msg);
    }

    public hideExpandPanel(animation: boolean): Promise<void> {
        return this._toolbar?.hideExpandPanel(animation) ?? Promise.resolve();
    }

    renderContent(): JSX.Element {
        const { onAddImageToMedicalRecord, onRevokeMessage, isFinished } = this.props;
        const { messages, currentChatUser, chatMessageCacheDir, hasMoreMessage } = this.bloc.currentState;
        LogUtils.d("ConversationChatView.renderContent hasMoreMessage = ", hasMoreMessage, ", messages = ", messages.length);
        return (
            <ChatView
                ref={(ref) => (this._chatView = ref)}
                messages={messages}
                user={currentChatUser}
                inputBar={this._renderInputBar()}
                onMessageListTouchDown={this._onMessageListTouchDown.bind(this)}
                cacheDir={chatMessageCacheDir}
                onReachEnd={() => this.bloc.requestLoadMore()}
                onRefresh={() => this._onRefresh()}
                dragToRefreshView={hasMoreMessage ? undefined : () => this.dragToRefreshView()}
                refreshView={hasMoreMessage ? undefined : () => this.refreshView()}
                onAddImageToMedicalRecord={onAddImageToMedicalRecord}
                onRevokeMessage={onRevokeMessage}
                onRedoRevokeMessage={this._onRedoRevokeMessage.bind(this)}
                isFinish={isFinished}
            />
        );
    }

    private dragToRefreshView() {
        const hasPreConversion = this.bloc.currentState.hasPreConsultation();
        return (
            <View
                style={{
                    height: 32,
                    justifyContent: "center",
                    alignItems: "center",
                    flexDirection: "row",
                }}
            >
                <AssetImageView name={"direction_down"} style={{ width: 14, height: 14 }} />
                <Text style={TextStyles.t14NT4}>{hasPreConversion ? "下拉查看上次咨询记录" : "没有更多咨询记录了"}</Text>
            </View>
        );
    }

    private refreshView(): JSX.Element {
        return (
            <View
                style={{
                    height: 32,
                    justifyContent: "center",
                    alignItems: "center",
                    flexDirection: "row",
                }}
            >
                <LoadingView size={14} style={{ marginRight: 6 }} />
                <Text style={TextStyles.t12NT4}>加载上次记录</Text>
            </View>
        );
    }

    _renderInputBar(): JSX.Element {
        const state = this.bloc.currentState;
        return (
            <ConversationChatToolbar
                ref={(ref) => (this._toolbar = ref)}
                onSend={this.onSend.bind(this)}
                user={state.currentChatUser}
                onFocus={() => this._onTextInputFocus()}
                onPanelExpand={() => this._onPanelExpand()}
                absolutPanelBuilder={this.props.absolutPanelBuilder}
                expandPanelBuilder={this.props.expandPanelBuilder}
                isFinish={this.props.isFinished}
            />
        );
    }

    _onMessageListTouchDown(): void {
        if (this._toolbar) {
            this._toolbar.hideExpandPanel();
            AbcTextInput.focusInput?.blur();
        }
    }
    _onRefresh(): void {
        const hasPreConsultation = this.bloc.innerState.hasPreConsultation();
        LogUtils.d("_onRefresh hasPreConsultation = " + hasPreConsultation);
        if (!hasPreConsultation) {
            this._chatView?.refreshCompleted();
            return;
        }

        this.bloc.requestPreConversionMgs();
    }

    async onSend(msg: ChatMessage): Promise<void> {
        this.bloc.requestSendMessage(msg);

        await delayed(300);

        this._chatView?.scrollToIndex(0);
    }

    private _onTextInputFocus() {
        delayed(300).subscribe((/*ignored*/) => {
            this._chatView?.scrollToIndex(0, true);
        });
    }

    //工具栏上扩展面板显示出来了
    private _onPanelExpand() {
        this._chatView?.scrollToIndex(0, true);
    }

    private _onRedoRevokeMessage(message: ChatMessage) {
        if (!!message.obj?.body?.text) {
            this._toolbar?._textInput?.setValue(message.obj.body.text, { shouldChange: true });
            this._toolbar?._textInput?.focus();
        }
    }
}
