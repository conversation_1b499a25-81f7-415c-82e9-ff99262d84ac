/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/11/24
 *
 * @description 随访，患者在线沟通
 */
import { BaseBlocNetworkPage } from "../base-ui/base-page";
import React from "react";
import { Text, View } from "@hippy/react";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";
import { ConversationChatView } from "../outpatient/views/chat/conversation-chat-view/conversation-chat-view";
import { BaseComponent } from "../base-ui/base-component";
import { IconFontView, SizedBox, Spacer } from "../base-ui";
import { GridView } from "../base-ui/views/grid-view";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { QuickReplayBottomSheet } from "../outpatient/chat-page/quick-replay-bottom-sheet";
import { CommonReplyMsgType } from "../outpatient/data/im-agent";
import { urlLauncher } from "../base-business/url-launcher/url-launcher";
import { Toast } from "../base-ui/dialog/toast";
import { PatientChatPageBloc } from "./patient-chat-page-bloc";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { PatientInfoWithTags } from "./views/patient-info-with-tags";
import { RegistrationNearestCreated } from "../registration/data/bean";
import { AbcButton } from "../base-ui/views/abc-button";
import { userCenter } from "../user-center";
import PatientDetailEditPage from "../outpatient/patient-edit-page/patient-edit-page";

interface PatientConversationPageProps {
    patientId: string;
    conversationId?: string;
}

export class PatientChatPage extends BaseBlocNetworkPage<PatientConversationPageProps, PatientChatPageBloc> {
    private _chatView: ConversationChatView | null = null;

    constructor(props: PatientConversationPageProps) {
        super(props);
        this.bloc = new PatientChatPageBloc(props.patientId, props.conversationId);
    }

    getAppBarTitle(): string {
        return "微信沟通";
    }

    getRightAppBarIcons(): JSX.Element[] {
        return [
            <Text
                key={0}
                style={TextStyles.t14NT6}
                onClick={() => {
                    if (this.bloc.currentState.patient)
                        ABCNavigator.navigateToPage(
                            <PatientDetailEditPage patient={this.bloc.currentState.patient} patientSwitchable={false} />
                        );
                    else Toast.show("未找找到患者信息", { warning: true });
                }}
            >
                详情
            </Text>,
        ];
    }

    getBottomSafeAreaColor(): Color {
        return Colors.window_bg;
    }

    getBackgroundColor(): Color {
        return "#EBEBEB";
    }

    renderContent(): JSX.Element {
        const regNearestCreatedDetail = this.bloc.currentState.regNearestCreatedDetail;
        return (
            <View style={{ flex: 1, overflow: "hidden" }}>
                <PatientInfoWithTags patient={this.bloc.currentState.patient} showDetail={true} />
                {this.bloc.conversationChatViewBloc && (
                    <ConversationChatView
                        ref={(ref) => (this._chatView = ref)}
                        bloc={this.bloc.conversationChatViewBloc}
                        absolutPanelBuilder={() => {
                            return (
                                <View>
                                    <_RegNearestCreatedPanel
                                        detail={regNearestCreatedDetail}
                                        handleCancelReg={() => {
                                            this.bloc.requestCancelReg(regNearestCreatedDetail!.registrationSheetId!);
                                        }}
                                        handleQuicklyReg={() => {
                                            this.bloc.requestQuicklyReg(regNearestCreatedDetail?.registrationSheetId);
                                        }}
                                    />
                                </View>
                            );
                        }}
                        expandPanelBuilder={() => {
                            return <_ChatExpandPanel onClickItem={this._onClickItem.bind(this)} />;
                        }}
                        onRevokeMessage={(message) => this.bloc.requestRevokeMessage(message)}
                    />
                )}
            </View>
        );
    }

    private async _onClickItem(item: _ExpandPanelItem) {
        let needHideExpandPanel = false;

        switch (item.id) {
            case _ToolBoxItemId.image: {
                if ((await this._chatView!.requestSendImage()) ?? false) needHideExpandPanel = true;
                break;
            }
            case _ToolBoxItemId.quickReplay: {
                const result = await QuickReplayBottomSheet.show(CommonReplyMsgType.patientConsultation);
                if (result) {
                    this._chatView!.requestSendText(result.content).then();
                    needHideExpandPanel = true;
                }
                break;
            }
            case _ToolBoxItemId.call: {
                const { patient } = this.bloc.currentState;
                if (patient?.mobile == undefined) {
                    Toast.show("患者未设置电话号码", { warning: true }).then();
                    return;
                }
                urlLauncher.tel(patient!.mobile!).then();
                break;
            }
            case _ToolBoxItemId.quickReq: {
                this.bloc.requestQuicklyReg();
                break;
            }
            case _ToolBoxItemId.kanban: {
                break;
            }
        }

        if (needHideExpandPanel) await this._chatView!.hideExpandPanel(false);
    }
}

interface _ExpandPanelItem {
    title: string;
    icon: string;
    id: number;
    color?: Color; //icon颜色,
}

interface _ExpandPanelProps {
    onClickItem: (item: _ExpandPanelItem) => void;
}

enum _ToolBoxItemId {
    image,
    quickReplay,
    call,
    quickReq,
    kanban,
}

const kExpandPanelHeight = pxToDp(216);

///扩展面板
class _ChatExpandPanel extends BaseComponent<_ExpandPanelProps> {
    private _items: _ExpandPanelItem[];
    private readonly _itemVies: JSX.Element[];

    constructor(props: _ExpandPanelProps) {
        super(props);

        this._items = [
            {
                title: "图片",
                icon: "pic",
                id: _ToolBoxItemId.image,
            },
            {
                title: "快速回复",
                icon: "message_common",
                id: _ToolBoxItemId.quickReplay,
            },

            ...(urlLauncher.supportTel()
                ? [
                      {
                          title: "电话沟通",
                          icon: "phone_call_glyph",
                          id: _ToolBoxItemId.call,
                      },
                  ]
                : []),
        ];

        if (userCenter.clinic?.isDentistryClinic) {
            this._items.push(
                ...[
                    {
                        title: "快速预约",
                        icon: "appointment",
                        id: _ToolBoxItemId.quickReq,
                    },
                    {
                        title: "预约看板",
                        icon: "board_1",
                        id: _ToolBoxItemId.kanban,
                    },
                ]
            );
        }

        this._itemVies = this._items.map((item) => {
            return (
                <View key={item.id} style={{ alignItems: "center" }} onClick={() => this.props.onClickItem(item)}>
                    <View
                        style={{
                            backgroundColor: Colors.white,
                            width: Sizes.dp48,
                            height: Sizes.dp48,
                            borderRadius: Sizes.dp12,
                            justifyContent: "center",
                            alignItems: "center",
                        }}
                    >
                        <IconFontView
                            name={item.icon}
                            style={{ width: Sizes.dp24, height: Sizes.dp24 }}
                            color={item.color ?? Colors.mainColor}
                        />
                    </View>
                    <Text style={[{ marginTop: 6 }, TextStyles.t12NT2]}>{item.title}</Text>
                </View>
            );
        });
    }

    render() {
        return (
            <GridView
                crossAxisCount={4}
                itemWidth={Sizes.dp87}
                mainAxisSpacing={Sizes.dp24}
                itemHeight={Sizes.dp76}
                style={{
                    height: kExpandPanelHeight,
                    paddingTop: Sizes.dp24,
                    paddingBottom: Sizes.dp16,
                }}
            >
                {this._itemVies}
            </GridView>
        );
    }
}

interface _RegNearestCreatedPanelProps {
    detail?: RegistrationNearestCreated;

    handleCancelReg?(): void;

    handleQuicklyReg?(): void;
}

interface _RegNearestCreatedPanelStates {
    showDetail: boolean;
}

class _RegNearestCreatedPanel extends BaseComponent<_RegNearestCreatedPanelProps, _RegNearestCreatedPanelStates> {
    constructor(props: _RegNearestCreatedPanelProps) {
        super(props);
    }

    handleCancelReg(): void {
        this.props.handleCancelReg?.();
    }

    render(): JSX.Element {
        const { detail } = this.props;
        if (!detail) return <View />;
        const { reserveDate, reserveStart, reserveEnd, doctorName, registrationProducts } = detail;
        return (
            <View
                style={[
                    {
                        backgroundColor: Colors.window_bg,
                        paddingHorizontal: Sizes.dp16,
                        paddingVertical: Sizes.dp12,
                    },
                ]}
            >
                <View style={[{ backgroundColor: Colors.white, borderRadius: Sizes.dp6, padding: Sizes.dp12 }]}>
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp20 })}>预约申请：</Text>
                        <Text style={TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp20 })}>
                            {`${reserveDate} ${reserveStart}~${reserveEnd}`}
                        </Text>
                    </View>
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text numberOfLines={1} style={[TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp20 }), { flexShrink: 1 }]}>
                            {`${doctorName}  ${registrationProducts?.map((it) => it.displayName).join("、")}`}
                        </Text>
                        <Spacer />
                        <View style={[ABCStyles.rowAlignCenter]}>
                            <AbcButton
                                style={{
                                    width: Sizes.dp62,
                                    padding: 0,
                                    backgroundColor: Colors.white,
                                    borderColor: Colors.mainColor,
                                    borderWidth: Sizes.dp1,
                                }}
                                textStyle={TextStyles.t14NM.copyWith({ lineHeight: Sizes.dp26 })}
                                text={"拒绝"}
                                onClick={this.handleCancelReg.bind(this)}
                            />
                            <SizedBox width={Sizes.dp8} />
                            <AbcButton
                                style={{ width: Sizes.dp62, padding: 0, backgroundColor: Colors.mainColor }}
                                textStyle={TextStyles.t14NW.copyWith({ lineHeight: Sizes.dp26 })}
                                text={"去预约"}
                                onClick={() => {
                                    this.props.handleQuicklyReg?.();
                                }}
                            />
                        </View>
                    </View>
                </View>
            </View>
        );
    }
}
