/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/6/12
 */
import React from "react";
import { IconFontView, SizedBox, Spacer, <PERSON>lB<PERSON>, ToolBarButtonStyle2 } from "../base-ui";
import { StyleSheet, Text, View } from "@hippy/react";
import { DrugInvoicePageBloc, ScrollToFocusItemState } from "./drug-invoice-page-bloc";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../base-ui/base-page";
import { BlocBuilder } from "../bloc";
import {
    AuditedStatus,
    CompoundedStatus,
    DispensingForm,
    DispensingStatus,
    DrugOperationRecordDetail,
    GetDrugInvoiceDetail,
    operationTypeEnum,
    ProcessedStatus,
} from "./data/pharmacy-bean";
import { ChargeSourceFormType } from "../charge/data/charge-beans";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";
import _, { cloneDeep } from "lodash";
import { ABCUtils } from "../base-ui/utils/utils";
import { ListSettingItemStyle } from "../base-ui/views/list-setting-item";
import { DispensingDrugChineseFormView } from "./dispensing-drug-chinese-form-view";
import { DispensingDrugWesternFormView } from "./dispensing-drug-western-form-view";
import { AbcButton } from "../base-ui/views/abc-button";
import { ExpressDeliveryInformationItem } from "./views/pharmacy-company-list";
import { AbcView } from "../base-ui/views/abc-view";
import AbcPatientCardInfoView from "../outpatient/views/new-patient-Info-view";
import { AbcBasePanel } from "../base-ui/abc-app-library";
import { TextStyle } from "../theme/text-styles";
import { AbcText } from "../base-ui/views/abc-text";
import { DialogIndex, showQueryDialog } from "../base-ui/dialog/dialog-builder";
import { StringUtils } from "../base-ui/utils/string-utils";
import { AbcScrollView } from "../base-ui/views/abc-scroll-view";
import { AirPharmacyOrderIdView } from "../views/air-pharmacy-order-id-view";
import { ScrollEvent } from "../base-ui/ui-events";
import { AbcPopMenu, MenuItem } from "../base-ui/views/pop-menu";
import { TreeDotView } from "../base-ui/iconfont/iconfont-view";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { Employee, HistoryPermissionModuleType } from "../base-business/data/beans";
import { ItemCardSelection, ItemCardSelectionProps } from "../base-ui/selection/item-card-selection";
import { AbcSet } from "../base-ui/utils/abc-set";
import { Toast } from "../base-ui/dialog/toast";
import { AbcBannerTips } from "../base-ui/abc-banner-tips/abc-banner-tips";
import { DeviceUtils } from "../base-ui/utils/device-utils";

interface DrugInvoicePageProps {
    chargeId: string;
}

interface DrugInvoicePageStates {
    selected: boolean;
}

const kFirstChild = "DrugInvoicePage.firstChild";
const kLastChild = "DrugInvoicePage.lastChild";

//@ts-ignore
export const DrugInvoicePageStyles = StyleSheet.create({
    activeButtonStyle: {
        ...Sizes.paddingLTRB(Sizes.dp8, Sizes.dp4),
        backgroundColor: Colors.white,
        borderWidth: Sizes.dpHalf,
    },
    disableButtonStyle: {
        ...Sizes.paddingLTRB(Sizes.dp8, Sizes.dp4),
        backgroundColor: Colors.P5,
        borderWidth: 0,
    },
});

enum PharmacyMenuItemValue {
    //退药
    refund = 1,
}

export class DrugInvoicePage extends BaseBlocNetworkPage<DrugInvoicePageProps, DrugInvoicePageBloc, DrugInvoicePageStates> {
    private _scrollView?: AbcScrollView | null;
    private scrollEnvY = 0;

    constructor(props: DrugInvoicePageProps) {
        super(props);
        this.bloc = new DrugInvoicePageBloc(props.chargeId);
        this.state = {
            selected: false,
        };
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    getAppBarTitle(): string {
        return "发药单";
    }

    getStatusBarColor(): Color {
        return Colors.panelBg;
    }

    getAppBarBgColor(): Color {
        return Colors.panelBg;
    }

    getBottomSafeAreaColor(): Color {
        return Colors.transparent;
    }

    renderAbcButton(options: { key: string; title: string; isDisableStyle?: boolean; onClick?: () => void }): JSX.Element {
        const { key, title, isDisableStyle, onClick } = options;
        return (
            <AbcButton
                style={{
                    height: Sizes.dp30,
                    minWidth: Sizes.dp36,
                    backgroundColor: isDisableStyle ? Colors.bdColor : Colors.mainColor,
                }}
                key={key}
                onClick={() => onClick?.()}
                pressColor={isDisableStyle ? undefined : Colors.mainColorPress}
            >
                <Text style={[TextStyles.t14MS2, ABCStyles.rowAlignCenter]} numberOfLines={1}>
                    {title}
                </Text>
            </AbcButton>
        );
    }
    private _createMenuItemsList(): MenuItem<number>[] {
        const menuItems: MenuItem<number>[] = [];
        const state = this.bloc.currentState;

        if (state.invoiceDetail?.isWaiting && state?.isPartDispensing) {
            menuItems.push(
                new MenuItem({
                    value: PharmacyMenuItemValue.refund,
                    icon: <AssetImageView name={"pharmacy_trash"} style={{ width: Sizes.dp22, height: Sizes.dp22, bottom: -Sizes.dp1 }} />,
                    text: "退药",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        return menuItems;
    }
    pharmacySettingIcons(): JSX.Element[] {
        const state = this.bloc.currentState;
        const _key = 0;
        if (state.invoiceDetail == undefined) return [<View key={_key} />];
        let isDisableRefund = false; // 部分发药支持退药下，此时无可退项，禁用退药按钮
        for (const item of state.invoiceDetail?.dispensingForms ?? []) {
            const firstDispensedItem = item.dispensingFormItems?.find(
                (formItem) => formItem.hasDispensingItem || item.sourceFormType == ChargeSourceFormType.package || formItem.isWaiting
            );
            if (!_.isEmpty(firstDispensedItem)) {
                isDisableRefund = true;
                break;
            }
        }

        const menuItems: MenuItem<number>[] = this._createMenuItemsList();

        if (_.isEmpty(menuItems)) return [<View key={_key} />];
        return [
            <View
                key={_key}
                style={{ paddingHorizontal: Sizes.dp8 }}
                collapsable={false}
                onClick={async () => {
                    const select = await AbcPopMenu.show(
                        menuItems,
                        { x: Sizes.dp24, y: Sizes.dp42 },
                        { x: Sizes.dp82, y: Sizes.dp42 },
                        { fullGrid: true }
                    );
                    switch (select) {
                        case PharmacyMenuItemValue.refund: {
                            if (isDisableRefund) this.bloc.requestRefundMedicine();
                            break;
                        }
                    }
                }}
            >
                <TreeDotView color={Colors.T1} />
            </View>,
        ];
    }
    getRightAppBarIcons(): JSX.Element[] {
        const state = this.bloc.currentState;
        const views: JSX.Element[] = [];

        // 以下用于判断发药按钮是否显示disable样式
        const cloneInvoiceDetail = _.cloneDeep(state.invoiceDetail);
        cloneInvoiceDetail?.dispensingForms?.forEach((forms) => {
            forms.dispensingFormItems?.forEach((item) => {
                _.remove(item.composeChildren ?? [], (item) =>
                    cloneInvoiceDetail.isVirtualPharmacy ? !item.virtualChecked : !item.checked
                );
            });
            _.remove(
                forms.dispensingFormItems ?? [],
                (item) =>
                    (cloneInvoiceDetail.isVirtualPharmacy ? !item.virtualChecked : !item.checked) &&
                    (forms.sourceFormType != ChargeSourceFormType.package || !item.composeChildren?.length)
            );
        });
        _.remove(cloneInvoiceDetail?.dispensingForms ?? [], (item) => !item.dispensingFormItems?.length);
        const isLockingOrder = state.orderIsLocking;
        if (state.invoiceDetail?.isWaiting) {
            views.push(
                <View style={[ABCStyles.rowAlignCenter]}>
                    <View key={"setting"} style={{ marginRight: Sizes.dp8 }}>
                        {this.pharmacySettingIcons()}
                    </View>
                    {this.renderAbcButton({
                        key: "dispensing",
                        title: "发药",
                        onClick: () => {
                            !isLockingOrder && !!cloneInvoiceDetail?.dispensingForms?.length && this.bloc.requestDispensingDrug();
                        },
                        isDisableStyle: isLockingOrder || !cloneInvoiceDetail?.dispensingForms?.length,
                    })}
                </View>
            );
        } else if (state.invoiceDetail?.isDispensed) {
            views.push(
                this.renderAbcButton({
                    key: "refund",
                    title: "退药",
                    onClick: () => !isLockingOrder && this.bloc.requestRefundMedicine(),
                    isDisableStyle: isLockingOrder,
                })
            );
        } else if (state.invoiceDetail?.isUnDispensed && !!state.invoiceDetail?.supportReDispense) {
            views.push(
                this.renderAbcButton({
                    key: "reDispensing",
                    title: "重新发药",
                    onClick: () => !isLockingOrder && this.bloc.requestReDispensing(),
                    isDisableStyle: isLockingOrder,
                })
            );
        } else {
            return [<View key={"null"} />];
        }
        return views;
    }

    renderReviewView(): JSX.Element {
        const dispensingForms = this.bloc.currentState.invoiceDetail?.dispensingForms;
        const underReview = !!dispensingForms?.some((item) => item.externalTrace?.length); // 审方按钮显示条件
        if (!underReview) return <View key={"underReview"} />;
        const underReviewCount = dispensingForms?.find((item) => item.externalTrace?.length);
        return (
            <AbcView
                key={"underReview"}
                style={[
                    ABCStyles.rowAlignCenter,
                    Sizes.paddingLTRB(Sizes.listHorizontalMargin, Sizes.dp12, Sizes.listHorizontalMargin, Sizes.dp12),
                    { backgroundColor: Colors.mainColor_08 },
                ]}
                onClick={() => {
                    this.setState({ selected: true });
                    this.bloc.requestCheckDispensingListStatus(() => {
                        this.setState({ selected: false });
                    });
                }}
            >
                <Text style={TextStyles.t14NM}>{`状态：${underReviewCount?.logisticsStatusStr}`}</Text>
                <Spacer />
                <IconFontView
                    name={"arrow_down"}
                    size={Sizes.dp12}
                    color={Colors.mainColor}
                    style={{ transform: [{ rotate: `${this.state.selected ? 180 : 0}deg` }] }}
                />
            </AbcView>
        );
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        const invoiceDetail = this.bloc.currentState.invoiceDetail;
        let statusName = "",
            statusNameStyle = {};
        if (invoiceDetail?.isDispensed) {
            statusName = "已发药";
        } else if (invoiceDetail?.isUnDispensed) {
            statusName = "已退药";
            statusNameStyle = TextStyles.t14NR2;
        } else if (invoiceDetail?.isRefundFee) {
            statusName = "已退费";
            statusNameStyle = TextStyles.t14NR2;
        } else if (invoiceDetail?.isCanceled) {
            statusName = "已关闭";
            statusNameStyle = TextStyles.t14NR2;
        }
        return (
            <BlocBuilder
                bloc={this.bloc}
                condition={() => true}
                build={() => (
                    <View style={ABCStyles.rowAlignCenter}>
                        <SizedBox width={Sizes.dp48} />
                        <Text style={TextStyles.t18NB}>{this.getAppBarTitle()}</Text>
                        {statusName != "" ? (
                            <Text
                                style={[
                                    TextStyles.t14NG2,
                                    statusNameStyle,
                                    {
                                        width: Sizes.dp48,
                                        textAlign: "center",
                                    },
                                ]}
                            >
                                {statusName}
                            </Text>
                        ) : (
                            <SizedBox width={Sizes.dp48} />
                        )}
                    </View>
                )}
            />
        );
    }

    // 滚动到最后一条
    private async _makeFocusItemVisible(): Promise<void> {
        this._scrollView?.scrollChildToVisible(kLastChild, kFirstChild, kLastChild);
    }

    componentDidMount(): void {
        super.componentDidMount();
        this.setContentStatus(ABCNetworkPageContentStatus.loading, null);
        const stateObserver = this.bloc.state.subscribe((state) => {
            let status = ABCNetworkPageContentStatus.show_data;
            let error: any;
            if (state.loading) {
                status = ABCNetworkPageContentStatus.loading;
            } else if (state.loadError) {
                status = ABCNetworkPageContentStatus.error;
                error = state.loadError;
            }
            this.setContentStatus(status, error);
        });

        // 监听滚动到指定位置
        this.bloc.state
            .subscribe(async (state) => {
                if (state instanceof ScrollToFocusItemState) {
                    await this._makeFocusItemVisible();
                }
            })
            .addToDisposableBag(this);

        this.addDisposable(stateObserver);
    }

    reloadData(): void {
        this.bloc.requestReloadData();
    }

    pharmacyServiceItemView(options: {
        title: string;
        contentStr: string;
        contentView?: JSX.Element;
        onClickStr: string;
        onClick?: () => void;
        onClickTextStyle?: TextStyle | TextStyle[];
        showBottomLine?: boolean;
        canShow?: boolean;
        canEdit?: boolean;
        showStatusBtn?: boolean; //是否显示状态按钮
        onCancel?: () => void;
        isShowRevokeBtn?: boolean;
    }): JSX.Element {
        const {
            title,
            contentStr,
            onClickStr,
            onClick,
            showBottomLine,
            canShow,
            canEdit,
            contentView,
            showStatusBtn = true,
            onCancel,
            isShowRevokeBtn,
        } = options;
        if (!canShow) return <View />;
        return (
            <View style={[ABCStyles.rowAlignCenter, showBottomLine ? ABCStyles.bottomLine : undefined, { paddingVertical: Sizes.dp10 }]}>
                <Text style={[TextStyles.t16NT2.copyWith({ color: Colors.t2 }), { marginRight: Sizes.dp16 }]}>{title}</Text>

                {contentView ? (
                    <View style={{ flex: 1, flexWrap: 1 }}>{contentView}</View>
                ) : (
                    <Text
                        style={[TextStyles.t14NT1, { flex: 1, marginRight: contentView ? Sizes.dp4 : Sizes.dp8 }, { flexShrink: 1 }]}
                        numberOfLines={1}
                    >
                        {contentStr}
                    </Text>
                )}
                {isShowRevokeBtn && (
                    <AbcView onClick={onCancel} style={{ marginRight: Sizes.dp8 }}>
                        <IconFontView name={"revoke"} size={Sizes.dp14} color={Colors.mainColor} />
                    </AbcView>
                )}
                {showStatusBtn && (
                    <AbcView
                        style={[
                            ABCStyles.rowAlignCenter,
                            Sizes.paddingLTRB(Sizes.dp8, Sizes.dp7, Sizes.dp8, Sizes.dp7),
                            { backgroundColor: !canEdit ? Colors.bdColor : Colors.mainColor_08, borderRadius: Sizes.dp4 },
                        ]}
                        onClick={onClick}
                    >
                        <Text style={TextStyles.t14MM2.copyWith({ color: !canEdit ? Colors.S2 : Colors.theme2 })} numberOfLines={1}>
                            {onClickStr}
                        </Text>
                    </AbcView>
                )}
            </View>
        );
    }

    /**
     * 药事服务
     */
    _renderPharmacyService(): JSX.Element {
        const state = this.bloc.currentState;
        const canEditPage = state.canEditPage;
        const isYeKaiTai = state.disableDispensing;

        const invoiceDetail = state.invoiceDetail,
            dispensingForms = invoiceDetail?.dispensingForms,
            isVirtualPharmacy = invoiceDetail?.isVirtualPharmacy;

        const forceOpenAuditAndCompound = !state.forceOpenAuditAndCompound(invoiceDetail?.pharmacyNo);

        const views: JSX.Element[] = [];
        const deliveryInfo = invoiceDetail?.deliveryInfo;

        const auditContentStrList: string[] = []; // 审核文本
        const allocateContentStrList: string[] = []; // 调配文本
        const processContentStrList: string[] = []; // 加工文本

        const auditList: DispensingForm[] = [];
        const auditAllCount: DispensingForm[] = [];

        const compoundedList: DispensingForm[] = [];
        const compoundedAllList: DispensingForm[] = [];

        let auditedOnClickStr = "完成审核";
        let compoundedOnClickStr = "完成调配";
        dispensingForms?.forEach((form) => {
            // 审核
            if (form?.auditedStatus) {
                auditAllCount.push(form);
                if (form?.auditedStatus == AuditedStatus.finish) {
                    auditList.push(form);
                }
            }
            // 调配
            if (form?.compoundedStatus) {
                compoundedAllList.push(form);
                if (form?.compoundedStatus == CompoundedStatus.finish) {
                    compoundedList.push(form);
                }
            }
        });

        // 完成部分审核
        if (!!auditList.length && auditList.length !== auditAllCount.length && auditAllCount.length !== 1) {
            auditedOnClickStr += `(${auditList.length}/${auditAllCount.length})`;
        }

        // 完成部分调配
        if (!!compoundedList.length && compoundedList.length !== compoundedAllList.length && compoundedAllList.length !== 1) {
            compoundedOnClickStr += `(${compoundedList.length}/${compoundedAllList.length})`;
        }

        // 可以展示的审核状态
        const canShowAuditedStatus = !(
            dispensingForms?.every((form) => form?.auditedStatus == AuditedStatus.none) ||
            state.disableDispensing ||
            !state.showPrescriptionReview
        );
        // 可以展示的调配状态
        const canShowCompoundedStatus = !(
            dispensingForms?.every((form) => form?.compoundedStatus == CompoundedStatus.none) ||
            state.disableDispensing ||
            !state.pharmacyConfig?.showPrescriptionCompound
        );
        // 可以展示的加工状态
        const canShowProcessedStatus = !(
            dispensingForms?.every((form) => form?.processedStatus == ProcessedStatus.none) ||
            invoiceDetail?.isVirtualPharmacy ||
            !state.forceOpenAuditAndCompound(invoiceDetail?.pharmacyNo)
        );
        // 可以展示的快递状态(代煎代配（虚拟）药房需要显示快递)
        const canShowDeliveryStatus = !(invoiceDetail?.notNeedDelivery || isYeKaiTai || forceOpenAuditAndCompound);

        dispensingForms
            ?.filter((form) => form.__isWesternPrescriptionForm)
            .forEach((form, index, self) => {
                if (self.length < 2) {
                    auditContentStrList.push(`中西成药处方(${form.__formSummaryInfo})`);
                    allocateContentStrList.push(`中西成药处方(${form.__formCompoundSummaryInfo})`);
                } else {
                    auditContentStrList.push(`中西成药处方${ABCUtils.toChineseNum(index + 1)}：(${form.__formSummaryInfo})`);
                    allocateContentStrList.push(`中西成药处方${ABCUtils.toChineseNum(index + 1)}：(${form.__formCompoundSummaryInfo})`);
                }
            });
        dispensingForms
            ?.filter((form) => form.__isInfusionPrescriptionForm)
            .forEach((form) => {
                auditContentStrList.push(`输注处方(${form.dispensingFormItems?.length})`);
                allocateContentStrList.push(`输注处方(${form.dispensingFormItems?.length})`);
            });
        dispensingForms
            ?.filter((form) => form.__isChineseForm)
            .forEach((form, index, self) => {
                if (self.length < 2) {
                    auditContentStrList.push(`中药处方(${form.__formSummaryInfo})`);
                    allocateContentStrList.push(`中药处方(${form.__formCompoundSummaryInfo})`);
                    processContentStrList.push(`中药处方(${form.__formProcessedSummaryInfo(state.isOpenTakeMedicine)})`);
                } else {
                    auditContentStrList.push(`中药处方${ABCUtils.toChineseNum(index + 1)}：(${form.__formSummaryInfo})`);
                    allocateContentStrList.push(`中药处方${ABCUtils.toChineseNum(index + 1)}：(${form.__formCompoundSummaryInfo})`);
                    processContentStrList.push(
                        `中药处方${ABCUtils.toChineseNum(index + 1)}：(${form.__formProcessedSummaryInfo(state.isOpenTakeMedicine)})`
                    );
                }
            });
        dispensingForms
            ?.filter((form) => form.__isExternalPrescriptionForm)
            .forEach((form) => {
                auditContentStrList.push(`外治处方(${form.dispensingFormItems?.length})`);
                allocateContentStrList.push(`外治处方(${form.dispensingFormItems?.length})`);
                processContentStrList.push(`外治处方(${form.dispensingFormItems?.length})`);
            });

        const needAuditStatus = dispensingForms?.some((form) => form?.auditedStatus == AuditedStatus.wait);
        const needCompoundedStatus = dispensingForms?.some((form) => form.compoundedStatus == CompoundedStatus.wait);

        const { allUnfinishStatus, finishStatus } = invoiceDetail!.count;
        let processStatus: string;
        if (finishStatus == allUnfinishStatus) {
            processStatus = "已加工";
        } else if (finishStatus == 0) {
            processStatus = `完成加工`;
        } else {
            processStatus = `完成加工(${finishStatus + "/" + allUnfinishStatus})`;
        }
        const isLockingOrder = state.orderIsLocking;
        // 已退费、已退药状态下，如果是已审核/已调配，支持撤销
        const isSupportCancel = invoiceDetail?.isRefundFee || invoiceDetail?.isUnDispensed;

        const pharmacyServiceListInfo = [
            {
                title: "审核",
                contentStr: auditContentStrList.join(" "),
                onClickStr: needAuditStatus ? auditedOnClickStr : "已审核",
                onClick: () => {
                    if (!isLockingOrder && canEditPage) this.bloc.requestUpdatePrescriptionStatus();
                },
                canShow: canShowAuditedStatus,
                canEdit: !isLockingOrder && canEditPage && needAuditStatus,
                onCancel: () =>
                    !isLockingOrder && (canEditPage || (isSupportCancel && !needAuditStatus)) && this.bloc.requestCancelAuditDispense(),
                isShowRevokeBtn: dispensingForms?.some((form) => form.auditedStatus == AuditedStatus.finish),
            },
            {
                title: "调配",
                contentStr: allocateContentStrList.join(" "),
                onClickStr: needCompoundedStatus ? compoundedOnClickStr : "已调配",
                onClick: () => {
                    if (!isLockingOrder && canEditPage) this.bloc.requestUpdatePrescriptionCompoundStatus();
                },
                canShow: canShowCompoundedStatus,
                canEdit: !isLockingOrder && canEditPage && needCompoundedStatus,
                onCancel: () =>
                    !isLockingOrder &&
                    (canEditPage || (isSupportCancel && !needCompoundedStatus)) &&
                    this.bloc.requestCancelCompoundDispense(),
                isShowRevokeBtn: dispensingForms?.some((form) => form.compoundedStatus == CompoundedStatus.finish),
            },
            {
                title: "加工",
                contentStr: processContentStrList.join(" "),
                onClickStr: processStatus,
                onClick: () => {
                    if (!isLockingOrder && canEditPage) this.bloc.requestUpdateProcessStatus();
                },
                canShow: canShowProcessedStatus,
                canEdit: !isLockingOrder && processStatus !== "已加工",
                showStatusBtn: !isVirtualPharmacy,
            },
            {
                title: "快递",
                onClickStr: `${invoiceDetail?.waitingDelivery ? "完成发货" : "已发货"}`,
                onClick: () => {
                    if (!isLockingOrder && canEditPage && deliveryInfo) this.bloc.requestUpdateDeliveryStatus(deliveryInfo);
                },
                canShow: canShowDeliveryStatus,
                canEdit: !isLockingOrder && !invoiceDetail?.finishDelivery,
                contentView: this._createExpressDeliveryInformationItem(),
                showStatusBtn: !isVirtualPharmacy,
            },
        ];
        (pharmacyServiceListInfo ?? [])
            .filter((item) => item.canShow)
            ?.map((item, index) => {
                views.push(
                    this.pharmacyServiceItemView({
                        title: item.title,
                        contentStr: item.contentStr ?? "",
                        onClickStr: item.onClickStr,
                        onClick: item.onClick,
                        showBottomLine: index + 1 != pharmacyServiceListInfo.length,
                        canShow: item.canShow,
                        canEdit: item.canEdit,
                        contentView: item.contentView,
                        showStatusBtn: item?.showStatusBtn,
                        onCancel: item.onCancel,
                        isShowRevokeBtn: item.isShowRevokeBtn,
                    })
                );
            });
        if (!views.length) return <View />;
        return (
            <AbcBasePanel
                panelStyle={[Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)]}
                contentStyle={[Sizes.paddingLTRB(Sizes.dp16, 0, Sizes.dp16, 0)]}
            >
                <Text style={[TextStyles.t18MT1, { paddingTop: Sizes.dp20 }]}>{"药事服务"}</Text>
                {views}
            </AbcBasePanel>
        );
    }

    _createForms(sourceFormType: number, titlePrefix: string, dispensingForm: DispensingForm[]): JSX.Element {
        const views: JSX.Element[] = [];
        const length = dispensingForm.length;
        let hasGoodsOrMaterial = false;
        dispensingForm.forEach((formItem, index) => {
            if (ABCUtils.isEmpty(dispensingForm)) return;
            let formView;
            // 金额显示成对应处方所有的金额，包括已经发药的部分
            const totalAmount = ABCUtils.formatPrice(Number(formItem.totalPrice) ?? 0);

            if (sourceFormType == ChargeSourceFormType.chinesePrescription) {
                formView = (
                    <DispensingDrugChineseFormView
                        key={formItem.id}
                        title={
                            titlePrefix +
                            (length > 1 ? ABCUtils.toChineseNum(index + 1) : "") +
                            (!!formItem?.vendorName ? "-" + formItem.vendorName : "")
                        }
                        form={formItem}
                    />
                );
            } else if (sourceFormType == ChargeSourceFormType.package) {
                formItem.dispensingFormItems?.forEach((composeItem) => {
                    views.push(
                        <DispensingDrugWesternFormView
                            sourceFormType={sourceFormType}
                            key={composeItem.id}
                            title={composeItem.name ?? ""}
                            formItems={composeItem.composeChildren!}
                            formTotalPrice={totalAmount}
                            form={composeItem}
                        />
                    );
                });
            } else if (formItem.__isGoodsForm || formItem.__isMaterialForm) {
                if (!hasGoodsOrMaterial) {
                    formView = (
                        <DispensingDrugWesternFormView
                            key={formItem.id}
                            title={titlePrefix + (length > 1 ? ABCUtils.toChineseNum(index + 1) : "")}
                            formItems={formItem.dispensingFormItems!}
                            formTotalPrice={totalAmount}
                            form={formItem}
                            forms={dispensingForm}
                            sourceFormType={sourceFormType}
                        />
                    );
                }
                hasGoodsOrMaterial = hasGoodsOrMaterial || formItem.__isGoodsForm || formItem.__isMaterialForm;
            } else {
                formView = (
                    <DispensingDrugWesternFormView
                        key={formItem.id}
                        title={titlePrefix + (length > 1 ? ABCUtils.toChineseNum(index + 1) : "")}
                        formItems={formItem.dispensingFormItems!}
                        formTotalPrice={totalAmount}
                        form={formItem}
                        forms={dispensingForm}
                        sourceFormType={sourceFormType}
                    />
                );
            }

            if (formView != null) {
                views.push(formView);
            }
        });
        return <View key={sourceFormType}>{views}</View>;
    }

    _renderAllForm(): JSX.Element[] {
        const state = this.bloc.currentState;
        const chineseForms: DispensingForm[] = [],
            westernForms: DispensingForm[] = [],
            infusionForms: DispensingForm[] = [],
            goodsForms: DispensingForm[] = [],
            packageFrom: DispensingForm[] = [],
            glassesFrom: DispensingForm[] = [],
            externalFrom: DispensingForm[] = [],
            giftForms: DispensingForm[] = []; //赠品
        const formsView: JSX.Element[] = [];

        state.invoiceDetail?.dispensingForms?.forEach((form) => {
            switch (form.sourceFormType) {
                case ChargeSourceFormType.chinesePrescription:
                    chineseForms.push(form);
                    break;
                case ChargeSourceFormType.westernPrescription:
                    westernForms.push(form);
                    break;
                case ChargeSourceFormType.infusionPrescription:
                    infusionForms.push(form);
                    break;
                case ChargeSourceFormType.glasses:
                    glassesFrom.push(form);
                    break;
                case ChargeSourceFormType.goods:
                case ChargeSourceFormType.material:
                    goodsForms.push(form);
                    break;
                case ChargeSourceFormType.gift:
                    giftForms.push(form);
                    break;
                case ChargeSourceFormType.package:
                    packageFrom.push(form);
                    break;
                case ChargeSourceFormType.externalPrescription:
                    externalFrom.push(form);
                    break;
            }
        });
        formsView.push(this._createForms(ChargeSourceFormType.goods, "材料商品", goodsForms));
        formsView.push(this._createForms(ChargeSourceFormType.westernPrescription, "成药处方", westernForms));
        formsView.push(this._createForms(ChargeSourceFormType.infusionPrescription, "输液处方", infusionForms));
        formsView.push(this._createForms(ChargeSourceFormType.chinesePrescription, "中药处方", chineseForms));
        formsView.push(this._createForms(ChargeSourceFormType.glasses, "眼镜商品", glassesFrom));
        formsView.push(this._createForms(ChargeSourceFormType.externalPrescription, "外治处方", externalFrom));
        formsView.push(this._createForms(ChargeSourceFormType.package, "", packageFrom));
        formsView.push(this._createForms(ChargeSourceFormType.gift, "赠品", giftForms));
        return formsView;
    }

    _renderDrugDeliveryStatusItem(options: {
        dispensingForms?: DispensingForm[];
        medicationSheetTitle?: string;
        pharmacyName?: string;
        prescriptionsCount?: number;
        statusNameColor?: Color;
        statusName?: string;
        showPrescriptionsCount?: boolean;
    }): JSX.Element {
        const itemViews = [];
        itemViews.push(
            <View style={[ABCStyles.rowAlignCenter, { marginBottom: Sizes.dp4 }]}>
                <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp20 }), { flexShrink: 1 }]} numberOfLines={1}>
                    {options?.pharmacyName}
                    {options?.showPrescriptionsCount ? `(${options?.prescriptionsCount}个处方)` : ""}
                </Text>
                <SizedBox width={Sizes.dp8} />
                <Text style={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp20, color: options?.statusNameColor })}>
                    {options?.statusName}
                </Text>
            </View>
        );
        return <View>{itemViews}</View>;
    }

    _renderDrugDeliveryStatus(): JSX.Element {
        const state = this.bloc.currentState,
            otherDispensingSheets = state.otherDispensingSheets;
        let statusNameColor = Colors.T4;
        let statusName = "";
        const views: JSX.Element[] = [];
        let otherDispensingSheetsCount = 0;
        let showPrescriptionsCount = true;
        otherDispensingSheets?.forEach((item) => {
            otherDispensingSheetsCount = item.dispensingForms?.length ?? 0;
            if (item?.status == DispensingStatus.waiting) {
                statusName = "待发";
                statusNameColor = Colors.B1;
            } else if (item?.status == DispensingStatus.dispensed) {
                statusName = "已发";
                showPrescriptionsCount = false;
            } else if (item?.status == DispensingStatus.unDispensed) {
                statusName = "已退";
            } else if (item?.status == DispensingStatus.canceled) {
                statusName = "关闭";
            }

            views.push(
                this._renderDrugDeliveryStatusItem({
                    pharmacyName: item.pharmacyName,
                    prescriptionsCount: otherDispensingSheetsCount,
                    statusNameColor: statusNameColor,
                    statusName: statusName,
                    showPrescriptionsCount: showPrescriptionsCount,
                })
            );
        });
        if (!otherDispensingSheetsCount) return <View />;
        return (
            <AbcBasePanel
                panelStyle={[
                    Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0),
                    {
                        padding: Sizes.dp16,
                    },
                ]}
            >
                <View style={{ flexDirection: "row", justifyContent: "flex-start" }}>
                    <View style={{ flexDirection: "row" }}>
                        <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp20 }), { flex: 1 }]} numberOfLines={1}>
                            {"关联发药单："}
                        </Text>
                    </View>
                    <View style={{ flexShrink: 1 }}>{views}</View>
                </View>
            </AbcBasePanel>
        );
    }

    // 部分退药按钮
    _createToolBarButton(): JSX.Element {
        const state = this.bloc.currentState;
        let isDisableRefund = false; // 部分发药支持退药下，此时无可退项，禁用退药按钮
        for (const item of state.invoiceDetail?.dispensingForms ?? []) {
            const firstDispensedItem = item.dispensingFormItems?.find(
                (formItem) => formItem.hasDispensingItem || item.sourceFormType == ChargeSourceFormType.package || formItem.isWaiting
            );
            if (!_.isEmpty(firstDispensedItem)) {
                isDisableRefund = true;
                break;
            }
        }

        if (state.invoiceDetail?.isWaiting && state?.isPartDispensing) {
            return (
                <ToolBar>
                    <ToolBarButtonStyle2
                        key={"partRefund"}
                        text={"退药"}
                        onClick={() => {
                            if (isDisableRefund) this.bloc.requestRefundMedicine();
                        }}
                    />
                </ToolBar>
            );
        } else {
            return <View />;
        }
    }

    // 快递信息
    _createExpressDeliveryInformationItem(): JSX.Element {
        const detailData = this.bloc.currentState.invoiceDetail,
            deliveryInfo = detailData?.deliveryInfo;
        const canEditPage = this.bloc.currentState.canEditPage;
        return (
            <View style={{ backgroundColor: Colors.white }}>
                {detailData?.deliveryType == 1 && (
                    <View>
                        <ExpressDeliveryInformationItem
                            deliveryInfo={deliveryInfo}
                            patient={detailData?.patient}
                            editable={canEditPage}
                            addressEditable={detailData.waitingDelivery}
                            feeMutable={false}
                            charged={!detailData.waitingDelivery}
                            showFee={false}
                            companyEditable={detailData.waitingDelivery}
                            onChanged={(deliveryInfo) => {
                                this.bloc.requestUpdateDeliveryInfo(deliveryInfo!);
                            }}
                            showDialog={true}
                        />
                    </View>
                )}
            </View>
        );
    }

    renderOperationRecordsItemView(options: {
        titleStr?: string;
        contentStr?: string;
        detailTypeStr?: string; // 详情类型
        detailContentStr?: string; // 详情
    }): JSX.Element {
        const { titleStr, contentStr, detailTypeStr, detailContentStr } = options;

        const list = [];
        list.push(detailContentStr?.split("<br />")); // 每组处方
        const view: JSX.Element[] = [];
        list.forEach((item) => {
            item?.forEach((f) => {
                let title = f?.split("：")[0].trim(); // 冒号前的标题
                title = title.replace("中西成药处方", "成药处方");
                let content = f?.split("：")[1]; // 冒号后的处方内容
                if (!_.includes(f, "：")) {
                    // 这里为了特殊处理修改发货信息中没有冒号无法区分title和content的情况
                    title = "";
                    content = f;
                }
                view.push(
                    <View style={{ flexDirection: "row", marginBottom: Sizes.dp8 }}>
                        <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp24 })}>
                            {`${title}${title ? "：" : ""}`}
                        </Text>
                        {!!content && (
                            <View style={{ flexShrink: 1 }}>
                                <Text style={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 })}>{content}</Text>
                            </View>
                        )}
                    </View>
                );
            });
        });

        return (
            <View>
                {!!titleStr && <Text style={TextStyles.t14NT1}>{titleStr}</Text>}
                <View style={[ABCStyles.rowAlignCenter, { flexGroup: 1 }]}>
                    {!!contentStr && (
                        <View style={ABCStyles.rowAlignCenter}>
                            <Text
                                onClick={async () => {
                                    const result = await showQueryDialog(detailTypeStr ?? "", <View>{view}</View>, "确定", "");
                                    if (result != DialogIndex.positive) return;
                                }}
                            >
                                {contentStr && <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2 })}>{contentStr}</Text>}
                                {detailContentStr && (
                                    <AbcText
                                        style={TextStyles.t14NM2}
                                    >
                                        {` 详情`}
                                    </AbcText>
                                )}
                            </Text>
                        </View>
                    )}
                </View>
            </View>
        );
    }

    // 医嘱事项
    _renderDoctorAdvise(): JSX.Element {
        const invoiceDetail = this.bloc.currentState.invoiceDetail;
        if (!invoiceDetail?.doctorAdvice) return <View />;
        return (
            <AbcBasePanel
                panelStyle={[
                    Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0),
                    Sizes.paddingLTRB(Sizes.listHorizontalMargin, Sizes.dp20, Sizes.listHorizontalMargin, Sizes.dp20),
                ]}
            >
                <Text style={[TextStyles.t18MT1, { marginBottom: Sizes.listHorizontalMargin }]}>{"医嘱事项"}</Text>
                <View style={{ flexDirection: "row", justifyContent: "flex-start" }}>
                    <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22 })]}>
                        {StringUtils.stringBr2N(invoiceDetail?.doctorAdvice)}
                    </Text>
                </View>
            </AbcBasePanel>
        );
    }

    otherSourceFormTypeContent(detail: DrugOperationRecordDetail): string {
        const otherSourceFormTypeContent: string[] = [];
        for (const form of detail?.forms ?? []) {
            otherSourceFormTypeContent.push(form.defaultSourceForm);
        }
        return otherSourceFormTypeContent.join("");
    }

    // 操作记录视图
    renderOperationRecords(): JSX.Element {
        const operationRecordsView: JSX.Element[] = [];

        const state = this.bloc.currentState;
        const drugOperationRecords = state.drugOperationRecords;

        let auditContentStr = ""; // 药事服务-审核（内容）
        let dispensedContentStr = ""; // 药事服务-调配（内容）
        let processingContentStr = ""; // 药事服务-加工（内容）
        let dispensingContentStr = ""; // 药事服务-发药（内容）
        let returnContentStr = ""; // 药事服务-退药（内容）

        const list: { titleStr?: string; contentStr?: string; detailTypeStr?: string; detailContentStr?: string }[] = [];
        drugOperationRecords?.map((item) => {
            const titleStr = `·${item.created ? item.created.format("yyyy-MM-dd HH:mm") : ""}  ${item.createdBy ?? ""}`;

            switch (item.operationType) {
                // 加工
                case operationTypeEnum.process:
                    processingContentStr = `加工 ${item.content} ${item.operatorName ? `；加工人(${item.operatorName})` : ""}`;
                    list.push({ titleStr: titleStr, contentStr: processingContentStr });
                    break;
                // 审核
                case operationTypeEnum.review:
                    auditContentStr = `审核 ${item.content}`;
                    list.push({ titleStr: titleStr, contentStr: auditContentStr });
                    break;
                // 快递发货
                case operationTypeEnum.parcel:
                    list.push({
                        titleStr: titleStr,
                        contentStr: `快递发货 ${item.content}`,
                        detailTypeStr: "快递详情",
                        detailContentStr: item.detailContent ?? "",
                    });
                    break;
                // 调配
                case operationTypeEnum.allocate:
                    dispensedContentStr = `调配 ${item.content}`;
                    list.push({ titleStr: titleStr, contentStr: dispensedContentStr });
                    break;
                // 发药
                case operationTypeEnum.dispensing:
                    dispensingContentStr = `发药 ${item.content}`;
                    list.push({
                        titleStr: titleStr,
                        contentStr: StringUtils.stringBr2N(dispensingContentStr),
                        detailTypeStr: "发药详情",
                        detailContentStr: this.otherSourceFormTypeContent(item.detail!), // 发药类型的药品详情后台为未拼接到detailContent中，需要特殊处理
                    });
                    break;
                // 退药
                case operationTypeEnum.return:
                    returnContentStr = `退药 ${item.content}`;
                    list.push({
                        titleStr: titleStr,
                        contentStr: returnContentStr,
                        detailTypeStr: "退药详情",
                        detailContentStr: this.otherSourceFormTypeContent(item.detail!),
                    });
                    break;
                // 修改发货
                case operationTypeEnum.modifyInfo:
                    list.push({
                        titleStr: titleStr,
                        contentStr: "修改发货信息",
                        detailTypeStr: "修改发货详情",
                        detailContentStr: item.detailContent ?? "",
                    });
                    break;
                // 收费
                case operationTypeEnum.charge:
                    list.push({ titleStr: titleStr, contentStr: `收费 ${item.content}` });
                    break;
                // 撤销审核
                case operationTypeEnum.cancelAudit:
                    list.push({
                        titleStr: titleStr,
                        contentStr: `撤销审核 ${item.content}`,
                    });
                    break;
                // 撤销调配
                case operationTypeEnum.cancelCompound:
                    list.push({
                        titleStr: titleStr,
                        contentStr: `撤销调配 ${item.content}`,
                    });
                    break;
                // 关闭
                case operationTypeEnum.close:
                    list.push({
                        titleStr: titleStr,
                        contentStr: `关闭 ${item.content}`,
                    });
                    break;
                //重新打开发药单
                case operationTypeEnum.reopen:
                    list.push({
                        titleStr: titleStr,
                        contentStr: `重新打开发药单 ${item.content}`,
                    });
                    break;
                // 退费完成
                case operationTypeEnum.closeCharge:
                    list.push({
                        titleStr: titleStr,
                        contentStr: `退费完成 ${item.content}`,
                    });
                    break;
                default:
                    return;
            }
        });
        list?.forEach((item, index) => {
            operationRecordsView.push(
                <View key={index} style={[{ marginBottom: index + 1 !== list.length ? Sizes.dp12 : undefined }]}>
                    {this.renderOperationRecordsItemView({
                        titleStr: item.titleStr,
                        contentStr: item.contentStr,
                        detailTypeStr: item.detailTypeStr,
                        detailContentStr: item.detailContentStr,
                    })}
                </View>
            );
        });
        return (
            <View style={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, Sizes.dp18)}>
                <AbcView
                    style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end" }]}
                    onClick={() => {
                        this.bloc.requestChangeOperationRecordsStatus(!state.operationRecordsSelected);
                    }}
                >
                    <Text style={TextStyles.t16NT}>{"操作记录"}</Text>
                    <IconFontView
                        size={Sizes.dp20}
                        name={state.operationRecordsSelected ? "arrow-up-big" : "arrow-down-big"}
                        color={Colors.mainColor}
                    />
                </AbcView>
                {state.operationRecordsSelected && (
                    <AbcBasePanel
                        panelStyle={[
                            Sizes.marginLTRB(0, Sizes.dp8, 0, Sizes.dp20),
                            Sizes.paddingLTRB(Sizes.listHorizontalMargin, Sizes.dp20, Sizes.listHorizontalMargin, Sizes.dp20),
                            { backgroundColor: "rgba(170,171,179,0.08)" },
                        ]}
                    >
                        {operationRecordsView}
                    </AbcBasePanel>
                )}
            </View>
        );
    }

    public renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        if (state.invoiceDetail == null) return <View />;
        const { matchEmployees, selectDispenser } = state;
        const isLockingOrder = state.orderIsLocking;
        const lockDetail = state.lockDetail;
        let tips = "";
        if (!!lockDetail) {
            tips = `收费员${lockDetail?.employeeName ?? ""}正在退费中，请稍候`;
        }
        return (
            <View style={{ flex: 1, ...(DeviceUtils.isOhos() ? undefined : { backgroundColor: Colors.prescriptionBg }) }}>
                {isLockingOrder && <AbcBannerTips tips={tips} />}
                {this.renderReviewView()}
                <AbcScrollView
                    ref={(ref) => (this._scrollView = ref)}
                    style={{ flex: 1 }}
                    onScrollEndDrag={(evt: ScrollEvent) => {
                        const {
                            contentOffset: { y },
                        } = evt;
                        this.scrollEnvY = y;
                    }}
                    showsVerticalScrollIndicator={false}
                    onContentSizeChanged={() => {
                        if (!state.operationRecordsSelected) {
                            this._scrollView?.scrollTo(0, this.scrollEnvY, true);
                        }
                    }}
                >
                    <View ref={kFirstChild} collapsable={false} />
                    <AbcPatientCardInfoView
                        isEditing={false}
                        patient={state.invoiceDetail.patient}
                        diagnoseCount={state.diagnoseCount}
                        type={HistoryPermissionModuleType.pharmacy}
                        isCanSeePatientHistoryInPharmacy={state.canSeePatientHistory}
                        canSeePatientMobileInPharmacy={state.canSeePatientPhone}
                        canEditPatientInfo={!isLockingOrder}
                    />
                    <DispenserView
                        invoiceDetail={state.invoiceDetail}
                        selectDispenser={selectDispenser}
                        drugDispensers={matchEmployees}
                        isLockingOrder={isLockingOrder}
                        onConfirmSelectDispenser={(data) => this.bloc.requestConfirmSelectDispenser(data)}
                        selectItemDispenser={(item) => this.bloc.requestSelectItemDispenser(item)}
                    />
                    {this._renderPharmacyService()}
                    {this._renderAllForm()}
                    {this._renderDoctorAdvise()}
                    {this._renderDrugDeliveryStatus()}
                    <AirPharmacyOrderIdView orderId={state.invoiceDetail.airPharmacyOrderId} />
                    {this.renderOperationRecords()}
                    <View ref={kLastChild} collapsable={false} />
                </AbcScrollView>
            </View>
        );
    }
}

interface DispenserViewProps {
    invoiceDetail?: GetDrugInvoiceDetail;
    drugDispensers?: Employee[];
    selectDispenser?: AbcSet<Employee>;
    isLockingOrder?: boolean;
    selectItemDispenser?(data: Employee): void;
    onConfirmSelectDispenser?(data?: Employee[]): void;
}
const DispenserView: React.FC<DispenserViewProps> = ({
    invoiceDetail,
    drugDispensers,
    selectDispenser,
    selectItemDispenser,
    onConfirmSelectDispenser,
    isLockingOrder,
}) => {
    const { isWaiting, dispensedByEmployee } = invoiceDetail || {};
    const dispensedByEmployeeNames = dispensedByEmployee?.map((t) => {
        return t.name;
    });
    if (!isWaiting && _.isEmpty(dispensedByEmployeeNames)) return <View />;
    const searchListByKeyword = (searchText?: string): Employee[] => {
        const copyDrugDispensersList = cloneDeep(drugDispensers) ?? [];
        let filterList: Employee[] = [];
        const keyword = (searchText ?? "").toLowerCase();
        if (keyword.length === 0) {
            filterList = copyDrugDispensersList;
        }
        filterList = copyDrugDispensersList.filter(
            (item) =>
                (item.employeeName?.toLowerCase().indexOf(keyword) ?? -1) >= 0 ||
                (item.employeeNamePy?.toLowerCase().indexOf(keyword) ?? -1) >= 0 ||
                (item.employeeNamePyFirst?.toLowerCase().indexOf(keyword) ?? -1) >= 0
        );
        return filterList;
    };
    const currentSelectItemDispenser = (data?: Employee): AbcSet<Employee> | undefined => {
        if (!data) return;
        selectItemDispenser?.(data);
        const copySelectDispenser: AbcSet<Employee> = cloneDeep(selectDispenser) ?? new AbcSet<Employee>();
        if (copySelectDispenser?.has(data)) {
            copySelectDispenser.delete(data);
        } else {
            if ((copySelectDispenser?.values()?.length ?? 0) < 5) {
                // 判断已选人数是否超过最大数
                copySelectDispenser?.add(data);
            } else {
                Toast.show("最多可选择5个发药人", { warning: true });
            }
        }
        return copySelectDispenser;
    };
    const options: ItemCardSelectionProps = {
        cardItemList: [
            {
                title: "发药人",
                contentHint: "选择发药人",
                content: dispensedByEmployeeNames?.join("、"),
                style: { paddingVertical: Sizes.dp5 },
                minTitleWidth: Sizes.dp60,
                itemStyle: !isLockingOrder && isWaiting ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal,
                onClick: () => (!isLockingOrder && isWaiting ? () => ({}) : undefined),
                type: 2,
                list: drugDispensers,
                selectionModel: "multiple",
                searchHint: "输入发药人姓名",
                topMaskHeight: Sizes.dp160,
                filterList: (value) => searchListByKeyword(value),
                displayOption: {
                    title: (data) => data["employeeName"] ?? "",
                },
                onSelectItem: (data) => currentSelectItemDispenser(data),
                selectItem: selectDispenser,
                onConfirm: (selectItem) => onConfirmSelectDispenser?.(selectItem),
            },
        ],
    };
    return <ItemCardSelection cardItemList={options.cardItemList} />;
};
