/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2023/8/15
 * @Copyright 成都字节流科技有限公司© 2023
 */
import React from "react";
import { View, Text, ScrollView, Dimensions } from "@hippy/react";
import { BaseComponent } from "../../base-ui/base-component";
import { BottomSheetHelper, showBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { DispenseAlertBatchItem, DispenseBatchItem, DispensingFormItem } from "../data/pharmacy-bean";
import { AbcView } from "../../base-ui/views/abc-view";
import _ from "lodash";
import colors from "../../theme/colors";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { NumberKeyboardBuilder } from "../../base-ui/views/keyboards/number-keyboard";
import { PrecisionLimitFormatter } from "../../base-ui/utils/formatter";
import { IconFontView } from "../../base-ui";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { Toast } from "../../base-ui/dialog/toast";
import { ABCUtils } from "../../base-ui/utils/utils";
import { keyboardListener } from "../../common-base-module/utils/keyboard-listener";

const _kTopMargin = 170;
const kHeight = Dimensions.get("window").height - _kTopMargin;

interface DispensingReSendModifyDialogProps {
    formInfo?: DispensingFormItem;
    sourceFormItem?: DispensingFormItem;
}

interface DispensingReSendModifyDialogStates {
    currentBatches?: DispenseBatchItem[];
    allAlterBatches?: DispenseAlertBatchItem[];
    showErrorHint: boolean;
}

export class DispensingReSendModifyDialog extends BaseComponent<DispensingReSendModifyDialogProps, DispensingReSendModifyDialogStates> {
    private contentHeight = kHeight;

    constructor(props: DispensingReSendModifyDialogProps) {
        super(props);
        this.state = {
            currentBatches: _.cloneDeep(props.formInfo?.dispenseBatches) ?? [],
            allAlterBatches: props.formInfo?.alternativeBatches ?? [],
            showErrorHint: false,
        };
    }

    static async show(props: DispensingReSendModifyDialogProps): Promise<DispenseBatchItem[]> {
        return showBottomSheet(<DispensingReSendModifyDialog {...props} />);
    }

    componentDidMount(): void {
        keyboardListener.subscribe((e) => {
            if (e.visible) {
                this.contentHeight = kHeight - (e.keyboardHeight ?? 0) + 32;
            } else {
                this.contentHeight = kHeight;
            }
            this.setState({});
        });
    }

    handleBatchItemChecked(item: DispenseAlertBatchItem): void {
        const { formInfo } = this.props;
        const totalCount = (formInfo?.unitCount ?? 0) * (formInfo?.doseCount ?? 1);
        const { currentBatches } = this.state;
        const sameIndex = currentBatches?.findIndex((batch) => batch.batchId === item.batchId) ?? -1;
        if (sameIndex > -1) {
            //删除
            currentBatches?.splice(sameIndex, 1);
        } else {
            const _sum = currentBatches?.map((item) => item.unitCount ?? 0)?.reduce((pre, cur) => pre + cur, 0) ?? 0;
            //新增
            const newBatchItem = {
                batchId: item.batchId,
                unitCount: Math.min(Math.max(0, (totalCount ?? 0) - _sum), item.stockUnitCount ?? 0),
                unit: item.unit,
            };
            currentBatches?.push(newBatchItem);
        }
        this.setState({ currentBatches });
    }

    handleBatchItemCountModify(item: DispenseAlertBatchItem, count?: number): void {
        const { currentBatches } = this.state;
        const sameIndex = currentBatches?.findIndex((batch) => batch.batchId === item.batchId) ?? -1;
        if (sameIndex > -1) {
            //删除
            currentBatches![sameIndex].unitCount = count ?? 0;
            this.setState({ currentBatches });
        }
    }

    handleSubmit(): void {
        //进行数据校验
        const { formInfo } = this.props;
        const { currentBatches } = this.state;
        const totalCount = (formInfo?.unitCount ?? 0) * (formInfo?.doseCount ?? 1);
        const allowSubmit =
            !!currentBatches?.every((item) => !!item.unitCount) &&
            currentBatches?.map((item) => item.unitCount ?? 0)?.reduce((pre, cur) => pre + cur) == totalCount;
        if (allowSubmit) {
            ABCNavigator.pop(this.state.currentBatches);
        } else {
            if (currentBatches?.map((item) => item.unitCount ?? 0)?.reduce((pre, cur) => pre + cur) != totalCount) {
                Toast.show(`发药数量不可低于${totalCount}${formInfo?.unit ?? ""}`, { warning: true });
            }
            this.setState({ showErrorHint: !allowSubmit });
        }
    }

    renderBaseMedicineView(): JSX.Element {
        const { formInfo, sourceFormItem } = this.props;
        const oldBatches = (sourceFormItem ?? formInfo)?.dispenseBatches ?? [];
        if (!formInfo) return <View />;
        const { productInfo, unitCount, doseCount } = formInfo;
        const totalCount = (unitCount ?? 0) * (doseCount ?? 1);
        return (
            <View style={[{ padding: Sizes.dp16 }]}>
                <View>
                    <Text style={[TextStyles.t16MT1]}>{`${productInfo?.displayName}(待发${totalCount}${formInfo.unit})`}</Text>
                </View>
                {oldBatches.map((o_batch, index) => (
                    <View key={index} style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp4 }]}>
                        <Text style={[TextStyles.t14NT6, { marginRight: Sizes.dp16 }]}>{`原批次：${o_batch.batchId}`}</Text>
                        <Text style={[TextStyles.t14NT6, { marginRight: Sizes.dp16 }]}>
                            {`进价：${ABCUtils.formatPriceWithRMB(o_batch.unitCostPrice ?? 0, false)}/${o_batch.unit}`}
                        </Text>
                        <Text style={[TextStyles.t14NT6]}>{`数量：${o_batch.unitCount}${o_batch.unit}`}</Text>
                    </View>
                ))}
            </View>
        );
    }

    renderBatchListView(): JSX.Element {
        const { currentBatches, allAlterBatches, showErrorHint } = this.state;
        if (!allAlterBatches) return <View />;
        return (
            <ScrollView style={[{ paddingLeft: Sizes.dp16, flex: 1 }]}>
                {allAlterBatches.map((item, index) => {
                    const sameItem = currentBatches?.find((it) => it.batchId == item.batchId);
                    return (
                        <BatchItemModifyView
                            key={index}
                            isSelect={!!sameItem}
                            showErrorHint={showErrorHint}
                            batchItemInfo={item}
                            count={sameItem?.unitCount ?? 1}
                            onCheck={this.handleBatchItemChecked.bind(this)}
                            onChange={this.handleBatchItemCountModify.bind(this)}
                        />
                    );
                })}
            </ScrollView>
        );
    }

    render(): JSX.Element {
        return (
            <View style={[ABCStyles.dialogBaseTopRadius, { height: this.contentHeight, backgroundColor: Colors.white }]}>
                {BottomSheetHelper.abcTitleBar({ title: "选择发药批次", showCloseButton: true })}
                {this.renderBaseMedicineView()}
                {this.renderBatchListView()}
                {BottomSheetHelper.createBottomBar({
                    text: "保存",
                    hideWhenKeyboardShow: true,
                    onClick: () => {
                        this.handleSubmit();
                    },
                })}
            </View>
        );
    }
}

interface BatchItemModifyViewProps {
    isSelect?: boolean;
    count?: number;
    batchItemInfo?: DispenseAlertBatchItem;
    showErrorHint?: boolean;
    onCheck?(arg: DispenseAlertBatchItem): void;
    onChange?(arg: DispenseAlertBatchItem, count: number): void;
}
const BatchItemModifyView = (props: BatchItemModifyViewProps): JSX.Element => {
    const { batchItemInfo, isSelect, count, showErrorHint, onCheck, onChange } = props;
    if (!batchItemInfo) return <View />;
    return (
        <AbcView
            style={[
                ABCStyles.bottomLine,
                { flexDirection: "row", marginTop: Sizes.dp16, paddingRight: Sizes.dp20, paddingBottom: isSelect ? Sizes.dp8 : Sizes.dp16 },
            ]}
            onClick={() => {
                onCheck?.(batchItemInfo);
            }}
        >
            <View style={[Sizes.paddingLTRB(0, Sizes.dp3, Sizes.dp8, 0)]}>
                {isSelect ? (
                    <AbcView
                        style={{ width: Sizes.dp16, height: Sizes.dp16 }}
                        onClick={() => {
                            onCheck?.(batchItemInfo);
                        }}
                    >
                        <IconFontView name={"Chosen"} size={Sizes.dp16} color={Colors.mainColor} />
                    </AbcView>
                ) : (
                    <AbcView
                        style={{
                            width: Sizes.dp16,
                            height: Sizes.dp16,
                            borderWidth: Sizes.dpHalf,
                            borderRadius: Sizes.dp16,
                            borderColor: Colors.P1,
                            paddingTop: Sizes.dp1,
                        }}
                        onClick={() => {
                            onCheck?.(batchItemInfo);
                        }}
                    />
                )}
            </View>
            <View style={{ flex: 1 }}>
                <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                    <Text style={[TextStyles.t16NT1, { flex: 1 }]} numberOfLines={1}>
                        {batchItemInfo?.batchId}
                    </Text>
                    <Text style={[TextStyles.t16NT1]}>
                        {`${ABCUtils.formatPriceWithRMB(batchItemInfo.unitCostPrice ?? 0, false)}/${batchItemInfo.unit}`}
                    </Text>
                    <Text style={[TextStyles.t16NT1, { width: Sizes.dp77, textAlign: "right" }]}>
                        {`${batchItemInfo.stockUnitCount}${batchItemInfo.unit}`}
                    </Text>
                </View>
                <View style={[ABCStyles.rowAlignCenterSpaceBetween, { marginTop: Sizes.dp8 }]}>
                    <Text style={[TextStyles.t14NT6]}>{`生产批号：${batchItemInfo?.batchNo || "-"}`}</Text>
                    <Text style={[TextStyles.t14NT6]}>{`效期：${batchItemInfo?.expireDate?.format("yyyy-MM-dd") ?? "-"}`}</Text>
                </View>
                {isSelect && (
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={[TextStyles.t14NT6]}>发药数量：</Text>
                        <View
                            style={[
                                { flexDirection: "row", borderRadius: Sizes.dp3, borderWidth: Sizes.dp1 },
                                {
                                    borderColor: showErrorHint || !_.isNumber(count) || count == 0 ? Colors.R2 : colors.P6,
                                },
                            ]}
                        >
                            <AbcTextInput
                                style={{
                                    width: Sizes.dp55,
                                    height: Sizes.dp32,
                                    textAlign: "center",
                                    borderRightWidth: Sizes.dp1,
                                    borderColor: colors.P6,
                                }}
                                defaultValue={count?.toString()}
                                multiline={false} // 单行
                                onChangeText={(text) => {
                                    onChange?.(batchItemInfo, Number(text));
                                }}
                                customKeyboardBuilder={new NumberKeyboardBuilder()} // 自定义键盘生成器
                                formatter={PrecisionLimitFormatter(0, (batchItemInfo.stockUnitCount ?? 0) + 0.00001)} // 限制输入框仅输入数字
                            />
                            <Text
                                style={[
                                    TextStyles.t16MT1,
                                    Sizes.paddingLTRB(Sizes.dp8, 0),
                                    { textAlign: "center", lineHeight: Sizes.dp32 },
                                ]}
                            >
                                {batchItemInfo.unit}
                            </Text>
                        </View>
                    </View>
                )}
            </View>
        </AbcView>
    );
};
